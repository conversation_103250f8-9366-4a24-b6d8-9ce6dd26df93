# `client/`

Shared CitizenFX framework code.

* [**citicore/**](./citicore): CitizenFX Runtime, handles loading components and implements the CitizenFX Object Model.
* [**citigame/**](./citigame): Auxiliary component for client-side lifecycle after bootstrapping.
* [**clrcore/**](./clrcore): CitizenFX.Core C# library.
* [**clrref/**](./clrref): Reference generation for the CitizenFX.Core library.
* [**common/**](./common): Shared source files included in all projects.
* [**console/**](./console): `FiveM.com` console launcher.
* [**diag/**](./diag): `CfxDiag` utility.
* [**launcher/**](./launcher): Main client entry point/bootstrapper.
* [**shared/**](./shared): Shared includes and source files.