#pragma once

static const char* g_eventNames1604[239]
{
	"UNUSED_0",
	"UNUSED_1",
	"UNUSED_2",
	"UNUSED_3",
	"UNUSED_4",
	"UNUSED_5",
	"CEventAgitated",
	"CEventAgitatedAction",
	"CEventEncroachingPed",
	"CEventCallForCover",
	"CEventCarUndriveable",
	"CEventClimbLadderOnRoute",
	"CEventClimbNavMeshOnRoute",
	"CEventAcquaintancePedDead",
	"CEventCommunicateEvent",
	"CEventCopCarBeingStolen",
	"CEventCrimeReported",
	"CEventDamage",
	"CEventDeadPedFound",
	"CEventDeath",
	"CEventDraggedOutCar",
	"UNUSED_21",
	"CEventAcquaintancePedLike",
	"CEventExplosionHeard",
	"CEventAcquaintancePedDislike",
	"UNUSED_25",
	"CEventFootStepHeard",
	"CEventGetOutOfWater",
	"CEventGivePedTask",
	"CEventGunAimedAt",
	"CEventHelpAmbientFriend",
	"CEventInjuredCryForHelp",
	"CEventCrimeCryForHelp",
	"CEventInAir",
	"UNUSED_34",
	"CEventLeaderEnteredCarAsDriver",
	"CEventAcquaintancePedWanted",
	"CEventLeaderExitedCarAsDriver",
	"CEventLeaderHolsteredWeapon",
	"CEventLeaderLeftCover",
	"CEventLeaderUnholsteredWeapon",
	"CEventMeleeAction",
	"CEventMustLeaveBoat",
	"CEventNewTask",
	"UNUSED_44",
	"CEventObjectCollision",
	"CEventOnFire",
	"CEventOpenDoor",
	"CEventShovePed",
	"CEventPedCollisionWithPed",
	"CEventPedCollisionWithPlayer",
	"CEventPedEnteredMyVehicle",
	"CEventPedJackingMyVehicle",
	"CEventPedOnCarRoof",
	"UNUSED_54",
	"UNUSED_55",
	"CEventPlayerCollisionWithPed",
	"UNUSED_57",
	"CEventPotentialBeWalkedInto",
	"CEventPotentialBlast",
	"CEventPotentialGetRunOver",
	"UNUSED_61",
	"UNUSED_62",
	"CEventPotentialWalkIntoVehicle",
	"CEventProvidingCover",
	"UNUSED_65",
	"CEventRanOverPed",
	"UNUSED_67",
	"CEventReactionEnemyPed",
	"CEventReactionInvestigateDeadPed",
	"CEventReactionInvestigateThreat",
	"CEventRequestHelpWithConfrontation",
	"CEventRespondedToThreat",
	"UNUSED_73",
	"CEventScriptCommand",
	"CEventShockingCarAlarm",
	"CEventShockingCarChase",
	"CEventShockingCarCrash",
	"CEventShockingBicycleCrash",
	"CEventShockingCarPileUp",
	"CEventShockingCarOnCar",
	"CEventShockingDangerousAnimal",
	"CEventShockingDeadBody",
	"CEventShockingDrivingOnPavement",
	"CEventShockingBicycleOnPavement",
	"CEventShockingEngineRevved",
	"CEventShockingExplosion",
	"CEventShockingFire",
	"CEventShockingGunFight",
	"CEventShockingGunshotFired",
	"CEventShockingHelicopterOverhead",
	"CEventShockingParachuterOverhead",
	"CEventShockingPedKnockedIntoByPlayer",
	"CEventShockingHornSounded",
	"CEventShockingInDangerousVehicle",
	"CEventShockingInjuredPed",
	"CEventShockingMadDriver",
	"CEventShockingMadDriverExtreme",
	"CEventShockingMadDriverBicycle",
	"CEventShockingMugging",
	"CEventShockingNonViolentWeaponAimedAt",
	"CEventShockingPedRunOver",
	"CEventShockingPedShot",
	"CEventShockingPlaneFlyby",
	"CEventShockingPotentialBlast",
	"CEventShockingPropertyDamage",
	"CEventShockingRunningPed",
	"CEventShockingRunningStampede",
	"CEventShockingSeenCarStolen",
	"CEventShockingSeenConfrontation",
	"CEventShockingSeenGangFight",
	"CEventShockingSeenInsult",
	"CEventShockingSeenMeleeAction",
	"CEventShockingSeenNiceCar",
	"CEventShockingSeenPedKilled",
	"CEventShockingVehicleTowed",
	"CEventShockingWeaponThreat",
	"CEventShockingWeirdPed",
	"CEventShockingWeirdPedApproaching",
	"CEventShockingSiren",
	"CEventShockingStudioBomb",
	"CEventShockingVisibleWeapon",
	"CEventGunShot",
	"CEventGunShotBulletImpact",
	"CEventGunShotWhizzedBy",
	"CEventFriendlyAimedAt",
	"CEventFriendlyFireNearMiss",
	"CEventShoutBlockingLos",
	"CEventShoutTargetPosition",
	"CEventStaticCountReachedMax",
	"CEventStuckInAir",
	"CEventSuspiciousActivity",
	"CEventSwitch2NM",
	"CEventUnidentifiedPed",
	"CEventVehicleCollision",
	"CEventVehicleDamageWeapon",
	"CEventVehicleOnFire",
	"UNUSED_137",
	"CEventDisturbance",
	"CEventEntityDamaged",
	"CEventEntityDestroyed",
	"CEventWrithe",
	"CEventHurtTransition",
	"CEventPlayerUnableToEnterVehicle",
	"CEventScenarioForceAction",
	"CEventStatChangedValue",
	"CEventPlayerDeath",
	"CEventPedSeenDeadPed",
	"UNUSED_148",
	"UNUSED_149",
	"UNUSED_150",
	"CEventNetworkPlayerJoinScript",
	"CEventNetworkPlayerLeftScript",
	"CEventNetworkStorePlayerLeft",
	"CEventNetworkStartSession",
	"CEventNetworkEndSession",
	"CEventNetworkStartMatch",
	"UNUSED_157",
	"CEventNetworkRemovedFromSessionDueToStall",
	"CEventNetworkRemovedFromSessionDueToComplaints",
	"CEventNetworkConnectionTimeout",
	"CEventNetworkPlayerSpawn",
	"CEventNetworkPlayerCollectedPickup",
	"CEventNetworkPlayerCollectedAmbientPickup",
	"CEventNetworkPlayerCollectedPortablePickup",
	"CEventNetworkPlayerDroppedPortablePickup",
	"UNUSED_166",
	"CEventNetworkInviteAccepted",
	"CEventNetworkInviteConfirmed",
	"CEventNetworkInviteRejected",
	"CEventNetworkSummon",
	"CEventNetworkScriptEvent",
	"UNUSED_172",
	"CEventNetworkSignInStateChanged",
	"CEventNetworkRosChanged",
	"CEventNetworkBail",
	"CEventNetworkHostMigration",
	"CEventNetworkFindSession",
	"CEventNetworkHostSession",
	"CEventNetworkJoinSession",
	"CEventNetworkJoinSessionResponse",
	"CEventNetworkCheatTriggered",
	"CEventNetworkEntityDamage",
	"CEventNetworkPlayerArrest",
	"CEventNetworkTimedExplosion",
	"CEventNetworkPrimaryClanChanged",
	"CEventNetworkClanJoined",
	"CEventNetworkClanLeft",
	"CEventNetworkClanInviteReceived",
	"CEventNetworkVoiceSessionStarted",
	"CEventNetworkVoiceSessionEnded",
	"CEventNetworkVoiceConnectionRequested",
	"CEventNetworkVoiceConnectionResponse",
	"CEventNetworkVoiceConnectionTerminated",
	"CEventNetworkTextMessageReceived",
	"CEventNetworkCloudFileResponse",
	"CEventNetworkPickupRespawned",
	"CEventNetworkPresence_StatUpdate",
	"CEventNetworkPedLeftBehind",
	"CEventNetwork_InboxMsgReceived",
	"CEventNetworkAttemptHostMigration",
	"CEventNetworkIncrementStat",
	"CEventNetworkSessionEvent",
	"CEventNetworkTransitionStarted",
	"CEventNetworkTransitionEvent",
	"CEventNetworkTransitionMemberJoined",
	"CEventNetworkTransitionMemberLeft",
	"CEventNetworkTransitionParameterChanged",
	"CEventNetworkClanKicked",
	"CEventNetworkTransitionStringChanged",
	"CEventNetworkTransitionGamerInstruction",
	"CEventNetworkPresenceInvite",
	"CEventNetworkPresenceInviteRemoved",
	"CEventNetworkPresenceInviteReply",
	"CEventNetworkCashTransactionLog",
	"CEventNetworkClanRankChanged",
	"CEventNetworkVehicleUndrivable",
	"CEventNetworkPresenceTriggerEvent",
	"CEventNetworkEmailReceivedEvent",
	"CEventNetworkFollowInviteReceived",
	"CEventNetworkAdminInvited",
	"CEventNetworkSpectateLocal",
	"CEventNetworkCloudEvent",
	"CEventNetworkShopTransaction",
	"UNUSED_224",
	"UNUSED_225",
	"CEventNetworkOnlinePermissionsUpdated",
	"CEventNetworkSystemServiceEvent",
	"CEventNetworkRequestDelay",
	"CEventNetworkSocialClubAccountLinked",
	"CEventNetworkScAdminPlayerUpdated",
	"CEventNetworkScAdminReceivedCash",
	"CEventNetworkClanInviteRequestReceived",
	"CEventNetworkMarketingEmailReceivedEvent",
	"CEventNetworkStuntPerformed",
	"UNUSED_235",
	"CEventErrorUnknownError",
	"CEventErrorArrayOverflow",
	"CEventErrorInstructionLimit",
};

static const char* g_eventNames2060[247]
{
	"UNUSED_0",
	"UNUSED_1",
	"UNUSED_2",
	"UNUSED_3",
	"UNUSED_4",
	"UNUSED_5",
	"CEventAgitated",
	"CEventAgitatedAction",
	"CEventEncroachingPed",
	"CEventCallForCover",
	"CEventCarUndriveable",
	"CEventClimbLadderOnRoute",
	"CEventClimbNavMeshOnRoute",
	"CEventAcquaintancePedDead",
	"CEventCommunicateEvent",
	"CEventCopCarBeingStolen",
	"CEventCrimeReported",
	"CEventDamage",
	"CEventDeadPedFound",
	"CEventDeath",
	"CEventDraggedOutCar",
	"UNUSED_21",
	"CEventAcquaintancePedLike",
	"CEventExplosionHeard",
	"CEventAcquaintancePedDislike",
	"UNUSED_25",
	"CEventFootStepHeard",
	"CEventGetOutOfWater",
	"CEventGivePedTask",
	"CEventGunAimedAt",
	"CEventHelpAmbientFriend",
	"CEventInjuredCryForHelp",
	"CEventCrimeCryForHelp",
	"CEventInAir",
	"UNUSED_34",
	"UNUSED_35",
	"CEventAcquaintancePedWanted",
	"UNUSED_37",
	"CEventLeaderExitedCarAsDriver",
	"CEventLeaderHolsteredWeapon",
	"CEventLeaderLeftCover",
	"CEventLeaderUnholsteredWeapon",
	"CEventMeleeAction",
	"CEventMustLeaveBoat",
	"CEventNewTask",
	"UNUSED_45",
	"CEventObjectCollision",
	"CEventOnFire",
	"CEventOpenDoor",
	"CEventShovePed",
	"CEventPedCollisionWithPed",
	"CEventPedCollisionWithPlayer",
	"CEventPedEnteredMyVehicle",
	"CEventPedJackingMyVehicle",
	"CEventPedOnCarRoof",
	"UNUSED_55",
	"UNUSED_56",
	"CEventPlayerCollisionWithPed",
	"UNUSED_58",
	"CEventPotentialBeWalkedInto",
	"CEventPotentialBlast",
	"CEventPotentialGetRunOver",
	"UNUSED_62",
	"UNUSED_63",
	"CEventPotentialWalkIntoVehicle",
	"CEventProvidingCover",
	"UNUSED_66",
	"CEventRanOverPed",
	"UNUSED_68",
	"CEventReactionEnemyPed",
	"CEventReactionInvestigateDeadPed",
	"CEventReactionInvestigateThreat",
	"CEventRequestHelpWithConfrontation",
	"CEventRespondedToThreat",
	"UNUSED_74",
	"CEventScriptCommand",
	"CEventShockingBrokenGlass",
	"CEventShockingCarAlarm",
	"CEventShockingCarChase",
	"CEventShockingCarCrash",
	"CEventShockingBicycleCrash",
	"CEventShockingCarPileUp",
	"CEventShockingCarOnCar",
	"CEventShockingDangerousAnimal",
	"CEventShockingDeadBody",
	"CEventShockingDrivingOnPavement",
	"CEventShockingBicycleOnPavement",
	"CEventShockingEngineRevved",
	"CEventShockingExplosion",
	"CEventShockingFire",
	"CEventShockingGunFight",
	"CEventShockingGunshotFired",
	"CEventShockingHelicopterOverhead",
	"CEventShockingParachuterOverhead",
	"CEventShockingPedKnockedIntoByPlayer",
	"CEventShockingHornSounded",
	"CEventShockingInDangerousVehicle",
	"CEventShockingInjuredPed",
	"CEventShockingMadDriver",
	"CEventShockingMadDriverExtreme",
	"CEventShockingMadDriverBicycle",
	"CEventShockingMugging",
	"CEventShockingNonViolentWeaponAimedAt",
	"CEventShockingPedRunOver",
	"CEventShockingPedShot",
	"CEventShockingPlaneFlyby",
	"CEventShockingPotentialBlast",
	"CEventShockingPropertyDamage",
	"CEventShockingRunningPed",
	"CEventShockingRunningStampede",
	"CEventShockingSeenCarStolen",
	"CEventShockingSeenConfrontation",
	"CEventShockingSeenGangFight",
	"CEventShockingSeenInsult",
	"CEventShockingSeenMeleeAction",
	"CEventShockingSeenNiceCar",
	"CEventShockingSeenPedKilled",
	"CEventShockingVehicleTowed",
	"CEventShockingWeaponThreat",
	"CEventShockingWeirdPed",
	"CEventShockingWeirdPedApproaching",
	"CEventShockingSiren",
	"CEventShockingStudioBomb",
	"CEventShockingVisibleWeapon",
	"CEventGunShot",
	"CEventGunShotBulletImpact",
	"CEventGunShotWhizzedBy",
	"CEventFriendlyAimedAt",
	"CEventFriendlyFireNearMiss",
	"CEventShoutBlockingLos",
	"CEventShoutTargetPosition",
	"CEventStaticCountReachedMax",
	"CEventStuckInAir",
	"CEventSuspiciousActivity",
	"CEventSwitch2NM",
	"CEventUnidentifiedPed",
	"CEventVehicleCollision",
	"CEventVehicleDamageWeapon",
	"CEventVehicleOnFire",
	"UNUSED_139",
	"CEventDisturbance",
	"CEventEntityDamaged",
	"CEventEntityDestroyed",
	"CEventWrithe",
	"CEventHurtTransition",
	"CEventPlayerUnableToEnterVehicle",
	"CEventScenarioForceAction",
	"CEventStatChangedValue",
	"CEventPlayerDeath",
	"CEventPedSeenDeadPed",
	"UNUSED_150",
	"UNUSED_151",
	"UNUSED_152",
	"CEventNetworkPlayerJoinScript",
	"CEventNetworkPlayerLeftScript",
	"CEventNetworkStorePlayerLeft",
	"CEventNetworkStartSession",
	"CEventNetworkEndSession",
	"CEventNetworkStartMatch",
	"UNUSED_159",
	"CEventNetworkRemovedFromSessionDueToStall",
	"CEventNetworkRemovedFromSessionDueToComplaints",
	"CEventNetworkConnectionTimeout",
	"CEventNetworkPedDroppedWeapon",
	"CEventNetworkPlayerSpawn",
	"CEventNetworkPlayerCollectedPickup",
	"CEventNetworkPlayerCollectedAmbientPickup",
	"CEventNetworkPlayerCollectedPortablePickup",
	"CEventNetworkPlayerDroppedPortablePickup",
	"UNUSED_169",
	"CEventNetworkInviteAccepted",
	"CEventNetworkInviteConfirmed",
	"CEventNetworkInviteRejected",
	"CEventNetworkSummon",
	"CEventNetworkScriptEvent",
	"UNUSED_175",
	"CEventNetworkSignInStateChanged",
	"CEventNetworkRosChanged",
	"CEventNetworkBail",
	"CEventNetworkHostMigration",
	"CEventNetworkFindSession",
	"CEventNetworkHostSession",
	"CEventNetworkJoinSession",
	"CEventNetworkJoinSessionResponse",
	"CEventNetworkCheatTriggered",
	"CEventNetworkEntityDamage",
	"CEventNetworkPlayerArrest",
	"CEventNetworkTimedExplosion",
	"CEventNetworkPrimaryClanChanged",
	"CEventNetworkClanJoined",
	"CEventNetworkClanLeft",
	"CEventNetworkClanInviteReceived",
	"CEventNetworkVoiceSessionStarted",
	"CEventNetworkVoiceSessionEnded",
	"CEventNetworkVoiceConnectionRequested",
	"CEventNetworkVoiceConnectionResponse",
	"CEventNetworkVoiceConnectionTerminated",
	"CEventNetworkTextMessageReceived",
	"CEventNetworkCloudFileResponse",
	"CEventNetworkPickupRespawned",
	"CEventNetworkPresence_StatUpdate",
	"CEventNetworkPedLeftBehind",
	"CEventNetwork_InboxMsgReceived",
	"CEventNetworkAttemptHostMigration",
	"CEventNetworkIncrementStat",
	"CEventNetworkSessionEvent",
	"CEventNetworkTransitionStarted",
	"CEventNetworkTransitionEvent",
	"CEventNetworkTransitionMemberJoined",
	"CEventNetworkTransitionMemberLeft",
	"CEventNetworkTransitionParameterChanged",
	"CEventNetworkClanKicked",
	"CEventNetworkTransitionStringChanged",
	"CEventNetworkTransitionGamerInstruction",
	"CEventNetworkPresenceInvite",
	"CEventNetworkPresenceInviteRemoved",
	"CEventNetworkPresenceInviteReply",
	"CEventNetworkCashTransactionLog",
	"CEventNetworkClanRankChanged",
	"CEventNetworkVehicleUndrivable",
	"CEventNetworkPresenceTriggerEvent",
	"CEventNetworkEmailReceivedEvent",
	"CEventNetworkFollowInviteReceived",
	"CEventNetworkAdminInvited",
	"CEventNetworkSpectateLocal",
	"CEventNetworkCloudEvent",
	"CEventNetworkShopTransaction",
	"UNUSED_227",
	"UNUSED_228",
	"CEventNetworkOnlinePermissionsUpdated",
	"CEventNetworkSystemServiceEvent",
	"CEventNetworkRequestDelay",
	"CEventNetworkSocialClubAccountLinked",
	"CEventNetworkScAdminPlayerUpdated",
	"CEventNetworkScAdminReceivedCash",
	"CEventNetworkClanInviteRequestReceived",
	"CEventNetworkMarketingEmailReceivedEvent",
	"CEventNetworkStuntPerformed",
	"CEventNetworkFiredDummyProjectile",
	"CEventNetworkPlayerEnteredVehicle",
	"CEventNetworkPlayerActivatedSpecialAbility",
	"CEventNetworkPlayerDeactivatedSpecialAbility",
	"CEventNetworkPlayerSpecialAbilityFailedActivation",
	"UNUSED_243",
	"CEventErrorUnknownError",
	"CEventErrorArrayOverflow",
	"CEventErrorInstructionLimit",
};

static const char* g_eventNames2189[248]
{
	"UNUSED_0",
	"UNUSED_1",
	"UNUSED_2",
	"UNUSED_3",
	"UNUSED_4",
	"UNUSED_5",
	"CEventAgitated",
	"CEventAgitatedAction",
	"CEventEncroachingPed",
	"CEventCallForCover",
	"CEventCarUndriveable",
	"CEventClimbLadderOnRoute",
	"CEventClimbNavMeshOnRoute",
	"CEventAcquaintancePedDead",
	"CEventCommunicateEvent",
	"CEventCopCarBeingStolen",
	"CEventCrimeReported",
	"CEventDamage",
	"CEventDeadPedFound",
	"CEventDeath",
	"CEventDraggedOutCar",
	"UNUSED_21",
	"CEventAcquaintancePedLike",
	"CEventExplosionHeard",
	"CEventAcquaintancePedDislike",
	"UNUSED_25",
	"CEventFootStepHeard",
	"CEventGetOutOfWater",
	"CEventGivePedTask",
	"CEventGunAimedAt",
	"CEventHelpAmbientFriend",
	"CEventInjuredCryForHelp",
	"CEventCrimeCryForHelp",
	"CEventInAir",
	"UNUSED_34",
	"UNUSED_35",
	"CEventAcquaintancePedWanted",
	"UNUSED_37",
	"CEventLeaderExitedCarAsDriver",
	"CEventLeaderHolsteredWeapon",
	"CEventLeaderLeftCover",
	"CEventLeaderUnholsteredWeapon",
	"CEventMeleeAction",
	"CEventMustLeaveBoat",
	"CEventNewTask",
	"UNUSED_45",
	"CEventObjectCollision",
	"CEventOnFire",
	"CEventOpenDoor",
	"CEventShovePed",
	"CEventPedCollisionWithPed",
	"CEventPedCollisionWithPlayer",
	"CEventPedEnteredMyVehicle",
	"CEventPedJackingMyVehicle",
	"CEventPedOnCarRoof",
	"UNUSED_55",
	"UNUSED_56",
	"CEventPlayerCollisionWithPed",
	"UNUSED_58",
	"CEventPotentialBeWalkedInto",
	"CEventPotentialBlast",
	"CEventPotentialGetRunOver",
	"UNUSED_62",
	"UNUSED_63",
	"CEventPotentialWalkIntoVehicle",
	"CEventProvidingCover",
	"UNUSED_66",
	"CEventRanOverPed",
	"UNUSED_68",
	"CEventReactionEnemyPed",
	"CEventReactionInvestigateDeadPed",
	"CEventReactionInvestigateThreat",
	"CEventRequestHelpWithConfrontation",
	"CEventRespondedToThreat",
	"UNUSED_74",
	"CEventScriptCommand",
	"CEventShockingBrokenGlass",
	"CEventShockingCarAlarm",
	"CEventShockingCarChase",
	"CEventShockingCarCrash",
	"CEventShockingBicycleCrash",
	"CEventShockingCarPileUp",
	"CEventShockingCarOnCar",
	"CEventShockingDangerousAnimal",
	"CEventShockingDeadBody",
	"CEventShockingDrivingOnPavement",
	"CEventShockingBicycleOnPavement",
	"CEventShockingEngineRevved",
	"CEventShockingExplosion",
	"CEventShockingFire",
	"CEventShockingGunFight",
	"CEventShockingGunshotFired",
	"CEventShockingHelicopterOverhead",
	"CEventShockingParachuterOverhead",
	"CEventShockingPedKnockedIntoByPlayer",
	"CEventShockingHornSounded",
	"CEventShockingInDangerousVehicle",
	"CEventShockingInjuredPed",
	"CEventShockingMadDriver",
	"CEventShockingMadDriverExtreme",
	"CEventShockingMadDriverBicycle",
	"CEventShockingMugging",
	"CEventShockingNonViolentWeaponAimedAt",
	"CEventShockingPedRunOver",
	"CEventShockingPedShot",
	"CEventShockingPlaneFlyby",
	"CEventShockingPotentialBlast",
	"CEventShockingPropertyDamage",
	"CEventShockingRunningPed",
	"CEventShockingRunningStampede",
	"CEventShockingSeenCarStolen",
	"CEventShockingSeenConfrontation",
	"CEventShockingSeenGangFight",
	"CEventShockingSeenInsult",
	"CEventShockingSeenMeleeAction",
	"CEventShockingSeenNiceCar",
	"CEventShockingSeenPedKilled",
	"CEventShockingVehicleTowed",
	"CEventShockingWeaponThreat",
	"CEventShockingWeirdPed",
	"CEventShockingWeirdPedApproaching",
	"CEventShockingSiren",
	"CEventShockingStudioBomb",
	"CEventShockingVisibleWeapon",
	"CEventGunShot",
	"CEventGunShotBulletImpact",
	"CEventGunShotWhizzedBy",
	"CEventFriendlyAimedAt",
	"CEventFriendlyFireNearMiss",
	"CEventShoutBlockingLos",
	"CEventShoutTargetPosition",
	"CEventStaticCountReachedMax",
	"CEventStuckInAir",
	"CEventSuspiciousActivity",
	"CEventSwitch2NM",
	"CEventUnidentifiedPed",
	"CEventVehicleCollision",
	"CEventVehicleDamageWeapon",
	"CEventVehicleOnFire",
	"UNUSED_139",
	"CEventDisturbance",
	"CEventEntityDamaged",
	"CEventEntityDestroyed",
	"CEventWrithe",
	"CEventHurtTransition",
	"CEventPlayerUnableToEnterVehicle",
	"CEventScenarioForceAction",
	"CEventStatChangedValue",
	"CEventPlayerDeath",
	"CEventPedSeenDeadPed",
	"UNUSED_150",
	"UNUSED_151",
	"UNUSED_152",
	"CEventNetworkPlayerJoinScript",
	"CEventNetworkPlayerLeftScript",
	"CEventNetworkStorePlayerLeft",
	"CEventNetworkStartSession",
	"CEventNetworkEndSession",
	"CEventNetworkStartMatch",
	"UNUSED_159",
	"CEventNetworkRemovedFromSessionDueToStall",
	"CEventNetworkRemovedFromSessionDueToComplaints",
	"CEventNetworkConnectionTimeout",
	"CEventNetworkPedDroppedWeapon",
	"CEventNetworkPlayerSpawn",
	"CEventNetworkPlayerCollectedPickup",
	"CEventNetworkPlayerCollectedAmbientPickup",
	"CEventNetworkPlayerCollectedPortablePickup",
	"CEventNetworkPlayerDroppedPortablePickup",
	"UNUSED_169",
	"CEventNetworkInviteAccepted",
	"CEventNetworkInviteConfirmed",
	"CEventNetworkInviteRejected",
	"CEventNetworkSummon",
	"CEventNetworkScriptEvent",
	"UNUSED_175",
	"CEventNetworkSignInStateChanged",
	"CEventNetworkRosChanged",
	"CEventNetworkBail",
	"CEventNetworkHostMigration",
	"CEventNetworkFindSession",
	"CEventNetworkHostSession",
	"CEventNetworkJoinSession",
	"CEventNetworkJoinSessionResponse",
	"CEventNetworkCheatTriggered",
	"CEventNetworkEntityDamage",
	"CEventNetworkPlayerArrest",
	"CEventNetworkTimedExplosion",
	"CEventNetworkPrimaryClanChanged",
	"CEventNetworkClanJoined",
	"CEventNetworkClanLeft",
	"CEventNetworkClanInviteReceived",
	"CEventNetworkVoiceSessionStarted",
	"CEventNetworkVoiceSessionEnded",
	"CEventNetworkVoiceConnectionRequested",
	"CEventNetworkVoiceConnectionResponse",
	"CEventNetworkVoiceConnectionTerminated",
	"CEventNetworkTextMessageReceived",
	"CEventNetworkCloudFileResponse",
	"CEventNetworkPickupRespawned",
	"CEventNetworkPresence_StatUpdate",
	"CEventNetworkPedLeftBehind",
	"CEventNetwork_InboxMsgReceived",
	"CEventNetworkAttemptHostMigration",
	"CEventNetworkIncrementStat",
	"CEventNetworkSessionEvent",
	"CEventNetworkTransitionStarted",
	"CEventNetworkTransitionEvent",
	"CEventNetworkTransitionMemberJoined",
	"CEventNetworkTransitionMemberLeft",
	"CEventNetworkTransitionParameterChanged",
	"CEventNetworkClanKicked",
	"CEventNetworkTransitionStringChanged",
	"CEventNetworkTransitionGamerInstruction",
	"CEventNetworkPresenceInvite",
	"CEventNetworkPresenceInviteRemoved",
	"CEventNetworkPresenceInviteReply",
	"CEventNetworkCashTransactionLog",
	"CEventNetworkClanRankChanged",
	"CEventNetworkVehicleUndrivable",
	"CEventNetworkPresenceTriggerEvent",
	"CEventNetworkEmailReceivedEvent",
	"CEventNetworkFollowInviteReceived",
	"CEventNetworkAdminInvited",
	"CEventNetworkSpectateLocal",
	"CEventNetworkCloudEvent",
	"CEventNetworkShopTransaction",
	"UNUSED_227",
	"UNUSED_228",
	"CEventNetworkOnlinePermissionsUpdated",
	"CEventNetworkSystemServiceEvent",
	"CEventNetworkRequestDelay",
	"CEventNetworkSocialClubAccountLinked",
	"CEventNetworkScAdminPlayerUpdated",
	"CEventNetworkScAdminReceivedCash",
	"CEventNetworkClanInviteRequestReceived",
	"CEventNetworkMarketingEmailReceivedEvent",
	"CEventNetworkStuntPerformed",
	"CEventNetworkFiredDummyProjectile",
	"CEventNetworkPlayerEnteredVehicle",
	"CEventNetworkPlayerActivatedSpecialAbility",
	"CEventNetworkPlayerDeactivatedSpecialAbility",
	"CEventNetworkPlayerSpecialAbilityFailedActivation",
	"CEventNetworkFiredVehicleProjectile",
	"UNUSED_244",
	"CEventErrorUnknownError",
	"CEventErrorArrayOverflow",
	"CEventErrorInstructionLimit"
};

static const char* g_eventNames2545[249]
{
	"UNUSED_0",
	"UNUSED_1",
	"UNUSED_2",
	"UNUSED_3",
	"UNUSED_4",
	"UNUSED_5",
	"CEventAgitated",
	"CEventAgitatedAction",
	"CEventEncroachingPed",
	"CEventCallForCover",
	"CEventCarUndriveable",
	"CEventClimbLadderOnRoute",
	"CEventClimbNavMeshOnRoute",
	"CEventAcquaintancePedDead",
	"CEventCommunicateEvent",
	"CEventCopCarBeingStolen",
	"CEventCrimeReported",
	"CEventDamage",
	"CEventDeadPedFound",
	"CEventDeath",
	"CEventDraggedOutCar",
	"UNUSED_21",
	"CEventAcquaintancePedLike",
	"CEventExplosionHeard",
	"CEventAcquaintancePedDislike",
	"UNUSED_25",
	"CEventFootStepHeard",
	"CEventGetOutOfWater",
	"CEventGivePedTask",
	"CEventGunAimedAt",
	"CEventHelpAmbientFriend",
	"CEventInjuredCryForHelp",
	"CEventCrimeCryForHelp",
	"CEventInAir",
	"UNUSED_34",
	"UNUSED_35",
	"CEventAcquaintancePedWanted",
	"UNUSED_37",
	"CEventLeaderExitedCarAsDriver",
	"CEventLeaderHolsteredWeapon",
	"CEventLeaderLeftCover",
	"CEventLeaderUnholsteredWeapon",
	"CEventMeleeAction",
	"CEventMustLeaveBoat",
	"CEventNewTask",
	"UNUSED_45",
	"CEventObjectCollision",
	"CEventOnFire",
	"CEventOpenDoor",
	"CEventShovePed",
	"CEventPedCollisionWithPed",
	"CEventPedCollisionWithPlayer",
	"CEventPedEnteredMyVehicle",
	"CEventPedJackingMyVehicle",
	"CEventPedOnCarRoof",
	"UNUSED_55",
	"UNUSED_56",
	"CEventPlayerCollisionWithPed",
	"UNUSED_58",
	"CEventPotentialBeWalkedInto",
	"CEventPotentialBlast",
	"CEventPotentialGetRunOver",
	"UNUSED_62",
	"UNUSED_63",
	"CEventPotentialWalkIntoVehicle",
	"CEventProvidingCover",
	"UNUSED_66",
	"CEventRanOverPed",
	"UNUSED_68",
	"CEventReactionEnemyPed",
	"CEventReactionInvestigateDeadPed",
	"CEventReactionInvestigateThreat",
	"CEventRequestHelpWithConfrontation",
	"CEventRespondedToThreat",
	"UNUSED_74",
	"CEventScriptCommand",
	"CEventShockingBrokenGlass",
	"CEventShockingCarAlarm",
	"CEventShockingCarChase",
	"CEventShockingCarCrash",
	"CEventShockingBicycleCrash",
	"CEventShockingCarPileUp",
	"CEventShockingCarOnCar",
	"CEventShockingDangerousAnimal",
	"CEventShockingDeadBody",
	"CEventShockingDrivingOnPavement",
	"CEventShockingBicycleOnPavement",
	"CEventShockingEngineRevved",
	"CEventShockingExplosion",
	"CEventShockingFire",
	"CEventShockingGunFight",
	"CEventShockingGunshotFired",
	"CEventShockingHelicopterOverhead",
	"CEventShockingParachuterOverhead",
	"CEventShockingPedKnockedIntoByPlayer",
	"CEventShockingHornSounded",
	"CEventShockingInDangerousVehicle",
	"CEventShockingInjuredPed",
	"CEventShockingMadDriver",
	"CEventShockingMadDriverExtreme",
	"CEventShockingMadDriverBicycle",
	"CEventShockingMugging",
	"CEventShockingNonViolentWeaponAimedAt",
	"CEventShockingPedRunOver",
	"CEventShockingPedShot",
	"CEventShockingPlaneFlyby",
	"CEventShockingPotentialBlast",
	"CEventShockingPropertyDamage",
	"CEventShockingRunningPed",
	"CEventShockingRunningStampede",
	"CEventShockingSeenCarStolen",
	"CEventShockingSeenConfrontation",
	"CEventShockingSeenGangFight",
	"CEventShockingSeenInsult",
	"CEventShockingSeenMeleeAction",
	"CEventShockingSeenNiceCar",
	"CEventShockingSeenPedKilled",
	"CEventShockingVehicleTowed",
	"CEventShockingWeaponThreat",
	"CEventShockingWeirdPed",
	"CEventShockingWeirdPedApproaching",
	"CEventShockingSiren",
	"CEventShockingStudioBomb",
	"CEventShockingVisibleWeapon",
	"CEventGunShot",
	"CEventGunShotBulletImpact",
	"CEventGunShotWhizzedBy",
	"CEventFriendlyAimedAt",
	"CEventFriendlyFireNearMiss",
	"CEventShoutBlockingLos",
	"CEventShoutTargetPosition",
	"CEventStaticCountReachedMax",
	"CEventStuckInAir",
	"CEventSuspiciousActivity",
	"CEventSwitch2NM",
	"CEventUnidentifiedPed",
	"CEventVehicleCollision",
	"CEventVehicleDamageWeapon",
	"CEventVehicleOnFire",
	"UNUSED_139",
	"CEventDisturbance",
	"CEventEntityDamaged",
	"CEventEntityDestroyed",
	"CEventWrithe",
	"CEventHurtTransition",
	"CEventPlayerUnableToEnterVehicle",
	"CEventScenarioForceAction",
	"CEventStatChangedValue",
	"CEventPlayerDeath",
	"CEventPedSeenDeadPed",
	"UNUSED_150",
	"UNUSED_151",
	"UNUSED_152",
	"CEventNetworkPlayerJoinScript",
	"CEventNetworkPlayerLeftScript",
	"CEventNetworkStorePlayerLeft",
	"CEventNetworkStartSession",
	"CEventNetworkEndSession",
	"CEventNetworkStartMatch",
	"UNUSED_159",
	"CEventNetworkRemovedFromSessionDueToStall",
	"CEventNetworkRemovedFromSessionDueToComplaints",
	"CEventNetworkConnectionTimeout",
	"CEventNetworkPedDroppedWeapon",
	"CEventNetworkPlayerSpawn",
	"CEventNetworkPlayerCollectedPickup",
	"CEventNetworkPlayerCollectedAmbientPickup",
	"CEventNetworkPlayerCollectedPortablePickup",
	"CEventNetworkPlayerDroppedPortablePickup",
	"UNUSED_169",
	"CEventNetworkInviteAccepted",
	"CEventNetworkInviteConfirmed",
	"CEventNetworkInviteRejected",
	"CEventNetworkSummon",
	"CEventNetworkScriptEvent",
	"UNUSED_175",
	"CEventNetworkSignInStateChanged",
	"CEventNetworkRosChanged",
	"CEventNetworkBail",
	"CEventNetworkHostMigration",
	"CEventNetworkFindSession",
	"CEventNetworkHostSession",
	"CEventNetworkJoinSession",
	"CEventNetworkJoinSessionResponse",
	"CEventNetworkCheatTriggered",
	"CEventNetworkEntityDamage",
	"CEventNetworkPlayerArrest",
	"CEventNetworkTimedExplosion",
	"CEventNetworkPrimaryClanChanged",
	"CEventNetworkClanJoined",
	"CEventNetworkClanLeft",
	"CEventNetworkClanInviteReceived",
	"CEventNetworkVoiceSessionStarted",
	"CEventNetworkVoiceSessionEnded",
	"CEventNetworkVoiceConnectionRequested",
	"CEventNetworkVoiceConnectionResponse",
	"CEventNetworkVoiceConnectionTerminated",
	"CEventNetworkTextMessageReceived",
	"CEventNetworkCloudFileResponse",
	"CEventNetworkPickupRespawned",
	"CEventNetworkPresence_StatUpdate",
	"CEventNetworkPedLeftBehind",
	"CEventNetwork_InboxMsgReceived",
	"CEventNetworkAttemptHostMigration",
	"CEventNetworkIncrementStat",
	"CEventNetworkSessionEvent",
	"CEventNetworkTransitionStarted",
	"CEventNetworkTransitionEvent",
	"CEventNetworkTransitionMemberJoined",
	"CEventNetworkTransitionMemberLeft",
	"CEventNetworkTransitionParameterChanged",
	"CEventNetworkClanKicked",
	"CEventNetworkTransitionStringChanged",
	"CEventNetworkTransitionGamerInstruction",
	"CEventNetworkPresenceInvite",
	"CEventNetworkPresenceInviteRemoved",
	"CEventNetworkPresenceInviteReply",
	"CEventNetworkCashTransactionLog",
	"CEventNetworkClanRankChanged",
	"CEventNetworkVehicleUndrivable",
	"CEventNetworkPresenceTriggerEvent",
	"CEventNetworkEmailReceivedEvent",
	"CEventNetworkFollowInviteReceived",
	"CEventNetworkAdminInvited",
	"CEventNetworkSpectateLocal",
	"CEventNetworkCloudEvent",
	"CEventNetworkShopTransaction",
	"UNUSED_227",
	"UNUSED_228",
	"CEventNetworkOnlinePermissionsUpdated",
	"CEventNetworkSystemServiceEvent",
	"CEventNetworkRequestDelay",
	"CEventNetworkSocialClubAccountLinked",
	"CEventNetworkScAdminPlayerUpdated",
	"CEventNetworkScAdminReceivedCash",
	"CEventNetworkClanInviteRequestReceived",
	"CEventNetworkMarketingEmailReceivedEvent",
	"CEventNetworkStuntPerformed",
	"CEventNetworkFiredDummyProjectile",
	"CEventNetworkPlayerEnteredVehicle",
	"CEventNetworkPlayerActivatedSpecialAbility",
	"CEventNetworkPlayerDeactivatedSpecialAbility",
	"CEventNetworkPlayerSpecialAbilityFailedActivation",
	"CEventNetworkFiredVehicleProjectile",
	"UNUSED_244",
	"UNUSED_245",
	"CEventErrorUnknownError",
	"CEventErrorArrayOverflow",
	"CEventErrorInstructionLimit"
};

static const char* g_eventNames2612[251]
{
	"UNUSED_0",
	"UNUSED_1",
	"UNUSED_2",
	"UNUSED_3",
	"UNUSED_4",
	"UNUSED_5",
	"CEventAgitated",
	"CEventAgitatedAction",
	"CEventEncroachingPed",
	"CEventCallForCover",
	"CEventCarUndriveable",
	"CEventClimbLadderOnRoute",
	"CEventClimbNavMeshOnRoute",
	"CEventAcquaintancePedDead",
	"CEventCommunicateEvent",
	"CEventCopCarBeingStolen",
	"CEventCrimeReported",
	"CEventDamage",
	"CEventDeadPedFound",
	"CEventDeath",
	"CEventDraggedOutCar",
	"UNUSED_21",
	"CEventAcquaintancePedLike",
	"CEventExplosionHeard",
	"CEventAcquaintancePedDislike",
	"UNUSED_25",
	"CEventFootStepHeard",
	"CEventGetOutOfWater",
	"CEventGivePedTask",
	"CEventGunAimedAt",
	"CEventHelpAmbientFriend",
	"CEventInjuredCryForHelp",
	"CEventCrimeCryForHelp",
	"CEventInAir",
	"UNUSED_34",
	"UNUSED_35",
	"CEventAcquaintancePedWanted",
	"UNUSED_37",
	"CEventLeaderExitedCarAsDriver",
	"CEventLeaderHolsteredWeapon",
	"CEventLeaderLeftCover",
	"CEventLeaderUnholsteredWeapon",
	"CEventMeleeAction",
	"CEventMustLeaveBoat",
	"CEventNewTask",
	"UNUSED_45",
	"CEventObjectCollision",
	"CEventOnFire",
	"CEventOpenDoor",
	"CEventShovePed",
	"CEventPedCollisionWithPed",
	"CEventPedCollisionWithPlayer",
	"CEventPedEnteredMyVehicle",
	"CEventPedJackingMyVehicle",
	"CEventPedOnCarRoof",
	"UNUSED_55",
	"UNUSED_56",
	"CEventPlayerCollisionWithPed",
	"UNUSED_58",
	"CEventPotentialBeWalkedInto",
	"CEventPotentialBlast",
	"CEventPotentialGetRunOver",
	"UNUSED_62",
	"UNUSED_63",
	"CEventPotentialWalkIntoVehicle",
	"CEventProvidingCover",
	"UNUSED_66",
	"CEventRanOverPed",
	"UNUSED_68",
	"CEventReactionEnemyPed",
	"CEventReactionInvestigateDeadPed",
	"CEventReactionInvestigateThreat",
	"CEventRequestHelpWithConfrontation",
	"CEventRespondedToThreat",
	"UNUSED_74",
	"CEventScriptCommand",
	"CEventShockingBrokenGlass",
	"CEventShockingCarAlarm",
	"CEventShockingCarChase",
	"CEventShockingCarCrash",
	"CEventShockingBicycleCrash",
	"CEventShockingCarPileUp",
	"CEventShockingCarOnCar",
	"CEventShockingDangerousAnimal",
	"CEventShockingDeadBody",
	"CEventShockingDrivingOnPavement",
	"CEventShockingBicycleOnPavement",
	"CEventShockingEngineRevved",
	"CEventShockingExplosion",
	"CEventShockingFire",
	"CEventShockingGunFight",
	"CEventShockingGunshotFired",
	"CEventShockingHelicopterOverhead",
	"CEventShockingParachuterOverhead",
	"CEventShockingPedKnockedIntoByPlayer",
	"CEventShockingHornSounded",
	"CEventShockingInDangerousVehicle",
	"CEventShockingInjuredPed",
	"CEventShockingMadDriver",
	"CEventShockingMadDriverExtreme",
	"CEventShockingMadDriverBicycle",
	"CEventShockingMugging",
	"CEventShockingNonViolentWeaponAimedAt",
	"CEventShockingPedRunOver",
	"CEventShockingPedShot",
	"CEventShockingPlaneFlyby",
	"CEventShockingPotentialBlast",
	"CEventShockingPropertyDamage",
	"CEventShockingRunningPed",
	"CEventShockingRunningStampede",
	"CEventShockingSeenCarStolen",
	"CEventShockingSeenConfrontation",
	"CEventShockingSeenGangFight",
	"CEventShockingSeenInsult",
	"CEventShockingSeenMeleeAction",
	"CEventShockingSeenNiceCar",
	"CEventShockingSeenPedKilled",
	"CEventShockingVehicleTowed",
	"CEventShockingWeaponThreat",
	"CEventShockingWeirdPed",
	"CEventShockingWeirdPedApproaching",
	"CEventShockingSiren",
	"CEventShockingStudioBomb",
	"CEventShockingVisibleWeapon",
	"CEventGunShot",
	"CEventGunShotBulletImpact",
	"CEventGunShotWhizzedBy",
	"CEventFriendlyAimedAt",
	"CEventFriendlyFireNearMiss",
	"CEventShoutBlockingLos",
	"CEventShoutTargetPosition",
	"CEventStaticCountReachedMax",
	"CEventStuckInAir",
	"CEventSuspiciousActivity",
	"CEventSwitch2NM",
	"CEventUnidentifiedPed",
	"CEventVehicleCollision",
	"CEventVehicleDamageWeapon",
	"CEventVehicleOnFire",
	"UNUSED_139",
	"CEventDisturbance",
	"CEventEntityDamaged",
	"CEventEntityDestroyed",
	"CEventWrithe",
	"CEventHurtTransition",
	"CEventPlayerUnableToEnterVehicle",
	"CEventScenarioForceAction",
	"CEventStatChangedValue",
	"CEventPlayerDeath",
	"CEventPedSeenDeadPed",
	"UNUSED_150",
	"UNUSED_151",
	"UNUSED_152",
	"CEventNetworkPlayerJoinScript",
	"CEventNetworkPlayerLeftScript",
	"CEventNetworkStorePlayerLeft",
	"CEventNetworkStartSession",
	"CEventNetworkEndSession",
	"CEventNetworkStartMatch",
	"UNUSED_159",
	"CEventNetworkRemovedFromSessionDueToStall",
	"CEventNetworkRemovedFromSessionDueToComplaints",
	"CEventNetworkConnectionTimeout",
	"CEventNetworkPedDroppedWeapon",
	"CEventNetworkPlayerSpawn",
	"CEventNetworkPlayerCollectedPickup",
	"CEventNetworkPlayerCollectedAmbientPickup",
	"CEventNetworkPlayerCollectedPortablePickup",
	"CEventNetworkPlayerDroppedPortablePickup",
	"UNUSED_169",
	"CEventNetworkInviteAccepted",
	"CEventNetworkInviteConfirmed",
	"CEventNetworkInviteRejected",
	"CEventNetworkSummon",
	"CEventNetworkScriptEvent",
	"UNUSED_175",
	"CEventNetworkSignInStateChanged",
	"CEventNetworkSignInChangeActioned",
	"CEventNetworkRosChanged",
	"CEventNetworkBail",
	"CEventNetworkHostMigration",
	"CEventNetworkFindSession",
	"CEventNetworkHostSession",
	"CEventNetworkJoinSession",
	"CEventNetworkJoinSessionResponse",
	"CEventNetworkCheatTriggered",
	"CEventNetworkEntityDamage",
	"CEventNetworkPlayerArrest",
	"CEventNetworkTimedExplosion",
	"CEventNetworkPrimaryClanChanged",
	"CEventNetworkClanJoined",
	"CEventNetworkClanLeft",
	"CEventNetworkClanInviteReceived",
	"CEventNetworkVoiceSessionStarted",
	"CEventNetworkVoiceSessionEnded",
	"CEventNetworkVoiceConnectionRequested",
	"CEventNetworkVoiceConnectionResponse",
	"CEventNetworkVoiceConnectionTerminated",
	"CEventNetworkTextMessageReceived",
	"CEventNetworkCloudFileResponse",
	"CEventNetworkPickupRespawned",
	"CEventNetworkPresence_StatUpdate",
	"CEventNetworkPedLeftBehind",
	"CEventNetwork_InboxMsgReceived",
	"CEventNetworkAttemptHostMigration",
	"CEventNetworkIncrementStat",
	"CEventNetworkSessionEvent",
	"CEventNetworkTransitionStarted",
	"CEventNetworkTransitionEvent",
	"CEventNetworkTransitionMemberJoined",
	"CEventNetworkTransitionMemberLeft",
	"CEventNetworkTransitionParameterChanged",
	"CEventNetworkClanKicked",
	"CEventNetworkTransitionStringChanged",
	"CEventNetworkTransitionGamerInstruction",
	"CEventNetworkPresenceInvite",
	"CEventNetworkPresenceInviteRemoved",
	"CEventNetworkPresenceInviteReply",
	"CEventNetworkCashTransactionLog",
	"CEventNetworkClanRankChanged",
	"CEventNetworkVehicleUndrivable",
	"CEventNetworkPresenceTriggerEvent",
	"CEventNetworkEmailReceivedEvent",
	"CEventNetworkFollowInviteReceived",
	"UNUSED_224",
	"CEventNetworkSpectateLocal",
	"CEventNetworkCloudEvent",
	"CEventNetworkShopTransaction",
	"UNUSED_228",
	"UNUSED_229",
	"CEventNetworkOnlinePermissionsUpdated",
	"CEventNetworkSystemServiceEvent",
	"CEventNetworkRequestDelay",
	"CEventNetworkSocialClubAccountLinked",
	"CEventNetworkScAdminPlayerUpdated",
	"CEventNetworkScAdminReceivedCash",
	"CEventNetworkClanInviteRequestReceived",
	"CEventNetworkMarketingEmailReceivedEvent",
	"CEventNetworkStuntPerformed",
	"CEventNetworkFiredDummyProjectile",
	"CEventNetworkPlayerEnteredVehicle",
	"CEventNetworkPlayerActivatedSpecialAbility",
	"CEventNetworkPlayerDeactivatedSpecialAbility",
	"CEventNetworkPlayerSpecialAbilityFailedActivation",
	"CEventNetworkFiredVehicleProjectile",
	"UNUSED_245",
	"UNUSED_246",
	"UNUSED_247",
	"CEventErrorUnknownError",
	"CEventErrorArrayOverflow",
	"CEventErrorInstructionLimit",
};

static const char* g_eventNames3258[252]
{
	"UNUSED_0",
	"UNUSED_1",
	"UNUSED_2",
	"UNUSED_3",
	"UNUSED_4",
	"UNUSED_5",
	"CEventAgitated",
	"CEventAgitatedAction",
	"CEventEncroachingPed",
	"CEventCallForCover",
	"CEventCarUndriveable",
	"CEventClimbLadderOnRoute",
	"CEventClimbNavMeshOnRoute",
	"CEventAcquaintancePedDead",
	"CEventCommunicateEvent",
	"CEventCopCarBeingStolen",
	"CEventCrimeReported",
	"CEventDamage",
	"CEventDeadPedFound",
	"CEventDeath",
	"CEventDraggedOutCar",
	"UNUSED_21",
	"CEventAcquaintancePedLike",
	"CEventExplosionHeard",
	"CEventAcquaintancePedDislike",
	"UNUSED_25",
	"CEventFootStepHeard",
	"CEventGetOutOfWater",
	"CEventGivePedTask",
	"CEventGunAimedAt",
	"CEventHelpAmbientFriend",
	"CEventInjuredCryForHelp",
	"CEventCrimeCryForHelp",
	"CEventInAir",
	"UNUSED_34",
	"UNUSED_35",
	"CEventAcquaintancePedWanted",
	"UNUSED_37",
	"CEventLeaderExitedCarAsDriver",
	"CEventLeaderHolsteredWeapon",
	"CEventLeaderLeftCover",
	"CEventLeaderUnholsteredWeapon",
	"CEventMeleeAction",
	"CEventMustLeaveBoat",
	"CEventNewTask",
	"UNUSED_45",
	"CEventObjectCollision",
	"CEventOnFire",
	"CEventOpenDoor",
	"CEventShovePed",
	"CEventPedCollisionWithPed",
	"CEventPedCollisionWithPlayer",
	"CEventPedEnteredMyVehicle",
	"CEventPedJackingMyVehicle",
	"CEventPedOnCarRoof",
	"UNUSED_55",
	"UNUSED_56",
	"CEventPlayerCollisionWithPed",
	"UNUSED_58",
	"CEventPotentialBeWalkedInto",
	"CEventPotentialBlast",
	"CEventPotentialGetRunOver",
	"UNUSED_62",
	"UNUSED_63",
	"CEventPotentialWalkIntoVehicle",
	"CEventProvidingCover",
	"UNUSED_66",
	"CEventRanOverPed",
	"UNUSED_68",
	"CEventReactionEnemyPed",
	"CEventReactionInvestigateDeadPed",
	"CEventReactionInvestigateThreat",
	"CEventRequestHelpWithConfrontation",
	"CEventRespondedToThreat",
	"UNUSED_74",
	"CEventScriptCommand",
	"CEventShockingBrokenGlass",
	"CEventShockingCarAlarm",
	"CEventShockingCarChase",
	"CEventShockingCarCrash",
	"CEventShockingBicycleCrash",
	"CEventShockingCarPileUp",
	"CEventShockingCarOnCar",
	"CEventShockingDangerousAnimal",
	"CEventShockingDeadBody",
	"CEventShockingDrivingOnPavement",
	"CEventShockingBicycleOnPavement",
	"CEventShockingEngineRevved",
	"CEventShockingExplosion",
	"CEventShockingFire",
	"CEventShockingGunFight",
	"CEventShockingGunshotFired",
	"CEventShockingHelicopterOverhead",
	"CEventShockingParachuterOverhead",
	"CEventShockingPedKnockedIntoByPlayer",
	"CEventShockingHornSounded",
	"CEventShockingInDangerousVehicle",
	"CEventShockingInjuredPed",
	"CEventShockingMadDriver",
	"CEventShockingMadDriverExtreme",
	"CEventShockingMadDriverBicycle",
	"CEventShockingMugging",
	"CEventShockingNonViolentWeaponAimedAt",
	"CEventShockingPedRunOver",
	"CEventShockingPedShot",
	"CEventShockingPlaneFlyby",
	"CEventShockingPotentialBlast",
	"CEventShockingPropertyDamage",
	"CEventShockingRunningPed",
	"CEventShockingRunningStampede",
	"CEventShockingSeenCarStolen",
	"CEventShockingSeenConfrontation",
	"CEventShockingSeenGangFight",
	"CEventShockingSeenInsult",
	"CEventShockingSeenMeleeAction",
	"CEventShockingSeenNiceCar",
	"CEventShockingSeenPedKilled",
	"CEventShockingVehicleTowed",
	"CEventShockingWeaponThreat",
	"CEventShockingWeirdPed",
	"CEventShockingWeirdPedApproaching",
	"CEventShockingSiren",
	"CEventShockingStudioBomb",
	"CEventShockingVisibleWeapon",
	"CEventGunShot",
	"CEventGunShotBulletImpact",
	"CEventGunShotWhizzedBy",
	"CEventFriendlyAimedAt",
	"CEventFriendlyFireNearMiss",
	"CEventShoutBlockingLos",
	"CEventShoutTargetPosition",
	"CEventStaticCountReachedMax",
	"CEventStuckInAir",
	"CEventSuspiciousActivity",
	"CEventSwitch2NM",
	"CEventUnidentifiedPed",
	"CEventVehicleCollision",
	"CEventVehicleDamageWeapon",
	"CEventVehicleOnFire",
	"UNUSED_139",
	"CEventDisturbance",
	"CEventEntityDamaged",
	"CEventEntityDestroyed",
	"CEventWrithe",
	"CEventHurtTransition",
	"CEventPlayerUnableToEnterVehicle",
	"CEventScenarioForceAction",
	"CEventStatChangedValue",
	"CEventPlayerDeath",
	"CEventPedSeenDeadPed",
	"UNUSED_150",
	"UNUSED_151",
	"UNUSED_152",
	"CEventNetworkPlayerJoinScript",
	"CEventNetworkPlayerLeftScript",
	"CEventNetworkStorePlayerLeft",
	"CEventNetworkStartSession",
	"CEventNetworkEndSession",
	"CEventNetworkStartMatch",
	"UNUSED_159",
	"CEventNetworkRemovedFromSessionDueToStall",
	"CEventNetworkRemovedFromSessionDueToComplaints",
	"CEventNetworkConnectionTimeout",
	"CEventNetworkPedDroppedWeapon",
	"CEventNetworkPlayerSpawn",
	"CEventNetworkPlayerCollectedPickup",
	"CEventNetworkPlayerCollectedAmbientPickup",
	"CEventNetworkPlayerCollectedPortablePickup",
	"CEventNetworkPlayerDroppedPortablePickup",
	"UNUSED_169",
	"CEventNetworkInviteAccepted",
	"CEventNetworkInviteConfirmed",
	"CEventNetworkInviteRejected",
	"CEventNetworkSummon",
	"CEventNetworkScriptEvent",
	"UNUSED_175",
	"CEventNetworkSignInStateChanged",
	"CEventNetworkSignInChangeActioned",
	"CEventNetworkRosChanged",
	"CEventNetworkBail",
	"CEventNetworkHostMigration",
	"CEventNetworkFindSession",
	"CEventNetworkHostSession",
	"CEventNetworkJoinSession",
	"CEventNetworkJoinSessionResponse",
	"CEventNetworkCheatTriggered",
	"CEventNetworkEntityDamage",
	"CEventNetworkPlayerArrest",
	"CEventNetworkTimedExplosion",
	"CEventNetworkPrimaryClanChanged",
	"CEventNetworkClanJoined",
	"CEventNetworkClanLeft",
	"CEventNetworkClanInviteReceived",
	"CEventNetworkVoiceSessionStarted",
	"CEventNetworkVoiceSessionEnded",
	"CEventNetworkVoiceConnectionRequested",
	"CEventNetworkVoiceConnectionResponse",
	"CEventNetworkVoiceConnectionTerminated",
	"CEventNetworkTextMessageReceived",
	"CEventNetworkCloudFileResponse",
	"CEventNetworkPickupRespawned",
	"CEventNetworkPresence_StatUpdate",
	"CEventNetworkPedLeftBehind",
	"CEventNetwork_InboxMsgReceived",
	"CEventNetworkAttemptHostMigration",
	"CEventNetworkIncrementStat",
	"CEventNetworkSessionEvent",
	"CEventNetworkTransitionStarted",
	"CEventNetworkTransitionEvent",
	"CEventNetworkTransitionMemberJoined",
	"CEventNetworkTransitionMemberLeft",
	"CEventNetworkTransitionParameterChanged",
	"CEventNetworkClanKicked",
	"CEventNetworkTransitionStringChanged",
	"CEventNetworkTransitionGamerInstruction",
	"CEventNetworkPresenceInvite",
	"CEventNetworkPresenceInviteRemoved",
	"CEventNetworkPresenceInviteReply",
	"CEventNetworkCashTransactionLog",
	"CEventNetworkClanRankChanged",
	"CEventNetworkVehicleUndrivable",
	"CEventNetworkPresenceTriggerEvent",
	"CEventNetworkEmailReceivedEvent",
	"CEventNetworkFollowInviteReceived",
	"UNUSED_224",
	"CEventNetworkSpectateLocal",
	"CEventNetworkCloudEvent",
	"CEventNetworkShopTransaction",
	"UNUSED_228",
	"UNUSED_229",
	"CEventNetworkOnlinePermissionsUpdated",
	"CEventNetworkSystemServiceEvent",
	"CEventNetworkRequestDelay",
	"UNUSED_233",
	"CEventNetworkSocialClubAccountLinked",
	"CEventNetworkScAdminPlayerUpdated",
	"CEventNetworkScAdminReceivedCash",
	"CEventNetworkClanInviteRequestReceived",
	"CEventNetworkMarketingEmailReceivedEvent",
	"CEventNetworkStuntPerformed",
	"CEventNetworkFiredDummyProjectile",
	"CEventNetworkPlayerEnteredVehicle",
	"CEventNetworkPlayerActivatedSpecialAbility",
	"CEventNetworkPlayerDeactivatedSpecialAbility",
	"CEventNetworkPlayerSpecialAbilityFailedActivation",
	"CEventNetworkFiredVehicleProjectile",
	"UNUSED_245",
	"UNUSED_246",
	"UNUSED_247",
	"CEventErrorUnknownError",
	"CEventErrorArrayOverflow",
	"CEventErrorInstructionLimit",
};
