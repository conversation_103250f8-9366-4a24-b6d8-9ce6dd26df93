// Copyright 2014 The Chromium Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     base/android/jni_generator/jni_generator.py
// For
//     org/chromium/TestJni

#ifndef org_chromium_TestJni_JNI
#define org_chromium_TestJni_JNI

#include <jni.h>

#include "base/android/jni_generator/jni_generator_helper.h"


// Step 1: Forward declarations.

JNI_REGISTRATION_EXPORT extern const char kClassPath_org_chromium_TestJni[];
const char kClassPath_org_chromium_TestJni[] = "org/chromium/TestJni";

JNI_REGISTRATION_EXPORT extern const char kClassPath_org_chromium_TestJni_00024InfoBar[];
const char kClassPath_org_chromium_TestJni_00024InfoBar[] = "org/chromium/TestJni$InfoBar";
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_REGISTRATION_EXPORT std::atomic<jclass> g_org_chromium_TestJni_clazz(nullptr);
#ifndef org_chromium_TestJni_clazz_defined
#define org_chromium_TestJni_clazz_defined
inline jclass org_chromium_TestJni_clazz(JNIEnv* env) {
  return base::android::LazyGetClass(env, kClassPath_org_chromium_TestJni,
      &g_org_chromium_TestJni_clazz);
}
#endif
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_REGISTRATION_EXPORT std::atomic<jclass> g_org_chromium_TestJni_00024InfoBar_clazz(nullptr);
#ifndef org_chromium_TestJni_00024InfoBar_clazz_defined
#define org_chromium_TestJni_00024InfoBar_clazz_defined
inline jclass org_chromium_TestJni_00024InfoBar_clazz(JNIEnv* env) {
  return base::android::LazyGetClass(env, kClassPath_org_chromium_TestJni_00024InfoBar,
      &g_org_chromium_TestJni_00024InfoBar_clazz);
}
#endif


// Step 2: Constants (optional).


// Step 3: Method stubs.

static std::atomic<jmethodID> g_org_chromium_TestJni_showConfirmInfoBar(nullptr);
static base::android::ScopedJavaLocalRef<jobject> Java_TestJni_showConfirmInfoBar(JNIEnv* env, const
    base::android::JavaRef<jobject>& obj, JniIntWrapper nativeInfoBar,
    const base::android::JavaRef<jstring>& buttonOk,
    const base::android::JavaRef<jstring>& buttonCancel,
    const base::android::JavaRef<jstring>& title,
    const base::android::JavaRef<jobject>& icon) {
  jclass clazz = org_chromium_TestJni_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_chromium_TestJni_clazz(env), NULL);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "showConfirmInfoBar",
"(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Landroid/graphics/Bitmap;)Lorg/chromium/Foo$InnerClass;",
          &g_org_chromium_TestJni_showConfirmInfoBar);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id, as_jint(nativeInfoBar), buttonOk.obj(), buttonCancel.obj(),
              title.obj(), icon.obj());
  return base::android::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_org_chromium_TestJni_showAutoLoginInfoBar(nullptr);
static base::android::ScopedJavaLocalRef<jobject> Java_TestJni_showAutoLoginInfoBar(JNIEnv* env,
    const base::android::JavaRef<jobject>& obj, JniIntWrapper nativeInfoBar,
    const base::android::JavaRef<jstring>& realm,
    const base::android::JavaRef<jstring>& account,
    const base::android::JavaRef<jstring>& args) {
  jclass clazz = org_chromium_TestJni_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_chromium_TestJni_clazz(env), NULL);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "showAutoLoginInfoBar",
          "(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;)Lorg/chromium/Foo$InnerClass;",
          &g_org_chromium_TestJni_showAutoLoginInfoBar);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id, as_jint(nativeInfoBar), realm.obj(), account.obj(),
              args.obj());
  return base::android::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_org_chromium_TestJni_00024InfoBar_dismiss(nullptr);
static void Java_InfoBar_dismiss(JNIEnv* env, const base::android::JavaRef<jobject>& obj) {
  jclass clazz = org_chromium_TestJni_00024InfoBar_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_chromium_TestJni_00024InfoBar_clazz(env));

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "dismiss",
          "()V",
          &g_org_chromium_TestJni_00024InfoBar_dismiss);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id);
}

static std::atomic<jmethodID> g_org_chromium_TestJni_shouldShowAutoLogin(nullptr);
static jboolean Java_TestJni_shouldShowAutoLogin(JNIEnv* env, const base::android::JavaRef<jobject>&
    view,
    const base::android::JavaRef<jstring>& realm,
    const base::android::JavaRef<jstring>& account,
    const base::android::JavaRef<jstring>& args) {
  jclass clazz = org_chromium_TestJni_clazz(env);
  CHECK_CLAZZ(env, clazz,
      org_chromium_TestJni_clazz(env), false);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "shouldShowAutoLogin",
          "(Landroid/view/View;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Z",
          &g_org_chromium_TestJni_shouldShowAutoLogin);

  jboolean ret =
      env->CallStaticBooleanMethod(clazz,
          call_context.base.method_id, view.obj(), realm.obj(), account.obj(), args.obj());
  return ret;
}

static std::atomic<jmethodID> g_org_chromium_TestJni_openUrl(nullptr);
static base::android::ScopedJavaLocalRef<jobject> Java_TestJni_openUrl(JNIEnv* env, const
    base::android::JavaRef<jstring>& url) {
  jclass clazz = org_chromium_TestJni_clazz(env);
  CHECK_CLAZZ(env, clazz,
      org_chromium_TestJni_clazz(env), NULL);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "openUrl",
          "(Ljava/lang/String;)Ljava/io/InputStream;",
          &g_org_chromium_TestJni_openUrl);

  jobject ret =
      env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, url.obj());
  return base::android::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_org_chromium_TestJni_activateHardwareAcceleration(nullptr);
static void Java_TestJni_activateHardwareAcceleration(JNIEnv* env, const
    base::android::JavaRef<jobject>& obj, jboolean activated,
    JniIntWrapper iPid,
    JniIntWrapper iType,
    JniIntWrapper iPrimaryID,
    JniIntWrapper iSecondaryID) {
  jclass clazz = org_chromium_TestJni_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_chromium_TestJni_clazz(env));

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "activateHardwareAcceleration",
          "(ZIIII)V",
          &g_org_chromium_TestJni_activateHardwareAcceleration);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id, activated, as_jint(iPid), as_jint(iType),
              as_jint(iPrimaryID), as_jint(iSecondaryID));
}

static std::atomic<jmethodID> g_org_chromium_TestJni_updateStatus(nullptr);
static jint Java_TestJni_updateStatus(JNIEnv* env, JniIntWrapper status) {
  jclass clazz = org_chromium_TestJni_clazz(env);
  CHECK_CLAZZ(env, clazz,
      org_chromium_TestJni_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "updateStatus",
          "(I)I",
          &g_org_chromium_TestJni_updateStatus);

  jint ret =
      env->CallStaticIntMethod(clazz,
          call_context.base.method_id, as_jint(status));
  return ret;
}

static std::atomic<jmethodID> g_org_chromium_TestJni_uncheckedCall(nullptr);
static void Java_TestJni_uncheckedCall(JNIEnv* env, const base::android::JavaRef<jobject>& obj,
    JniIntWrapper iParam) {
  jclass clazz = org_chromium_TestJni_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_chromium_TestJni_clazz(env));

  jni_generator::JniJavaCallContextUnchecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "uncheckedCall",
          "(I)V",
          &g_org_chromium_TestJni_uncheckedCall);

     env->CallVoidMethod(obj.obj(),
          call_context.method_id, as_jint(iParam));
}

static std::atomic<jmethodID> g_org_chromium_TestJni_returnByteArray(nullptr);
static base::android::ScopedJavaLocalRef<jbyteArray> Java_TestJni_returnByteArray(JNIEnv* env, const
    base::android::JavaRef<jobject>& obj) {
  jclass clazz = org_chromium_TestJni_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_chromium_TestJni_clazz(env), NULL);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "returnByteArray",
          "()[B",
          &g_org_chromium_TestJni_returnByteArray);

  jbyteArray ret =
      static_cast<jbyteArray>(env->CallObjectMethod(obj.obj(),
          call_context.base.method_id));
  return base::android::ScopedJavaLocalRef<jbyteArray>(env, ret);
}

static std::atomic<jmethodID> g_org_chromium_TestJni_returnBooleanArray(nullptr);
static base::android::ScopedJavaLocalRef<jbooleanArray> Java_TestJni_returnBooleanArray(JNIEnv* env,
    const base::android::JavaRef<jobject>& obj) {
  jclass clazz = org_chromium_TestJni_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_chromium_TestJni_clazz(env), NULL);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "returnBooleanArray",
          "()[Z",
          &g_org_chromium_TestJni_returnBooleanArray);

  jbooleanArray ret =
      static_cast<jbooleanArray>(env->CallObjectMethod(obj.obj(),
          call_context.base.method_id));
  return base::android::ScopedJavaLocalRef<jbooleanArray>(env, ret);
}

static std::atomic<jmethodID> g_org_chromium_TestJni_returnCharArray(nullptr);
static base::android::ScopedJavaLocalRef<jcharArray> Java_TestJni_returnCharArray(JNIEnv* env, const
    base::android::JavaRef<jobject>& obj) {
  jclass clazz = org_chromium_TestJni_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_chromium_TestJni_clazz(env), NULL);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "returnCharArray",
          "()[C",
          &g_org_chromium_TestJni_returnCharArray);

  jcharArray ret =
      static_cast<jcharArray>(env->CallObjectMethod(obj.obj(),
          call_context.base.method_id));
  return base::android::ScopedJavaLocalRef<jcharArray>(env, ret);
}

static std::atomic<jmethodID> g_org_chromium_TestJni_returnShortArray(nullptr);
static base::android::ScopedJavaLocalRef<jshortArray> Java_TestJni_returnShortArray(JNIEnv* env,
    const base::android::JavaRef<jobject>& obj) {
  jclass clazz = org_chromium_TestJni_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_chromium_TestJni_clazz(env), NULL);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "returnShortArray",
          "()[S",
          &g_org_chromium_TestJni_returnShortArray);

  jshortArray ret =
      static_cast<jshortArray>(env->CallObjectMethod(obj.obj(),
          call_context.base.method_id));
  return base::android::ScopedJavaLocalRef<jshortArray>(env, ret);
}

static std::atomic<jmethodID> g_org_chromium_TestJni_returnIntArray(nullptr);
static base::android::ScopedJavaLocalRef<jintArray> Java_TestJni_returnIntArray(JNIEnv* env, const
    base::android::JavaRef<jobject>& obj) {
  jclass clazz = org_chromium_TestJni_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_chromium_TestJni_clazz(env), NULL);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "returnIntArray",
          "()[I",
          &g_org_chromium_TestJni_returnIntArray);

  jintArray ret =
      static_cast<jintArray>(env->CallObjectMethod(obj.obj(),
          call_context.base.method_id));
  return base::android::ScopedJavaLocalRef<jintArray>(env, ret);
}

static std::atomic<jmethodID> g_org_chromium_TestJni_returnLongArray(nullptr);
static base::android::ScopedJavaLocalRef<jlongArray> Java_TestJni_returnLongArray(JNIEnv* env, const
    base::android::JavaRef<jobject>& obj) {
  jclass clazz = org_chromium_TestJni_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_chromium_TestJni_clazz(env), NULL);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "returnLongArray",
          "()[J",
          &g_org_chromium_TestJni_returnLongArray);

  jlongArray ret =
      static_cast<jlongArray>(env->CallObjectMethod(obj.obj(),
          call_context.base.method_id));
  return base::android::ScopedJavaLocalRef<jlongArray>(env, ret);
}

static std::atomic<jmethodID> g_org_chromium_TestJni_returnDoubleArray(nullptr);
static base::android::ScopedJavaLocalRef<jdoubleArray> Java_TestJni_returnDoubleArray(JNIEnv* env,
    const base::android::JavaRef<jobject>& obj) {
  jclass clazz = org_chromium_TestJni_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_chromium_TestJni_clazz(env), NULL);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "returnDoubleArray",
          "()[D",
          &g_org_chromium_TestJni_returnDoubleArray);

  jdoubleArray ret =
      static_cast<jdoubleArray>(env->CallObjectMethod(obj.obj(),
          call_context.base.method_id));
  return base::android::ScopedJavaLocalRef<jdoubleArray>(env, ret);
}

static std::atomic<jmethodID> g_org_chromium_TestJni_returnObjectArray(nullptr);
static base::android::ScopedJavaLocalRef<jobjectArray> Java_TestJni_returnObjectArray(JNIEnv* env,
    const base::android::JavaRef<jobject>& obj) {
  jclass clazz = org_chromium_TestJni_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_chromium_TestJni_clazz(env), NULL);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "returnObjectArray",
          "()[Ljava/lang/Object;",
          &g_org_chromium_TestJni_returnObjectArray);

  jobjectArray ret =
      static_cast<jobjectArray>(env->CallObjectMethod(obj.obj(),
          call_context.base.method_id));
  return base::android::ScopedJavaLocalRef<jobjectArray>(env, ret);
}

static std::atomic<jmethodID> g_org_chromium_TestJni_returnArrayOfByteArray(nullptr);
static base::android::ScopedJavaLocalRef<jobjectArray> Java_TestJni_returnArrayOfByteArray(JNIEnv*
    env, const base::android::JavaRef<jobject>& obj) {
  jclass clazz = org_chromium_TestJni_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_chromium_TestJni_clazz(env), NULL);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "returnArrayOfByteArray",
          "()[[B",
          &g_org_chromium_TestJni_returnArrayOfByteArray);

  jobjectArray ret =
      static_cast<jobjectArray>(env->CallObjectMethod(obj.obj(),
          call_context.base.method_id));
  return base::android::ScopedJavaLocalRef<jobjectArray>(env, ret);
}

static std::atomic<jmethodID> g_org_chromium_TestJni_getCompressFormat(nullptr);
static base::android::ScopedJavaLocalRef<jobject> Java_TestJni_getCompressFormat(JNIEnv* env, const
    base::android::JavaRef<jobject>& obj) {
  jclass clazz = org_chromium_TestJni_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_chromium_TestJni_clazz(env), NULL);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getCompressFormat",
          "()Landroid/graphics/Bitmap$CompressFormat;",
          &g_org_chromium_TestJni_getCompressFormat);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return base::android::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_org_chromium_TestJni_getCompressFormatList(nullptr);
static base::android::ScopedJavaLocalRef<jobject> Java_TestJni_getCompressFormatList(JNIEnv* env,
    const base::android::JavaRef<jobject>& obj) {
  jclass clazz = org_chromium_TestJni_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_chromium_TestJni_clazz(env), NULL);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getCompressFormatList",
          "()Ljava/util/List;",
          &g_org_chromium_TestJni_getCompressFormatList);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return base::android::ScopedJavaLocalRef<jobject>(env, ret);
}

// Step 4: Generated test functions (optional).


#endif  // org_chromium_TestJni_JNI
