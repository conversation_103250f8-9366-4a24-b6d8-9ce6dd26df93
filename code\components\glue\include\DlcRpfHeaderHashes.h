#pragma once

/*
 * This file is autogenerated.
 * Do not edit it manually!
 */

#include "Sha256ByteUtils.h"

namespace cfx { namespace puremode {

Sha256Result dlcSafeHashesInit[] = {
    // update/x64/dlcpacks/mp2023_01/dlc.rpf
    ShaUnpack("e59190d9b1fe2a45a01914924870902d7e9589e82ae07ff6f7b1d58463b57477"), // dlc.rpf
    ShaUnpack("9e62b8877ac16976b6dd836f2f2e75c80ceff5ac87643dd45537254b2904b30f"), // dlc.rpf/x64/anim/creaturemetadata.rpf
    ShaUnpack("7d7608d0c5984a84ab524f78b5ed2a11c36886c2eddc2b4c82365130df803f4b"), // dlc.rpf/x64/anim/cutscene/cuts_sum23_cm1_int.rpf
    ShaUnpack("e3665982771d13dab31da9edf5ff82bc0ef7e955b1bf071be41f39e70a2131f8"), // dlc.rpf/x64/anim/cutscene/cuts_sum23_cm6_ext.rpf
    ShaUnpack("4d647cbfc7032606bb2916262fa2321d426587e5f4ad537dc7b884365f50cc33"), // dlc.rpf/x64/anim/ingame/clip_anim@.rpf
    ShaUnpack("25db859115a10c805fed7c88c78d12e5b6768b1d40ce39eaaf5e6e029831c174"), // dlc.rpf/x64/data/effects/ptfx.rpf
    ShaUnpack("d6e30712317594881da8867f0d954633b1d5a76f7a1cf299f6bd9edad24d2318"), // dlc.rpf/x64/data/lang/americandlc.rpf
    ShaUnpack("5732a5f69aeb4dd106dafb17db0ec0229d8e09007732028817fe6f0370c6a989"), // dlc.rpf/x64/data/lang/chinesedlc.rpf
    ShaUnpack("99ac0167d2a5e8a7bf9d7d07351abcf7979a5c1e6a213b14bfca5fc4e7676d8b"), // dlc.rpf/x64/data/lang/chinesesimpdlc.rpf
    ShaUnpack("6af98f81ca6b48cce1580f1a18711746f3df38fb9ff47744b4eb610d86fa387f"), // dlc.rpf/x64/data/lang/frenchdlc.rpf
    ShaUnpack("7f8384034a718d20d459ccd95fd4479f3f3a032e939b7f5178191e39ddb52c01"), // dlc.rpf/x64/data/lang/germandlc.rpf
    ShaUnpack("52ce075b2205506047edf78cdf140bf291dd939c8194e66e677d3a423814d01a"), // dlc.rpf/x64/data/lang/italiandlc.rpf
    ShaUnpack("d434924250e93982454ba0923b85b7f51b98c06c26c4a854071ce04aa1491ccd"), // dlc.rpf/x64/data/lang/japanesedlc.rpf
    ShaUnpack("191065a0ee6b871e31335eb70479d479600cb085935d937abdfa9dce330e9b3b"), // dlc.rpf/x64/data/lang/koreandlc.rpf
    ShaUnpack("fd4127cc73c6b9a76ca021bb1323143cdccb01010c5a19d82bea64e4ef7a68f4"), // dlc.rpf/x64/data/lang/mexicandlc.rpf
    ShaUnpack("f419b913230b0e751fa927ab3da285b75dbe0892ae355edfee75dc526f86abe9"), // dlc.rpf/x64/data/lang/polishdlc.rpf
    ShaUnpack("6b7c400b415b7d4d4eb60225a960de462c70c0892bf59a318cb2e95b5321d848"), // dlc.rpf/x64/data/lang/portuguesedlc.rpf
    ShaUnpack("628dd191145a22c7e137d892657e7bd885a1ec5db966d11dfe1fd401d3552588"), // dlc.rpf/x64/data/lang/russiandlc.rpf
    ShaUnpack("14211ea3a73f9910941360ada2b719501c2f245a0068728a99a08454fa459432"), // dlc.rpf/x64/data/lang/spanishdlc.rpf
    ShaUnpack("05712c2fde8c1589b165ff7520ea27e8bb097a1a33bd7137bab9a56bbf479377"), // dlc.rpf/x64/levels/gta5/navmeshes.rpf
    ShaUnpack("8159178326fe5d423a08125260222c8142052e5610e03d6c6865ac4e85d5649a"), // dlc.rpf/x64/levels/gta5/interiors/dlc_int_01_m23_1.rpf
    ShaUnpack("2c3de91a4ba3b2481adfc34a30ff3f145259be7a918ff8cde4dca66eb4f4cd0f"), // dlc.rpf/x64/levels/gta5/interiors/dlc_int_02_m23_1.rpf
    ShaUnpack("97c0ceeebcecf7ce7aff89dae8f7919e19f624d3fd6cababcb4d04071b7206a3"), // dlc.rpf/x64/levels/gta5/interiors/dlc_int_03_m23_1.rpf
    ShaUnpack("e08942493cdea1513fae36c59a760b0c219d9d796d22de54e81e4a8929ac27be"), // dlc.rpf/x64/levels/gta5/interiors/int_placement_m23_1.rpf
    ShaUnpack("41283db5b3f858e6f2d596b7d4384182af7b38f1161e89e5304e2a05ec5980fa"), // dlc.rpf/x64/levels/gta5/mp2023_01_additions/mp2023_01_additions.rpf
    ShaUnpack("5510abafd70ff2f252429448f4bb024251fdde120abe9be4027341443622bd41"), // dlc.rpf/x64/levels/gta5/mp2023_01_additions/mp2023_01_additions_metadata.rpf
    ShaUnpack("b14ea029bddc14e870a702ef730221e339a030543fbeefbe9f7e45ba6dc447f5"), // dlc.rpf/x64/levels/gta5/props/prop_m23_1_accs_01.rpf
    ShaUnpack("61b0125e234e7e7d7c045d37f947ab7a04fbccf3a0110897e07901c190013079"), // dlc.rpf/x64/levels/gta5/props/prop_m23_1_barge.rpf
    ShaUnpack("410da3af42138c9743d0010356aa5d67002be1abb231d2fac3b72cfc4dadf493"), // dlc.rpf/x64/levels/gta5/props/prop_m23_1_containers.rpf
    ShaUnpack("1ad28e98084806d5c03324c266930e5e612e55775adcd629fc5409f5af32a42c"), // dlc.rpf/x64/levels/gta5/props/prop_m23_1_crates.rpf
    ShaUnpack("73959cbaf03c32783b85fc91872c69b59434d1f75942ac2cf8fe1bb2045de435"), // dlc.rpf/x64/levels/gta5/props/prop_m23_1_deathmatch.rpf
    ShaUnpack("08022aa4090a74478c7edd229fdbedd4acb00fd1efccbac8ef3e4a9a3f685540"), // dlc.rpf/x64/levels/gta5/props/prop_m23_1_doors.rpf
    ShaUnpack("597b2de914a833b1ba2136ba46fa18724a00d44b2b3a05b3bc2ea4f598a6d04e"), // dlc.rpf/x64/levels/gta5/props/prop_m23_1_facility.rpf
    ShaUnpack("600aea0d8ce976d632efe5e228df74dcd1643f8d19b326ee23c01018e198e1f2"), // dlc.rpf/x64/levels/gta5/props/prop_m23_1_facility_01.rpf
    ShaUnpack("a3fdab4a2827e40c372c0ea89aa749207e29ca513e2486c760ad8ac1e7e970d6"), // dlc.rpf/x64/levels/gta5/props/prop_m23_1_lev_des_01.rpf
    ShaUnpack("fe8d6b2b999c18a0baede57647a2137edae88261973189c03d5eb968ad11d445"), // dlc.rpf/x64/levels/gta5/props/prop_m23_1_plane_crash_01.rpf
    ShaUnpack("f26374be39cfd89dd8768e6c02dcd38b5a1f87f050e45faae949a257f3e4f36a"), // dlc.rpf/x64/levels/gta5/props/prop_m23_1_targets.rpf
    ShaUnpack("fb9dc968f15ce119b18b15d0eaad7eab937d5355b79b42feec308f1b308c6c9b"), // dlc.rpf/x64/levels/gta5/props/prop_m23_1_vehicles.rpf
    ShaUnpack("b6efbfc198def78780282ac5a0eb335d6525a0cce755723740bc97552def773c"), // dlc.rpf/x64/levels/gta5/props/prop_m31_1_arena_pipes.rpf
    ShaUnpack("a0eacdbeeed25a3ee6bc421c1f17cda0cbf79a4f0addf17a751342e10359d059"), // dlc.rpf/x64/levels/gta5/vehicles/mp2023_01.rpf
    ShaUnpack("99f305243ff058200d1c48f8c678cb712016072a2f7e272d229424ec953f234e"), // dlc.rpf/x64/levels/mp2023_01/vehiclemods/avenger3_mods.rpf
    ShaUnpack("6e7b1086bdd213c66f265f558305ebaf4be6b578f72e281c6747ee1ca44efa13"), // dlc.rpf/x64/levels/mp2023_01/vehiclemods/brigham_mods.rpf
    ShaUnpack("645a73b2d61fe60902f0ae8ce558754c6475262118b09e7f47db10bbd55ff7cb"), // dlc.rpf/x64/levels/mp2023_01/vehiclemods/buffalo5_mods.rpf
    ShaUnpack("c4988ed561f606d10f37efa8c94530b3e8c7cde88ff7847f22d5b79d74e735d8"), // dlc.rpf/x64/levels/mp2023_01/vehiclemods/clique2_mods.rpf
    ShaUnpack("0f5706679843ba6c3d2e9cb55f4ca96796223b01338a989bcdec73c7da8fbbf5"), // dlc.rpf/x64/levels/mp2023_01/vehiclemods/conada2_mods.rpf
    ShaUnpack("9dcd8d6b35dd80d3e81a6f619c157549058214c3887cc79527c6fe163962c3ed"), // dlc.rpf/x64/levels/mp2023_01/vehiclemods/coureur_mods.rpf
    ShaUnpack("9684ef1835a2ac0ef50927a2578ee0da94c3cd7739a5e222fb73574cb0400803"), // dlc.rpf/x64/levels/mp2023_01/vehiclemods/gauntlet6_mods.rpf
    ShaUnpack("fc950e6bf59455e4248ec1b64841f1c5f943778e7a3a5b6579251b60b67c151f"), // dlc.rpf/x64/levels/mp2023_01/vehiclemods/l35_mods.rpf
    ShaUnpack("c4e83d56fe071061838a3dd667e3cc5c527c167858ed18c7029ca8729b884b36"), // dlc.rpf/x64/levels/mp2023_01/vehiclemods/monstrociti_mods.rpf
    ShaUnpack("61a130f808464f14d1dedaee3b6fd5ca952005825b6b8d3c33f07b2b6476d920"), // dlc.rpf/x64/levels/mp2023_01/vehiclemods/raiju_mods.rpf
    ShaUnpack("5c584587893fc7ba435e2d55dc3e348008b899f1b813bcdabe46a77c36f03a37"), // dlc.rpf/x64/levels/mp2023_01/vehiclemods/ratel_mods.rpf
    ShaUnpack("03414c59dd49c2176720061cc38716778ac01ff3024bf0cae06d6a876e37e3df"), // dlc.rpf/x64/levels/mp2023_01/vehiclemods/speedo5_mods.rpf
    ShaUnpack("331b1cf3792c96bb32beab9384b70d062bbd4fe40b7869c9a058d03d8cac1407"), // dlc.rpf/x64/levels/mp2023_01/vehiclemods/stingertt_mods.rpf
    ShaUnpack("76ba475cccda25f29a10e048894506c259f27e7eb6fff50c293a01a86efd2b85"), // dlc.rpf/x64/levels/mp2023_01/vehiclemods/streamer216_mods.rpf
    ShaUnpack("e62d786a33c63f5b596307c5f377d031f2d8efbcbf92ea9b3f599cdb64c448db"), // dlc.rpf/x64/models/cdimages/mp2023_01_female.rpf
    ShaUnpack("e2c818b4b5ca26bfaf8ea8cce1d39b75f23cffd9feb7cc230a29a0243f59ce42"), // dlc.rpf/x64/models/cdimages/mp2023_01_female_p.rpf
    ShaUnpack("cde819796c9c2d8ab3353fda43a02b801c4e1bb79f92b96dbbb882743cdbb682"), // dlc.rpf/x64/models/cdimages/mp2023_01_male.rpf
    ShaUnpack("26b300fb2a2266d2e9e98a81ece168901f923d4682c5c6167b5106df83b3e83a"), // dlc.rpf/x64/models/cdimages/mp2023_01_male_p.rpf
    ShaUnpack("aeb96254b8449ced87fd2c4a4a081e3535c0e4a04203387c1316ad0f5eaf773e"), // dlc.rpf/x64/models/cdimages/mp2023_01_ped_mp_overlay_txds.rpf
    ShaUnpack("83dd2bd252257940d9918a2bd6990aa32dd9e255aec22ea5ea7d77f4d5c19134"), // dlc.rpf/x64/models/cdimages/weapons.rpf
    ShaUnpack("77c32475af461cdcf2bb1cb539fcf8dff8893b3dd1f8f25815a074e40f660d32"), // dlc.rpf/x64/models/cdimages/peds/mp2023_01.rpf
    ShaUnpack("9f1feba4bf0d81d3171441a830f8d4a013d5ddc82dd96399efbda0d67fba2a7e"), // dlc.rpf/x64/models/cdimages/peds/mp2023_01_p.rpf
    // update/x64/dlcpacks/mp2023_01_g9ec/dlc.rpf
    ShaUnpack("a2a6256897632fc79880fb5889abdce24cfc0bd4761f612c4049e875b9e2562b"), // dlc.rpf
    ShaUnpack("3369429c3492615570c9cc5924f42105d8f41fd480658f92a7754571801e5de3"), // dlc.rpf/x64/levels/gta5/mp2023_01_g9ec_additions/mp2023_01_g9ec_additions.rpf
    ShaUnpack("5192a73497574c80ae0103c33b461d9ec927171e4b7ef889d0bd12d9b24edb82"), // dlc.rpf/x64/levels/gta5/mp2023_01_g9ec_additions/mp2023_01_g9ec_additions_metadata.rpf
    ShaUnpack("dd817495df4a50d1931c9a5e07f868fd499bdd3c3b5a405325110840db7b24ac"), // dlc.rpf/x64/levels/mp2023_01_g9ec/vehiclemods/buffalo5hsw_mods.rpf
    ShaUnpack("1f86f0cb5846ea5c0fefbf000ff7cc68fbcc65ed19577112f94ea687713b1e15"), // dlc.rpf/x64/levels/mp2023_01_g9ec/vehiclemods/coureurhsw_mods.rpf
    ShaUnpack("ebff97dcb258dfcf8f51a3d0fa636e2bf2d3f12b221151a6ca3924ca026cd3bc"), // dlc.rpf/x64/levels/mp2023_01_g9ec/vehiclemods/monstrocitihsw_mods.rpf
    ShaUnpack("f19275d03ffd37cddf4d70f38b3b188893a31b896c73695633f8248b0508eaa7"), // dlc.rpf/x64/levels/mp2023_01_g9ec/vehiclemods/stingertthsw_mods.rpf
    // update/x64/dlcpacks/mp2023_02/dlc.rpf
    ShaUnpack("c6790090ae45d22392a3b54b542c876da14be45d096e981015d39989696fcab1"), // dlc.rpf
    ShaUnpack("1f2a10627974085149e4484f89908a56315c7d23a14895f96241acb37a40ee5f"), // dlc.rpf/x64/anim/creaturemetadata.rpf
    ShaUnpack("220d19a0d3a28f359518d2e3b95b1732b1acda5e29b0f9c0481ae90fdd68e094"), // dlc.rpf/x64/anim/expressions.rpf
    ShaUnpack("96bd86ab41e875a5597f391bcfc1484a19b55574f0500d878cf06309a11283da"), // dlc.rpf/x64/anim/cutscene/cuts_xm4_cbr.rpf
    ShaUnpack("c464c6d58765e6ea984639b889252b3c85663980fd326dc571c5a9ae6d768b29"), // dlc.rpf/x64/anim/cutscene/cuts_xm4_cbr6.rpf
    ShaUnpack("92368216a6cb17cbf3c490035f10f7c92dc565b3a7dcfe4fd01035186be0a3b8"), // dlc.rpf/x64/anim/cutscene/cuts_xm4_rob_cas.rpf
    ShaUnpack("19fa21f1077670109ccf54b250647648549a600de7768fd42ed3c82db6bd6dbf"), // dlc.rpf/x64/anim/cutscene/cuts_xm4_rob_ship.rpf
    ShaUnpack("9c3c7db53d221816ff796927d149aac82ca3f5d5e40c049395f38a0eda3c47b6"), // dlc.rpf/x64/anim/cutscene/cuts_xm4_rob_sub.rpf
    ShaUnpack("6628e740a4c4550fbbb22625327364b9079f38330fd6f8d67b9ef2b7d5736d10"), // dlc.rpf/x64/anim/cutscene/cuts_xm4_rr_fin.rpf
    ShaUnpack("79a39e7e7e6c4226aba3a5c5549b885154879fefbb6b23e4ad86a17bb0b85cab"), // dlc.rpf/x64/anim/cutscene/cuts_xm4_yard.rpf
    ShaUnpack("95e440b971f0c49724742f6718fdc0d4f1812fe765f7ee2b95aacdc15cd09794"), // dlc.rpf/x64/anim/ingame/clip_anim@.rpf
    ShaUnpack("e42f18635be010faf640836e22430da660d20fcab98657f8e4640ea9a5e739bc"), // dlc.rpf/x64/audio/occlusion.rpf
    ShaUnpack("b40347d4c1fd03625ba350eb782f42c625859eb8b52975dd675ad9006c0b3fb4"), // dlc.rpf/x64/data/effects/ptfx.rpf
    ShaUnpack("f6532bf2a292a3e407fd99d316ab847f2b362f1067daf304e59ff02ba4f43921"), // dlc.rpf/x64/data/lang/americandlc.rpf
    ShaUnpack("b3a3e86a76a327886784ac43a96ade1106d269fd3f1f99e9b9ce7bd2dfe8b7e6"), // dlc.rpf/x64/data/lang/chinesedlc.rpf
    ShaUnpack("ae30e72a612b0193f1bc8edd0327962cf71a2f3442f363f72496123ed2a7cc40"), // dlc.rpf/x64/data/lang/chinesesimpdlc.rpf
    ShaUnpack("45ffc12214b525baa01b5c9c673960820a4f4b166f235f2ea104813be61e3d0e"), // dlc.rpf/x64/data/lang/frenchdlc.rpf
    ShaUnpack("7b8fc56965b68723c205643015fe6e7155b8ddf932b178f4ef780be7b71d858d"), // dlc.rpf/x64/data/lang/germandlc.rpf
    ShaUnpack("d7c3c14a26ca9bd79302aeb28a2f522067a34914890a12d18e2237d847d4b326"), // dlc.rpf/x64/data/lang/italiandlc.rpf
    ShaUnpack("e17b28f8566846ffe13063b9e130e89755651e82e4578ab110be61a74d046460"), // dlc.rpf/x64/data/lang/japanesedlc.rpf
    ShaUnpack("e88bf7f0ae8b17d1f12e1d1b6c6b00764a7d21623dd25b39b0bd60f5b099f019"), // dlc.rpf/x64/data/lang/koreandlc.rpf
    ShaUnpack("da5279bd4e9bcff9b18d116ff93646ac3c684f06bf3c2afd24a196c1a3ca243e"), // dlc.rpf/x64/data/lang/mexicandlc.rpf
    ShaUnpack("23fee1d001833262e2c12619afd58012effcf91f931c1ca0ba09fef93f3075ac"), // dlc.rpf/x64/data/lang/polishdlc.rpf
    ShaUnpack("71729462293815772c2cefef8c8a1d0ecabc1e0d4a3c8d9e306e679f67519a7e"), // dlc.rpf/x64/data/lang/portuguesedlc.rpf
    ShaUnpack("5b7bcdb0ee372da88591fd8e5307833ff4b83d5b9c58e9335a4311ab80626107"), // dlc.rpf/x64/data/lang/russiandlc.rpf
    ShaUnpack("ed754cd8886bd3e82c485ef9eb2a2e8a54fa05a0d6a62ac9d415d2429d28775b"), // dlc.rpf/x64/data/lang/spanishdlc.rpf
    ShaUnpack("6235467415ee25961177423ec96aa37db29a78e5a930ba5dd7ab2693d8f4eb55"), // dlc.rpf/x64/levels/gta5/navmeshes.rpf
    ShaUnpack("76c0c02849b1b92ae5e92b477fb04666fdd0d54686bf3732201a29a6aba6edcf"), // dlc.rpf/x64/levels/gta5/interiors/int_placement_m23_2.rpf
    ShaUnpack("ddac1cf164e42906060edd0869aa1431633c473b45089088851318a0df4a3e9c"), // dlc.rpf/x64/levels/gta5/interiors/mp2023_02_dlc_int_1.rpf
    ShaUnpack("a52b504d8b4b62a1412fc67846d7989296b56cbbf346ab6e6ba96fa8abde4b64"), // dlc.rpf/x64/levels/gta5/interiors/mp2023_02_dlc_int_2.rpf
    ShaUnpack("0d9460269cd5acd05763cc29c33a07ea100fea62fc6ee22a1f33d228f46bf664"), // dlc.rpf/x64/levels/gta5/interiors/mp2023_02_dlc_int_4.rpf
    ShaUnpack("a6c412a50e5e6d1135e5fa4a87ef4db29b727793d68dee80ccd652c6c220a886"), // dlc.rpf/x64/levels/gta5/interiors/mp2023_02_dlc_int_5.rpf
    ShaUnpack("af032bca575e8fceec2c27408468fdf5c4d55f1cbc85ee8730803947833b9915"), // dlc.rpf/x64/levels/gta5/interiors/mp2023_02_dlc_int_6.rpf
    ShaUnpack("3463d907980bf19f04ffb0aacee617d6e955828bea3c08986b549353ca510ab6"), // dlc.rpf/x64/levels/gta5/interiors/mp2023_02_dlc_int_7.rpf
    ShaUnpack("9cca2c3cab12496afc90f0b8b29e74e1d470bd964576305ccaeb2ee13d442a74"), // dlc.rpf/x64/levels/gta5/mp2023_02_additions/mp2023_02_additions.rpf
    ShaUnpack("0839ce8afd893eae5848db90c49ed6bcf755d180b0dea95db946e898bf00051e"), // dlc.rpf/x64/levels/gta5/mp2023_02_additions/mp2023_02_additions_metadata.rpf
    ShaUnpack("8e64d3e847c10cd8af8549e5e88b6201fbc710a055e5fc46d00709eef98c6514"), // dlc.rpf/x64/levels/gta5/props/prop_m23_2_accs_01.rpf
    ShaUnpack("517620cfdc39522f05bf1af0d543e5869860d462648af1fc050a1186d6956ad5"), // dlc.rpf/x64/levels/gta5/props/prop_m23_2_accs_02.rpf
    ShaUnpack("6a90b3c13923ba8abbc89e7b2d03365fe9749f30d212efa7a0b91eab89a526fe"), // dlc.rpf/x64/levels/gta5/props/prop_m23_2_arena.rpf
    ShaUnpack("1ad6047ecfc1e9d67d80ff4a4460403ee8fdc7dab46ba873e2db5b4995cf8265"), // dlc.rpf/x64/levels/gta5/props/prop_m23_2_blocks.rpf
    ShaUnpack("8c47e9a8ddd0a189866852bb1aaf109855cba40ae0e10b9aebfb45a5fb099e1a"), // dlc.rpf/x64/levels/gta5/props/prop_m23_2_cabinet.rpf
    ShaUnpack("e1b715149d99c08a2c21cf93cbdb4b9271b87f7fb11ccfc459ba459b31c7c8b4"), // dlc.rpf/x64/levels/gta5/props/prop_m23_2_casinobase.rpf
    ShaUnpack("699924395daa882d9c9a6ea7708fc6da8b71d00dac353c988d397ea75d8f4074"), // dlc.rpf/x64/levels/gta5/props/prop_m23_2_containers.rpf
    ShaUnpack("e39f300b029d930fab24fbf1245b3dfe18de6c6aae48b0586489d587b2187f8b"), // dlc.rpf/x64/levels/gta5/props/prop_m23_2_crates.rpf
    ShaUnpack("da6a01ef6c2674b869b2d938598e49f8aea126abee3a198d022b4df169a4731c"), // dlc.rpf/x64/levels/gta5/props/prop_m23_2_deathmatch.rpf
    ShaUnpack("710e8d26bba717719f7219356502529a8ee994ed5af47dfbfe20f80427502a3b"), // dlc.rpf/x64/levels/gta5/props/prop_m23_2_doors.rpf
    ShaUnpack("c4b4056657640b2b12f2ac35bf7267eb50cfb4ccd07f746b4ed4ac8c78ce0595"), // dlc.rpf/x64/levels/gta5/props/prop_m23_2_helipad.rpf
    ShaUnpack("5de48a52645e22a2a19c6b1c0899d97f514bee9e71599bfb1c3e249493b83fe6"), // dlc.rpf/x64/levels/gta5/props/prop_m23_2_holiday.rpf
    ShaUnpack("abbe49b528a47d3f489dc5febfed3f89c3560071e69d123196f0631d02a7fcb2"), // dlc.rpf/x64/levels/gta5/props/prop_m23_2_lev_des_01.rpf
    ShaUnpack("7e624f8a2806d81631a999d23af2404e456725249e17779fa5adc49d6008feb6"), // dlc.rpf/x64/levels/gta5/props/prop_m23_2_machinery.rpf
    ShaUnpack("0f5112542870b5f5b38091c27f86374b11a161ff802d421e044c7970c98a631d"), // dlc.rpf/x64/levels/gta5/props/prop_m23_2_race.rpf
    ShaUnpack("8b6fc5d359d62c8d53d116e704fc2256e33e9f920fc0a8b2bb11fea0407254ce"), // dlc.rpf/x64/levels/gta5/props/prop_m23_2_sleigh.rpf
    ShaUnpack("bb6e543ca0c5ddf95169fc2b02e19b211ff4855c297468bd5cd2f20e1a13e2cd"), // dlc.rpf/x64/levels/gta5/props/prop_m23_2_vault.rpf
    ShaUnpack("2068f71ff7612b279adeac0cb955cfcb7013c039a0fe547dd1494aa635974ec6"), // dlc.rpf/x64/levels/gta5/vehicles/mp2023_02.rpf
    ShaUnpack("7a71204568616564682d6bad2a0806a94860736519752838c935784a7f8e8fb7"), // dlc.rpf/x64/levels/mp2023_02/vehiclemods/aleutian_mods.rpf
    ShaUnpack("8db3455cd22bb5eff3527fb25bb95732785a2f73daa9583be8e2ca3d77e65e0c"), // dlc.rpf/x64/levels/mp2023_02/vehiclemods/asterope2_mods.rpf
    ShaUnpack("9ced1ded08f3cd968d28c9154622b92870aa8747fcadd09513fd6678bcd54ae1"), // dlc.rpf/x64/levels/mp2023_02/vehiclemods/baller8_mods.rpf
    ShaUnpack("c0924a0fb99930b1b796eb696f43516a79a4c6c64ab6067a00e884f78d6f652d"), // dlc.rpf/x64/levels/mp2023_02/vehiclemods/cavalcade3_mods.rpf
    ShaUnpack("b64a42f2a59277bd0e43ba8a3b94fe5177f0e6933ac7f8113914ff9cb11148b0"), // dlc.rpf/x64/levels/mp2023_02/vehiclemods/dominator9_mods.rpf
    ShaUnpack("1b946dee534c3e700a42a6f4781e93ac7967984cbc81bd4ccf128f6a8c8bb097"), // dlc.rpf/x64/levels/mp2023_02/vehiclemods/dorado_mods.rpf
    ShaUnpack("a657df681e3f07851288887f09b7c715bcc71766f097b4bb8ca212c64e2837f9"), // dlc.rpf/x64/levels/mp2023_02/vehiclemods/fr36_mods.rpf
    ShaUnpack("7ff9f37490fedd927d989a349c8b3b72bf6d3a5b4989b54890548d168616e5ff"), // dlc.rpf/x64/levels/mp2023_02/vehiclemods/impaler5_mods.rpf
    ShaUnpack("9a838683ffd4bd9964d7d0d68471fa125810992d4f92ed40fc1e97b3cf490544"), // dlc.rpf/x64/levels/mp2023_02/vehiclemods/impaler6_mods.rpf
    ShaUnpack("59cd33489d9d04e3bf381914c7a49413a745e08cd1db406d6abc922a12f0a019"), // dlc.rpf/x64/levels/mp2023_02/vehiclemods/polgauntlet_mods.rpf
    ShaUnpack("e6205895605e74948dae7a1bbc2027ea14e13cac5e4c707659c8d4afd75ef5cd"), // dlc.rpf/x64/levels/mp2023_02/vehiclemods/police5_mods.rpf
    ShaUnpack("b5d50a44daf7c2c6bfcec521c17e16137ba51760d1368fa4e62df3c313a6d637"), // dlc.rpf/x64/levels/mp2023_02/vehiclemods/polshared_mods.rpf
    ShaUnpack("b071142f01aee19c0421ac19486dc768e890444a913e64c9fe3cccb34a0a3017"), // dlc.rpf/x64/levels/mp2023_02/vehiclemods/terminus_mods.rpf
    ShaUnpack("3e148f43da6532b25af935248d49c8eca52af121953d3881c11d9deb264c6ac7"), // dlc.rpf/x64/levels/mp2023_02/vehiclemods/turismo3_mods.rpf
    ShaUnpack("56769357eb103aba4558b158bd21da4414d707bd55de2b81734e4fde8f21758e"), // dlc.rpf/x64/levels/mp2023_02/vehiclemods/vigero3_mods.rpf
    ShaUnpack("0b1df77a5e6f5e33b0deb9527a6af4fc926432426fcafe3a7e505fc4a4fdcf09"), // dlc.rpf/x64/levels/mp2023_02/vehiclemods/vivanite_mods.rpf
    ShaUnpack("77453d23e6635746ad002ba7e133b79e679fe2ded59424a6a575da8dffe5581f"), // dlc.rpf/x64/models/cdimages/mp2023_02_female.rpf
    ShaUnpack("0ac1c53a5ec7cdf9bd0c6b4a6c471c06a62467df9459f9541c8f8e3435b2736c"), // dlc.rpf/x64/models/cdimages/mp2023_02_female_p.rpf
    ShaUnpack("9664030bfa3276bcd727a9c592e613a4d9dd5ebd566128ac7fc0cad5e28964b5"), // dlc.rpf/x64/models/cdimages/mp2023_02_male.rpf
    ShaUnpack("50cc9c24c7945e0da2bd3c0e1183f4f60eae397b1cb7b6a634c41d9d32625a10"), // dlc.rpf/x64/models/cdimages/mp2023_02_male_p.rpf
    ShaUnpack("fc89ac085f30bc3528bda47ce025ca57d45300610efcc6762dbaf172e7fa9c09"), // dlc.rpf/x64/models/cdimages/mp2023_02_ped_mp_overlay_txds.rpf
    ShaUnpack("a5279cbf0d02ddb54063ca6b646b1afd0eaa667bd69597694a9f742c2a44e038"), // dlc.rpf/x64/models/cdimages/weapons.rpf
    ShaUnpack("845313d56805e521d92520a954c7ea41a6335c6b11fa224f886ab9ce82256db6"), // dlc.rpf/x64/models/cdimages/peds/mp2023_02.rpf
    ShaUnpack("39a5c475841b2dfea67fa169667ae74a68cd3c9678f5c49f89fe56074e1337cd"), // dlc.rpf/x64/models/cdimages/peds/mp2023_02_p.rpf
    ShaUnpack("127f8a57e80c3d90f2b704165922cbed3a382ec2a09ab78a4a3ed7650c731aa5"), // dlc.rpf
    ShaUnpack("999f273c13d91174c3406cdc0a485944207102d8658bf191c5ee7b2011027f81"), // dlc.rpf/x64/levels/gta5/props/prop_m23_2_crates.rpf
    // update/x64/dlcpacks/mp2023_02_g9ec/dlc.rpf
    ShaUnpack("fb4d37b4ce041d88c1dd32b2e0e8b8d585ddd8eb936f950ae2bfb9090190091c"), // dlc.rpf
    ShaUnpack("ad3ee7fdf2fc90bf0e80fb0625058189d0a206771cd5459c79f7aa7d90884fc4"), // dlc.rpf/x64/levels/gta5/interiors/int_placement_m23_2_g9ec.rpf
    ShaUnpack("e4a98c045c2492035af0b7a95b1555bc367399968354f1b0eafe9fde31b09e9a"), // dlc.rpf/x64/levels/gta5/interiors/mp2023_02_dlc_int_3.rpf
    ShaUnpack("1779b86830529dbee031501fbe7c19eddf06dfd4009a28a6814269094c994833"), // dlc.rpf/x64/levels/gta5/mp2023_02_g9ec_additions/mp2023_02_g9ec_additions.rpf
    ShaUnpack("8ef745ce42c2e87644176ab635bae870ca125feb64d9f2bad7cba5e245116754"), // dlc.rpf/x64/levels/gta5/mp2023_02_g9ec_additions/mp2023_02_g9ec_additions_metadata.rpf
    ShaUnpack("c9df93e3f4a7d8f4ac9be5603f7c732347b54285f18d25dfe2b8d6dcc91232c8"), // dlc.rpf/x64/levels/mp2023_02_g9ec/vehiclemods/vivanitehsw_mods.rpf
    // update/x64/dlcpacks/mpairraces/dlc.rpf
    ShaUnpack("1e197f3fb7a17d0024fa1c1eb82cdb085f33f6f4d9876ae40b916a879de325df"), // dlc.rpf
    ShaUnpack("05df9433697ae3b0e9c3e90756b98c61ed031f89f75f56159286f753054366bc"), // dlc.rpf/x64/anim/creaturemetadata.rpf
    ShaUnpack("7e56a902c8468a9163e58fe476a8f24d17a810ce00e58575b4037bec3e91d511"), // dlc.rpf/x64/data/effects/ptfx.rpf
    ShaUnpack("9f8225da35cf75f2a3039668425798bcc03eaddded1a9a551ecd8ea37908d62a"), // dlc.rpf/x64/levels/gta5/props/prop_ar_blocks.rpf
    ShaUnpack("3b041cbf109a617a565d204cc7a0daff5c4e44d60ad6431c7ae22f046ed0cdb2"), // dlc.rpf/x64/levels/gta5/props/prop_ar_checkpoints.rpf
    ShaUnpack("c440e825592485c0f203c4b8e929ff846971502f8b9e33065341552de52a0406"), // dlc.rpf/x64/levels/gta5/props/prop_ar_cp_towers.rpf
    ShaUnpack("8aa760256754a41daef290e93e03cc98a99e21605781c03ce5e4c521a1a385e7"), // dlc.rpf/x64/levels/gta5/props/prop_ar_firehoops.rpf
    ShaUnpack("1ca1e1237bfe3ce304f2ec11898a8129e8921c5d6f1205ebae7b3bd68936752f"), // dlc.rpf/x64/levels/gta5/props/prop_ar_inflatable_cp.rpf
    ShaUnpack("55e1f5a169b08f755eb524238b3124e114bc0307b0e7cc638cc9086546d91ef9"), // dlc.rpf/x64/levels/gta5/props/prop_ar_jetski_ramp.rpf
    ShaUnpack("1fb2bc454fefa8d41aa112e462887412a7abe7b826d071664d8e38d96b058fa4"), // dlc.rpf/x64/levels/gta5/props/prop_ar_neongates.rpf
    ShaUnpack("dd5d5330f2cf7188500453350badb0e177aa60a97d9f5caf7f771540985d708f"), // dlc.rpf/x64/levels/gta5/props/prop_ar_signs.rpf
    ShaUnpack("2e8e6d75296ccac8c69016f1277d35f09c8d45f4fb6fe68a38cb33e70b262554"), // dlc.rpf/x64/levels/gta5/props/prop_ar_stunt.rpf
    ShaUnpack("35a6afe2b281342b83f9d0871dc9100142d184e095c4b023c90cbfb9c48ba74b"), // dlc.rpf/x64/levels/gta5/props/prop_ar_tubes.rpf
    ShaUnpack("bf5e7aec4e1ccae6d6b0776eb9c920b6f6bbc7e616bc4547ad1021ccc6319975"), // dlc.rpf/x64/models/cdimages/mpairraces_female.rpf
    ShaUnpack("74040da97d84bee3553743c3bcca4131f362d25e5a9ca28f97bf991f720dee79"), // dlc.rpf/x64/models/cdimages/mpairraces_female_p.rpf
    ShaUnpack("0adb15a602fef997146aa5786e0c6250ff4da9b09effe368d00657d6faf9a892"), // dlc.rpf/x64/models/cdimages/mpairraces_male.rpf
    ShaUnpack("48498511a1b0ca11c0f02affdefeb8984a38df1f20bb68962a87c3d17628b149"), // dlc.rpf/x64/models/cdimages/mpairraces_ped_mp_overlay_txds.rpf
    // update/x64/dlcpacks/mpapartment/dlc.rpf
    ShaUnpack("e8296ff8fdb02c8259cc7fe012f3bbb89728761873e81aa60e6678269313c2dc"), // dlc.rpf
    ShaUnpack("1431e90b7dd9374341b655b19188392da727c51d159f46aa6ee67300e89319ed"), // dlc.rpf/x64/anim/creaturemetadata.rpf
    ShaUnpack("b073bff5847e23009a6534d2721e2ebed00bd2e7b54a7744f06f3d1e43908667"), // dlc.rpf/x64/anim/cutscene/cuts_humane.rpf
    ShaUnpack("80a0cb19bb34317854fe384cad90ca5f90c8da02b2355d56e276546b159b7f5f"), // dlc.rpf/x64/anim/cutscene/cuts_narcotic.rpf
    ShaUnpack("8a396f01e6f454a26a6d7322ae5ec3b12da5ba8009d4251925b37d731467c96a"), // dlc.rpf/x64/anim/cutscene/cuts_prison.rpf
    ShaUnpack("084076dd20e65eb17eb111655ed818eb7dd7b79a6f02d8f8de9d44c3217cfd16"), // dlc.rpf/x64/anim/ingame/clip_anim@.rpf
    ShaUnpack("6b41f8cde7f8c2021106d449578964903953a5db9f8c74123a13472d86b16e45"), // dlc.rpf/x64/audio/occlusion.rpf
    ShaUnpack("bc9c108990962744b8ac6bec719c9691ae92a4308d4e847d87dcc6f64dc1c4cc"), // dlc.rpf/x64/data/cdimages/eyefind.rpf
    ShaUnpack("f1a6161ec3f869822748de3f1f01d92cb0b9f6675b8bf6f019c777b4d415f257"), // dlc.rpf/x64/data/cdimages/larssandelbow.rpf
    ShaUnpack("22697730201f015223690025e9ca227f49fa3b92a670936653e34de7a4502116"), // dlc.rpf/x64/data/effects/ptfx.rpf
    ShaUnpack("83fb864b3fb70b7944e691b84507ccd7f2938548f2c3c00472f669a60997020c"), // dlc.rpf/x64/data/lang/americandlc.rpf
    ShaUnpack("31575d92140fd6289e8bc967c3309b084d95cea64848528bae613e3c38589551"), // dlc.rpf/x64/data/lang/chinesedlc.rpf
    ShaUnpack("7f8b57d6f7ecf99b0ce149d664ca49fd777c7ba23b6190fa1b514967eda721c6"), // dlc.rpf/x64/data/lang/frenchdlc.rpf
    ShaUnpack("84eb899a3ad128fda29ef6987c478558636d8525671495cbd43e4e20d9b7ccc2"), // dlc.rpf/x64/data/lang/germandlc.rpf
    ShaUnpack("40725fff4718f00dd2b49a3c0c660491bfc1fd06ae7ac3d9b6a7dcd9d8798a88"), // dlc.rpf/x64/data/lang/italiandlc.rpf
    ShaUnpack("27555094475d2924a0b728b24019e996bf141339d62c93549abc522142de5d6a"), // dlc.rpf/x64/data/lang/japanesedlc.rpf
    ShaUnpack("bce99c2598e1c2f76cd7041375c247d5f717dc624090272049c1bfa2a5088d83"), // dlc.rpf/x64/data/lang/koreandlc.rpf
    ShaUnpack("dc4cabfe16360215091da5727035705bcf4a39c21ebddef470f49ca178b7271f"), // dlc.rpf/x64/data/lang/mexicandlc.rpf
    ShaUnpack("b61dcc72d53067eda3e32260c1898e2bee72373f1f707549aa4f625d10c15181"), // dlc.rpf/x64/data/lang/polishdlc.rpf
    ShaUnpack("04f63af064bb1a3e1be39d03ce1310e30287159b8d3acae5f417bbd6a3490e13"), // dlc.rpf/x64/data/lang/portuguesedlc.rpf
    ShaUnpack("9416cbbc7891fa223c85f45e02e64142b79580c8dffb95b65e7e4f33295f0389"), // dlc.rpf/x64/data/lang/russiandlc.rpf
    ShaUnpack("e532bf59636a13177d5775c49904ab53616729e29919c9df11ef30fe4d708df1"), // dlc.rpf/x64/data/lang/spanishdlc.rpf
    ShaUnpack("b6e7bea17d09f2dea11ef474d96e1927d8edb961f9895c6255239a1f6328e643"), // dlc.rpf/x64/levels/gta5/lodlights.rpf
    ShaUnpack("593c1f555efefb135169600468de1a9106f48d90fae94f4daa6445ce670826a9"), // dlc.rpf/x64/levels/gta5/_citye/sunset/bt1_05.rpf
    ShaUnpack("46ce927d079c5df97cc427b5c9887f63789c3013b9a2345acccbb4320da82bc4"), // dlc.rpf/x64/levels/gta5/_citye/sunset/ss1_02.rpf
    ShaUnpack("87121b4d386b37f73cbe2803cf0895dac19faa404eab5783fbc1acecf7ea8837"), // dlc.rpf/x64/levels/gta5/_citye/sunset/ss1_05.rpf
    ShaUnpack("ccec05485cf2726d08a17f32d6160d65f8bc75e63a438b511dc645969dfc4ff5"), // dlc.rpf/x64/levels/gta5/_citye/sunset/ss1_11.rpf
    ShaUnpack("d6a1632bbde09636180d396415355ce5e1b000b9c30d73339f556122ac4e15ce"), // dlc.rpf/x64/levels/gta5/_citye/sunset/ss1_occl.rpf
    ShaUnpack("3a1762a30b046e7b33170ab99397b15ab158d52b89b7dbd8c82a940a8315fc94"), // dlc.rpf/x64/levels/gta5/_citye/sunset/sunset.rpf
    ShaUnpack("750cc1d40e13da5fecbe3511531ee51520124fc5f5438fd4ff5ea9edc4b2547c"), // dlc.rpf/x64/levels/gta5/_citye/sunset/sunset_metadata.rpf
    ShaUnpack("c2a4b452a4e1dc66d57265037c4030e776dd243fb6a0f7f807ec1c551e2aad75"), // dlc.rpf/x64/levels/gta5/_hills/cityhills_02/ch2_04.rpf
    ShaUnpack("381a72022183c1de11bb50f49263c78da54e1b5b37e832bba9ea6c9c4a7503f8"), // dlc.rpf/x64/levels/gta5/_hills/cityhills_02/ch2_05c.rpf
    ShaUnpack("70a8b57865846d56e97b483e993aa407462bd58d0d3c64ee63d3919a326a5b24"), // dlc.rpf/x64/levels/gta5/_hills/cityhills_02/ch2_05e.rpf
    ShaUnpack("1941df50dec092b2f691e9d0f24190da2a21a06c4fa4bbc89ffca3de8d30b95e"), // dlc.rpf/x64/levels/gta5/_hills/cityhills_02/ch2_07b.rpf
    ShaUnpack("1c7693a45df13a0163472a3b1021e853417570361ff2028eb7b25f093d03af20"), // dlc.rpf/x64/levels/gta5/_hills/cityhills_02/ch2_08.rpf
    ShaUnpack("0a33282d198b31f0f70c5d5a8320466d7191c85981bac944a4c9b9c5e105ce1f"), // dlc.rpf/x64/levels/gta5/_hills/cityhills_02/ch2_09b.rpf
    ShaUnpack("594691b3eeb4eb6e29a3688f853944bdc908401af265c07f61e0b2d5f9c7cc32"), // dlc.rpf/x64/levels/gta5/_hills/cityhills_02/ch2_09c.rpf
    ShaUnpack("ff5675d5c5b58b45e26e09c206be36ecf122733eb70fd2b38b06a22c8c9902a8"), // dlc.rpf/x64/levels/gta5/_hills/cityhills_02/ch2_10.rpf
    ShaUnpack("e97e0f666c61d6749d6664c15088f506961bf94524e4c368e4752fffb7f1484b"), // dlc.rpf/x64/levels/gta5/_hills/cityhills_02/ch2_11.rpf
    ShaUnpack("61ae1b9cf6d0a405c930a7f8696d09e95dd48629b37d3561229b8f9af35292cb"), // dlc.rpf/x64/levels/gta5/_hills/cityhills_02/ch2_12b.rpf
    ShaUnpack("905e49be7c1c9ccf79aaa44ce201c4062c5d4d212497707d1c133c411667b282"), // dlc.rpf/x64/levels/gta5/_hills/cityhills_02/ch2_occl.rpf
    ShaUnpack("e065eb48a77146e38cdcdd39e988abe55289e5f4c2f2dac2fb50eaa9efa044e3"), // dlc.rpf/x64/levels/gta5/_hills/cityhills_02/ch2_roads.rpf
    ShaUnpack("7c1075487c2e3f316460e12e65ad15c98ca71c834dc4d8b1a0c2713efc1eec35"), // dlc.rpf/x64/levels/gta5/_hills/cityhills_02/ch2_roadsb.rpf
    ShaUnpack("ac28301b67b8107dd1e93745f358880c023a898a3464c6104a9322f0b601a0f6"), // dlc.rpf/x64/levels/gta5/_hills/cityhills_02/ch2_superyacht.rpf
    ShaUnpack("43b0f34529ab6344944e3171ed98c0a3ca0829b8327789c5767fc75d8a3a7781"), // dlc.rpf/x64/levels/gta5/_hills/cityhills_02/cityhills_02.rpf
    ShaUnpack("568aad0d2644009af0f4cf5c837fe4064e1f63d49661ad087e0c58a320a9ed00"), // dlc.rpf/x64/levels/gta5/_hills/cityhills_02/cityhills_02_metadata.rpf
    ShaUnpack("241baec40ee047a82bc7cbd5bf5ab0123de07e3d2ec18ecf9b3a421173051a24"), // dlc.rpf/x64/levels/gta5/_hills/cityhills_02/superyacht_metadata.rpf
    ShaUnpack("2a6ff4023e1f9276bc1137b2cdcd99bbce4477115a0134a130e5c8efa1a5d245"), // dlc.rpf/x64/levels/gta5/interiors/int_mp_h_01.rpf
    ShaUnpack("db5820ac3f9ec42a54624108a11f22ca2b46ae1b341a0c2cfd65fe124e62268e"), // dlc.rpf/x64/levels/gta5/interiors/int_mp_h_02.rpf
    ShaUnpack("24b302094de3f1a129e7c6f16920f4258d31c2b78f96dba476fa79647984e677"), // dlc.rpf/x64/levels/gta5/interiors/int_mp_h_03.rpf
    ShaUnpack("a257d4ed0eaf40d7b4812c9e682755a125201c97eca07af8140a9b49454a2bfb"), // dlc.rpf/x64/levels/gta5/interiors/int_mp_h_04.rpf
    ShaUnpack("47ab71146c09d4cad449ef18f8fbfaa5d1c2108b3fc2599217526a05ea859791"), // dlc.rpf/x64/levels/gta5/interiors/int_mp_h_05.rpf
    ShaUnpack("c4e0f1265be8f569efa8f44f867a13d1fca09c6625587e7cce0f45727fccc874"), // dlc.rpf/x64/levels/gta5/interiors/int_mp_h_06.rpf
    ShaUnpack("6ecfb8e8250d76245d575f8cfbc2d70cb2c4ba16a2ab0f4c3bf62f248756e81f"), // dlc.rpf/x64/levels/gta5/interiors/int_mp_h_07.rpf
    ShaUnpack("d1274b105d0119c766e0aea4d9220c915e2247ea9d1339927f06b3100436777c"), // dlc.rpf/x64/levels/gta5/interiors/int_mp_h_08.rpf
    ShaUnpack("df3ef7e4b9ad4ec5ca7d32dc235e3a386c3b0a366432de6364014bc4d4ed182d"), // dlc.rpf/x64/levels/gta5/interiors/int_mp_stilts_a.rpf
    ShaUnpack("b7b7c4c31ec7b0bf0b555badb6ed20e9fdb0ff2b2ca00f8b3d1336541b356d3a"), // dlc.rpf/x64/levels/gta5/interiors/int_mp_stilts_b.rpf
    ShaUnpack("f41b48e83c8689f8a30c7068cef2775107507a142619233bc7e2f4e9c2d07093"), // dlc.rpf/x64/levels/gta5/interiors/int_mp_yacht.rpf
    ShaUnpack("b347ea32e2e1364f335c43e6bd9bf565b29b969a71551abbd6a01785cb967e80"), // dlc.rpf/x64/levels/gta5/interiors/v_inttest.rpf
    ShaUnpack("205167f46b533bb38365b8109f0e0c9aacf5f31ead1158c67b5c0eb10b5ffb59"), // dlc.rpf/x64/levels/gta5/props/int_mp_doors.rpf
    ShaUnpack("744ea57ba1e7b79c44a55d3ccbc6a3f0631c59d85b4fe16292ea8bfd4a9cc825"), // dlc.rpf/x64/levels/gta5/props/mp_apa_prop_doors.rpf
    ShaUnpack("8c99df4a2e4096214a3b661bb45dbb1aefa50040503d4f879f190398f8517b07"), // dlc.rpf/x64/levels/gta5/props/mp_apa_yacht_buoys.rpf
    ShaUnpack("98903c4f4d34c7f19b2f5494f6f596fe180a1027541c265e5c59908dd5b2ad22"), // dlc.rpf/x64/levels/gta5/props/mp_apa_yacht_flags.rpf
    ShaUnpack("ea0b24b4b20addcf8b6c6c85704cf5194c615c315de54e7fb6e99b46c87d5664"), // dlc.rpf/x64/levels/gta5/props/high/int_mp_doors.rpf
    ShaUnpack("b9af6e93a77a8013205cd98cb94e9a48687674c2c0a99e71cf30e21438996e7f"), // dlc.rpf/x64/levels/gta5/props/high/int_mp_h_props.rpf
    ShaUnpack("0d36e99571b02ed20b2406b881a6e33ef9a50653f2892b375d31bceafa8ddf3a"), // dlc.rpf/x64/levels/gta5/props/lev_des/lev_des_apa.rpf
    ShaUnpack("2ec1f1fb1d855edc4202113877e4ccd59eae71e0d4e4e5a303e9fa2ac5b3198d"), // dlc.rpf/x64/levels/gta5/props/skinned/props_skinned.rpf
    ShaUnpack("6416a49fe29b8f4ad5defc5618212b89af1b9ad2915cec9292f5698ef584649f"), // dlc.rpf/x64/levels/gta5/props/yacht/mp_apa_beast_icon.rpf
    ShaUnpack("d5a6cd0349134ede47ea9a7ced552509ea7f802a032170b95621037c5ce61f0e"), // dlc.rpf/x64/levels/gta5/props/yacht/mp_apa_crashed_usaf.rpf
    ShaUnpack("28d9ca1b990227f7ae37be38debb38e24db8700f1feaac14b74b517ac14f6ff4"), // dlc.rpf/x64/levels/gta5/props/yacht/mp_apa_yacht.rpf
    ShaUnpack("d907310da6135636b43e5779ddfd6ac264e8379e7a35cfa1705d973f5973e7c8"), // dlc.rpf/x64/levels/gta5/props/yacht/mp_apa_yacht_door.rpf
    ShaUnpack("a56166c153c2cdbeb4ef93d8ce3762e6893a74b1ec4e98cc6da9680c9b3a5dae"), // dlc.rpf/x64/levels/gta5/props/yacht/mp_apa_yacht_jacuzzi.rpf
    ShaUnpack("43da348486ea72c3f39b4da8b0555c51d9b76f140b07b6f7cf7d81a8a45aec70"), // dlc.rpf/x64/levels/gta5/props/yacht/mp_apa_yacht_lightrig.rpf
    ShaUnpack("e397260af847e06538e3b87c24dea1780de86c52cb522cf0802a8c5debeeba18"), // dlc.rpf/x64/levels/gta5/props/yacht/mp_apa_yacht_text.rpf
    ShaUnpack("bf2d1b418bda5bae4743f4cc758a6d7cc0948a68989c712113e67bb62c78ade4"), // dlc.rpf/x64/levels/gta5/vehicles/apartmentvehicles.rpf
    ShaUnpack("f8ce7d6b4bcffd8d70adec9047be976c9f519ecb27bf489f2fa4d78690f73fbf"), // dlc.rpf/x64/levels/mpapartment/vehiclemods/mamba_mods.rpf
    ShaUnpack("18503a8497c7256f3aa31ffd9447e0e1be4ceafebb0592c58b1650e430c709f1"), // dlc.rpf/x64/levels/mpapartment/vehiclemods/nightshade_mods.rpf
    ShaUnpack("dd18f6c40f7cb43ad0a6a0b77a3da53f51be827bff4075e86a51e3afa3fb2f3d"), // dlc.rpf/x64/levels/mpapartment/vehiclemods/schafter3_mods.rpf
    ShaUnpack("b0f8de101018415235967e6abeb6d3b1cfc37fcdaa21d8e41f31016cfe610967"), // dlc.rpf/x64/levels/mpapartment/vehiclemods/schafter4_mods.rpf
    ShaUnpack("7d55ac5d6c6501def7fabfd8c7a741ffa0ccac1dc5a1000945bad6c2d7543435"), // dlc.rpf/x64/levels/mpapartment/vehiclemods/schafter5_mods.rpf
    ShaUnpack("3f227c646b9546e466cb072cd348b4d74ff482cacadd544173031471115704f7"), // dlc.rpf/x64/levels/mpapartment/vehiclemods/schafter6_mods.rpf
    ShaUnpack("87dd3c96ffe5bb85ca2acad21d24dd880e0fc7852b22f0a2e71fea5dcd7d1739"), // dlc.rpf/x64/levels/mpapartment/vehiclemods/verlierer2_mods.rpf
    ShaUnpack("6f4d79427dd0b342deedcb5494a929d1bcafc9e09c5a3218acf123b4008e6d20"), // dlc.rpf/x64/models/cdimages/mpapartment.rpf
    ShaUnpack("dc8cf6088f0eb5c56bf3278916bdcfad2db906b325a06da14d444be6e2aaca2a"), // dlc.rpf/x64/models/cdimages/mpapartment_p.rpf
    ShaUnpack("dc2fe18da1a8dcbd109ffe97c05fde8b0ffc566bfe826813db30e4bff7155cce"), // dlc.rpf/x64/models/cdimages/mpapt01.rpf
    ShaUnpack("3a8b067f96b645059cc83a0dfcb9ab8e3e6c72be127122e21348cb885e0f1eaa"), // dlc.rpf/x64/models/cdimages/mpapt01_p.rpf
    ShaUnpack("b6ea39f1550bb056701641a7a39ce1e1637ee9a5797e9367df9060a23a5b468d"), // dlc.rpf/x64/patch/data/lang/american.rpf
    // update/x64/dlcpacks/mpassault/dlc.rpf
    ShaUnpack("05ca4a78b305d676a15d76befac1dd6be9900b3311a36aaaaf5cf02ad10eec6e"), // dlc.rpf
    ShaUnpack("73c26a603e6e29de4814f817dfeb0ef33ffad74d28a38128b247c09a247eb5af"), // dlc.rpf/x64/anim/ingame/clip_anim@.rpf
    ShaUnpack("29418946184991dd2bcd28c09c08278ec9b0ca26884f27d88758a956e624fca1"), // dlc.rpf/x64/data/effects/ptfx.rpf
    ShaUnpack("f2776e11ea97f71a92f29a2fb9a0b3876d6ffff9a3e1f53f81f47fdab28046d9"), // dlc.rpf/x64/data/lang/americandlc.rpf
    ShaUnpack("b2d4c0dd2e5fe6659be95cc7773603b54b92be3387a313287fd51dfa79c6b0cc"), // dlc.rpf/x64/data/lang/chinesedlc.rpf
    ShaUnpack("9195258fd40315cc9eed0ae1b5e67cc4415e1e8015c2e42fe9a7707b17835250"), // dlc.rpf/x64/data/lang/chinesesimpdlc.rpf
    ShaUnpack("f70ae72b3c5be67227460a57e784b7899bc23c34bbd341c438913eefe5044dec"), // dlc.rpf/x64/data/lang/frenchdlc.rpf
    ShaUnpack("2704ad3a94306aed6f3c94e24b60f523ac0e7efa2d2bb471258d344f8ee4bfe6"), // dlc.rpf/x64/data/lang/germandlc.rpf
    ShaUnpack("265188d9be98a757e8ed87bde3e5e7e19e9323d1c49991cad02cebf2cfbc51d1"), // dlc.rpf/x64/data/lang/italiandlc.rpf
    ShaUnpack("b9b6e81d0fc9e7d4117e249fce7aaa02ecec46c5b21ef853b5582cf3682dc0bc"), // dlc.rpf/x64/data/lang/japanesedlc.rpf
    ShaUnpack("0ea90087a612e7c918723328712d855113e5331e1f2b49708a03b8824b22c5f5"), // dlc.rpf/x64/data/lang/koreandlc.rpf
    ShaUnpack("4c90f2c04a893f335afdcab88cbec7dbe738a206b2cb97b90ba168e0c0adc5bb"), // dlc.rpf/x64/data/lang/mexicandlc.rpf
    ShaUnpack("d9d32bca1eaf8c36fc961db21967ef386e05e0c9bf9df2361316330be0b06a98"), // dlc.rpf/x64/data/lang/polishdlc.rpf
    ShaUnpack("ddeb2b776ed1202037dfef5e8aea55802609716dcd2fc03913fc2dd7a659e1af"), // dlc.rpf/x64/data/lang/portuguesedlc.rpf
    ShaUnpack("9b6d9d1b695b474225379cdf592cb929699117f8d92f99f0f14bebe322ac6d15"), // dlc.rpf/x64/data/lang/russiandlc.rpf
    ShaUnpack("ae706d65b642ba8ab296ce2d33da310c5bf27b30d13daa193e9771feca9b9a8e"), // dlc.rpf/x64/data/lang/spanishdlc.rpf
    ShaUnpack("3e52657f1b4ea61fdd5642ba75bc8af562b6912024d8a32954a578d88d09ac38"), // dlc.rpf/x64/levels/gta5/props/prop_as_accs.rpf
    ShaUnpack("ee84113446a20676904af7a6a1c0c5f50ab2622ef9d7eb649bae85e7199b34c5"), // dlc.rpf/x64/levels/gta5/props/prop_as_stunt.rpf
    ShaUnpack("58f07150d33b60179d9c6cd0c01e5133e929acf681f8b31ffe5766869703b149"), // dlc.rpf/x64/levels/gta5/props/prop_as_targets.rpf
    ShaUnpack("de94667ec09dc9835ccdd60da013544134b3d9d5fdc42fc7a1f59988b64b5e3e"), // dlc.rpf/x64/levels/gta5/vehicles/mpassault.rpf
    ShaUnpack("1c6903c967ce1ba0509cd3487e41af584b902ee0bed0afcaf1fa6f04469eaeae"), // dlc.rpf/x64/levels/mpassault/vehiclemods/caracara_mods.rpf
    ShaUnpack("98c24e270e9f78c4d99f229c8cd1abb5978b7f5dd10546eec9e57867a06f5f3b"), // dlc.rpf/x64/levels/mpassault/vehiclemods/cheburek_mods.rpf
    ShaUnpack("a5cf2b45b669469eee5ea0727904099d8a96f0714bc868a48fba2144ddc9ce30"), // dlc.rpf/x64/levels/mpassault/vehiclemods/dominator3_mods.rpf
    ShaUnpack("a54c91aeb3a03f482ae8dc844b92c40b930ffddfe1e393265719c6c03c935778"), // dlc.rpf/x64/levels/mpassault/vehiclemods/ellie_mods.rpf
    ShaUnpack("4b4538e5bc71d8b348f68cfdb2b486b0681222f9524b96ac5a4da25dadc42fdc"), // dlc.rpf/x64/levels/mpassault/vehiclemods/entity2_mods.rpf
    ShaUnpack("83d59821c1085cfdc1a81964b20906fe292573ef58621b86dcb87712fd761cf6"), // dlc.rpf/x64/levels/mpassault/vehiclemods/fagaloa_mods.rpf
    ShaUnpack("a3638c608f70ae70c749275bbebdc84985ef7b83ae1e6d1d30871095f7652eb6"), // dlc.rpf/x64/levels/mpassault/vehiclemods/flashgt_mods.rpf
    ShaUnpack("4f8a8bc2203b8309122365c4b2e208aaafd4f5f3d151bea43e1423a832d85ff0"), // dlc.rpf/x64/levels/mpassault/vehiclemods/gb200_mods.rpf
    ShaUnpack("65ecdabe272ba367f2edebe70ec618826d3f5eec1979d040dea1696c345c71b3"), // dlc.rpf/x64/levels/mpassault/vehiclemods/hotring_mods.rpf
    ShaUnpack("1f8135750654757d37201d047102bd1dfd270c0fca7877e8c5db8494dd7dc00d"), // dlc.rpf/x64/levels/mpassault/vehiclemods/issi3_mods.rpf
    ShaUnpack("19b3708aae465d083ed1fe42f0e5f289c64907fe3243036c084adeb86a6ea125"), // dlc.rpf/x64/levels/mpassault/vehiclemods/jester3_mods.rpf
    ShaUnpack("3900cb5bc67513c9b94c88acf90be43d2da8eeb2e15fa031fd15d2fd1b85d691"), // dlc.rpf/x64/levels/mpassault/vehiclemods/michelli_mods.rpf
    ShaUnpack("567e02db71c446183b1828ac7d0156ea118528374eee061423c72639f69a5d04"), // dlc.rpf/x64/levels/mpassault/vehiclemods/seasparrow_mods.rpf
    ShaUnpack("a9d7ac995e30fef49f76e4fd16352bf001b1d29a67f3e6cad464b9dd37af9003"), // dlc.rpf/x64/levels/mpassault/vehiclemods/taipan_mods.rpf
    ShaUnpack("952ceaa89f7afaccb768c5d56cf655321d9e9c7d30419e7b3a3a16c8c8bb1204"), // dlc.rpf/x64/levels/mpassault/vehiclemods/tezeract_mods.rpf
    ShaUnpack("9d88593de4b37f1bfa168fd3728b0396488ecfbf48d8b7619795b8ccb054ffb4"), // dlc.rpf/x64/levels/mpassault/vehiclemods/tyrant_mods.rpf
    ShaUnpack("dffe3f664e2aee7eb525dc8f288aa5906cc1656bfd46b2e2d8abcdfa530c4341"), // dlc.rpf/x64/models/cdimages/mpassault_female.rpf
    ShaUnpack("841606f05f9b6773f53c5f5fa0ea4d9a6ee680bb9bfbfe0ff74cc8a2a5d400b4"), // dlc.rpf/x64/models/cdimages/mpassault_male.rpf
    // update/x64/dlcpacks/mpbattle/dlc.rpf
    ShaUnpack("418b44283d6cc933c22cc6ba6683c3aad62fb294249fb01d065c2f1b3b038c11"), // dlc.rpf
    ShaUnpack("7074e6deb5ddf08d2b72a8526f654f7f331fdc7cc829c15e4763f3fd1526cd4b"), // dlc.rpf/x64/anim/creaturemetadata.rpf
    ShaUnpack("b40d3c5e1e4a6a0a20fbf66a92e7a994e2a884dc67f73fc8eb24aa24b48e4158"), // dlc.rpf/x64/anim/cutscene/cuts_bmad.rpf
    ShaUnpack("75bc28c3e248fedf66d7036dd634ad01c12b529b8d9d3e9507ca1288ad6822b7"), // dlc.rpf/x64/anim/cutscene/cuts_club.rpf
    ShaUnpack("a8fe7d515b24ef0483b7a0ae5d1984049d0cdd0cd4af2bdd979d871dfb44223e"), // dlc.rpf/x64/anim/cutscene/cuts_dixn.rpf
    ShaUnpack("17d424e7168b2f889ef1bb3113fbb4ff8c8582091912b5bb2c33ecf979d7fb8d"), // dlc.rpf/x64/anim/cutscene/cuts_smun.rpf
    ShaUnpack("af4f18b5b522fe29c2561911ee5ac7b5770d429e88c3fb7556b52dbf89e61ca9"), // dlc.rpf/x64/anim/cutscene/cuts_tale.rpf
    ShaUnpack("ba026abca214147489ab0939f037abc8d5987624b9c88f75f7407b29f22962a9"), // dlc.rpf/x64/anim/ingame/clip_anim@.rpf
    // update/x64/dlcpacks/mpbiker/dlc.rpf
    ShaUnpack("05ea56f31883921fb2dcf3d08c613d3788a4ab692142adf85658c8b9c8e389d8"), // dlc.rpf
    ShaUnpack("93791acb94b4f8239a8d590032c0d5346d30ee6accf5ef057bded3f30f1fa9c1"), // dlc.rpf/x64/anim/ingame/clip_anim@.rpf
    ShaUnpack("f4a17e20ad02f6f9f3dc038c426cdb7437383904bbd47f13d7526b96d23190b0"), // dlc.rpf/x64/audio/occlusion.rpf
    ShaUnpack("f4978648cd1ab57e675f3481c08b073d69ad79734050fe21e99368664a3ff419"), // dlc.rpf/x64/data/effects/ptfx.rpf
    ShaUnpack("e94ceea876d43ee06112b7353151b2df6372060de7a407a97c0a025eaba165ab"), // dlc.rpf/x64/data/lang/americandlc.rpf
    ShaUnpack("7eff557323863f0ab82e66b253ce3145e6e75feb7dab38f025f285ef93fa8736"), // dlc.rpf/x64/data/lang/chinesedlc.rpf
    ShaUnpack("7da14996fa3fbb8c628a26c514f52c71f5fc1d556284c6336e38490478e807a9"), // dlc.rpf/x64/data/lang/frenchdlc.rpf
    ShaUnpack("9147ad0b1015e3a295593d2ca588daff8304ebd98542e313f739187e7ba57532"), // dlc.rpf/x64/data/lang/germandlc.rpf
    ShaUnpack("8b9583a287f6bea3ee93c5ae64fa7306111fbe4ac5d0d9a9173c64a82053d419"), // dlc.rpf/x64/data/lang/italiandlc.rpf
    ShaUnpack("7772901dae3d45f1b2d9de6535afb37bc6e9ed131c1e111be919a423bb90c4a2"), // dlc.rpf/x64/data/lang/japanesedlc.rpf
    ShaUnpack("500e901d519cf2e47e9dd01d4a88af9b2ca9dbb01b533e4dc62eab2fbbba9bac"), // dlc.rpf/x64/data/lang/koreandlc.rpf
    ShaUnpack("bd68035ce821702d1adaa78d4eca686e99898f73c982763bcdd086e4c5f8d74d"), // dlc.rpf/x64/data/lang/mexicandlc.rpf
    ShaUnpack("c7ed8f11081426c56f9d9acf61f3fc3fc5abc25fba5e3155f58e5ad81723f40b"), // dlc.rpf/x64/data/lang/polishdlc.rpf
    ShaUnpack("e439bf0e97072d33077e66be73325589259b828ff550d5eb30eff5c44391bcf0"), // dlc.rpf/x64/data/lang/portuguesedlc.rpf
    ShaUnpack("328b6345714612393d6ecd8940b164b8e584753975988d648d89b127eb3ea60e"), // dlc.rpf/x64/data/lang/russiandlc.rpf
    ShaUnpack("a371dd10a681d19d835780343df24d4e26d41568457dcfb207b710b643b765dd"), // dlc.rpf/x64/data/lang/spanishdlc.rpf
    ShaUnpack("d2da493598d73b312d9143bd517a3dd4513656c2a03b7490225f242acb883676"), // dlc.rpf/x64/levels/gta5/_citye/indust_01/id1_06.rpf
    ShaUnpack("11b67f77fb3edbfc54350e434694e94de963d5e94ad4d99cadf8e56505300759"), // dlc.rpf/x64/levels/gta5/_citye/indust_01/id1_07.rpf
    ShaUnpack("2a071fbc1d0afce0bdfef6198a1d6747f854ca441879bdd29d7326ac5b733f50"), // dlc.rpf/x64/levels/gta5/_citye/indust_01/id1_23.rpf
    ShaUnpack("272b94d03e68fa11dcf5892d7e8a728978e9dd3df474058bb79bbea402067310"), // dlc.rpf/x64/levels/gta5/_citye/indust_01/id1_occl.rpf
    ShaUnpack("3632aeeb5f33e33cf5fefe7d96874928121301d537d4fc569fd76fc028254a74"), // dlc.rpf/x64/levels/gta5/_citye/indust_01/indust_01.rpf
    ShaUnpack("10fb3bd083bfb1f5bc4fe40216369d6d5716c80c70575df3c7abc56ba79da47e"), // dlc.rpf/x64/levels/gta5/_citye/indust_01/indust_01_metadata.rpf
    ShaUnpack("f8d6d8b611c8c7971db1e8caf4565aa9dfeba99c35b805ab07a63f8b6fd25451"), // dlc.rpf/x64/levels/gta5/interiors/biker_dlc_int_01.rpf
    ShaUnpack("1eeb37773953aafcc2c1a3acd12c2dc969cc92fe0c1fda3715ab77dbe5f22764"), // dlc.rpf/x64/levels/gta5/interiors/biker_dlc_int_02.rpf
    ShaUnpack("152a6c8ab8cf1b2793f6af6644fbf59ce03d449af766fbc151d66ebfe0f6afbf"), // dlc.rpf/x64/levels/gta5/interiors/biker_dlc_int_03.rpf
    ShaUnpack("5285d165bbfbdc35337f7766b9100d9f48a77b51824af314dc20cb758dbe1b3a"), // dlc.rpf/x64/levels/gta5/interiors/biker_dlc_int_ware01.rpf
    ShaUnpack("48ea5dc5a820df9454ad8bd1f008def10513d73c6ab96543f22f2ad7ea133700"), // dlc.rpf/x64/levels/gta5/interiors/biker_dlc_int_ware02.rpf
    ShaUnpack("b7cb1d472a410637e49e25e95c03ddaa38a4f74c772da88ee0d8ed9b9f5f89b1"), // dlc.rpf/x64/levels/gta5/interiors/biker_dlc_int_ware03.rpf
    ShaUnpack("a4ce375dd27d54e139ca1da259954836020ca7f3ad3c72250420b8f34e8e02f9"), // dlc.rpf/x64/levels/gta5/interiors/biker_dlc_int_ware04.rpf
    ShaUnpack("f0abab79375fc857b86098f70a5e78199046246fd49c5e882c85608b6997d3c1"), // dlc.rpf/x64/levels/gta5/interiors/biker_dlc_int_ware05.rpf
    ShaUnpack("05d473c73073246172481292fb78dffd7c030ff72c4d456d64148e24b0fbc685"), // dlc.rpf/x64/levels/gta5/interiors/biker_interior_placement.rpf
    ShaUnpack("fbd84ddf53c30793f5fb82ee40271d090d2210c76583cc89e141a4964dce752d"), // dlc.rpf/x64/levels/gta5/props/biker_dlc_int_props.rpf
    ShaUnpack("707f4ec96143a74665bca51cea879b40efdfd75c3b55d9c54ce412b28ce5fa42"), // dlc.rpf/x64/levels/gta5/props/mp_biker_clubhouse.rpf
    ShaUnpack("3ca006effa110cd221522539fdc3f807d1817de2254e706eb4834c0d5ead8cfc"), // dlc.rpf/x64/levels/gta5/props/mp_biker_coke.rpf
    ShaUnpack("f3b5e54432c1043ac290840cb2f80b54218ad351b16161458a3771a866628f30"), // dlc.rpf/x64/levels/gta5/props/mp_biker_deadline.rpf
    ShaUnpack("31ac55b810feb17acdccb20f9b6ef386086ed80893a40c48cc0a54c8de9b8dbd"), // dlc.rpf/x64/levels/gta5/props/mp_biker_deadline_jumps.rpf
    ShaUnpack("9c5eaf7b3f2a644d632dfc93df3c9c445ae8e2715fc52f8120f418c622eb7843"), // dlc.rpf/x64/levels/gta5/props/mp_biker_deadline_tubes.rpf
    ShaUnpack("2286bbef524e05e40dc26fbf11d5ce1d107058511807d4a916c0cdc7528ac958"), // dlc.rpf/x64/levels/gta5/props/mp_biker_fakeid.rpf
    ShaUnpack("8b525c85038b1090006fd859a78dd74d31d336f347b52c41fc9a6f66dd49d899"), // dlc.rpf/x64/levels/gta5/props/mp_biker_gunlocker.rpf
    ShaUnpack("321bea6ecd9297e01d391a17ba8eb102bd17f5c902d2cfc57dbd6d6d7f204006"), // dlc.rpf/x64/levels/gta5/props/mp_biker_meth.rpf
    ShaUnpack("e22fddeaf705e9808d488f5e03a08aadd8d54d851431ac26ce6bbb2ba389ad02"), // dlc.rpf/x64/levels/gta5/props/mp_biker_money.rpf
    ShaUnpack("ec8676053839fa2b56830a6ca571e2cb98c7a49231779f4de28f899f2a015687"), // dlc.rpf/x64/levels/gta5/props/mp_biker_printingmachines.rpf
    ShaUnpack("3a16a5aa81fe1959006fee4965ebcb90a1273f5bc48a4b1e8a505e7e82dbfbe4"), // dlc.rpf/x64/levels/gta5/props/mp_biker_weed.rpf
    ShaUnpack("d1a0ad28f988c32dfaccf0603697e036cc082d04e7f46f223d04540e240d7b50"), // dlc.rpf/x64/levels/gta5/props/prop_clubhouse_jukebox.rpf
    ShaUnpack("f5e5f426fe3f604f8055978523bcd2fa10f483d3fae4585d4caa6d182c16b3dc"), // dlc.rpf/x64/levels/gta5/props/prop_clubhouse_seating.rpf
    ShaUnpack("3cb89d7f8dde6dbe0998b390384d3d97b283e43b88623b9498c64d4f7c17b189"), // dlc.rpf/x64/levels/gta5/props/prop_grenades.rpf
    ShaUnpack("6f333f15e56bf9861917d96f71a9a7e39068daa4d9745565db108d1593956956"), // dlc.rpf/x64/levels/gta5/props/prop_memorial_wall.rpf
    ShaUnpack("4015f1c2105770986a381feedc0096cb93c9094b414ba61bf7f259eacb22d5dd"), // dlc.rpf/x64/levels/gta5/props/prop_rt_clubhouse_table.rpf
    ShaUnpack("1dc7918d6d6f06ce57a0bbe11aacef395db2e3b14a554d51c2664a6360cb3bfa"), // dlc.rpf/x64/levels/gta5/props/prop_slow_down.rpf
    ShaUnpack("350d71fe020a4c4e76afaf3bc2f2bbbe8c9f464c331ebbddf4ac964b11042b37"), // dlc.rpf/x64/levels/gta5/vehicles/mpbikervehicles.rpf
    ShaUnpack("921c1a87ce8d97c21819dea23bdbf64637553436a207e8a90874f0cac4c224e3"), // dlc.rpf/x64/levels/mpbiker/vehiclemods/avarus_mods.rpf
    ShaUnpack("f952100b573a35d2d4708191ed4fa23ac8069395cb7d2a0d35f4ac2c450f74f5"), // dlc.rpf/x64/levels/mpbiker/vehiclemods/blazer4_mods.rpf
    ShaUnpack("49c7742b40d2e3c9dde58398e2aa71fcb0eaa3878ba85c8dc09e0b708f97ccdb"), // dlc.rpf/x64/levels/mpbiker/vehiclemods/chimera_mods.rpf
    ShaUnpack("ccfdeece04b007634b545a085aa6a9b2ec05bf56d74f33b46f6dcebdffa144d1"), // dlc.rpf/x64/levels/mpbiker/vehiclemods/daemon2_mods.rpf
    ShaUnpack("ff7dd2f9f2120436374b8a533c10d849c4171d828e182a81178888cdb6fd0c46"), // dlc.rpf/x64/levels/mpbiker/vehiclemods/defiler_mods.rpf
    ShaUnpack("b1713f722ad8e1a752b02c6fb88e80ed7d9a6ccd9ddad2c626f41ca21271d7b6"), // dlc.rpf/x64/levels/mpbiker/vehiclemods/esskey_mods.rpf
    ShaUnpack("9b4f2844d92ea25c60b82d5bc644301754935789277f895aaf34323f2d2526b8"), // dlc.rpf/x64/levels/mpbiker/vehiclemods/faggio3_mods.rpf
    ShaUnpack("675014d469f87dddff4d27b20f303ae9658a4ac1a2847122e3b6e6b80e66d239"), // dlc.rpf/x64/levels/mpbiker/vehiclemods/hakuchou2_mods.rpf
    ShaUnpack("7166ac10209213839d50d1d2842a256110b6b4c3145f3bc7aa10bb862a167f9a"), // dlc.rpf/x64/levels/mpbiker/vehiclemods/manchez_mods.rpf
    ShaUnpack("9c5893b5cee84f7a253f5f67ebf9f6c2ac29c3ece45972cc674bdc0ca1df8f6a"), // dlc.rpf/x64/levels/mpbiker/vehiclemods/nightblade_mods.rpf
    ShaUnpack("9ec29e611f340bdc12cc12f532d2bb10c7548e865582d77538ea5742f3695d6a"), // dlc.rpf/x64/levels/mpbiker/vehiclemods/ratbike_mods.rpf
    ShaUnpack("86c01e4b9bc6427626821d71657011cf698c32d3672b76db590faccf5f3b1c42"), // dlc.rpf/x64/levels/mpbiker/vehiclemods/sanctus_mods.rpf
    ShaUnpack("1b31885e657df4ae8be48de02588e872cc3d5494b99f2d169d2985f4d62b86ed"), // dlc.rpf/x64/levels/mpbiker/vehiclemods/shotaro_mods.rpf
    ShaUnpack("c36b99da14deae66c5306841b7abccb8774737006a6a45a1b7404e9173308467"), // dlc.rpf/x64/levels/mpbiker/vehiclemods/tornado6_mods.rpf
    ShaUnpack("7db5160e939b20b9358455520723f3aecf18018688f5dfe3578efa10029bf13c"), // dlc.rpf/x64/levels/mpbiker/vehiclemods/vortex_mods.rpf
    ShaUnpack("6ee267e9d81e05af581814f59cb8d9b6838f6015406f830223893da73fa0bc31"), // dlc.rpf/x64/levels/mpbiker/vehiclemods/wheels3_mods.rpf
    ShaUnpack("4fb0fe326fb094f0fe37c6432f916fc74fc9d27f64435ef465cfb8a71de8060f"), // dlc.rpf/x64/levels/mpbiker/vehiclemods/wolfsbane_mods.rpf
    ShaUnpack("ded4dc86ab40cb202cf3949f74b0a9fb8e1687a780b5b478e767028370baf9a5"), // dlc.rpf/x64/levels/mpbiker/vehiclemods/youga2_mods.rpf
    ShaUnpack("0dfe7910722e866308fca7c1e1ab8a7f289afa734fcaf7ff3c6adc8b06412a76"), // dlc.rpf/x64/levels/mpbiker/vehiclemods/zombiea_mods.rpf
    ShaUnpack("9efdd9c3a0ba381ef41dfbd3d35a94871b2e817557f2fc4cfe4aa8865c99646b"), // dlc.rpf/x64/levels/mpbiker/vehiclemods/zombieb_mods.rpf
    ShaUnpack("b315e48d9ef37524deef844cb16e48c21514216f8e6cff5c089a3f09eacca30d"), // dlc.rpf/x64/models/cdimages/mpbiker.rpf
    ShaUnpack("b17f2b95fe14df74343ed3309934908d94b0e227e736deaaa179f59a22c41931"), // dlc.rpf/x64/models/cdimages/mpbiker_female.rpf
    ShaUnpack("aa1fc48ed0ec1ae24eb8c74989889d98e635b2f00a2226910a4b8d4881b9c710"), // dlc.rpf/x64/models/cdimages/mpbiker_female_p.rpf
    ShaUnpack("7e83181d09cee5ae1fd2bbd8f38d27d9fa0fa570859c8bc6df5b2c8c8cbffdf6"), // dlc.rpf/x64/models/cdimages/mpbiker_male.rpf
    ShaUnpack("d6e91ec79a3d3dcefab25ad48c27ece91fec69408696968918a5f61b2755ea73"), // dlc.rpf/x64/models/cdimages/mpbiker_male_p.rpf
    ShaUnpack("0e0dd410dc32383429b04660805874940df6297310f122488f7eb48f24402b55"), // dlc.rpf/x64/models/cdimages/mpbiker_p.rpf
    ShaUnpack("7b5be9ae44d38bce27cc667f174425c8be0a6beb63a55cf154aefad842e98aed"), // dlc.rpf/x64/models/cdimages/mpbiker_ped_mp_overlay_txds.rpf
    ShaUnpack("b7184eb818c08d89e0136385a92bdf6b4ae9f450d5b545c41c36a7cdcce317ea"), // dlc.rpf/x64/models/cdimages/weapons.rpf
    // update/x64/dlcpacks/mpchristmas2/dlc.rpf
    ShaUnpack("3242ba696a77894f6b21f8a6efe0fc7038ce666c4788b49c3d7f14049fbd44f3"), // dlc.rpf
    ShaUnpack("f332bcda4e40b879d662a0d9d3ba96c4995e4b27c9d1ea341e41215aad8842b4"), // dlc.rpf/x64/anim/creaturemetadata.rpf
    ShaUnpack("6fe99d8d26c222d927234e6ce5208ad1d2f391b3bf1c6f7eabb1b51c82c76e61"), // dlc.rpf/x64/anim/ingame/clip_anim@.rpf
    ShaUnpack("6863511e16c8e17761e392170e16fc8b675358a24e01d2c914071727ed689cdf"), // dlc.rpf/x64/data/effects/ptfx.rpf
    ShaUnpack("b05b4fcafe8085777c9df4f4f4bff1a1d7ab48daaf0324afaef2cc68f79c9658"), // dlc.rpf/x64/data/lang/americandlc.rpf
    ShaUnpack("84460904a7eedb6f78077edfdc02ab80c9a96a1cfdedb3075dfc4ab53f65155a"), // dlc.rpf/x64/data/lang/chinesedlc.rpf
    ShaUnpack("99411df16993303bdb05e859772cd2a615602a351fb830879a8e2814ec5fd955"), // dlc.rpf/x64/data/lang/frenchdlc.rpf
    ShaUnpack("aebbd8aff30bc95b818d98eedf509f172e2b81c68fef844730cc19669c394d74"), // dlc.rpf/x64/data/lang/germandlc.rpf
    ShaUnpack("416c3b0aae2f410ac05099e6f6360056781c717966b149abeae6ecc4b919fb6d"), // dlc.rpf/x64/data/lang/italiandlc.rpf
    ShaUnpack("d428236c6d88d1a37d247390008b1db9f33c80bdd2da11892b5cd75a28adcca5"), // dlc.rpf/x64/data/lang/japanesedlc.rpf
    ShaUnpack("12f80d8279beff7a14b7f977b11d978a93d36e3e1e9729553164bb0f7338ae87"), // dlc.rpf/x64/data/lang/koreandlc.rpf
    ShaUnpack("6f5843d0962474a9e29cc1f540d5e17b4656ae9cbd5169021e94f297d219ed8d"), // dlc.rpf/x64/data/lang/mexicandlc.rpf
    ShaUnpack("7ee215e00933210173d89dfe029cd5ebcde90efbd965d9d007958fa71ab08abb"), // dlc.rpf/x64/data/lang/polishdlc.rpf
    ShaUnpack("7ada459d54d5378b1777f58b30e66c290b8d0a5008036d84f4a9b10e7ecfa35a"), // dlc.rpf/x64/data/lang/portuguesedlc.rpf
    ShaUnpack("1b59c2e3e0b81139c60383500dc66a3f0bfda0e69952dd9684c259e03792fbe1"), // dlc.rpf/x64/data/lang/russiandlc.rpf
    ShaUnpack("0fee4202f38d482f98920ecd698d8575c5a384f3b92302325333717cc6785031"), // dlc.rpf/x64/data/lang/spanishdlc.rpf
    ShaUnpack("ab00122f72aefabdfa7c3eb9139c25589d16d82f5ba3e7518d21aec1815107fb"), // dlc.rpf/x64/levels/gta5/props/v_christmas_tree.rpf
    ShaUnpack("59f83158c3990926ac37de13e08ec2dfcbbc3bc0cacacec6e90155468e3e9266"), // dlc.rpf/x64/levels/gta5/vehicles/xmas2vehicles.rpf
    ShaUnpack("17751fcb33532f6b716890da55fc34608723cb563996e2d0d5b1a7a0a36f1ddd"), // dlc.rpf/x64/levels/mpchristmas2/vehiclemods/jester2_mods.rpf
    ShaUnpack("f143f8857856f07448c96c3deb3d2813ddcd601a752dac276959e72784c48ad6"), // dlc.rpf/x64/levels/mpchristmas2/vehiclemods/massacro2_mods.rpf
    ShaUnpack("b7274c821d6760ce397545107b75bc86184ce1aa0205d7f345cad40e995515e4"), // dlc.rpf/x64/levels/mpchristmas2/vehiclemods/ratloader2_mods.rpf
    ShaUnpack("ee4a590da76a198e30dd29b558012c34249d3255b47eefa9aecc99c9fc671368"), // dlc.rpf/x64/models/cdimages/mpchristmas2_ped_mp_overlay_txds.rpf
    ShaUnpack("cd6c3e737a0a100cbd6289d89b427d7615d4ffd3a0c3e44b250a1e17c7a8446f"), // dlc.rpf/x64/models/cdimages/mpxmas2.rpf
    ShaUnpack("4e6d7b560d53c2fc4c5d9241b97511e718a0067c31eec2bfa251e3f745516557"), // dlc.rpf/x64/models/cdimages/mpxmas2_p.rpf
    ShaUnpack("0623e77dc25985f914f5f7f3d3c2d9621e24029e33100b96e7226738ebd467f5"), // dlc.rpf/x64/models/cdimages/weapons.rpf
    // update/x64/dlcpacks/mpchristmas2017/dlc.rpf
    ShaUnpack("8bb538bb3bcd051bbda35e3153bea6f250e9fa2f1a442604b308dfcc7b5ff176"), // dlc.rpf
    ShaUnpack("6bda2ba4b70fb93e642e84e37e704d10c0295eee1595bf504eb4db8a3fd5caca"), // dlc.rpf/x64/anim/expressions.rpf
    ShaUnpack("fc3741964c774ab1b782960ddb35954f236a8e1896168176095dd4999e6cdc29"), // dlc.rpf/x64/anim/cutscene/cuts_iaa.rpf
    ShaUnpack("41ae093877834eeb72af37ceed4bd7d0f9244f2a660b5ad161cf93b33651c451"), // dlc.rpf/x64/anim/cutscene/cuts_iaaj.rpf
    ShaUnpack("37e4e3315815fe0ba8be841c96685e79e9ac2b8ba37c449e409d9d129ba31320"), // dlc.rpf/x64/anim/cutscene/cuts_sil.rpf
    ShaUnpack("d47b925d51da5cf4af10dd8122820d032a115be24eb2852b33c32ff2c9a80f7e"), // dlc.rpf/x64/anim/cutscene/cuts_silj.rpf
    ShaUnpack("e0d9992347472d911b490613ca66f08499f97a630f58fd588a5648a167330e8f"), // dlc.rpf/x64/anim/cutscene/cuts_sub.rpf
    ShaUnpack("ed25627bda2d1d620145a97a81daeae7fa8586ce2f86bbf60969b2b31537fb6c"), // dlc.rpf/x64/anim/cutscene/cuts_subj.rpf
    ShaUnpack("8d9dd18f6d5b135921d8d22dabb37ab53da93d8a6e6bd3f53d26c8441dfd4164"), // dlc.rpf/x64/anim/ingame/clip_anim@.rpf
    ShaUnpack("ff2fa2260c67fe04e4495d001b2c5951a4e82a0cca65c9d6fb9eedd9601689b2"), // dlc.rpf/x64/audio/occlusion.rpf
    ShaUnpack("e68932540ecbb52293fb6f460a5e1781573a39daa5f7ab831867a0d496989c5e"), // dlc.rpf/x64/data/effects/ptfx.rpf
    ShaUnpack("448ade7a8254cd08bcc5678961d2b8ce7868763ef3d2e4cd5789898ff5c71577"), // dlc.rpf/x64/data/lang/americandlc.rpf
    ShaUnpack("556be6dd7059ef693438c43f8a617a7144149132274402f0808c5acb8f6bd74a"), // dlc.rpf/x64/data/lang/chinesedlc.rpf
    ShaUnpack("53bf09107ea321b16d66bf5fd6e4fdf0deb820cee44676b8ad5d506ffc85bb91"), // dlc.rpf/x64/data/lang/chinesesimpdlc.rpf
    ShaUnpack("9af958160730aae1d64bd07a8f62a65b59125a7f572e721c3864e6b0ce816738"), // dlc.rpf/x64/data/lang/frenchdlc.rpf
    ShaUnpack("882e143aed8eefe4bdc1351adb65dde05244da8fe85be66ba20ba3c86cd790af"), // dlc.rpf/x64/data/lang/germandlc.rpf
    ShaUnpack("98b9788edd436e9f5296abba805875f26025a2bbfac0ac7d23d498feada26e36"), // dlc.rpf/x64/data/lang/italiandlc.rpf
    ShaUnpack("ee7f26e0a7600c90b83ca9222324864d094df99f81006e654bde6de86f88d1c9"), // dlc.rpf/x64/data/lang/japanesedlc.rpf
    ShaUnpack("9babae7a956547e4f90a1940bbde18cf7bde375f500522df171b7e4713d9f445"), // dlc.rpf/x64/data/lang/koreandlc.rpf
    ShaUnpack("08066ad10e3fea4fb7ead448e4b0a881ce86a3abf115b283988a3bdda4402069"), // dlc.rpf/x64/data/lang/mexicandlc.rpf
    ShaUnpack("a954de1aa35b7d42a88109b83f4f7265407c2a4866633871727f65a2b95f2ad2"), // dlc.rpf/x64/data/lang/polishdlc.rpf
    ShaUnpack("f105935be4328d38ccf49224d4974addfc22cbef72e1543a57583c768d355f00"), // dlc.rpf/x64/data/lang/portuguesedlc.rpf
    ShaUnpack("a3d8d3e07e80a3d0e012468303e1d3c9758694074f29461e40a469fc454611b4"), // dlc.rpf/x64/data/lang/russiandlc.rpf
    ShaUnpack("041f14703b5534ae586c7fa667963eff894d4541c07da45306f4504908e78fc8"), // dlc.rpf/x64/data/lang/spanishdlc.rpf
    ShaUnpack("05bdbf5c07ea17b58135dee1bddcb57f972c86f44559f00e4ad534a40cf4dccd"), // dlc.rpf/x64/levels/gta5/navmeshes.rpf
    ShaUnpack("503e5100d2d39e2ee003f317147e678f7cc3410355b02e49dbb5b1346776c47e"), // dlc.rpf/x64/levels/gta5/additions/mpchristmasadditions.rpf
    ShaUnpack("59a9d4a0ed80f8b815a7f85f1027811a413caeb75c739d1f532bf4514276dea6"), // dlc.rpf/x64/levels/gta5/interiors/x17dlc_int_01.rpf
    ShaUnpack("da9fa237cd6ec773a5157bf8d9ef76e60fe82f4919882e3eede9f98c1c7d8337"), // dlc.rpf/x64/levels/gta5/interiors/x17dlc_int_02.rpf
    ShaUnpack("105e809b5ce8c8f4667a188aa1d1d85bc1ec6e5e4058d2352249ff99d3e03672"), // dlc.rpf/x64/levels/gta5/interiors/x17dlc_int_base.rpf
    ShaUnpack("4da2d472395cf60e40f13ab9423dd2885a26c261ee15873af954bdf7cc22d836"), // dlc.rpf/x64/levels/gta5/interiors/x17dlc_int_facility.rpf
    ShaUnpack("c1b8a85e6b4bdcbcea19b938cbbfec042ceec2839af3e6147189460760ad124e"), // dlc.rpf/x64/levels/gta5/interiors/x17dlc_int_facility2.rpf
    ShaUnpack("762b95df55c901c777f0d8131489b05abb9cc59f220fd67f1421fc22e616287a"), // dlc.rpf/x64/levels/gta5/interiors/x17dlc_int_lab.rpf
    ShaUnpack("739b0b66446833a60bc7ffc93e033ad1cac6e0d9e4e436e74d9136d999bd1a8c"), // dlc.rpf/x64/levels/gta5/interiors/x17dlc_int_placement.rpf
    ShaUnpack("2a51a88983face7fb05097c5cce4b1a764b7d0309ccc62d3f7cd7f064e309fb0"), // dlc.rpf/x64/levels/gta5/interiors/x17dlc_int_silo.rpf
    ShaUnpack("178071f57cefadbd20f52d7e501a005465e2bb6f8a4da8824ada2af3967f549e"), // dlc.rpf/x64/levels/gta5/interiors/x17dlc_int_sub.rpf
    ShaUnpack("48584b3b61a868b4ebc9fa93d1134b73156bab188a32d0a45a9c7e2b31297ad9"), // dlc.rpf/x64/levels/gta5/props/prop_x17dlc_monitor_wall_01a.rpf
    ShaUnpack("c3218f2da1ca5ba70df5c8bc361d3960c9e24ec2c3f75ec261d05cfa43bb49e6"), // dlc.rpf/x64/levels/gta5/props/prop_x17dlc_rep_sign_01a.rpf
    ShaUnpack("56087ed515532a0816dbf365ba2243785ea50f57479bac111040ae26e70fbcad"), // dlc.rpf/x64/levels/gta5/props/x17dlc_props_accs.rpf
    ShaUnpack("5ae5e9b801fe84f98a2c7aa6ef4053dab40646bcc7b667752167ef67f8d06112"), // dlc.rpf/x64/levels/gta5/props/x17dlc_props_accs_01.rpf
    ShaUnpack("851a296032ac0304e37de09b787a245e54d1f36640620e130cd7d99389a529bb"), // dlc.rpf/x64/levels/gta5/props/x17dlc_props_auto_salvage.rpf
    ShaUnpack("8fba59a733ab113bf766c4520d331a66217b9e0796ef3229177f4cdd3e2e5fac"), // dlc.rpf/x64/levels/gta5/props/x17dlc_props_barge.rpf
    ShaUnpack("4fef09fb94a3c6265edfe2d755128b14d22f4d0882972a3cd92c589748ace849"), // dlc.rpf/x64/levels/gta5/props/x17dlc_props_base.rpf
    ShaUnpack("af28bfcc105d8969398dc3875f203944fbc735ebda751f64dede3962ff0e8727"), // dlc.rpf/x64/levels/gta5/props/x17dlc_props_cover.rpf
    ShaUnpack("2acdc2180d0a807eaf869fdf630ca7d6f58424d6dbab91a7dad89a47294f1e38"), // dlc.rpf/x64/levels/gta5/props/x17dlc_props_facility.rpf
    ShaUnpack("070515de19f62ac8af9f69a34dcc9b6cc165c49ac3d56494d65c3628bf775fb9"), // dlc.rpf/x64/levels/gta5/props/x17dlc_props_facility_01.rpf
    ShaUnpack("1868618548dee93988ed50656304bebc2fbddf511c80f80af8ed7e0cf07f8a4f"), // dlc.rpf/x64/levels/gta5/props/x17dlc_props_int_lev_des.rpf
    ShaUnpack("75c3694f263add10a07c6cecf056473ca3324e050be2f2223461899365b370e8"), // dlc.rpf/x64/levels/gta5/props/x17dlc_props_lab.rpf
    ShaUnpack("2ef8a83646170e47089e6ef743ae5abb507c1896e01f3d21b71ce95f6177288b"), // dlc.rpf/x64/levels/gta5/props/x17dlc_props_mines.rpf
    ShaUnpack("2a5f2692175c00b275f1170ff2cf3cf3b4efa8eb7fc8d356a788268fda5bc722"), // dlc.rpf/x64/levels/gta5/props/x17dlc_props_osphatch.rpf
    ShaUnpack("b4c25d8fb6496de99110fe30ccbdf6b5d2917ba21f54a4cea2ba03b32e584e6c"), // dlc.rpf/x64/levels/gta5/props/x17dlc_props_para.rpf
    ShaUnpack("a0fa837cb0972349d9ede78cdbea23f70b5d97ca884ee0f8900e33ef0385a56f"), // dlc.rpf/x64/levels/gta5/props/x17dlc_props_sam.rpf
    ShaUnpack("6ac57a60f8326f0a65e2332de518f0e2f3930834efaac3b9a3ea6d37dbc66dbb"), // dlc.rpf/x64/levels/gta5/props/x17dlc_props_shamal.rpf
    ShaUnpack("24645517859b3e8434a238e70c435796e40a582c2522272f61617127ab1b6a89"), // dlc.rpf/x64/levels/gta5/props/x17dlc_props_silohatch.rpf
    ShaUnpack("93f03441f68bcb4d1648c8d594a30bcff399408d41067f237463805f98d6e3c6"), // dlc.rpf/x64/levels/gta5/props/x17dlc_props_sub.rpf
    ShaUnpack("6e99c0b5666f3be76b3f7c2489b4399485845fd44fb438f9b62bf9dc6faec22f"), // dlc.rpf/x64/levels/gta5/props/x17dlc_props_trail.rpf
    ShaUnpack("83d0a6b02ba21281145ac39977d448c2f697418c6f21b320824572aabdc3227b"), // dlc.rpf/x64/levels/gta5/vehicles/mpchristmas2017.rpf
    ShaUnpack("3dff95dcf72ddfa79a7f1d8fcbc3dab810a468add319ed8fa3b6cdc7a2104629"), // dlc.rpf/x64/levels/mpchristmas2017/vehiclemods/akula_mods.rpf
    ShaUnpack("a5fe9fa65ac9f9284c50ca6ae4d42e574fdbeed09d92a42d84ff2607f0106f01"), // dlc.rpf/x64/levels/mpchristmas2017/vehiclemods/autarch_mods.rpf
    ShaUnpack("6a2c59cf7d7a51ae63c37b9b9781a00a4b8f5269912d60402c820ea26baf908f"), // dlc.rpf/x64/levels/mpchristmas2017/vehiclemods/avenger_mods.rpf
    ShaUnpack("4b61d32fb602917327a16448509560fdf119c559f31c39a85efe480cca576402"), // dlc.rpf/x64/levels/mpchristmas2017/vehiclemods/barrage_mods.rpf
    ShaUnpack("93f3781ce6806cd3080772580d48c6fbe006cc4663a338073b45f2faa12ac64a"), // dlc.rpf/x64/levels/mpchristmas2017/vehiclemods/chernobog_mods.rpf
    ShaUnpack("803d8bb2be916d9f16dfec087376ebafd9bcc12c87d1cbe8b2669beceb59869a"), // dlc.rpf/x64/levels/mpchristmas2017/vehiclemods/comet4_mods.rpf
    ShaUnpack("5d81ecc3ddf2c2e018438102cb8c9ae9633f03dca78abb77d3b5e06f0d239c39"), // dlc.rpf/x64/levels/mpchristmas2017/vehiclemods/comet5_mods.rpf
    ShaUnpack("12ded50fc4b54e1e74208d25b4b2d200bd23db1de9da42a3a656f418f842b021"), // dlc.rpf/x64/levels/mpchristmas2017/vehiclemods/deluxo_mods.rpf
    ShaUnpack("f1308b51859448a62de5d33dc26e972e0c77788bdf816a34ab05e60a29afc5ae"), // dlc.rpf/x64/levels/mpchristmas2017/vehiclemods/gt500_mods.rpf
    ShaUnpack("5af224cd8c9440b6beda96bafea5878ebcc9dfd920de1d2af65c3005765c6490"), // dlc.rpf/x64/levels/mpchristmas2017/vehiclemods/hermes_mods.rpf
    ShaUnpack("0e65b923d97b5ee84a90cc6deea2fdca43653a73dc4cc07bc6a60ab36afdf4ff"), // dlc.rpf/x64/levels/mpchristmas2017/vehiclemods/hustler_mods.rpf
    ShaUnpack("5fbce9d928e3a666356bfe5f4b345cc34075244a71b3f9b1df91dae20a8deba3"), // dlc.rpf/x64/levels/mpchristmas2017/vehiclemods/kamacho_mods.rpf
    ShaUnpack("cbde7f6b67e9859d5b324911b8e3a759fd93ecb26eabcb625afa21dab35c8440"), // dlc.rpf/x64/levels/mpchristmas2017/vehiclemods/khanjali_mods.rpf
    ShaUnpack("b9004fb7760c11ca21bbe95bb7354d3716f0e889cdc0c2e451aadec5fa635aa0"), // dlc.rpf/x64/levels/mpchristmas2017/vehiclemods/neon_mods.rpf
    ShaUnpack("d4b0ff1d9df4d4f2eda14eb3734f38092cfc4dae78dbf02f0cc4e70c1bdfcdca"), // dlc.rpf/x64/levels/mpchristmas2017/vehiclemods/pariah_mods.rpf
    ShaUnpack("ee2410d167406d4322d1e88f6bac84fc1fa9544cf076c273225096ffaba01fb5"), // dlc.rpf/x64/levels/mpchristmas2017/vehiclemods/raiden_mods.rpf
    ShaUnpack("7cff04dd53d31a63e9446eeb5982c3765c75837bd1bd423f5f8b075a86cfca41"), // dlc.rpf/x64/levels/mpchristmas2017/vehiclemods/revolter_mods.rpf
    ShaUnpack("ebe884a1881dddbf62706b5769a4c0bd0c21bd3a6d91602a8c78163724d2ae38"), // dlc.rpf/x64/levels/mpchristmas2017/vehiclemods/riata_mods.rpf
    ShaUnpack("4ec3b108f3e72550d56d1c914bae0ba2de0f1bb89ada431468f3bfb2bd88e7e4"), // dlc.rpf/x64/levels/mpchristmas2017/vehiclemods/riot2_mods.rpf
    ShaUnpack("a4c21554ecfb893d04b0fdb0b0a750227146482e0b39b9ad4657444eff3b74bf"), // dlc.rpf/x64/levels/mpchristmas2017/vehiclemods/savestra_mods.rpf
    ShaUnpack("b819ba79704d6346e75fe85f55c95367d78f83795171d7d1e8796d68b706989d"), // dlc.rpf/x64/levels/mpchristmas2017/vehiclemods/sc1_mods.rpf
    ShaUnpack("8409d97efa6eb92981b79b213b002e2b63342d56a685ab0cc9442679d1789df9"), // dlc.rpf/x64/levels/mpchristmas2017/vehiclemods/sentinel3_mods.rpf
    ShaUnpack("2ac2b2cd5c4a975fd4c220a4cb276fc69c727b0dd3fb414b01d64e156b69bdbd"), // dlc.rpf/x64/levels/mpchristmas2017/vehiclemods/streiter_mods.rpf
    ShaUnpack("e9a2e1dbbfe249a7111e85cf500b9eba0e6dd89611377e284328ffcddb2854ff"), // dlc.rpf/x64/levels/mpchristmas2017/vehiclemods/thruster_mods.rpf
    ShaUnpack("534795a9bc49972678cbbd12aff54a8d90e583bff1ae66f79cb8a11172354a15"), // dlc.rpf/x64/levels/mpchristmas2017/vehiclemods/vehcamo_mods.rpf
    ShaUnpack("5115d0e2140ad4946378c5fa109cd127c9c08ce07f35edb036f3fc87f79389a0"), // dlc.rpf/x64/levels/mpchristmas2017/vehiclemods/viseris_mods.rpf
    ShaUnpack("16802d2fce928d7276623ff38df3a46d045bd0cb424c6a70f9f86e55d6e06a50"), // dlc.rpf/x64/levels/mpchristmas2017/vehiclemods/volatol_mods.rpf
    ShaUnpack("43f5433719617c093e94954570228871c668cd8deb34baca0fd592322db33734"), // dlc.rpf/x64/levels/mpchristmas2017/vehiclemods/yosemite_mods.rpf
    ShaUnpack("969fe2af5623b3a19d61361f6b022d851948bb0002441926073168c2b128456f"), // dlc.rpf/x64/levels/mpchristmas2017/vehiclemods/z190_mods.rpf
    ShaUnpack("f4736bc8e6eed36be106db6d560bbbb6f3c5ee4caf66b22c91480ae12eecd12b"), // dlc.rpf/x64/models/cdimages/mpchristmas2017_cutspeds.rpf
    ShaUnpack("c54c28404185194c19223e864d4d6f22bac2854d4bbaef117983430bd4f7cea8"), // dlc.rpf/x64/models/cdimages/mpchristmas2017_cutspeds_p.rpf
    ShaUnpack("80cc55ea7731277dce4639aa5f792207069e7d75419e8641f7cfcd91b8f9044e"), // dlc.rpf/x64/models/cdimages/mpchristmas2017_female.rpf
    ShaUnpack("29b63677b287b3e84b0077fe9c7d5a59ec8809d4a7605f6909a17127e7d8e357"), // dlc.rpf/x64/models/cdimages/mpchristmas2017_female_p.rpf
    ShaUnpack("3381cfa66595f58ca022502f2070ae123459298863250bd1e36786f132c5129c"), // dlc.rpf/x64/models/cdimages/mpchristmas2017_male.rpf
    ShaUnpack("4ee44d6f15098819e28083b6712d11c065cace3385c9fba5f60e785cb5e9b7d7"), // dlc.rpf/x64/models/cdimages/mpchristmas2017_male_p.rpf
    ShaUnpack("0dbda9df569fe99c5296dc105a89dcb34b5f09749185f35e348a9d3115c9ceeb"), // dlc.rpf/x64/models/cdimages/mpchristmas2017_ped_mp_overlay_txds.rpf
    ShaUnpack("2b72d37591d6c1e2d1d075df2e37651e0e01468ef3b54fdd29669be63a8a6613"), // dlc.rpf/x64/models/cdimages/weapons.rpf
    ShaUnpack("e8c2d8f905a91c842384eb91ca186b3a7bd0ab318d6ce97ff07f7de1b760ba97"), // dlc.rpf/x64/models/cdimages/peds/mpchristmas2017.rpf
    ShaUnpack("5408fac35a92619059207617b09e5591efb4ce898384109f6c6be029a5a38002"), // dlc.rpf/x64/models/cdimages/peds/mpchristmas2017_p.rpf
    // update/x64/dlcpacks/mpchristmas2018/dlc.rpf
    ShaUnpack("6d9c8d18df370ef9bed74cc56fcdf47733d366cb19683d234e8a8b972c784270"), // dlc.rpf
    ShaUnpack("d7a05cc53b594cae6424e47b508f2a7c12845625cdd399ced0a3cd7124b010a9"), // dlc.rpf/x64/anim/creaturemetadata.rpf
    ShaUnpack("375117122268665afa88b289124520bd4e5bb04bbcf1ee199d08720193baec64"), // dlc.rpf/x64/anim/cutscene/cuts_arena.rpf
    ShaUnpack("4a3a11417b1f9c955f702616922687147e2a1b2591cb437ac7fa50e0ef15b87b"), // dlc.rpf/x64/anim/ingame/clip_anim@.rpf
    ShaUnpack("50fe7ce672582edc196294b36a1908e556a7ddc8a0e52313d587338300f500af"), // dlc.rpf/x64/audio/occlusion.rpf
    ShaUnpack("7081dc63c6d080bce6e41e97b73710a3082cab2aced58756d0ac1b7a1b8ddfa2"), // dlc.rpf/x64/data/effects/ptfx.rpf
    ShaUnpack("66b703dfe303404fd93cfcf414b7214ed83ffab30aade3b62c16f29da08ddf5c"), // dlc.rpf/x64/data/lang/americandlc.rpf
    ShaUnpack("1e1b932988ff00880c0c8a8fd8ef53f0c80b3da0d6b485275c01afd1bbf4e1c3"), // dlc.rpf/x64/data/lang/chinesedlc.rpf
    ShaUnpack("0ab5ec7dba5e87ced5c5b32c8cbd44788fdb5af96a6ea26c62ca937134129416"), // dlc.rpf/x64/data/lang/chinesesimpdlc.rpf
    ShaUnpack("49e394fcf921962aa60746463edf034f3bc2facf03168bdd5a709d0fca8e44a9"), // dlc.rpf/x64/data/lang/frenchdlc.rpf
    ShaUnpack("9fff60517e663bcdf4dd4bcbb34bcebbf67dd403036a13a1b6efeff12a517af4"), // dlc.rpf/x64/data/lang/germandlc.rpf
    ShaUnpack("21e7eeaa3c47a6993cb52b27336d56c4f860a3635a40bcacc0dc38ada99ff0e5"), // dlc.rpf/x64/data/lang/italiandlc.rpf
    ShaUnpack("6096b951bd9358e30e94de69e0c1ff26a8ed2da861739d2c262f9feb4bbf2e52"), // dlc.rpf/x64/data/lang/japanesedlc.rpf
    ShaUnpack("e9af367ddccb86eef75101c32dd11452ccf32dd25211bbe1e4acaab49fc2b84d"), // dlc.rpf/x64/data/lang/koreandlc.rpf
    ShaUnpack("fe5037be7f9bd2db206e01b92cdaf5af98f590767f51a90f4fd11b72678580a6"), // dlc.rpf/x64/data/lang/mexicandlc.rpf
    ShaUnpack("7274fd134b6db8dec018a23a3b7e9cf677493a99356d14e9e88a572b905f4e09"), // dlc.rpf/x64/data/lang/polishdlc.rpf
    ShaUnpack("4bbb479b9c2e30fc16a8f2d7a0dd670d5e98cc97548c7868f69f03ed7cfcc892"), // dlc.rpf/x64/data/lang/portuguesedlc.rpf
    ShaUnpack("f1445832486def7690d2bc41b931600eb9ec9282d16c67574b7207cbc99c3fb1"), // dlc.rpf/x64/data/lang/russiandlc.rpf
    ShaUnpack("c30ac59f4dfbca1d25b1bac2c89fbea73905d4bf1c1c675603c9de6ffba56ff0"), // dlc.rpf/x64/data/lang/spanishdlc.rpf
    ShaUnpack("700d95bb89864ee80b0dc173aede4df755c6d0c09c8f010349a780b3bb80f0a5"), // dlc.rpf/x64/levels/gta5/mpchristmasbanners.rpf
    ShaUnpack("0c89052168165207b83f792c586e459195a36fee2076b71085111574f9830653"), // dlc.rpf/x64/levels/gta5/interiors/int_placement_xs.rpf
    ShaUnpack("3554947b7f0fb510e406b5756a7d2d171cf0cab4820ca83afe772ff5cd1c7206"), // dlc.rpf/x64/levels/gta5/interiors/x18dlc_int_01.rpf
    ShaUnpack("f0785f127489ace9d59479c8c24b3fa43869b82ef92521ab573b16f46e89dfd8"), // dlc.rpf/x64/levels/gta5/interiors/x18dlc_int_mod.rpf
    ShaUnpack("5203a66319b0f66c46c792fac2511e908e370382905a1be8b004ab5cdf4320d3"), // dlc.rpf/x64/levels/gta5/interiors/x18dlc_int_vip.rpf
    ShaUnpack("b23bae5f2fc876dd91e107128fa7a7f6135b81707be0aa02203b32cd930e37cc"), // dlc.rpf/x64/levels/gta5/props/prop_arena_accs.rpf
    ShaUnpack("aa0bdf8b16019e0aa147d2d75f673aafb96c496892bca11a8f74270a6d8ab643"), // dlc.rpf/x64/levels/gta5/props/prop_arena_accs_scifi.rpf
    ShaUnpack("c2b20588b45804abc812fd22da18ad3943ff594e6f52fa9efe8e96f276e01c24"), // dlc.rpf/x64/levels/gta5/props/prop_arena_bollards_rising.rpf
    ShaUnpack("0f774f7e4b2e7944b180e7e3751eee4a2c2fcac4ae03cdf9884fda760a4e2332"), // dlc.rpf/x64/levels/gta5/props/prop_arena_bollards_rising_scifi.rpf
    ShaUnpack("4736800e60e8e3a0f8d3195df49d28415c0f2ea0749bf291f07223d7c93b1e8b"), // dlc.rpf/x64/levels/gta5/props/prop_arena_bollards_rising_wasteland.rpf
    ShaUnpack("ddfd95f70990e84022eb6e632ef80596d858f880f7f07f6fcc61ff97f371b7dd"), // dlc.rpf/x64/levels/gta5/props/prop_arena_bollards_side.rpf
    ShaUnpack("67e8c1798c70b3c959dc306d152d23dfbe49716449dc3473a0a4f50b2ad88dab"), // dlc.rpf/x64/levels/gta5/props/prop_arena_bollards_side_scifi.rpf
    ShaUnpack("14d9137cce634dd433bd7d92adf90dabb1998ec9914c3192ca48e7cb94ab60db"), // dlc.rpf/x64/levels/gta5/props/prop_arena_bollards_side_wasteland.rpf
    ShaUnpack("4042ccf6263c3282349fc9c75cc14b1279c644aee6a72744ac985bf648568374"), // dlc.rpf/x64/levels/gta5/props/prop_arena_cutscene.rpf
    ShaUnpack("1b9f11c2775bea49a4be988db7a5da72660503766061f0048d6f3b196c49a9f5"), // dlc.rpf/x64/levels/gta5/props/prop_arena_des_buildings.rpf
    ShaUnpack("3f174e4cc9ecb6c3620fb8b57534870a24a81c2e97384404b3f33683716fbb54"), // dlc.rpf/x64/levels/gta5/props/prop_arena_des_buildings_01.rpf
    ShaUnpack("9a1b0ce637f19f33fcbb71c2d1cf2ed0562b07335dbc7c5ae86f824447a2d0cb"), // dlc.rpf/x64/levels/gta5/props/prop_arena_des_skin.rpf
    ShaUnpack("c406079b4281ddf5805553611a49bb2473104c8641bc1c441fd6c1204a5d917f"), // dlc.rpf/x64/levels/gta5/props/prop_arena_drones.rpf
    ShaUnpack("fadcc65b5569c523520b87a3fb01cd89f9b03c815e0156a0d3e6ad3ebe0413ec"), // dlc.rpf/x64/levels/gta5/props/prop_arena_fences.rpf
    ShaUnpack("e20f584b97c5270b992ac37ca940c14f6c4fc22a27885dd6c9b3fb181bc9619e"), // dlc.rpf/x64/levels/gta5/props/prop_arena_fences_scifi.rpf
    ShaUnpack("b0fa1295671b96fe27a65fe48926da242d9829065c637bd44b3c65254d42ed0e"), // dlc.rpf/x64/levels/gta5/props/prop_arena_fences_wasteland.rpf
    ShaUnpack("8193118076a37363eb6b838afb3f2c04e1b43c034c5e712274fa23e2d9a6886d"), // dlc.rpf/x64/levels/gta5/props/prop_arena_flippers.rpf
    ShaUnpack("56d5133169f71f615fea84e1c9253345c5002bcc5941b96e1646445b51a2f57a"), // dlc.rpf/x64/levels/gta5/props/prop_arena_flippers_scifi.rpf
    ShaUnpack("91e4a3766f16b894f05103bc7a10abacb1062613b331965d20daa89c541487ba"), // dlc.rpf/x64/levels/gta5/props/prop_arena_flippers_wasteland.rpf
    ShaUnpack("31240cbb9e5026671fb026225e5d3c3c40ba399b0d43b571b63abfba4fa2ea8a"), // dlc.rpf/x64/levels/gta5/props/prop_arena_icons.rpf
    ShaUnpack("ab9e92b84fa5c758c050560eae3cd938eb554c2af80019898b7ecf5f15bd201e"), // dlc.rpf/x64/levels/gta5/props/prop_arena_lights_01.rpf
    ShaUnpack("49a8ecd2df258a9b8a0b0e2d40638adb5b34618d8e8c41d8ab35b9dc4e1109ee"), // dlc.rpf/x64/levels/gta5/props/prop_arena_mines.rpf
    ShaUnpack("8e52f534e30e7153ebbf6e0caf017ec8b01bb016bb792dbd5e38bdcf182872eb"), // dlc.rpf/x64/levels/gta5/props/prop_arena_mines_scifi.rpf
    ShaUnpack("71e88441011ba2c4c56f3ebebb4fb59468adcae57e615806a87d4f47d773e1fa"), // dlc.rpf/x64/levels/gta5/props/prop_arena_mines_wasteland.rpf
    ShaUnpack("81bdf5eb5c5d026e794e2abc633ec8346151f3faf0948a36c946bb543c7d979a"), // dlc.rpf/x64/levels/gta5/props/prop_arena_obstacles_scifi.rpf
    ShaUnpack("ff8fc9d1f8f0490ae4c625fa50c071d4f7f24730928871db809fd8d4be458e9b"), // dlc.rpf/x64/levels/gta5/props/prop_arena_pipes.rpf
    ShaUnpack("a3fa3561ac0cdc672789f534f5659737e046556e8e135ac185fb9328cee031f0"), // dlc.rpf/x64/levels/gta5/props/prop_arena_pits.rpf
    ShaUnpack("cb200aef60a2729eda2a731643dbb2b838a8ae0f6828287c8465770d5b979c71"), // dlc.rpf/x64/levels/gta5/props/prop_arena_pits_fire.rpf
    ShaUnpack("cf29d46360fdea2dd675527f2001c7fd11e45983bd86dd6f66b81dff459245df"), // dlc.rpf/x64/levels/gta5/props/prop_arena_pits_fire_scifi.rpf
    ShaUnpack("98b8c6b1e2f306a9940c6196d3f23f2cb8652b6d3d5d460dd5124b31f23af587"), // dlc.rpf/x64/levels/gta5/props/prop_arena_pits_fire_wasteland.rpf
    ShaUnpack("8c6dd4543d7568179c60178ad725c75d630d60b46fe30c8c010baefeb2f49f36"), // dlc.rpf/x64/levels/gta5/props/prop_arena_pits_scifi.rpf
    ShaUnpack("107f1c1acba1492cedc4f7f03d6ecf67107073f22032b9d967c4f5d0d9bf2e76"), // dlc.rpf/x64/levels/gta5/props/prop_arena_pits_wasteland.rpf
    ShaUnpack("100b3c0769b27e3c41ecc9b708267a11c85f857f823cb50cab22334ff1ff0df0"), // dlc.rpf/x64/levels/gta5/props/prop_arena_plates.rpf
    ShaUnpack("f75e43a73ff8cc33e49009193e3f32b61fa9e5acc59df012317a22e0354d8d49"), // dlc.rpf/x64/levels/gta5/props/prop_arena_plates_scifi.rpf
    ShaUnpack("709c7fdf736ed220d9a7f6328ff905917d71b4b3f0f90ba0f0510dda87b579ae"), // dlc.rpf/x64/levels/gta5/props/prop_arena_plates_wasteland.rpf
    ShaUnpack("42e117f133f4078ff1ae748f604fdd2cb7994b628b05c5859a39ee83584278e6"), // dlc.rpf/x64/levels/gta5/props/prop_arena_rubbish_wasteland.rpf
    ShaUnpack("b176599b13fe911443f40e5771a3cab93ccbebc94dcd477c816f68395506fcc7"), // dlc.rpf/x64/levels/gta5/props/prop_arena_screens.rpf
    ShaUnpack("e1cfb49a33eb1f441ad620bcb91a3b8dab7215ca7e1744c50754f536f26a8765"), // dlc.rpf/x64/levels/gta5/props/prop_arena_slowdown.rpf
    ShaUnpack("f5ca7839aa88addde2ac37ec62e645da290f2f238e57207bbadfe19d0cea6459"), // dlc.rpf/x64/levels/gta5/props/prop_arena_spikes.rpf
    ShaUnpack("d580de1a5817896133386ceb8042e07a505082d8ae925c1aa7412185a3702e8c"), // dlc.rpf/x64/levels/gta5/props/prop_arena_spikes_scifi.rpf
    ShaUnpack("d316b01a99c2072bb2fb20d253d8fe07f4d479f8b693efa0805e9bdf61f99a8b"), // dlc.rpf/x64/levels/gta5/props/prop_arena_stunt.rpf
    ShaUnpack("fd45998d925e839f8a2871d644b37142dd6ce8d46149fc3e43c530f1dd3d349c"), // dlc.rpf/x64/levels/gta5/props/prop_arena_stunt_scifi.rpf
    ShaUnpack("237aa9621d24afae900d3876c5335e765cd0c661826b475fe6678233002ac21a"), // dlc.rpf/x64/levels/gta5/props/prop_arena_stunt_wasteland.rpf
    ShaUnpack("90b619c59d6d4e5e0cd90a3c17c68312eb0714445ba43c9f329ab43e07a5b167"), // dlc.rpf/x64/levels/gta5/props/prop_arena_towers.rpf
    ShaUnpack("0be10639e5a23bd6576db723282b2f7aae6a72467d30676e777336d8e148300e"), // dlc.rpf/x64/levels/gta5/props/prop_arena_track_lights_01.rpf
    ShaUnpack("8d137a8b5850eb2ae11132d83935a469d117b8d65049e29fbdd8dca7466e604b"), // dlc.rpf/x64/levels/gta5/props/prop_arena_trinkets.rpf
    ShaUnpack("8a9e528182d067d45d7aca8be4462422accc75323977b86dd371dd7d9f048c52"), // dlc.rpf/x64/levels/gta5/props/prop_arena_trophies.rpf
    ShaUnpack("4fe6011ab7869603c7f359910e949738c052281fa93433b78ed8f688b550d56c"), // dlc.rpf/x64/levels/gta5/props/prop_arena_turntables.rpf
    ShaUnpack("24bfc0918c52aed8dd77d87039d9d80cc2083adbe4a81ebc9e4d5befa6663035"), // dlc.rpf/x64/levels/gta5/props/prop_arena_turntables_scifi.rpf
    ShaUnpack("a6a613d80a445e23ee76d3b07c8eb5cd252f57216b8d748fb75a76dec501659a"), // dlc.rpf/x64/levels/gta5/props/prop_arena_turntables_wasteland.rpf
    ShaUnpack("04dcbef96f91d931a0cddd19cb4d4d966e29eecc4052377c2828eb96f65b95b9"), // dlc.rpf/x64/levels/gta5/props/prop_arena_turrets.rpf
    ShaUnpack("ef2c09b96bd528373fc881e5ae2d91439db7ff2e3d74120b204dc4d4df263e77"), // dlc.rpf/x64/levels/gta5/props/prop_arena_turrets_scifi.rpf
    ShaUnpack("c48b15ee71c4ce6578029907a756a03e82dae95b7f4885dda42ad677786efe6d"), // dlc.rpf/x64/levels/gta5/props/prop_arena_turrets_wasteland.rpf
    ShaUnpack("0b8c6e2ed92b89e120b122ab7eb04d28b05532026886979e49ee10d4de06f7ed"), // dlc.rpf/x64/levels/gta5/props/prop_arena_walls.rpf
    ShaUnpack("11edebf8be2f1dc3f4d7ed8ef0044318154b01abbe51c318ebb3451836fc9d69"), // dlc.rpf/x64/levels/gta5/props/prop_arena_walls_rising.rpf
    ShaUnpack("79721291a0d9f792253853e56c1bbc38fd043b51f8a5c8de5ae55bca02902a2a"), // dlc.rpf/x64/levels/gta5/props/prop_arena_walls_rising_scifi.rpf
    ShaUnpack("d4af7d430b20b7c99a0657955bcde8ac99380ffcf9074f05fcab377c55a810ba"), // dlc.rpf/x64/levels/gta5/props/prop_arena_walls_rising_wasteland.rpf
    ShaUnpack("fcba00ef4db2512574a50d902be688ac4353c0a78bbe7c8890a8d031ccb5702e"), // dlc.rpf/x64/levels/gta5/props/prop_arena_walls_scifi.rpf
    ShaUnpack("0b42e260167e11620c2a1dd24e83ee36129bf8b1bc13d200c9ecbff9d847cd10"), // dlc.rpf/x64/levels/gta5/props/prop_arena_walls_tyres.rpf
    ShaUnpack("3f9846a2305430b0bbd35789c79a4ad3b8360645c50a4c4a7f033aaf41debddd"), // dlc.rpf/x64/levels/gta5/props/prop_arena_walls_tyres_scifi.rpf
    ShaUnpack("0914aed2b6e5bb9169506cd3a2127ec18ec5f464d442827538465e0b72dab7ea"), // dlc.rpf/x64/levels/gta5/props/prop_arena_walls_tyres_wasteland.rpf
    ShaUnpack("13a8448b8a7fa93d11e37690ca731f4928bfd8e722780b062c806804f6f0e8e8"), // dlc.rpf/x64/levels/gta5/props/prop_arena_walls_wasteland.rpf
    ShaUnpack("7ec1a5338ca1b303bda7bfe2605ac30a61c4670f3a4eb3c414165782ff6dc86b"), // dlc.rpf/x64/levels/gta5/props/prop_arena_wedges.rpf
    ShaUnpack("ecf2eef82f998a982b71673be02666988c91a6379efc0fccad67b4ad7c446604"), // dlc.rpf/x64/levels/gta5/props/prop_arena_wedges_scifi.rpf
    ShaUnpack("41ddc2b6e0f56fd3aba4404bc156f0aa8f408fdbef5909d93e947f565f977f70"), // dlc.rpf/x64/levels/gta5/props/prop_arena_wedges_wasteland.rpf
    ShaUnpack("8a88637c5503176887bd45ec7cae158c1a9ab7bb725d30f44c5ef7f851fbb850"), // dlc.rpf/x64/levels/gta5/props/prop_int_arena_christmas.rpf
    ShaUnpack("666f6ecef00eff12e371fa62a7bc488010068040e14a1591f7946f71b1e924de"), // dlc.rpf/x64/levels/gta5/props/prop_int_arena_combined.rpf
    ShaUnpack("1c507401fcdc5800e5bb191a9549e603af84ad442c2dc8ecb5041bae09dd6216"), // dlc.rpf/x64/levels/gta5/props/prop_int_arena_combined_02.rpf
    ShaUnpack("e8dfe2a316a586b507166e377de71bc5be683f4002be5193a8c35e617fd85be9"), // dlc.rpf/x64/levels/gta5/props/prop_int_arena_detail_02.rpf
    ShaUnpack("ab92c49bf5a5344705c7e03b1fdc3bab9d9505292929d8182d49caf8d1cec60e"), // dlc.rpf/x64/levels/gta5/props/prop_int_arena_detail_03.rpf
    ShaUnpack("34d38af34ec17d8ac36d5256854122e3f15a78b6ea0a6b640e6f1031f024c9ce"), // dlc.rpf/x64/levels/gta5/props/prop_int_arena_detail_04.rpf
    ShaUnpack("f7a352ae711271168c6af6270e0b0150997a91be27fc465948dda503c325a70b"), // dlc.rpf/x64/levels/gta5/props/prop_int_arena_detail_05.rpf
    ShaUnpack("8170e5a332a505809e1c16fe3fb623b05b686369dcf50f5c20beda5933635815"), // dlc.rpf/x64/levels/gta5/props/prop_int_arena_structure.rpf
    ShaUnpack("f1cbe3995a8f404584ab0ce4118166a37d81d131e00c9096167b7bd5a78214e9"), // dlc.rpf/x64/levels/gta5/props/prop_int_arena_terrain.rpf
    ShaUnpack("67da8fc7461de6792b3cac99f7f2ecfe04cdc75d96b6487e305e8e233cdf6dc5"), // dlc.rpf/x64/levels/gta5/props/prop_int_arena_trinkets.rpf
    ShaUnpack("24e02f42b66e7216cd6220cce4e8b6a07a8c720af35fdf1bb9562f94e8ea4a97"), // dlc.rpf/x64/levels/gta5/props/prop_int_lounge_garage.rpf
    ShaUnpack("1cb04f223eb0e6506297dabe73849c34ff273e76e7c7e3293c37f60ab77a0f50"), // dlc.rpf/x64/levels/gta5/vehicles/mpchristmas2018.rpf
    ShaUnpack("2fb0b7bd1771e947169c7c90c37f736a28abe3ca75ff49c790756e1fe27a0d5b"), // dlc.rpf/x64/levels/mpchristmas2018/vehiclemods/arenamod1_mods.rpf
    ShaUnpack("ce6d0af4b9e11f928a1c3520fdb707a27729d54d5599888a2cc28f763756b0fa"), // dlc.rpf/x64/levels/mpchristmas2018/vehiclemods/bruiser2_mods.rpf
    ShaUnpack("2e29fa6087af811836e73106874d330a6d7d25bf5aa08c292001ca9843c3f1ea"), // dlc.rpf/x64/levels/mpchristmas2018/vehiclemods/bruiser3_mods.rpf
    ShaUnpack("83e5749ca0539d6b9252201da1c6e4382738b16bcf499b51759dff722e7d90dd"), // dlc.rpf/x64/levels/mpchristmas2018/vehiclemods/bruiser_mods.rpf
    ShaUnpack("688e4863a656061fb38426f485ee614522a487cb1d179faae2543060ff2be208"), // dlc.rpf/x64/levels/mpchristmas2018/vehiclemods/brutus2_mods.rpf
    ShaUnpack("db6544bcc9ee3c04f6e13bb57f17a14a4c4befb54afeb3c91b27f0ea449da19b"), // dlc.rpf/x64/levels/mpchristmas2018/vehiclemods/brutus3_mods.rpf
    ShaUnpack("53564a7f12c6c11ae9b7aa7b74635cf8141e89cb8f911742706c2a58724af845"), // dlc.rpf/x64/levels/mpchristmas2018/vehiclemods/brutus_mods.rpf
    ShaUnpack("c4cdb4e7cb07ca89b7dfa5231400263c6265d54e951dbbc4e6513ea0c9e478e0"), // dlc.rpf/x64/levels/mpchristmas2018/vehiclemods/cerberus2_mods.rpf
    ShaUnpack("aa7e87b9083a9f34b10ccfd5f4e44c26d12fb50298e7ac53e87f140ee83fb168"), // dlc.rpf/x64/levels/mpchristmas2018/vehiclemods/cerberus3_mods.rpf
    ShaUnpack("8e22b87c636d11c6bcd0263732f9e46f36b6575a8781073c81800139568481c5"), // dlc.rpf/x64/levels/mpchristmas2018/vehiclemods/cerberus_mods.rpf
    ShaUnpack("a99a8f70fb9ec5a41b00ef7c5a16c40d719a6c49017dab6a33e8b7a147d194b5"), // dlc.rpf/x64/levels/mpchristmas2018/vehiclemods/clique_mods.rpf
    ShaUnpack("e1021e2e96f7f4c66a56df56811544b5ce7bceb8b8c3d5f4aca91de62a8d2d56"), // dlc.rpf/x64/levels/mpchristmas2018/vehiclemods/deathbike2_mods.rpf
    ShaUnpack("cc6ac59aca5e2cc13888cbe03e57c24750c2f5ee0ac4a4d8b840b4007ece7ce6"), // dlc.rpf/x64/levels/mpchristmas2018/vehiclemods/deathbike3_mods.rpf
    ShaUnpack("15479415538f69caefa35174c7c44f7af70b0cc5d018f373178b2d91a4cd75bb"), // dlc.rpf/x64/levels/mpchristmas2018/vehiclemods/deathbike_mods.rpf
    ShaUnpack("9daddda154dcb791759264273898059832a27f86bf7ff34fc46d3fee0a6bf09f"), // dlc.rpf/x64/levels/mpchristmas2018/vehiclemods/deveste_mods.rpf
    ShaUnpack("430d23477d3ccf25179c10f301b7f2cde4a837ca7f4fd102fce42dace1cab989"), // dlc.rpf/x64/levels/mpchristmas2018/vehiclemods/deviant_mods.rpf
    ShaUnpack("a54287fa133ef769e000e170ab177cf4696cf63bbf9eef0d90c7969a87c357c3"), // dlc.rpf/x64/levels/mpchristmas2018/vehiclemods/dominator4_mods.rpf
    ShaUnpack("9ad4d65c58db481a47d96c0a433cef79e2347db52840bf2ba8180ab3b829de0f"), // dlc.rpf/x64/levels/mpchristmas2018/vehiclemods/dominator5_mods.rpf
    ShaUnpack("33bd1e8a89ba03629f357fc197e45373c1005aed82a8845d1240f696739a605f"), // dlc.rpf/x64/levels/mpchristmas2018/vehiclemods/dominator6_mods.rpf
    ShaUnpack("ded3728820fbb25bbdc1e361382723ac14cf8cd7565674ab931afc41f83bcfb2"), // dlc.rpf/x64/levels/mpchristmas2018/vehiclemods/impaler2_mods.rpf
    ShaUnpack("a007731cb4becb85f239b6b5a1e83789ffc80f1c6d34d2edd1b9789bda4475e9"), // dlc.rpf/x64/levels/mpchristmas2018/vehiclemods/impaler3_mods.rpf
    ShaUnpack("26215048f3251e8ab39e0d4ecdc33a829506c523d1834d935f35a86607dacd56"), // dlc.rpf/x64/levels/mpchristmas2018/vehiclemods/impaler4_mods.rpf
    ShaUnpack("99ede38b095af95cd51568f8b80c623cad7289be12e013fcdfb0702de398d52d"), // dlc.rpf/x64/levels/mpchristmas2018/vehiclemods/impaler_mods.rpf
    ShaUnpack("494a8f04c88bbc7f9e5440cfeeb63b137d06b7d0e9bc461b3eb40fb8bf799b17"), // dlc.rpf/x64/levels/mpchristmas2018/vehiclemods/imperator2_mods.rpf
    ShaUnpack("b5e0988df092035e40c435f6c7d78e2e46f9de7c039691891d158f27e4c9c0b3"), // dlc.rpf/x64/levels/mpchristmas2018/vehiclemods/imperator3_mods.rpf
    ShaUnpack("92aee44b13d8cc331becf926fbf628fe0ad8c45d77a898c136b48a62e5eb9fbf"), // dlc.rpf/x64/levels/mpchristmas2018/vehiclemods/imperator_mods.rpf
    ShaUnpack("4c648163a9ec45900b3577d4b6e9c94aa42543f52241352d55c60f664cb259cd"), // dlc.rpf/x64/levels/mpchristmas2018/vehiclemods/issi4_mods.rpf
    ShaUnpack("ae40417554b587fe473799dd56edb913eedfb305996c135ec9a63d898b0389e3"), // dlc.rpf/x64/levels/mpchristmas2018/vehiclemods/issi5_mods.rpf
    ShaUnpack("7cd055f2e4c1c0cc98a9b7d82bd351b53bd5c9d9beef0a24ed8943049e892632"), // dlc.rpf/x64/levels/mpchristmas2018/vehiclemods/issi6_mods.rpf
    ShaUnpack("6260e2d5143dbf997c7cf8b26b8410698c80de259d3931fd2974a89145a28850"), // dlc.rpf/x64/levels/mpchristmas2018/vehiclemods/italigto_mods.rpf
    ShaUnpack("afe3d4527df511c3786c9d260d1c4ff9274f2017654d8249b8cd45a6420446c8"), // dlc.rpf/x64/levels/mpchristmas2018/vehiclemods/monster3_mods.rpf
    ShaUnpack("3e30c227cf0837e8b734600a25c99b014116e18a7da4a7e41f5c53509f60b250"), // dlc.rpf/x64/levels/mpchristmas2018/vehiclemods/monster4_mods.rpf
    ShaUnpack("7948cc9bb0a4d36534e4eabc8dc8f6466a9b84b1053124df35f9c448bbd12119"), // dlc.rpf/x64/levels/mpchristmas2018/vehiclemods/monster5_mods.rpf
    ShaUnpack("6ca995d88212a73ef27a813537e519c92dcc53623687ab8e26d4bb8fb479a77a"), // dlc.rpf/x64/levels/mpchristmas2018/vehiclemods/rcbandito_mods.rpf
    ShaUnpack("df1ef2b2447c8cdf4acd565d8d9090268bce873458f07d59da58c4c7d5b480fd"), // dlc.rpf/x64/levels/mpchristmas2018/vehiclemods/scarab2_mods.rpf
    ShaUnpack("6d9d8af4955a519bb1c7863cbaf42c4bc6b2895c5e7f1a6a25207f585b171143"), // dlc.rpf/x64/levels/mpchristmas2018/vehiclemods/scarab3_mods.rpf
    ShaUnpack("0de655e0e23200f7986a32d8473f30354ea6127138b5cbfa0665d38a33b505c9"), // dlc.rpf/x64/levels/mpchristmas2018/vehiclemods/scarab_mods.rpf
    ShaUnpack("4a45ae54b2956313b2a44cbd006244afc72392f7bd770dac62e867d3ea317f39"), // dlc.rpf/x64/levels/mpchristmas2018/vehiclemods/schlagen_mods.rpf
    ShaUnpack("e23659e7f24d715f11c92fe9029af3d18600ce19d5043a2e0e0022b8f1a22207"), // dlc.rpf/x64/levels/mpchristmas2018/vehiclemods/slamvan4_mods.rpf
    ShaUnpack("02c6519343ef7f12a523ab39e493fd0f7f11db73323265af206571557f76fa73"), // dlc.rpf/x64/levels/mpchristmas2018/vehiclemods/slamvan5_mods.rpf
    ShaUnpack("15354c8beb30259e827acd67e7e898e8954cb562dc818661b828223a9e4d8520"), // dlc.rpf/x64/levels/mpchristmas2018/vehiclemods/slamvan6_mods.rpf
    ShaUnpack("4255e9f473cce86bee92baa3203e33b52b5b3a3e3f6d6d8eaa2d7446a4e2798c"), // dlc.rpf/x64/levels/mpchristmas2018/vehiclemods/toros_mods.rpf
    ShaUnpack("347a153805106a1956afecd957a42356a77d0a280772d8db29293bc58b743965"), // dlc.rpf/x64/levels/mpchristmas2018/vehiclemods/tulip_mods.rpf
    ShaUnpack("b6abd39f90bf2086d979b81169c6dc6580cb42349c0b6e316df04a30663a780c"), // dlc.rpf/x64/levels/mpchristmas2018/vehiclemods/vamos_mods.rpf
    ShaUnpack("09753833b8ab447fdc724ace091d415162b206569c272536a94fed44308eb37a"), // dlc.rpf/x64/levels/mpchristmas2018/vehiclemods/zr3802_mods.rpf
    ShaUnpack("bfa73ccb0d5417bf848f8c6949df44c23a65351ae67a48bca51681e809ce3756"), // dlc.rpf/x64/levels/mpchristmas2018/vehiclemods/zr3803_mods.rpf
    ShaUnpack("8a6e7e416dcac1a2e51666e033bbf4be3a1686dfbe9c62f82d49648bb185d232"), // dlc.rpf/x64/levels/mpchristmas2018/vehiclemods/zr380_mods.rpf
    ShaUnpack("70bb14a82a78142ce4a452ae7119d8a1e0487f4fbbda377fa99dbeb31e7349a2"), // dlc.rpf/x64/models/cdimages/mpchristmas2018_female.rpf
    ShaUnpack("a6486db67d072ecba180acad484c092b69608e28e9f1afb894a94f9e3433c206"), // dlc.rpf/x64/models/cdimages/mpchristmas2018_female_p.rpf
    ShaUnpack("91dfd6ff642aaa51e41776ca0550267df69183c10fbc9964388780bd2d4260e8"), // dlc.rpf/x64/models/cdimages/mpchristmas2018_male.rpf
    ShaUnpack("a7f0a3a8682b589f9c1893356f683c311ef684c5d878b16a22a87019e545e460"), // dlc.rpf/x64/models/cdimages/mpchristmas2018_male_p.rpf
    ShaUnpack("6c915353e25efd62b52c3f2313657eb3c478c089180fd2c4b36cc933758e2528"), // dlc.rpf/x64/models/cdimages/mpchristmas2018_ped_mp_overlay_txds.rpf
    ShaUnpack("386dc092bde3f9156a63d38c9e4033a60afde828cbf55f6da422a2b777909c63"), // dlc.rpf/x64/models/cdimages/weapons.rpf
    ShaUnpack("de2ac5b082ba0d226eb1f5a836b40b279f3d703a9f5861f76d83e7ef50a5dcaf"), // dlc.rpf/x64/models/cdimages/peds/mpchristmas2018.rpf
    ShaUnpack("203c5c240c269e7159b1c3a74cb1ae1b43e574292c67eff5aed18adf658937f4"), // dlc.rpf/x64/models/cdimages/peds/mpchristmas2018_p.rpf
    // update/x64/dlcpacks/mpchristmas3/dlc.rpf
    ShaUnpack("ffcc5a691941130c18494e5385bcbcfdac5fee65580084e52bee6aa2345e3610"), // dlc.rpf
    ShaUnpack("7631d69e2b4da5e11296358ea56f8a84bdbe4add7d1037380f3d3d5c86101f02"), // dlc.rpf/x64/anim/creaturemetadata.rpf
    ShaUnpack("6cf669cb144d81b2480db25f9de5f27ddd0fd3110eba3111e7a9d58e90ebae2b"), // dlc.rpf/x64/anim/cutscene/cuts_xm3_drg1_ace.rpf
    ShaUnpack("bd658e6aedc98e8f259a86ea8ec5d766f6c3b7911cb0c043f2deadb18ed7b4d7"), // dlc.rpf/x64/anim/cutscene/cuts_xm3_drg1_bmx.rpf
    ShaUnpack("bd22e1031699cec8d432072c6d28fd95228b3cba8e72ca9389225f102804f342"), // dlc.rpf/x64/anim/cutscene/cuts_xm3_drg1_trn.rpf
    ShaUnpack("24df338700371bb4d031dfd1d12cb49cbb524a61c2f67e5241255533d9cc42ff"), // dlc.rpf/x64/anim/cutscene/cuts_xm3_drg2_cook.rpf
    ShaUnpack("e98dfd4dc255a61464517656dbcd781701bfd1b67667dfdf963788995eac35fc"), // dlc.rpf/x64/anim/cutscene/cuts_xm3_drg2_pln.rpf
    ShaUnpack("fc7c7a0bfc5b8b472f22880e873d50d628bed6c6d03bb3c0eac6200cf51b195c"), // dlc.rpf/x64/anim/cutscene/cuts_xm3_drg2_rhb.rpf
    ShaUnpack("274b3feb9bb3c45a063100db3043b4ab9c18f833d5ac35ca06824c971fd431cf"), // dlc.rpf/x64/anim/ingame/clip_anim@.rpf
    ShaUnpack("fb52547cf145234f6dc69109c57c880492fa50a2df3b476b1073da7ab52cb17e"), // dlc.rpf/x64/audio/occlusion.rpf
    ShaUnpack("a74b741056a14b97d3eb5926b53aa6f4c091b44fe42d9fb6561dc216aa3c7222"), // dlc.rpf/x64/data/effects/ptfx.rpf
    ShaUnpack("cc263e1ec37bd249bd93576815549d954e406b66b76ee36918292506e3258dfd"), // dlc.rpf/x64/data/lang/americandlc.rpf
    ShaUnpack("f34121b2601f2e8e234beffb850cf26c20e7cef90ee95c4ca223d3b8d6f5889f"), // dlc.rpf/x64/data/lang/chinesedlc.rpf
    ShaUnpack("78043ed9af2035ea6f0714b386f5d30fa338ba05ad1edbb1f7ca4ee82794d54d"), // dlc.rpf/x64/data/lang/chinesesimpdlc.rpf
    ShaUnpack("acc014e6130eb6714133ff54ffa0860a45939f486bf78af2e7ac1cf09a4396dc"), // dlc.rpf/x64/data/lang/frenchdlc.rpf
    ShaUnpack("ee9589364c490c4cde5346bf6a0752816f986e497df8caef0f1e4e52531e05e9"), // dlc.rpf/x64/data/lang/germandlc.rpf
    ShaUnpack("1bae3b89b041b18fcc5013b1208397ed18f2d0ea009a41cc5ea52523d29eb873"), // dlc.rpf/x64/data/lang/italiandlc.rpf
    ShaUnpack("feaeadb50dce3095dc4c263192bc38e5c57e4094707ece5cc4e3bd992b89b704"), // dlc.rpf/x64/data/lang/japanesedlc.rpf
    ShaUnpack("7c41de69d32d95f3740e356b07a7dca287b0c9b008735aade5b6090b1fed9fd8"), // dlc.rpf/x64/data/lang/koreandlc.rpf
    ShaUnpack("c8fc3f0365836311b86bf43199348211a1e0cdeea32cac0542c4761c6cc69ec5"), // dlc.rpf/x64/data/lang/mexicandlc.rpf
    ShaUnpack("464b30934653496c1d0f03f5880b58d7961d4341d07175bd3144594ba6661e3b"), // dlc.rpf/x64/data/lang/polishdlc.rpf
    ShaUnpack("3d3058e485743d10623df9c1bab2c3bef2a715de833780fa6eeaa3890b42207c"), // dlc.rpf/x64/data/lang/portuguesedlc.rpf
    ShaUnpack("ce91436a5a7bb5d76717f91a7257a2929dcd7343f6496d99c2cc28430cd6f991"), // dlc.rpf/x64/data/lang/russiandlc.rpf
    ShaUnpack("6bee253a9c7b88b29db42b3bfcc1ae2bea722367d3fce64ba7055f1de935aa7e"), // dlc.rpf/x64/data/lang/spanishdlc.rpf
    ShaUnpack("09c173cf35617897446d192d542d16210927cb570768bb346368d499f676e857"), // dlc.rpf/x64/levels/gta5/navmeshes.rpf
    ShaUnpack("bcfd7e9751f00cad3c4c9c3f17c7a2f508605ea50357a0ccc01cc208fb78c596"), // dlc.rpf/x64/levels/gta5/interiors/dlc_int_01_xm3.rpf
    ShaUnpack("5c0ded873f13df8c08f83e45da108ff2f310b03bc73ff82729b4d062883b78bd"), // dlc.rpf/x64/levels/gta5/interiors/dlc_int_02_xm3.rpf
    ShaUnpack("f0b41bafb7744de4045e2bf60289b3edefec6e47b2bddab1258ffef1d87fafca"), // dlc.rpf/x64/levels/gta5/interiors/dlc_int_03_xm3.rpf
    ShaUnpack("8752722ebe4e0087ca6038634d2c2d372bb50e518e436c684cd06844c145c124"), // dlc.rpf/x64/levels/gta5/interiors/dlc_int_04_xm3.rpf
    ShaUnpack("411e0258d736cf772a2dc70a2629f2016856b423213e6fcd3a3286953d8f5c23"), // dlc.rpf/x64/levels/gta5/interiors/int_placement_xm3.rpf
    ShaUnpack("90ad4626d8285d7cf41fb73d3f2df29624dc6e3a9ae1d9d3651fdddf29b732a8"), // dlc.rpf/x64/levels/gta5/mpchristmas3_additions/mpchristmas3_additions.rpf
    ShaUnpack("509e642bef8b0a48f6f4d3ed6f1916c805e859e4d5b2d29ec2b2efd6b7958fc8"), // dlc.rpf/x64/levels/gta5/mpchristmas3_additions/mpchristmas3_additions_metadata.rpf
    ShaUnpack("090fe7f190e333ada8c4bfae677aaed169dbbe210ea2778dc8242ca4e607d681"), // dlc.rpf/x64/levels/gta5/props/prop_xm3_accs_01.rpf
    ShaUnpack("668bb674bd6a57f4058b83f1f56a95b326270faadc0a8d60b9d3b6d7727c98b0"), // dlc.rpf/x64/levels/gta5/props/prop_xm3_accs_02.rpf
    ShaUnpack("26537251aadc0d465575fdb4b2500aac7875ce5ef8f5ee6f4d722eabb6c16f44"), // dlc.rpf/x64/levels/gta5/props/prop_xm3_benches.rpf
    ShaUnpack("c3143d377c418da6b5fa87e9b3c002e71b9311450fbe3b9b50444ac906c802b5"), // dlc.rpf/x64/levels/gta5/props/prop_xm3_doors.rpf
    ShaUnpack("36dd6d8c29ba51ee546c6b208df11dcfe88b33978bbd7df460491e36cdf91011"), // dlc.rpf/x64/levels/gta5/props/prop_xm3_lab.rpf
    ShaUnpack("636fb3e85ee86830d65ecc4e015b84d8afc1f3e0e6beefc5d7a4859a3c9fc696"), // dlc.rpf/x64/levels/gta5/props/prop_xm3_lsd.rpf
    ShaUnpack("10aad4e3110142ff5cc6935fd464c7a91718158826b8178002cd048228d789d4"), // dlc.rpf/x64/levels/gta5/props/prop_xm3_money.rpf
    ShaUnpack("388e3d847b82b850ed063a3adbb16de561c860431955446759a192d79c09f353"), // dlc.rpf/x64/levels/gta5/props/prop_xm3_plane_crash.rpf
    ShaUnpack("ae19d0599b1ced665aed74c27f4d1b69f93132ed4112e9408570f91d7646ef68"), // dlc.rpf/x64/levels/gta5/props/prop_xm3_plane_crash_end.rpf
    ShaUnpack("ade4be7d75a9654cff644f8b3436790bbf11e43c72e734ea6caa96de3f000250"), // dlc.rpf/x64/levels/gta5/props/prop_xm3_storage.rpf
    ShaUnpack("ae0b1e762c3533c1b238b4248d199c41595acd92f246b3fc57aaf3de195a310f"), // dlc.rpf/x64/levels/gta5/props/prop_xm3_storage_01.rpf
    ShaUnpack("279ca2419498be177fa50b097ea70f25f116866dc830e60af90f133753cfd934"), // dlc.rpf/x64/levels/gta5/props/prop_xm3_stunt_track.rpf
    ShaUnpack("4a5148662a41d47089c8f19f70811fe0bb7d6b1012d8c203208964ac526aaab2"), // dlc.rpf/x64/levels/gta5/props/prop_xm3_trip.rpf
    ShaUnpack("a701ba8aa54cc0355c7840d7e16dc545f7bd10dca0cf8cb397ccd6dc380382f7"), // dlc.rpf/x64/levels/gta5/props/prop_xm3_vehicles.rpf
    ShaUnpack("007d6b62dc09d0d84832ecdf6c24adede7af41d54b9a007a32114842631b6784"), // dlc.rpf/x64/levels/gta5/props/prop_xm3_weed.rpf
    ShaUnpack("89eb75ec197ca3d1c26eda5a64d9d1df08226931a86eca8ec7abc3a4ffb23b88"), // dlc.rpf/x64/levels/gta5/vehicles/mpchristmas3.rpf
    ShaUnpack("b954a5331795a03a3e2b4e3f1c37ea14438fe7f03a6409ca8fae240deafc2b64"), // dlc.rpf/x64/levels/mpchristmas3/vehiclemods/boor_mods.rpf
    ShaUnpack("c7de7f3b1f68b98080e8956049512aeec51f0b49385634324849fb8ba5d319b3"), // dlc.rpf/x64/levels/mpchristmas3/vehiclemods/brickade2_mods.rpf
    ShaUnpack("df48db9461369043491c1dd9ac5f8d21715dce0cc5d178241956d33e08152135"), // dlc.rpf/x64/levels/mpchristmas3/vehiclemods/broadway_mods.rpf
    ShaUnpack("00074b56e0e25466c90f4807dde80dce20799583968fde834d308e28304e31b3"), // dlc.rpf/x64/levels/mpchristmas3/vehiclemods/entity3_mods.rpf
    ShaUnpack("7a3235a6498f79fa7bf2bbeb9112d31b45ceb53e738246452f0abbe3c028749f"), // dlc.rpf/x64/levels/mpchristmas3/vehiclemods/eudora_mods.rpf
    ShaUnpack("5e124ba0a444d4437a18ba7bd1374a43dbdd9190257acf9be6c98ac291e2337a"), // dlc.rpf/x64/levels/mpchristmas3/vehiclemods/everon2_mods.rpf
    ShaUnpack("4b7b8233b6ec04ebf0555603784ab24381ab0c8fd46e118a73e4ef93c2d3fc7c"), // dlc.rpf/x64/levels/mpchristmas3/vehiclemods/issi8_mods.rpf
    ShaUnpack("258d87a100153e309da204d574c8a3b3fe85a3e5407f4097a8f3d9664700df63"), // dlc.rpf/x64/levels/mpchristmas3/vehiclemods/journey2_mods.rpf
    ShaUnpack("5a74b5b6b4834326ef034c134a132483d9abca7a44e9df4043f2a64cf62c3544"), // dlc.rpf/x64/levels/mpchristmas3/vehiclemods/manchez3_mods.rpf
    ShaUnpack("9a62daf4ce070bd9d7d4b2a4fea451b009a3244daf45425cb2b93f86e5a6d3f4"), // dlc.rpf/x64/levels/mpchristmas3/vehiclemods/panthere_mods.rpf
    ShaUnpack("17897fe1ec7e332accbf89642f97195f5a3d0a6fc8b6a5b3b6644690fa806534"), // dlc.rpf/x64/levels/mpchristmas3/vehiclemods/powersurge_mods.rpf
    ShaUnpack("f475e2b6746b300382e70b34dd3b3557af112d69c360ff677b294718d51cadbc"), // dlc.rpf/x64/levels/mpchristmas3/vehiclemods/r300_mods.rpf
    ShaUnpack("c701b8a4d9edd3b5f2f5933319612927c685b07ae301de9652c1ab610b3fcdaf"), // dlc.rpf/x64/levels/mpchristmas3/vehiclemods/surfer3_mods.rpf
    ShaUnpack("ac1f443e3c729a68c70ae4e18f6d0c0c7ce15ba0898d19a5431cda8811fc5250"), // dlc.rpf/x64/levels/mpchristmas3/vehiclemods/tahoma_mods.rpf
    ShaUnpack("6717f88d027548fbee0b41c2c83c0a10e4b158ec01f0af7492da2a9e4b71c593"), // dlc.rpf/x64/levels/mpchristmas3/vehiclemods/tulip2_mods.rpf
    ShaUnpack("61945183b8ba5d93f3342410187239bda8443886472c2349fa79dee31d7cde64"), // dlc.rpf/x64/levels/mpchristmas3/vehiclemods/virtue_mods.rpf
    ShaUnpack("6a3af1b0e4c867ddef68b4d151096cbf1beb1e13384f35521c0111acb1739034"), // dlc.rpf/x64/models/cdimages/mpchristmas3_female.rpf
    ShaUnpack("5d28ed2115f9594e1eeaaec8aa74b65091b7e489e9c99d56a8db3e7f78fd2806"), // dlc.rpf/x64/models/cdimages/mpchristmas3_female_p.rpf
    ShaUnpack("ffd365073c296aa044dc34eaeb3383c4dc5e098157ea5a02516781b01a13d64a"), // dlc.rpf/x64/models/cdimages/mpchristmas3_male.rpf
    ShaUnpack("0ed75aee0f39f672da1f856946248ede368b8b87c8d691772a880f5b39440b5b"), // dlc.rpf/x64/models/cdimages/mpchristmas3_male_p.rpf
    ShaUnpack("12876b58ab17ca88cd5c19515923140614340fda492c1b3237bf89794043cf92"), // dlc.rpf/x64/models/cdimages/mpchristmas3_ped_mp_overlay_txds.rpf
    ShaUnpack("9116dcbaf4d8d8b84f8a8b3581d0e822f623af990f0dc616b63d092eff35d70f"), // dlc.rpf/x64/models/cdimages/weapons.rpf
    ShaUnpack("bcabc91394a61059af3a7d91b943015af21aeb7b8ebe7942dfc4459e57c94d4a"), // dlc.rpf/x64/models/cdimages/peds/mpchristmas3.rpf
    ShaUnpack("24448f165182802cf1a6dd17f772017250f61e80615c316ee3f6b6d89c5e4e33"), // dlc.rpf/x64/models/cdimages/peds/mpchristmas3_cutspeds.rpf
    ShaUnpack("78c24391e7470dceef05adc9ce712a169b4df11003b9477842ab70d3c7311b57"), // dlc.rpf/x64/models/cdimages/peds/mpchristmas3_cutspeds_p.rpf
    ShaUnpack("2c55d029f95e082b0006d3c212788bac0ed4760f983b2c195f696c790b3e0303"), // dlc.rpf/x64/models/cdimages/peds/mpchristmas3_p.rpf
    // update/x64/dlcpacks/mpchristmas3_g9ec/dlc.rpf
    ShaUnpack("84e61637cf76f5a50020c3116984bf3a1a6e76f35bd58645fc3c9e8ddf878401"), // dlc.rpf
    ShaUnpack("c0efaf8fa061c69be13042f644350e8b75d47e74b5012df6fa97b6898eea201f"), // dlc.rpf/x64/levels/mpchristmas3_g9ec/vehiclemods/entity3hsw_mods.rpf
    ShaUnpack("6ce8c0a9a3f4df30285544533fb020d2bf96652a1082ee390924e3048e8a7c02"), // dlc.rpf/x64/levels/mpchristmas3_g9ec/vehiclemods/issi8hsw_mods.rpf
    // update/x64/dlcpacks/mpexecutive/dlc.rpf
    ShaUnpack("2074694692c4bb2a4a38ea7bcfde6e57e4711e686bd369830d932c1c9cb447e8"), // dlc.rpf
    ShaUnpack("563b41f87e808f98c7cf3c7b8775ea5824b33419c67989cbf4e5041b3ed55469"), // dlc.rpf/x64/anim/creaturemetadata.rpf
    ShaUnpack("14344a88d7be22c306c1245a4efb6e2ce8ead878a2d92a6fcf7de9eb1785d98a"), // dlc.rpf/x64/anim/ingame/clip_anim@.rpf
    ShaUnpack("e01c82bafd0ed0711892d6c35c414715f85eb69e165ec1d9c91f3e7d0f1e6db6"), // dlc.rpf/x64/audio/occlusion.rpf
    ShaUnpack("5bfa2abf630d55dd6e9d6aac6665f8ca3ac69ac4ebcd31c59ed5e8844c3eb4f9"), // dlc.rpf/x64/data/effects/ptfx.rpf
    ShaUnpack("c15b9faf0a58e28e1d11ad27abe2dd5e287c26129142c13a7616ad50824cbbc4"), // dlc.rpf/x64/data/lang/americandlc.rpf
    ShaUnpack("c88d33b0f0ee46790056a6739df2969db025019050d3bdf872debcc96091309a"), // dlc.rpf/x64/data/lang/chinesedlc.rpf
    ShaUnpack("2215d1a19abdda4530e1738be83f2226c845f9f44077ce44b6a937a31b12a141"), // dlc.rpf/x64/data/lang/frenchdlc.rpf
    ShaUnpack("24236d8299b202061d527c089b79fe43b53443dfc3f28c9b9897c7e6918c6ddc"), // dlc.rpf/x64/data/lang/germandlc.rpf
    ShaUnpack("1080f7f03c8b586c7373f99458ba552126897097cc64a13340f36ba1cad93b4f"), // dlc.rpf/x64/data/lang/italiandlc.rpf
    ShaUnpack("83001c6ebaf91d742006dc011250dda6827d94ebabf197673bdedbcde8a81f69"), // dlc.rpf/x64/data/lang/japanesedlc.rpf
    ShaUnpack("4fe772cc5d81c53a50684a4d2ae6ad36f2edbd6fbf82afab8efaefd482b6b81b"), // dlc.rpf/x64/data/lang/koreandlc.rpf
    ShaUnpack("a392cc2050bffcf6a009961ffc8a9500f6a6514151670a1d80a0f24a62c07a24"), // dlc.rpf/x64/data/lang/mexicandlc.rpf
    ShaUnpack("480edd1a76523962c0de21ef8a960280ba4de417d28e3360b93bf2ad5de7d919"), // dlc.rpf/x64/data/lang/polishdlc.rpf
    ShaUnpack("5693bdd5f21ebe9c0964896b5621c662adc2f7c70dc4c5904b9190b7d54766af"), // dlc.rpf/x64/data/lang/portuguesedlc.rpf
    ShaUnpack("9c2f3f4e430e682934b4eb572b744da454163a7419f2c1942f06fa8f86e06f71"), // dlc.rpf/x64/data/lang/russiandlc.rpf
    ShaUnpack("b0867c53b40a86be3d0aec973b7667a96038ec87517bb7e483ee333399c860c5"), // dlc.rpf/x64/data/lang/spanishdlc.rpf
    ShaUnpack("2600b121feda071933696f12107cb2490c8452b79b9716a5e1dca4f624dc4fdc"), // dlc.rpf/x64/levels/gta5/interiors/exec_warehouse_placement.rpf
    ShaUnpack("e4cfad56492581157cc20d2c2cb30d0b2fc709456a0f4294a502c05e8126bf66"), // dlc.rpf/x64/levels/gta5/interiors/int_office_01_dlc.rpf
    ShaUnpack("70342b5fa49c12692cb154c657f31b0499c21bf8210356155d71f42b277746f5"), // dlc.rpf/x64/levels/gta5/interiors/int_office_01b_dlc.rpf
    ShaUnpack("1dce0e3e0316f892926cba4a9313ab329a3138de9be839dae2421c4f6b0bf4b9"), // dlc.rpf/x64/levels/gta5/interiors/int_office_01c_dlc.rpf
    ShaUnpack("8f558b9aa5b229d6d0a1589083668627d9b2ac8d6cdd1f1e33cd2ca7e408e676"), // dlc.rpf/x64/levels/gta5/interiors/int_office_02_dlc.rpf
    ShaUnpack("0763053e02d068f07da7f95aa43a0ee9050aa35bf1d809e10b69ff79978c17c3"), // dlc.rpf/x64/levels/gta5/interiors/int_office_02b_dlc.rpf
    ShaUnpack("2fcf5f28701d8e8caac6a13824e69ba95758f733486d5f0f8fd3a2827b00156d"), // dlc.rpf/x64/levels/gta5/interiors/int_office_02c_dlc.rpf
    ShaUnpack("546feae7aa357a6ababe565510378cf8667c9b64307abe24b061ce7adc9831d0"), // dlc.rpf/x64/levels/gta5/interiors/int_office_03_dlc.rpf
    ShaUnpack("a186af74845c6a5e19318500673d130757b724ff8efb1d4b0c7a055cfd9b6b84"), // dlc.rpf/x64/levels/gta5/interiors/int_office_03b_dlc.rpf
    ShaUnpack("cd5159d69f5a48bff2cc166edc85515e80aa6c4d502ac4cb672d401f035553d8"), // dlc.rpf/x64/levels/gta5/interiors/int_office_03c_dlc.rpf
    ShaUnpack("941edd4b7266dfb671657758c7666e288c39affbe3336c139e3a414d9c979f82"), // dlc.rpf/x64/levels/gta5/interiors/int_warehouse_large_dlc.rpf
    ShaUnpack("c13ba7033291b172285f74a70225cb8da266c91b4f5a5c4974c6a15beecbbed9"), // dlc.rpf/x64/levels/gta5/interiors/int_warehouse_medium_dlc.rpf
    ShaUnpack("d48358488cad86021c802a7312c290e8a3457fc835aa1637e3b727c3b02123b9"), // dlc.rpf/x64/levels/gta5/interiors/int_warehouse_small_dlc.rpf
    ShaUnpack("a78b438aa942508cf074ba546e351b09d862497c41f9f55dc5313d8e943ccdc5"), // dlc.rpf/x64/levels/gta5/props/int_mp_doors_ex.rpf
    ShaUnpack("8f0467022567291b8b5d06a0e160417ea57087e51734303c8f42163d21f928f6"), // dlc.rpf/x64/levels/gta5/props/int_mp_h_accessories_ex.rpf
    ShaUnpack("a95bc645f1b17283cab41ddf4f2ae6c2e9b9a6d1a98b350844b2a5bf24b3c8a4"), // dlc.rpf/x64/levels/gta5/props/int_mp_h_dining_ex.rpf
    ShaUnpack("a70544fd70223f1537d6be187296bfbc6cec519131bf67c04f79631ba14364a3"), // dlc.rpf/x64/levels/gta5/props/int_mp_h_lighting_ex.rpf
    ShaUnpack("ff16287ddf4aa543961420fdf1280ac33fcad922f98f75700fa9dfbe548631ea"), // dlc.rpf/x64/levels/gta5/props/int_mp_h_seating_ex.rpf
    ShaUnpack("8d385183cd787e3719e832f5bb0f22a8eb295c38fe2e6797aac78720b730afd9"), // dlc.rpf/x64/levels/gta5/props/int_mp_h_tables_ex.rpf
    ShaUnpack("fac7104d2ec5f3893d8ecc666b94c86db1fc6c44934cdfddd76034a8fe4a7947"), // dlc.rpf/x64/levels/gta5/props/prop_adv_case.rpf
    ShaUnpack("4f5b7e3ad78e04c1b37ee281d7d5f8d5da1c1845c750a17e2db039ddfa43ccb9"), // dlc.rpf/x64/levels/gta5/props/prop_exec_chairs.rpf
    ShaUnpack("40d4750585913c61d9610e3f865338f6508d4c52480e2711bc87ab3ea5fb4434"), // dlc.rpf/x64/levels/gta5/props/prop_exec_concrate.rpf
    ShaUnpack("9df6bef58f9583428cc7b763bf92a54ad4f20aaa38ba4aa5adb6f7b1082f3c93"), // dlc.rpf/x64/levels/gta5/props/prop_exec_crashedp.rpf
    ShaUnpack("7bf7a2612db1b37949cce45f3e2dea364b3ca97c4a04cedd104a028b7150e1d1"), // dlc.rpf/x64/levels/gta5/props/prop_exec_doors.rpf
    ShaUnpack("3bfddb73f58e32335e7d7db2a84cfeb1c570cd4aca29dd9e415203929a1cf0cc"), // dlc.rpf/x64/levels/gta5/props/prop_tv_settop_remote.rpf
    ShaUnpack("06b298e6d40cd32e7ff08079a2fc7406e7fa7cf48fbd01050e0cd924f32adf67"), // dlc.rpf/x64/levels/gta5/vehicles/mpexecutivevehicles.rpf
    ShaUnpack("a4a143ef43280c5abf51bfac7103998289a5f3a5b9d0abf49e875c5e1fc898ad"), // dlc.rpf/x64/levels/mpexecutive/vehiclemods/bestiagts_mods.rpf
    ShaUnpack("d5642ee3f0ab146951180bdb09868ba0990f9cdc3adb60d098c7d3d2193d00b0"), // dlc.rpf/x64/levels/mpexecutive/vehiclemods/fmj_mods.rpf
    ShaUnpack("10f777c3fbe9422f4f79ca05ecced62d6cd7f82dd5c18c339e4a2b042f27cb5c"), // dlc.rpf/x64/levels/mpexecutive/vehiclemods/pfister811_mods.rpf
    ShaUnpack("3f0470565df641db97ba1038521cafbddb126643e65e2985ebe0471e5731ccf7"), // dlc.rpf/x64/levels/mpexecutive/vehiclemods/prototipo_mods.rpf
    ShaUnpack("7d6e70d4b6fabbc9cb50ceb2f5901a761ec9dc8aa16ed9129334bf0b0037b087"), // dlc.rpf/x64/levels/mpexecutive/vehiclemods/reaper_mods.rpf
    ShaUnpack("a317f460dcdb4167b4428c7be92d0b365e95de7a32837aae916acf79af8ff445"), // dlc.rpf/x64/levels/mpexecutive/vehiclemods/seven70_mods.rpf
    ShaUnpack("dfaf1d02467f72fbd5d88f1e6f0e40e3c0b2ae106353fae35892a64c7e1992c3"), // dlc.rpf/x64/models/cdimages/mpexecutive_female.rpf
    ShaUnpack("936724ec8fdf3380e76469b0c9e68cfc6337a6c361ada01ccdf276cc6098a896"), // dlc.rpf/x64/models/cdimages/mpexecutive_female_p.rpf
    ShaUnpack("23472dbcd924aec5fb7929f52283edfe576890f9ec98b2716fda19d4f03f24da"), // dlc.rpf/x64/models/cdimages/mpexecutive_male.rpf
    ShaUnpack("63658f85f88c8e14c294ed67a000d5def05058c9d133c92f28721dfeb69112b7"), // dlc.rpf/x64/models/cdimages/mpexecutive_male_p.rpf
    ShaUnpack("6a69655de640533053d9db83cad9fcbf360df97efbda736c7bd7ffe6415f32f8"), // dlc.rpf/x64/models/cdimages/mpexecutive_ped_mp_overlay_txds.rpf
    ShaUnpack("d7181028cc082d13aa1200b75a0197944f9dac26bd3811630975d0645cf5a1e5"), // dlc.rpf/x64/models/cdimages/peds/mpexecutive_component.rpf
    ShaUnpack("bfc52a0768163da30320164d87262c2cefeaa9fa34939ea504c9f289dff3393e"), // dlc.rpf/x64/models/cdimages/peds/mpexecutive_component_p.rpf
    ShaUnpack("ba909a172fcabcd282c8036fe06de513e9cb6e02c13244d33428c45cfd5b9059"), // dlc.rpf/x64/models/cdimages/peds/mpexecutive_streamed.rpf
    ShaUnpack("3ded638f3a09c50f463e482de5e2995936158cec630feea74f5c274f1459bfd5"), // dlc.rpf/x64/models/cdimages/peds/mpexecutive_streamed_p.rpf
    // update/x64/dlcpacks/mpg9ec/dlc.rpf
    ShaUnpack("5246a2746389001c495b444b988ff4fb91025dc19db6ce25e3394985c46b4973"), // dlc.rpf
    ShaUnpack("c83a5cd92daf02c724a199a68242c947398ac22112a592294f94d6b855acb1fa"), // dlc.rpf/x64/anim/creaturemetadata.rpf
    ShaUnpack("21b59e329c8539428917f767d21aeb6cf33d9276fa01a19cc79d98a8635cd39e"), // dlc.rpf/x64/anim/cutscene/cuts_gen9_mig.rpf
    ShaUnpack("56ef485152dddb19885c6b08e2c0d4ee5931b0665caeba20eadabab523d607f4"), // dlc.rpf/x64/anim/ingame/clip_anim@.rpf
    ShaUnpack("31e1180e2cd78c1763a4de49a09896a9ce6150a90f46fd947e19faae141c9d42"), // dlc.rpf/x64/data/effects/ptfx.rpf
    ShaUnpack("ab14d7ddb28a0afb59ca701b77b0107e80dcef0d4bfae3f2546bbdb0c8f12391"), // dlc.rpf/x64/data/lang/americandlc.rpf
    ShaUnpack("e3a93f70e69368bc32e1167739b26513af6e20803bf9045b3af23d1405fcf8a8"), // dlc.rpf/x64/data/lang/chinesedlc.rpf
    ShaUnpack("35b5158350f6f698d914492da91ed4d1c5cdec0dcf08659192a92f39a4cebd3a"), // dlc.rpf/x64/data/lang/chinesesimpdlc.rpf
    ShaUnpack("5257ed8cac0d8032bb14d3a256967afb6f6d09841fd09b23294605d2b2ce03b3"), // dlc.rpf/x64/data/lang/frenchdlc.rpf
    ShaUnpack("bbdc2da57da405ec50b1dfbc5a6831e07d0c55b65d3e90bfa94e4aef0dba5fe5"), // dlc.rpf/x64/data/lang/germandlc.rpf
    ShaUnpack("01f56a97629c2803151d8ac95639ae15fd8a95540d6c5a506eea107b5b4d38e7"), // dlc.rpf/x64/data/lang/italiandlc.rpf
    ShaUnpack("dccd760f34e75282d02c11eb9a3234b477149c42ea672a6547ed8d87af7d7f3c"), // dlc.rpf/x64/data/lang/japanesedlc.rpf
    ShaUnpack("136a733022cfcc900e008c658ebb741fde8b65bb25bd2ca87faee56c64a226d1"), // dlc.rpf/x64/data/lang/koreandlc.rpf
    ShaUnpack("16b546a7a45ee008db5f9cce24acb216dcd3cb2b8265602e8e124832d902142f"), // dlc.rpf/x64/data/lang/mexicandlc.rpf
    ShaUnpack("a2f0e1bc998481de05f3a818b601786a950eb7533b96a229a9ffe9b9114becf9"), // dlc.rpf/x64/data/lang/polishdlc.rpf
    ShaUnpack("0910472a609763a69f8ad41e0f6afdb52b43574e6191300cfcc820bc152c5ce9"), // dlc.rpf/x64/data/lang/portuguesedlc.rpf
    ShaUnpack("33d26fc3468f68124129745d112309db37218f38877f8d8312f9e1a49597d58b"), // dlc.rpf/x64/data/lang/russiandlc.rpf
    ShaUnpack("d55223ba8c838b0a191e6899dbe053ee89f32726d782aa0511aa7900f005a1b4"), // dlc.rpf/x64/data/lang/spanishdlc.rpf
    ShaUnpack("f342e3339294bbdd55eb359314818c74a4b67dbe9bb6d6bb2a6a2aadb3ada299"), // dlc.rpf/x64/levels/gta5/props/prop_exc_01.rpf
    ShaUnpack("5a614b4a7be209b8b6440ee6ed248a30f72fa6c60a9af5c6fe0927bf4d1603f0"), // dlc.rpf/x64/levels/gta5/props/prop_tr_overlay.rpf
    ShaUnpack("ac72613aa91f264a628ab7e0e2b8ecab6c1937c13585cc87f457422e094ecec6"), // dlc.rpf/x64/levels/gta5/vehicles/mpg9ec.rpf
    ShaUnpack("8a320ef0afe60b070a8ea4676a8c1a793c5b0d3667ba0da321b8f5a4cb34b7b5"), // dlc.rpf/x64/levels/mpg9ec/vehiclemods/arbitergt_mods.rpf
    ShaUnpack("3c01bd51126c99b829015adcd7c637afa7c737f702959d5f218f7622794c3c20"), // dlc.rpf/x64/levels/mpg9ec/vehiclemods/astron2_mods.rpf
    ShaUnpack("35443c6da240748aea4d92cd819d3edcea12f4f4b8e56ea7f834919e2ea1df8c"), // dlc.rpf/x64/levels/mpg9ec/vehiclemods/cyclone2_mods.rpf
    ShaUnpack("ad6a081a2765fb1ce0ad63de0591ac28225ce5d7f9324a30c1d42d72eec17b16"), // dlc.rpf/x64/levels/mpg9ec/vehiclemods/ignus2_mods.rpf
    ShaUnpack("a4e36a8633d3a568de38e64b062d394b30c078fcf82ae71b46188e3c6ee1509b"), // dlc.rpf/x64/levels/mpg9ec/vehiclemods/s95_mods.rpf
    ShaUnpack("980cddb45dbbc33a39abef0011b7ad40c2afcb36e645e78395bb6ee8ee99e8a9"), // dlc.rpf/x64/levels/mpg9ec/vehiclemods/vehlivery3_mods.rpf
    ShaUnpack("0371b1d62fe1b19a92a7909731cc50df7688387675f2421548470fced1cd659b"), // dlc.rpf/x64/models/cdimages/mpg9ec_female.rpf
    ShaUnpack("a46adba5fab1309b5df7c01ece95c780eb5fe3467af36affdb2dbe56c691423c"), // dlc.rpf/x64/models/cdimages/mpg9ec_female_p.rpf
    ShaUnpack("3cc52ed2ad57999465a351e463d5cffe3850224d314dca3ca05125cc95135c41"), // dlc.rpf/x64/models/cdimages/weapons.rpf
    // update/x64/dlcpacks/mpgunrunning/dlc.rpf
    ShaUnpack("19e63c462fded002d4efe12beb1a956066834990b16f16cda8a46966b7d880e2"), // dlc.rpf
    ShaUnpack("151aac971c7b442ce9bec732b4fc132d40646f9b72dc9f1332ceb90945663623"), // dlc.rpf/x64/anim/cutscene/cuts_bunk.rpf
    ShaUnpack("3a94811f4504bd7eb3d6dd7ab1245db926db23daf4007ae95e7b2332406018b0"), // dlc.rpf/x64/anim/ingame/clip_anim@.rpf
    ShaUnpack("3b349cf3478a06ec3240a182b8d64c0d39eac9196a3cb05234f64a98117134ea"), // dlc.rpf/x64/audio/occlusion.rpf
    ShaUnpack("ff3d01cd435d799a8fedfeaa25626df7d6c46394e5d488e28d32b2016695169d"), // dlc.rpf/x64/data/effects/ptfx.rpf
    ShaUnpack("d5ed90a99c3943081f70d0891cca64666e576607ee9d355c232134c4c2a9daf2"), // dlc.rpf/x64/data/lang/americandlc.rpf
    ShaUnpack("b7eae5b63ca20add74f3f19ed2135ee0244dc44aba7546532811478ca946e4f9"), // dlc.rpf/x64/data/lang/chinesedlc.rpf
    ShaUnpack("74485b4597ea1fb0b8fc3f95576e500b8330e66d63d659d69faee7f65fff345b"), // dlc.rpf/x64/data/lang/frenchdlc.rpf
    ShaUnpack("2edc6ccdda3ee9170fdb29fd34fbe423bc001e8826eb5d35efa969c0b4c9734c"), // dlc.rpf/x64/data/lang/germandlc.rpf
    ShaUnpack("487b1c80cd494aded6fc4f0fc75210a7aa9ee6f7bc893858ea0f79f3c6749fbe"), // dlc.rpf/x64/data/lang/italiandlc.rpf
    ShaUnpack("b13272aa145c9f4777908c8a992490c1744e00d87511be12929e540c1c37eb92"), // dlc.rpf/x64/data/lang/japanesedlc.rpf
    ShaUnpack("eee386fd3b22df240be861df09c47cfc0a3ee9f583e5751e0583ec670428778a"), // dlc.rpf/x64/data/lang/koreandlc.rpf
    ShaUnpack("2f661a8f9e9315e8f5ab1dac2a1b6a10f77cb5773aecd46c8b2c55237ce5d833"), // dlc.rpf/x64/data/lang/mexicandlc.rpf
    ShaUnpack("db111c7c4108d7a9d2a6c92386ac0b9e80af2c1c0be0c02d61b406c47a1e3957"), // dlc.rpf/x64/data/lang/polishdlc.rpf
    ShaUnpack("f5dd97eb736b0def518d3f72ddad0c8a8e57275901cf42c56750d1e44e7645ce"), // dlc.rpf/x64/data/lang/portuguesedlc.rpf
    ShaUnpack("7cb42dde891f7d8faf83613c3bfc919ecc560beea5b74ee96a421ceb04db26f1"), // dlc.rpf/x64/data/lang/russiandlc.rpf
    ShaUnpack("c62a3c6009b05f498c325c852145ee1d9beb8688c52839095e8792df3ac33979"), // dlc.rpf/x64/data/lang/spanishdlc.rpf
    ShaUnpack("e736a3bd27a48c824b7239e0d6562de9d74addfc8d8b23771db92ccf20ee4b2a"), // dlc.rpf/x64/levels/gta5/interiors/gr_entrance_placement.rpf
    ShaUnpack("64768f31608ba65c96a3b3a6840ec5e16f1bff0820ceea71d90bc86390dd650e"), // dlc.rpf/x64/levels/gta5/interiors/grdlc_int_01.rpf
    ShaUnpack("8f3ccba93cb1c31bec22a303f06e73506d20588e0cc32fe13074cf4e6e02d255"), // dlc.rpf/x64/levels/gta5/interiors/grdlc_int_02.rpf
    ShaUnpack("a304581c3baf34f9d5a0f48f7de4d1f1b068720ab12c96d54d1f49d5b7e10f92"), // dlc.rpf/x64/levels/gta5/interiors/grdlc_interior_placement.rpf
    ShaUnpack("48e0645db2f7f9fff26ac6299fb384099f7cde826ef73528984dcd251ab37966"), // dlc.rpf/x64/levels/gta5/interiors/grdlc_yacht_int.rpf
    ShaUnpack("4284c89eea6265df8813530fbc727501a4a4857931c904952c2e2d0b56a6a755"), // dlc.rpf/x64/levels/gta5/interiors/grdlc_yacht_lod.rpf
    ShaUnpack("37098909f80db8c239975a7a5f6873c86aa1158916fdf923eb13a261c8d949ee"), // dlc.rpf/x64/levels/gta5/interiors/grdlc_yacht_placement.rpf
    ShaUnpack("2b6d0503399f64bfd02cb52e15c7aa4c8fa13a353dbad46252447b38818ee94e"), // dlc.rpf/x64/levels/gta5/interiors/yacht.rpf
    ShaUnpack("38c75ed442a29446ead4577b82cfc511fbd082749b6cc16318a4aa8c71027733"), // dlc.rpf/x64/levels/gta5/interiors/yacht_metadata.rpf
    ShaUnpack("46c96a71bc09db2735d81761a4b2a7a7b18fb76d71972f3a5baf0b584cc873bb"), // dlc.rpf/x64/levels/gta5/props/dlc_gr_chair.rpf
    ShaUnpack("02c9d9fe846a1a35040ffac67023f5c4102e5ffe3b28b95fe10fbce859154594"), // dlc.rpf/x64/levels/gta5/props/dlc_gr_yacht_props.rpf
    ShaUnpack("d62b9a31d1407dd446b92f6fb16558a9476e57602fb6122292f485c6382ab4ea"), // dlc.rpf/x64/levels/gta5/props/dlc_grinterior_lightrig_props.rpf
    ShaUnpack("3cb46a49a9705ec18c0c77de3b053e05f6c31c1b32c4a75ab6e8cc237b23406c"), // dlc.rpf/x64/levels/gta5/props/dlc_grinterior_props.rpf
    ShaUnpack("0af2d9b16634e547119f1fb1c3507570febb559c9af8e941958d0200287fb9d5"), // dlc.rpf/x64/levels/gta5/props/mp_gr_landing_pads.rpf
    ShaUnpack("bca8f6707f0fdfe512fc5f0925fcf8fada01e93eb464618f7fafd963a33eb00d"), // dlc.rpf/x64/levels/gta5/props/prop_gr_appliances.rpf
    ShaUnpack("496b15167133604973dcc4ffff855bee2929138291032e0d2d31dc3f499bebd4"), // dlc.rpf/x64/levels/gta5/props/prop_gr_benches.rpf
    ShaUnpack("58042191776125ea57bf9019ddddf0c8da54fb2304a38eb920d3b06731fd3308"), // dlc.rpf/x64/levels/gta5/props/prop_gr_bunkerdoor.rpf
    ShaUnpack("5346367f9f3192a36e309f1a2020530ed630153684ba26122653ae8eb8bdaf9e"), // dlc.rpf/x64/levels/gta5/props/prop_gr_crates.rpf
    ShaUnpack("d097332abaf13f46b24598ab4e1646657c87f730c55e26b6ddbb0913b3e535f5"), // dlc.rpf/x64/levels/gta5/props/prop_gr_cratespile.rpf
    ShaUnpack("c1b6da2758ea07801e6cbd3b61f88529b707c2f87e1fdb2a18b3182ca7c81127"), // dlc.rpf/x64/levels/gta5/props/prop_gr_gates.rpf
    ShaUnpack("e3e6f458d4e950979723dc03a58aea813e7b204b00deab66af5e468a539de81b"), // dlc.rpf/x64/levels/gta5/props/prop_gr_gunsmithsupl.rpf
    ShaUnpack("14587637c6e8089247ea9ea729723cb91a8f04e1b169d9ec1a272f6e20258b65"), // dlc.rpf/x64/levels/gta5/props/prop_gr_machinery.rpf
    ShaUnpack("408baa5a01010104e397bd2b36de69cb0da0cd628074463d6cf20d50ca60a9f5"), // dlc.rpf/x64/levels/gta5/props/prop_gr_machinery_cnc.rpf
    ShaUnpack("677379282ee0cc17b09bda56a00df0d28f3feed57e454e5d6495007d59a849a6"), // dlc.rpf/x64/levels/gta5/props/prop_gr_mine.rpf
    ShaUnpack("9cf4e916c90a7df1f0d4ed0d42fcbaa5a9300412bf5845a3689612a5511472a7"), // dlc.rpf/x64/levels/gta5/props/prop_gr_missile.rpf
    ShaUnpack("6d1476b8b86f8f6c4ff6e0497586258b0e10e9310a77460ac8d126c0f82bbfcb"), // dlc.rpf/x64/levels/gta5/props/prop_gr_para_01.rpf
    ShaUnpack("7476459e536c63512dc9f820c024840c463d5d1b9b25f7dca6d675c45f75bd82"), // dlc.rpf/x64/levels/gta5/vehicles/mpgunrunningvehicles.rpf
    ShaUnpack("28f7f27bc6dd1fb612885be444f44abff0b522760aa619ff9ee6ee1458d0f3f1"), // dlc.rpf/x64/levels/mpgunrunning/vehiclemods/apc_mods.rpf
    ShaUnpack("faab11c5707ec896d974b3e35a6f70265f04c9e79b2dbe903ecc24509b087521"), // dlc.rpf/x64/levels/mpgunrunning/vehiclemods/ardent_mods.rpf
    ShaUnpack("df1b792d5c90ec484b0f1d1bd04f384dd96d0ae13f6c7bdce19f8fa20c504e6a"), // dlc.rpf/x64/levels/mpgunrunning/vehiclemods/cheetah2_mods.rpf
    ShaUnpack("406b82e839a50cd2e27d427d831ff7375def264cffcb10715da212f7c0f063a6"), // dlc.rpf/x64/levels/mpgunrunning/vehiclemods/dune3_mods.rpf
    ShaUnpack("0fcea4a82ea7173b16cb9678e1282b6bf41693b75d09a2ece8dcac53c4a8b372"), // dlc.rpf/x64/levels/mpgunrunning/vehiclemods/halftrack_mods.rpf
    ShaUnpack("d19f53ed76156899699068c785b757d0e56940f57afe26a32e60f7c3b6a6db57"), // dlc.rpf/x64/levels/mpgunrunning/vehiclemods/insurgent3_mods.rpf
    ShaUnpack("955d5cc7009f48404387ab10c743364cb770b35414d313a80aff22bb0df7dee4"), // dlc.rpf/x64/levels/mpgunrunning/vehiclemods/nightshark_mods.rpf
    ShaUnpack("3cc19c4ec1a107a72b483ef55e73119e9a3ac67d325fcfc4a238c2186f2ba41e"), // dlc.rpf/x64/levels/mpgunrunning/vehiclemods/oppressor_mods.rpf
    ShaUnpack("76e2792f7ae21a8b5ee14e3ba81f3701cba37be20deeee2f2d79c7b3874d98f2"), // dlc.rpf/x64/levels/mpgunrunning/vehiclemods/streethawk_mods.rpf
    ShaUnpack("07fe19374531f3da324dc81c657e6b49bdadeeadda338c2fba95c3f82e9e9b9d"), // dlc.rpf/x64/levels/mpgunrunning/vehiclemods/tampa3_mods.rpf
    ShaUnpack("d84df1d31176174985177c60d11d55e56acda405c072ddf00bd3b65542ccac8f"), // dlc.rpf/x64/levels/mpgunrunning/vehiclemods/technical3_mods.rpf
    ShaUnpack("f279b9427eb05082b0f794bd2a0fbfa1e206c798fa505bbac6ea6bd3dde1ae9a"), // dlc.rpf/x64/levels/mpgunrunning/vehiclemods/torero_mods.rpf
    ShaUnpack("f702597e9c0e08cc51ee14908754cfa1efa523f978e7e167a2c21ed0e602bdd2"), // dlc.rpf/x64/levels/mpgunrunning/vehiclemods/trailerlarge_mods.rpf
    ShaUnpack("****************************************************************"), // dlc.rpf/x64/levels/mpgunrunning/vehiclemods/trailersmall2_mods.rpf
    ShaUnpack("79f00f31b4cd5bc4761af85b4bffb492cfc2ed141e9bb0c8bc46a69c4fbebf40"), // dlc.rpf/x64/levels/mpgunrunning/vehiclemods/vagner_mods.rpf
    ShaUnpack("b17ac94beb063ba302c9bc3e9823d99276e4db5de092bf601226338d51cf3896"), // dlc.rpf/x64/levels/mpgunrunning/vehiclemods/xa21_mods.rpf
    ShaUnpack("4b40f7f1b602c8c709a1f5063e86ca62efbae289bcbee92d11b4ab1cf4dfd668"), // dlc.rpf/x64/models/cdimages/mpgunrunning_female.rpf
    ShaUnpack("3737a52ed4c492ba76dd0bb2f806f96795b9c0dceda54d704cd76dbe70397d72"), // dlc.rpf/x64/models/cdimages/mpgunrunning_female_p.rpf
    ShaUnpack("9d2e31ac23908962496f2193ba88b2be596c70d4d506513e511d2e2d0bde793a"), // dlc.rpf/x64/models/cdimages/mpgunrunning_male.rpf
    ShaUnpack("cbfec9b9d1411e91daff060c09b52a0e84c45156c26b1aedf0e45c54aa8649d7"), // dlc.rpf/x64/models/cdimages/mpgunrunning_male_p.rpf
    ShaUnpack("191cedb17e6f5b07d38b0c6901e5251a465debeb4b01036e2331861abb02419a"), // dlc.rpf/x64/models/cdimages/mpgunrunning_ped_mp_overlay_txds.rpf
    ShaUnpack("059b868cc1de8b87d775b471da8434b35de71922dea4e9d9d132ce353bd7cb16"), // dlc.rpf/x64/models/cdimages/weapons.rpf
    ShaUnpack("e1226ea7cdf8681f70c0c935a9fb011a61ec232a76243ee6e4e75145af46a22a"), // dlc.rpf/x64/models/cdimages/peds/mpgunrunning.rpf
    ShaUnpack("d66d54d211c63bb8221c4c8b4fb717dfc80c76beadbaa28df402fd2432abbc0b"), // dlc.rpf/x64/models/cdimages/peds/mpgunrunning_p.rpf
    // update/x64/dlcpacks/mphalloween/dlc.rpf
    ShaUnpack("7272fe3859de32aac65f4f47a3e1ab97a25c1fb191b63673cf280a6b7ae0fc7d"), // dlc.rpf
    ShaUnpack("68cfac2959e422c2f94633163c3855facf2a19e7feafae6ff717913d228bca04"), // dlc.rpf/x64/anim/ingame/clip_anim@.rpf
    ShaUnpack("422ddb138a6594b260ca0ff817f12071260eeefa1d3a4ceaeb44268777ed10cd"), // dlc.rpf/x64/data/lang/americandlc.rpf
    ShaUnpack("a411104359ac9ba5a40e7ecb7665f10ff2d8bfdb86b425d37a006a04b612f93a"), // dlc.rpf/x64/data/lang/chinesedlc.rpf
    ShaUnpack("0ad741dfc2c0dd0bd949bfc173515fcb21f0cd592015df8063cdcf74f689276d"), // dlc.rpf/x64/data/lang/frenchdlc.rpf
    ShaUnpack("f15fd07f12f6fbc1a27449d9cbc6d0c3bb30ed0b4e807b221af3fd1467406ebd"), // dlc.rpf/x64/data/lang/germandlc.rpf
    ShaUnpack("a9272bb43bb66906826a13ead1468a4230638aebe10368603443287020f00dcf"), // dlc.rpf/x64/data/lang/italiandlc.rpf
    ShaUnpack("a3bd170c02e87362ba72e71d7b63701e7a03ca93786c9edbe18616fd8e6f5123"), // dlc.rpf/x64/data/lang/japanesedlc.rpf
    ShaUnpack("66f16ab8df562ef10dd82f1a39fa2ed49515ff79d1049691f742999ce5fe8567"), // dlc.rpf/x64/data/lang/koreandlc.rpf
    ShaUnpack("58f8bc23de6ba1965f8ac94d6e1620dedf390042fe20974663b983ba2d2569f7"), // dlc.rpf/x64/data/lang/mexicandlc.rpf
    ShaUnpack("f080b94a6b313590d90fe8c23a91093bf366295ec704fc3e84d1afdc4b6511e7"), // dlc.rpf/x64/data/lang/polishdlc.rpf
    ShaUnpack("d33c3a2c99ac25687b406520632333adbc586c3858d2a881f264811b4a2b7730"), // dlc.rpf/x64/data/lang/portuguesedlc.rpf
    ShaUnpack("e586ddbee829020f66769a46a168aa02139fb9cb500821d423ebac0d2d251ea0"), // dlc.rpf/x64/data/lang/russiandlc.rpf
    ShaUnpack("1628cdf58e155e611573b83d812b43bc44a52831832b051a2cafb2328c72bcca"), // dlc.rpf/x64/data/lang/spanishdlc.rpf
    ShaUnpack("434a5259100fbe4e6b1f1e2043f87df4536725dc419eb5b633af94f7df21afbf"), // dlc.rpf/x64/levels/gta5/vehicles/hwvehicles.rpf
    ShaUnpack("d57288126bfb345bd6e73d1245deac780b8e480b47fc9008e4a7e95050fe7afe"), // dlc.rpf/x64/levels/mphalloween/vehiclemods/btype2_mods.rpf
    ShaUnpack("14a013ea6a99ad8513a83a9a986ccd70015b393505a80f4e0e7044bc69967271"), // dlc.rpf/x64/levels/mphalloween/vehiclemods/lurcher_mods.rpf
    ShaUnpack("ad8c9f411bd1fe87aabcf691ad0c08755225529cef457dd7c38ceb539c65b54f"), // dlc.rpf/x64/models/cdimages/mphalloween.rpf
    ShaUnpack("817bdfcee66fbe0dd4151fa9897d8a1593266c175ecc593dd5fd30dc5bdccc5f"), // dlc.rpf/x64/models/cdimages/mphalloween_ped_mp_overlay_txds.rpf
    // update/x64/dlcpacks/mpheist/dlc.rpf
    ShaUnpack("7324110d4d6fc52e44f9a657e409b4cdf9faf5008f71072b7f7281035693fb6e"), // dlc.rpf
    ShaUnpack("97ca2f275a05154e702bfbabf924d305ad250a8a544c68ed81ddfeef768d9187"), // dlc.rpf/x64/cs3_txd_patches.rpf
    ShaUnpack("2c15fd4c356fb16ec0b40e85f37ab34423f102955872002b01c555fb44186b85"), // dlc.rpf/x64/anim/creaturemetadata.rpf
    ShaUnpack("472325349ee4534092305fc5478932ea2fa738833cdebb8973c0e2c2fbc4cc7f"), // dlc.rpf/x64/anim/expressions.rpf
    ShaUnpack("208cc179c8b7e0da8073f86233ad080abe9aca25ac849d7bf5d5c43ad7ef31de"), // dlc.rpf/x64/anim/filters.rpf
    ShaUnpack("80a4b40aa1ce8e630b81c32843321d8fe53a70e2ed61a176ed011cb3b6875db6"), // dlc.rpf/x64/anim/networkdefs.rpf
    ShaUnpack("87bf39c7bcf12e5a579ab5552891912d81a8dc69b5992fbb993613946221037d"), // dlc.rpf/x64/anim/cutscene/cuts_heist_int.rpf
    ShaUnpack("e34285310ae25d241123595f32e1e06197b9753c0cf579d9e9d412fd55858583"), // dlc.rpf/x64/anim/cutscene/cuts_humane.rpf
    ShaUnpack("2310ae783cf51c1d6e681791c07e22020598e1adf1d7157ed265b5e404cb4eb6"), // dlc.rpf/x64/anim/cutscene/cuts_narcotic.rpf
    ShaUnpack("79490ad7552ceb4c8802376219d45c7634ff3aa833dff2e30e89739db9c1a292"), // dlc.rpf/x64/anim/cutscene/cuts_pacific.rpf
    ShaUnpack("9376b9b3211538db374547496f0465085991bedc7e4788fd4029dfd6ad7ee9af"), // dlc.rpf/x64/anim/cutscene/cuts_prison.rpf
    ShaUnpack("31e58a5c643aa2a2ad350dbfcc6207bb68a677f327b76871edd7b6d0ff390163"), // dlc.rpf/x64/anim/cutscene/cuts_tutorial.rpf
    ShaUnpack("bf6cd1b082065dddd8f4e80fe62a09adff3e0b410904186713f5d3a4cd8794e4"), // dlc.rpf/x64/anim/ingame/clip_anim@.rpf
    ShaUnpack("462c4fa511b0352e315d1fcee66c35f1a4dcbd24af8ffb36194e19397afa81b4"), // dlc.rpf/x64/audio/occlusion.rpf
    ShaUnpack("ed9ac0d933c167bdbf36bb3b799df1c08146fe82a506a5c95f3b752a48aa5065"), // dlc.rpf/x64/data/cdimages/carrec.rpf
    ShaUnpack("9d2384247cfb1797b2c2caa6991507e6427eea0be8738b57521f5c00d98ee2bc"), // dlc.rpf/x64/data/cdimages/docktease.rpf
    ShaUnpack("1c569be15837218fb5282bd38047fa404abb6be8d312cf1e5364301d8afca180"), // dlc.rpf/x64/data/cdimages/elitastravel.rpf
    ShaUnpack("b1c8e46d592e45e9b9e7c9c1bc1e663a875109e62bbdb996834880a8ad5f66e7"), // dlc.rpf/x64/data/cdimages/heist_mp.rpf
    ShaUnpack("c906a4878aa1eb550452c351b8ac550af710d53bd2006833cfad3b8d29733bc8"), // dlc.rpf/x64/data/cdimages/legendarymotsport.rpf
    ShaUnpack("f5d0b25a94f9330f8374731e4477fffebe89945b6414850972283bc05c731f75"), // dlc.rpf/x64/data/cdimages/superautos.rpf
    ShaUnpack("c1e5361940d26c9043e47777b547dca9e339e5938a77ea033ddb27a0fdcdca12"), // dlc.rpf/x64/data/cdimages/warstock.rpf
    ShaUnpack("f626b1d5f49331e4c12ec5d36ffd691177b966cd3110224d361e1b5dc2e76f83"), // dlc.rpf/x64/data/effects/ptfx.rpf
    ShaUnpack("c7aeeaf71db78ee0969065b1bfdc32bd04a8c118573e37f29f3f081a38ed30c3"), // dlc.rpf/x64/data/effects/ptfx_hi.rpf
    ShaUnpack("29254e480f91b1a3278d3fc26025fff81d07a0f6871eb6449d54093cd687143f"), // dlc.rpf/x64/data/lang/americandlc.rpf
    ShaUnpack("a14ca0079a0c02a182be0b1e9fb34752a9a7f595b08efa0d9d09918b9b34863c"), // dlc.rpf/x64/data/lang/chinesedlc.rpf
    ShaUnpack("7f84e24052a2f3a12089cf1c72be07dce8e6127e9b86631e206068e0de57dd04"), // dlc.rpf/x64/data/lang/frenchdlc.rpf
    ShaUnpack("12b77707fd6c43e6ef0368ee5c87a3428d3f5877a21a8c1cb4c332b0bb9ee127"), // dlc.rpf/x64/data/lang/germandlc.rpf
    ShaUnpack("031744e68f8e81967a733994d6aba71a0eee24497b30b45baacddce64f1b0c9a"), // dlc.rpf/x64/data/lang/italiandlc.rpf
    ShaUnpack("b854be1e4ab21368aa6c2e609e4ea435cc849d8f4750865073c15a5af4011e9e"), // dlc.rpf/x64/data/lang/japanesedlc.rpf
    ShaUnpack("251056873016adc5857aa2d6e3dcd78d3d038970351ebaf76fb2678b8f092319"), // dlc.rpf/x64/data/lang/koreandlc.rpf
    ShaUnpack("cdf3f0c00c6c135fba8104c072ebf2d1412c4e79b65592f61df7bfb7dbf6b70a"), // dlc.rpf/x64/data/lang/mexicandlc.rpf
    ShaUnpack("ea5ce9ac71c02511603e47847d9a324351f8f32ece873d28f975e4805cab59da"), // dlc.rpf/x64/data/lang/polishdlc.rpf
    ShaUnpack("60990b26a791ee64844a5b369902317702b8c2eed862a92bc732a73ac9007b32"), // dlc.rpf/x64/data/lang/portuguesedlc.rpf
    ShaUnpack("d17eccec37c9d9dbd5bcf03359721d46af9dcf59786848f9104db8d0e4cd3afe"), // dlc.rpf/x64/data/lang/russiandlc.rpf
    ShaUnpack("21c413e47f2b2fa747de02eecc3c7475e72f96576cd9a0bd912a6276867bb87f"), // dlc.rpf/x64/data/lang/spanishdlc.rpf
    ShaUnpack("b779347e70acb5453ecde2d77a3babb12d11860bb81fa6c31da2c2fc55d66d67"), // dlc.rpf/x64/levels/gta5/lodlights.rpf
    ShaUnpack("3cc419f288b4ce1ccd1e1ac561d707ce851f53e1b340dda8b612d73ad299c842"), // dlc.rpf/x64/levels/gta5/navmeshes.rpf
    ShaUnpack("d646f36d186d0247725cda1370d480ed5ca3b55cd7b4e8eac39df5c0924c3ed0"), // dlc.rpf/x64/levels/gta5/_citye/downtown_01/downtown.rpf
    ShaUnpack("7e99981b74a400ce7f85113784c87ca9d8f2bcc3b2855921bfd3fe9780845e5f"), // dlc.rpf/x64/levels/gta5/_citye/downtown_01/downtown_01_metadata.rpf
    ShaUnpack("a7c88cac5ff40a70dc9c2b0321be2b3f190847864dc678f31d1c8949c41366bf"), // dlc.rpf/x64/levels/gta5/_citye/downtown_01/dt1_03.rpf
    ShaUnpack("1edceb2d0948da35c426d7cc502c95ec48363b7573c552ec1565226baf900517"), // dlc.rpf/x64/levels/gta5/_citye/downtown_01/dt1_08.rpf
    ShaUnpack("fcd0f1ea3b44b4a2a50983db621911e29000fa5039ac7975d1521d8df113d34d"), // dlc.rpf/x64/levels/gta5/_citye/downtown_01/dt1_12.rpf
    ShaUnpack("df08accaba5cec652d7a073305d6bb3e3b7b43bc5b8fea3102f4700e0b7cbac0"), // dlc.rpf/x64/levels/gta5/_citye/downtown_01/dt1_19.rpf
    ShaUnpack("f53d21631590c0d2a3f4458cc6ac4a51b3adcc302d05716a879f1c543c3140c0"), // dlc.rpf/x64/levels/gta5/_citye/downtown_01/dt1_20.rpf
    ShaUnpack("474bb0231197c392ffbd3e1be6f9c8bd3e8243ff518821b42f842727fd7fb570"), // dlc.rpf/x64/levels/gta5/_citye/downtown_01/dt1_21.rpf
    ShaUnpack("0fef318b3fc0101634d96f1e68a0bc4158231ad73fc8c27b45be5c718acbb867"), // dlc.rpf/x64/levels/gta5/_citye/downtown_01/dt1_22.rpf
    ShaUnpack("7106c5dc8459368615f429f0827f1703ce4c0890442852f2534875d127957c80"), // dlc.rpf/x64/levels/gta5/_citye/downtown_01/dt1_23.rpf
    ShaUnpack("a3d15f1a4b85d71be89dac8c0824109df6190fa82a56d80bb422a6d0b44ae654"), // dlc.rpf/x64/levels/gta5/_citye/downtown_01/dt1_carrier.rpf
    ShaUnpack("400dfe0097097312e4f0483c8aa0bebe1a654179674d354eb4ffd73cca27dd5b"), // dlc.rpf/x64/levels/gta5/_citye/downtown_01/dt1_carrier_metadata.rpf
    ShaUnpack("6810f04acc11bd5b0217ef151c1ed065f1a26274920e946cfff4fead5862841e"), // dlc.rpf/x64/levels/gta5/_citye/downtown_01/dt1_carrmp.rpf
    ShaUnpack("6ae8febbf069a8f47678aec5f7d4b23a01ac5b02fa52042874e2789aa1022e9d"), // dlc.rpf/x64/levels/gta5/_citye/downtown_01/dt1_carrmp_lod.rpf
    ShaUnpack("ae39e4a4286b3b271be4420b8bd6afa2f271a1f1275375d9008a1e1e2236cf89"), // dlc.rpf/x64/levels/gta5/_citye/downtown_01/dt1_occl.rpf
    ShaUnpack("e2d41e0f3cc66ab998525aacbeffa85a640fbd52c11291e7d29cbbf88052c03d"), // dlc.rpf/x64/levels/gta5/_citye/downtown_01/dt1_tcmods.rpf
    ShaUnpack("f7f8b9a9db8134838647784fd946050374995492235cb2e2521038197c3a804b"), // dlc.rpf/x64/levels/gta5/_citye/downtown_01/dt1_tcmods_metadata.rpf
    ShaUnpack("c3f2bb7b7418c69c27b573b362530b97a88685831fb16fba630c019a26176dc3"), // dlc.rpf/x64/levels/gta5/_citye/downtown_01/fwy_02.rpf
    ShaUnpack("0f1e997b3a2b758767abc91db51a41e3e46ed11cd5e2763cd82589f3b01c68c7"), // dlc.rpf/x64/levels/gta5/_citye/hollywood_01/hollywood_metadata.rpf
    ShaUnpack("2cc5356c23dca233e2d329ae923831a401fed3e93cb84a971722ac16974b5943"), // dlc.rpf/x64/levels/gta5/_citye/hollywood_01/hw1_02.rpf
    ShaUnpack("ea8a9dff669f53e82037b47dc3df631924010389039fb9744fa6d0bf752f0c58"), // dlc.rpf/x64/levels/gta5/_citye/hollywood_01/hw1_06.rpf
    ShaUnpack("68a9d354774ff3c118106a9892eaeca25ed8b3089b4b3ef6fe1213e5e88f5c4e"), // dlc.rpf/x64/levels/gta5/_citye/hollywood_01/hw1_07.rpf
    ShaUnpack("9b5cce3a00e292e23ba04f23db124820a3614267a218ad75b4db07d0a1b5bd9d"), // dlc.rpf/x64/levels/gta5/_citye/hollywood_01/hw1_08.rpf
    ShaUnpack("f4871e7d735435676321a546beb18cac59a2d9f1a1c6826e39cd0df3a564d02a"), // dlc.rpf/x64/levels/gta5/_citye/hollywood_01/hw1_13.rpf
    ShaUnpack("d1e64983a712c6986ce662d01ad036077a7877baa14ade3fbe2fe50b8c3185f5"), // dlc.rpf/x64/levels/gta5/_citye/hollywood_01/hw1_14.rpf
    ShaUnpack("a3a717f76e8a7a209860c2f869398c8a7db8b6e467a119e12de51595cf02007d"), // dlc.rpf/x64/levels/gta5/_citye/hollywood_01/hw1_24.rpf
    ShaUnpack("6f9daab8a9a9fe05984ac0dbdcd41e2360e24573330efdbffe88610fcb46e8ce"), // dlc.rpf/x64/levels/gta5/_citye/hollywood_01/hw1_blimp.rpf
    ShaUnpack("2b1b3edeba5c96f887a6be12d3e338a87beec79005f1d57f8e0b5a4624e2c686"), // dlc.rpf/x64/levels/gta5/_citye/hollywood_01/hw1_rd.rpf
    ShaUnpack("1059bac60e9fb139d16da1896546e94d0eb0a8c8025d9e5c250214744c579691"), // dlc.rpf/x64/levels/gta5/_citye/indust_01/id1_06.rpf
    ShaUnpack("b9a15aa831cb9948e1e3c9ff8c9f2294961b47b74c1564d37f2231a04e33f01f"), // dlc.rpf/x64/levels/gta5/_citye/indust_01/id1_07.rpf
    ShaUnpack("07c0045518b8d63b554ddb993a3239b57873339d5299a3f673e356e7fa4ed008"), // dlc.rpf/x64/levels/gta5/_citye/indust_01/id1_occl.rpf
    ShaUnpack("997069db9951afe7f21c2098bae51d447aae5e552817c182229b73e2c4d44cc4"), // dlc.rpf/x64/levels/gta5/_citye/indust_01/indust_01.rpf
    ShaUnpack("7b468b43e1ad0fa75841c9d736ec569a869678e1ff487dbe010fd7c45ac33b41"), // dlc.rpf/x64/levels/gta5/_citye/indust_01/indust_01_metadata.rpf
    ShaUnpack("2d6028a1ffcf5c2edecf81ebbdbb60a5466cd72d397fc35909c1afa9ef7f02be"), // dlc.rpf/x64/levels/gta5/_citye/indust_02/id2_06.rpf
    ShaUnpack("14c40497f027d4023266f964ed2fdb1aab809ba7e80cdd22c939c4dad1952b88"), // dlc.rpf/x64/levels/gta5/_citye/indust_02/id2_14.rpf
    ShaUnpack("145c663f9f2c4987606195c0fa1f98779178cbeaf54465ec43ac02990a738f83"), // dlc.rpf/x64/levels/gta5/_citye/indust_02/id2_lod.rpf
    ShaUnpack("9597040ccc006d0bb91d8fa2c63a3602605a8ac54fa38be96c5aa7f8618648ec"), // dlc.rpf/x64/levels/gta5/_citye/indust_02/id2_occl.rpf
    ShaUnpack("84e18ec8b4760d0638a8ea89727e5ceb24b56d80da8436d59886b69f15d8fa00"), // dlc.rpf/x64/levels/gta5/_citye/indust_02/indust_02.rpf
    ShaUnpack("17613815d6fe5cb3b0b83611b82e4b44f89873446f22c85bb6453c3a7429cd75"), // dlc.rpf/x64/levels/gta5/_citye/indust_02/indust_02_metadata.rpf
    ShaUnpack("9edff7b93d0252722eff4d48cc97c83ad4282535874bb80150a31f82417cd89d"), // dlc.rpf/x64/levels/gta5/_citye/port_01/po1_01.rpf
    ShaUnpack("49be83bb6825070c3d29ac7dfcdb97ad0120c30992085a089193462d0c9987e8"), // dlc.rpf/x64/levels/gta5/_citye/port_01/po1_occl.rpf
    ShaUnpack("8909389a0c84d7eed28d727c1ecb25f5b76e675881f034c8557887ad8af1da04"), // dlc.rpf/x64/levels/gta5/_citye/port_01/po1_sh2.rpf
    ShaUnpack("e8be0b9315e0a910b44ccc33d631341deb69801a4a4e87fc6f5e407ab0506f4c"), // dlc.rpf/x64/levels/gta5/_citye/port_01/port_metadata.rpf
    ShaUnpack("93c85eadf39d85fb9535d30361e8b0eef5dd4ae29548f0ada17397a633400ff7"), // dlc.rpf/x64/levels/gta5/_citye/scentral_01/sc1_00b.rpf
    ShaUnpack("698259569495f15925509acadae368815c40c29dbb4bfbb3e40d3a2a31f1a70d"), // dlc.rpf/x64/levels/gta5/_citye/scentral_01/sc1_11.rpf
    ShaUnpack("2b8814a47ba708a03d6b54ed133a4c111451cbf0b4e1dc3065d86f932698b7a4"), // dlc.rpf/x64/levels/gta5/_citye/scentral_01/sc1_12.rpf
    ShaUnpack("eb585668543f09707ec9ffc04965484adffdd5084624d8f229931370c6b1e8a1"), // dlc.rpf/x64/levels/gta5/_citye/scentral_01/sc1_23.rpf
    ShaUnpack("230d8250e03b91b8d8840a38c62fc5af0aaa353a5b190babfcd7d449626ea531"), // dlc.rpf/x64/levels/gta5/_citye/scentral_01/sc1_occl.rpf
    ShaUnpack("2b60d3f4c787e34ae80c4bd27ecafc5c83c2802fa4a756f9175f62a3fd4337d1"), // dlc.rpf/x64/levels/gta5/_citye/scentral_01/sc1_rd.rpf
    ShaUnpack("46cad447fcdcbb8b0a5d0f49fc71a5e9f3a89c53ea0f0b53ccf931fe7a4fc73f"), // dlc.rpf/x64/levels/gta5/_citye/scentral_01/scentral.rpf
    ShaUnpack("0ae012a45966da959fda47521eca76aad34e516e5dca32ff1f20b29ae88b1ead"), // dlc.rpf/x64/levels/gta5/_citye/scentral_01/scentral_metadata.rpf
    ShaUnpack("d125856fc1f3403c2a0d32167c3b5b5f4fd384d9c7980298c86c7399e38150b4"), // dlc.rpf/x64/levels/gta5/_citye/sunset/bt1_05.rpf
    ShaUnpack("3151c636bab6f1b30ba66f7f7c45df5200c5f20fddd5a5728ae004e6e05af5d6"), // dlc.rpf/x64/levels/gta5/_citye/sunset/ss1_02.rpf
    ShaUnpack("bbf8aea5af362dcd711ebb1945fa00a5af584ba5e8d233db58c70e4c74f71634"), // dlc.rpf/x64/levels/gta5/_citye/sunset/ss1_05.rpf
    ShaUnpack("6916403ef5185daa6df863f7c5e2cba4d174441b63deb58f4b1ce58f6e0aa590"), // dlc.rpf/x64/levels/gta5/_citye/sunset/ss1_11.rpf
    ShaUnpack("76a0ee0461642d41b9017e947643218e9c640c43781614dd4a5a6f17d936af73"), // dlc.rpf/x64/levels/gta5/_citye/sunset/ss1_occl.rpf
    ShaUnpack("a0de90db6d334b4043a327648dc5417814b9e8dfe96ea5ffbc34a653711e73c4"), // dlc.rpf/x64/levels/gta5/_citye/sunset/sunset.rpf
    ShaUnpack("0f7642a3ff0a792485e5dc343ba3f8d5fb6150b3660796ba150bbb68ef86468e"), // dlc.rpf/x64/levels/gta5/_citye/sunset/sunset_metadata.rpf
    ShaUnpack("64f447154d08c17fd34ff120fe086a2ef1ceffd835576866d05feb8faa221e5c"), // dlc.rpf/x64/levels/gta5/_cityw/airport_01/airport.rpf
    ShaUnpack("c9a4afaf96e297272767d18400b0a201a2521a0177fe4b8bd081307fd5a64916"), // dlc.rpf/x64/levels/gta5/_cityw/airport_01/airport_metadata.rpf
    ShaUnpack("6ddf74edb35fb013898f1deda7a308742babb5793ec3b853e6f6f9dd3db8b0ae"), // dlc.rpf/x64/levels/gta5/_cityw/airport_01/ap1_01_a.rpf
    ShaUnpack("a34c73e85a66f044403a342e954abf618d39945e7646a2b0d94fbb74cdf9341b"), // dlc.rpf/x64/levels/gta5/_cityw/airport_01/ap1_01_c.rpf
    ShaUnpack("7d39982e56accf194afc3727f84c51910a67c70bdba4bd2a87d34fd9e78301d1"), // dlc.rpf/x64/levels/gta5/_cityw/airport_01/ap1_03.rpf
    ShaUnpack("2dcd53bee74e4188ddcdf932a9a98f2e3fdb44fb39260b8a3b63bcea04a363cc"), // dlc.rpf/x64/levels/gta5/_cityw/airport_01/ap1_04.rpf
    ShaUnpack("6a2f40f7e3a08fc05fb0d4f3889206730c8c0d60e6c31d947441b60aff58c0b7"), // dlc.rpf/x64/levels/gta5/_cityw/airport_01/ap1_lod.rpf
    ShaUnpack("074241728bfb4ead6b28fc65349ff6fd459fde2125b2c8b26556f95cde918dbd"), // dlc.rpf/x64/levels/gta5/_cityw/beverly_01/beverly.rpf
    ShaUnpack("ec23cc4683330b4a0b52d8f3dc021ae371cb928583a1aefd39d990960b527a75"), // dlc.rpf/x64/levels/gta5/_cityw/beverly_01/beverly_metadata.rpf
    ShaUnpack("5fd338a392064db8addf3c1c1a392260e9b2f01cf8b0f5823bf4ab767304c5e1"), // dlc.rpf/x64/levels/gta5/_cityw/beverly_01/bh1_03.rpf
    ShaUnpack("ab9f6205ab8571fd152a471287c5394163575f78a05e06c92bb7743dfd856f21"), // dlc.rpf/x64/levels/gta5/_cityw/beverly_01/bh1_04.rpf
    ShaUnpack("00862de45ecd25ac4427a08d525fa680532bef16a428d18dd5a6c8b2a470ef58"), // dlc.rpf/x64/levels/gta5/_cityw/beverly_01/bh1_08.rpf
    ShaUnpack("6a8dd58e4c1c9d3b328b01997fd72b52335c0108be4ecc62b4dd87bbc0c28148"), // dlc.rpf/x64/levels/gta5/_cityw/beverly_01/bh1_09.rpf
    ShaUnpack("5b0078dae3c20e50b052b9bc4350fbb1a705a719b952af784b0db9b85fbefa76"), // dlc.rpf/x64/levels/gta5/_cityw/beverly_01/bh1_20.rpf
    ShaUnpack("135284a7dc8531d8badd9b2082459759087a3ef3c9c528bf6fbac649c468eea5"), // dlc.rpf/x64/levels/gta5/_cityw/beverly_01/bh1_22.rpf
    ShaUnpack("018e9155a3c98036980ecef9f47e3bfe919f66e180027abc98d6b67d9d1c7fdc"), // dlc.rpf/x64/levels/gta5/_cityw/beverly_01/bh1_40.rpf
    ShaUnpack("41c4d62fd8820e028aa5c6db1fa700bb694003e44699db39384c16447681fa32"), // dlc.rpf/x64/levels/gta5/_cityw/beverly_01/bh1_lod.rpf
    ShaUnpack("f9699d5e8a2c9a6d78ac96728ef8990b486566bb2906e3172612412caf6384ca"), // dlc.rpf/x64/levels/gta5/_cityw/koreatown_01/koreatown_metadata.rpf
    ShaUnpack("3e333775bb9b8a804694326ae1ed1e82f1c6180ddff048fe98b5d7e22be462a0"), // dlc.rpf/x64/levels/gta5/_cityw/koreatown_01/kt1_04.rpf
    ShaUnpack("1e248eb4e9988974447a9858133c2eded3f32fad69998d77b5e30d89181a35b4"), // dlc.rpf/x64/levels/gta5/_cityw/koreatown_01/kt1_11.rpf
    ShaUnpack("508676815f0af97f356c9b871ca59ac34736bedde5416e9837050d881eeb8b12"), // dlc.rpf/x64/levels/gta5/_cityw/santamon_01/santamon_metadata.rpf
    ShaUnpack("708179204aa19dc76442afe01361a7ece2bc86cc20f04e4ce410f15a6ab44d7b"), // dlc.rpf/x64/levels/gta5/_cityw/santamon_01/sm_14.rpf
    ShaUnpack("7fef274169da1b4e275b967efcc412808e305017d38bf4469aa0e792fd25cafd"), // dlc.rpf/x64/levels/gta5/_cityw/santamon_01/sm_16.rpf
    ShaUnpack("5693e4ffa81b563e1a9aaba76af37c200f155f2a8358e82bfaeb462bebbaa0fe"), // dlc.rpf/x64/levels/gta5/_cityw/santamon_01/sm_18.rpf
    ShaUnpack("333a3d75a3cd7a974337d9a01e43cbb1a492d52193e08341df209ece5b842d4b"), // dlc.rpf/x64/levels/gta5/_cityw/santamon_01/sm_22.rpf
    ShaUnpack("d27c5bec15c0b71ba9d69c41c2ee7c87050c0b15bfe4ef1e8b85966c84c31b6a"), // dlc.rpf/x64/levels/gta5/_cityw/santamon_01/sm_24.rpf
    ShaUnpack("ce1cd0e37cdebdf4658bad6f724e43e7c5cfd0f0c8a7ffe8bce228b025359a61"), // dlc.rpf/x64/levels/gta5/_cityw/santamon_01/sm_occl.rpf
    ShaUnpack("765035d454d1bf7e1a32c7dab7c944e5e79bd10d33d264bd3d1593258134115f"), // dlc.rpf/x64/levels/gta5/_cityw/venice_01/vb_19.rpf
    ShaUnpack("db52fbe2dad99739d62b1b78a35066a2fabf99fde87e057530d18311918fceee"), // dlc.rpf/x64/levels/gta5/_cityw/venice_01/vb_34.rpf
    ShaUnpack("34adeb4c8be41a1acc3b702da162f7f105c0bf6dfc6495a5dcf8924c9879802f"), // dlc.rpf/x64/levels/gta5/_cityw/venice_01/vb_44.rpf
    ShaUnpack("8de5d1766071000b71cac82a10924103be62d934a2ca785c84f8afde15d21998"), // dlc.rpf/x64/levels/gta5/_cityw/venice_01/vb_rv.rpf
    ShaUnpack("ef333f4e1d9a6a8a316969d5299a5ceb163b3af566f1393327cc7e7ad54a3e41"), // dlc.rpf/x64/levels/gta5/_cityw/venice_01/venice.rpf
    ShaUnpack("5dc16fed1520999bedadc55a265442bc12ba0fbdd67aaffc326c72fa27c364ad"), // dlc.rpf/x64/levels/gta5/_cityw/venice_01/venice_metadata.rpf
    ShaUnpack("5b8f6c465dcd2e97a910fb361aff49987111faa61238c2acdeeeb596c7974267"), // dlc.rpf/x64/levels/gta5/_hills/cityhills_01/ch1_01.rpf
    ShaUnpack("3512f297580ef33d653a8c8eb9463d87afaf5679d10eacb3b5c6945da341206b"), // dlc.rpf/x64/levels/gta5/_hills/cityhills_01/ch1_02.rpf
    ShaUnpack("a55e2e54b99c360aca68f277a622eea3c8d5fd2987b09e0d918b99fd7411577e"), // dlc.rpf/x64/levels/gta5/_hills/cityhills_01/ch1_06e.rpf
    ShaUnpack("5c3692def7911db050e4cc36607c709adacf0290a21bf851c48f7968532d7440"), // dlc.rpf/x64/levels/gta5/_hills/cityhills_01/ch1_07.rpf
    ShaUnpack("7f485e1aaa75e5412a3a7226d80018a12a861c9d2719a09349ab28b4522688b5"), // dlc.rpf/x64/levels/gta5/_hills/cityhills_01/ch1_10.rpf
    ShaUnpack("f359c5c4924da5dca24e04ff42cfebf4395a62582d4e7d1a7e27881c947f04d1"), // dlc.rpf/x64/levels/gta5/_hills/cityhills_01/ch1_lod.rpf
    ShaUnpack("7325e38449b5d78cfe8ee33fbcbc8a21cead4832633435c03196f9ac5b29161c"), // dlc.rpf/x64/levels/gta5/_hills/cityhills_01/ch1_yacht.rpf
    ShaUnpack("30dc665fb68a830ff53485d79bc02650ebb4132d9f45da8cbd83ec85b4a18f20"), // dlc.rpf/x64/levels/gta5/_hills/cityhills_01/ch1_yacht_lod.rpf
    ShaUnpack("6013ab98c3ceb418c372e79e4cca00e514b211208a9b0d9aa2501f351412815d"), // dlc.rpf/x64/levels/gta5/_hills/cityhills_01/cityhills_01.rpf
    ShaUnpack("3dce8764d7ddfced50b302c4c08bb8b08a5cdaf4228eda68fdef0133a64fe598"), // dlc.rpf/x64/levels/gta5/_hills/cityhills_01/cityhills_01_metadata.rpf
    ShaUnpack("08184ad85fb1bc6b5ad39e57899f5e80b79638ff2280e6ac35d89329ef096264"), // dlc.rpf/x64/levels/gta5/_hills/cityhills_01/yacht.rpf
    ShaUnpack("1c8d244df45859c8e1be67dac964fb5983e172ddb69873dbf6fe6b427b352263"), // dlc.rpf/x64/levels/gta5/_hills/cityhills_01/yacht_metadata.rpf
    ShaUnpack("17904090398c2e2eb8e979b9aea3fb886e9c6ac58a0e3bbf68b735e3326d37b4"), // dlc.rpf/x64/levels/gta5/_hills/cityhills_02/ch2_04.rpf
    ShaUnpack("e21d5d47add472b6546b9d5e1e2fa1c9816bf0bf65782d0ee194f17061c45183"), // dlc.rpf/x64/levels/gta5/_hills/cityhills_02/ch2_07b.rpf
    ShaUnpack("d04e8ee898f66728fbb8780982efe4f79ddf9cf621639f6cacb8e4db5df94bef"), // dlc.rpf/x64/levels/gta5/_hills/cityhills_02/ch2_08.rpf
    ShaUnpack("3a3f4dafba973b33606be406574b389787b0d4de45909944d07dfd7494644274"), // dlc.rpf/x64/levels/gta5/_hills/cityhills_02/ch2_09b.rpf
    ShaUnpack("3efed78b4ab8b93548a04e8c577b75022fd243016be465fd2b0ad23bd792e7dc"), // dlc.rpf/x64/levels/gta5/_hills/cityhills_02/ch2_09c.rpf
    ShaUnpack("5f7345efba232fe6b1936ac483abb5e2f11f24d0ba2f4ba622afea473390c08e"), // dlc.rpf/x64/levels/gta5/_hills/cityhills_02/ch2_10.rpf
    ShaUnpack("0495477fe3b8f9d0eb216be473453fa061b4d7c0e1b6c72211b5b9ce4d493df2"), // dlc.rpf/x64/levels/gta5/_hills/cityhills_02/ch2_11.rpf
    ShaUnpack("5223f33fcfb8fa33b047e0d612501c5d6b4922cd7fbb227cc55a09d867657406"), // dlc.rpf/x64/levels/gta5/_hills/cityhills_02/ch2_roadsb.rpf
    ShaUnpack("d4e0f620e41e970f98aada91af566cf044f2ea33ed9bfe87546e0ba70070b318"), // dlc.rpf/x64/levels/gta5/_hills/cityhills_02/cityhills_02.rpf
    ShaUnpack("2dd91f209d6678befa05d88617ce10d5c9fc937c1419cf34bac0f2918699cc3f"), // dlc.rpf/x64/levels/gta5/_hills/cityhills_02/cityhills_02_metadata.rpf
    ShaUnpack("a2814d179c6247278116d6ef48c3ceca1059a4032a396b1297a75bf3cdb6dbf6"), // dlc.rpf/x64/levels/gta5/_hills/cityhills_03/ch3_04.rpf
    ShaUnpack("afdb29e70c03e9ac6007a6788a71d99593b76a3e339bf4cf58dbfdf819c1d439"), // dlc.rpf/x64/levels/gta5/_hills/cityhills_03/ch3_08.rpf
    ShaUnpack("9ace30eb4bc6363b3a1f76e01e7c00ae68b8357e2a587c0d281448b006ba955d"), // dlc.rpf/x64/levels/gta5/_hills/cityhills_03/ch3_rd1a.rpf
    ShaUnpack("f11484f1d205e5d009f21df78b24aa884f41de4680972367cc7d0a4e07c6f792"), // dlc.rpf/x64/levels/gta5/_hills/cityhills_03/cityhills_03_metadata.rpf
    ShaUnpack("62c66b497bb73d6e0e77368577cce1d6eec1e3adf1a1849ddfb3e37516271b65"), // dlc.rpf/x64/levels/gta5/_hills/country_01/country_01.rpf
    ShaUnpack("177ac7eb2e32d65f3448044d9b72abe2865aa301d9a1ee8d3c9eb533a4f122c7"), // dlc.rpf/x64/levels/gta5/_hills/country_01/country_01_metadata.rpf
    ShaUnpack("ca76c25c5f3ca09539930de18ee97e8d61364089e8d08277bf04e9d46590d06e"), // dlc.rpf/x64/levels/gta5/_hills/country_01/cs1_02.rpf
    ShaUnpack("b13ec79f785a5fd008567f7547b17af3823063fc3b346eed068997524f2a4966"), // dlc.rpf/x64/levels/gta5/_hills/country_01/cs1_08.rpf
    ShaUnpack("6f197d4d4a2d113de8697b19637402e5eaa3abc88c8dce4ea97541a9678bd58c"), // dlc.rpf/x64/levels/gta5/_hills/country_01/cs1_10.rpf
    ShaUnpack("34f6b4762ca359ac9d74b7998981986ecd810a63044a2acce18608c956f414cd"), // dlc.rpf/x64/levels/gta5/_hills/country_01/cs1_11.rpf
    ShaUnpack("668137819e3de87b5d706488327d85789392abd0c1c1431a711e92e99e0eaf2c"), // dlc.rpf/x64/levels/gta5/_hills/country_01/cs1_11b.rpf
    ShaUnpack("2cbf9374cfa9aeffa0067ae02a7c8e2975e3b96a4864a693bcd3628db7e262db"), // dlc.rpf/x64/levels/gta5/_hills/country_01/cs1_12.rpf
    ShaUnpack("9ab22a87553bc14d85efd00898484ee929ab0140e8de5aa8cabd12342f651196"), // dlc.rpf/x64/levels/gta5/_hills/country_01/cs1_14b.rpf
    ShaUnpack("83ab960b3dcea3f92308418fee9c21b62e03d17d72db38a9f78b70e18dc554a0"), // dlc.rpf/x64/levels/gta5/_hills/country_01/cs1_15c.rpf
    ShaUnpack("dc93ef3f33f6e82b9aa6f7d4788da1c72b8de44ce3fa11fea2d9e60c1f547dd1"), // dlc.rpf/x64/levels/gta5/_hills/country_01/cs1_instance_placement.rpf
    ShaUnpack("652c7e9592f80d6f7660340e09aaa3f90e6cbec70aa51ee7599a4a3f7d8f9ab4"), // dlc.rpf/x64/levels/gta5/_hills/country_01/cs1_lod2.rpf
    ShaUnpack("41abd94c9948be99b8021af56caddb99133bc418a538988ac25ed870523d5203"), // dlc.rpf/x64/levels/gta5/_hills/country_01/cs1_roadsa.rpf
    ShaUnpack("1cd9bf80b13922ca44a1f2a47ee11228924026c0e4af3fa4a88f0507ba50efe9"), // dlc.rpf/x64/levels/gta5/_hills/country_02/country_02.rpf
    ShaUnpack("a823f7f641981a163df57987a92e95705fead765be1bad83eecdd0b5ebd51e47"), // dlc.rpf/x64/levels/gta5/_hills/country_02/country_02_metadata.rpf
    ShaUnpack("3e91577a8b875fecc0c6d1d8b5e3c20f4f14d611a9e8cc328924b3c0bb313003"), // dlc.rpf/x64/levels/gta5/_hills/country_02/cs2_05.rpf
    ShaUnpack("f67515016e48ec54fd9ff17aa39d1cc54e9a7c4abd1446634a03afd92a3052aa"), // dlc.rpf/x64/levels/gta5/_hills/country_02/cs2_08.rpf
    ShaUnpack("abbdd4e431f2a17cdd0903f5f5e68168b876af12edc7d04b017cd683f63a88de"), // dlc.rpf/x64/levels/gta5/_hills/country_02/cs2_29.rpf
    ShaUnpack("a5b3b90eb069507af520337f715a568616e1110f434a168b4ba6ac1cdb86b35e"), // dlc.rpf/x64/levels/gta5/_hills/country_02/cs2_instance_placement.rpf
    ShaUnpack("2002a4aa52798896df4bbace2ae93d7cfe84bc89b8dfc0e56aea1f4217c6f01c"), // dlc.rpf/x64/levels/gta5/_hills/country_03/country_03.rpf
    ShaUnpack("be5833568d0f05c2847933a106225b86a8c2bf38960c7e4e75d1079f5a35727f"), // dlc.rpf/x64/levels/gta5/_hills/country_03/country_03_metadata.rpf
    ShaUnpack("d97ef3d3d1c61df1c80a1a7ab0d97d905653499dd1c97ecb13ef6bd7a9d59dd4"), // dlc.rpf/x64/levels/gta5/_hills/country_03/cs3_04.rpf
    ShaUnpack("f7623e357f0ffa698eb23f29d69c2ff7070e07f2ae9e8371f7e8808585ab50a2"), // dlc.rpf/x64/levels/gta5/_hills/country_03/cs3_07.rpf
    ShaUnpack("15ee255180d841ddd978a8709ddd874022bfce33ac98ef8e3bc924a9360ce03f"), // dlc.rpf/x64/levels/gta5/_hills/country_03/cs3_occl.rpf
    ShaUnpack("ea1b965c06228b5fb10957dd76be239273ed0fbe0de61d92847e5053820283c3"), // dlc.rpf/x64/levels/gta5/_hills/country_04/country_04.rpf
    ShaUnpack("f761a19724f26f46fb83b5139600bf75c44522d7250834fe3d9035ec0508ffb0"), // dlc.rpf/x64/levels/gta5/_hills/country_04/country_04_metadata.rpf
    ShaUnpack("fe5d9f16d74e9b8f65fab3fac1b1eebb5088176314db9a424e1540d2b1cb171e"), // dlc.rpf/x64/levels/gta5/_hills/country_04/cs4_02.rpf
    ShaUnpack("3d62b200cd9670a30a6f6e6bb9fafd9fd552d92ab3f33d6841729a663bfd24f6"), // dlc.rpf/x64/levels/gta5/_hills/country_04/cs4_06.rpf
    ShaUnpack("a6a904a6dd2cfd637078dc1f09e987bc743ac257e8a4b36af409e33b9ba44c29"), // dlc.rpf/x64/levels/gta5/_hills/country_04/cs4_10.rpf
    ShaUnpack("10711454460b5318e7f58485ae07dc9d38523dab2ca397c7082e94bd3bc7a5ec"), // dlc.rpf/x64/levels/gta5/_hills/country_06/country_06.rpf
    ShaUnpack("71951a1d19dca7ce5d7c39e50607276314871fb1ae926726c2e84acf088866d9"), // dlc.rpf/x64/levels/gta5/_hills/country_06/country_06_metadata.rpf
    ShaUnpack("231e18b4dd9674e137d65338cc0324e93444332173d4928f4c3ccfa88f87bbd5"), // dlc.rpf/x64/levels/gta5/_hills/country_06/cs6_03.rpf
    ShaUnpack("f7386718c39422fb81ce988ac9117f904c508ecb065d98107a3308a492f9b601"), // dlc.rpf/x64/levels/gta5/_hills/country_06/cs6_04.rpf
    ShaUnpack("9575625208984a8f8c9c512e8943f55989184ae7eee239ed2083a393d9d39a0a"), // dlc.rpf/x64/levels/gta5/interiors/dlc_apart_high2_new.rpf
    ShaUnpack("75939878ff3becbd79ebdedd4c83b27e132cb2d76c96fdc4f8248707e2abf86a"), // dlc.rpf/x64/levels/gta5/interiors/dlc_apart_high_new.rpf
    ShaUnpack("2fad7d6587cd872cd72875009406e7763cf117f1ac19097b3ab405a7f576eb58"), // dlc.rpf/x64/levels/gta5/interiors/dlc_garage_high_new.rpf
    ShaUnpack("ee1219aac3750f58a4f345d550f365d187ff8b444aadb5f2f23a451428c6d3ef"), // dlc.rpf/x64/levels/gta5/interiors/dlc_generic_bank.rpf
    ShaUnpack("4c6058720bc89c453ff8d10126cd526ecee1083368e3f556983e543b8a4dbb08"), // dlc.rpf/x64/levels/gta5/interiors/dlc_heist_police.rpf
    ShaUnpack("441e074e08c2ff9d2e211dacd0ab9763d6e25439f35d2b457ddba085f46a25fa"), // dlc.rpf/x64/levels/gta5/interiors/dlc_mpheist_carrier.rpf
    ShaUnpack("af79aa5cab3f954eda601dba061271f8d2ca7f38d097e04121e252b9fbb7fdd4"), // dlc.rpf/x64/levels/gta5/interiors/heist_ornate_bank.rpf
    ShaUnpack("1f018deea38f829ae511b911d61333dbd3054c6219bef9d507bd0fbe06eb4b3d"), // dlc.rpf/x64/levels/gta5/interiors/mpheist_int_placement.rpf
    ShaUnpack("8d4f29e82afd2e68759b4750b17762e3253eafc9a1350a9712b4ffbb506f2d18"), // dlc.rpf/x64/levels/gta5/interiors/mpheist_yacht.rpf
    ShaUnpack("9a30405ff20bd5ce71b87721fe7318245a67d76cd311f41a1bf707f1ed3a05a3"), // dlc.rpf/x64/levels/gta5/props/lev_des_mp_dlc.rpf
    ShaUnpack("113689c9adfaf8f491926d0bc0f9ad28fd1eace990a06f4c2ffd47e67fc5686e"), // dlc.rpf/x64/levels/gta5/props/mp_residential_heist.rpf
    ShaUnpack("0ba4923a2cbd3fa3386d82383425779aad32d80b4a3a10f544d15c7f4d48c36c"), // dlc.rpf/x64/levels/gta5/props/v_heist1.rpf
    ShaUnpack("098e6e217d2c785268a7aae4a992ae41f5009939ac180f77c8fbfe4cd46e1670"), // dlc.rpf/x64/levels/gta5/vehicles/mpheistvehicles.rpf
    ShaUnpack("08ad810e008a3268cc4f1a16af7c37a6878a22ad7f75d529235de025f21db43c"), // dlc.rpf/x64/levels/mpheist/vehiclemods/dubsta3_mods.rpf
    ShaUnpack("db96bf2c43e02a83c25935a225760e78eac73c175e414c9c1cbca7c779afd7a1"), // dlc.rpf/x64/levels/mpheist/vehiclemods/enduro_mods.rpf
    ShaUnpack("329839dacdc97841279128320234771d192eafd8208a25f3ce7f152675e28b28"), // dlc.rpf/x64/levels/mpheist/vehiclemods/gburrito2_mods.rpf
    ShaUnpack("a0c0af857c219cbf20d59cbeca17e38bbfb640ecee35d869907e7a7e55972733"), // dlc.rpf/x64/levels/mpheist/vehiclemods/guardian_mods.rpf
    ShaUnpack("c6497885bf676a6219ab802eafaf0e309a8022ce1400b116b15ed0759bf56e3e"), // dlc.rpf/x64/levels/mpheist/vehiclemods/kuruma_mods.rpf
    ShaUnpack("5d625bac75952968498cc78d44c9e498dc069522333f09bd44aa93b55c5a062a"), // dlc.rpf/x64/levels/mpheist/vehiclemods/wheels2_mods.rpf
    ShaUnpack("03c26e214fb1392450a8ce631aa11d61b51b352b80b2d561dc6329e3b8bdaa70"), // dlc.rpf/x64/models/cdimages/mpheist_componentpeds.rpf
    ShaUnpack("b0bc0f7309c99ac89aa31d4210444003366ca5667bbc562da0c812bace0cfdbc"), // dlc.rpf/x64/models/cdimages/mpheist_componentpeds_p.rpf
    ShaUnpack("457e42d0030110b3c65af73e2fd0e98ebe54fa45f17c438958ecc1df6bd87ccb"), // dlc.rpf/x64/models/cdimages/mpheist_cutspeds.rpf
    ShaUnpack("9dc2a38d2a24892c0b7aa8b20cff1e5f3f6f3676b4c8dcb1675cf8308a85fce5"), // dlc.rpf/x64/models/cdimages/mpheist_ped_mp_overlay_txds.rpf
    ShaUnpack("f709ef2997d62f5e107d9d41b28effaf46656aaf8c956704bc55e58a4fd2489d"), // dlc.rpf/x64/models/cdimages/mpheist_streamedpeds.rpf
    ShaUnpack("5befe33f3fdcbda4a00c5111408c1444a18ea9193e7968d720c519974523db4b"), // dlc.rpf/x64/models/cdimages/mpheist_streamedpeds_p.rpf
    ShaUnpack("0569ef136fab68c6be213e2cf6a4f9253e861532881412cae66b4067807e7252"), // dlc.rpf/x64/models/cdimages/trevor_outfits.rpf
    ShaUnpack("5e844192def11e43028fe6097e0eaf43d2d64907aab6e6f688721fce157adcec"), // dlc.rpf/x64/models/cdimages/weapons.rpf
    ShaUnpack("b6d75111f9071e9238e9b0d3d0d3d361fc2dd19cb3eeee26e564f8a48bf9e6d9"), // dlc.rpf/x64/textures/script_txds.rpf
    // update/x64/dlcpacks/mpheist3/dlc.rpf
    ShaUnpack("1390b431e2089babe74e8667751c4c5d8acbb15aa90fdbb4b618ab536d82dec2"), // dlc.rpf
    ShaUnpack("30896c7fb81e23c1cdc2686fdb755f86fccebf56040e4630e6d9cfb78b486fb4"), // dlc.rpf/x64/anim/creaturemetadata.rpf
    ShaUnpack("e9a4189ae70f03a41e53b360a96f66e46ab872935721651c674e71afb99f0675"), // dlc.rpf/x64/anim/expressions.rpf
    ShaUnpack("18494711b6a84212c26fa11a9ad5c396e3723123f1a85965c474bf6346dc3860"), // dlc.rpf/x64/anim/cutscene/cuts_hs3.rpf
    ShaUnpack("033a0d8e1925898b246d98dac4c73d6e837836a7370ae4912e4c009f0176ada2"), // dlc.rpf/x64/anim/cutscene/cuts_hs3f.rpf
    ShaUnpack("4468874f6ed4455efd56d76aabd763170d6e0ce31871b565586711edebc0dfd9"), // dlc.rpf/x64/anim/ingame/clip_anim@.rpf
    ShaUnpack("4d620075d15a197ce42da6b050a937b7212f4f3c744dda10160eb002dc5d86cc"), // dlc.rpf/x64/anim/ingame/clip_anim_casino_a@.rpf
    ShaUnpack("f956e25b435c7ca08738b22f364055f71d667287941dc5896f732f94c740e59a"), // dlc.rpf/x64/anim/ingame/clip_anim_casino_b@.rpf
    ShaUnpack("ee2b1b13e04c4e7b4dbc20574d101867f76474d2c2ad39f48870691c43a52601"), // dlc.rpf/x64/anim/ingame/clip_anim_heist@.rpf
    ShaUnpack("f8fb04b51f5573bc65a5b9743ece31f724044cb57709ffdc691f491443570d61"), // dlc.rpf/x64/audio/occlusion.rpf
    ShaUnpack("b43c8368bb7ca14ffd885db9c7677e3a6d41412a33e81deb39628f53966e85c8"), // dlc.rpf/x64/data/effects/ptfx.rpf
    ShaUnpack("ce24f139ad15421690aa0b9aea3d98e6b2b5d627f4423664eca90f8a45b7a0a7"), // dlc.rpf/x64/data/lang/americandlc.rpf
    ShaUnpack("3f85bfc5b96185f1756c5d7c8aa44ca1392f83e80f08ab48697c6bc7b74368d0"), // dlc.rpf/x64/data/lang/chinesedlc.rpf
    ShaUnpack("7a174ac25ae0346a49e1f5bd98ef9c4f1f4bc6b0aefec3cc079df3bd736324df"), // dlc.rpf/x64/data/lang/chinesesimpdlc.rpf
    ShaUnpack("c944ce203882e172b01eea0cc4e6891eb016beec1762ea1e650766b1bec4bf4f"), // dlc.rpf/x64/data/lang/frenchdlc.rpf
    ShaUnpack("af76861f5fcea5c12519931a05f018063112f22354f3ac34e6468c9367c2b452"), // dlc.rpf/x64/data/lang/germandlc.rpf
    ShaUnpack("b9231c02b9c3eec8a2805b09e6ae2ca4383cff0a887ceb4625a7f7fac0065ca2"), // dlc.rpf/x64/data/lang/italiandlc.rpf
    ShaUnpack("91df0b51136f3b4554223f78cc6e51323391d3307c1d5599de0868de11d43b2d"), // dlc.rpf/x64/data/lang/japanesedlc.rpf
    ShaUnpack("8244e8b0073bb0ff6e8056be469f811ba831f97f33c2bc9389c0ab73f27e9d72"), // dlc.rpf/x64/data/lang/koreandlc.rpf
    ShaUnpack("b06727763749aca87a86df0f23bada7ba495f5070da80db5532ff2111f29e03f"), // dlc.rpf/x64/data/lang/mexicandlc.rpf
    ShaUnpack("65d709f36e946ceffbc933258a1b298333cf4ffe0ee600bd55dc358c5c12f390"), // dlc.rpf/x64/data/lang/polishdlc.rpf
    ShaUnpack("06b993e3be92c31a42bbb48ee9cd624a139271ff4b849a979a8a38456e7f1fc0"), // dlc.rpf/x64/data/lang/portuguesedlc.rpf
    ShaUnpack("3538ee534dfc3a55fe64c89e05bca66e85b6b4a62ecd935216dc178b3304458e"), // dlc.rpf/x64/data/lang/russiandlc.rpf
    ShaUnpack("a2308339cd94882ee032c65308e9a9dce3be5858404e9e4f3971af0b2879802e"), // dlc.rpf/x64/data/lang/spanishdlc.rpf
    ShaUnpack("8237b2f66b11acf9ccf614357d54d763d84428440ab9b6de0ac31b615d08338a"), // dlc.rpf/x64/levels/gta5/navmeshes.rpf
    ShaUnpack("248c1c97b435ea8d5391052b36eb76a74721940c880066b3d55d0118919777c9"), // dlc.rpf/x64/levels/gta5/_cityw/additions_01/mph3_add.rpf
    ShaUnpack("30ffde27ae2bacb2a09a8176354221b29703d4bf5041a179650db987001b0a26"), // dlc.rpf/x64/levels/gta5/_cityw/additions_01/mph3_add_metadata.rpf
    ShaUnpack("37855d588c36bebfbe32fb50d4baaa34d866b83e010f0209b496a3d829eb9091"), // dlc.rpf/x64/levels/gta5/interiors/dlc_int_01_ch.rpf
    ShaUnpack("e7eee5893ea46c5449e9c4c432fba7ceacffbe4a033d4fdc08143783f61cbbe5"), // dlc.rpf/x64/levels/gta5/interiors/dlc_int_02_ch.rpf
    ShaUnpack("27226e2d0a13df19a7d9181f63a6ef9e9f19820f11b3a0e64906ff8379891794"), // dlc.rpf/x64/levels/gta5/interiors/dlc_int_03_ch.rpf
    ShaUnpack("5236b3fd7284028bf7f427c7e4f796d068156b17da5b2b951571e068d89995f4"), // dlc.rpf/x64/levels/gta5/interiors/dlc_int_04_ch.rpf
    ShaUnpack("f2730e1a648ff4e139e74b7208529c60b8cb40dfb6508f0a9a3e949b01eff368"), // dlc.rpf/x64/levels/gta5/interiors/dlc_int_06_ch.rpf
    ShaUnpack("9cc4b7d40eb873e86209c62c185c1e3a27fbb4c93338c52c5928431d6f68f0f9"), // dlc.rpf/x64/levels/gta5/interiors/dlc_int_07_ch.rpf
    ShaUnpack("0623d3549f8eded87660d0a24a406a31ce5fde56ecbf165a0ed3fbd2e46023a7"), // dlc.rpf/x64/levels/gta5/interiors/dlc_int_08_ch.rpf
    ShaUnpack("e14754f7b4e8352420b1e9ec0820968d57b0611256ff06f7e83e4932e8c2baf4"), // dlc.rpf/x64/levels/gta5/interiors/dlc_int_09_ch.rpf
    ShaUnpack("21cb2c909a7a8ce168f08eac793a0efdd82e559e14f64553aad7bfafd22484db"), // dlc.rpf/x64/levels/gta5/interiors/dlc_int_10_ch.rpf
    ShaUnpack("cedd9b1c7379ad6280a4d6cb9377dea5557d1af1138fbfc08c50e0c10d260407"), // dlc.rpf/x64/levels/gta5/interiors/dlc_int_11_ch.rpf
    ShaUnpack("673b3e70a5227267172d443b415cc682e88c437ee77ef6df64257bd89979196b"), // dlc.rpf/x64/levels/gta5/interiors/dlc_int_12_ch.rpf
    ShaUnpack("335dd182904b25125fa3363378891f5c36a9568f3d26dde76289fe1a33e8c926"), // dlc.rpf/x64/levels/gta5/interiors/int_placement_ch.rpf
    ShaUnpack("faf129c62d83dffe5bdaa87ab7935ff9e7d61b29e2d962722fe23c16fbd56e31"), // dlc.rpf/x64/levels/gta5/props/prop_ch_arcade_claw.rpf
    ShaUnpack("b30d939d8ed8a539a7cf3546ddeba1091b0461b7064608723bd34484a894af19"), // dlc.rpf/x64/levels/gta5/props/prop_ch_arcade_collect.rpf
    ShaUnpack("bacfe8f97b4e1dea2f63e9476df521fc9665e53adef2f8810f4e0255e6df43e7"), // dlc.rpf/x64/levels/gta5/props/prop_ch_arcade_degenatron.rpf
    ShaUnpack("9523d65a32cc43c1947e68001dc82c126dc2be2597074cf24b0486f1a3586cba"), // dlc.rpf/x64/levels/gta5/props/prop_ch_arcade_drones.rpf
    ShaUnpack("e5431e21ec9e5291f0fa349ef2d1b66607aea22bd2896c641298ee8b8fb9b6cf"), // dlc.rpf/x64/levels/gta5/props/prop_ch_arcade_fortune.rpf
    ShaUnpack("152f392b53c310a142ebf790088f1d3279ce93d8ff6a17327c874b95beb1adba"), // dlc.rpf/x64/levels/gta5/props/prop_ch_arcade_gun.rpf
    ShaUnpack("3b3d7d122dbe9646a7ae54046be59d3e06a7531ab414030ca3a54887626adb0b"), // dlc.rpf/x64/levels/gta5/props/prop_ch_arcade_jukebox.rpf
    ShaUnpack("b6a88a50a41cdb1a6660321160c3ccc9958ebd26b815639c646634e936d113d1"), // dlc.rpf/x64/levels/gta5/props/prop_ch_arcade_love.rpf
    ShaUnpack("22b99dd130653a5c54980851956f64c8eb79532e3d3c217e7bd76aee4f4a8c25"), // dlc.rpf/x64/levels/gta5/props/prop_ch_arcade_race.rpf
    ShaUnpack("ee7a565a469e8a7439701186a3ba3fc9c6f8da4b5d91f9e1d0d0e4163fd64d55"), // dlc.rpf/x64/levels/gta5/props/prop_ch_arcade_space.rpf
    ShaUnpack("26d6418b044fbd5c410abf4425a81864b38a7cecb5e5f35d540b70dd2a1bf197"), // dlc.rpf/x64/levels/gta5/props/prop_ch_arcade_street.rpf
    ShaUnpack("3e0bcd6351e59067674b3b1642e01b6583e60ab096ae73396e2ebce6a33b89ba"), // dlc.rpf/x64/levels/gta5/props/prop_ch_arcade_wizard.rpf
    ShaUnpack("a714cb77c0fde73fc5acebbc0820465f6f123dcd4e405fb9a3316fd647d00120"), // dlc.rpf/x64/levels/gta5/props/prop_ch_casino_accs_01.rpf
    ShaUnpack("b5e5933b49cf7bdb4620b79e6840dce33f59077a674b0317c6c054632a2911f9"), // dlc.rpf/x64/levels/gta5/props/prop_ch_casino_accs_02.rpf
    ShaUnpack("3e4cebe5c16acd10df22d57b717bbed6f198da4836e6f1c9d76f1e2535c6f6d6"), // dlc.rpf/x64/levels/gta5/props/prop_ch_casino_backarea.rpf
    ShaUnpack("92a8bc3918c230731c89ce4cebfe82b07c6529f4cbd7d9147a39f3a2fb7ddc98"), // dlc.rpf/x64/levels/gta5/props/prop_ch_casino_drones.rpf
    ShaUnpack("6c738dd3c78cde264adeebea454feab494656f957c7041372f58af2eabe3fa26"), // dlc.rpf/x64/levels/gta5/props/prop_ch_casino_lucky_wheel.rpf
    ShaUnpack("0d3a21ee25838b33f4b9599f830a9c9ff12c78eb3905709d4c5a9568c39c3299"), // dlc.rpf/x64/levels/gta5/props/prop_ch_casino_machinery.rpf
    ShaUnpack("021cd130f743a61d6b33ff5b83962da65da9874e5c3741316b417d5199e0c43e"), // dlc.rpf/x64/levels/gta5/props/prop_ch_casino_main.rpf
    ShaUnpack("dd77f096ee35759e0c70afccec31d24cb8efd88a99fa7de11717fca8beaa17cf"), // dlc.rpf/x64/levels/gta5/props/prop_ch_casino_main_01.rpf
    ShaUnpack("6aad3675182f0e93be5cbfbcdd1ff2cd491cc2e7dd19202fdfcfdeb150a7c1be"), // dlc.rpf/x64/levels/gta5/props/prop_ch_casino_security.rpf
    ShaUnpack("f52a950afe115cbe2533bc69fe9cc602fd1e69304cce560565b53ca878d3f686"), // dlc.rpf/x64/levels/gta5/props/prop_ch_casino_slots.rpf
    ShaUnpack("80a055ff99ca82e08b185b4e7349a58c937b2010030444977f9322cf37ab0a21"), // dlc.rpf/x64/levels/gta5/props/prop_ch_casino_tables.rpf
    ShaUnpack("71c9de2483d948b6b5b444173a3ba3072756abecbdb8792794f0ade52eab8b5a"), // dlc.rpf/x64/levels/gta5/props/prop_ch_casino_trophies.rpf
    ShaUnpack("8e10e56d84c97872f87c48c363dfc72c648c652b0d52ec925b73cb10d0ec27fd"), // dlc.rpf/x64/levels/gta5/props/prop_ch_casino_vault.rpf
    ShaUnpack("f4d282ac222ad89d98da363b2575adf5baf13c3dedbedcf2215a50735872508a"), // dlc.rpf/x64/levels/gta5/props/prop_ch_collectibles_freemode.rpf
    ShaUnpack("e7e0134206efb330bfc26c004569faffa3d896144cf8b95211cee586e7bbb628"), // dlc.rpf/x64/levels/gta5/props/prop_ch_des_tunnel.rpf
    ShaUnpack("c786bb79fce20a0a34464892b140ee889b91fc61a5bf8b4680ab5dce7ce61a92"), // dlc.rpf/x64/levels/gta5/props/prop_ch_des_tunnel_end.rpf
    ShaUnpack("897f0e68e0967747ea0d1b0f1f2d50e0044187818dce3e35596a723a34561ea4"), // dlc.rpf/x64/levels/gta5/props/prop_ch_des_vault_door.rpf
    ShaUnpack("62d01ba2034f35b4fde35e7a15964fe35de290a67b74ca0de60a7d309eb30680"), // dlc.rpf/x64/levels/gta5/props/prop_ch_des_vault_door_end.rpf
    ShaUnpack("0677b1f39e2786392c4b4e07f5d62f091e03c59c7607574ca8148ca51dece6b8"), // dlc.rpf/x64/levels/gta5/props/prop_ch_mission_freemode.rpf
    ShaUnpack("aaa687eeaa448209d8ffa61ab63bbc43f315e212522bbe5919f05a3a6e011827"), // dlc.rpf/x64/levels/gta5/props/prop_ch_morgue.rpf
    ShaUnpack("451105b8509d837ea7ad96996ff4e6b6949d0a2de3ea0ac786c158d23233b757"), // dlc.rpf/x64/levels/gta5/props/prop_ch_penthouse.rpf
    ShaUnpack("988689501deba0504b9c10a088a221f88214f7c6173a575405febba282e2bf6a"), // dlc.rpf/x64/levels/gta5/props/prop_ch_track.rpf
    ShaUnpack("0f0bc380d42370042f9c772976f0d5d79b65562c0c1c119521e0103c2b9ae142"), // dlc.rpf/x64/levels/gta5/props/prop_ch_tunnel.rpf
    ShaUnpack("58fb11c1e4c535863867580473aa3cba2b42e57db5860e676e60f71f4e4e52b4"), // dlc.rpf/x64/levels/gta5/props/prop_ch_vault.rpf
    ShaUnpack("6c1341ced32f29a853cc93597382284b0ed227911cb4fad4a200202a86823c2e"), // dlc.rpf/x64/levels/gta5/vehicles/mpheist3.rpf
    ShaUnpack("ae825c4fe7677df9d8c7bfa1369b738e0da40fef24485e3db765033ebb1357fc"), // dlc.rpf/x64/levels/mpheist3/vehiclemods/asbo_mods.rpf
    ShaUnpack("4e127fa5ae67823854cd20cd0b0aabc6ed33dc08d18107cdf381aba39a4a2db4"), // dlc.rpf/x64/levels/mpheist3/vehiclemods/everon_mods.rpf
    ShaUnpack("8562bd278c979073e38ce38c6cfee6434d195717c2027132955f7a647e18da56"), // dlc.rpf/x64/levels/mpheist3/vehiclemods/formula2_mods.rpf
    ShaUnpack("0d81d8d7e5aab098021bd2ed091222ddf165bd4266163479157df71f65eebc26"), // dlc.rpf/x64/levels/mpheist3/vehiclemods/formula_mods.rpf
    ShaUnpack("b8325be02b177ad92487ab2b8f73930ca697ee514df12c16f3761b456895e814"), // dlc.rpf/x64/levels/mpheist3/vehiclemods/furia_mods.rpf
    ShaUnpack("006171a0946d987e79827708f86043aa30d1cff2dc12eeb5fa6d599728725ff5"), // dlc.rpf/x64/levels/mpheist3/vehiclemods/imorgon_mods.rpf
    ShaUnpack("8b62406deab992e846a5fae45d0fd127de5e457fbe808b87578fb72acecf436f"), // dlc.rpf/x64/levels/mpheist3/vehiclemods/jb7002_mods.rpf
    ShaUnpack("11f22d770ed4f634302a2c3fec76c5ba286e70681cf0df48e5625f9d8dc3a1be"), // dlc.rpf/x64/levels/mpheist3/vehiclemods/kanjo_mods.rpf
    ShaUnpack("c344d566fb1d0aa8936a413ba135fd65a56fdf9722e341a3b1bc08dfd667c97c"), // dlc.rpf/x64/levels/mpheist3/vehiclemods/komoda_mods.rpf
    ShaUnpack("9a433a018002a5797f9719ef36188e081cd3ff6c58efb5eae82b4651e860a015"), // dlc.rpf/x64/levels/mpheist3/vehiclemods/minitank_mods.rpf
    ShaUnpack("72e084644eebaa3e1f5c7afcd8e08370eda1f3e915f9242d480703a653d06a36"), // dlc.rpf/x64/levels/mpheist3/vehiclemods/racecarwheels_mods.rpf
    ShaUnpack("908b47fae87093b373a0a15c486fc7045295e840b2f2c5eb87568be90c06a228"), // dlc.rpf/x64/levels/mpheist3/vehiclemods/rebla_mods.rpf
    ShaUnpack("4449e570980dcdee78f830b84eeff07b062ff5d10d5a6aac03f7b8f5cf215ef8"), // dlc.rpf/x64/levels/mpheist3/vehiclemods/retinue2_mods.rpf
    ShaUnpack("bd8b4b9edd8e8f6a658ef3a3be96d6cfe24491a81e8ea316b876895fbec85cca"), // dlc.rpf/x64/levels/mpheist3/vehiclemods/stryder_mods.rpf
    ShaUnpack("19d8cb3bf100f781d02478b064ba3cbdba7f2e15d4d4f233502f0c746208d251"), // dlc.rpf/x64/levels/mpheist3/vehiclemods/sugoi_mods.rpf
    ShaUnpack("b6f75a14173f54c8c753460948a83c3b1df74b89773dd48131cf366a69c857c9"), // dlc.rpf/x64/levels/mpheist3/vehiclemods/sultan2_mods.rpf
    ShaUnpack("59474b927aed6ca58746f5a7e306b0729212440e0b73100acd52d4fe40194e3d"), // dlc.rpf/x64/levels/mpheist3/vehiclemods/vagrant_mods.rpf
    ShaUnpack("709a4aecad484c397e35a6dfb12365ba31a21d17774899286111fae855319242"), // dlc.rpf/x64/levels/mpheist3/vehiclemods/vstr_mods.rpf
    ShaUnpack("f0374d6c9aa7d2fcd1aabfd2493397625cfa74c15496aba2bbd571958ffcea86"), // dlc.rpf/x64/levels/mpheist3/vehiclemods/yosemite2_mods.rpf
    ShaUnpack("495afb254e385316cde005a7507f46055459121db8b032648f6b0ecf041d6408"), // dlc.rpf/x64/levels/mpheist3/vehiclemods/zhaba_mods.rpf
    ShaUnpack("48cf18c3898cd57c19998c85a4980c2212a40079f9980a2156f1b2dec46c5106"), // dlc.rpf/x64/models/cdimages/mpheist3_female.rpf
    ShaUnpack("defdf025c9f7ac0c6fb87c53d61804bfcb985a59562959084f6e2380c71b58df"), // dlc.rpf/x64/models/cdimages/mpheist3_female_p.rpf
    ShaUnpack("e149b42fa6ca08c92e0abfa06908f3b42ecc4866506a6ce2d7903f52ce681ee9"), // dlc.rpf/x64/models/cdimages/mpheist3_male.rpf
    ShaUnpack("48efe6df4eb24cf494a4a411b74f67560ea3ba9df1828663efd60669c8d57571"), // dlc.rpf/x64/models/cdimages/mpheist3_male_p.rpf
    ShaUnpack("62da3eb01c7d2e6e5561c679236bbbd6ff0823a4f7eb5c30d0904553128e012c"), // dlc.rpf/x64/models/cdimages/mpheist3_ped_mp_overlay_txds.rpf
    ShaUnpack("e3f235d6908f9a354a6492e655f01897a0b30b9296dfe650fef4014c906d0789"), // dlc.rpf/x64/models/cdimages/weapons.rpf
    ShaUnpack("fdbdc57c8a4e357c298b56f6591ddb329678b594d1947eee1b62c3aca3d1339f"), // dlc.rpf/x64/models/cdimages/peds/mpheist3.rpf
    ShaUnpack("861652c96f55aafbf9fb6a1f345a572d109122637852317371c43625288ae08b"), // dlc.rpf/x64/models/cdimages/peds/mpheist3_cutspeds.rpf
    ShaUnpack("adbd371055e6bfee7c8774dc99be36c3f0542081aa9809b456c72fb77b6103d9"), // dlc.rpf/x64/models/cdimages/peds/mpheist3_cutspeds_p.rpf
    ShaUnpack("40340c002b45d3610cd3c446374e068ff3efb85475ce01dd1eb1ed39df39eea5"), // dlc.rpf/x64/models/cdimages/peds/mpheist3_p.rpf
    // update/x64/dlcpacks/mpheist4/dlc.rpf
    ShaUnpack("5aaf3a417c6bfc33f9e2bd95b2d911f9b043732cd884f307323c6749caad030a"), // dlc.rpf
    ShaUnpack("49d2179809234cffa8150ed89287645eadfbef11ae226192fbd5cac3ff5d96cb"), // dlc.rpf/x64/anim/creaturemetadata.rpf
    ShaUnpack("f2abd7ce2a3b4aa31a6192b8a4496a6b75fa216226142e6c35b2c22bf0fb33d8"), // dlc.rpf/x64/anim/expressions.rpf
    ShaUnpack("71abfe5febf9c4b08faaec3b3a6156a153f091f44cae4cede4c71ec599d44b68"), // dlc.rpf/x64/anim/cutscene/cuts_hs4.rpf
    ShaUnpack("5ccf9afc4ee9e368ca0eb0911f4b5f915d65fc3f01c91c30021119e007148cea"), // dlc.rpf/x64/anim/cutscene/cuts_hs4_cas.rpf
    ShaUnpack("1f32b71a1d4ab24b0b763d0eb4c124ab87eeef27b3b0e00034749b029fb88e20"), // dlc.rpf/x64/anim/cutscene/cuts_hs4f.rpf
    ShaUnpack("8a3757cd9d117fbe6fce790f24dd7ce81d6f129c65e5a9373fb0b23ae44075af"), // dlc.rpf/x64/anim/ingame/clip_anim@.rpf
    ShaUnpack("a4067f952c201245c1e989913f0bb8aaf30f2bbf54cf14edf4b5a9a465e3cd77"), // dlc.rpf/x64/anim/ingame/clip_anim_heist@.rpf
    // update/x64/dlcpacks/mpimportexport/dlc.rpf
    ShaUnpack("ff23727b248e5933df8793c5e1da13ca3ccbc5fce20f22714adc0065b078d306"), // dlc.rpf
    ShaUnpack("aeb8af6f22413b55535b6f4cb26af0b164962d30f97bd4a8a55954cdb8f41b6e"), // dlc.rpf/x64/anim/creaturemetadata.rpf
    ShaUnpack("caa45ebdba61abb5732031db4bf9812884d9b8185ea61b4ca49bc7e56719106d"), // dlc.rpf/x64/anim/cutscene/cuts_impexp.rpf
    ShaUnpack("ddaf2c743b5641dae7936c75ce03765a148870b104f54180d448e7ce9739ca97"), // dlc.rpf/x64/anim/ingame/clip_anim@.rpf
    ShaUnpack("bcb4b7c0c5e0de629c872806ca4c4972a83aadadb3cd6176536f3b68bec1902c"), // dlc.rpf/x64/audio/occlusion.rpf
    ShaUnpack("a75d07ced4e2b720d2aa39708a8de285632f1528099911089540490f62e4d99e"), // dlc.rpf/x64/data/effects/ptfx.rpf
    ShaUnpack("fc0e13be6f2e6cc75be9e428dc274100b3a26b7ce6957f022fc19963e3d4f646"), // dlc.rpf/x64/data/lang/americandlc.rpf
    ShaUnpack("6cf7b2e3301b2e4bd2d33e049df80cf8daa89e2c72d9d3fb4876f904810e3bc5"), // dlc.rpf/x64/data/lang/chinesedlc.rpf
    ShaUnpack("963bf26eb9ab36c5d900de7e44571f20cd4100af0c687a7d2786ef9a9c2f6619"), // dlc.rpf/x64/data/lang/frenchdlc.rpf
    ShaUnpack("37b63f92dea4568fc3c2cf017b4264cacbcc73c5125a64035096c18bef24656a"), // dlc.rpf/x64/data/lang/germandlc.rpf
    ShaUnpack("c8f38735757d7231df16a91c39382af7336a11d7f749edb3abe89fbc8babc9ab"), // dlc.rpf/x64/data/lang/italiandlc.rpf
    ShaUnpack("1265201b8222a1efc43691b382374d19c9b97c56b2427c88787c0c229a1f6039"), // dlc.rpf/x64/data/lang/japanesedlc.rpf
    ShaUnpack("d692f1bd1c45402cce0c60bbf62ce817ed4d0e58ea17968d5dbd08374c4520ff"), // dlc.rpf/x64/data/lang/koreandlc.rpf
    ShaUnpack("da9dfd0271d657fce9e2182e5520a2b6e247e0ec1a1fd4c1e52b3f0e70e73b4f"), // dlc.rpf/x64/data/lang/mexicandlc.rpf
    ShaUnpack("a2658c65385562e157ce0410f9a486c19699b378fd7f726456ab8a8ed90acafc"), // dlc.rpf/x64/data/lang/polishdlc.rpf
    ShaUnpack("651980f6ac654beb15d220b35f2ca079b511a69e7bab5b9f94dcbe9838bc51d8"), // dlc.rpf/x64/data/lang/portuguesedlc.rpf
    ShaUnpack("f12a1e607d53aef3328a3f2cbd2b9754de7f7132eeb0a84c55b968132d0db2fa"), // dlc.rpf/x64/data/lang/russiandlc.rpf
    ShaUnpack("0f472ce524bfc5acc2b6441ab0897d9106f16ae196f659947453710088429924"), // dlc.rpf/x64/data/lang/spanishdlc.rpf
    ShaUnpack("76beaea7f4f73d9c945ea3517735a179f09f9d5ac6c16a226cb35af62ac64817"), // dlc.rpf/x64/levels/gta5/interiors/impexp_interior_01.rpf
    ShaUnpack("cb64eeeca267bdef33ae72f503fac63e4c49af13c8629c35b8e78a16c2bcfbf8"), // dlc.rpf/x64/levels/gta5/interiors/impexp_interior_02.rpf
    ShaUnpack("afa05d6ee18857e68e462ee0ace93790995e18cd0332da64546bf93d9b4333f3"), // dlc.rpf/x64/levels/gta5/interiors/impexp_interior_placement.rpf
    ShaUnpack("0823b7ccdfcf3b399f7d71ad4bf7dc2d6269911e28729192a005e1662b25ac87"), // dlc.rpf/x64/levels/gta5/interiors/impexp_intwaremed.rpf
    ShaUnpack("581b1f617db100834fc1293decd55b7c7e506febdd567973f405daa1b843fd0e"), // dlc.rpf/x64/levels/gta5/interiors/imptexp_mod_int_01.rpf
    ShaUnpack("c6144d8e6a8d14f94309d8725ac8df935db2d6b0b9eefcc8ff0d5c40d734b44f"), // dlc.rpf/x64/levels/gta5/props/imp_exp_interior_props.rpf
    ShaUnpack("684e551d3e1eba8a74162d1f8b9f75b6c5a695d0d2b9853f5a0293e7b107765b"), // dlc.rpf/x64/levels/gta5/props/prop_adv_hdsec.rpf
    ShaUnpack("062b44166b2a70d3b178051444270172325671ffb87a40a7a08be74adc680bed"), // dlc.rpf/x64/levels/gta5/props/prop_groupbarrel.rpf
    ShaUnpack("f08545d47bc58c121232f232ec4b37f6f60f55d65706a6d60da50956b32b7d78"), // dlc.rpf/x64/levels/gta5/props/prop_impex_gate.rpf
    ShaUnpack("a85821c9708a1c66ff654008b5a40df2ee77a54cd4693a9295539c5d13b4df5f"), // dlc.rpf/x64/levels/gta5/props/prop_impexp_car_parts.rpf
    ShaUnpack("b0e712ba729023c2435f0a12f2182e11eee10c6b57b7195ea0238356ef49a5a9"), // dlc.rpf/x64/levels/gta5/props/prop_impexp_carlifts.rpf
    ShaUnpack("4682a639754e52cf09f3503f7bd083d144321cec20a9f37b2c9fee503887484d"), // dlc.rpf/x64/levels/gta5/props/prop_impexp_crates.rpf
    ShaUnpack("6630344750460b58870c5f7ad91aa1e86a591f16efc6bc295d3c975ce679b86b"), // dlc.rpf/x64/levels/gta5/props/prop_impexp_deadline.rpf
    ShaUnpack("f738f99e97d074091e9560ef208ce7dd8be3d51d0c5fba5ca4f6235ac995e570"), // dlc.rpf/x64/levels/gta5/props/prop_impexp_doors.rpf
    ShaUnpack("f691d91c2b165f1d1b16fd137c9199b4e66dccbfbf4200e54f2e2488f9a98fb0"), // dlc.rpf/x64/levels/gta5/props/prop_impexp_garage_equipment.rpf
    ShaUnpack("2094d4c11c8127249e5386627d21ad3db1ae18f86c08b08ef7f7c71949cdea04"), // dlc.rpf/x64/levels/gta5/props/prop_impexp_para.rpf
    ShaUnpack("685276b9345f383ee95c0690ed5380e42836932244a6069033a4e8081a8a6600"), // dlc.rpf/x64/levels/gta5/props/prop_impexp_seating.rpf
    ShaUnpack("21994c8cf47d331a63b6db6301ca9105b783f333dc4208c24816b33bdeab5363"), // dlc.rpf/x64/levels/gta5/props/prop_lives_bomb.rpf
    ShaUnpack("47b0b69c7b33c55a181d76f823a9600131d89ac606d17bab15c70f86236ca3af"), // dlc.rpf/x64/levels/gta5/props/prop_ship.rpf
    ShaUnpack("b525473ffc324eb31abd7951306f06c38d422e5ea67145846ee1286fbac2da92"), // dlc.rpf/x64/levels/gta5/vehicles/mpimportexportvehicles.rpf
    ShaUnpack("71e50e4a5da0bf99d1479d313a5ab30ac9b61aa415b4571de4c7049f2cdf7fe5"), // dlc.rpf/x64/levels/mpimportexport/vehiclemods/comet3_mods.rpf
    ShaUnpack("16bdeca6f341915ea6e487868b59dd69ee0fb1342de1ba889fe6f6289aab2b44"), // dlc.rpf/x64/levels/mpimportexport/vehiclemods/diablous2_mods.rpf
    ShaUnpack("fbf4fdc02b246b3c8232cc42e02c51fef2a27bb7568094e4684d070274479b90"), // dlc.rpf/x64/levels/mpimportexport/vehiclemods/elegy_mods.rpf
    ShaUnpack("a62888dfd3fc8d15d01dafee4070e8dda7df6b66ce0236e5fc017b2b5ce5fdcb"), // dlc.rpf/x64/levels/mpimportexport/vehiclemods/fcr2_mods.rpf
    ShaUnpack("f37a47e261cc40ba45a62cdb25530535c2b545e3fdd16270b2bc4e6391482a88"), // dlc.rpf/x64/levels/mpimportexport/vehiclemods/fcr_mods.rpf
    ShaUnpack("9b4982e227ea6d7669f7757fb73e787e56287c8ec6471f9e98cdd58a2024b8b1"), // dlc.rpf/x64/levels/mpimportexport/vehiclemods/italigtb2_mods.rpf
    ShaUnpack("27b5ee0f4e2f1c113d85c71f74951854bcd486abf243f3fcc3c343ff12aa070d"), // dlc.rpf/x64/levels/mpimportexport/vehiclemods/italigtb_mods.rpf
    ShaUnpack("0eeb3ba55ae6c610ba9491147def2ca725c0c971e900afe9a99ec2a0ec503143"), // dlc.rpf/x64/levels/mpimportexport/vehiclemods/nero2_mods.rpf
    ShaUnpack("948a158335efa190106d7df9865d93c1c78efa71d2f2038eb08ceb6febca748b"), // dlc.rpf/x64/levels/mpimportexport/vehiclemods/nero_mods.rpf
    ShaUnpack("ce8a9850bfd91be4eaf955aa45f8fcf3549d8c36c4bdef17b86e94421e47d9b9"), // dlc.rpf/x64/levels/mpimportexport/vehiclemods/penetrator_mods.rpf
    ShaUnpack("f0752692a14bd607e2864b1ae90575be8f0c3afb264646c668edee3b69fd8692"), // dlc.rpf/x64/levels/mpimportexport/vehiclemods/specter2_mods.rpf
    ShaUnpack("3cbdde53bb41b057c84c5540759691974519585c1ff64fba3a92175f6ec275e8"), // dlc.rpf/x64/levels/mpimportexport/vehiclemods/specter_mods.rpf
    ShaUnpack("e8fc548c58ed53199e8966f596df0391c50ed9e2bcc19d4d2863afded47b4cd6"), // dlc.rpf/x64/levels/mpimportexport/vehiclemods/tempesta_mods.rpf
    ShaUnpack("2710ff0a7664bc7bb2515685583646aed9ee9e123b9e0e457cfbd8d7f5f9e2d3"), // dlc.rpf/x64/models/cdimages/mpimportexport.rpf
    ShaUnpack("092b4b5fb1dd7149344ecf42212d1c12e3358efa96c8e22314a6161aceabb223"), // dlc.rpf/x64/models/cdimages/mpimportexport_female.rpf
    ShaUnpack("f890e8c0eafc11c5c3662af0f2c2212cc5fb6024dcd3906af164b910752ede3a"), // dlc.rpf/x64/models/cdimages/mpimportexport_female_p.rpf
    ShaUnpack("82a5b3ba252c6c69e3c9c97e48b1d549d3bf042abf027099bb60650c4d9e63bb"), // dlc.rpf/x64/models/cdimages/mpimportexport_male.rpf
    ShaUnpack("392a5cbd3efa62c8449a93ac449c13b45eb5365804b4a96396db753201a09988"), // dlc.rpf/x64/models/cdimages/mpimportexport_male_p.rpf
    ShaUnpack("ee0c05864151dda587f2630f803335d0c60c287375b97ba9286085cefad34cf2"), // dlc.rpf/x64/models/cdimages/mpimportexport_p.rpf
    ShaUnpack("56bea4ee5bdeb88b49c1f92b1a42d9341914a4307f7e74016a2802c63538c884"), // dlc.rpf/x64/models/cdimages/mpimportexport_ped_mp_overlay_txds.rpf
    ShaUnpack("b889737c0eaf14f1a47fd63c1ba8cec457c96639796d72cd07f5acca480895b9"), // dlc.rpf/x64/models/cdimages/mpimportexport_streamed.rpf
    ShaUnpack("1d55e349419e527d1d16b63691ab589a73facd76858937e5e6febd7c17800e86"), // dlc.rpf/x64/models/cdimages/mpimportexport_streamed_p.rpf
    // update/x64/dlcpacks/mpjanuary2016/dlc.rpf
    ShaUnpack("dd7e394505d21e086315b29f171b64f1111bb902836716e4fe46a72342be97d6"), // dlc.rpf
    ShaUnpack("667830a87dd8a761a418f82b2997919c1444dbd388c4306c0faeed4c6bd63a45"), // dlc.rpf/x64/anim/creaturemetadata.rpf
    ShaUnpack("91496a52daa2d5a105add4348c06f8b2fbff77151037337f5fa60c946a56e59f"), // dlc.rpf/x64/data/lang/americandlc.rpf
    ShaUnpack("fa640763ac0d5cd77b98977292a9964119e9bb7b5572fc8d0da1539b3d250523"), // dlc.rpf/x64/data/lang/chinesedlc.rpf
    ShaUnpack("a0cb032a3d32afb1255276d017c23f46dba0d70263fcd4333c2b4ce19cdd9cc0"), // dlc.rpf/x64/data/lang/frenchdlc.rpf
    ShaUnpack("2cffff5637b9ad4cb2484ba99e42264057686c54b709665e3b30a4c1d8bbf8d0"), // dlc.rpf/x64/data/lang/germandlc.rpf
    ShaUnpack("75f8b920d2dd1b78730d895a716245cfd919a542fcbe798d28d7df9ceeb5d9cd"), // dlc.rpf/x64/data/lang/italiandlc.rpf
    ShaUnpack("7fa0801a4c20a0919a14a3534c22c5ed3120bf7f8d76018d27f7a18f5a3eb5e1"), // dlc.rpf/x64/data/lang/japanesedlc.rpf
    ShaUnpack("7317093de8001c0bcc541af1b44e05393029726a2d077069f2ff525cb4255e57"), // dlc.rpf/x64/data/lang/koreandlc.rpf
    ShaUnpack("37913456f455586b16cbeedc4787779d7a1e7d0e028f193f12678d737d848654"), // dlc.rpf/x64/data/lang/mexicandlc.rpf
    ShaUnpack("283fdcd4099d72c81bdd4207ff867d063415ced9618349be22f65b5c0bc25b3d"), // dlc.rpf/x64/data/lang/polishdlc.rpf
    ShaUnpack("67808c19c01c1a2d140d901fd155eae3ed889e6a2423a4204be8a3a09af313a3"), // dlc.rpf/x64/data/lang/portuguesedlc.rpf
    ShaUnpack("c89c121be2c1cf7113f99cd71d429dc8216f42e0d20fab327e0e9cfe9eb2b819"), // dlc.rpf/x64/data/lang/russiandlc.rpf
    ShaUnpack("2de2d10418511ef8dd8c66b5ea5ff9851c2d521915c68f32f5ee4a1534cf1ceb"), // dlc.rpf/x64/data/lang/spanishdlc.rpf
    ShaUnpack("e5d4f7c40d5fe4ab00909900d06b65a77406d1591067989c5d161bad8e0ffa87"), // dlc.rpf/x64/levels/gta5/vehicles/mpjanuary2016vehicles.rpf
    ShaUnpack("35f43899170909289eba87a42f278a522923db2e94bfcf32609f35f6e8b91fab"), // dlc.rpf/x64/levels/mpjanuary2016/vehiclemods/banshee2_mods.rpf
    ShaUnpack("10823e3ad2ca27fece654500127ad4033d10eee1dd282ff65595f2a1643fd75c"), // dlc.rpf/x64/levels/mpjanuary2016/vehiclemods/sultanrs_mods.rpf
    ShaUnpack("3201b020ad9b2cf042ad4353246391b6f3ebdae377011ee43be2ec017a367167"), // dlc.rpf/x64/models/cdimages/mpjanuary2016_female.rpf
    ShaUnpack("c74f34df4c9926e9d42dcdf0eb2a103ba49f59a8eed07b4916e845865a6ef652"), // dlc.rpf/x64/models/cdimages/mpjanuary2016_female_p.rpf
    ShaUnpack("de9d284a8089549aacf476a974c784f88911d73fd775a056bd11fe424fea1d91"), // dlc.rpf/x64/models/cdimages/mpjanuary2016_male.rpf
    ShaUnpack("8683fe4f408c539d8ced55cd443e13771668b585950ec04a32d1eea89e95b462"), // dlc.rpf/x64/models/cdimages/mpjanuary2016_male_p.rpf
    // update/x64/dlcpacks/mplowrider/dlc.rpf
    ShaUnpack("3c1cd7191a3ab09997ea27dbeaf87e34309d3f301cb746a8caeb1d87ce2c68e6"), // dlc.rpf
    ShaUnpack("9b192f2414539ce71686ae46c31e7cfdf649e12205e6ec07a8da1f28f6a72e6d"), // dlc.rpf/x64/anim/creaturemetadata.rpf
    ShaUnpack("5a9276f9d648355f1fa7a9f9f509c9f5372cbdd8976d37e61c87d4d3fedbff2a"), // dlc.rpf/x64/anim/expressions.rpf
    ShaUnpack("c8de2a00e3174d6ce92c639a21132afac4e9d0311f756d317613b2303e5e29b3"), // dlc.rpf/x64/anim/cutscene/cuts_low_drv.rpf
    ShaUnpack("307a328bfc7b8cf7f0ced5fdf2951c717a59aefdd7e7e6aad57fef4711e78cc3"), // dlc.rpf/x64/anim/cutscene/cuts_low_fin.rpf
    ShaUnpack("2753abbd14e455c25590304006ca18bbf3839f102117433c8a3a0ed1dd63f5a7"), // dlc.rpf/x64/anim/cutscene/cuts_low_fun.rpf
    ShaUnpack("2c509609715eb34b8fdb57d12bc1791992ce58663a5d07b97adfa54703c32046"), // dlc.rpf/x64/anim/cutscene/cuts_low_int.rpf
    ShaUnpack("27a498f6d3d6c95921df074e6487d4873f40127dd53775b18f294d66296267d7"), // dlc.rpf/x64/anim/cutscene/cuts_low_pho.rpf
    ShaUnpack("3f48fb0484b69c258005ef47f9bd97f59120beff59be524abb4b3825f617d1cd"), // dlc.rpf/x64/anim/cutscene/cuts_low_tra.rpf
    ShaUnpack("3de5b7cf78b0ed9589fc6798d32f96a593234b5b6793f6801295f8e9fd8c99e2"), // dlc.rpf/x64/anim/ingame/clip_anim@.rpf
    ShaUnpack("1e0423d1c154d4a09e06e032c6b7b931394195f37167ad06616e423b6e3aa210"), // dlc.rpf/x64/audio/occlusion.rpf
    ShaUnpack("fadfa750cb300cc64363a6045624689b43f7dc538967f4c7835a47d778550a5c"), // dlc.rpf/x64/data/effects/ptfx.rpf
    ShaUnpack("39431d5fa6ecdff892265ded0ba980e0bfbc5377458ffeed510587a2d0aa4570"), // dlc.rpf/x64/data/lang/americandlc.rpf
    ShaUnpack("d7e08b6562f2a27660524a3c5d04c7273d9dc0a46b7267432e245ab54db23e22"), // dlc.rpf/x64/data/lang/chinesedlc.rpf
    ShaUnpack("95377b641ca60515efb6639b34fbbe8fdab9574430331dbf1edd13678a7475dd"), // dlc.rpf/x64/data/lang/frenchdlc.rpf
    ShaUnpack("72679a139452b33a325393d591c15462d181b4dc992d86072c2ed426a428ed06"), // dlc.rpf/x64/data/lang/germandlc.rpf
    ShaUnpack("6a79f6310623cb26df03c393032ec24ac1b9501374b0a82bc6834e21cbcafe7e"), // dlc.rpf/x64/data/lang/italiandlc.rpf
    ShaUnpack("be767c39027c2fc12d3ba087c096f9462f7b455697e60d4201534adaab4c917a"), // dlc.rpf/x64/data/lang/japanesedlc.rpf
    ShaUnpack("bc4281e3c188f5f616afb71a25c9ae820dd3610dd672c6fb12e353fff3dca92b"), // dlc.rpf/x64/data/lang/koreandlc.rpf
    ShaUnpack("c635af0190e15cd04f83647dccccd23a912a2d78a645a0718c393b1b6b4b4c25"), // dlc.rpf/x64/data/lang/mexicandlc.rpf
    ShaUnpack("905772d7c5c4987fc65a33d493e3ea9a2c5131f0c12b34185d7eb21d99c7ddd9"), // dlc.rpf/x64/data/lang/polishdlc.rpf
    ShaUnpack("6bb5948340a6ab0bc6e38b2254457ecd148efdb4339ba18b7aa912255a64b526"), // dlc.rpf/x64/data/lang/portuguesedlc.rpf
    ShaUnpack("a3ea50f46cbde390a1c9dc960e7765e71de9330c7f50cbc5c8d681635ceb1246"), // dlc.rpf/x64/data/lang/russiandlc.rpf
    ShaUnpack("8c9b05d6f50c55a65a83e3d9f88d19b8acddc45b37213eeae7a26016593cbfab"), // dlc.rpf/x64/data/lang/spanishdlc.rpf
    ShaUnpack("4ea3945a46362e676232eaeb5698717c1305090e1d4d8f6afa39ba5c958ec17c"), // dlc.rpf/x64/levels/gta5/_citye/indust_01/id1_06.rpf
    ShaUnpack("1db8ab4b8caa6f0c9030a30d2425126d74d692bad0db142fd79b60e432fb1c54"), // dlc.rpf/x64/levels/gta5/_citye/indust_01/id1_07.rpf
    ShaUnpack("6ee729a901db2e5d968e02771658e040365f606b5cb2692c8f7af0def42af677"), // dlc.rpf/x64/levels/gta5/_citye/indust_01/id1_occl.rpf
    ShaUnpack("d37ceb03cf18eaf167b019bef4dbbe7a684f312d81c350a4f3ca202c6085da0c"), // dlc.rpf/x64/levels/gta5/_citye/indust_01/indust_01.rpf
    ShaUnpack("3882ab18fb69d33a4da2e12aedd69da12a5e144ed07f4c818459ff56cd93ee75"), // dlc.rpf/x64/levels/gta5/_citye/indust_01/indust_01_metadata.rpf
    ShaUnpack("3346e8d2d76525d9a445c8929f8c6310945d6e262836a8d086581124396a5bf2"), // dlc.rpf/x64/levels/gta5/_citye/scentral_01/sc1_00b.rpf
    ShaUnpack("75668cd5b930126260c38834c2706fec93bef910d7585de141ced35b5d97f270"), // dlc.rpf/x64/levels/gta5/_citye/scentral_01/sc1_02.rpf
    ShaUnpack("c545ed41245daf287c7d77e9602c062cae88343cc53fc43ebcbf2122436458b4"), // dlc.rpf/x64/levels/gta5/_citye/scentral_01/sc1_11.rpf
    ShaUnpack("f3cdd051dbb2f58a61c36315846d3a14897678abbb90b304b8cbe29eefc249a0"), // dlc.rpf/x64/levels/gta5/_citye/scentral_01/sc1_12.rpf
    ShaUnpack("23bbcd50a88e420c924ff557b164f43626d6d0957cde9d625dfeb99d1bd33690"), // dlc.rpf/x64/levels/gta5/_citye/scentral_01/sc1_23.rpf
    ShaUnpack("3d5968e0da767efc79d6ead87b29ca3f45345d968a77ec6384c978fee9ab705c"), // dlc.rpf/x64/levels/gta5/_citye/scentral_01/sc1_rd.rpf
    ShaUnpack("5f2cd1d99b21325c73296c030a054eec049a35d64e803d13e2a3f0ec0196e6b8"), // dlc.rpf/x64/levels/gta5/_citye/scentral_01/scentral.rpf
    ShaUnpack("0e6a860b996d12db6822c79c7ddd9d970a4efa95affa216740ef723c793829b4"), // dlc.rpf/x64/levels/gta5/_citye/scentral_01/scentral_metadata.rpf
    ShaUnpack("ca63f6207790cbe5a3029e23734ea0dc18dfdb08d272eb07558a58d7aec0b37c"), // dlc.rpf/x64/levels/gta5/_hills/country_04/country_04_metadata.rpf
    ShaUnpack("4cc70993fcd4f362bf2f70dfe54219a43b05b792c0df2eaaf09f94ae2f7e4b78"), // dlc.rpf/x64/levels/gta5/_hills/country_04/cs4_02.rpf
    ShaUnpack("f5cdad081ea9a27cbbbe32ef6109baecf1121335e0f22c7c0c86d3ac80dd5580"), // dlc.rpf/x64/levels/gta5/_hills/country_04/cs4_04.rpf
    ShaUnpack("ba0763893b30b28984c0e52ca055a4571bf963313043cf5b2b747b415ea5fc50"), // dlc.rpf/x64/levels/gta5/_hills/country_04/cs4_06.rpf
    ShaUnpack("3e8c5e5338757e28279c18cf35cb62cddaad01393eedc51efde177d7df900229"), // dlc.rpf/x64/levels/gta5/_hills/country_04/cs4_10.rpf
    ShaUnpack("03def1e6878d7de16c4d34be32475f47071eb9bd13898b8d0c6362135be54904"), // dlc.rpf/x64/levels/gta5/_hills/country_06/country_06.rpf
    ShaUnpack("98efd8f5d12de4da6eb5fdf4db73fff2c96c8bce6cab6d924787bda2c2c156f2"), // dlc.rpf/x64/levels/gta5/_hills/country_06/country_06_metadata.rpf
    ShaUnpack("3656ac787d8afe531c139336d05eb5c0ab31e3884bba0df1b8dba15538c57b34"), // dlc.rpf/x64/levels/gta5/_hills/country_06/cs6_03.rpf
    ShaUnpack("0e4e5fc0395014906da57487898d870303a4dbd6ad170d9223f509d8fcb12cbc"), // dlc.rpf/x64/levels/gta5/_hills/country_06/cs6_04.rpf
    ShaUnpack("95dd18bb2f8c71539d434b500a22b78cc59f0b24669565d9541788f584bb14d9"), // dlc.rpf/x64/levels/gta5/_hills/country_06/cs6_08.rpf
    ShaUnpack("3dfa30caaa528d23ae74155bf382b321f3f20e6e31b3ddfc5c31f8a37a2fb407"), // dlc.rpf/x64/levels/gta5/interiors/supermod_garage_int.rpf
    ShaUnpack("a762ed61c096b71d2c341b449629f8b09dd0ef9391c736515bcde35549e623b9"), // dlc.rpf/x64/levels/gta5/props/prop_boathousedoor.rpf
    ShaUnpack("df636fd7314f0f3541f1a937c3bdde825597f99842de9b56626fcdacfe8ce982"), // dlc.rpf/x64/levels/gta5/props/prop_carburettor_01.rpf
    ShaUnpack("d4add66062cae6df42ada916f2bf34f93c480d645d6b47a77c403ab0e1dfffa0"), // dlc.rpf/x64/levels/gta5/props/prop_carkey_fob.rpf
    ShaUnpack("472dec9d0122894baf805d11ac4aace5d590960ff55ff8f1bd66905d4e0de0d4"), // dlc.rpf/x64/levels/gta5/props/prop_clubstool_01.rpf
    ShaUnpack("69dca8b6cc61066bb4e99deba436960955c29575e37f3c6c44b291c283dfb45f"), // dlc.rpf/x64/levels/gta5/props/prop_rail_col_01.rpf
    ShaUnpack("774b74a0e877394040aaa4665cd1b59ddc4579c30c23aa4f6c7d4d5a33b3ac1f"), // dlc.rpf/x64/levels/gta5/props/prop_suitbag_01.rpf
    ShaUnpack("9ff72fa3ebf89cde3cae2f93ee874808e5cb48ac67dcd9f56b5a0fbd4ba68148"), // dlc.rpf/x64/levels/gta5/props/prop_supermod_door_01.rpf
    ShaUnpack("258cf55e186ae8634b93814f251c84219965567d23bc11edafb91cefb1d838bb"), // dlc.rpf/x64/levels/gta5/props/prop_supermod_lframe.rpf
    ShaUnpack("bebcedd6a90a392679b28cb90eec0db263e25a347a5f9deab44ee237f17cafc1"), // dlc.rpf/x64/levels/gta5/vehicles/lowvehicles.rpf
    ShaUnpack("e7bf27e0fe88ac3f2a440d1a4555c2f3f5519ad2506eced6873200ca701638c8"), // dlc.rpf/x64/levels/mplowrider/vehiclemods/buc_supermod1_mods.rpf
    ShaUnpack("bc392a5a2f919cd1e8feca59c7eceac35c53bf48aba6364334894fbac43567fc"), // dlc.rpf/x64/levels/mplowrider/vehiclemods/buccaneer2_mods.rpf
    ShaUnpack("02e63ca1103d8f2f1dab1435d01d8fba4e16b68b64a7be96f9778987a3b42af0"), // dlc.rpf/x64/levels/mplowrider/vehiclemods/chi_supermod1_mods.rpf
    ShaUnpack("5c369723456a49999668fd94e8eea9bca0836ac99b36af9a5deaa7d14e983c30"), // dlc.rpf/x64/levels/mplowrider/vehiclemods/chino2_mods.rpf
    ShaUnpack("846469c968bae7cfc9d4b3991ec60fafcb1c1ab69ce785f4a369125de9a09aca"), // dlc.rpf/x64/levels/mplowrider/vehiclemods/fac_supermod1_mods.rpf
    ShaUnpack("2c5fa84fb1d5da437b1c4df978a980bf052c483e5bca833c472c73935dcfc93e"), // dlc.rpf/x64/levels/mplowrider/vehiclemods/faction2_mods.rpf
    ShaUnpack("f8a85c3ead67e7a379f55b7e50107ddbfb60e8ed5ff5e5c94b6ac310815200f3"), // dlc.rpf/x64/levels/mplowrider/vehiclemods/moon_supermod1_mods.rpf
    ShaUnpack("3f1d160a542f91267fb572aa0b12c13fec24e11fc91973e0c500e013655935b7"), // dlc.rpf/x64/levels/mplowrider/vehiclemods/moonbeam2_mods.rpf
    ShaUnpack("87eb802e39fa8997f961bcd83b7b19e37b9316d3578ef3b015228dec54528e74"), // dlc.rpf/x64/levels/mplowrider/vehiclemods/pri_supermod1_mods.rpf
    ShaUnpack("ad6c7af97d84ae40d4592d4b8e93d88a0294cefae361e3cfd5316db50e378f07"), // dlc.rpf/x64/levels/mplowrider/vehiclemods/primo2_mods.rpf
    ShaUnpack("888db419f581594c42618403b46c95f2eae5b7621b47e05045be97da888a0800"), // dlc.rpf/x64/levels/mplowrider/vehiclemods/smod1wheels_mods.rpf
    ShaUnpack("3d81d6ddf816820445523e4fa6c65ae6e1362067d98cc833e40a9290ee466a4b"), // dlc.rpf/x64/levels/mplowrider/vehiclemods/supermod1_mods.rpf
    ShaUnpack("f98882e1571c248a46d29036fc51602296ebe07de683f815c543ce3e1b63fda8"), // dlc.rpf/x64/levels/mplowrider/vehiclemods/voo_supermod1_mods.rpf
    ShaUnpack("2e5e4407c0dd6deb2093745e3c71ac32b1621e9d2a606b3ad4f81ed63e9f31b0"), // dlc.rpf/x64/levels/mplowrider/vehiclemods/voodoo_mods.rpf
    ShaUnpack("2a16e4cb4bc8dfcb7c53ca22a80501621156e1f365424ef4644d41cfdb9245c8"), // dlc.rpf/x64/models/cdimages/mplowrider.rpf
    ShaUnpack("ef6f524e6fa10d5adedac81b5fde93c5b47a4e551a1bdd0c5bd47fd834addb6e"), // dlc.rpf/x64/models/cdimages/mplowrider_female.rpf
    ShaUnpack("2f7a9c2305244a944d99900af70da6d7f2e02c3292dbef4a46e828622ac34ea4"), // dlc.rpf/x64/models/cdimages/mplowrider_female_p.rpf
    ShaUnpack("b1f95e510798a1db49191e3da7c81e90434ca2599b4985ab7ed637369ae5e9b9"), // dlc.rpf/x64/models/cdimages/mplowrider_male.rpf
    ShaUnpack("a0812c64e1656547d2a9644cd93c0002b24b9fcb0f421a26603eb82896888f86"), // dlc.rpf/x64/models/cdimages/mplowrider_male_p.rpf
    ShaUnpack("1ec8a0543b40652838323f33cf268266ebe3e02209412a00af561e843526b752"), // dlc.rpf/x64/models/cdimages/mplowrider_p.rpf
    ShaUnpack("e9fb593b7134eb51a690aa0229b2ac0255476fd9591398174c8804f1bf212a47"), // dlc.rpf/x64/models/cdimages/mplowrider_ped_mp_overlay_txds.rpf
    ShaUnpack("730155bd9f9e64ebfa50684c211ae340cba04b9c270a567dc15aa2e5c7d492bd"), // dlc.rpf/x64/models/cdimages/weapons.rpf
    // update/x64/dlcpacks/mplowrider2/dlc.rpf
    ShaUnpack("c9b1b4d6a3300f810a49a35c46bec3b595d4df5b407664743c226c50261d53de"), // dlc.rpf
    ShaUnpack("4f242884703abdd3576fec7c49f00b6d37b093c07aa2cfcc556a82f6cebf6b72"), // dlc.rpf/x64/anim/creaturemetadata.rpf
    ShaUnpack("f5078c531a937e5caea7c7d9e2a3b372eb750ba7cf6bf820168b601808bd40ba"), // dlc.rpf/x64/anim/ingame/clip_anim@.rpf
    ShaUnpack("c9e5eeded0e0a882ddef582ea5d8f3184d4bf1100dd904cbd6b626a1e054921d"), // dlc.rpf/x64/data/effects/ptfx.rpf
    ShaUnpack("2ed35f5a688e2ceebd81053a46042ab3d126d4653eecc1977d6830feb53b6298"), // dlc.rpf/x64/data/lang/americandlc.rpf
    ShaUnpack("b63b4a23ee652387300eca3a63e3fa5ee42b1cbc23d99f60e9ac95961376e342"), // dlc.rpf/x64/data/lang/chinesedlc.rpf
    ShaUnpack("d5f758a8a1b61e2645047b357a4e06b2d3e1193d8b2a7e504b605efd68630d60"), // dlc.rpf/x64/data/lang/frenchdlc.rpf
    ShaUnpack("1d934dadb35df08c7eac7dc1363d031010ac781ee956a26854e600e4f8e1ee9c"), // dlc.rpf/x64/data/lang/germandlc.rpf
    ShaUnpack("b0fbc0755136d3e595adc002ab55307a231e47b88b10166b1aa80987b152018d"), // dlc.rpf/x64/data/lang/italiandlc.rpf
    ShaUnpack("fc7959efef81741b49ae0d6f2aeaba633ec49634e54d334bcbb1ae2e25f5fa85"), // dlc.rpf/x64/data/lang/japanesedlc.rpf
    ShaUnpack("78e6183ed2bf61342745cc5c7bc0f8d40b8ce38eb32957b792e97f374a0796aa"), // dlc.rpf/x64/data/lang/koreandlc.rpf
    ShaUnpack("92c4bf8ae42a412cc58312e5d3facc47424f0f05ead344c028c833187104d5c1"), // dlc.rpf/x64/data/lang/mexicandlc.rpf
    ShaUnpack("22d4c633de0c75c38f374b3be5ffeb24cfc4da6947f9f2864a475fdb7b7a572f"), // dlc.rpf/x64/data/lang/polishdlc.rpf
    ShaUnpack("d690ee478b639cbae9905e56bfdff528cc7c99f1dd312542b2504ff364e01cfd"), // dlc.rpf/x64/data/lang/portuguesedlc.rpf
    ShaUnpack("006cb24fb0a211f8622c92c672451003bee51e78440d260875c8090421f68e78"), // dlc.rpf/x64/data/lang/russiandlc.rpf
    ShaUnpack("bd51e2d2b1b1a45e47ee586bf2da226f1d287383a9bdd7fa23b221971f87300f"), // dlc.rpf/x64/data/lang/spanishdlc.rpf
    ShaUnpack("392486ea9f7f1503bcd693095c81465fde4c3ea343c6c74be91f573a8e81b9a6"), // dlc.rpf/x64/levels/gta5/props/prop_gc_grenades.rpf
    ShaUnpack("3738870d0918a930d8aa39185a6379914d3b0db041ac1819ee78c9370dcfb1c5"), // dlc.rpf/x64/levels/gta5/props/prop_ibi.rpf
    ShaUnpack("9209de4ac39e8982e6f6c41f4c15f1ab23bc27b65f640b6dddf568b0310e9a33"), // dlc.rpf/x64/levels/gta5/vehicles/mplowrider2vehicles.rpf
    ShaUnpack("8e687be097d351454012749f7338af8a7f8fd9a1a48c712c9d555e0477df10b6"), // dlc.rpf/x64/levels/mplowrider2/vehiclemods/faction3_mods.rpf
    ShaUnpack("71e260941e346ec427982f0c0b285e21f23c5725a060ded7f2adeeb4c3b70ced"), // dlc.rpf/x64/levels/mplowrider2/vehiclemods/minivan2_mods.rpf
    ShaUnpack("eaeb655f2dffc1e1bccf4945bbebf179645e58e9dc1d6026aa0efdb5ca5c90d6"), // dlc.rpf/x64/levels/mplowrider2/vehiclemods/sabregt2_mods.rpf
    ShaUnpack("7830c4be3764809e3f7468db604806635d85715c0f8343ff1e5dd65acbce58a2"), // dlc.rpf/x64/levels/mplowrider2/vehiclemods/slamvan3_mods.rpf
    ShaUnpack("ec27cc51721267f6191af44122729a57e7395db94c33c5e0a53d680a2bcd7d7a"), // dlc.rpf/x64/levels/mplowrider2/vehiclemods/smod2wheels_mods.rpf
    ShaUnpack("4820e72903868bcd40f618b3c76bf22504a0705f2f2c9f85ad8f854496cb1c5e"), // dlc.rpf/x64/levels/mplowrider2/vehiclemods/tornado5_mods.rpf
    ShaUnpack("24b24eb842da46de426513fd9b8e5b11ca6b16808464c3f0fa7768ec7808f6e0"), // dlc.rpf/x64/levels/mplowrider2/vehiclemods/virgo2_mods.rpf
    ShaUnpack("fd2e58d244ca6699de2ab107514402e74efa22f56789896d9b71c2647a1c54db"), // dlc.rpf/x64/models/cdimages/mplowrider2_female.rpf
    ShaUnpack("8b6227d575568a46b6b79d3b92693d53c50d3aedcd2a44c3d22a66e17d7ac645"), // dlc.rpf/x64/models/cdimages/mplowrider2_female_p.rpf
    ShaUnpack("fb5177ea5a6e58131e6ed2878174ee07a8366ea23b6ac66d7252874039e676ac"), // dlc.rpf/x64/models/cdimages/mplowrider2_male.rpf
    ShaUnpack("52099fae89541bbe115dc0eebc80aee7642b0a4666284a075884e9de3fa14a03"), // dlc.rpf/x64/models/cdimages/mplowrider2_ped_mp_overlay_txds.rpf
    ShaUnpack("49d4c8ec82a1e9ae28b5e7f0acaac20d76cd074f7bc3bb1534fe546c478b9eb4"), // dlc.rpf/x64/models/cdimages/weapons.rpf
    // update/x64/dlcpacks/mpluxe/dlc.rpf
    ShaUnpack("e606262fe540dd193dcb5efa61f56668161cc1898df2ee71a43b23d8e5dfdf8a"), // dlc.rpf
    ShaUnpack("f7a9a84fe59795e3d58afc57e6aa47aec2d20d445214ff4ef1f0e529cee48904"), // dlc.rpf/x64/anim/creaturemetadata.rpf
    ShaUnpack("d9cfeac1fa6d58968593740d3323849b99047ca6d014e940d4bcecb1c9a33fb6"), // dlc.rpf/x64/anim/ingame/clip_anim@.rpf
    ShaUnpack("2ae778e8ddeb094ba3585e7c10a646d33a3934ac531fe4380b990f9809c38d09"), // dlc.rpf/x64/data/effects/ptfx.rpf
    ShaUnpack("929a30af86e19b2228e222809f297b359d4cae6cb0dea3a8830394ded02afc7b"), // dlc.rpf/x64/data/lang/americandlc.rpf
    ShaUnpack("22e7630aa96b810a53eec2da586e2b23412c62e87b302420f1dc4b77b3fa0a8a"), // dlc.rpf/x64/data/lang/chinesedlc.rpf
    ShaUnpack("e2c8b55a9bf9430ef998837b980541c7783cdb210cef03c61ca8c4ae9d4f5ba6"), // dlc.rpf/x64/data/lang/frenchdlc.rpf
    ShaUnpack("49ccd13e635a0015341d11f3ca7619f95b091e578716968908ffa287b6afb0cb"), // dlc.rpf/x64/data/lang/germandlc.rpf
    ShaUnpack("754659fc6a36032856680dea17fa30d2825193e4d9cf387ee069dc15b05ebe0c"), // dlc.rpf/x64/data/lang/italiandlc.rpf
    ShaUnpack("7008de4bca63f2f49202a7061445eebfa22751b00b40f2677635d47c6dbe6250"), // dlc.rpf/x64/data/lang/japanesedlc.rpf
    ShaUnpack("4d1c5233919ff73dc95c71b7c584bd7650777d801ca98ec2622ba37302251036"), // dlc.rpf/x64/data/lang/koreandlc.rpf
    ShaUnpack("fa6b4c095ee2562b2fc0ab38fe78a68f56c8dd7873e4c339628de33cf4652aab"), // dlc.rpf/x64/data/lang/mexicandlc.rpf
    ShaUnpack("6c1c913adb8d021dca2b354f8c335d526af775207e780fa5e34636ba7d1d4865"), // dlc.rpf/x64/data/lang/polishdlc.rpf
    ShaUnpack("9675cbcd61f9f6ebe4956f2f34a5e47e150a758b446532234bd0fb9f008c096a"), // dlc.rpf/x64/data/lang/portuguesedlc.rpf
    ShaUnpack("421665b5a28f3faa724d3f2317ae13f78465d2e74d59c09b26d9c4556f88ffb6"), // dlc.rpf/x64/data/lang/russiandlc.rpf
    ShaUnpack("ca188651a8008fee592b9d6f34642b909e759ce180364bce6a1e043894c4efef"), // dlc.rpf/x64/data/lang/spanishdlc.rpf
    ShaUnpack("50dc0a07fa959c9f9dd8d8313bac9fc8550e1e6d4227199b4f99cbd25ace540a"), // dlc.rpf/x64/levels/gta5/props/luxe_props.rpf
    ShaUnpack("0cae71164c3b2840129c1d088bb92ff2374dc2495ca195a960bc38d7722404eb"), // dlc.rpf/x64/levels/gta5/props/p_champ_flute_s.rpf
    ShaUnpack("701bf55d284c7624bd44a5d8d16849cc4400addf516b139f839d440fa9cd73df"), // dlc.rpf/x64/levels/gta5/props/p_pour_champagne_s.rpf
    ShaUnpack("4275dbb2f80b0c2bb3cf2a1ee9298960bace1fefdb90c72528627602f70eb9fb"), // dlc.rpf/x64/levels/gta5/vehicles/mpluxevehicles.rpf
    ShaUnpack("823757ea31419739b89ba6f21cb20ae7ddaef2c34315d46b3c50d723fef48a9c"), // dlc.rpf/x64/levels/mpluxe/vehiclemods/feltzer3_mods.rpf
    ShaUnpack("8977f56a534d0fe16d37f3616b516bada60d12580d4943a5ebad449924477d6c"), // dlc.rpf/x64/levels/mpluxe/vehiclemods/osiris_mods.rpf
    ShaUnpack("0360ecc545a0ee2e5259582c1de9c9ffcbadda41a3295fd5bb972269e10bfdf7"), // dlc.rpf/x64/levels/mpluxe/vehiclemods/virgo_mods.rpf
    ShaUnpack("f72f671c3c96201596484165778cd424227040c3478872975ac854ba51188bf4"), // dlc.rpf/x64/models/cdimages/mpluxe_female.rpf
    ShaUnpack("8b2fdf3b33d3eb18c025e68e9d1105b9276eb2b52dbbd7186832f5663a4944a3"), // dlc.rpf/x64/models/cdimages/mpluxe_female_p.rpf
    ShaUnpack("ca6db74aeac2d25ead7ce5f34153c670e74d6437a184a4d08239c8223baacd40"), // dlc.rpf/x64/models/cdimages/mpluxe_male.rpf
    ShaUnpack("2079f04e354e54a8574d9063fb5e3f09b933cf95f7941770e8e25eeaea3500b6"), // dlc.rpf/x64/models/cdimages/mpluxe_male_p.rpf
    ShaUnpack("c2885b256d8170d07e55d19c48eef7bfa941289d63f3600fb056516d851018f5"), // dlc.rpf/x64/models/cdimages/mpluxe_ped_mp_overlay_txds.rpf
    ShaUnpack("0732bde6e98ba1904be677f55cd2aebc93a340c6ffd1a592fde9a9b3be40ef4f"), // dlc.rpf/x64/models/cdimages/weapons.rpf
    ShaUnpack("638c4821e9a38b8c87478c072645b8d387560dda11c8da7925af142ce624ecd7"), // dlc.rpf/x64/patch/data/lang/american.rpf
    // update/x64/dlcpacks/mpluxe2/dlc.rpf
    ShaUnpack("10284b7b86c2f392cb53cab600bd87b6cb391a85bd28398c5cc5858e8e92a920"), // dlc.rpf
    ShaUnpack("686d57e89758501495d8fc7204bb350babd090e7e41b1573e7c972f6d410f1bc"), // dlc.rpf/x64/anim/ingame/clip_anim@.rpf
    ShaUnpack("e555d19f1bf2a6be000998da5eca6610caef367863d9cda05692f46e0d9a34e9"), // dlc.rpf/x64/data/lang/americandlc.rpf
    ShaUnpack("4e3f0de41b1b14677a45df6b393e44b647e7220b895c1e606ec28f0f1f3e94b6"), // dlc.rpf/x64/data/lang/chinesedlc.rpf
    ShaUnpack("409fd35ba6453082eef411af6c49b41d4455b5bc6b0e65eb3361a8beb91b2cae"), // dlc.rpf/x64/data/lang/frenchdlc.rpf
    ShaUnpack("8612678ff2260689e6581369002d3f773c986f673845bdac62ab8c43c2f6fbbd"), // dlc.rpf/x64/data/lang/germandlc.rpf
    ShaUnpack("4808c8998e533f1f29819cd9f640bde5dbb9d09d1ebca8ceb4f0a6b5ae22c5fc"), // dlc.rpf/x64/data/lang/italiandlc.rpf
    ShaUnpack("55fa3a4b99a2f58e499c6f82b6c8dbcbc91ad68b9fd2ee05a96d3b451696d223"), // dlc.rpf/x64/data/lang/japanesedlc.rpf
    ShaUnpack("9fa64e19d8cfc655d9c8f42cdcbad054b837dc0e0e159868250f8843db73f0ba"), // dlc.rpf/x64/data/lang/koreandlc.rpf
    ShaUnpack("133735e5fec7b80688677480bf85b8b7749c35f7ea3af39b5b35d98dfb117240"), // dlc.rpf/x64/data/lang/mexicandlc.rpf
    ShaUnpack("29716352cf77583bc43fa04369e82a151467e2975b996aacdad0cd122b1e39e8"), // dlc.rpf/x64/data/lang/polishdlc.rpf
    ShaUnpack("71ac132e0ee6996282ce5c7e5ec6899bedcfe898f0ae64a9e8f981f6ee8a2ad1"), // dlc.rpf/x64/data/lang/portuguesedlc.rpf
    ShaUnpack("9dbb48f8d9507e3343871bca496a7043b18096a2526d82a0da81b6cd9e00f9ec"), // dlc.rpf/x64/data/lang/russiandlc.rpf
    ShaUnpack("249f4db12e6400a63b5d480dbf63c3ff172a40c3c2e1438ff6d8b11a15c5c322"), // dlc.rpf/x64/data/lang/spanishdlc.rpf
    ShaUnpack("1647429664df652c96c02ac4ff2642820f626c110d3b079ac82d0ea144df1fdc"), // dlc.rpf/x64/levels/gta5/vehicles/mpluxe2vehicles.rpf
    ShaUnpack("27848b1d421386e94c0e5b6537ac530680ac43b884debb98e49c358e879956f4"), // dlc.rpf/x64/levels/mpluxe2/vehiclemods/brawler_mods.rpf
    ShaUnpack("26ff683bec904bca3f0370fbbfcb5225f0c45cb60f99f3e74a36137a5ccfe170"), // dlc.rpf/x64/levels/mpluxe2/vehiclemods/chino_mods.rpf
    ShaUnpack("2358d840883b11a0300df7c09954c59c763068548d96c2c2fe1c1e0e90c9d940"), // dlc.rpf/x64/levels/mpluxe2/vehiclemods/coquette3_mods.rpf
    ShaUnpack("0ad1d9d417f89d45da63943093a83a4e651722efde29f54f47a7c6c1b314cc29"), // dlc.rpf/x64/levels/mpluxe2/vehiclemods/t20_mods.rpf
    ShaUnpack("a88158a292a65fdfa1d3465c991bc95f23ed93517ea1246fe2a1f4b70461a4e5"), // dlc.rpf/x64/levels/mpluxe2/vehiclemods/vindicator_mods.rpf
    ShaUnpack("c36e46e2fecd2a45ae5fad1db9184f892267242e9824c553da3eb0e7529e0fdb"), // dlc.rpf/x64/models/cdimages/mpluxe2_female.rpf
    ShaUnpack("8a4951a001f7df4c530a113ded67a82e69647051b282d5c85b34e0bd2df4731d"), // dlc.rpf/x64/models/cdimages/mpluxe2_female_p.rpf
    ShaUnpack("8b5905236ed15038f2b7c721e627523765295c781d95f9846e6e2ff02807c28b"), // dlc.rpf/x64/models/cdimages/mpluxe2_male.rpf
    ShaUnpack("4227d8d7990b0837cf4ce758dcd1866139a6552eeccd354fbe0e16ee4817b09e"), // dlc.rpf/x64/models/cdimages/mpluxe2_male_p.rpf
    ShaUnpack("1e825d78f604f22c13090cd434e1321a86fc5c9bc3b67126d370ac90ae79b741"), // dlc.rpf/x64/models/cdimages/mpluxe2_ped_mp_overlay_txds.rpf
    ShaUnpack("9b40d2e59e0efe04575e889169cda418c91a6e8853383ab2e7c921ea57e708e0"), // dlc.rpf/x64/models/cdimages/weapons.rpf
    // update/x64/dlcpacks/mppatchesng/dlc.rpf
    ShaUnpack("93e584abed17f8df20ad60bac9fe9fe928746dc3801c98e476575d8f4db5e505"), // dlc.rpf
    ShaUnpack("3a033292ab01eafaf2a85993307d28d9857e535ebef170701ff50d43257a2e42"), // dlc.rpf/x64/anim/creaturemetadata.rpf
    ShaUnpack("7a5ed02cdd9fcd544f2f5c5423379e1002aca796f8fd7b9ec411f824374ce93f"), // dlc.rpf/x64/anim/ingame/clip_anim@.rpf
    ShaUnpack("ff8eb30aa9a475bc55d2958302032b22fc4704d3d0faeac5b752424cf8852290"), // dlc.rpf/x64/models/cdimages/mphipster_ped_mp_overlay_txds.rpf
    ShaUnpack("feb42f82d68ca7fb38fe3f236fd4d9aba57e5d58595b3590548ffc2b9a09032c"), // dlc.rpf/x64/models/cdimages/mppatches.rpf
    ShaUnpack("7dda7af48e31738f279cca112fe96a450a5863a69f6c3896c53727d350f098a5"), // dlc.rpf/x64/models/cdimages/mppatches_cutspeds.rpf
    ShaUnpack("44598eb75940dd50ec65e57b119b62627780bb2e275c53cadcb547587fb9178b"), // dlc.rpf/x64/models/cdimages/mppatches_cutspeds_p.rpf
    ShaUnpack("f5630451bcd781af8c98a37c438976d8311f62a71840c28656da15ceebb8b0a3"), // dlc.rpf/x64/models/cdimages/mppatches_f_outfits.rpf
    ShaUnpack("593a66f54ce3ffefb26a5d63b49e5123e024290c64a7a3b3774463da117680ba"), // dlc.rpf/x64/models/cdimages/mppatches_f_outfits_p.rpf
    ShaUnpack("9737895e331ffdb68813976f8db6c8030c1f27980a3de356b36c5a260b60ad61"), // dlc.rpf/x64/models/cdimages/mppatches_m_outfits.rpf
    ShaUnpack("708c09daac2ffe0d40ca14e5ad9b3900572545340c6714b7fc197a73af9090b3"), // dlc.rpf/x64/models/cdimages/mppatches_m_outfits_p.rpf
    ShaUnpack("e9781530e5c7f7ceabc5df08e5c4d2d12ab9b427d0cd7e28c592b4e14a67c9db"), // dlc.rpf/x64/models/cdimages/mppatches_p.rpf
    // update/x64/dlcpacks/mpreplay/dlc.rpf
    ShaUnpack("f163ec99048188e15d736b8b1ff7dfe481caac1ba574d0ad942fcf3608b629ee"), // dlc.rpf
    ShaUnpack("aceafabeb5d5e418a56107155a3dc6bed6a2d00563e3563197a64f3bdb083d51"), // dlc.rpf/x64/anim/ingame/clip_anim@.rpf
    ShaUnpack("28a4a5c45cf8d85dfd12c40c2d2a9a98dddc52762cdc29034e47736c1a6d1d43"), // dlc.rpf/x64/models/cdimages/mpreplay_female.rpf
    ShaUnpack("bf67ecad679a0496d8d787a3b68c462ed76b3b1328eab4fa010c58528d603c7c"), // dlc.rpf/x64/models/cdimages/mpreplay_male.rpf
    // update/x64/dlcpacks/mpsecurity/dlc.rpf
    ShaUnpack("b2d394f59b0f8a3d5eac9b81c27c818de60fd054f614e3f4ed11e22aa955668d"), // dlc.rpf
    ShaUnpack("994f6694cc2f96362a7e1647e2d0fe13c59129866b862100038e08c946ef8b7a"), // dlc.rpf/x64/anim/creaturemetadata.rpf
    ShaUnpack("41b2499814770446140bbd99adf392edcbd5f4b91a93ff1569b57113cd6374a3"), // dlc.rpf/x64/anim/expressions.rpf
    ShaUnpack("5d84977a232ed1a18504f04ce3f93590034979a4480b2eefdab1e875bd0861d5"), // dlc.rpf/x64/anim/cutscene/cuts_fix_agy.rpf
    ShaUnpack("b4d7a85610ef2ef08c37bcc2e043afda73b04707b040fed241cbc23d4d61ced9"), // dlc.rpf/x64/anim/cutscene/cuts_fix_art.rpf
    ShaUnpack("454da84c2b099b29d9d381d502ec50307399d5ccb54769120f04b583a7ba480b"), // dlc.rpf/x64/anim/cutscene/cuts_fix_bil.rpf
    ShaUnpack("99896a3a05c7241db2e212cdcaa51f60193a802822f42ba8b584b4767f06fb91"), // dlc.rpf/x64/anim/cutscene/cuts_fix_golf.rpf
    ShaUnpack("fdd615717158f6e4150f9e6d26f5a606087cc3261aed01b23c340168cda7d94d"), // dlc.rpf/x64/anim/cutscene/cuts_fix_low.rpf
    ShaUnpack("fd6b1e3fae0a3aca417e3cb2a7ac0005deb9f75c6aabbdf65596d3c40276e476"), // dlc.rpf/x64/anim/cutscene/cuts_fix_pro.rpf
    ShaUnpack("5fa9023580472b6e719aefb4170ce496bb2d7d4adfa3b2138c8f99419e0551ce"), // dlc.rpf/x64/anim/cutscene/cuts_fix_prod.rpf
    ShaUnpack("81f25ac8063d65f53055dabba5240afb9e88394d4dd41b7982579442f23ee47e"), // dlc.rpf/x64/anim/cutscene/cuts_fix_set.rpf
    ShaUnpack("d348f08af09467f7995807603fce4b9da297faec61ebaabe696492e6dd39d362"), // dlc.rpf/x64/anim/cutscene/cuts_fix_stu.rpf
    ShaUnpack("e339df63fbbbfb40855817e85a26d7588cf533abd323e290b862cc8088c99381"), // dlc.rpf/x64/anim/cutscene/cuts_fix_trip1.rpf
    ShaUnpack("7e5a82d2a06d2516ada2f2428560bdb9dbcacb186eb4e7ee7b6b843a3a160c7e"), // dlc.rpf/x64/anim/cutscene/cuts_fix_trip2.rpf
    ShaUnpack("b49bac1db49fa3c48d64a40efe28af747961724d523ca6ab9346925e273f9599"), // dlc.rpf/x64/anim/cutscene/cuts_fix_trip3.rpf
    ShaUnpack("33a75896f39019c9483ac7d47cc13d5bcb7e3c787be5b17b27d48634ba506fae"), // dlc.rpf/x64/anim/cutscene/cuts_fix_trk.rpf
    ShaUnpack("561a068e39e3176d8415d282961d101e6a5b12bbb3695815c66dc36c8123bbb2"), // dlc.rpf/x64/anim/cutscene/cuts_fix_weed.rpf
    ShaUnpack("fd4837c4c41b91e3a1a241e9415520a2aa47f4b4d636be8ab0aab362896818aa"), // dlc.rpf/x64/anim/cutscene/cuts_fix_wrm.rpf
    ShaUnpack("4c23d3963787508bbd5e2f247dfeaffc707736ef1f2aae811075e2b912172294"), // dlc.rpf/x64/anim/cutscene/cuts_fixf_fin.rpf
    ShaUnpack("8b2f48f538ebab9c922aa5064cfe9b9f6be20e79ffd1f61613d5d9b5e05bfd59"), // dlc.rpf/x64/anim/ingame/clip_anim@.rpf
    // update/x64/dlcpacks/mpsmuggler/dlc.rpf
    ShaUnpack("2527cfa62528975ea5798fa783e8fcb6103ba05114b99788236eeb4218dfb8dd"), // dlc.rpf
    ShaUnpack("8cfccec5e6b022915a00d9ffe4bbdf54102696b6639d1a3ec7b3920dfbb6196c"), // dlc.rpf/x64/anim/creaturemetadata.rpf
    ShaUnpack("453fc62c79cb24fcaddb8f0d98a347d756468fb40ff516fa06112db56cd3d555"), // dlc.rpf/x64/anim/cutscene/cuts_hang.rpf
    ShaUnpack("4b2b536cf948a558aaf94e235fe3b71a92b69a3d6e6a1fc69f47590a03eecd96"), // dlc.rpf/x64/anim/ingame/clip_anim@.rpf
    ShaUnpack("f773de069478de010cf4ebc486549b138c66b63e80cdbd66d7b061ab42943875"), // dlc.rpf/x64/data/effects/ptfx.rpf
    ShaUnpack("2a0ada8aa07cd4886fe3955fbe640e0c0c8ceaf75c5a5847e804e415c0333d4f"), // dlc.rpf/x64/data/lang/americandlc.rpf
    ShaUnpack("299bf433c146e156da3dde8904e0669f874b9dbd5adc412299ae00666fc7ac56"), // dlc.rpf/x64/data/lang/chinesedlc.rpf
    ShaUnpack("51c3207e10317dc7dab0ac3ba481c8bb6f1a73a9cb54440f10da86408b87a807"), // dlc.rpf/x64/data/lang/frenchdlc.rpf
    ShaUnpack("355c6dc1a667c8da9215ceb9cf51ae3a8b00469a7f3d1011d67554c6a7354712"), // dlc.rpf/x64/data/lang/germandlc.rpf
    ShaUnpack("554634e16a60b13f3d3dcd51b5b403633416eb46116109f7063af687c21f9101"), // dlc.rpf/x64/data/lang/italiandlc.rpf
    ShaUnpack("dae3c714b9917540ddb7aab5fa130416a5e89b82490d83132887d65c634735c5"), // dlc.rpf/x64/data/lang/japanesedlc.rpf
    ShaUnpack("897ef75dc187354a6bfb45d77018ec8fc04c13cead6d8fa9e93dc774507d228c"), // dlc.rpf/x64/data/lang/koreandlc.rpf
    ShaUnpack("ecb9d19d817f3ac4acc893ab0a729546eb0f60f92f518f871a3bc8459f43bade"), // dlc.rpf/x64/data/lang/mexicandlc.rpf
    ShaUnpack("34f8f32c4c637a87f6ef04583e74483b852605dfd4410369dcb85bb8757f2d81"), // dlc.rpf/x64/data/lang/polishdlc.rpf
    ShaUnpack("2647d339d6e21663d1cc5d635a541ae99f09b810eec68379ebaee6a7fff5ac02"), // dlc.rpf/x64/data/lang/portuguesedlc.rpf
    ShaUnpack("2238675a874f3519cbc439ada104423127aff298cb65e4e86109645204d36d7d"), // dlc.rpf/x64/data/lang/russiandlc.rpf
    ShaUnpack("8e9509d952d1f53e21bb7f25f7adfb85564bf111172be4d8d122364ff6c4f5f4"), // dlc.rpf/x64/data/lang/spanishdlc.rpf
    ShaUnpack("22001bc876531ba30f0426c7e9d278751f6082096b83b659b3d6cabdebd57060"), // dlc.rpf/x64/levels/gta5/interiors/smugdlc_int_01.rpf
    ShaUnpack("95d78dc112d7e34917c745ac8cb1ab023f3c38b9ddca308e126a129a6a42b288"), // dlc.rpf/x64/levels/gta5/interiors/smugdlc_interior_placement.rpf
    ShaUnpack("aa6ea11446ff19fe43d2427d85d75b15c8ab69ddacbc2bf96e2c485839d58834"), // dlc.rpf/x64/levels/gta5/props/prop_smug_accs.rpf
    ShaUnpack("739798411218e4e9acecdfbde479403d986f807980b621b70e97973a3461968a"), // dlc.rpf/x64/levels/gta5/props/prop_smug_cont.rpf
    ShaUnpack("16543c53a8032fdc457c8266213f5728add2c9b64a4d44c6ffa8436532d1cbc6"), // dlc.rpf/x64/levels/gta5/props/prop_smug_cranes.rpf
    ShaUnpack("4dbce4fd9e6e41573f396a12eb915bacbe1f79b406f7e077b524aa5ff1e34994"), // dlc.rpf/x64/levels/gta5/props/prop_smug_crates.rpf
    ShaUnpack("9b14c75c420983747e549c2e2c3818c6719e1161de0b8d6a2145806c12ac8e08"), // dlc.rpf/x64/levels/gta5/props/prop_smug_crates_01.rpf
    ShaUnpack("c41a4b5ddd6c932ecdf2f6005838cde6b14684a0b051df3b04ceb77369cd57e2"), // dlc.rpf/x64/levels/gta5/props/prop_smug_crates_des.rpf
    ShaUnpack("d4e20f83ac26095df98292769dd3e229ceccf2c945797fb133e69c99c93dcfe0"), // dlc.rpf/x64/levels/gta5/props/prop_smug_heli.rpf
    ShaUnpack("1436b536b058b6b9490829b44b9a784c8b783bae314f285840fffa6e55af5edc"), // dlc.rpf/x64/levels/gta5/props/prop_smug_hgrdoors.rpf
    ShaUnpack("038ccf1d11315ebc945a50587d04476bd6692e117c6415050243f1b643c883cb"), // dlc.rpf/x64/levels/gta5/props/prop_smug_lights.rpf
    ShaUnpack("00e716897481b492d94a2d3139789a18000dbdeab5a710a348a45ffb30dbecb9"), // dlc.rpf/x64/levels/gta5/props/prop_smug_shadows.rpf
    ShaUnpack("a1fd1c9be77bcfe10bc42933bb75d8c8caa2da258266f84f1c88a5ea3b3762c4"), // dlc.rpf/x64/levels/gta5/props/smugdlc_interior_props.rpf
    ShaUnpack("be0cc12bccaa132cf3d9e3b1f409d5e805fd36c04827738a6aae6eae17c3f478"), // dlc.rpf/x64/levels/gta5/vehicles/mpsmugglervehicles.rpf
    ShaUnpack("6fbc804ba21883cf31462ba673908409ae640d329c4fe73180daa8e4a3d57d49"), // dlc.rpf/x64/levels/mpsmuggler/vehiclemods/alphaz1_mods.rpf
    ShaUnpack("1d605a81fa5d7963b3d05379705439f09cdb61dc23ae093ec6c2fd727ca0b625"), // dlc.rpf/x64/levels/mpsmuggler/vehiclemods/bombushka_mods.rpf
    ShaUnpack("f30125f73ec2f92f125e6f335eb49e13f77b78e35388cef5f0e456837f3e3379"), // dlc.rpf/x64/levels/mpsmuggler/vehiclemods/cyclone_mods.rpf
    ShaUnpack("34958d26144bebf1853f46de0f857fb942d5b6dff7dc34b6af872d39072e8e92"), // dlc.rpf/x64/levels/mpsmuggler/vehiclemods/havok_mods.rpf
    ShaUnpack("44627725d105bfb8d5f2f46d6060b6d3b6db1c0c6604c4bd57c1d59b8826123a"), // dlc.rpf/x64/levels/mpsmuggler/vehiclemods/howard_mods.rpf
    ShaUnpack("8fcde7571a443dcfe75ce5a749a6dcfeb425567f5e5ba3a0a00afbe706a7d5f4"), // dlc.rpf/x64/levels/mpsmuggler/vehiclemods/hunter_mods.rpf
    ShaUnpack("5338c4d1832dae2406a270ba30ad0c2abde243ff8eb2aa0306d4c24a46ac4e56"), // dlc.rpf/x64/levels/mpsmuggler/vehiclemods/microlight_mods.rpf
    ShaUnpack("7e9714f46c9a027e24f6c1985a9f40b13360fc506fb94cf8674187dc01a7017b"), // dlc.rpf/x64/levels/mpsmuggler/vehiclemods/mogul_mods.rpf
    ShaUnpack("ca3da5a9c54e795fe44af8b3f427e0c6ef0eeed236020018e205eb55855e1b5d"), // dlc.rpf/x64/levels/mpsmuggler/vehiclemods/molotok_mods.rpf
    ShaUnpack("46fdf4298aab2290e2826a7510201d76ee021ede27cdb0ba762a5e7ef43acb7d"), // dlc.rpf/x64/levels/mpsmuggler/vehiclemods/nokota_mods.rpf
    ShaUnpack("63a7b82f32045d1b1e6ebe3cae9a88d452cca36382df06cde757d9436c8d5f93"), // dlc.rpf/x64/levels/mpsmuggler/vehiclemods/pyro_mods.rpf
    ShaUnpack("310660db79e26958df0088ac78810c14c22736dd651bf356c3e14073b31f80b8"), // dlc.rpf/x64/levels/mpsmuggler/vehiclemods/rapidgt3_mods.rpf
    ShaUnpack("5727d48e68ea07a4f1167823aa3f70e5cb4dc3272fb24762d6fe71913bc80dca"), // dlc.rpf/x64/levels/mpsmuggler/vehiclemods/retinue_mods.rpf
    ShaUnpack("b456d2edff970b1d4aee574f1decd6d2c9c96c7810a717d69ec4b33288390c10"), // dlc.rpf/x64/levels/mpsmuggler/vehiclemods/rogue_mods.rpf
    ShaUnpack("3c26a366b11cc9f43217808e943d371502624c076581e841b2300c909b3574fc"), // dlc.rpf/x64/levels/mpsmuggler/vehiclemods/seabreeze_mods.rpf
    ShaUnpack("b969b17e0e027f6c0d63ad78ec160f2c3665361de25c5e049643738fcba3d311"), // dlc.rpf/x64/levels/mpsmuggler/vehiclemods/starling_mods.rpf
    ShaUnpack("dbc3fe26632d80b40889dc8ea9bb3c6c09c98ac1eedf20bf4ede5dfe1dab0a96"), // dlc.rpf/x64/levels/mpsmuggler/vehiclemods/tula_mods.rpf
    ShaUnpack("bb146e26a1089231f98f60cd87e5c71abba76d412d2ebdf2999c0cd97781e195"), // dlc.rpf/x64/levels/mpsmuggler/vehiclemods/vigilante_mods.rpf
    ShaUnpack("91f9bfb2a1fb84fae0be68475594f51e66bef0cc8c701cfbb8e3a868f60f7bbb"), // dlc.rpf/x64/levels/mpsmuggler/vehiclemods/visione_mods.rpf
    ShaUnpack("c2a9249a1913e2d59252b97ebe7b592bc2a83ee4dd1cfe7d29fcb78e1d14d322"), // dlc.rpf/x64/models/cdimages/mpsmuggler_female.rpf
    ShaUnpack("0447626e71941ed67a1224d76628be31bbd2cb02832d1d2ac97ba22aa13b361b"), // dlc.rpf/x64/models/cdimages/mpsmuggler_female_p.rpf
    ShaUnpack("621b8fbae0ffc3bd26a2e63796d0090fce8c9dab06663650a685abd2ec9b0ea6"), // dlc.rpf/x64/models/cdimages/mpsmuggler_male.rpf
    ShaUnpack("5ff80b1a9e886f956542bd74a36b123d3f8cc9390a347e8d5ea83af8363f5686"), // dlc.rpf/x64/models/cdimages/mpsmuggler_male_p.rpf
    ShaUnpack("22f2cd5cd47bd0342b4d9b2fa8863d79085e00bb6e6119b505624c60bb1a81a8"), // dlc.rpf/x64/models/cdimages/mpsmuggler_ped_mp_overlay_txds.rpf
    ShaUnpack("916e1f887d4d93b35cfd5ac089a3206657a7ed2972d47b0116bdd84e2de762c4"), // dlc.rpf/x64/models/cdimages/peds/mpsmuggler.rpf
    ShaUnpack("0c494cb1dfb02d3ee9cb9e2b980d03eb9152571272c0a572e3e5e249315f8832"), // dlc.rpf/x64/models/cdimages/peds/mpsmuggler_p.rpf
    // update/x64/dlcpacks/mpspecialraces/dlc.rpf
    ShaUnpack("04b177d82193be26f6a1d15cbbdf5913c9d16ee67cf9735a7e26aeb77655e941"), // dlc.rpf
    ShaUnpack("509ebb53c0e43531f4d75ec4c01d05db8efac6aa19f577f10079976a9ee0bdac"), // dlc.rpf/x64/anim/ingame/clip_anim@.rpf
    ShaUnpack("2a889d87e8a8989d638617244d1b974a99b01473cb8e8bbe6bf3568cca6141fb"), // dlc.rpf/x64/data/effects/ptfx.rpf
    ShaUnpack("4a4621ed6cea3a187cdba0ce739e35ae4626b93856bcc502cf9f98601b1fd1b7"), // dlc.rpf/x64/data/effects/ptfx_lo.rpf
    ShaUnpack("182390eb75a399ee35b8b099b839c8ea3d71aafee845f755711babb3b3bfb27f"), // dlc.rpf/x64/data/lang/americandlc.rpf
    ShaUnpack("1e1f6b32e01b16c6cf16a0c652808c23954927d059eab1388d5928752b681489"), // dlc.rpf/x64/data/lang/chinesedlc.rpf
    ShaUnpack("571941e563422aa3ae1f2b60d52da813ba4788271f3a2d483f4eb89b608f1c4c"), // dlc.rpf/x64/data/lang/frenchdlc.rpf
    ShaUnpack("92319753f695c183e60ea1e06e32f475faacff18aba7d42c62d125eb0dbfd316"), // dlc.rpf/x64/data/lang/germandlc.rpf
    ShaUnpack("f91e59ea75c1975a76e558fd7de95d263db519d0032e91fa382716cb24017fa8"), // dlc.rpf/x64/data/lang/italiandlc.rpf
    ShaUnpack("6978512d85927d625bb8ff05ec01b9d0355d115e6f0c4d1c56e85751b72c089d"), // dlc.rpf/x64/data/lang/japanesedlc.rpf
    ShaUnpack("e5db1462264bde76a534b02e0fd8e8ac649490c14f9eefe85dd5fa13e89373c8"), // dlc.rpf/x64/data/lang/koreandlc.rpf
    ShaUnpack("1e0670674dccc629f9e72fb23d8f4f0162fdcc2daa8250eb043824e0df641390"), // dlc.rpf/x64/data/lang/mexicandlc.rpf
    ShaUnpack("1cf2311482f85de9d19b9fc382db4d84a96c4697f00a8427ed9073590e605455"), // dlc.rpf/x64/data/lang/polishdlc.rpf
    ShaUnpack("3c0763b9469f6b8968ea21de3b09ac8715a809f88de323632b5c05b1ad89c8a5"), // dlc.rpf/x64/data/lang/portuguesedlc.rpf
    ShaUnpack("5bc3e5fbd01cdf40497e359724575669f84ce536582f9dbee2f11c945e8674d7"), // dlc.rpf/x64/data/lang/russiandlc.rpf
    ShaUnpack("120ee9fb7be0a392c86ec0ae6fe78df2ed8391768b4f878892373826b2ccf4fb"), // dlc.rpf/x64/data/lang/spanishdlc.rpf
    ShaUnpack("939a1f912e965cb6930de579e3f9e5277886a8b59f4f654893102725d5b51b26"), // dlc.rpf/x64/levels/gta5/props/mp_specraces_blocks.rpf
    ShaUnpack("f57309abb6d545ff10e3dbe06de7e12e3d8c258d62addcd5e34cd112160f4163"), // dlc.rpf/x64/levels/gta5/props/mp_specraces_crates.rpf
    ShaUnpack("bd753a552c3b8b98dfd170d2d948edf2ebc304a01f13b5a6731c40e3d285b776"), // dlc.rpf/x64/levels/gta5/props/mp_specraces_landing_pads.rpf
    ShaUnpack("dbb489ce37f6a8c527cdc7fb78764a47b6da69e5271630f87f037cdc6647ce3a"), // dlc.rpf/x64/levels/gta5/props/mp_specraces_obstacles.rpf
    ShaUnpack("31d1d1b5b1bea88b7796fef9235905a782ac9ccc9f333871b0b8a734165259fb"), // dlc.rpf/x64/levels/gta5/props/mp_specraces_pads.rpf
    ShaUnpack("e1ac4172f834f955788db466c75db4a6cfcb8ea2b0d7449d0a25de91e905c20d"), // dlc.rpf/x64/levels/gta5/props/mp_specraces_para.rpf
    ShaUnpack("a8d89d1f9a74c48ee6b53958fd2dd20c36718a2718adcaa0653dba5576f9fb0e"), // dlc.rpf/x64/levels/gta5/props/mp_specraces_para_01.rpf
    ShaUnpack("50567d876d05220114c875bc28467f3564f8536c8f8a7f09f6a98621f0ca3001"), // dlc.rpf/x64/levels/gta5/props/mp_specraces_signs.rpf
    ShaUnpack("d09411ea7940b1dd90cec2d58815d641d3b6da1145875ba68fb750fb4907f638"), // dlc.rpf/x64/levels/gta5/props/mp_specraces_track.rpf
    ShaUnpack("88e71a60340852062b834b2fcefb972a91af4c1991b78d5240bde5c2223b5f06"), // dlc.rpf/x64/levels/gta5/props/mp_specraces_tubes_neonrd.rpf
    ShaUnpack("8b2ec4a0e1b56655ee0b9f2ae3603066dd040d3a3af37c46e6c333158eb6acd3"), // dlc.rpf/x64/levels/gta5/props/mp_specraces_tubes_rings.rpf
    ShaUnpack("13d5d6c22784696a2342d4b7b0cc47aec7da6bd6b7df41af863e53cc9b98a3f1"), // dlc.rpf/x64/levels/gta5/props/mp_specraces_tubes_spirals.rpf
    ShaUnpack("27c43dcdf480c4857ff1d0cec5d68ec191385ec12fe51c1fdea3fcb1ca25d19b"), // dlc.rpf/x64/levels/gta5/props/mp_specraces_tubes_stripes.rpf
    ShaUnpack("c687e253e2178e886415d493d08cb6df64aea85d71862206be04c2e82b922a26"), // dlc.rpf/x64/levels/gta5/props/mp_specraces_tubes_waves.rpf
    ShaUnpack("9339370d81f3d394564d230490ed48302182e747b8d4a4b156339552ecec8b93"), // dlc.rpf/x64/levels/gta5/vehicles/mpspecialracesvehicles.rpf
    ShaUnpack("d3f35b3bbb8da18d8b0f550c3490c226e68098cbbffe7b369b1e270c7534a5bd"), // dlc.rpf/x64/levels/mpspecialraces/vehiclemods/gp1_mods.rpf
    ShaUnpack("dff5d0f5a2cee98eead0dd2b107869b0a2f18619ce1ad6234122f1a867054974"), // dlc.rpf/x64/levels/mpspecialraces/vehiclemods/infernus2_mods.rpf
    ShaUnpack("41db1fb91e2fcd0a149519dab7e4f67bc4f45c20206030c374ef14660422e2ff"), // dlc.rpf/x64/levels/mpspecialraces/vehiclemods/ruston_mods.rpf
    ShaUnpack("6dcc54c91d8a2a6cecab8ad5024c780c9c63d0b8850c56b180ceeb0f083e791b"), // dlc.rpf/x64/levels/mpspecialraces/vehiclemods/turismo2_mods.rpf
    // update/x64/dlcpacks/mpstunt/dlc.rpf
    ShaUnpack("ef2228281f1c7a382adad7b2d6e3f326bf95fc25ff75d63f0005cd9c6861cf4f"), // dlc.rpf
    ShaUnpack("422d2b8d3b78ba6b448f729a050a6d5926e0d84bbc7e8eeb9894005bd1858fe0"), // dlc.rpf/x64/anim/creaturemetadata.rpf
    ShaUnpack("d8fc7eeabb1539de783f60c243606278bfadf66b56136738781320f166809f4d"), // dlc.rpf/x64/anim/ingame/clip_anim@.rpf
    ShaUnpack("21470f311c6ca7e9e5c9f82c3fbbc58cced412ae800f850523d0b80ec8aa2f32"), // dlc.rpf/x64/data/effects/ptfx.rpf
    ShaUnpack("b570348eeecd819a27d2ba6e2bd65970fed2295a67603bdabea554dfce6d96b8"), // dlc.rpf/x64/data/lang/americandlc.rpf
    ShaUnpack("837e071bd17e4013518d20f10656740baccd9fef831cb0f2078c4a3af6d6cd6c"), // dlc.rpf/x64/data/lang/chinesedlc.rpf
    ShaUnpack("a02f319bf981737bcafd2fbb6f51a9e7bedc05aaecaee522d774c4245d7aa28d"), // dlc.rpf/x64/data/lang/frenchdlc.rpf
    ShaUnpack("0e2df7b8e02a9d3d719de8cb7dbd93d28b0f7a17be2997ceb0e275a45bd14153"), // dlc.rpf/x64/data/lang/germandlc.rpf
    ShaUnpack("070418f54d1ab87eec89c9db8edb086523cfc25200d457e782e5a4c56085c8dc"), // dlc.rpf/x64/data/lang/italiandlc.rpf
    ShaUnpack("8f0e3f54295434d744ebc105218c6f30af6a8fb69bb74f845162c983cabea1cb"), // dlc.rpf/x64/data/lang/japanesedlc.rpf
    ShaUnpack("673b2b31426c8d94e05d12694c5d8f6caa96a062e46d89e16ca4a66bea64680b"), // dlc.rpf/x64/data/lang/koreandlc.rpf
    ShaUnpack("1665c2b42729f6517c956a73d60990ca85e374bcff8af961b1b342862f82fc0d"), // dlc.rpf/x64/data/lang/mexicandlc.rpf
    ShaUnpack("c57bc408e8d5eddf22c884b8cbc17605d0965eeb11de495fd16db7996d97106a"), // dlc.rpf/x64/data/lang/polishdlc.rpf
    ShaUnpack("fc3aac0647347acbac73ef4394797b7a93f5ec0ce72d3857aca3525117fdcaf2"), // dlc.rpf/x64/data/lang/portuguesedlc.rpf
    ShaUnpack("4b76051ce6d868c3bae9abe9f0db1a764bc0e0500c5c0db62d71d8b69f1a09bb"), // dlc.rpf/x64/data/lang/russiandlc.rpf
    ShaUnpack("0035be640f3faab17900521c7d80beda91296b8ec73537be665812efd1707f53"), // dlc.rpf/x64/data/lang/spanishdlc.rpf
    ShaUnpack("7cda62c366511dc3df031620f75ebe89fe560860d662956675f3e20a86c1e5f1"), // dlc.rpf/x64/levels/gta5/props/mp_race_track.rpf
    ShaUnpack("efbd3a1fa9b2af46f3472cfc83bd4d3c663e75f408a74d94cdb632022b190cf9"), // dlc.rpf/x64/levels/gta5/props/mp_stunt_course.rpf
    ShaUnpack("1792c12006c5226a97707e5f9139e5662352ce375dd6638098743824dcd8a5ef"), // dlc.rpf/x64/levels/gta5/props/mp_stunt_hoardings.rpf
    ShaUnpack("46d1ca527b2f27627a745afccffa365e7a3a8172775de8904ea25a8da0293f80"), // dlc.rpf/x64/levels/gta5/props/mp_stunt_inflatables.rpf
    ShaUnpack("8b5bebf4c7d78a51ba96992c5540ba19b51da62f8ee95ac85581f6604f04107b"), // dlc.rpf/x64/levels/gta5/props/mp_stunt_track.rpf
    ShaUnpack("4eab052f1182498c206ae196699877f5dffca5ac06db625aa94620197f78ddbc"), // dlc.rpf/x64/levels/gta5/props/mp_stunt_tubes.rpf
    ShaUnpack("33cc9e00ade8cfe4e7f96851b13fd5ff1347d9107bbb64fd8c56705826b6856a"), // dlc.rpf/x64/levels/gta5/props/prop_c4_stack.rpf
    ShaUnpack("b04a41e802377e6806c29567fc145984c31a787fe8aea008b2ac78b1391bfcaa"), // dlc.rpf/x64/levels/gta5/props/prop_lives_bomb.rpf
    ShaUnpack("3f4b3302ea2279e73fecd957f5882444eec2fcf258a6934124fe52bd9f9a357c"), // dlc.rpf/x64/levels/gta5/vehicles/mpstuntvehicles.rpf
    ShaUnpack("37d11282a9785128fc7867210960beb162a2fa37ad76ccb72ae26c3fe84ee144"), // dlc.rpf/x64/levels/mpstunt/vehiclemods/bf400_mods.rpf
    ShaUnpack("1e142208dd0e9c20ab64004ef67b29fc57c89dba760350f4e352d3edf3f5386a"), // dlc.rpf/x64/levels/mpstunt/vehiclemods/brioso_mods.rpf
    ShaUnpack("98a20429abf93e7006caab27c1a73a36a0db63ea96b66aa4f98f03b39ce9cce0"), // dlc.rpf/x64/levels/mpstunt/vehiclemods/cliffhanger_mods.rpf
    ShaUnpack("db269c2be7e8f736aa4421b0ee07edee2739a840b5c4abb9663a2025042f366c"), // dlc.rpf/x64/levels/mpstunt/vehiclemods/gargoyle_mods.rpf
    ShaUnpack("10c2482d17de9d57890c23444ca34ab6af6d963ccc5914dcdff7e3441a97c728"), // dlc.rpf/x64/levels/mpstunt/vehiclemods/le7b_mods.rpf
    ShaUnpack("7cb03e1e86f31187b8ad863bd645522fe28cdfb9e1db3e18181dc8819c64fcf4"), // dlc.rpf/x64/levels/mpstunt/vehiclemods/lynx_mods.rpf
    ShaUnpack("025336f0c0679a1d1f0308cb1f9a94f6e1bd9288ca475d9ab02a8d041bf6c869"), // dlc.rpf/x64/levels/mpstunt/vehiclemods/omnis_mods.rpf
    ShaUnpack("c392df2445cc573483d969f042e39a8f93f7ffe8098993632fbc34a1c51adcf3"), // dlc.rpf/x64/levels/mpstunt/vehiclemods/rallytruck_mods.rpf
    ShaUnpack("b36b6b4a8693eb30476663399538b04136fa8dc44d722430d3fc137c4682312e"), // dlc.rpf/x64/levels/mpstunt/vehiclemods/sheava_mods.rpf
    ShaUnpack("411912cb75d9849dc2bbf5bf54825cdeed03cae675c0eae8ebffd84ecd2b9318"), // dlc.rpf/x64/levels/mpstunt/vehiclemods/tampa2_mods.rpf
    ShaUnpack("3047bf5ee4194f7102ae775eff5a6f5177d7b78b47c13d45e9b234d423fa16d6"), // dlc.rpf/x64/levels/mpstunt/vehiclemods/trophytruck2_mods.rpf
    ShaUnpack("fd97131c915b9d8747c5c079aa2f2677a4e6b0bd52edbdb904b84984db8bee15"), // dlc.rpf/x64/levels/mpstunt/vehiclemods/trophytruck_mods.rpf
    ShaUnpack("babae380cc126d82b3c4ec7abf9619772f5ed827391541c93a0ff226639d4e7a"), // dlc.rpf/x64/levels/mpstunt/vehiclemods/tropos_mods.rpf
    ShaUnpack("44dc779e367f305f006e9876913ebbf35a650f5ea6a1c590251817e0610e0526"), // dlc.rpf/x64/levels/mpstunt/vehiclemods/tyrus_mods.rpf
    ShaUnpack("254b684fca23fd94cd87fe0be0150d2f1ebea4750d5ad3c811cf9de9a168f128"), // dlc.rpf/x64/models/cdimages/mpstunt_female.rpf
    ShaUnpack("1c8f3ed7a9353e2a61b18363890121344ca1af0cd089b28d17b721ba0e89db5f"), // dlc.rpf/x64/models/cdimages/mpstunt_female_p.rpf
    ShaUnpack("f955f2400e9a7dbfeae89d293c9ede07d56bfecc26a52565adce3996b1adc7d1"), // dlc.rpf/x64/models/cdimages/mpstunt_male.rpf
    ShaUnpack("73bfa6173375d6af594076fc82b84535baaa5fb9c5af27853752dbfa64ca4139"), // dlc.rpf/x64/models/cdimages/mpstunt_male_p.rpf
    ShaUnpack("6d3125730eb264fb3f06840a4c1fdbf73c475e7ec45ae6866516e504f310823b"), // dlc.rpf/x64/models/cdimages/mpstunt_ped_mp_overlay_txds.rpf
    // update/x64/dlcpacks/mpsum/dlc.rpf
    ShaUnpack("f3066c8bb5f503750b37c5637d0e3880a0e9fc663e5a7453707c6c78c98eae38"), // dlc.rpf
    ShaUnpack("a8b80f863fe5d53bac05ea9bf4697463bcaf6fdb1deab310c1b92d80dfde2323"), // dlc.rpf/x64/anim/creaturemetadata.rpf
    ShaUnpack("695862ac5889c97f88a3fe5127d17f9efccd51812cd22acfd85637bd3d6bf80d"), // dlc.rpf/x64/anim/ingame/clip_anim@.rpf
    ShaUnpack("e08e0fb7d70a1037c5c09d0e6b8e798ed5ee63864e0d631d2569955846a6e846"), // dlc.rpf/x64/anim/ingame/clip_anim_freemode@.rpf
    ShaUnpack("6bf8ce2a1da56189da66c6e4cdfb0a565ae3b0c5088680d7992acb6b8878e8db"), // dlc.rpf/x64/anim/ingame/clip_anim_heist@.rpf
    ShaUnpack("7e60aee644825e5d6844ad6b8e1c8cc1c321a15c7bde243ef6df962dcf1080fc"), // dlc.rpf/x64/data/effects/ptfx.rpf
    ShaUnpack("3c2c745342dfe99ff4438b2ab30fb82a9299688152135a64439a603af3d3286e"), // dlc.rpf/x64/data/lang/americandlc.rpf
    ShaUnpack("965a1ab0d5e7ba76a6f1f8ead4f31dc1f270f41211b7f07fcc6cbc7b16eb2393"), // dlc.rpf/x64/data/lang/chinesedlc.rpf
    ShaUnpack("f41aa7feae1898f58d9cbb29a5c37fe71c3280781e77087f6990fb4112a4b6a7"), // dlc.rpf/x64/data/lang/chinesesimpdlc.rpf
    ShaUnpack("f968a2daff17e4bcc9a1fe52794506669b582b299cf1fa6110951dfe2058c957"), // dlc.rpf/x64/data/lang/frenchdlc.rpf
    ShaUnpack("2c5d51f06ce4a6b6aaffacf9adf4e9ceb8546d8ad906c178eee0afce1b206047"), // dlc.rpf/x64/data/lang/germandlc.rpf
    ShaUnpack("65cbc0c313af52cf4f1a068f756c89d25ccf87ef82bb88eea5f9ebd8c0459992"), // dlc.rpf/x64/data/lang/italiandlc.rpf
    ShaUnpack("60c325df345ef6566b1f372ec429dcc6f54dc18ad9206d5d11ea134f7eb4f660"), // dlc.rpf/x64/data/lang/japanesedlc.rpf
    ShaUnpack("0b27ba60ec25a3c2d6ec33497300bcd095adc2887d513e698119c9d42c2af83f"), // dlc.rpf/x64/data/lang/koreandlc.rpf
    ShaUnpack("d2c20be535a940da14a7f7b5ea4c62652126eaae2116723537fff9dda809eede"), // dlc.rpf/x64/data/lang/mexicandlc.rpf
    ShaUnpack("cc0fe3f2e22cdfa4d971033f5256b9477833619dc2445aee4939846c67c635f9"), // dlc.rpf/x64/data/lang/polishdlc.rpf
    ShaUnpack("d88ad2dfa6cf3e583c211290cc3ecfd98dd93a87783bc8a822cbf4fb92c0e556"), // dlc.rpf/x64/data/lang/portuguesedlc.rpf
    ShaUnpack("091480518f71f95edf7714674575a05420f7a508b24a8c6ae3eb7b27d5949bd2"), // dlc.rpf/x64/data/lang/russiandlc.rpf
    ShaUnpack("f811f2dd86596f9546d526c16bbc0d1423aabd6a03e353d66c6752b6b3038a40"), // dlc.rpf/x64/data/lang/spanishdlc.rpf
    ShaUnpack("77a42570f438600b50ddeae02e014eae7bbec33fc1dc076819281350943e44aa"), // dlc.rpf/x64/levels/gta5/navmeshes.rpf
    ShaUnpack("2e4022d216dcf06d99431d6947b59efdd9e79a3f74f4151dd2b04d99356034c1"), // dlc.rpf/x64/levels/gta5/_cityw/additions_01/sum_yacht.rpf
    ShaUnpack("daf3d5bf21d86e5f5f3917fb0cf5967c0d1284d4a0178a7510be46e8b0a33d13"), // dlc.rpf/x64/levels/gta5/_cityw/additions_01/sum_yacht_metadata.rpf
    ShaUnpack("d9a9de42ab1256ee92e72556f3d086f1c082c5f00f039b53bafcf87edb054c48"), // dlc.rpf/x64/levels/gta5/interiors/int_mp_yacht.rpf
    ShaUnpack("a3a97fda1246e3dab0dc8126d86798a78a51f6d6d6c40455628c468c6e4d10d1"), // dlc.rpf/x64/levels/gta5/props/prop_sum_accs_01.rpf
    ShaUnpack("43bd681d92d71ede172f1ba63617d783e53363c8621392990c7bfd5f68ca1d80"), // dlc.rpf/x64/levels/gta5/props/prop_sum_arcade_qub3d.rpf
    ShaUnpack("aa6e9b5b335f7b8f1deea9c5f85a221fc3b5f4ee378f72c95d275baa11993127"), // dlc.rpf/x64/levels/gta5/props/prop_sum_arcade_strength.rpf
    ShaUnpack("137b9910e4ed0d5dd4b96aedfef30844b9666f94b570a75a06eea3e399581017"), // dlc.rpf/x64/levels/gta5/props/prop_sum_arcade_trophies.rpf
    ShaUnpack("b1183706206cbdcebc0ec904fe7494976ff0a10695c9640b01d769099d44636d"), // dlc.rpf/x64/levels/gta5/props/prop_sum_barge_01.rpf
    ShaUnpack("d4d21f552584dd9bcfeba99c8654f5254c12fe07f001281aa4e851143af5286a"), // dlc.rpf/x64/levels/gta5/props/prop_sum_collectibles.rpf
    ShaUnpack("30e146309365c4682a12d42000a2d306e799b1b8bf61d95ddc3bb41f02b9b30d"), // dlc.rpf/x64/levels/gta5/props/prop_sum_track.rpf
    ShaUnpack("a35ff30f29273bb4c4a5a1fbce9b9861b140a57af0bfcdaf3a20e30ff51006f3"), // dlc.rpf/x64/levels/gta5/props/prop_sum_track_01.rpf
    ShaUnpack("45ead71990bb02edae0e61dd3555a40de7d529a773ab616ffca428b74b0fa919"), // dlc.rpf/x64/levels/gta5/props/prop_sum_track_garage.rpf
    ShaUnpack("8e9f2dfb766c14f3d22019a905d5e0a79577a2aa8fb61608636923cd5f0ccf7b"), // dlc.rpf/x64/levels/gta5/props/prop_sum_track_hoardings.rpf
    ShaUnpack("47ee790f8f8019523f0d5841b46f5e1a1e3333649f95b7844bb9d392ffb5793b"), // dlc.rpf/x64/levels/gta5/props/prop_sum_track_hoardings_01.rpf
    ShaUnpack("defaf4b42562ddb0ad9fd83f1f1ed0fc885f438b68f79bf3f4d99350f032ab6e"), // dlc.rpf/x64/levels/gta5/props/prop_sum_ufo.rpf
    ShaUnpack("cb7c6b931fb20908a9634aa09e064077a48f075c78417f92adce8cc4ac32c0da"), // dlc.rpf/x64/levels/gta5/props/prop_sum_yacht_accs.rpf
    ShaUnpack("2b4a56e1694fffaac5c338ac411d60df0f36d570117a8811d908dd4ff9f0283c"), // dlc.rpf/x64/levels/gta5/props/high/int_mp_doors.rpf
    ShaUnpack("fe0aa5249ac8d8ac216a7833fb52be085f1bdde14a2ec6ccbe371935228d279d"), // dlc.rpf/x64/levels/gta5/props/high/int_mp_h_props.rpf
    ShaUnpack("ee531505239dbac94ceeef55b399da4702f8995f52ba595f79e9267d841eb868"), // dlc.rpf/x64/levels/gta5/props/yacht/mp_apa_yacht.rpf
    ShaUnpack("81bde14076281f1b4e9f6232ddcb3f57c3efffa86ce870eaef61af3a0257c564"), // dlc.rpf/x64/levels/gta5/props/yacht/mp_apa_yacht_jacuzzi.rpf
    ShaUnpack("ae1e2a3899ca4cb6f75a0565c465e4c5fb25fe04e86d24f91c190ec61da213eb"), // dlc.rpf/x64/levels/gta5/vehicles/mpsum.rpf
    ShaUnpack("6e56b988ac7ad037a4297c621810ef17d04a6167031dc76c0ef4c749470e1c37"), // dlc.rpf/x64/levels/mpsum/vehiclemods/club_mods.rpf
    ShaUnpack("2cd85540da7fe5a50f2e1fbe3dc5771742a40cfd1226ed051a303835bb738cb3"), // dlc.rpf/x64/levels/mpsum/vehiclemods/coquette4_mods.rpf
    ShaUnpack("f7ef161ff4b8f981f6431e42997f1880e8f476006d5c952613836e592fc59951"), // dlc.rpf/x64/levels/mpsum/vehiclemods/dukes3_mods.rpf
    ShaUnpack("3c601e3af675b0efc6c50402136dee40f73f42c476555a99583f94e69e72ae12"), // dlc.rpf/x64/levels/mpsum/vehiclemods/gauntlet5_mods.rpf
    ShaUnpack("2de3c335a7e5104992f0d8ea67323fe906cc3469783eccfbed2b11747f79cfa5"), // dlc.rpf/x64/levels/mpsum/vehiclemods/glendale2_mods.rpf
    ShaUnpack("668f8bfaa68736468f84c2b72a084741ebbf36a4ecaec6c66b077f5a29a7597f"), // dlc.rpf/x64/levels/mpsum/vehiclemods/landstalker2_mods.rpf
    ShaUnpack("5bf0f1648abd8139fcc6f166baa5efbbb9b064104682f2c8b31a3764aac8847c"), // dlc.rpf/x64/levels/mpsum/vehiclemods/manana2_mods.rpf
    ShaUnpack("31b267657cf0e3bff4ee0d1ad9b708b5cdda41510f1739f8423078d6217b9428"), // dlc.rpf/x64/levels/mpsum/vehiclemods/openwheel1_mods.rpf
    ShaUnpack("9c0d6b9fe73e1ab662c9d1a412a7b4bb792bd1f16efccfa46fdfde773472dabe"), // dlc.rpf/x64/levels/mpsum/vehiclemods/openwheel2_mods.rpf
    ShaUnpack("be976f90040fd504992bdae37327102537de40ff659e704097d98e1f8cf1e00a"), // dlc.rpf/x64/levels/mpsum/vehiclemods/penumbra2_mods.rpf
    ShaUnpack("5532b3cf5fa83eee9403b44ea3b4a55f8f9b21aaf5ece68f30882d05eb2a7d06"), // dlc.rpf/x64/levels/mpsum/vehiclemods/peyote3_mods.rpf
    ShaUnpack("ab9dc99a5db42c7370319f17104f703413fc8e587badbbd53cc5aac6a0ed6ec3"), // dlc.rpf/x64/levels/mpsum/vehiclemods/seminole2_mods.rpf
    ShaUnpack("799cc0cc7271ed5d54dd6b95565e5174b02b2ef415fe8bed281c5cb4c9ba6d16"), // dlc.rpf/x64/levels/mpsum/vehiclemods/smod4wheels_mods.rpf
    ShaUnpack("70909325dc5b48e3c35925f4c917863de4c0604bd76a7b9d9aa06884bb2e7a30"), // dlc.rpf/x64/levels/mpsum/vehiclemods/tigon_mods.rpf
    ShaUnpack("e4838eedb8b21d18f99e8e7bb0083d6e811633d8c786f672ac5ef77e5c0ff855"), // dlc.rpf/x64/levels/mpsum/vehiclemods/yosemite3_mods.rpf
    ShaUnpack("81d9365fd64c5bc84966be1c64c6185a624b8cb06f92e2edb478f7b19a9be53f"), // dlc.rpf/x64/levels/mpsum/vehiclemods/youga3_mods.rpf
    ShaUnpack("1ce4e0ed5c1998548fde755e974a3f414811bd1ba5ec8ee3b60d4b62b08626d6"), // dlc.rpf/x64/models/cdimages/mpsum_female.rpf
    ShaUnpack("fd94b8e3c4f44b0662be3cfc7e403bfd0a86bdee4f0ac0e7652c34703562f90e"), // dlc.rpf/x64/models/cdimages/mpsum_female_p.rpf
    ShaUnpack("652a8969fbfe2bfa140b6a1f2c7159b97d143365d087077b0dd0d88cb7e80386"), // dlc.rpf/x64/models/cdimages/mpsum_male.rpf
    ShaUnpack("70097f6de85f3b69388b7f253c1c3ae8214284ae0bdbfb085728772a2f784c84"), // dlc.rpf/x64/models/cdimages/mpsum_male_p.rpf
    ShaUnpack("106181448591f162722e73a393406112663cac41fef5a50727fc724a1184ebb8"), // dlc.rpf/x64/models/cdimages/mpsum_ped_mp_overlay_txds.rpf
    // update/x64/dlcpacks/mpsum2/dlc.rpf
    ShaUnpack("65b8304f793c81977b2ead686691df2358fbdc9bda3e31ce146f98f5edb75b5c"), // dlc.rpf
    ShaUnpack("1d596f250cfb13df19ef10a22046673ec2957b52654236f138b8c53b9d63b0ce"), // dlc.rpf/x64/anim/creaturemetadata.rpf
    ShaUnpack("07c7986d813e02010cfd8014da195eba753461649938571c962d9df643035830"), // dlc.rpf/x64/anim/ingame/clip_anim@.rpf
    ShaUnpack("51d01cf164fa34fd037626c0a6ccc80242e7f1129875f9c072fd2fa50a212cec"), // dlc.rpf/x64/data/effects/ptfx.rpf
    ShaUnpack("ca37b0ed30011482281404c4b23c6d0e4a42b5b9e35cda478e3694eb2c02e3e2"), // dlc.rpf/x64/data/lang/americandlc.rpf
    ShaUnpack("a36bea5446fc9d0ae1c4d87d9efbf0a021adcc794fe08719347b1c29a4174f09"), // dlc.rpf/x64/data/lang/chinesedlc.rpf
    ShaUnpack("4bde8fd00d34f27e6e59c02c15caec8ecfdf1bddd7ca02bd53d067ad29acd72e"), // dlc.rpf/x64/data/lang/chinesesimpdlc.rpf
    ShaUnpack("a46b6032889f6a48c6c08c830665b5f7130b8bc4f229439c0189ce33ed12119d"), // dlc.rpf/x64/data/lang/frenchdlc.rpf
    ShaUnpack("c6e252c5424ce4c2a6e7872c17fe3eb6b351820a6792b72b2c46bbf0f7f1cc49"), // dlc.rpf/x64/data/lang/germandlc.rpf
    ShaUnpack("f1fd1669d4fd36d4294571ace3d21cf33a1ab79986e7204f71d195734b72668e"), // dlc.rpf/x64/data/lang/italiandlc.rpf
    ShaUnpack("20db84a17c9ea10ae6a7706db156cd533af09fd9029ca059f127cf2b7348f83f"), // dlc.rpf/x64/data/lang/japanesedlc.rpf
    ShaUnpack("ab7f771cbae70e9c2129ef87309807c0c923056cd0b5c7a995e6bfdd33527a41"), // dlc.rpf/x64/data/lang/koreandlc.rpf
    ShaUnpack("b123c34bd6e8d2159abede27b50fa422ccc615d1e9bf31a08d58efd4ce4893d1"), // dlc.rpf/x64/data/lang/mexicandlc.rpf
    ShaUnpack("a11dc0a39019c50bf96910afc75c8bc6152ab3109c12243900d3b2f78996afeb"), // dlc.rpf/x64/data/lang/polishdlc.rpf
    ShaUnpack("71577077a816659d3fca984f6952d0e464a3f9652b4a9094d1652d6f4767fcbf"), // dlc.rpf/x64/data/lang/portuguesedlc.rpf
    ShaUnpack("6ca848da08a73bde5ca7ca8db91d59327bd93aecfe5c25390da67d1a0545e0ef"), // dlc.rpf/x64/data/lang/russiandlc.rpf
    ShaUnpack("8cbff69c774ef810e75ddc0ab021e5a15806acc3fa2699e573e33ffa41b69514"), // dlc.rpf/x64/data/lang/spanishdlc.rpf
    ShaUnpack("abf788eb1e6a330dd05f6f5afb410a86322403ca65bf3a8adaf9cd9bf6463620"), // dlc.rpf/x64/levels/gta5/navmeshes.rpf
    ShaUnpack("01f736a396d72a524f7d3e58f451fd3044719c7789b08da23ad7f72be8318e01"), // dlc.rpf/x64/levels/gta5/interiors/dlc_int_03_sum2.rpf
    ShaUnpack("f9531d0324f553f06f9b492158f68960cdd97e1c39469fd03457884aebd51266"), // dlc.rpf/x64/levels/gta5/interiors/dlc_int_04_sum2.rpf
    ShaUnpack("b01b41f41e72f9098406e9cc330b65da316ce14d08190b4d8b8b0ddf8072286c"), // dlc.rpf/x64/levels/gta5/interiors/int_placement_sum2.rpf
    ShaUnpack("d1f1d0c6ff60d930a04305c8892c4227d4f1f796a8ef5f8907185a9960505f6c"), // dlc.rpf/x64/levels/gta5/mpsum2_additions/mpsum2_additions.rpf
    ShaUnpack("7698eae30f2ac0440724535d61f8c1194ac7c4a345906bc42e61cdcf51568b86"), // dlc.rpf/x64/levels/gta5/mpsum2_additions/mpsum2_additions_metadata.rpf
    ShaUnpack("e5a9d27bbc7026a2cf7d6909e0a84f905d204b883e4dde8507d9230cd478bacd"), // dlc.rpf/x64/levels/gta5/props/prop_reh_accs_01.rpf
    ShaUnpack("033b03dfdfdaebb1b5039f09fdb0b8cf6151c6947e420420b54ffc9685921768"), // dlc.rpf/x64/levels/gta5/props/prop_reh_accs_02.rpf
    ShaUnpack("3c7fc15d9382a4c733c4f272493c25b6efe18c42697385777deca2f2a408ac03"), // dlc.rpf/x64/levels/gta5/props/prop_reh_cabin.rpf
    ShaUnpack("d28c4576c6894c6a04d286416e456b3999a710fc074b80fef74ca05e81591779"), // dlc.rpf/x64/levels/gta5/props/prop_reh_collectibles.rpf
    ShaUnpack("da99dd2e621be06fe176374d0cc4cd1297c88fba4031da4110c5343f99809d22"), // dlc.rpf/x64/levels/gta5/props/prop_reh_doors.rpf
    ShaUnpack("d6f8e448d83ce2cb98fe0bed9ca5b37d141e693a92b7d4b6bbb063a083c64232"), // dlc.rpf/x64/levels/gta5/props/prop_reh_drones.rpf
    ShaUnpack("7d070bd93a606d29fb8010211fc3a216f2ae7ad542ce3eeea8acb188e0190f5e"), // dlc.rpf/x64/levels/gta5/props/prop_reh_para.rpf
    ShaUnpack("f13484e7162f816ecadeac8243f00862c84a19ade790328a19ea49f9c86bafb9"), // dlc.rpf/x64/levels/gta5/vehicles/mpsum2.rpf
    ShaUnpack("c5bca6373da7cb60bcd8f6649c761fb51dc45402a8b4746803dd0a3b0ab83baa"), // dlc.rpf/x64/levels/mpsum2/vehiclemods/brioso3_mods.rpf
    ShaUnpack("e88462e4d7510e55a372415327883a04e1c7338081502eb407bb37fed6334120"), // dlc.rpf/x64/levels/mpsum2/vehiclemods/conada_mods.rpf
    ShaUnpack("ec3505b51bff747cfc568cd2b1a054b415cb97ea6be975640bdb4b3124e47a4a"), // dlc.rpf/x64/levels/mpsum2/vehiclemods/corsita_mods.rpf
    ShaUnpack("b730fb77caacb802e69920828c7cabaa568943a643d0f68ee27f43b0bd2edb20"), // dlc.rpf/x64/levels/mpsum2/vehiclemods/draugur_mods.rpf
    ShaUnpack("5e75f85ebf9e819d0c5b57294769471cb616a6b20343094c23d869e5544d9987"), // dlc.rpf/x64/levels/mpsum2/vehiclemods/greenwood_mods.rpf
    ShaUnpack("5dd7d5b3de83c9dfbb788946d57f302532014488107f914e45c04ec09496bd6a"), // dlc.rpf/x64/levels/mpsum2/vehiclemods/kanjosj_mods.rpf
    ShaUnpack("1cee0a0913cd2db99abcf1fb6296f1a8fa2503cefc20da5d649d65bbe708b0e7"), // dlc.rpf/x64/levels/mpsum2/vehiclemods/lm87_mods.rpf
    ShaUnpack("3ba75751623b0e022bcb878dfc96255f71afc34c675efe33351f835c9eb09fc6"), // dlc.rpf/x64/levels/mpsum2/vehiclemods/omnisegt_mods.rpf
    ShaUnpack("ce5ba8ab4a1bbf977c3bf82d063f62862e7b0a572d4ccca34cd61be1b0b5bb3d"), // dlc.rpf/x64/levels/mpsum2/vehiclemods/postlude_mods.rpf
    ShaUnpack("dee34513cb00b1450d697da636263946ed01d6fcdcd980c104c55f52ef3380f5"), // dlc.rpf/x64/levels/mpsum2/vehiclemods/rhinehart_mods.rpf
    ShaUnpack("7c16a37358a144441ffbb7bd09d33c21a6d50e72ed60a563ca1cbc0da9a6cc39"), // dlc.rpf/x64/levels/mpsum2/vehiclemods/ruiner4_mods.rpf
    ShaUnpack("16857514681ca94fb17c2695a6e0ad3a2c09a5bdec7795f9b20fbabced0c760f"), // dlc.rpf/x64/levels/mpsum2/vehiclemods/sentinel4_mods.rpf
    ShaUnpack("24510a31415ef216309a42a392f94c68e885bc249d9baa70d48841ad7e82de94"), // dlc.rpf/x64/levels/mpsum2/vehiclemods/sm722_mods.rpf
    ShaUnpack("c6e33899f075f9a38d42a096418bb6ee60190a0dcf3c9db55540dfef9b54daba"), // dlc.rpf/x64/levels/mpsum2/vehiclemods/tenf2_mods.rpf
    ShaUnpack("88829b92789850e0f0e9af393e28f2f66dea8ce5b3dbab0b681e384c34b1f8db"), // dlc.rpf/x64/levels/mpsum2/vehiclemods/tenf_mods.rpf
    ShaUnpack("0d345b19322a24ca36e7a49535b25e87bd87ea37fd47298e533a4bf5ba6dd3fb"), // dlc.rpf/x64/levels/mpsum2/vehiclemods/torero2_mods.rpf
    ShaUnpack("8043afe7eac11d0f032ee27e212f58ed60e8aefa846c58347f70c76256b3c6ca"), // dlc.rpf/x64/levels/mpsum2/vehiclemods/vigero2_mods.rpf
    ShaUnpack("ef178022f130233e9f4f1a66c15ef4cbeb37f215e6b8324eec64b9f515401690"), // dlc.rpf/x64/levels/mpsum2/vehiclemods/weevil2_mods.rpf
    ShaUnpack("dba880905cb3d3e45e244cec137b8c6c8ac61fbab732b1006d5835b5b6c90ebb"), // dlc.rpf/x64/models/cdimages/mpsum2_female.rpf
    ShaUnpack("450790ef6e726729ffa73045a215b54b1a0d7329d77a5f4ee454a989d8011194"), // dlc.rpf/x64/models/cdimages/mpsum2_female_p.rpf
    ShaUnpack("9c0abff32d319e57ce0a4f7f104c4dcbb4c2311d8edc3fc8268a4f150a2ad4b5"), // dlc.rpf/x64/models/cdimages/mpsum2_male.rpf
    ShaUnpack("742d5ce7dc227af9c8192a0c8236e12eb72f4fcd4bab545b0afd499f29bb8fef"), // dlc.rpf/x64/models/cdimages/mpsum2_male_p.rpf
    ShaUnpack("2eeceaeb85a0c215cc10791afdad501f012e3add461ee16b2a1eafdf0d016c2b"), // dlc.rpf/x64/models/cdimages/mpsum2_ped_mp_overlay_txds.rpf
    ShaUnpack("be7e873c89d069c5c3578d61155cb2b38eaabea61f60e7feddca02094aa55e6b"), // dlc.rpf/x64/models/cdimages/weapons.rpf
    ShaUnpack("800e8a953dca91a60817d96fe043992056c49b4884ae39257dad59a83aa381a7"), // dlc.rpf/x64/models/cdimages/peds/mpsum2.rpf
    ShaUnpack("4d9f0874d06123e5357fab107bd0223a64296710782d4332b43d9950d6bf3c9f"), // dlc.rpf/x64/models/cdimages/peds/mpsum2_p.rpf
    // update/x64/dlcpacks/mpsum2_g9ec/dlc.rpf
    ShaUnpack("1cabab5cd5f3e62fa3fa03916605df3e6a4bfebf4d384a054d2fea26774e93c1"), // dlc.rpf
    ShaUnpack("f8562811081fddc355c1cf73f77f26498f02fd7a32898f01e98f301b3382f726"), // dlc.rpf/x64/anim/creaturemetadata.rpf
    ShaUnpack("d2a2b20782f39cca688db4bafaea276b3be0f8732a082900c26b054400f11cf7"), // dlc.rpf/x64/data/lang/americandlc.rpf
    ShaUnpack("05647dca3ff6e5490c18e021e81d82036d107f9d80c8ae6b4ddf520d1ef271c8"), // dlc.rpf/x64/data/lang/chinesedlc.rpf
    ShaUnpack("6625416ffd858c1d906c51829d0c11bb6507066f4b128f0525669a690d454476"), // dlc.rpf/x64/data/lang/chinesesimpdlc.rpf
    ShaUnpack("821728c4499747ba3262d72322403b8635892c74025bb61092aa679dfc5606ed"), // dlc.rpf/x64/data/lang/frenchdlc.rpf
    ShaUnpack("7ae176c2eab22b280704603475beac00f51842ddc140b871f7da7b33c7fe902b"), // dlc.rpf/x64/data/lang/germandlc.rpf
    ShaUnpack("e63488e106f040db02e17ac00302430d1c31a8e2ec4758caffdaf11bf914ac78"), // dlc.rpf/x64/data/lang/italiandlc.rpf
    ShaUnpack("3e209004057c02b5c4c140fa120878bb66bf7e5ca11bea43a1f22e7d54f56597"), // dlc.rpf/x64/data/lang/japanesedlc.rpf
    ShaUnpack("a558be1c2de9b7de5a04774b6483903b1dcfc8f0b43dc6e78e3e5a8503e2211d"), // dlc.rpf/x64/data/lang/koreandlc.rpf
    ShaUnpack("33f187223cce25042fe3297dfdb696d9201e17be853c959ae7e70a32cfd18082"), // dlc.rpf/x64/data/lang/mexicandlc.rpf
    ShaUnpack("8656ba701ea7dde2e602a2ebe5c56e7945b1db1c11019e9c444894a265d7edd7"), // dlc.rpf/x64/data/lang/polishdlc.rpf
    ShaUnpack("0a2a860d72e81a7ae09544c599e860f0d4125534f68edd5ad68018d153c418b2"), // dlc.rpf/x64/data/lang/portuguesedlc.rpf
    ShaUnpack("f8e555539a5d5e801c1b72af54cdc5dcb35b37f40804303e7dc2356fc89f3125"), // dlc.rpf/x64/data/lang/russiandlc.rpf
    ShaUnpack("9b319cd3a69642ceb1be4ef4d87581b1a1217300d0b6383a056222ae8b34bc7c"), // dlc.rpf/x64/data/lang/spanishdlc.rpf
    ShaUnpack("77aa9c5e4573f8fcd9a3d1880b15fb425e661e73385c527121ec50d3cc56d545"), // dlc.rpf/x64/levels/mpsum2_g9ec/vehiclemods/feltzer3hsw_mods.rpf
    ShaUnpack("10d0d8b7fb3eaf71cead5ae44d10db1397de2d82aed3774308e0fdb9f9869cf5"), // dlc.rpf/x64/levels/mpsum2_g9ec/vehiclemods/vigero2hsw_mods.rpf
    ShaUnpack("62a9a1be2be2599f4200cb32e04e0a8b3890b54cdfa29a7833c2bda29b25b99d"), // dlc.rpf/x64/models/cdimages/mpsum2_g9ec_female.rpf
    ShaUnpack("84ab27f1552c89d6a4ca49c9f910a9ac620b72c9a5c07e36461e2c36db3615ca"), // dlc.rpf/x64/models/cdimages/mpsum2_g9ec_female_p.rpf
    ShaUnpack("b09ff7c4be744f5062a865a10b8f854fcc20eeb50797275b598c3c1d9b84510e"), // dlc.rpf/x64/models/cdimages/mpsum2_g9ec_male.rpf
    // update/x64/dlcpacks/mptuner/dlc.rpf
    ShaUnpack("d97bb80e0ae0b5816e12e3226482fcdef4b123148253b4304de647942671bcea"), // dlc.rpf
    ShaUnpack("e0fa83280787d9c3357c069c03923b3dd61490c3f690362edec15bfede0e21ff"), // dlc.rpf/x64/anim/creaturemetadata.rpf
    ShaUnpack("84c3237ff2585a87961dbbfab1e1f097c1c0a4be2597c834856db96b65506752"), // dlc.rpf/x64/anim/cutscene/cuts_tun.rpf
    ShaUnpack("2a26a9e228ce0be6c94f8085deba403b294b2e5e3148eee8629e36a6bf4529b4"), // dlc.rpf/x64/anim/cutscene/cuts_tunf.rpf
    ShaUnpack("61309e8bd004fcbd9c738ef30cb7eb88d62f11afe1ca9689d2f3a6f21a37ee68"), // dlc.rpf/x64/anim/ingame/clip_anim@.rpf
    ShaUnpack("7e35260aa8bcea0af76f158859f7d09b7561e2aa6460a85bf91c9aaa9eb06b4f"), // dlc.rpf/x64/audio/occlusion.rpf
    ShaUnpack("579411f060aec903d5f4b84808e62200d06ca575569c21d147227ab18b25fcc3"), // dlc.rpf/x64/data/effects/ptfx.rpf
    ShaUnpack("92e2988ab670e13de9ca91bdaa5f1b54e66a3e687c08be9de3e21a19e6bd1efc"), // dlc.rpf/x64/data/lang/americandlc.rpf
    ShaUnpack("e34e5fdb9fecab8511f50a8ebb87cedfcdc72bee5d7faf34e1118a29c987a0de"), // dlc.rpf/x64/data/lang/chinesedlc.rpf
    ShaUnpack("857263fcc1b3a782bff2a61f712dc86105e5a936593afbf3df81a99f2d2943b0"), // dlc.rpf/x64/data/lang/chinesesimpdlc.rpf
    ShaUnpack("e37c0d1891ff17840da8932f24eae1538fe799d3579a27815aa6982efb42745f"), // dlc.rpf/x64/data/lang/frenchdlc.rpf
    ShaUnpack("f52133a94f6dfe15ab45af211fd53a1bb855f9d1a8b3bcf23338913fe33f1bf1"), // dlc.rpf/x64/data/lang/germandlc.rpf
    ShaUnpack("0faa66918d1a692534779cea7feada7df9470a802795df6b60f6feded28574a8"), // dlc.rpf/x64/data/lang/italiandlc.rpf
    ShaUnpack("f4e8cd05141dc6606a2619897ac454068139cafb1501d567f91833d90cfabb88"), // dlc.rpf/x64/data/lang/japanesedlc.rpf
    ShaUnpack("234977dc28b56969937e9403cd3bb067579fe826c922d449c9c2d2961974d6f0"), // dlc.rpf/x64/data/lang/koreandlc.rpf
    ShaUnpack("4068f0e64e8f6a34b31ce4c4b69dda5c17c80dd6284080d0bda1a5a8a1eb7432"), // dlc.rpf/x64/data/lang/mexicandlc.rpf
    ShaUnpack("94211c782b977c170372906a6bdb03b2649d834788e2eca917dd4141c18b985d"), // dlc.rpf/x64/data/lang/polishdlc.rpf
    ShaUnpack("e81b3e6cfeab814470e3053f7c6c7d646b740519f38a809f344a077e55a55287"), // dlc.rpf/x64/data/lang/portuguesedlc.rpf
    ShaUnpack("a05ac0a0d0ffbdff66fdd1cc00d8f7cd774750869bd5675015688f2b6fb01346"), // dlc.rpf/x64/data/lang/russiandlc.rpf
    ShaUnpack("a9dd9f596191f225448688ae449bc7986417d44a61415ffa1fd6b1fc84afaa21"), // dlc.rpf/x64/data/lang/spanishdlc.rpf
    ShaUnpack("94e595d2bd71400d9c9301108242a0356c5b51a9c44d0de71a5ee7c038d1bd04"), // dlc.rpf/x64/levels/gta5/navmeshes.rpf
    ShaUnpack("23316abc4bb9569312467da30d8299b99c8bcc8f8ab7fbcffaf074ad7fc5472d"), // dlc.rpf/x64/levels/gta5/_citye/tuner_additions/dt1_17_tuner_additions.rpf
    ShaUnpack("53400db1ed6252df2cc98250b88da9968434663d28a0d747eda1f35777180c86"), // dlc.rpf/x64/levels/gta5/_citye/tuner_additions/id2_18_tuner_additions.rpf
    ShaUnpack("2f9c120ceafd98c25e073e3f20e8167662f60074e8c9dc17ac6ac39a343dbd80"), // dlc.rpf/x64/levels/gta5/_citye/tuner_additions/sc1_02_tuner_additions.rpf
    ShaUnpack("586c16a2df25439f9af8dcdcdde8f2eb41f01752ca3e25f66d7410abacb26b5b"), // dlc.rpf/x64/levels/gta5/_citye/tuner_additions/sc1_28_tuner_additions.rpf
    ShaUnpack("258cc73196d6b0da47f10be5bc2c8356138840a7299982304c42f275053ae026"), // dlc.rpf/x64/levels/gta5/_citye/tuner_additions/ss1_05_tuner_additions.rpf
    ShaUnpack("7641902d2e35d5e33330f0eaec87abc854666ab259aff79058d4792e3d9c2cf6"), // dlc.rpf/x64/levels/gta5/_citye/tuner_additions/tuner_additions_metadata.rpf
    ShaUnpack("bbb50125d3d5978f46d809f5a3f3384f30b88a906a22d1ee8622711ea236ec8e"), // dlc.rpf/x64/levels/gta5/interiors/dlc_int_01_tr.rpf
    ShaUnpack("722e6e49320bee623a38906f995d15bc402729a0a46d6f4e9b0ab3dc725a4fd6"), // dlc.rpf/x64/levels/gta5/interiors/dlc_int_02_tr.rpf
    ShaUnpack("745cd4705a682a9d71b9f352033c5e739f11c57a351a2bdf33b6968ac859a8de"), // dlc.rpf/x64/levels/gta5/interiors/dlc_int_04_tr.rpf
    ShaUnpack("ecf0128d7f5509af4089d4bbb19ede4c4c2005db78fa032b77cd5beb639363e7"), // dlc.rpf/x64/levels/gta5/interiors/int_placement_tr.rpf
    ShaUnpack("65143c1f5c4d89a4e9149c8b847fffc647712eeb408af93f7826b6e29479754a"), // dlc.rpf/x64/levels/gta5/props/prop_tr_01.rpf
    ShaUnpack("b1aa9d4de136039589bf92dd738c32ea2a2f713ee1360c18ee26fc3a08718739"), // dlc.rpf/x64/levels/gta5/props/prop_tr_accs_01.rpf
    ShaUnpack("2ae0400aafba713ab1eaeacc006edfc9e875a89569ec145e2857f8f1c7f96628"), // dlc.rpf/x64/levels/gta5/props/prop_tr_accs_02.rpf
    ShaUnpack("2044cb919ab80e570786839fbd4cef18dd04b87e50c6b7ae70d4a03325f874a8"), // dlc.rpf/x64/levels/gta5/props/prop_tr_arcade_camhedz.rpf
    ShaUnpack("ef9a7ff76fa00c89f05050261321aca511a4e16ef666b38c0ffb44a7a8886c66"), // dlc.rpf/x64/levels/gta5/props/prop_tr_arcade_trophies.rpf
    ShaUnpack("647114509011c18d8360961e66e19907cad33bc50bde5ca895292279da0efd9e"), // dlc.rpf/x64/levels/gta5/props/prop_tr_cabin.rpf
    ShaUnpack("fe33a8c87e779ae4ffc4e757787848934679f845687195b73773744eee9afcf7"), // dlc.rpf/x64/levels/gta5/props/prop_tr_collectibles_01.rpf
    ShaUnpack("3a1a7af6a16c24e7b933b6bf8b4dd398f054851ac68a55bd85237daca8c537f9"), // dlc.rpf/x64/levels/gta5/props/prop_tr_containers.rpf
    ShaUnpack("f5bcd673673aab422f72f92490a5399428c2cb4e43db8145379f70502b63d490"), // dlc.rpf/x64/levels/gta5/props/prop_tr_crates.rpf
    ShaUnpack("9f76a69046ab626df53583bcaf6dc421bc9cac25bf2ec7e7cb17615c9018a56f"), // dlc.rpf/x64/levels/gta5/props/prop_tr_garage_equipment.rpf
    ShaUnpack("10dd25315c68fed89d4284a1f9c4ed992b5264519f41c0516a7cd92303bb887a"), // dlc.rpf/x64/levels/gta5/props/prop_tr_meth.rpf
    ShaUnpack("c3d2147b3b8164323a6f213a2831c5e6fc25ec80f386a909364097618c50bef5"), // dlc.rpf/x64/levels/gta5/props/prop_tr_para.rpf
    ShaUnpack("27fb061013ea245b1f92238a9589447884e85041feedf5e303d054eb78a29345"), // dlc.rpf/x64/levels/gta5/props/prop_tr_track_course.rpf
    ShaUnpack("efb14695dc4e6252511751ddb36a9bacb5ae74132013c5d58b6e57d12df3f51a"), // dlc.rpf/x64/levels/gta5/props/prop_tr_track_hoardings.rpf
    ShaUnpack("fc1a7685d438e058aead5713ef19f2cac1f1342dd6c69f6c66f2f39e7b6b97a9"), // dlc.rpf/x64/levels/gta5/props/prop_tr_vehicles.rpf
    ShaUnpack("a340870045e9d456096150463c489cb2588b39f707e2655d56f2b6b3fc4ad77d"), // dlc.rpf/x64/levels/gta5/vehicles/mptuner.rpf
    ShaUnpack("17a20e208299d761e6272eb5202da13f01f5be984b66b8ee011a89ef342a1937"), // dlc.rpf/x64/levels/mptuner/vehiclemods/calico_mods.rpf
    ShaUnpack("24a15aa513328deb2895eb99428dd847089512e2ec46502b0718273bed5da0b6"), // dlc.rpf/x64/levels/mptuner/vehiclemods/comet6_mods.rpf
    ShaUnpack("92fcfaf413de960e708efd3ea019aac85db7da8c673130c234c29e6795065e4e"), // dlc.rpf/x64/levels/mptuner/vehiclemods/cypher_mods.rpf
    ShaUnpack("94beece249dbfd2f2583087d2900568f07c6c168f8ed61a9a0b259b4a9cd9abd"), // dlc.rpf/x64/levels/mptuner/vehiclemods/dominator7_mods.rpf
    ShaUnpack("c5a16d49efd29453a0254c9ffcc012fe9a2384d1654c35205e42debe0813bbdd"), // dlc.rpf/x64/levels/mptuner/vehiclemods/dominator8_mods.rpf
    ShaUnpack("3ab075bf769b2a6f2599b27ebfe721ade4523febeb0dc7a540daa6e487d473e3"), // dlc.rpf/x64/levels/mptuner/vehiclemods/euros_mods.rpf
    ShaUnpack("61617f2a1464fe2c12d6731dd67cd2854c37444de9b581e431893aaa842a8a95"), // dlc.rpf/x64/levels/mptuner/vehiclemods/futo2_mods.rpf
    ShaUnpack("19b9ffb8b0fe41095f7e6e4c75279da9d3aa7c4019409c4133627f8b674a1884"), // dlc.rpf/x64/levels/mptuner/vehiclemods/growler_mods.rpf
    ShaUnpack("eb5bbdda76c67860a44772dda3b8acc84c092a9e1abb56a250aad94383a910b4"), // dlc.rpf/x64/levels/mptuner/vehiclemods/jester4_mods.rpf
    ShaUnpack("9bd237387781815c1a3bde74c6422c7c06fcac4dfae31640db88eb936cc74015"), // dlc.rpf/x64/levels/mptuner/vehiclemods/previon_mods.rpf
    ShaUnpack("fe60fdca90ee0d0b1163075b8833147713dc554c673e3282c6c6ed0b5f216876"), // dlc.rpf/x64/levels/mptuner/vehiclemods/remus_mods.rpf
    ShaUnpack("d547e8197c7ebdebdfd584ca8bed963c6b7877636a94cc051647ca8cd056a5b0"), // dlc.rpf/x64/levels/mptuner/vehiclemods/rt3000_mods.rpf
    ShaUnpack("5c1f716d4d85b725fb73d24ca5503ff402b25d2515b8ec4397ccd4e3a867f754"), // dlc.rpf/x64/levels/mptuner/vehiclemods/smod5wheels_mods.rpf
    ShaUnpack("6c708f239330047247bc243e2318e1c37ac98e81ba59da7ab7c35393a0fc2a17"), // dlc.rpf/x64/levels/mptuner/vehiclemods/sultan3_mods.rpf
    ShaUnpack("ef53f171b6e8736c1034159a314d5e11b82c00a1a7ed9b5c66be3c21285240dd"), // dlc.rpf/x64/levels/mptuner/vehiclemods/tailgater2_mods.rpf
    ShaUnpack("b2c9205f89b02050e7e1cff58c59fb435dd6e039882363ad4463fa071967fbbc"), // dlc.rpf/x64/levels/mptuner/vehiclemods/vectre_mods.rpf
    ShaUnpack("41c3592b351abc7a84b0896c45858567d23a6d504eda238d0030a5a1581060a4"), // dlc.rpf/x64/levels/mptuner/vehiclemods/vehlivery2_mods.rpf
    ShaUnpack("02e58a57c45c7f5703a4dab140bbaeb168c8546c28d1de29b32171332096f760"), // dlc.rpf/x64/levels/mptuner/vehiclemods/warrener2_mods.rpf
    ShaUnpack("a3d5151988d1450ed2ecb4aa544eec287129a5615f9847f88f2787d06d93d9c9"), // dlc.rpf/x64/levels/mptuner/vehiclemods/zr350_mods.rpf
    ShaUnpack("4cd06861d38a9641b4ee72484d84c337bcfaa3841ff415d4b245b8901a97c3af"), // dlc.rpf/x64/models/cdimages/mptuner_female.rpf
    ShaUnpack("61ef3b0c2534a3d86d6d77741218afdefcec2ccc96b2e533a847f5eaf0f40504"), // dlc.rpf/x64/models/cdimages/mptuner_female_p.rpf
    ShaUnpack("2fa2cf41a024691db21df0a6c6b34444dbbd1257820cdc7a10af3a414d287478"), // dlc.rpf/x64/models/cdimages/peds/mptuner.rpf
    ShaUnpack("8eae4b0e7356ba17e3a467cb6c5367ff009800312f685407ffccd158799a40ff"), // dlc.rpf/x64/models/cdimages/peds/mptuner_p.rpf
    // update/x64/dlcpacks/mpvalentines2/dlc.rpf
    ShaUnpack("d2bf0d6bb75c4b5ead830eaa067c2202f25a988977545da04e19ccdf73b68133"), // dlc.rpf
    ShaUnpack("a54f33ec4af386fb61788cb0012b513b69e812683651358d95933b0293321c60"), // dlc.rpf/x64/data/lang/americandlc.rpf
    ShaUnpack("ac4f7b139ce8389059fd236e842e95d3a362873c89f8a41cd7f8f68f63f1c20e"), // dlc.rpf/x64/data/lang/chinesedlc.rpf
    ShaUnpack("dcdd3aaf35e9d1a25700e82daed383300124ad5873a0d33a309f226119d7c99c"), // dlc.rpf/x64/data/lang/frenchdlc.rpf
    ShaUnpack("3daedc7c5b29c13b7a0e23c0ffe5586e9505644c974d612dd2991079975e168b"), // dlc.rpf/x64/data/lang/germandlc.rpf
    ShaUnpack("3316fba3235183bf01b94fcf916b68ae7c0f881745ac07c55be878c771737fde"), // dlc.rpf/x64/data/lang/italiandlc.rpf
    ShaUnpack("8c986d0f687a9e3af1001dc0b6eb57c9f5435d27f24a607e085f88d8798f9887"), // dlc.rpf/x64/data/lang/japanesedlc.rpf
    ShaUnpack("e03cfec401f788f94bf54a8ef40bb134cb8b2faf9ae2f231e34a8bfc4b78df2e"), // dlc.rpf/x64/data/lang/koreandlc.rpf
    ShaUnpack("7ead50cca49b8232a3089b82de8a8339a863dd1e876c926d726f8bba4d1c7571"), // dlc.rpf/x64/data/lang/mexicandlc.rpf
    ShaUnpack("b2947c74399fbf994ec47b2593c7130c5bd2e452ff3a9258005158596dac42c0"), // dlc.rpf/x64/data/lang/polishdlc.rpf
    ShaUnpack("88f65b0fba68e27439ec7631420df5e342b96eebaf0dc291e88ea07811f78294"), // dlc.rpf/x64/data/lang/portuguesedlc.rpf
    ShaUnpack("bd02125927b3b69c7759fd22ab46fadf503d27ea522e45ca11a172f67f364590"), // dlc.rpf/x64/data/lang/russiandlc.rpf
    ShaUnpack("7698517c917a7c7ec0616056e3c717797fb9444149bc4d46f95275255799dd39"), // dlc.rpf/x64/data/lang/spanishdlc.rpf
    ShaUnpack("d6eb42919799c582ed4ecce837b7334ce7ad36634ca74cb3f6a2de8f8c958788"), // dlc.rpf/x64/levels/gta5/vehicles/mpvalentines2vehicles.rpf
    ShaUnpack("43c2c610fcec0baad5448a2982a056d1756541dbe15009f12a49d2bdfd8db93c"), // dlc.rpf/x64/levels/mpvalentines2/vehiclemods/btype3_mods.rpf
    ShaUnpack("3633ceb7a28ab7abcd2a7ba32e3a2568bc1541178ca21738d7627f4ee686a0a7"), // dlc.rpf/x64/models/cdimages/mpvalentines2_female.rpf
    ShaUnpack("f62ed0d012eb2a80abcb9034c37ed87cafe13bd0aa8f5e405263521344fd8f33"), // dlc.rpf/x64/models/cdimages/mpvalentines2_male.rpf
    ShaUnpack("03e04bffa041aa7282657100192273fed819db6b99547b404e0b38efa14818fa"), // dlc.rpf/x64/models/cdimages/mpvalentines2_male_p.rpf
    // update/x64/dlcpacks/mpvinewood/dlc.rpf
    ShaUnpack("327a62a02d6b6a2fb5488f10ac8c0f2e13c18be320bff250760255e52b8c581a"), // dlc.rpf
    ShaUnpack("5450ff61e2fc2104be142c35c933bea9b7da58496905e0fa1c3169028262a305"), // dlc.rpf/x64/anim/creaturemetadata.rpf
    ShaUnpack("c47152355b37c2ca26e8025c9b05e415a55948e345048a5d09a7736741545f70"), // dlc.rpf/x64/anim/expressions.rpf
    ShaUnpack("d2dc085549d0bf8ff87635d429848f56fa19502a6ab34913bce86eddbbab8d41"), // dlc.rpf/x64/anim/cutscene/cuts_mpcas.rpf
    ShaUnpack("3c3fdd5356cad2386e472b6b34fbd70697511f0b08fee4d605b52accd0d77adb"), // dlc.rpf/x64/anim/cutscene/cuts_mpcas1.rpf
    ShaUnpack("404105e0b439cd717dfdbde6d7184b4c14984967317a62dca7b194da740dc025"), // dlc.rpf/x64/anim/cutscene/cuts_mpcas2.rpf
    ShaUnpack("6e67619308acd0a5a4e911ffae3b6cda7eba62aebf99ace27a53bf0258d4e079"), // dlc.rpf/x64/anim/cutscene/cuts_mpcas3.rpf
    ShaUnpack("743ee16ba477337d3766a53b4970aa5e32e21cfdb90d7a92b4b91ba76e216418"), // dlc.rpf/x64/anim/cutscene/cuts_mpcas4.rpf
    ShaUnpack("c194372f7cf0ca6e0d16ec9a5d0caac5472ec5d164dc63fb256b68a2a5c500a8"), // dlc.rpf/x64/anim/cutscene/cuts_mpcas5.rpf
    ShaUnpack("717a0d1cda1f4fc80e685207a8db8a73a66ce6283faad248109e3e5ee1cb183b"), // dlc.rpf/x64/anim/cutscene/cuts_mpcas6.rpf
    ShaUnpack("559b48c1dc5f3707e297a6de63c2c907b526a59e804b7978be181b761940375a"), // dlc.rpf/x64/anim/cutscene/cuts_mpsui.rpf
    ShaUnpack("64b4deaa3baf340a40ba86e4ca1abb81edd69c4ae8e4fd3a4359a4bda9136614"), // dlc.rpf/x64/anim/ingame/clip_anim@.rpf
    ShaUnpack("5435e8fbd7643800217d0d4ae576c1827ba60b95a11458e0ddd8154abc279efe"), // dlc.rpf/x64/anim/ingame/clip_anim_casino_a@.rpf
    ShaUnpack("abde05354e6587349092e335b19e7144112a285089b9e1a8c49bad5ed5d278f2"), // dlc.rpf/x64/anim/ingame/clip_anim_casino_b@.rpf
    ShaUnpack("ecad6b30464290742976d68bb6db44c870baed1b42832465c69da146a4cb5158"), // dlc.rpf/x64/audio/occlusion.rpf
    ShaUnpack("0639b24d4b0f0cd73430fe7491c2e85d8f994bd8ac751cc55890b470577c5e46"), // dlc.rpf/x64/data/cdimages/moviesubs.rpf
    ShaUnpack("a4e970fa29486f819f644796a00aceaca857b5f8d1929a8affc357d774b70d97"), // dlc.rpf/x64/data/effects/ptfx.rpf
    ShaUnpack("b9b9986bae9ef2436e12ba9ccbad9483a000b9bd727d472c9cd3d6640487d0fe"), // dlc.rpf/x64/data/lang/americandlc.rpf
    ShaUnpack("c6cc5e6c72ff75a49dda304e8706a071cad21e870938bec7800a8fa69f406e17"), // dlc.rpf/x64/data/lang/chinesedlc.rpf
    ShaUnpack("3559dd88053d5dc1a02eafc8e1ba29554982f4b33f060eafbbdb103a165309a4"), // dlc.rpf/x64/data/lang/chinesesimpdlc.rpf
    ShaUnpack("87bfe387253b8f2fb6b47ed601eadb5fa11f3503f2f47bdd59864007739e45cd"), // dlc.rpf/x64/data/lang/frenchdlc.rpf
    ShaUnpack("cc3dab56d108bbcefbe8e39c91eec01b4b90ee0375e8e2ddc9b12d88843a1e04"), // dlc.rpf/x64/data/lang/germandlc.rpf
    ShaUnpack("6f12bfe8f88a5ce33f36d9fe17925f8997872ec5639fa52c6de7b043e6cb3980"), // dlc.rpf/x64/data/lang/italiandlc.rpf
    ShaUnpack("879af850665a584864eddad404fbf90e13e22da57caf86d70ffe6a7cb2e1bbcc"), // dlc.rpf/x64/data/lang/japanesedlc.rpf
    ShaUnpack("104c239b6ecaaf2b523968b44bda71a60c3245d0c69030d8480b515e9614dccd"), // dlc.rpf/x64/data/lang/koreandlc.rpf
    ShaUnpack("01f2e14648eec6bd369015b3051baf8b956ce1897c40f35b260034db0f5fcbe4"), // dlc.rpf/x64/data/lang/mexicandlc.rpf
    ShaUnpack("85404b084e541209ce5724d6796a5b1b763ef43094a1dfdd177279c67a1cad0d"), // dlc.rpf/x64/data/lang/polishdlc.rpf
    ShaUnpack("db6953e424fdf8175e5c38738936b1c76d2ae7e63b394b64cde6d49599b76ad3"), // dlc.rpf/x64/data/lang/portuguesedlc.rpf
    ShaUnpack("6c0c0c5a84778da39a98ede601f0cbe55ca9922fdb7b3f4e541f729767f4bc48"), // dlc.rpf/x64/data/lang/russiandlc.rpf
    ShaUnpack("c4994dc695f783554a298c1a1b7f98a9fe4b6380d092bb4066f549a0036948bb"), // dlc.rpf/x64/data/lang/spanishdlc.rpf
    ShaUnpack("02daa6ab15efb25491196ba521ee1302fea4cf5607fbdf7a41be1e32737ad584"), // dlc.rpf/x64/levels/gta5/lodlights.rpf
    ShaUnpack("27f27457dabefaaee451182252b56f75cbcce85e6bb23ff87387d39c32d7e4d5"), // dlc.rpf/x64/levels/gta5/navmeshes.rpf
    ShaUnpack("ed972ea361938ae186292de4e407418cbebd6eb1e7be6a3a37e94ec5cb27e7fa"), // dlc.rpf/x64/levels/gta5/_hills/cityhills_03/ch3_additions.rpf
    ShaUnpack("85a670b3ac5528cba1c3691db239167bad62c6187a4a0473d931c78800c8d6a4"), // dlc.rpf/x64/levels/gta5/destruction/des_vine_casino_doors.rpf
    ShaUnpack("314a19d02961248f44eabbf7f24c4df4dd0ad8ac63de499ce0df6241b3d8ff51"), // dlc.rpf/x64/levels/gta5/interiors/int_placement_vw.rpf
    ShaUnpack("b64e00765520f1f3318d45ec9aab7fa3befbeb7b1f14b0c0f74e35e5314518a8"), // dlc.rpf/x64/levels/gta5/interiors/vwdlc_int_01.rpf
    ShaUnpack("584955ff32381f5adfce56e4fa3d3d3d7fc73e6bf47ab1cac88e98f1c173d4c1"), // dlc.rpf/x64/levels/gta5/interiors/vwdlc_int_02.rpf
    ShaUnpack("32d8dd46e9e459f7551cd1db142c040e38633b5e1275e70ae4b2d63f9bbc1cb8"), // dlc.rpf/x64/levels/gta5/interiors/vwdlc_int_03.rpf
    ShaUnpack("4102ea8c0a027c7a99d7a5aaa9d02c01a3a3fb309b330c972095dfe95f993eca"), // dlc.rpf/x64/levels/gta5/interiors/vwdlc_int_05.rpf
    ShaUnpack("16cbb2ad5e9537eb0cbb983d1830fc123beb65e84ea8f30ea328d2b7c3e7c695"), // dlc.rpf/x64/levels/gta5/props/prop_vw_accs.rpf
    ShaUnpack("04b1b93cb32be1ddfa9684f766c1a76f57ca84eadddb1f86e3b01a5a80405f7f"), // dlc.rpf/x64/levels/gta5/props/prop_vw_accs_01.rpf
    ShaUnpack("b6874923561a3ff4306a7266ef82dd9db1f2dbd5cb1ee112fc8149346f1b38ed"), // dlc.rpf/x64/levels/gta5/props/prop_vw_animated_walls.rpf
    ShaUnpack("d1a5175926a678ea9665ddbf7dad0bd4c754fcb257f9f31554cdd06a7da29b31"), // dlc.rpf/x64/levels/gta5/props/prop_vw_cabinets.rpf
    ShaUnpack("40382d22cfd3024215253b57ca6b6e5d1d3faa8d6f3c3c9729dca4efbdc2243c"), // dlc.rpf/x64/levels/gta5/props/prop_vw_cards.rpf
    ShaUnpack("6120152d73d0071eeda811d74002cc8948ed0719d51f87951dd2af8673686064"), // dlc.rpf/x64/levels/gta5/props/prop_vw_casino.rpf
    ShaUnpack("41f39518a5edfaf2bf4680b43b4fcc0151980150b5735860ac26e091ae31ffc9"), // dlc.rpf/x64/levels/gta5/props/prop_vw_casino_art_01.rpf
    ShaUnpack("89a06d2fd05127a3b879bfaa691f714b895a2db20add6ea8f74dfb74d18c3a18"), // dlc.rpf/x64/levels/gta5/props/prop_vw_casino_art_02.rpf
    ShaUnpack("55e446f5209fb981c0539033defeca8fa0a54e8c0cd09ab27406546126f4f6d4"), // dlc.rpf/x64/levels/gta5/props/prop_vw_casino_art_03.rpf
    ShaUnpack("39a98ad3a628ed2d6b2afb5eb8de4b87635441071739be9418876c7e7db1e272"), // dlc.rpf/x64/levels/gta5/props/prop_vw_casino_art_04.rpf
    ShaUnpack("021c47f5b172f12c5849ca84a5eb9376e4ae7768038ab1afe30cd2014efa07f8"), // dlc.rpf/x64/levels/gta5/props/prop_vw_casino_art_05.rpf
    ShaUnpack("b74695f8296f1d1661963f927905f1c3b1e77594b37e31020120ea35bc98331b"), // dlc.rpf/x64/levels/gta5/props/prop_vw_casinochips.rpf
    ShaUnpack("42c69ab40019a3ad6e856766aecf85f23ff400462e8a3738acbf956bdaf8373a"), // dlc.rpf/x64/levels/gta5/props/prop_vw_collectibles.rpf
    ShaUnpack("efc9e2af4ae0d67ab98a1598a948924339270459e80b9c46afbeccdeca198bb4"), // dlc.rpf/x64/levels/gta5/props/prop_vw_disposal.rpf
    ShaUnpack("4a68fc2db9d8b9d4ae5d7442bbcd6172359d7b36a68ef3964aa2d1b13046e43b"), // dlc.rpf/x64/levels/gta5/props/prop_vw_door_break.rpf
    ShaUnpack("afbd65f0ede0d032f76a7d8c4cb1118627fc6a6c271b7547cab2dab34ba0fecd"), // dlc.rpf/x64/levels/gta5/props/prop_vw_machinery.rpf
    ShaUnpack("a3bb836e29f8977ec50d1f2c108ff7cb1319112bcd51e6035a6d1fa98213c02d"), // dlc.rpf/x64/levels/gta5/props/prop_vw_screens.rpf
    ShaUnpack("e79709d2c96b55e55f4d14d2e046b34493004fadeb6f789e8289f9446a52e62d"), // dlc.rpf/x64/levels/gta5/props/prop_vw_storage.rpf
    ShaUnpack("928abbcd2927d23554349e876f4ecdbe4676349977efc1978d0b464790c1aabe"), // dlc.rpf/x64/levels/gta5/props/prop_vw_tables.rpf
    ShaUnpack("5a621849b4f9cf07beed4e25f13a6ad1a699274af0c00fe3a70379eeabe1b0e0"), // dlc.rpf/x64/levels/gta5/props/prop_vw_turntable.rpf
    ShaUnpack("a6c54fa573ca32b5e63a06254246c827bdaf3524a6b50736ec7216a96afa7b3e"), // dlc.rpf/x64/levels/gta5/vehicles/mpvinewood.rpf
    ShaUnpack("4e6d25099fe1308326a1ab29c62ae6ef3faacf808d21955b89073e7f66f8fdda"), // dlc.rpf/x64/levels/mpvinewood/vehiclemods/caracara2_mods.rpf
    ShaUnpack("bf1410a6333e74d5c4b4167922f9858ba430a131ba3b6eb81fbc7b827b5fda5b"), // dlc.rpf/x64/levels/mpvinewood/vehiclemods/drafter_mods.rpf
    ShaUnpack("3fc0f6992aad0e21bed860ca1a2533de581a53be1826f824ba086e4a0b3054c5"), // dlc.rpf/x64/levels/mpvinewood/vehiclemods/dynasty_mods.rpf
    ShaUnpack("41f350b0718fe97ecc21bca5c868d69369dd5ad944a658fad60da799728e7a45"), // dlc.rpf/x64/levels/mpvinewood/vehiclemods/emerus_mods.rpf
    ShaUnpack("a6da67d28ee8b2666578dabbcecf8b18ca3fed8161b28ea0831f606561005338"), // dlc.rpf/x64/levels/mpvinewood/vehiclemods/gauntlet3_mods.rpf
    ShaUnpack("2a16eddee9c5b89336a5005281e40d1c746c1f498ed71b29bc51d754d919cd12"), // dlc.rpf/x64/levels/mpvinewood/vehiclemods/gauntlet4_mods.rpf
    ShaUnpack("067dec56b5d564a7cb7e9899e861d8cb6f411198f593f1a636dd099cc7faa5a3"), // dlc.rpf/x64/levels/mpvinewood/vehiclemods/hellion_mods.rpf
    ShaUnpack("842150e818e3abc9d46bc514480788215e7bd240ad585a7f0e88776520da830c"), // dlc.rpf/x64/levels/mpvinewood/vehiclemods/issi7_mods.rpf
    ShaUnpack("979aa46641e201c7ce77a090bd62dfce37396f02c87953b314c8c336ae7b8bc7"), // dlc.rpf/x64/levels/mpvinewood/vehiclemods/jugular_mods.rpf
    ShaUnpack("088f5b3447965df2e3cd830951b187ac0d7b7db6f38340a1a20796784c8ba27b"), // dlc.rpf/x64/levels/mpvinewood/vehiclemods/krieger_mods.rpf
    ShaUnpack("39138726a5775ece44f1055cf8827cd6a6492af558107a4d026e93bec3639cf8"), // dlc.rpf/x64/levels/mpvinewood/vehiclemods/locust_mods.rpf
    ShaUnpack("a630f58ec181745980416c4fc28a476c8db3b462c73c6255f42c5352ca40033d"), // dlc.rpf/x64/levels/mpvinewood/vehiclemods/nebula_mods.rpf
    ShaUnpack("213fd4eecb84a9be4e5583770cedd133e7994b1f21fdf1e139a661caf23a0f3c"), // dlc.rpf/x64/levels/mpvinewood/vehiclemods/neo_mods.rpf
    ShaUnpack("3d8e83c1edadd2c353a0b7af8f554e76db4a9c7d9e0ecc4ba3b891b7d20d6d1c"), // dlc.rpf/x64/levels/mpvinewood/vehiclemods/novak_mods.rpf
    ShaUnpack("661eaad21ec090865397f0dc001a769f37133db0853e7405fec3d89eb12789f7"), // dlc.rpf/x64/levels/mpvinewood/vehiclemods/paragon_mods.rpf
    ShaUnpack("0cd13684c18ec79e87ef046cc3b7af7cb886678a32a684ac0754b32b8b44d937"), // dlc.rpf/x64/levels/mpvinewood/vehiclemods/peyote2_mods.rpf
    ShaUnpack("a5289889c2cfd1c875aef5049fd968ae2d138103043be346333cdc3bb4c811a5"), // dlc.rpf/x64/levels/mpvinewood/vehiclemods/rrocket_mods.rpf
    ShaUnpack("b019ffddd15829ed6eaf9191488951876a2c14e638aa0dc39ed86e861d772a75"), // dlc.rpf/x64/levels/mpvinewood/vehiclemods/s80_mods.rpf
    ShaUnpack("401e3cd580b1edca21fb7c3a4b05e339954341702525d91ae3f3b77eb5eccd7d"), // dlc.rpf/x64/levels/mpvinewood/vehiclemods/thrax_mods.rpf
    ShaUnpack("fc178daa5768252457c426de20140a166cf68dab5f232e550e0bfe9d9669f6d8"), // dlc.rpf/x64/levels/mpvinewood/vehiclemods/zion3_mods.rpf
    ShaUnpack("e7dacb19fda1b739c8326f2f4bbb4f2b641ad19f2d7f1f7015d51fa8f76075e6"), // dlc.rpf/x64/levels/mpvinewood/vehiclemods/zorrusso_mods.rpf
    ShaUnpack("04820ff17bb3aa9701d667f22cd9ba20392126613dd44b757104ff20a5c73e5a"), // dlc.rpf/x64/models/cdimages/mpvinewood_female.rpf
    ShaUnpack("10f222eb467f6713ca9270c4836c4ea3c5a95effa6f754593e804a6391301897"), // dlc.rpf/x64/models/cdimages/mpvinewood_female_p.rpf
    ShaUnpack("5f6764a2ba5bde7c34eb6bae8dc7f574033529b6a33559c8e4d085f44c95ef1a"), // dlc.rpf/x64/models/cdimages/mpvinewood_male.rpf
    ShaUnpack("c1bc24ff24026ec158f043dfdfe027868f49f7f130f92275ad42fd501bceab1e"), // dlc.rpf/x64/models/cdimages/mpvinewood_male_p.rpf
    ShaUnpack("6731710085f10f4cedaf1fadf0787b2bafc56938b7786d23547ae371777b1f85"), // dlc.rpf/x64/models/cdimages/mpvinewood_ped_mp_overlay_txds.rpf
    ShaUnpack("6e7b74abe66c881ae8ee87f6e9ba1a9d82c950c7452ef9c83029988ff2f0521b"), // dlc.rpf/x64/models/cdimages/peds/mpvinewood.rpf
    ShaUnpack("53e8ec1ec54567e857691270bb83cbda295f5db7e71f5a8aa5c76cdb6d9dee16"), // dlc.rpf/x64/models/cdimages/peds/mpvinewood_cutspeds.rpf
    ShaUnpack("bd51207d104dd685ee45654a857ad81bc46057839a51381d5946a1eb0e573544"), // dlc.rpf/x64/models/cdimages/peds/mpvinewood_cutspeds_p.rpf
    ShaUnpack("db4dfcccf097c0ae1d1fefff1c650a38bf8977ff1c00f964c1a5de88d64ae3e5"), // dlc.rpf/x64/models/cdimages/peds/mpvinewood_p.rpf
    ShaUnpack("afe808e5ef2d83fbbcd317b3e060d743e751c38982b42dd73af65c2d061a407c"), // dlc.rpf/x64/models/cdimages/peds/mpvinewood_streamed.rpf
    ShaUnpack("ba04858dce13005933b7a33c33913227bb3628356e42a940704be1264018ba89"), // dlc.rpf/x64/models/cdimages/peds/mpvinewood_streamed_p.rpf
    // update/x64/dlcpacks/mpxmas_604490/dlc.rpf
    ShaUnpack("9120e4db0d25c85f8f20a73182fe3ce1d8cf30420b3e79eec00eb931c8610b45"), // dlc.rpf
    ShaUnpack("61bccd5a7e06e80de3fca17bdbf5ddcca3f8070d53b10ddebeb2cbe4c758294f"), // dlc.rpf/x64/data/lang/americandlc.rpf
    ShaUnpack("a6116b95dc8a1701dffecfec334fe813c0019830eb80b4835a81f6fe7062f9c2"), // dlc.rpf/x64/data/lang/chinesedlc.rpf
    ShaUnpack("2b4152863881739600e19ff9680e78286250b8c93cf67ee4605a1972bb20222e"), // dlc.rpf/x64/data/lang/frenchdlc.rpf
    ShaUnpack("be8c91a9af2664853dd658cb2988fdbe01fb4b6a28c982f9ce3d1f223c521b58"), // dlc.rpf/x64/data/lang/germandlc.rpf
    ShaUnpack("53a6436d72a3dc88ca3525af84ccaa630d1a1ad7bc77b8985f8bfc4e48ebcfce"), // dlc.rpf/x64/data/lang/italiandlc.rpf
    ShaUnpack("937186150080c30a3c77f3053bad580d2c884d4604f714a29e145b5ead5f98c8"), // dlc.rpf/x64/data/lang/japanesedlc.rpf
    ShaUnpack("8c2e1e8ef789167f7b1fa68bba5a76a957dc88f59463ba9d5d3ee8eb36463300"), // dlc.rpf/x64/data/lang/koreandlc.rpf
    ShaUnpack("4f7951dd43e95a92b22dc7733fba0a155111c2498d2553ea2522fc0de406811d"), // dlc.rpf/x64/data/lang/mexicandlc.rpf
    ShaUnpack("d78ec5cb6e49af4be6b5b9f792d78ee61f2951dafaf0e374aee1e36a7e7f1d92"), // dlc.rpf/x64/data/lang/polishdlc.rpf
    ShaUnpack("21f0de04566b90a0eac4b1d28b44588cce765b4de93ec5ab7a5cf5f77e829f0e"), // dlc.rpf/x64/data/lang/portuguesedlc.rpf
    ShaUnpack("30692f591cf1629ade2d3eb8232a6f8a51f55ab2616dfeffdde55374482324f0"), // dlc.rpf/x64/data/lang/russiandlc.rpf
    ShaUnpack("94c1b29677fb45135abf909a55fcbcc44ec9a404375566b0e572495f12168e8a"), // dlc.rpf/x64/data/lang/spanishdlc.rpf
    ShaUnpack("add0fbbcb6e22810590fe7bf8d79dab1da38467552914cad33346a76cfb34c3e"), // dlc.rpf/x64/levels/gta5/props/v_christmas_tree_15.rpf
    ShaUnpack("6f6908b7462dd50f608cc882b1dceca8771e884eb49a046942456b4b53df86d9"), // dlc.rpf/x64/levels/gta5/vehicles/mpxmas_604490vehicles.rpf
    ShaUnpack("4e9befdd64b050e42f59beccd16537e863e002aea178143729585b3f6be69a85"), // dlc.rpf/x64/levels/mpxmas_604490/vehiclemods/tampa_mods.rpf
    ShaUnpack("13c97e4edde8dfef4632ed1822b912cdf64bd2beca33603e27baee6b04255c09"), // dlc.rpf/x64/models/cdimages/mpxmas_604490_female.rpf
    ShaUnpack("e60349fc05e92c427b9befe9535a72a1d78b7b573ef64ce131a3964fc8ccb628"), // dlc.rpf/x64/models/cdimages/mpxmas_604490_male.rpf
    ShaUnpack("25483511baf740f3d5ac8b7bd18042a9349357b78095eaf1229bee6e0399b02a"), // dlc.rpf/x64/models/cdimages/mpxmas_604490_ped_mp_overlay_txds.rpf
    // update/x64/dlcpacks/patch2023_01/dlc.rpf
    ShaUnpack("ca88eceb66c01027348d0217cabc7b8eafed5cc81faf89be5c0794d320470ba0"), // dlc.rpf
    ShaUnpack("638188ad91502b3cf2dbbbac86d05e512bbdad911994686ac734867dfb1d76a0"), // dlc.rpf/x64/levels/gta5/vehicles.rpf
    ShaUnpack("d4f9a412fcfe77a08873b3b03d27afbd0e72469de2caa4c057eaa2b2b2692ed3"), // dlc.rpf/x64/levels/patch2023_01/vehiclemods/boor_mods.rpf
    ShaUnpack("a6be09a6921a8994d693d239a72f22eea718d65f4117a03be4f0ed1b058cc685"), // dlc.rpf/x64/levels/patch2023_01/vehiclemods/r300_mods.rpf
    ShaUnpack("6753f8d4b0f7d4c98a2fcbeb4527749caea345dab88020c397c94a79bc6995d8"), // dlc.rpf/x64/models/cdimages/patch2023_01.rpf
    ShaUnpack("055ad5e14adf28fecdb48bbdb20d9c8e9a87aa01d3b4e6123e5fbea0fc66cdc4"), // dlc.rpf/x64/models/cdimages/patch2023_01_female.rpf
    ShaUnpack("5cfbc28c2d52989c8d95c053733eef78c38ce1274bc0a65cf89527ce3229cd60"), // dlc.rpf/x64/models/cdimages/patch2023_01_female_p.rpf
    ShaUnpack("dc2d16423b6865e6f28b01b23285922b0d09d3ec8b10bf16e78d91afd786eeeb"), // dlc.rpf/x64/models/cdimages/patch2023_01_male.rpf
    ShaUnpack("c2c8ea6c5b7d24a58b17a6fe222e2158a34fa96b1bba7ee28c7c1c11e2ae441f"), // dlc.rpf/x64/models/cdimages/patch2023_01_male_p.rpf
    // update/x64/dlcpacks/patch2023_01_g9ec/dlc.rpf
    ShaUnpack("d3793d0267df6ec7ced5c8efc1f7fbc6547ef0e34aa33744e1142127fe9a94d5"), // dlc.rpf
    ShaUnpack("117ceff0e5c8701c2c29a686ffb7dd44e2d7a94848c99c5b7f43784063e86342"), // dlc.rpf/x64/models/cdimages/patch2023_01_g9ec_female.rpf
    ShaUnpack("44b8aa6f871b36488fb3c3d5b007303209865d7546ebfe2ed1790b150adb08f2"), // dlc.rpf/x64/models/cdimages/patch2023_01_g9ec_female_p.rpf
    ShaUnpack("93dabd0702c05162246012a495d6124090648782f63b0577324237bb642641c0"), // dlc.rpf/x64/models/cdimages/patch2023_01_g9ec_male.rpf
    // update/x64/dlcpacks/patch2023_02/dlc.rpf
    ShaUnpack("f0d655b6e27691333726b6d7f0204e9ac47a9b7e223fc48643f9906e235bb7e0"), // dlc.rpf
    ShaUnpack("c13a88ef2964a6e681d079c70ca461f5cd65dafc7a37881bbf0192cddf3159c9"), // dlc.rpf/x64/anim/ingame/clip_anim@.rpf
    ShaUnpack("b77171eded30eed760381910121da96d16f483b402ddfe4d9effc08bb92ac6c6"), // dlc.rpf/x64/anim/ingame/clip_melee@.rpf
    ShaUnpack("7754b94689af68473ca09d1a5af07d16a1355db983f4f65070ec51007fd53e6e"), // dlc.rpf/x64/levels/gta5/vehicles.rpf
    ShaUnpack("ab26007cf460f6852043a06d1169e6bfc1f12d15b3a59911313adaf04d3ef3cb"), // dlc.rpf/x64/models/cdimages/patch2023_02.rpf
    ShaUnpack("edde741f9f48e3a8f91dec7f7103af27ba83195377caa4990cfcefb3e1642c3d"), // dlc.rpf/x64/models/cdimages/patch2023_02_female.rpf
    ShaUnpack("564f190e658017f3362b34a7695b519f772f8b6cf1cd2b49a8834cc0e214931a"), // dlc.rpf/x64/models/cdimages/patch2023_02_female_p.rpf
    ShaUnpack("5abcd5caf6145a0ba72ff923f8ad23f9ab249807ff76d1fd7a6163867b790ad7"), // dlc.rpf/x64/models/cdimages/patch2023_02_male.rpf
    ShaUnpack("0cd65ba914fd5b078a07de5e08ab949f7afc84535baa94f3c465db191e10ac21"), // dlc.rpf/x64/models/cdimages/patch2023_02_male_p.rpf
    // update/x64/dlcpacks/patchday10ng/dlc.rpf
    ShaUnpack("7933b27f44897fb6cf4080b1ca032ae2a825b5b0aa408b6027e7c12ce4fac7fe"), // dlc.rpf
    ShaUnpack("4af010e512459d4f37131211b82b31e3895fd48d7e4625c77b1bbc345ba2d864"), // dlc.rpf/x64/anim/ingame/clip_anim@.rpf
    ShaUnpack("120d4f2dcff51f0ca7d3291a1b7b9cb8be58fb33d0c49d83251665b8a6eeaf5f"), // dlc.rpf/x64/anim/ingame/clip_cover@.rpf
    ShaUnpack("2d67bcc9bad91c3d3ebe65315ecc3450adc9fd27991e9c9537cd9447d71e339d"), // dlc.rpf/x64/anim/ingame/clip_melee@.rpf
    ShaUnpack("873788c3496d9dc2426f9b3c17a2caa68e5f499b5d5a6a1d719dd46221bd4f4d"), // dlc.rpf/x64/anim/ingame/clip_veh@.rpf
    ShaUnpack("28c5e75da4d1013b18f23104d4fa27dd24486df7cb37f2e3db80062251bb56b6"), // dlc.rpf/x64/anim/ingame/clip_weapons@.rpf
    ShaUnpack("82ddf7e45917689f1875abe12a37a912973f8cf61ca2cccbf5ad936a61238d4a"), // dlc.rpf/x64/levels/gta5/vehicles.rpf
    ShaUnpack("698b6bbafbc11ff562ce77777da544e7d60b00d26c2abc82a1c64aaeb13625a7"), // dlc.rpf/x64/levels/gta5/generic/icons5.rpf
    ShaUnpack("98d6da027bd8cb11bb3e7fbbdc882a5eb3ab3e6e15a7418238e58a6a81159a3f"), // dlc.rpf/x64/levels/gta5/props/industrial/v_airport.rpf
    ShaUnpack("2089a66872890a2a257ae7ab97aeed4cd86bc122b116f3693b0cad97797612c4"), // dlc.rpf/x64/levels/gta5/props/lev_des/lev_des.rpf
    ShaUnpack("24c178169f67b717faba950c2caf2d8a195aca0cf9f485846938d3ea2844fa06"), // dlc.rpf/x64/levels/gta5/props/residential/v_electrical.rpf
    ShaUnpack("00bce44f64d88a5fc1e747de92ce9245b27f986bc9f0db651e1d7e2fb2dbb547"), // dlc.rpf/x64/levels/gta5/props/roadside/v_utility.rpf
    ShaUnpack("a356fd24a10d9a5aa0781e1f861dd30dbac6fb354e98d37bb19ef3c1cb6b4e85"), // dlc.rpf/x64/levels/patchday10ng/vehiclemods/sultan_mods.rpf
    ShaUnpack("2a2b8037db39e02ab65dc9b9ef35e9710791439ab92e61197defbdc7619c1267"), // dlc.rpf/x64/levels/patchday10ng/vehiclemods/tornado5_mods.rpf
    ShaUnpack("2a464613aa7e51307954e4280b18f85eab76e8b04d6d962cceaf7b6428a39778"), // dlc.rpf/x64/models/cdimages/patchday10ng.rpf
    ShaUnpack("47e5d77c9d16595c181e5c7927e6790be36d02a4d9c66f13ac3f868e4df53d0b"), // dlc.rpf/x64/models/cdimages/patchday10ng_female.rpf
    ShaUnpack("6c37411a726117438c97eacd8190914f7cc27839140d7ca7c2b44ae1228765ae"), // dlc.rpf/x64/models/cdimages/patchday10ng_male.rpf
    ShaUnpack("5df1671d9226bca5c8bc5f5d8f2aa66d764f43905b0c546f5762272f872978d3"), // dlc.rpf/x64/models/cdimages/weapons.rpf
    // update/x64/dlcpacks/patchday11ng/dlc.rpf
    ShaUnpack("b3b693f373b23153c09c943c9dac38876d2a3f61d41cac7a59ad6f72c5a1ad0c"), // dlc.rpf
    ShaUnpack("546774a5e6e36217565d0c76b59212d9e28ab62b1747dab6c0a12823f1a1b514"), // dlc.rpf/x64/anim/ingame/clip_amb@.rpf
    ShaUnpack("602e3cefe64ae0b746ad16787601d2b3932c9a6e061c2da70def3f8efe48d91c"), // dlc.rpf/x64/anim/ingame/clip_anim@.rpf
    ShaUnpack("9c773e982e20e8b1b10c7a32ff440e11f0111dfefa5f2e0edaa12cce23e29b08"), // dlc.rpf/x64/anim/ingame/clip_veh@.rpf
    ShaUnpack("32622a916bc8dcbdac55592e647a70c3a304aa04eb562d64b060222b2cbbccba"), // dlc.rpf/x64/levels/gta5/vehicles.rpf
    ShaUnpack("edaa0778ccb165bba9cc03ce1ef59ac64dfff99fab7b7ea519860a0c3fa97c42"), // dlc.rpf/x64/models/cdimages/patchday11ng_female.rpf
    ShaUnpack("0461a6576b4e4990c44c68d180605a222031657468f074d2d7d471b1cd99a73c"), // dlc.rpf/x64/models/cdimages/patchday11ng_female_p.rpf
    ShaUnpack("591ded016efa1a004a135d072e20159a22a5315da3a1fe6ca5bb0359fd35a6ef"), // dlc.rpf/x64/models/cdimages/patchday11ng_male.rpf
    ShaUnpack("00643d5b348ab79d0b9f94e7b228400bde448c1d9b20aff20ff65df09c4c8311"), // dlc.rpf/x64/models/cdimages/patchday11ng_male_p.rpf
    // update/x64/dlcpacks/patchday12ng/dlc.rpf
    ShaUnpack("ca590afb0f01e7ad2c16241d24a98fa5a2f82a43552c425be778977f6d75e83a"), // dlc.rpf
    ShaUnpack("bf189fb8667855e278ee2f7b8156ab0f4a55cf4809521779a77e0b42c59f9f28"), // dlc.rpf/x64/anim/ingame/clip_amb@.rpf
    ShaUnpack("6a5dc8ab21e7c59e754dae0521375133ed679f157b30632bd00e9567e8e80d3a"), // dlc.rpf/x64/anim/ingame/clip_anim@.rpf
    ShaUnpack("0bb4f7635b9c1d23e3ec1c730524b5c034a2d438da06e122c8762991da32265b"), // dlc.rpf/x64/anim/ingame/clip_melee@.rpf
    ShaUnpack("e364772416946f802d053a21a3d23055698278bde35ff764abc5fa21089f3294"), // dlc.rpf/x64/anim/ingame/clip_move_.rpf
    ShaUnpack("e3af270ada2d78cc7a065561ced168cc83ac76be6f867ecc9cd1da31702081e1"), // dlc.rpf/x64/anim/ingame/clip_veh@.rpf
    ShaUnpack("5476b0fb64858552301bd2e56275411424c64e45977d5ddf7baf9a8c9b220c8e"), // dlc.rpf/x64/anim/ingame/clip_weapons@.rpf
    ShaUnpack("56b3444cbc6de99fec35d48c487a3288d5ce4b01ade10a6b4d88576dfb60cbee"), // dlc.rpf/x64/levels/gta5/vehicles.rpf
    ShaUnpack("f9173e40e55b5322beaa8b57149f966a9446ff6f3d91e1fe5367316ee0598494"), // dlc.rpf/x64/levels/gta5/generic/icons7.rpf
    ShaUnpack("b1d66fccc417054e95404bcdced3cb83daaeac4964879ad104dbc9c4bbb61dbc"), // dlc.rpf/x64/levels/gta5/props/vegetation/v_potted.rpf
    ShaUnpack("2e4d6eded2c3226bc651657047a698a3cc08b6702eaa9448bb19f184c2028eb3"), // dlc.rpf/x64/levels/patchday12ng/vehiclemods/bagger_mods.rpf
    ShaUnpack("b405ed40f8cff1f1f6fa8e724c26899f6bd7d4d31a0234bcdc542633ce7dae8a"), // dlc.rpf/x64/levels/patchday12ng/vehiclemods/btype2_mods.rpf
    ShaUnpack("45b83f4eb3a8b4d1e9ef13ae5dc54a2e5efb1d419e79e6df88744b28a7600ad6"), // dlc.rpf/x64/levels/patchday12ng/vehiclemods/gargoyle_mods.rpf
    ShaUnpack("1de7353acaf337a5fd2bc2e4bf49076e5af47a3de86da30b9967d8b29cd1f9a3"), // dlc.rpf/x64/levels/patchday12ng/vehiclemods/wheels2_mods.rpf
    ShaUnpack("d3854c0120b6c40e586e1d256101bb347ba82aebc3b6a30fc6059882c7cd43d2"), // dlc.rpf/x64/models/cdimages/patchday12ng.rpf
    ShaUnpack("a5cb435c2a8d7b8dcc9e2bf889a0947e74b94aae1384215b6b3ac9d201353b11"), // dlc.rpf/x64/models/cdimages/patchday12ng_female.rpf
    ShaUnpack("9c32b637f663e41452b8e0b6d018b0b0d6821a317b7ce7bbd4836d0d64dfb53c"), // dlc.rpf/x64/models/cdimages/patchday12ng_female_p.rpf
    ShaUnpack("4019ebc7e67e089757cf9301380d1f0e350a8350c836f3720cfb50db45c4948f"), // dlc.rpf/x64/models/cdimages/patchday12ng_male.rpf
    // update/x64/dlcpacks/patchday13ng/dlc.rpf
    ShaUnpack("08a8c0fa75657fe5338e8e28f8e528e300f2f4a1a09ecbf2c7ab25d2900540e1"), // dlc.rpf
    ShaUnpack("580efd23297d7111ba9645e5a143af18ed8881074d54fb85ee45f8e9220ccf45"), // dlc.rpf/x64/anim/ingame/clip_amb@.rpf
    ShaUnpack("dd7933cf7d1a1f99793e2622f0751f5d98b132fab9a006a70375427ab050e786"), // dlc.rpf/x64/anim/ingame/clip_anim@.rpf
    ShaUnpack("22cb26a01fa261361de6267a305f7f62c7552c2ac256613ded0084dd279e8a6b"), // dlc.rpf/x64/anim/ingame/clip_move_.rpf
    ShaUnpack("10587130c4b73410f533e6e4aefbe54867857b0e2b1d829ca81a152ad14b6cc4"), // dlc.rpf/x64/anim/ingame/clip_veh@.rpf
    ShaUnpack("8552e65d6205cb18a3d799942035987a07a72b6c4c5596f179f285faacb577e0"), // dlc.rpf/x64/levels/gta5/vehicles.rpf
    ShaUnpack("d687637f4a492ad699bbb47d17e6b9772a67dd3badc1928ab40e8ee7665d2826"), // dlc.rpf/x64/levels/gta5/generic/icons8.rpf
    ShaUnpack("adcf035e1e27388647a8bb8291c2cef7253b0cf4e40ed44a77ee014fcbde921f"), // dlc.rpf/x64/levels/patchday13ng/vehiclemods/banshee2_mods.rpf
    ShaUnpack("2c4da39b1b10eb42fee769e5c077d99153c9bb13377848ef0d05d10f7454e824"), // dlc.rpf/x64/levels/patchday13ng/vehiclemods/blazer4_mods.rpf
    ShaUnpack("0944f6a9380457021542471ff499a511e7d9364428bd40d3d72edd8ceacb1b78"), // dlc.rpf/x64/levels/patchday13ng/vehiclemods/chimera_mods.rpf
    ShaUnpack("c360cfa660f4978dc793aa24e2c5b1208f7ca723e507a5d098b866b06fa2657b"), // dlc.rpf/x64/levels/patchday13ng/vehiclemods/manchez_mods.rpf
    ShaUnpack("40607ee15a370dc6fd71069373228ce9c55fab78147ac367a113f9ae2df704a8"), // dlc.rpf/x64/levels/patchday13ng/vehiclemods/tailgater_mods.rpf
    ShaUnpack("b53be5346a2a09c09c3a4efd8251b8d8f4a57d064ea7a9288da8e7709e7b5472"), // dlc.rpf/x64/models/cdimages/patchday13ng.rpf
    ShaUnpack("ca566b2a053d86ca16b26b8b72e70f2f9ba536d61dceb0a79e55c0a0b0ae1ce5"), // dlc.rpf/x64/models/cdimages/patchday13ng_female.rpf
    ShaUnpack("010f9e326963425fb0d1e73464fa50956f1ef8b7b871ae7bdbd91e2c5993ec93"), // dlc.rpf/x64/models/cdimages/patchday13ng_male.rpf
    // update/x64/dlcpacks/patchday14ng/dlc.rpf
    ShaUnpack("3013dac4cc84eedd77058befed8a85a73ff781ae732bc57319cad6f4aa2112af"), // dlc.rpf
    ShaUnpack("2c7b9a3c71249a11e930bfbac5564352604ed5998d2f7680c7afc3171270a686"), // dlc.rpf/x64/anim/ingame/clip_anim@.rpf
    ShaUnpack("0a8364b40fea481b3fb7c36e3d738f1ea1a0ccafbd8c4c270a75c3485aa12922"), // dlc.rpf/x64/anim/ingame/clip_veh@.rpf
    ShaUnpack("6823ec37636d9541a3f38fea6f6fe45b9a6e3fef90a5f9318938c3a483c9c394"), // dlc.rpf/x64/levels/gta5/vehicles.rpf
    ShaUnpack("c6125f64ff3597d84f17fdd7002c0a9095e2c1aa5b6fb0f84272d347ef650fda"), // dlc.rpf/x64/levels/gta5/props/icons9.rpf
    ShaUnpack("1cb34cd44e14c3dd3144ed96899493b5f1755938306b5bd622df8aaefe876455"), // dlc.rpf/x64/levels/patchday14ng/vehiclemods/elegy_mods.rpf
    ShaUnpack("0f4eedc88274c93bf5a9841ae6da3931869c871903bb138f9b45a52713596003"), // dlc.rpf/x64/levels/patchday14ng/vehiclemods/italigtb2_mods.rpf
    ShaUnpack("8fffa0a653e734238ef063d593fbc5f40561ed3de2f9f9e6a8a240fcad2b7fa2"), // dlc.rpf/x64/models/cdimages/patchday14ng.rpf
    ShaUnpack("1405a92020458cbcd2ca4c127909529cb71d4f6d3a0729ea212b97829eb5fb96"), // dlc.rpf/x64/models/cdimages/patchday14ng_female.rpf
    ShaUnpack("f44e929c7bb35e0259f5b19921ee797fbe5a6f5ae90bb4e66104ce497f55fa6d"), // dlc.rpf/x64/models/cdimages/patchday14ng_male.rpf
    // update/x64/dlcpacks/patchday15ng/dlc.rpf
    ShaUnpack("67ec41cf7407b666d2eff9b9a087d37a03b033dcf854c7747a69615654d2f505"), // dlc.rpf
    ShaUnpack("040e00209d1b5c5a7e62edb3c19ba235970448bb5639c9d037144f9308d7edc7"), // dlc.rpf/x64/anim/ingame/clip_anim@.rpf
    ShaUnpack("560267a2db7968e86c4afbab50960fb0f4153e83281aa14316020606f5a0d910"), // dlc.rpf/x64/anim/ingame/clip_veh@.rpf
    ShaUnpack("2237c6e9af2327ed9581bf9026f330735277ca185bda83888786fb3356031b70"), // dlc.rpf/x64/levels/gta5/vehicles.rpf
    ShaUnpack("e6b030d14a6beaf1ff38927112d00626d80525cfe2fe82035e90f3c5f233100c"), // dlc.rpf/x64/levels/gta5/props/icons10.rpf
    ShaUnpack("669e1ce582464028d42be29650552f9d47bc08d5d77127ebc36b464c6d04d69c"), // dlc.rpf/x64/levels/patchday15ng/vehiclemods/turismo2_mods.rpf
    ShaUnpack("6ba87131847091266bf33c5dad1ec994b873b39224314f136fcf852373c04e96"), // dlc.rpf/x64/models/cdimages/patchday15ng.rpf
    ShaUnpack("6803c30d341f2c6de0c74f22fb89e9e04c406b314169d2e0cfa27739df49715d"), // dlc.rpf/x64/models/cdimages/patchday15ng_female.rpf
    ShaUnpack("635d15175e5df961e70e9f584d2ee67794f8af7e2a7950ffa131628f42270009"), // dlc.rpf/x64/models/cdimages/patchday15ng_male.rpf
    ShaUnpack("7a758be57eb03374bfa9dc73281b364e9008cbb4a95a14c2112df93fb49fdf75"), // dlc.rpf/x64/models/cdimages/patchday15ng_p.rpf
    // update/x64/dlcpacks/patchday16ng/dlc.rpf
    ShaUnpack("a2f8aebd464fee42f9bd50e3b1df3850a6e691ca4102d5dfeec4bc800c73109a"), // dlc.rpf
    ShaUnpack("62099b7136db06e77355381dadfbddb3a9f02773dae240b6b63787152a4ab393"), // dlc.rpf/x64/anim/ingame/clip_anim@.rpf
    ShaUnpack("ba8cad1ee905caec11878e303e62d9be88714bd9f96ccf8b302bc84369ac207b"), // dlc.rpf/x64/anim/ingame/clip_veh@.rpf
    ShaUnpack("7fb8df6d8a3be79bbd6302073e330dadbff8f7c0ad213ab8c296e0d64fc006dd"), // dlc.rpf/x64/levels/gta5/vehicles.rpf
    ShaUnpack("c512f70af860de155df7bc064b215dc3e0f3a942e93c50b03595474147ab444e"), // dlc.rpf/x64/levels/gta5/generic/icons11.rpf
    ShaUnpack("29d6c7e5f47141819f985241ad0c9e12425bff6815ed17f0809ec3bda7b946a9"), // dlc.rpf/x64/levels/patchday16ng/vehiclemods/torero_mods.rpf
    ShaUnpack("33f4695f588ef1c61e516d050375074c77f648d01594ad17ff293bc97d8c9984"), // dlc.rpf/x64/models/cdimages/patchday16ng.rpf
    ShaUnpack("e8218008012b28fd7568190f1c5601014f29ea9a4e3fb6dc84b46abc760a7b63"), // dlc.rpf/x64/models/cdimages/patchday16ng_female.rpf
    ShaUnpack("ca3e4b40128cdd9641c5e46331b0e3a0c80f28e15c68f47411c681136a3b6a42"), // dlc.rpf/x64/models/cdimages/patchday16ng_male.rpf
    // update/x64/dlcpacks/patchday17ng/dlc.rpf
    ShaUnpack("1d443c4e4650e41c1f19c0b0dbe65ed25c54a6e9d3bb80c3ac9b19e0fdc3507a"), // dlc.rpf
    ShaUnpack("6c392e1202eecf5860fbe1326c11d3d34c7982492f0d030a51f390da8e966173"), // dlc.rpf/x64/anim/ingame/clip_anim@.rpf
    ShaUnpack("1a64e41c2f4f76e3383eefa7c280493e476666ea0524a05c3a720c102a32e0f0"), // dlc.rpf/x64/anim/ingame/clip_veh@.rpf
    ShaUnpack("6c3c477bac594456437345cc3bc9bf35412c312b4727adb0c9ae94f6ee991ed2"), // dlc.rpf/x64/anim/ingame/clip_weapons@.rpf
    ShaUnpack("c77c27fc25624ab280ca75b10aa46157fb29258a4d036e8f8c1c05311bf26b7e"), // dlc.rpf/x64/levels/gta5/vehicles.rpf
    ShaUnpack("ab85151a9751786c24f430e276238f6f04027f278501763b5dc9d1e3d6acfb46"), // dlc.rpf/x64/levels/gta5/generic/icons12.rpf
    ShaUnpack("599d7be454a2f9003fbf0cde0fb9c963d7ec4009f1f2a6e367cd37f874167fed"), // dlc.rpf/x64/levels/patchday17ng/vehiclemods/retinue_mods.rpf
    ShaUnpack("def302c655501d83c5222d7ddfbb8624b1a0090d0bd5af51499cbefb26a95592"), // dlc.rpf/x64/models/cdimages/patchday17ng.rpf
    ShaUnpack("0b3c6d4a792f71df4add6e684951273a5d4e52b081647e37740db415cd4f0331"), // dlc.rpf/x64/models/cdimages/patchday17ng_female.rpf
    ShaUnpack("338c554dcf3151a2178acccd731dc305f83d134ae98936e4285a4f808bfdd2e7"), // dlc.rpf/x64/models/cdimages/patchday17ng_male.rpf
    ShaUnpack("05dc8e840049268aafc119c59f73ac44271f58364ae8c64a34f41cbd42b6d8ef"), // dlc.rpf/x64/models/cdimages/patchday17ng_p.rpf
    // update/x64/dlcpacks/patchday18ng/dlc.rpf
    ShaUnpack("2ccc41617a78d54e91ad4836257e2a995d4548d7998e9066927d64142e1fa624"), // dlc.rpf
    ShaUnpack("eac5f434988922c886f9ef5fecaba74f3034395b0f8d16a27471150967e529d4"), // dlc.rpf/x64/anim/ingame/clip_anim@.rpf
    ShaUnpack("84b465d820628be0006ba35eb1116392b04ae5cb5732e77add730696550a4d8c"), // dlc.rpf/x64/anim/ingame/clip_veh@.rpf
    ShaUnpack("def21dce261794cc42ebc3ead735ea854d8a6d8d2f7bc65ea14b51bfeee9e4c1"), // dlc.rpf/x64/levels/gta5/vehicles.rpf
    ShaUnpack("61c744ac142f09b695842b17f55ef3054df7f88571edde5afd9c19359a5eb749"), // dlc.rpf/x64/levels/gta5/props/icons13.rpf
    // update/x64/dlcpacks/patchday19ng/dlc.rpf
    ShaUnpack("90934e91588f179a2275a372a6600a824f04ef19d47cf4b608c56f8c5ef8d569"), // dlc.rpf
    ShaUnpack("2872c3dababa6db5e16577bbe7b31e62d8f7d9fcdba40dd6327d2662feb9e88b"), // dlc.rpf/x64/anim/ingame/clip_amb@.rpf
    ShaUnpack("3b0027d31e30b2788fbd20116e9ab98d314e27299254038f9e78a02525b9db89"), // dlc.rpf/x64/anim/ingame/clip_anim@.rpf
    ShaUnpack("8b9ab9ac74ac771400c4bc866b39cda65b5a5a05a7f15496bdfece39e373ce45"), // dlc.rpf/x64/anim/ingame/clip_facials@.rpf
    ShaUnpack("7d6a3a24b0fd8400903a3c1f451328ee6cbfea4cafdfb24efdb820f85ed67c3e"), // dlc.rpf/x64/anim/ingame/clip_veh@.rpf
    ShaUnpack("79052ee4968c01aa2e00e2abc48b6a36e23475035bff569ebf6a5d8c96f20e90"), // dlc.rpf/x64/levels/gta5/vehicles.rpf
    ShaUnpack("83e23da98b84c23eb659499b03a73f57fedb2c849adbe94b0b91275424500c50"), // dlc.rpf/x64/levels/patchday19ng/vehiclemods/ardent_mods.rpf
    ShaUnpack("3459d7ca01a5e5f0f3ca32795e06fe4633b3fff682981656cbbfc1580454f1cb"), // dlc.rpf/x64/levels/patchday19ng/vehiclemods/barrage_mods.rpf
    ShaUnpack("a43ba1587a8cb0ff60fa07c8eff8eb4f7fb2b21bacffb9dd0bd3b4665e40ccfe"), // dlc.rpf/x64/levels/patchday19ng/vehiclemods/caracara_mods.rpf
    ShaUnpack("21cce1f7318f140680e492fd3f142094cc0c7a87583a45a01c953535baee9610"), // dlc.rpf/x64/levels/patchday19ng/vehiclemods/cheburek_mods.rpf
    ShaUnpack("fcc9be64b20c815a119fd86ed60cb6fd6d3085fc008ecba060f0b66cc8b722f4"), // dlc.rpf/x64/levels/patchday19ng/vehiclemods/coquette2_mods.rpf
    ShaUnpack("9afc7618bfe60f6153af4efbca09de1b8131b6ecb9626d6f950b39d35059f053"), // dlc.rpf/x64/levels/patchday19ng/vehiclemods/dominator3_mods.rpf
    ShaUnpack("6959cd52914c9d5d899242780147b85c41e3b6a11677496535dd90a155335590"), // dlc.rpf/x64/levels/patchday19ng/vehiclemods/entity2_mods.rpf
    ShaUnpack("b9d115fe76b80b3bd8bfef9e82f931c95844abac833a068978c34c764f2a4b8a"), // dlc.rpf/x64/levels/patchday19ng/vehiclemods/futo_mods.rpf
    ShaUnpack("743f6913cb9907156ab20c64c6c053594ad878426aa38d3e412cb4509937a353"), // dlc.rpf/x64/levels/patchday19ng/vehiclemods/gb200_mods.rpf
    ShaUnpack("6317d1efbbe7f804ff21a3de6011a58e80ee1f7ac4ba19995a546febde5c78bb"), // dlc.rpf/x64/levels/patchday19ng/vehiclemods/hermes_mods.rpf
    ShaUnpack("29d79d45c4e6f38b4345741b9e5390f750e2f3188b584c0863916753c1c45dcf"), // dlc.rpf/x64/levels/patchday19ng/vehiclemods/jester3_mods.rpf
    ShaUnpack("537ab251ee7eaeaab298e400d36baca8c26b0e2d1dce4e7b48a041c40d26303f"), // dlc.rpf/x64/levels/patchday19ng/vehiclemods/kamacho_mods.rpf
    ShaUnpack("0436ef4dd502ff133ecc83b24d293a12f19593fa7a8e44e3a8be901a35a944c4"), // dlc.rpf/x64/levels/patchday19ng/vehiclemods/mamba_mods.rpf
    ShaUnpack("ac59682542161c852c8ed3f66d56d8ef4bcec86bcc4cf6b727268f14721d4b46"), // dlc.rpf/x64/levels/patchday19ng/vehiclemods/nightshark_mods.rpf
    ShaUnpack("f1c69e830f681a2bca97327f1d4be3b925ca7ac2e51cca097996a502701ace7c"), // dlc.rpf/x64/levels/patchday19ng/vehiclemods/penumbra_mods.rpf
    ShaUnpack("b0ae64654e180d9dca6d7a00c1832b1c2689c8a68cca69358b836ae6d6fa5068"), // dlc.rpf/x64/levels/patchday19ng/vehiclemods/prairie_mods.rpf
    ShaUnpack("e1e1e8c0a5f01815d4f59e949571bb1cdb4d113472fabab7664b8fd3d6e1cb09"), // dlc.rpf/x64/levels/patchday19ng/vehiclemods/riot2_mods.rpf
    ShaUnpack("2cb3adf4fa69cfa12c33294fe92ddf585f1d9ebfa8c646720e5c79a3a242af5e"), // dlc.rpf/x64/levels/patchday19ng/vehiclemods/ruiner_mods.rpf
    ShaUnpack("587a2c639c033d8cbfd761e08c32954e4b729c21ca5fc71b50e6f6652b64c30b"), // dlc.rpf/x64/levels/patchday19ng/vehiclemods/savestra_mods.rpf
    ShaUnpack("9d8935b31bdb3cf4ef6cedc4052b8eea2328a4ec15c03e5f5f66cbab6dcfd686"), // dlc.rpf/x64/levels/patchday19ng/vehiclemods/vehcamo_mods.rpf
    ShaUnpack("d14fa1f4345fc694c58366a4d3673e3fe533a8fe08903df3a219b31465768852"), // dlc.rpf/x64/levels/patchday19ng/vehiclemods/z190_mods.rpf
    ShaUnpack("61cbf6edb9e4d9d7e1ae8f8d475e768037e13c46f7e86dcd91ab9e9866428897"), // dlc.rpf/x64/models/cdimages/patchday19ng_female.rpf
    ShaUnpack("1de7dbcd3f4a2887589885ca32f9986b52f4f2d548196d53263b515d8431cadb"), // dlc.rpf/x64/models/cdimages/patchday19ng_male.rpf
    ShaUnpack("9992109c6aa17e11dca4022ef1dbf6480919f09fa106e0eb6679c61f5ea7977b"), // dlc.rpf/x64/models/cdimages/peds/patchday19ng.rpf
    // update/x64/dlcpacks/patchday1ng/dlc.rpf
    ShaUnpack("6e8cb7b2363a5da55c3b5c91c12c24455938215601a04cc0307bbaa873b7f051"), // dlc.rpf
    ShaUnpack("dcad873dc7e014f249eb8da6c47af7d3a2f9a6f1ac3ed18701694feaa8dd76f4"), // dlc.rpf/x64/txd_map_patches.rpf
    ShaUnpack("91360a7e1bc5e490fadaee71d35944ca412e1c9135fe9f00be6bd86eecac20dc"), // dlc.rpf/x64/txd_patches.rpf
    ShaUnpack("a655ea0aa419fbf361d39b1d1731e9ba6b67854344485de5a82a9c843dd70d65"), // dlc.rpf/x64/anim/expressions.rpf
    ShaUnpack("37065f56e71389b10d4eed86caa7499a4629a440cdb5ef4bad41075f48e8358e"), // dlc.rpf/x64/anim/networkdefs.rpf
    ShaUnpack("b10ef7fc72eb243aa56aba93e85f78cf0b3db60c693c9bc984bfcead1f39d7b2"), // dlc.rpf/x64/anim/cutscene/cuts_ah_1.rpf
    ShaUnpack("e33efa31328a1a60e005f5182537db382ef8ed47e2ee76adaae2251724b321ff"), // dlc.rpf/x64/anim/cutscene/cuts_drf.rpf
    ShaUnpack("7a038cf1d5c90457820546e83a9abae5a69498213ab5b37c7ad134a14cb6fb53"), // dlc.rpf/x64/anim/ingame/clip_amb@.rpf
    ShaUnpack("f251497a72a0af1669d9c6658d3501f396811439fbc9b9de349cfb7c9fd6ab3e"), // dlc.rpf/x64/anim/ingame/clip_cellphone@.rpf
    ShaUnpack("b743a48296201e0a90de7b215e9b8aaf258358c71bce5347140c578ebd6e7ba2"), // dlc.rpf/x64/anim/ingame/clip_cover@.rpf
    ShaUnpack("ff0a58b218d82fd0f5ef0e542f69fd8b3b4079fa2660d076fddb1e6bbda7cb4f"), // dlc.rpf/x64/anim/ingame/clip_creatures@.rpf
    ShaUnpack("1c38ecac547840fe975f08224d4ac7ee452165f3bbad5c123f913197e3790c32"), // dlc.rpf/x64/anim/ingame/clip_facials@.rpf
    ShaUnpack("2bfaf21a4c6f1c18539bf6ba95136a19d8e40abdbaf864cd96f1a13ea7bddc42"), // dlc.rpf/x64/anim/ingame/clip_get_up@.rpf
    ShaUnpack("be1c15f50a17e3614be5ab97a204354e6aed56e9aa74f272a30c41cd360c53c8"), // dlc.rpf/x64/anim/ingame/clip_ladders.rpf
    ShaUnpack("38349b4dfb0ceff139bcd9a4805ce228012d14f5de4921043b861cb6856caafe"), // dlc.rpf/x64/anim/ingame/clip_melee@.rpf
    ShaUnpack("e88d9a7e6914981452229dd04a2135201903151038e96f2aed43307e79ec7468"), // dlc.rpf/x64/anim/ingame/clip_mini@.rpf
    ShaUnpack("574a38e4211e87bfaab002d97401909decefe5b1ff1ce9d0b21654c1cd123fe5"), // dlc.rpf/x64/anim/ingame/clip_miss.rpf
    ShaUnpack("efa40ce047fe3e01fb982f2b7bb3e96860ff46975533dbf1b34be7785089b8b2"), // dlc.rpf/x64/anim/ingame/clip_move_.rpf
    ShaUnpack("1be5da5f93fb1677cb976fb683cfff50debecba2ea7a65c1b440b47b80fe6790"), // dlc.rpf/x64/anim/ingame/clip_mp_.rpf
    ShaUnpack("18fdab703f33e8754719cecb143e2a851fb44cf75dab62757d29db885b480e6c"), // dlc.rpf/x64/anim/ingame/clip_oddjobs@.rpf
    ShaUnpack("88f02ed07dbd232eb2721d3635e07bd87e593e2ae2be586c6d3368a8ecf6e9bb"), // dlc.rpf/x64/anim/ingame/clip_random@.rpf
    ShaUnpack("0ad7f60fd9229d3405cc1c3ab1ae2c98b1c3eeed388e1f35c6767ed1c1c89bdc"), // dlc.rpf/x64/anim/ingame/clip_safe@.rpf
    ShaUnpack("25656ebc16f94d0bfe7696abd35afc1c1e91cce9d9035c86569214aaa233894c"), // dlc.rpf/x64/anim/ingame/clip_save.rpf
    ShaUnpack("eb48f2cd8612be399835b810cb993ae1807a23afd9f3bcb41b382cb95856c32d"), // dlc.rpf/x64/anim/ingame/clip_skydive@.rpf
    ShaUnpack("a14b712054d712598d79db0d9720b2c3ec7ab8080f7c754abb63f5275a3169d8"), // dlc.rpf/x64/anim/ingame/clip_swimming@.rpf
    ShaUnpack("a5c6a47d8dd4f02eb87ec9abe1cf76c55a4ce405ee97a97c8621e711ba479ed1"), // dlc.rpf/x64/anim/ingame/clip_veh@.rpf
    ShaUnpack("5fa83039c3e8a62b70f46a166cb916a53200b48f32a22e8c3a5696493ae70f37"), // dlc.rpf/x64/anim/ingame/clip_weapons@.rpf
    ShaUnpack("2a1bd052110934d71b29eafd3ffd44c11de7bcdc5ff9c781bf9b058a6d3faff5"), // dlc.rpf/x64/levels/gta5/vehicles.rpf
    ShaUnpack("19727d6d030f15bd4d6c508193a71176bb8840b3506b70d4b69de138473df7e9"), // dlc.rpf/x64/levels/gta5/_citye/downtown_01/downtown.rpf
    ShaUnpack("f89fbf350595598cd1b2f418cd0ce22bf214dc93596c5c7d62d03005797645c2"), // dlc.rpf/x64/levels/gta5/_citye/downtown_01/downtown_01_metadata.rpf
    ShaUnpack("c37f2b50531cde2aab752324517836fc19f5c4c28715dbd12dc66b2ba86d86b1"), // dlc.rpf/x64/levels/gta5/_citye/downtown_01/dt1_03.rpf
    ShaUnpack("a0cec20aeb1a875191d5023a749f0a66ba2bb8c3cf810c5724d4377c1306df1c"), // dlc.rpf/x64/levels/gta5/_citye/downtown_01/dt1_19.rpf
    ShaUnpack("ee62e649f74a0b02913a3c658fb98b4c25b4c4d96cd58d97f0129048cf6e1c24"), // dlc.rpf/x64/levels/gta5/_citye/downtown_01/dt1_occl.rpf
    ShaUnpack("62472a49346e95c9813d33ed64b2e1779fa86bee63a2a373988b8c930546f393"), // dlc.rpf/x64/levels/gta5/_citye/downtown_01/fwy_01.rpf
    ShaUnpack("34217be845f48d9fb4882c1c91919d8ab8fd8feb8f4ca519600a43b2f43f4645"), // dlc.rpf/x64/levels/gta5/_citye/downtown_01/fwy_04.rpf
    ShaUnpack("aeacdb724ae5015becf62f3b4a1e9e25b49b106346f6ca0e4f6ee9bf891622e2"), // dlc.rpf/x64/levels/gta5/_citye/hollywood_01/hollywood_metadata.rpf
    ShaUnpack("a57cf16c59cfad0d3a30fa1f4b68bb0242a050b0ab3c7272fea0a4509c719cf0"), // dlc.rpf/x64/levels/gta5/_citye/hollywood_01/hw1_02.rpf
    ShaUnpack("91346d4cf706e890feb617fae32963d1a3eed84c9551c9f6375514e7629a6863"), // dlc.rpf/x64/levels/gta5/_citye/hollywood_01/hw1_27.rpf
    ShaUnpack("172407ac313945f3e64c3d5ac8167eb700821ab78d473127f92d3ef73ba9ca36"), // dlc.rpf/x64/levels/gta5/_citye/hollywood_01/hw1_instance_placement.rpf
    ShaUnpack("ce86242d58cbcc77caf94789b7068a588fa0dd1ca469cbb5f96577b767557ee0"), // dlc.rpf/x64/levels/gta5/_citye/indust_01/id1_instance_placement.rpf
    ShaUnpack("6c21b88e840dd3e79502d26bca742432f5c549d028577601d80eb8146e294c2b"), // dlc.rpf/x64/levels/gta5/_citye/indust_02/id2_13.rpf
    ShaUnpack("eaf7bccb8d0cae8c6481a202b42363970152b2d4c437e762767faac5847addd2"), // dlc.rpf/x64/levels/gta5/_citye/indust_02/id2_21_a.rpf
    ShaUnpack("40dd4fa4c4a8a5d9d96d9132dd8ff41b33423ca9b1accd6f0da0a635276b506c"), // dlc.rpf/x64/levels/gta5/_citye/indust_02/id2_21_b.rpf
    ShaUnpack("4a65957bdc4ec92edbeb102b8a37df977f9891f59d27e5cd66e3bb5f2175e290"), // dlc.rpf/x64/levels/gta5/_citye/indust_02/id2_21_c.rpf
    ShaUnpack("ef6fe27f33d9188318bb7f07e4d3bcf632f38859be251a80e00e856daaa9d93e"), // dlc.rpf/x64/levels/gta5/_citye/indust_02/id2_21_d.rpf
    ShaUnpack("f009fd716de198a5f90c3243f371bed3a968055a8cf15afd34766b2f9b1a7551"), // dlc.rpf/x64/levels/gta5/_citye/indust_02/id2_instance_placement.rpf
    ShaUnpack("9baaaa7231c55d04c799ecebf18cf4fdeb80528262661a8982a9b5360bdcd2eb"), // dlc.rpf/x64/levels/gta5/_citye/indust_02/id2_rd.rpf
    ShaUnpack("70be9b08f2cf1c37d06e6cc2f4d9b42e7ef15e0a99ca03bf4eaf29c4184b1152"), // dlc.rpf/x64/levels/gta5/_citye/indust_02/id2_rdb.rpf
    ShaUnpack("0069d93e08fbff81dda64664a584683ba058d8475ceb8525bc5ee08055a59e18"), // dlc.rpf/x64/levels/gta5/_citye/indust_02/indust_02_metadata.rpf
    ShaUnpack("83dfd7a2a2679a22fd3a3af92f9a53a289ed7f82432e59503038bf6bce80920a"), // dlc.rpf/x64/levels/gta5/_citye/port_01/po1_occl.rpf
    ShaUnpack("db52a5630c6beaf80a2c2c73e925f8dfe6778a51fba52f3805893417337a6979"), // dlc.rpf/x64/levels/gta5/_citye/scentral_01/sc1_12.rpf
    ShaUnpack("93a02025cf10191aab43e686a35c9394507fcfa32e2d5e9cdc175f88848f62e3"), // dlc.rpf/x64/levels/gta5/_citye/scentral_01/sc1_31.rpf
    ShaUnpack("8325eee3c30ff079b4c6d381febee01118de535f52af690439d5636c813de0e0"), // dlc.rpf/x64/levels/gta5/_citye/scentral_01/sc1_32.rpf
    ShaUnpack("8c592065f2445f60e8640b59fcbf5c0e1f58de7d41a6125ff18f4ff5a379376c"), // dlc.rpf/x64/levels/gta5/_citye/scentral_01/sc1_occl.rpf
    ShaUnpack("c99196f60e165ce15d858e3aba06a57d756fb4de68801ada6606ae33b9a7ba17"), // dlc.rpf/x64/levels/gta5/_citye/scentral_01/scentral_metadata.rpf
    ShaUnpack("d127564cf89642de1dfb100bcc0f0ea27878fe423dc708c13155885fc1210ffd"), // dlc.rpf/x64/levels/gta5/_citye/sunset/ss1_13.rpf
    ShaUnpack("0b682f07453e0de8a211f6a50559a935cca615f396e215e084ec792008d937df"), // dlc.rpf/x64/levels/gta5/_citye/sunset/sunset_metadata.rpf
    ShaUnpack("127fb11e28ad4d3a7822d85c8441691190f51887d0a98a1fb5cabbab72721bba"), // dlc.rpf/x64/levels/gta5/_cityw/beverly_01/beverly_metadata.rpf
    ShaUnpack("fa3318300fdb2d30aca18707d96b58e2af22337bfb7b2a92a0b3ed8aabd45575"), // dlc.rpf/x64/levels/gta5/_cityw/beverly_01/bh1_08.rpf
    ShaUnpack("4beca59c880fe55bdb1f6f4b8516a5c78f8d0d6f9cf78feb16d3537095cfc0d6"), // dlc.rpf/x64/levels/gta5/_cityw/beverly_01/bh1_21.rpf
    ShaUnpack("2289133bbe04e151dab834d05dcb9cce7ac1112c2a6f4cd1738b12a0448fc183"), // dlc.rpf/x64/levels/gta5/_cityw/sanpedro_01/sanpedro_metadata.rpf
    ShaUnpack("10c8a3b266694112326b0dfef5aab003cd17c732339eac15fdd5baae68a843e6"), // dlc.rpf/x64/levels/gta5/_cityw/sanpedro_01/sp1_rd.rpf
    ShaUnpack("78d6d08d53d14bdace669bc001884110b7d253f7cd4f37aa7b6731b37f07aa5d"), // dlc.rpf/x64/levels/gta5/_hills/country_01/country_01_metadata.rpf
    ShaUnpack("bfcf86b073fb98ba0a705bb5eb15e321ab2be5e64915bf3d068e0ce7a8d8bda9"), // dlc.rpf/x64/levels/gta5/_hills/country_01/cs1_02.rpf
    ShaUnpack("1e3695e1a3fdf226566ca06d75647c84a30d658b60abeebdf6c8eb5525de255d"), // dlc.rpf/x64/levels/gta5/_hills/country_04/country_04_metadata.rpf
    ShaUnpack("e38560ea7933b8e6e5a0946c82dd43fd46f0d18ea7bed0fc8bb04a7c66e3d1fa"), // dlc.rpf/x64/levels/gta5/_hills/country_04/cs4_03.rpf
    ShaUnpack("7b0125a735efcb7b8b269ce972ed9355c22f64c46d84dbe6192bd0845e692502"), // dlc.rpf/x64/levels/gta5/_hills/country_06/country_06.rpf
    ShaUnpack("a6825b09dbc36fc89541fa849307869deebb7526bac35cc2520f523fb1a45bf1"), // dlc.rpf/x64/levels/gta5/_hills/country_06/country_06_metadata.rpf
    ShaUnpack("0c43049de217a8e14f57b14a2acd91dd58a832f859fa33b864c0cda28328c7de"), // dlc.rpf/x64/levels/gta5/_hills/country_06/cs6_03.rpf
    ShaUnpack("2e69eb003e4bbf7ec4df422c64166d6e6ec170fe6186da47b1b6f76f3a95fcf1"), // dlc.rpf/x64/levels/gta5/_hills/country_06/cs6_instance_placement.rpf
    ShaUnpack("264c170e00914049185eb34becb122a600bf94c430a3e5dbfe7073cf699799f0"), // dlc.rpf/x64/levels/gta5/props/v_ng_proppatch_01.rpf
    ShaUnpack("d9ba942f2e2122c9e14878848f1f17f29990a984a6acc15f70961960acaabbbc"), // dlc.rpf/x64/levels/gta5/props/building/v_rooftop.rpf
    ShaUnpack("80154506b51324cea73bdc8549d8e5f72838cf9bb5182a046b62c451528f6e16"), // dlc.rpf/x64/levels/gta5/props/industrial/v_airport.rpf
    ShaUnpack("39c8767b16a0010b838d3b53a301078054059b4cfc765098d8c7078e63d80fa3"), // dlc.rpf/x64/levels/gta5/props/lev_des/v_minigame.rpf
    ShaUnpack("de05bd3891f028c25f300ea654d670799db40b526b6ac1e743caaece621e3e42"), // dlc.rpf/x64/levels/gta5/props/lev_des/v_set_pieces.rpf
    ShaUnpack("77412d58c539710419447eb19dba89f806d688e38020e6b1da7f31d457880221"), // dlc.rpf/x64/levels/gta5/props/recreational/v_coin_op.rpf
    ShaUnpack("8c5d87def66a58da039a65c8d8d1813948dd5de34be393ec9cd05a1d99d08892"), // dlc.rpf/x64/levels/gta5/props/recreational/v_sports.rpf
    ShaUnpack("a79380062c65f2bfe5da109d79773febaacc92108d79d6b1f5f4c1d73dec2d5c"), // dlc.rpf/x64/levels/gta5/props/roadside/v_construction.rpf
    ShaUnpack("6d34fcbec5386c8898dc472eecb126d0bbd0afa5392986ee16df69eade9eabf6"), // dlc.rpf/x64/levels/gta5/props/roadside/v_fences.rpf
    ShaUnpack("9b6f29b03890712f89df8b928f6592393265b57cad4e34e8cedf4104300cfebe"), // dlc.rpf/x64/levels/gta5/props/roadside/v_rubbish.rpf
    ShaUnpack("3d3340d177616e2311391845afe98a921c9171e5ce8404e8afbfe5c040086625"), // dlc.rpf/x64/levels/gta5/props/vegetation/v_bush.rpf
    ShaUnpack("e212b881ff7090d49c6359cbb4827ab4677d82e768afe85f5934c6c3b3bb48de"), // dlc.rpf/x64/levels/gta5/props/vegetation/v_cacti.rpf
    ShaUnpack("5d4562f4b2ab664e254c0fd1f3234762d114096f51c9639148c875ed36b2a995"), // dlc.rpf/x64/levels/patchday1ng/vehiclemods/akuma_mods.rpf
    ShaUnpack("c1e59ae19e75bb67045ee390c2add81612ae50c5d38bd94542348e0e12872eaa"), // dlc.rpf/x64/levels/patchday1ng/vehiclemods/alpha_mods.rpf
    ShaUnpack("214d2d454f581a3a57b758b40f2a527e238866a34b4b8f85c74114295d3d30d1"), // dlc.rpf/x64/levels/patchday1ng/vehiclemods/banshee_mods.rpf
    ShaUnpack("d9b49edb1cb6a031e22220b81c9bb0278f8b3e70b96530b96b2f7a32a8909813"), // dlc.rpf/x64/levels/patchday1ng/vehiclemods/bifta_mods.rpf
    ShaUnpack("73f2014161063adcee629e959e9c491d9cae317a7490d0193b6543944876effb"), // dlc.rpf/x64/levels/patchday1ng/vehiclemods/blista_mods.rpf
    ShaUnpack("9f214652a0b9e70bbd550d15cd4c681a881b6374bff4aca149c3ef46ce79d388"), // dlc.rpf/x64/levels/patchday1ng/vehiclemods/bodhi2_mods.rpf
    ShaUnpack("539d4f1251a3529862f3635c9c4c767ce5023814b9a92f80ca0d9cf935f30f62"), // dlc.rpf/x64/levels/patchday1ng/vehiclemods/buccaneer_mods.rpf
    ShaUnpack("bd73bd211171004b36563f3ecb5f2f47c2fcac098e2416824c8883ac1d741348"), // dlc.rpf/x64/levels/patchday1ng/vehiclemods/buffalo2_mods.rpf
    ShaUnpack("1399c3a8c5b2d56d62d2b1772c81f3dc8f5975d15486f0244eda8c0c77cb3977"), // dlc.rpf/x64/levels/patchday1ng/vehiclemods/buffalo3_mods.rpf
    ShaUnpack("89d0acb02e522c165d77928095864c98b38e930aa5014ff0fdbe675b92752c20"), // dlc.rpf/x64/levels/patchday1ng/vehiclemods/buffalo_mods.rpf
    ShaUnpack("4c0e05754693d3000f678dd9b86d05f13fd4d003809463dba47e62cad3433036"), // dlc.rpf/x64/levels/patchday1ng/vehiclemods/cavalcade_mods.rpf
    ShaUnpack("99428a3f7750f64e24c4709fcf70248e9e2b0f5e27eb15b9329940202f83407e"), // dlc.rpf/x64/levels/patchday1ng/vehiclemods/comet2_mods.rpf
    ShaUnpack("74f52561ec7f006c06a989e1bd04c2c70d2337c808c94c9db0933795471e225e"), // dlc.rpf/x64/levels/patchday1ng/vehiclemods/coquette2_mods.rpf
    ShaUnpack("b9f6afc3d9c275ffc01fe9f60b0eb6769201ea222f99e405dfcbf5ae9f94ff8b"), // dlc.rpf/x64/levels/patchday1ng/vehiclemods/dubsta2_mods.rpf
    ShaUnpack("f8005458026bee4c5271e38705956588aaa001d9e8656fb4d92ae3e9400c8c97"), // dlc.rpf/x64/levels/patchday1ng/vehiclemods/dubsta3_mods.rpf
    ShaUnpack("012d6dc14061ee03cdd4553d0cad6a9af419cc17f987eee08022b1c5905cc006"), // dlc.rpf/x64/levels/patchday1ng/vehiclemods/dubsta_mods.rpf
    ShaUnpack("ab83d2c1b6b143d87e3714bdeadbe70d161dc2ce9e6d6e035d2995f48bff3301"), // dlc.rpf/x64/levels/patchday1ng/vehiclemods/elegy2_mods.rpf
    ShaUnpack("84d6a8e44ba51f7129d17d88ade39731c804e1aa53ad9ac8f59d57bad5fee9b7"), // dlc.rpf/x64/levels/patchday1ng/vehiclemods/felon_mods.rpf
    ShaUnpack("f6525a706f052525af32adb0f330f85c06b43fb65a722d953b0ebccbd30b4b3f"), // dlc.rpf/x64/levels/patchday1ng/vehiclemods/feltzer2_mods.rpf
    ShaUnpack("a15045d8a5cef728a40bc70237df76782f0af82dd92af538ca4282f5905a281f"), // dlc.rpf/x64/levels/patchday1ng/vehiclemods/futo_mods.rpf
    ShaUnpack("52d8e77ca87a701ada12396662b8a468e05f55ade98b61a49505677725716d44"), // dlc.rpf/x64/levels/patchday1ng/vehiclemods/glendale_mods.rpf
    ShaUnpack("3eb0a460a43863eb77609473a420b39b4170f5ddd96bc03d04823e6c7137edad"), // dlc.rpf/x64/levels/patchday1ng/vehiclemods/gresley_mods.rpf
    ShaUnpack("732293433557db96dfc75d1640ac61931314c56e1d38a691ccd66dbdd8afbf59"), // dlc.rpf/x64/levels/patchday1ng/vehiclemods/huntley_mods.rpf
    ShaUnpack("d74158af9ffe8e65dd0fc1615ab525a54bcb0caf9fae93e9fd86ddc8e07ca8c8"), // dlc.rpf/x64/levels/patchday1ng/vehiclemods/intruder_mods.rpf
    ShaUnpack("a986d3724327009f7acaa6086bbcfa0178e0dddaac6433eca142f474e0d7d29d"), // dlc.rpf/x64/levels/patchday1ng/vehiclemods/issi2_mods.rpf
    ShaUnpack("e7dc3906b7f41753d342546aa316e31103cf9a7979af19568264c9cebfe57fbe"), // dlc.rpf/x64/levels/patchday1ng/vehiclemods/jackal_mods.rpf
    ShaUnpack("289e6c52957a0d58ed6f49666c911b78633557ad75b67de1c6907f0baa382c88"), // dlc.rpf/x64/levels/patchday1ng/vehiclemods/jester_mods.rpf
    ShaUnpack("06b7fe8a04723ad6c50f954a48aa4573d3d1ae2977a4af21782ad3df34d4d6ae"), // dlc.rpf/x64/levels/patchday1ng/vehiclemods/massacro_mods.rpf
    ShaUnpack("c00b021cd07cf0baabbd168ccdc8a2c70817cdfc4e5fbedee48d862b4dedd780"), // dlc.rpf/x64/levels/patchday1ng/vehiclemods/panto_mods.rpf
    ShaUnpack("51fc8c25fc3e76569d9585d808091241313298867bace1a3874577f4dd00f593"), // dlc.rpf/x64/levels/patchday1ng/vehiclemods/phoenix_mods.rpf
    ShaUnpack("46e5f7e5c34aa3c9826f2e00f45ac49b90be13dd9eb2c5064e608ffe2954c60c"), // dlc.rpf/x64/levels/patchday1ng/vehiclemods/sabregt_mods.rpf
    ShaUnpack("2532f85dcc7b25fb003594664de5fe099c5f52fe4d22e14d3c0925b5d7d9994a"), // dlc.rpf/x64/levels/patchday1ng/vehiclemods/tailgater_mods.rpf
    ShaUnpack("6c28c8b15f77b59b5db1a4162f422571d6a6e8e929bbe743bd0f0ee629985fc5"), // dlc.rpf/x64/levels/patchday1ng/vehiclemods/turismor_mods.rpf
    ShaUnpack("d974f66e000bad8dd791b930326232b4e052436103b958aab49041908066f806"), // dlc.rpf/x64/levels/patchday1ng/vehiclemods/vigero_mods.rpf
    ShaUnpack("5391231f6a76b58ee0ec6abf35fc027a56e16ae84aafcad3fa31b102e7d20c5d"), // dlc.rpf/x64/levels/patchday1ng/vehiclemods/warrener_mods.rpf
    ShaUnpack("bc9ab5b035b2f3beb1a83c413c407e7fa07ca3404a99692ebfc0ca089034f141"), // dlc.rpf/x64/levels/patchday1ng/vehiclemods/youga_mods.rpf
    ShaUnpack("f3cc815e3845a0075a5f890411ce42335a4e45122b49aeea333ea0722dbe8ba5"), // dlc.rpf/x64/models/cdimages/ped_mp_overlay_txds.rpf
    ShaUnpack("17854bc98a7e56658ed6c7eeb59350859223877b46486f920f44d5905dfa6af2"), // dlc.rpf/x64/models/cdimages/streamedpeds_strm.rpf
    ShaUnpack("c7a10dcc490d9516eb2ad4d659c9717198bfe5ab9a9ffa50a780a7ed512cd564"), // dlc.rpf/x64/models/cdimages/weapons.rpf
    ShaUnpack("6fe6c72e3962a9c375c626bfd1607e48d09598213ba32941570a204905e3568a"), // dlc.rpf/x64/patch/data/lang/american.rpf
    ShaUnpack("e513196995db8a156041f6d574593d0ec4ebf0ca62207cea35296dc169b32926"), // dlc.rpf/x64/patch/data/lang/chinese.rpf
    ShaUnpack("ee8c531e27cef670a3b5cb385e6ebc634e3d0153643cca8244e472ccbd55c092"), // dlc.rpf/x64/patch/data/lang/french.rpf
    ShaUnpack("f1d85426bc2b8daf85d01dd6b5a3ed0b0019c49fcc9c174a092adc483b1f085e"), // dlc.rpf/x64/patch/data/lang/german.rpf
    ShaUnpack("822211a177bf58387c3e52373d87475f8d2de65b50864612f18161dd5b50db76"), // dlc.rpf/x64/patch/data/lang/italian.rpf
    ShaUnpack("f5e5deba18f1545f8bd718f0332966eef0294198e0720ca01a60bcbba4df2deb"), // dlc.rpf/x64/patch/data/lang/japanese.rpf
    ShaUnpack("abaf6289419f3bcdecb6b205877eb67817d3afef1863a6df98c685e08dd112f8"), // dlc.rpf/x64/patch/data/lang/korean.rpf
    ShaUnpack("29139afa0b1b5331d43fe2894c97e47629c94ecea9e7cdeac97d12a0f701d60e"), // dlc.rpf/x64/patch/data/lang/mexican.rpf
    ShaUnpack("a1da64d80ff07cbbe4181c23e04c6ce0e8fd3ddd8f0f6f7f91f1f8e6272d12e1"), // dlc.rpf/x64/patch/data/lang/polish.rpf
    ShaUnpack("b6c4579011d0557250225f5d65e1308a0360faa1e22db0afd7cbee1e2a30fc58"), // dlc.rpf/x64/patch/data/lang/portuguese.rpf
    ShaUnpack("31700754cba52bad68f73240e613869758bad15e1a1946a5c3e50c2985127fe2"), // dlc.rpf/x64/patch/data/lang/russian.rpf
    ShaUnpack("ff19713f9726a1cff26869733fc1b3b9da40961dec8987613afe920facfa5286"), // dlc.rpf/x64/patch/data/lang/spanish.rpf
    ShaUnpack("16655a8feb6353e38edcd805c7299cb8506ec497b70bdf9fb545aa983c813090"), // dlc.rpf/x64/textures/script_txds.rpf
    // update/x64/dlcpacks/patchday20ng/dlc.rpf
    ShaUnpack("b6e49836facfcb02da19856ec3ffeb7ffb44ab8d4680f67da890f1851988290d"), // dlc.rpf
    ShaUnpack("6892ea46b2d425b5296079ea923103d80fa802f0ef3aa1ecf1e35af1ee145d4c"), // dlc.rpf/x64/anim/ingame/clip_anim@.rpf
    ShaUnpack("0c00969eb64813c0b7367348b40759b503fb27e806603f932e3e702ab03cc659"), // dlc.rpf/x64/anim/ingame/clip_veh@.rpf
    ShaUnpack("c283df5e128701fc11a1360281124b6be02265adcf3a3571bb3b5b061cecf6ee"), // dlc.rpf/x64/levels/gta5/vehicles.rpf
    ShaUnpack("702beff3945a0f2208196de75369e4cb6e6b37e7a1131403b3c14ef31ea5647f"), // dlc.rpf/x64/levels/gta5/props/icons15.rpf
    ShaUnpack("2afc7579506e87c2498e6c3bfcbcf5b86f2d8b88b37e073d7298c928b1e88cee"), // dlc.rpf/x64/levels/patchday20ng/vehiclemods/jester3_mods.rpf
    ShaUnpack("70dcd2a64e965386375baf19310e0193618a6a6944985273e2226178643306e7"), // dlc.rpf/x64/levels/patchday20ng/vehiclemods/pounder2_mods.rpf
    ShaUnpack("b6c8dd95b6937fc33175dc76e1bf4b311f18b62d5b8ab73b6125c98116b03a57"), // dlc.rpf/x64/models/cdimages/patchday20ng_female.rpf
    ShaUnpack("c0cfc318195b470bab5d00a1e87516a7f9d9d854e9f75dc8db11b5d33d40d858"), // dlc.rpf/x64/models/cdimages/patchday20ng_male.rpf
    ShaUnpack("e4f0fd2fdeaa0d485555b7ff2f3de39a6f0171134a6f64392523dc1989bea2f1"), // dlc.rpf/x64/models/cdimages/peds/patchday20ng.rpf
    ShaUnpack("39dbca7f9342c9c2648b24b34f7a8f29ac5e86fa4b4ac265108f833757cb8d71"), // dlc.rpf/x64/models/cdimages/peds/patchday20ng_p.rpf
    // update/x64/dlcpacks/patchday21ng/dlc.rpf
    ShaUnpack("4c41deb029af1859df1b069433d000620b2535cc46a1efb7d36f6cb2f35a53b6"), // dlc.rpf
    ShaUnpack("27ba3e819deac9831ba824afca21202a0eb399c48dc31ec0c88d792cefa2556a"), // dlc.rpf/x64/anim/ingame/clip_anim@.rpf
    ShaUnpack("0420a84345bb290e1e7f7b6367c742876cb50312738553644a76330c57cded13"), // dlc.rpf/x64/anim/ingame/clip_facials@.rpf
    ShaUnpack("bc7fdcf478919c752cd17f42d3db7950273ee410b27ea47cabbf4c4ac112e0fe"), // dlc.rpf/x64/anim/ingame/clip_veh@.rpf
    ShaUnpack("66e628f6671344019e0751849a0cdf7999de0c77aab821339b831ac65be69cde"), // dlc.rpf/x64/levels/gta5/vehicles.rpf
    ShaUnpack("2ecdfd7b67e9c82ba0851cd3e41cf6265b2488eb21e6a0b0074abb8812a3abb5"), // dlc.rpf/x64/levels/patchday21ng/vehiclemods/bruiser3_mods.rpf
    ShaUnpack("69620a20ba01301a0deb483e6ec9ff71d7d4a0511bf2c47a54501a84d24df647"), // dlc.rpf/x64/levels/patchday21ng/vehiclemods/brutus2_mods.rpf
    ShaUnpack("052f452db77ef85d12e270638212d9eb8157bf49a450e6ce568274dacc4de8de"), // dlc.rpf/x64/levels/patchday21ng/vehiclemods/brutus3_mods.rpf
    ShaUnpack("cdc78810f6a073d352fe32454830e8394a590004a1fbd906008395b631ffebee"), // dlc.rpf/x64/levels/patchday21ng/vehiclemods/deathbike3_mods.rpf
    ShaUnpack("c96566eb8f5cc85f6411243f33bd2c73a6bd7b550ba96027fa126a267441a31c"), // dlc.rpf/x64/levels/patchday21ng/vehiclemods/deathbike_mods.rpf
    ShaUnpack("db3974c39f065d99e552b26175f1da7a78a0e6088c36d6268c37e878ee8aaf95"), // dlc.rpf/x64/levels/patchday21ng/vehiclemods/dominator4_mods.rpf
    ShaUnpack("0c8b95ea63deaba64f93fb10b8bf8b9c695b825e77249aca78d7b64000c51e54"), // dlc.rpf/x64/levels/patchday21ng/vehiclemods/dominator5_mods.rpf
    ShaUnpack("698d4bd1c1e9205da3b0350956f435a689ae675d08996a86589cb9dbe0afdbb8"), // dlc.rpf/x64/levels/patchday21ng/vehiclemods/dominator6_mods.rpf
    ShaUnpack("7386658ee80edebfeedf02dd952ee9611d5e519dac54cf81fe1df0a454e26715"), // dlc.rpf/x64/levels/patchday21ng/vehiclemods/impaler2_mods.rpf
    ShaUnpack("1efd8d4834e68c9874341c9c76d8ed2e44f00b20e9204d8e76e489c282abb1d1"), // dlc.rpf/x64/levels/patchday21ng/vehiclemods/issi4_mods.rpf
    ShaUnpack("62cdf45d47489e7dc5e50249a2eafd6fdf6a60a1e08ef1d2d05afc122a57b6e8"), // dlc.rpf/x64/levels/patchday21ng/vehiclemods/issi6_mods.rpf
    ShaUnpack("07ced41c7c00fde59147f6eb0f75908199c51008830d9785dec00df7286ee730"), // dlc.rpf/x64/levels/patchday21ng/vehiclemods/monster3_mods.rpf
    ShaUnpack("651baf1d4de320aebba909e333f767ab08558c252ae6cdcace18f9bd97498f2e"), // dlc.rpf/x64/levels/patchday21ng/vehiclemods/monster4_mods.rpf
    ShaUnpack("3302017ca0edcea071ebf2e008cc8d50b8f49b29696cb6a3e56e97f0d747cf5b"), // dlc.rpf/x64/levels/patchday21ng/vehiclemods/monster5_mods.rpf
    ShaUnpack("b982d726544b855ef0ee13b20ffc73ec6db39f51fa0e48c8bda38dd3038003b2"), // dlc.rpf/x64/levels/patchday21ng/vehiclemods/scarab2_mods.rpf
    ShaUnpack("fefa66a998dbfbef47c07a4f5442b2ce497120fd0f9070a0532c1ded28cfc451"), // dlc.rpf/x64/levels/patchday21ng/vehiclemods/scarab3_mods.rpf
    ShaUnpack("d82089acac6f642c024dc1ea0f61aedef5e150b855c59c829ef3b8adedc77a66"), // dlc.rpf/x64/levels/patchday21ng/vehiclemods/scarab_mods.rpf
    ShaUnpack("015c656b0058573ec5c763a38adcfc025ac9f546b2dd9fae9acacc8f61c18b4e"), // dlc.rpf/x64/levels/patchday21ng/vehiclemods/slamvan6_mods.rpf
    ShaUnpack("889abc43a1dc0f696903ed7b5c6b0397718a319ebdf0383a03112dfd2c659b19"), // dlc.rpf/x64/levels/patchday21ng/vehiclemods/zr3802_mods.rpf
    ShaUnpack("1ef0d7a7d767a70886c43031fc5dc2d4e8d467fdf6c4f26cc864eed6cf4712bb"), // dlc.rpf/x64/levels/patchday21ng/vehiclemods/zr3803_mods.rpf
    ShaUnpack("50f084335a511b741fffd8a6b117c4fe3b6d2094cb7417a5cd0d80984590116c"), // dlc.rpf/x64/levels/patchday21ng/vehiclemods/zr380_mods.rpf
    ShaUnpack("9a392b129c8fceaa2e88305b21784469e96b4038f134372c0d2a01023fcb3eeb"), // dlc.rpf/x64/models/cdimages/patchday21ng_female.rpf
    ShaUnpack("1653cf676afbcbc96353bf4190ff67cd6ba2adced811905f06b189bf869e9897"), // dlc.rpf/x64/models/cdimages/patchday21ng_male.rpf
    ShaUnpack("40dc004d244febe6e10153d81498997b80ebc7a90d2fbad5032ca1a3a236405d"), // dlc.rpf/x64/models/cdimages/peds/patchday21ng.rpf
    // update/x64/dlcpacks/patchday22ng/dlc.rpf
    ShaUnpack("3c78a6c938b1d13af526c7d2ffa69ee1a3f6b6f2bebfb39016b8e2d09c1cb8e4"), // dlc.rpf
    ShaUnpack("c6cf007072addea6e2babd4697fd66b91fb263abb1bc5ad7eed85903679019cb"), // dlc.rpf/x64/anim/ingame/clip_anim@.rpf
    ShaUnpack("abf2f611586220a3fd334d9724e79dc51bf5a43272718e2bb8130bc9ae8292d6"), // dlc.rpf/x64/anim/ingame/clip_anim_casino_a@.rpf
    ShaUnpack("42a8d16ba04d3d2ee6474eb626f154cb62794dd439dad5a0a4a0b85b2938d8e7"), // dlc.rpf/x64/anim/ingame/clip_anim_casino_b@.rpf
    ShaUnpack("67b7ed9c5fddaeb74a748c716fe15335e5b89fada7ad091856143c8d5f5d411f"), // dlc.rpf/x64/anim/ingame/clip_veh@.rpf
    ShaUnpack("af83b52688871890ecc0b3ec8d505b3d4dda90250aeafdbc9a3c65e61bee23f5"), // dlc.rpf/x64/levels/gta5/vehicles.rpf
    ShaUnpack("1d2ab1ba0438e215762e92ec0be354b98348b29efe4b1050967a38aa6a438d70"), // dlc.rpf/x64/levels/patchday22ng/vehiclemods/imperator2_mods.rpf
    ShaUnpack("c02fb02e1a18b526c8700f2b622c002816e688102559b4db762ac4b50d26715e"), // dlc.rpf/x64/levels/patchday22ng/vehiclemods/imperator3_mods.rpf
    ShaUnpack("2d6c9fcb2c60b66a18da92486e8ab9defb44dc16cf85628c01223d4d8e232f36"), // dlc.rpf/x64/levels/patchday22ng/vehiclemods/nebula_mods.rpf
    ShaUnpack("ba11c0d29f323ae7ee9fb82e51a5a13f46e9fa393520554d8b80060ded2eb95e"), // dlc.rpf/x64/levels/patchday22ng/vehiclemods/paragon_mods.rpf
    ShaUnpack("7a5219b5c60b3009a6fdea4a0e75a0fcef3411c7c05f652134305014c8afca56"), // dlc.rpf/x64/levels/patchday22ng/vehiclemods/s80_mods.rpf
    ShaUnpack("b9c96925184418eeedb7e0e87043466f82ffd0684494d2fa84e6097a653e55ae"), // dlc.rpf/x64/levels/patchday22ng/vehiclemods/thrax_mods.rpf
    ShaUnpack("05f71477c0515aa434a2a082edbfff878b4ca88aea46ecafc1d2a0159a1c3284"), // dlc.rpf/x64/levels/patchday22ng/vehiclemods/wheels2_mods.rpf
    ShaUnpack("aae05c510f50766e2ba5c8a378577b05bf1c99abf0a2a9475455e5f85a68f5c9"), // dlc.rpf/x64/levels/patchday22ng/vehiclemods/wheels_mods.rpf
    ShaUnpack("0525f2ed20bcdeb45a99ece89aca082944839c1a535fa71869feb2ef0fb5710b"), // dlc.rpf/x64/levels/patchday22ng/vehiclemods/zion3_mods.rpf
    ShaUnpack("e7bf80d6200c4fe8563670e6463a75c3c3fe42eb58d2996abe7402b23fe16402"), // dlc.rpf/x64/models/cdimages/patchday22ng_female.rpf
    ShaUnpack("fbd435ac1779515d9c9c9e47254a4d7df2e26377de6d2f57c3ef2e10dcb3be72"), // dlc.rpf/x64/models/cdimages/patchday22ng_female_p.rpf
    ShaUnpack("67bc5d85574ffc64b4a0f2b6d2b462cd31ad9fe9551be2c336f4cd2f91dee0b7"), // dlc.rpf/x64/models/cdimages/patchday22ng_male.rpf
    ShaUnpack("9e328c67ec0f8a6d10736b018b66e069e267e34af250bf07e28f035dd8b3ccc3"), // dlc.rpf/x64/models/cdimages/patchday22ng_male_p.rpf
    ShaUnpack("e3c8d26797f8ad2549f2fea453dec5310f7df6672764b33b42d4bbbf2c1b805d"), // dlc.rpf/x64/models/cdimages/peds/patchday22ng.rpf
    ShaUnpack("770149c4860295c52326aadbb928b393de683e3deb256934071bba0e9b1a5323"), // dlc.rpf/x64/models/cdimages/peds/patchday22ng_p.rpf
    // update/x64/dlcpacks/patchday23ng/dlc.rpf
    ShaUnpack("315211c8cf18c747bc3d349f6eaa29cdf0ae502f8a0e563c901e78dc35dbc45b"), // dlc.rpf
    ShaUnpack("fe78ab791b427a219524507f71ef56dc292210a1834f518e65d8b09bf25bb757"), // dlc.rpf/x64/levels/gta5/vehicles.rpf
    ShaUnpack("776322235eacd13d4161193147968df84e3033d789f58b413b4536cad00d1c9a"), // dlc.rpf/x64/levels/gta5/props/icons16.rpf
    ShaUnpack("1e217de2d2bf559bae7f9ba3a09dfff0112869e1a87cf789d74f912205321307"), // dlc.rpf/x64/levels/patchday23ng/vehiclemods/gauntlet4_mods.rpf
    ShaUnpack("395ea0573b20aff964b32f14c7e60530249aafebe328df306a4ebb49778584a2"), // dlc.rpf/x64/levels/patchday23ng/vehiclemods/gb200_mods.rpf
    ShaUnpack("c6265a5bbd2f53f9ff93c1d8ef73c0ce482b24fe6301bd8bdc6e1da24965c03d"), // dlc.rpf/x64/levels/patchday23ng/vehiclemods/nebula_mods.rpf
    ShaUnpack("22dcb327b3683729c393b616846a76117733b061508332862700a2c614f00fe1"), // dlc.rpf/x64/levels/patchday23ng/vehiclemods/sultanrs_mods.rpf
    ShaUnpack("b3801deeb1e6a8aa3cd4e5ced58c8989dc4aa9c8bae96545fcd861ba560e3072"), // dlc.rpf/x64/models/cdimages/patchday23ng_female.rpf
    ShaUnpack("a6d5b782b3123542dbe5404a67862b8ef87ef2bcd7ec4d92bc1c0dfc4f0f870b"), // dlc.rpf/x64/models/cdimages/patchday23ng_male.rpf
    ShaUnpack("196f1fce5bf4e7330639b86296eb697d0d6c7fffa99955bb9aaf4ee98de7f58c"), // dlc.rpf/x64/models/cdimages/peds/patchday23ng.rpf
    ShaUnpack("e13200ab6dfee5d889398120d98108773d33ba4fa0bd6aa218195aeeca82d5c1"), // dlc.rpf/x64/models/cdimages/peds/patchday23ng_p.rpf
    // update/x64/dlcpacks/patchday24ng/dlc.rpf
    ShaUnpack("27aaeb5df2c374fd420306c66a3221e59922a58e906aeedbb04e990fc2d46060"), // dlc.rpf
    ShaUnpack("fcc653a0590ae298f548ebf96221acca96052f3de5d52e7ceda9927da94fdbb2"), // dlc.rpf/x64/anim/ingame/clip_anim@.rpf
    ShaUnpack("347b626b75f31abf737449863d74de4f0f1ed27fd8052b9ddb8fe9dc674f613f"), // dlc.rpf/x64/anim/ingame/clip_facials@.rpf
    ShaUnpack("eef2504e15693854bc7d6670cbf9d2fbcb49f19b4aae9871a1d40a3bb39d73fc"), // dlc.rpf/x64/levels/gta5/vehicles.rpf
    ShaUnpack("c5bf01c8d79c9362bbce9aa5bbabb0db4353ffb445fb8df8ec68eb8d82aee511"), // dlc.rpf/x64/levels/patchday24ng/vehiclemods/coquette4_mods.rpf
    ShaUnpack("589d16884b8a1fb01062e73758fff0cfbad6f4f5dd46a229850b23f40989258a"), // dlc.rpf/x64/levels/patchday24ng/vehiclemods/gauntlet5_mods.rpf
    ShaUnpack("7241ec7765a28b33a93d3592b7aa3db5da00ae6edd1e875b942580c1d922de34"), // dlc.rpf/x64/levels/patchday24ng/vehiclemods/manana2_mods.rpf
    ShaUnpack("fe229cd2c92a6e4250659fefc1c91b35cdde301b00a4c7f88bdf0082a5a4da30"), // dlc.rpf/x64/levels/patchday24ng/vehiclemods/youga3_mods.rpf
    ShaUnpack("303fadb4a2a331e5f6202a393419ea6d75d3dd738fcd895b3e81abc8920b2613"), // dlc.rpf/x64/models/cdimages/patchday24ng_female.rpf
    ShaUnpack("77566fba5897393f1a27bd72e7605377437d2f0885aff82d52ae95016ba9689a"), // dlc.rpf/x64/models/cdimages/patchday24ng_female_p.rpf
    ShaUnpack("04780223ffc19677609e24da2d6af49a425d421dd413683d4749410c13dd8d17"), // dlc.rpf/x64/models/cdimages/patchday24ng_male.rpf
    ShaUnpack("738f1d2e87f7b5fe31f11ae079a47f49a76f2775d79a0f9eab7c15fb934f672d"), // dlc.rpf/x64/models/cdimages/patchday24ng_male_p.rpf
    ShaUnpack("b427debf79c8e67e00d601638d7113e40af8b9af41dc99c1b706bc10404e6b28"), // dlc.rpf/x64/models/cdimages/peds/patchday24ng_p.rpf
    // update/x64/dlcpacks/patchday25ng/dlc.rpf
    ShaUnpack("ee617d5e24d81ea148fc0a0822625b1403554c8172239197d2736bd2fd3bffbd"), // dlc.rpf
    ShaUnpack("687972a544477f3cd48b61df0b1eee1746a61ba85484f394c83bd4dfcebf6426"), // dlc.rpf/x64/anim/ingame/clip_anim@.rpf
    ShaUnpack("5931a1838afd9a59d93facd2b053b6210b0acd48ef8c2c2ae8c485447ac60dfc"), // dlc.rpf/x64/levels/gta5/vehicles.rpf
    ShaUnpack("15eed367acc9dc8ebb160b522582333805772d01d3d0775b3ddbfedf3eaab06f"), // dlc.rpf/x64/levels/patchday25ng/vehiclemods/banshee_mods.rpf
    ShaUnpack("4448d07b24a31e1c1b8b4d7c1a8a6f80d5fcd8a1157e06a8d215457f80d224e3"), // dlc.rpf/x64/levels/patchday25ng/vehiclemods/kuruma_mods.rpf
    ShaUnpack("1489a0ee071dd12578e3c5197cdb09ea6d41c858fc3c57932b441169557d5c5c"), // dlc.rpf/x64/levels/patchday25ng/vehiclemods/weevil_mods.rpf
    ShaUnpack("104d20ad2d6181873f21c83ff6862c644f4239375a1484a73561209e0e9300b7"), // dlc.rpf/x64/models/cdimages/patchday25ng.rpf
    ShaUnpack("1cd757c80f6bbe00bd2414737b782ecbcd2356f1579eaa27c2f4fd7c6a955318"), // dlc.rpf/x64/models/cdimages/patchday25ng_female.rpf
    ShaUnpack("edc25e5900c823e0b55148aa289f4da28e2ccc6064e074e2b9ecf503dbdc754d"), // dlc.rpf/x64/models/cdimages/patchday25ng_male.rpf
    // update/x64/dlcpacks/patchday26ng/dlc.rpf
    ShaUnpack("21865e7e88413296caecca9a0eb0b9b58f28f70269bc8c3c8ae809ebaf7850b6"), // dlc.rpf
    ShaUnpack("a71554b3bdbf7be95dd3bd7d9425b45979a1638b89d2a97e41aae8bb5d048c0d"), // dlc.rpf/x64/anim/ingame/clip_veh@.rpf
    ShaUnpack("de08057b2d73ad19e6f61cbdb9fe444f8f6b3737907f6d8ce9ca9a1622b4da8c"), // dlc.rpf/x64/levels/gta5/vb_additions.rpf
    ShaUnpack("3a02e02da5acb4d3a8fb547133dd4b39c57432f334739e4281835436cfd481bc"), // dlc.rpf/x64/levels/gta5/vehicles.rpf
    ShaUnpack("4a33a61f1908e5a0512fec702126f16ec229acda2b93aae5c934c198d734c9b4"), // dlc.rpf/x64/levels/patchday26ng/vehiclemods/brioso2_mods.rpf
    ShaUnpack("c70cd2bdc2838a0daedf709744cb596cfa826f8aabfd706f3e72001599602289"), // dlc.rpf/x64/levels/patchday26ng/vehiclemods/calico_mods.rpf
    ShaUnpack("6c6fd1a0cf725b2b307483705a2f3446b833c58417b6d09940a77badc96b40ae"), // dlc.rpf/x64/levels/patchday26ng/vehiclemods/comet6_mods.rpf
    ShaUnpack("cc6c563e71109483280c754df5ff3cf244a9fe12f1f024d9bf029d9a6d05f8b7"), // dlc.rpf/x64/levels/patchday26ng/vehiclemods/dominator7_mods.rpf
    ShaUnpack("598fe0164718116303628fc4835abb0fc585f6e2fb14a52ef182ef7cba18c7ab"), // dlc.rpf/x64/levels/patchday26ng/vehiclemods/hakuchou2_mods.rpf
    ShaUnpack("54ceceba80ed60cb9902bae85035f01fabd95f8ee984104880f511cc39d0e71d"), // dlc.rpf/x64/levels/patchday26ng/vehiclemods/jester4_mods.rpf
    ShaUnpack("52f30b077ccc95303c63f1244548ab0f8269419734dde7d3b1edaaa0fc568fb5"), // dlc.rpf/x64/levels/patchday26ng/vehiclemods/remus_mods.rpf
    ShaUnpack("b3adad43c36a73471efe38654aa3969b37026f6b1b35b4f8127c4380390e681e"), // dlc.rpf/x64/levels/patchday26ng/vehiclemods/rt3000_mods.rpf
    ShaUnpack("193640bc744c99bfde9ce2e24cd21a8e1c0cee0d49807e5cc9e30eba21e5a9ca"), // dlc.rpf/x64/levels/patchday26ng/vehiclemods/sentinel_mods.rpf
    ShaUnpack("d7981e65804001b9ecc65592757c03043c3ede60b89fbba7e9645b3b7b1da32b"), // dlc.rpf/x64/levels/patchday26ng/vehiclemods/tailgater2_mods.rpf
    ShaUnpack("6f91fa7a0686b3b969ce32bca1653f89774c08d3f5bc800c2aeb129caf8b74ba"), // dlc.rpf/x64/levels/patchday26ng/vehiclemods/turismo2_mods.rpf
    ShaUnpack("e48b2770220496a7ac4831129c23e985771d371cc0d912d34aed8ecce37e075c"), // dlc.rpf/x64/levels/patchday26ng/vehiclemods/warrener2_mods.rpf
    ShaUnpack("db20baac407d34686261a9fb2e127c1d859ba006c2d4885950963af2b476fba2"), // dlc.rpf/x64/levels/patchday26ng/vehiclemods/zr350_mods.rpf
    ShaUnpack("f5c41679061fb4b768e361884f832d78a7c0b064d797e421b77106325620aa3b"), // dlc.rpf/x64/models/cdimages/patchday26ng_female.rpf
    ShaUnpack("372bdac87a5d1a0b73c38b6ec4a63ca1344e7cdcbfea7fce2d8e6c819bf9b307"), // dlc.rpf/x64/models/cdimages/patchday26ng_male.rpf
    ShaUnpack("e19acd82bacbf4974f5271770bec9dd5fef4e2341f8f1b30cca4849c3d90a4a6"), // dlc.rpf/x64/models/cdimages/peds/patchday26ng.rpf
    // update/x64/dlcpacks/patchday27g9ecng/dlc.rpf
    ShaUnpack("2017a685ba2200eb28e90304f79d68e222e7d4892c4267c905f41957175c6311"), // dlc.rpf
    ShaUnpack("45d8a472e78fb9db7ebf49a37c8bd05eccf7a08892973e056462a4f68ee049b8"), // dlc.rpf/x64/levels/gta5/vehicles.rpf
    // update/x64/dlcpacks/patchday27ng/dlc.rpf
    ShaUnpack("0060bf520034b888f9dc46491b7a6ac1ac2c9cd7990deda561b6bf81b2f15382"), // dlc.rpf
    ShaUnpack("536149012c0ffc18eee321a5c45c0e02109d1056f810d1271663f06d8b396227"), // dlc.rpf/x64/anim/ingame/clip_veh@.rpf
    ShaUnpack("8d2387f3d4b81f1e72f423cb89dc16f9ae7d92196896f0ef99361d13eb8e4228"), // dlc.rpf/x64/anim/ingame/clip_weapons@.rpf
    ShaUnpack("8ece98ba2eb6ac9521ba8d1022ba863b2782292f0ea5dc83efdab6c570db5af7"), // dlc.rpf/x64/levels/gta5/dt_additions.rpf
    ShaUnpack("d6470c89cd45f8052a996585ccb2e696fdba2d2e3aa6fdebbda32e596d3bff6b"), // dlc.rpf/x64/levels/gta5/vehicles.rpf
    ShaUnpack("cc0ce8ee4b021883542798e7c1e4f57912e894e1f2c021a07da2c394cbb33122"), // dlc.rpf/x64/levels/gta5/props/prop_tr_accs_02.rpf
    ShaUnpack("783c78060d72d794ba4a456eec0de600d8e88833af15d2fe41070a7e9ab78ec2"), // dlc.rpf/x64/levels/gta5/_citye/downtown_01/downtown.rpf
    ShaUnpack("1c31cb77f491d4773fd19c50f715804b9b1d943c08441af385ae2ae4bb76fe88"), // dlc.rpf/x64/levels/gta5/_citye/downtown_01/downtown_01_metadata.rpf
    ShaUnpack("8ef3ab8ac9503404a655b31426a195041c5cfe49581ddd24311c9b9c64fca272"), // dlc.rpf/x64/levels/gta5/_citye/downtown_01/dt1_03.rpf
    ShaUnpack("e377973111fbd216ed3c03ec3503387b93b84bd9d5e7427cdf592d8627243eca"), // dlc.rpf/x64/levels/gta5/_citye/downtown_01/dt1_15.rpf
    ShaUnpack("9a03c217793f9ca9567e5fe1118f24261953ac7a4f7b5af1190e2a4067f00634"), // dlc.rpf/x64/levels/gta5/_citye/downtown_01/dt1_19.rpf
    ShaUnpack("ce05b768786d446b5f947c4fc8cc60bc792a8099570fa243c7e331a8f02f4704"), // dlc.rpf/x64/levels/gta5/_citye/downtown_01/dt1_lod.rpf
    ShaUnpack("d76e56748020c2be5983f7eca138b5772f302e2e3ce8daba311110bbdf69ec58"), // dlc.rpf/x64/levels/gta5/_citye/downtown_01/dt1_occl.rpf
    ShaUnpack("dfea8ebb996dae50d0f8c7a0ba0272b34f1bbe817dd4cc4c388766aaa0927ace"), // dlc.rpf/x64/levels/gta5/_citye/downtown_01/fwy_01.rpf
    ShaUnpack("6c27a9168bdabe6c04fb3a34e2a7cb56a85997838e88739fb1c3e5e34b14f792"), // dlc.rpf/x64/levels/gta5/_citye/downtown_01/fwy_04.rpf
    ShaUnpack("471fdd894e6fbeece3870a549866b3e297fe759afd753eb5cd133b82097f92cb"), // dlc.rpf/x64/levels/gta5/_citye/hollywood_01/hollywood_metadata.rpf
    ShaUnpack("964ed9e4dc4013e3116ba6e2dc0fbb7cf7394c43ba9a7510dc743f1675a37010"), // dlc.rpf/x64/levels/gta5/_citye/hollywood_01/hw1_01.rpf
    ShaUnpack("61ae01a196ddfb06c30305659bdecf3a37e9519f1f5e6d6fa8a94893e29db7ab"), // dlc.rpf/x64/levels/gta5/_citye/hollywood_01/hw1_28.rpf
    ShaUnpack("8bed00a7f127f54a6a0cfd4021234b7492b9568802826eddb72f6a5d677b32d7"), // dlc.rpf/x64/levels/gta5/_citye/hollywood_01/hw1_blimp.rpf
    ShaUnpack("a6996231d8d02171215ef6337c7082a3f5d0ef56587d70d80cef08743dc1b80d"), // dlc.rpf/x64/levels/gta5/_citye/sunset/bt1_04.rpf
    ShaUnpack("468af9a35b967f14a3f8c2261da80fe6a556f7bcf4fec9626f0da1cf586eda76"), // dlc.rpf/x64/levels/gta5/_citye/sunset/ss1_occl.rpf
    ShaUnpack("0ff75781c403a8b4a22dfb85e0b208893cb627829300cc1667d6b917e61ea21d"), // dlc.rpf/x64/levels/gta5/_citye/sunset/sunset.rpf
    ShaUnpack("192059aac5001d4b9f7cb0bc745a33bb129e76d3056be5bdfe4c1c28c310f120"), // dlc.rpf/x64/levels/gta5/_citye/sunset/sunset_metadata.rpf
    ShaUnpack("74a8a105bd8c0b43c18c047baec84541179283aef8ee09f87689c5aa85f402cf"), // dlc.rpf/x64/levels/gta5/_cityw/beverly_01/beverly.rpf
    ShaUnpack("610c7437c4f207970a935832f18cd1fc1b7f9b1188138a741228762d5e877f6f"), // dlc.rpf/x64/levels/gta5/_cityw/beverly_01/beverly_metadata.rpf
    ShaUnpack("ef6d31988761eab34fbdb3b9bb3ce12d5941897cd3a93dd372f1f16e035dee39"), // dlc.rpf/x64/levels/gta5/_cityw/beverly_01/bh1_38.rpf
    ShaUnpack("f351cbfd1f6194bc73edcb3a355d419c41d3f8bf13afc36617cf4a5fa9f0bb60"), // dlc.rpf/x64/levels/gta5/_cityw/beverly_01/bh1_40.rpf
    ShaUnpack("f59ecdd18e922864f95544b146a180cdd964268f78c7d17e74948e68426a8a39"), // dlc.rpf/x64/levels/gta5/_cityw/beverly_01/bh1_43.rpf
    ShaUnpack("f8d2f7658ef89107e88f71a135ad16ef0a95c249231d8da319f6b243c0132222"), // dlc.rpf/x64/levels/gta5/_cityw/beverly_01/bh1_occl.rpf
    ShaUnpack("5700ec5ff82ebda7f8ade08ab199a7c70e028745f01fdf8591bd7d959c10d97a"), // dlc.rpf/x64/levels/gta5/_cityw/santamon_01/santamon.rpf
    ShaUnpack("426356a45a26f8754b1fbc391ec746ecc4ed50f15c9d0c7e7988762b557901b4"), // dlc.rpf/x64/levels/gta5/_cityw/santamon_01/santamon_metadata.rpf
    ShaUnpack("0ec50a97faefa035675754efa31b719280f92587e69a3a333397e76149deba7d"), // dlc.rpf/x64/levels/gta5/_cityw/santamon_01/sm_20.rpf
    ShaUnpack("4ddb69726144a01e031155debef24f9ad4c60fc23fd36838a09b5fe5727e8228"), // dlc.rpf/x64/levels/gta5/_cityw/santamon_01/sm_lod.rpf
    ShaUnpack("51676623270f408e8732922c4cd09484c842d3fbba1e59d5f13989fc37541250"), // dlc.rpf/x64/levels/gta5/_cityw/santamon_01/sm_occl.rpf
    ShaUnpack("4288bd26ae718376e075551a1228958b1b1bf8272cd55da97ed9fa27a55eb5b1"), // dlc.rpf/x64/levels/gta5/_cityw/santamon_01/sm_rd.rpf
    ShaUnpack("db8aa7b0f5a675f45018f288c99b76a14fa08ed2a5d4846d97b5084b1617b5e1"), // dlc.rpf/x64/levels/gta5/_cityw/venice_01/vb_10.rpf
    ShaUnpack("9d4b383f97a3d79e1d180d046dcd9f1c3e056fe2e337f49dc5d658c693f8e466"), // dlc.rpf/x64/levels/gta5/_cityw/venice_01/vb_19.rpf
    ShaUnpack("9a1516e47439730a8eef6d357df864c6798cac3ca53b9e4589fc6aee6c67560f"), // dlc.rpf/x64/levels/gta5/_cityw/venice_01/vb_34.rpf
    ShaUnpack("a9c94b8b4ae3f630b7194899df41ff4e700696700a034fab16d1696360c14523"), // dlc.rpf/x64/levels/gta5/_cityw/venice_01/venice.rpf
    ShaUnpack("6573e29973ad3011125326f2365791ea3b9e4554610a60ad7eb2f38f4c010598"), // dlc.rpf/x64/levels/gta5/_cityw/venice_01/venice_metadata.rpf
    ShaUnpack("4445132d1d856f8625ecc7cc272c64d27487e38c2ceeb73d66127afd8c3ea0cc"), // dlc.rpf/x64/levels/patchday27ng/vehiclemods/buffalo4_mods.rpf
    ShaUnpack("91fa23b5d2dd0100351eea358d664d035a8dcbc8697ca9beb1f1af374ce9ba15"), // dlc.rpf/x64/levels/patchday27ng/vehiclemods/champion_mods.rpf
    ShaUnpack("521fcafc11a9fef3e51bd2424b220f075ee38ffb6e89de7183410f511e61da15"), // dlc.rpf/x64/levels/patchday27ng/vehiclemods/feltzer3_mods.rpf
    ShaUnpack("199692652bb6896625422842b59c080d5508f285fb1771227ae93c9f83b8c13f"), // dlc.rpf/x64/levels/patchday27ng/vehiclemods/granger2_mods.rpf
    ShaUnpack("eed45d4d478c4db983ec8bb74dca8b42b899b80e72140c62ea2c922bcff2b0a1"), // dlc.rpf/x64/levels/patchday27ng/vehiclemods/picador_mods.rpf
    ShaUnpack("2c51cbef6ab892ac297a648bd3572dc07c6353702109c4a9e3ddeb9766b07c58"), // dlc.rpf/x64/levels/patchday27ng/vehiclemods/schwarzer_mods.rpf
    ShaUnpack("0480902b6ed7eced5d0658d7d904f9ffe7e5bc59b63fc1a4cd9a59cebde10ebe"), // dlc.rpf/x64/levels/patchday27ng/vehiclemods/seasparrow2_mods.rpf
    ShaUnpack("a1ca41685cec1aa6dff6c4cd67c1e508f619b79a7c9cc6ae03719d0d1a8b8d42"), // dlc.rpf/x64/levels/patchday27ng/vehiclemods/seasparrow3_mods.rpf
    ShaUnpack("b3495ff41ca82fa14620410e573ce18bc01a4091757f384126ef8e7b7879f002"), // dlc.rpf/x64/levels/patchday27ng/vehiclemods/sentinel_mods.rpf
    ShaUnpack("41a487e8fb303a59c4c5b6beb570a422e2c7bee42ba66c86c46e9032d39eb71b"), // dlc.rpf/x64/levels/patchday27ng/vehiclemods/shinobi_mods.rpf
    ShaUnpack("4ea09989e31477b19ff981b50217694800d851a4b06784fc848c97010041854e"), // dlc.rpf/x64/levels/patchday27ng/vehiclemods/tailgater_mods.rpf
    ShaUnpack("a0e1444b9aa1e19a3c0bf0c9fb089454148cea24ecef090fa81925b9fe35a3c8"), // dlc.rpf/x64/levels/patchday27ng/vehiclemods/turismor_mods.rpf
    ShaUnpack("4d26496e2c7027c61781bba056893ec1248594d94f75792bde7408ff72cd07c7"), // dlc.rpf/x64/levels/patchday27ng/vehiclemods/zeno_mods.rpf
    ShaUnpack("422251c31f4052a2efa52f6a7402199e525e4038d447775c0471093165b05a7c"), // dlc.rpf/x64/levels/patchday27ng/vehiclemods/zentorno_mods.rpf
    ShaUnpack("c642442a522414b3cc2814ca407323c16c600f0fd3b356abc24bc2ca415c852c"), // dlc.rpf/x64/models/cdimages/patchday27ng_female.rpf
    ShaUnpack("6483d63a0a05f9bdb027fae031e902a45776941c18b0890bbf74b916746fbaf7"), // dlc.rpf/x64/models/cdimages/patchday27ng_male.rpf
    ShaUnpack("aec50d0b634110946197bd2b1d75979d498d105dc66982db6334e665b365cb5e"), // dlc.rpf/x64/models/cdimages/weapons.rpf
    ShaUnpack("6b11ac9fb4d41a23314f31e4b37e9c190564f71b0666d9dc5f1a231c0c9fd838"), // dlc.rpf/x64/models/cdimages/peds/patchday27ng.rpf
    ShaUnpack("9bc58a3559931e5bf9bd5ff67005c7da746443a775eaed111c176559ddd14e0c"), // dlc.rpf/x64/models/cdimages/peds/patchday27ng_cutspeds.rpf
    // update/x64/dlcpacks/patchday28g9ecng/dlc.rpf
    ShaUnpack("f115c03b53ea373b8cc4f60ff466d2fa0de7bec8345028238af89a6440f38b87"), // dlc.rpf
    ShaUnpack("4b5758d47474c8904f9da2857a11308d03f1999ae753aae15e6d8d04d4fb0a0e"), // dlc.rpf/x64/levels/gta5/vehicles.rpf
    // update/x64/dlcpacks/patchday28ng/dlc.rpf
    ShaUnpack("52f3158a026bf0bca872acd5b57447d3854cf4e82d5ab68834ca6fd263015a84"), // dlc.rpf
    ShaUnpack("465920b52878c21d148c31fccdd42dd22691fc0788f4af8a810a5d7d6df222b9"), // dlc.rpf/x64/levels/gta5/vehicles.rpf
    ShaUnpack("a4df40f227892a8692ef55f07d2a1815c094547a705af00f9440033d443c5ea7"), // dlc.rpf/x64/levels/gta5/props/roadside/v_construction.rpf
    ShaUnpack("54903a824ed29fb7836eb00357d5ca6226a6110e35f4f5942d0655de4e49ed0c"), // dlc.rpf/x64/levels/patchday28ng/vehiclemods/brioso3_mods.rpf
    ShaUnpack("ee25958105acae4403de7814e0711d5981c60229f739f84234149418f7527f5d"), // dlc.rpf/x64/levels/patchday28ng/vehiclemods/buffalo4_mods.rpf
    ShaUnpack("9c90621fba3b3d2ebd4cc9dfff00cd29309a655953c92f01e93c5c44d0cf782e"), // dlc.rpf/x64/levels/patchday28ng/vehiclemods/greenwood_mods.rpf
    ShaUnpack("71be6f14d5d86169ece2bf1bf80f5105107c7e4396551741cc58f5c5a0e515d3"), // dlc.rpf/x64/levels/patchday28ng/vehiclemods/nebula_mods.rpf
    ShaUnpack("369d5ca163c546040bdd56eb606f0898ae66e0a0a608ad8bd04c6065b34b03dc"), // dlc.rpf/x64/levels/patchday28ng/vehiclemods/ruiner4_mods.rpf
    ShaUnpack("a48cca9859494abb2b69e248b347933f3252175d849ee9b7c41eada3dfdb7b88"), // dlc.rpf/x64/levels/patchday28ng/vehiclemods/zentorno_mods.rpf
    ShaUnpack("cdfb3249815c75d7b62d1cecd008a5be1abd9f08e818e59b101f6141317ab161"), // dlc.rpf/x64/models/cdimages/patchday28ng_female.rpf
    ShaUnpack("097d9dbaa4aece714c38d7769fb45bf63b21bb682e52575c5a0d591f97bf028a"), // dlc.rpf/x64/models/cdimages/patchday28ng_male.rpf
    ShaUnpack("aeac57941634e0af8d41479d3bf750b0a4022a82d0162e2328e8ecfd845e6070"), // dlc.rpf/x64/models/cdimages/peds/patchday28ng_p.rpf
    // update/x64/dlcpacks/patchday2bng/dlc.rpf
    ShaUnpack("6ba0e28b749c6aaf390b47c4db9fcc9f3c76f063784e40c3183340a9ebb0eb89"), // dlc.rpf
    ShaUnpack("81d672d9208e3147701e2f987fb823b073546d3915ce82d550291695082ea966"), // dlc.rpf/x64/anim/ingame/clip_melee@.rpf
    ShaUnpack("0bc7f09b821b554d2b55f660482a380c8eee18bb8eb05e0b5bb9af154e567690"), // dlc.rpf/x64/anim/ingame/clip_mp_.rpf
    ShaUnpack("fed608bd44d6cde910268d2f3333371f785050ba698a378445e0c625da6c23ed"), // dlc.rpf/x64/anim/ingame/clip_veh@.rpf
    ShaUnpack("f8c8e8e323082c6b872d04641a87003f03c8acaa69b47fb87cf928bf71bc6fa0"), // dlc.rpf/x64/levels/gta5/props/mplts_props.rpf
    ShaUnpack("0683d7bee7bdff895fdfb93e3226d0a0670339872cb410ba0861db1166c58d3e"), // dlc.rpf/x64/levels/gta5/props/v_christmas_tree.rpf
    ShaUnpack("64ee97f69141c46e3934e6e3ff25ce7ce87e9e2ff9903027ded0cc3d4d2a7d49"), // dlc.rpf/x64/levels/gta5/props/lev_des/v_lev_doors.rpf
    ShaUnpack("0efc71d2ab996ae20494d722cefcadaaa87e63d0b1b94206b52935106c0511f3"), // dlc.rpf/x64/levels/gta5/props/roadside/v_bins.rpf
    // update/x64/dlcpacks/patchday2ng/dlc.rpf
    ShaUnpack("3bb6ac5f1c99798fd8ea1da28c27e1a52f4c83f3def9489901969f328a358564"), // dlc.rpf
    ShaUnpack("09ef4eff7c9a0fe7b82bc28f3a3b55b36c92cd5a55c7ea7701279633b7fbbbb5"), // dlc.rpf/x64/anim/filters.rpf
    ShaUnpack("7159c16baba9cf8a54dce94fbb24d81cc98c2bed053b00f078e8776284e101cd"), // dlc.rpf/x64/anim/networkdefs.rpf
    ShaUnpack("cf69015b56d7532f4a361e56451d44a38d04b90dfdfbc75bec57b0ab2bfb29ff"), // dlc.rpf/x64/anim/posematchers.rpf
    ShaUnpack("b49d6c154b5d0c0281bb53aa55bc5370209744cff9ac64f963f122f80eadb763"), // dlc.rpf/x64/anim/cutscene/cuts_ah_1.rpf
    ShaUnpack("f33ee3cec550032e0e0f899e5366549a8c075dedb73af072f29db442a28f1e10"), // dlc.rpf/x64/anim/cutscene/cuts_family_3.rpf
    ShaUnpack("2f2950f48f763ad07876773d085fd7fbbdb38c489d46c0223089752cbe7d801c"), // dlc.rpf/x64/anim/cutscene/cuts_josh_1.rpf
    ShaUnpack("13087de3e3d10708329d611fea3b76f15330633313f9c845533d175a2bd95863"), // dlc.rpf/x64/anim/cutscene/cuts_josh_2.rpf
    ShaUnpack("d34f54d132338ebd3c697721d0fb12fcb132c71253089871b5b44e5f2a66f55d"), // dlc.rpf/x64/anim/cutscene/cuts_josh_3.rpf
    ShaUnpack("2cb56e3fbb125c80a710398f56f6416768ef9a32ea0128049aba77c2f668feab"), // dlc.rpf/x64/anim/cutscene/cuts_lamar_1.rpf
    ShaUnpack("59350d749b78fbf90b8b117f49df039823e3a09e91fc736fc024d6a9fc956381"), // dlc.rpf/x64/anim/cutscene/cuts_martin_1.rpf
    ShaUnpack("ecb8edbd84824d2508f6e411a0af8d561e91fe2aff1f3362b746591cdd54cb3e"), // dlc.rpf/x64/anim/cutscene/cuts_michael_3.rpf
    ShaUnpack("a164856fbf1bfe3154416ce1adb47dfdf8bcbc71950cea0e0c1e1b4b05d7b5c7"), // dlc.rpf/x64/anim/cutscene/cuts_mp.rpf
    ShaUnpack("ccfb7ad9b1a2132340ff18d1604b98e03376be0a628b6d9a4f93feaed10d01ed"), // dlc.rpf/x64/anim/cutscene/cuts_oddjobs.rpf
    ShaUnpack("40d9729efd4d4153240149388a6ee5d343f3a1c2d38b7cdb3a63acaebbfa9047"), // dlc.rpf/x64/anim/cutscene/cuts_random_peds.rpf
    ShaUnpack("f0e83f303772189eae154bb51d4bc7f40f7a1d5ede18fad27a1eae7e4bc638cf"), // dlc.rpf/x64/anim/ingame/clip_amb@.rpf
    ShaUnpack("2bfd3d6062dc77672e8bd6e771ce915d3c785ea1379aa354c0b2b0df38154ed3"), // dlc.rpf/x64/anim/ingame/clip_cellphone@.rpf
    ShaUnpack("0814dd8ac65ef0cc68f2a3dd4d6345fa2a2198b37d0a4862425270d1b6acb2dd"), // dlc.rpf/x64/anim/ingame/clip_combat@.rpf
    ShaUnpack("266be95f4b85520c02b9ed7cc423c3e62c2f7fca010b98b017f2ce1292ba32bb"), // dlc.rpf/x64/anim/ingame/clip_cover@.rpf
    ShaUnpack("835989720260bfb4272a30216631113fac72d0e2c6425134738421ea94b0f8bc"), // dlc.rpf/x64/anim/ingame/clip_creatures@.rpf
    ShaUnpack("1fdc4fa4188076bbd285e04937d6e7d0c8c73f0dc5284f08cb579fa00eeec284"), // dlc.rpf/x64/anim/ingame/clip_dam_.rpf
    ShaUnpack("3a0734b53a26613e9191e033d818be1a2514fb9b98aabe1d98a769793aeb4db7"), // dlc.rpf/x64/anim/ingame/clip_director@.rpf
    ShaUnpack("67d4d4cdcfa49170856634bce39c457ca9ba50a39971b64125f1c2aac0f58790"), // dlc.rpf/x64/anim/ingame/clip_facials@.rpf
    ShaUnpack("3a3f21d61eda4f4ae3b7dad56f5da0f6b376bbb95c8829e5494df49918d403a7"), // dlc.rpf/x64/anim/ingame/clip_get_up@.rpf
    ShaUnpack("62237a1d88f3c870930a415a4103a2377cd4a043f5243ef97fe6097681e34ae7"), // dlc.rpf/x64/anim/ingame/clip_ladders.rpf
    ShaUnpack("47842ff7639ebde1ca67209f0baa4d003271614241bb4798d138328f044c6dac"), // dlc.rpf/x64/anim/ingame/clip_melee@.rpf
    ShaUnpack("e1a540b580551ef2393126ecab846f7ffca7b79f75b5df75e3900f74d849bb2a"), // dlc.rpf/x64/anim/ingame/clip_mini@.rpf
    ShaUnpack("a030e46249f4d90cd66f414e44f253c3ef4b3de87d74cd27a421af1b229ce1eb"), // dlc.rpf/x64/anim/ingame/clip_miss.rpf
    ShaUnpack("7237f36e683fdea5325742d5dc810295df0b0dbf3126dfe30f5797b4e1e9136f"), // dlc.rpf/x64/anim/ingame/clip_move_.rpf
    ShaUnpack("f4f87415add8b5cf23bace327dfba897ad014c07437a42d0a9f2c98f20d697ba"), // dlc.rpf/x64/anim/ingame/clip_mp_.rpf
    ShaUnpack("420638e166ba8b2aec5b30856ebc9e78968f5e5d7df40344b5508b029e634440"), // dlc.rpf/x64/anim/ingame/clip_ped.rpf
    ShaUnpack("08f5c1aff527993ea9ff3139bb0f6dd1ad1d6d1f345b442517b43c8bad6e7ce8"), // dlc.rpf/x64/anim/ingame/clip_rcm.rpf
    ShaUnpack("9e76adcf6fee444038c57ff0c42e4b963269b66780f0cbc238f5dd81d82d59c2"), // dlc.rpf/x64/anim/ingame/clip_save.rpf
    ShaUnpack("3f6b755f7c3e27f67cfb98ea6a6ae2e7039e1d85c3d6976d30920ec984591a28"), // dlc.rpf/x64/anim/ingame/clip_skydive@.rpf
    ShaUnpack("9bea5b84c06efdaa1abe11900c1fe0fe62a5f4a49b182c767900488199aa026c"), // dlc.rpf/x64/anim/ingame/clip_veh@.rpf
    ShaUnpack("b61eb0fc69d5dd4b5e5070fcda1301f3b02ac90deccfd2f11dfbe1397e621eab"), // dlc.rpf/x64/anim/ingame/clip_weapons@.rpf
    ShaUnpack("56443ac0c71b9e3fb311a601d40826b995cdaa9141d28a3c580272c41d83c44b"), // dlc.rpf/x64/levels/gta5/vehicles.rpf
    ShaUnpack("8b76fcb460d17b8086ca5360dd4e87b1d9d1c76fc33570062e29dc1d7b153915"), // dlc.rpf/x64/levels/gta5/_citye/downtown_01/downtown_01_metadata.rpf
    ShaUnpack("5ca515d7d119574c84b8532b09ab40c815990008d7e92a88c25c760e994c2762"), // dlc.rpf/x64/levels/gta5/_citye/downtown_01/dt1_03.rpf
    ShaUnpack("dd1f6a16cf1bf9a785871162c73721e551f2dbf7eb27bf142c94038793b024c5"), // dlc.rpf/x64/levels/gta5/_citye/downtown_01/dt1_15.rpf
    ShaUnpack("0fd83092187d1dd465d404d9db53e103e901c82e5927474996911293ae48885e"), // dlc.rpf/x64/levels/gta5/_citye/downtown_01/dt1_19.rpf
    ShaUnpack("76e9bb968f5222907820b3bdf1b708090357bcecc8d884d066ffa8c262b54bb3"), // dlc.rpf/x64/levels/gta5/_citye/downtown_01/dt1_lod.rpf
    ShaUnpack("716280e0897e43d97969bd2152fa9ffbf318ec2cb1b4a70e0a5e91e744efe541"), // dlc.rpf/x64/levels/gta5/_citye/downtown_01/dt1_occl.rpf
    ShaUnpack("ef2ae9f94573fa61de477d7e451cce874aec1a53d8078cbffd3310a6409f07bf"), // dlc.rpf/x64/levels/gta5/_citye/downtown_01/fwy_04.rpf
    ShaUnpack("645bc2068e9c552786dcd65d6bd287966699a8942d7fc153454fed8ed07c9a47"), // dlc.rpf/x64/levels/gta5/_citye/downtown_01/replay_placement.rpf
    ShaUnpack("5b3048e01d270757bd2aae58e9fe4f01acad1456be94dd37bb6219303d0dbf7c"), // dlc.rpf/x64/levels/gta5/_citye/hollywood_01/hollywood_metadata.rpf
    ShaUnpack("06268164f89bb7d2852daad1a925476d13680fc700a6aa37d476591dc78ac00a"), // dlc.rpf/x64/levels/gta5/_citye/hollywood_01/hw1_01.rpf
    ShaUnpack("5702c82138449ba3a9ce264f702b3e89351d4e160e8535d5640ce023187b70d3"), // dlc.rpf/x64/levels/gta5/_citye/hollywood_01/hw1_28.rpf
    ShaUnpack("30b886a3656513f27895126356938d8e91a9bc17a527491bc716c816367436b5"), // dlc.rpf/x64/levels/gta5/_citye/indust_01/id1_06.rpf
    ShaUnpack("cceea0332414a75b91cd6413f61633d363a905cd33fce2cc13051cb0ecab7e52"), // dlc.rpf/x64/levels/gta5/_citye/indust_01/indust_01_metadata.rpf
    ShaUnpack("baa67ef835f6bcbd4ca35a7c52d6dec6a14e4019036ea18de7b8b641b370bb10"), // dlc.rpf/x64/levels/gta5/_citye/indust_02/id2_13.rpf
    ShaUnpack("f60e58629929a45c0939e33053e545cdd7be56f1b03a4a9014143984521837e2"), // dlc.rpf/x64/levels/gta5/_citye/indust_02/id2_21_c.rpf
    ShaUnpack("150ad214a3626d8c548716118949dd0a47f637cc3ba6581c9dd09bd9a2ba9f72"), // dlc.rpf/x64/levels/gta5/_citye/indust_02/id2_21_d.rpf
    ShaUnpack("9988d2320ee336bb47b895cc9611a45645229bfd8ef5a8e0c6f09011ead0d875"), // dlc.rpf/x64/levels/gta5/_citye/indust_02/id2_rd.rpf
    ShaUnpack("fe18c727df652a9371354ed529a831212e1c5a98cd94564f01cd1d69eae1399f"), // dlc.rpf/x64/levels/gta5/_citye/indust_02/id2_rdb.rpf
    ShaUnpack("47bae1cada3727754b9ec5d7e1160af72e0f220617619b415a804dbad9853316"), // dlc.rpf/x64/levels/gta5/_citye/indust_02/indust_02_metadata.rpf
    ShaUnpack("b0d4aac3c2a137f32084a1d3060f90ee787770f18132f36a5fe6e133864f2d1d"), // dlc.rpf/x64/levels/gta5/_citye/scentral_01/sc1_13.rpf
    ShaUnpack("b7fec18c9d80afe127f126ba838e676cf10b36874d9248d198eb4feeabd9f8dc"), // dlc.rpf/x64/levels/gta5/_citye/scentral_01/sc1_20.rpf
    ShaUnpack("c788905afb3c8ca0c2b6f5537f75562c12488cb698d3fadb2173dd71357ea260"), // dlc.rpf/x64/levels/gta5/_citye/scentral_01/sc1_occl.rpf
    ShaUnpack("cf52936c4e0a9714a43d92817c6cba77dd0286e50129d99e92313e75c7a8095a"), // dlc.rpf/x64/levels/gta5/_citye/scentral_01/scentral_metadata.rpf
    ShaUnpack("1cbdee91cf4da0255fdf67f71e703e515806dfba7c8474c2ef0e26af856ae21c"), // dlc.rpf/x64/levels/gta5/_citye/sunset/sunset_metadata.rpf
    ShaUnpack("46a9d401daaaf2925ebcc9fe3dd8129210271f471de065e3a3fd5ee4ecb4eee1"), // dlc.rpf/x64/levels/gta5/_cityw/beverly_01/beverly_metadata.rpf
    ShaUnpack("c514b4285f9a8441a740372e57b5ad52828bf42162d36fe7879a309b712b2e98"), // dlc.rpf/x64/levels/gta5/_cityw/beverly_01/bh1_38.rpf
    ShaUnpack("5e2b15de80784efd377982afe58a24960aecfbaa6458781309ff964096a3a689"), // dlc.rpf/x64/levels/gta5/_cityw/beverly_01/bh1_40.rpf
    ShaUnpack("00f3c5b9d1c9d53108bb60e2ada55c32f6655f6a57a647eb0baef4984a1cca5e"), // dlc.rpf/x64/levels/gta5/_cityw/beverly_01/bh1_occl.rpf
    ShaUnpack("2fd4c2fbfe66db05612311bfaae3453390cc6424d35181f52253f884d83663a2"), // dlc.rpf/x64/levels/gta5/_cityw/sanpedro_01/sp1_occl.rpf
    ShaUnpack("5a98b2c876e1344c49a86586cafce56ed56c5d2b1262c03f26e8f99d12471c7d"), // dlc.rpf/x64/levels/gta5/_cityw/sanpedro_01/sp1_rd.rpf
    ShaUnpack("75a419155c04639d43585398473a0afe7901356a80bde38350bf44e539e28cf4"), // dlc.rpf/x64/levels/gta5/_cityw/santamon_01/santamon.rpf
    ShaUnpack("1f87fd6393c329a30835d521b8b27c720b1a5dd154949d005ea3f79a6fd64524"), // dlc.rpf/x64/levels/gta5/_cityw/santamon_01/santamon_metadata.rpf
    ShaUnpack("a202a885354bb67e2a4ff200f2b992c29bb0fa52d31a7797907aa6f3569cc1e5"), // dlc.rpf/x64/levels/gta5/_cityw/santamon_01/sm_20.rpf
    ShaUnpack("c46346a6d655e246dba4cacb2e892b4d580891cfa43a364cb9805976538615d0"), // dlc.rpf/x64/levels/gta5/_cityw/santamon_01/sm_occl.rpf
    ShaUnpack("3c43e85030550479075c644eef457c75e9752b58eebaa54c310eba03d24cd6e7"), // dlc.rpf/x64/levels/gta5/_cityw/venice_01/vb_10.rpf
    ShaUnpack("7b7d5d48220052a9c85aa306a8569ae49b9f0a0b0e5b9cf7c3a345d3459df1a1"), // dlc.rpf/x64/levels/gta5/_cityw/venice_01/vb_19.rpf
    ShaUnpack("de7921622ad0206bd413d46adad314088c69a19b3e8784c6035838ac6851f689"), // dlc.rpf/x64/levels/gta5/_cityw/venice_01/venice.rpf
    ShaUnpack("eb3c9cccb10d51aa6a420fbdae5ddc4e9afe834e198d28f5e79c7adea5bf8ead"), // dlc.rpf/x64/levels/gta5/_cityw/venice_01/venice_metadata.rpf
    ShaUnpack("1a52725b0e7e7c7be7f7c64411528558c823e16d84c6a4b3816bd65799155158"), // dlc.rpf/x64/levels/gta5/_hills/cityhills_01/ch1_occl.rpf
    ShaUnpack("3a88d049fee22437022415d8fab6251646e14cef2610c1056ad4f18dad39c06d"), // dlc.rpf/x64/levels/gta5/_hills/cityhills_03/ch3_12.rpf
    ShaUnpack("6772fc7ee432a564c85332313588a93296475fd23920436039f38f3e1be0fadb"), // dlc.rpf/x64/levels/gta5/_hills/cityhills_03/cityhills_03_metadata.rpf
    ShaUnpack("03f1936c5132f91dd1004770e1282cc2185e570746a6a89fbf944808aa3dadfe"), // dlc.rpf/x64/levels/gta5/_hills/country_01/country_01_metadata.rpf
    ShaUnpack("c23c71241468811f15d3eb288a5b1a4ffde4e2349122a01ebed0373b82a9bb88"), // dlc.rpf/x64/levels/gta5/_hills/country_01/cs1_02.rpf
    ShaUnpack("c11a70c8769c74c0d8adfc4d371a0b400033946de22a53c27f80f4d45b9039ac"), // dlc.rpf/x64/levels/gta5/_hills/country_01/cs1_03.rpf
    ShaUnpack("72edad34e39e5af3775a57854cc4925a57893e40cc5a2dadb34a3d48488037be"), // dlc.rpf/x64/levels/gta5/_hills/country_02/country_02_metadata.rpf
    ShaUnpack("b94fd6689f4fbbe64740bea5ac98d36475a8a558f25ba0976c44e99e9d103141"), // dlc.rpf/x64/levels/gta5/_hills/country_02/cs2_09.rpf
    ShaUnpack("f4d81fd0e62147849a4d43fefed5787dd0338c40ae9ebc54747a2f48e6497713"), // dlc.rpf/x64/levels/gta5/_hills/country_03/country_03.rpf
    ShaUnpack("156de890dbe36f14c3a6f1f316a8065383c4c8ed2e201b42fb2a82c788ec690b"), // dlc.rpf/x64/levels/gta5/_hills/country_03/country_03_metadata.rpf
    ShaUnpack("9bd96ade4dd6102e4f2adb4b5be11ec841dd2510bbf0e84c7929ad5b0bdebdc5"), // dlc.rpf/x64/levels/gta5/_hills/country_03/cs3_04.rpf
    ShaUnpack("afe5bfd9cd1af498edfc2521ca92f496b493a2e4f738ec998d54c49b72bc7d51"), // dlc.rpf/x64/levels/gta5/_hills/country_03/cs3_occl.rpf
    ShaUnpack("ee120b6f7b3db214e14e10ec3d4a5fad670e89b424911405cf914031dd74d0e7"), // dlc.rpf/x64/levels/gta5/_hills/country_06/country_06.rpf
    ShaUnpack("846969d808a8e6d5e3ffbfe131b8f6ae561a07645ffaea81c25aedc109618147"), // dlc.rpf/x64/levels/gta5/_hills/country_06/country_06_metadata.rpf
    ShaUnpack("a4158e685f5f54603224bbca4877f1b52de989ae649c67a230f204906733d1fe"), // dlc.rpf/x64/levels/gta5/_hills/country_06/cs6_03.rpf
    ShaUnpack("ecfdddf6da740d16a40ab5783f3a36df429dee0d5f4c53443a5219ba047cdcff"), // dlc.rpf/x64/levels/gta5/generic/gtxd.rpf
    ShaUnpack("3b8b3724155c053ad253e7c197a0c82beb8afb081e4b0be6e2e81b03d13be930"), // dlc.rpf/x64/levels/gta5/interiors/v_int_31a.rpf
    ShaUnpack("f1bbca14a2ab8b15a41ce464f4736088632c6ef37897639d917f865581ddf404"), // dlc.rpf/x64/levels/gta5/interiors/v_int_44.rpf
    ShaUnpack("faaaa1376bea87b74f01878cf06fba5b0f9439c49f05d3fbc1ea5ab99cef71f6"), // dlc.rpf/x64/levels/gta5/interiors/v_int_55.rpf
    ShaUnpack("a14e4c8c16e3da99456e8e28072eef823414a9d014d0da752eb3f3ae53887e26"), // dlc.rpf/x64/levels/gta5/interiors/v_int_57.rpf
    ShaUnpack("e990076fe3273730cfede4b1626dfad1c07b202f5f0fa511a1396aa08cac1b12"), // dlc.rpf/x64/levels/gta5/interiors/v_int_58.rpf
    ShaUnpack("60265d53e9de6864f9837301abc33e42c26813447e1333a0ffbac8a0bee5a348"), // dlc.rpf/x64/levels/gta5/interiors/v_int_60.rpf
    ShaUnpack("3d21d2e2fc52c3043d6f3452a65902034c8f77961b99fec441ef057a419ade62"), // dlc.rpf/x64/levels/gta5/interiors/v_int_73.rpf
    ShaUnpack("e4c9c2c285be42b54227389629ec2489ba0c2c2da3320d4462f0b15bdd8ea616"), // dlc.rpf/x64/levels/gta5/interiors/v_int_74.rpf
    ShaUnpack("5bec5d2940976729ec4a6e20d10d53920e7f4341ccf15572eb33b21dba90668f"), // dlc.rpf/x64/levels/gta5/interiors/int_props/int_lev_des.rpf
    ShaUnpack("ad905b689e2a83f165a8a2914643a7ed588cf774933f4fc2524be6c75e599c95"), // dlc.rpf/x64/levels/gta5/interiors/int_props/int_medical.rpf
    ShaUnpack("179163f17b425107dd27adca53aa3b461f7e4bccc09131e4e18c19e25b35dd11"), // dlc.rpf/x64/levels/gta5/props/mplts_props.rpf
    ShaUnpack("d3b867778a386f18da7a9f7a377702f5ec4fcd36758975cbd450eb609efd6fa7"), // dlc.rpf/x64/levels/gta5/props/v_christmas_tree.rpf
    ShaUnpack("8a342bb06b1c31be8ef2afd11a5bc0d01e8e4d5c6609fdc8909bd34f8b9e69c1"), // dlc.rpf/x64/levels/gta5/props/v_ng_proppatch_01.rpf
    ShaUnpack("b029ca2553d0055a6ef67b86a529a6d796f53112a2be62710cf4b60e6477bf87"), // dlc.rpf/x64/levels/gta5/props/building/v_rooftop.rpf
    ShaUnpack("0342609f7800c49b268712f8f3162f6c063c1fa1114e87d376d6b982a4988246"), // dlc.rpf/x64/levels/gta5/props/commercial/v_office.rpf
    ShaUnpack("9cfe410d008b14fd9e0b9ad987043830697b1ec2655f932168a20c97dca08851"), // dlc.rpf/x64/levels/gta5/props/industrial/v_airport.rpf
    ShaUnpack("1d953da4ce68fdf311a9c240e1673af54e9c5f94dcf81c3b082712c81778c65b"), // dlc.rpf/x64/levels/gta5/props/industrial/v_industrial_2.rpf
    ShaUnpack("1bc02899657af3d6421947f302aed3c6e7c165d48c845c79a889da3fcea33e0a"), // dlc.rpf/x64/levels/gta5/props/lev_des/lev_des.rpf
    ShaUnpack("445061cdbdc7013dee476e685592184257103e5957c96a9e20650eafec69b2b9"), // dlc.rpf/x64/levels/gta5/props/lev_des/v_minigame.rpf
    ShaUnpack("6031953daa2de6b1883a37151ff0c89ad856365cbb9483b3b2effe2781150ae9"), // dlc.rpf/x64/levels/gta5/props/lev_des/v_set_pieces.rpf
    ShaUnpack("84359d2ab4c003de1630c563bb988f2d2efcc0f6893e65ef5321547c1eff0ab1"), // dlc.rpf/x64/levels/gta5/props/recreational/v_coin_op.rpf
    ShaUnpack("846da79eeb4fff0cd2a4bf585faf83f04c0767470f36f1e7bb4f150f7b8dd117"), // dlc.rpf/x64/levels/gta5/props/recreational/v_sports.rpf
    ShaUnpack("0d9cd1e5813481d9cb80c60b6d767cb537057f09e9afbd34b08ebefdf1bda4f1"), // dlc.rpf/x64/levels/gta5/props/residential/v_garden.rpf
    ShaUnpack("82cda215efab96eed134b74becaf9fc54d95b4db1ec201e0b5a8313b1d2f541f"), // dlc.rpf/x64/levels/gta5/props/roadside/v_bins.rpf
    ShaUnpack("44e2d9a12cd90bf9e7c8ffb3da80086e767dbb47121f27b290440b901d441737"), // dlc.rpf/x64/levels/gta5/props/roadside/v_construction.rpf
    ShaUnpack("54c0c8bfea52a6df2c75159d77cd783c3e9b2fc6c293dd87ffbe8a8f4ff842c7"), // dlc.rpf/x64/levels/gta5/props/roadside/v_fences.rpf
    ShaUnpack("7257b3ea6c3088b5f2d6d016d6c6f7d9088cbfefd5c805db4a5ba4591bde64ba"), // dlc.rpf/x64/levels/gta5/props/roadside/v_rubbish.rpf
    ShaUnpack("06dd4fc5d3cb37294973ce2099142aa05f52b4612f35a0254bb3a80312d5902c"), // dlc.rpf/x64/levels/gta5/props/roadside/v_storage.rpf
    ShaUnpack("70a5a7bb71984e83772d0b6db7f7fbdb022cafaa98bee296bf6fdbddb31c40cf"), // dlc.rpf/x64/levels/gta5/props/roadside/v_traffic_lights.rpf
    ShaUnpack("944dd68133bd54e8300fa7dc2f03b3e7741bdb82b4103a58d4c533de5fe03c89"), // dlc.rpf/x64/levels/gta5/props/roadside/v_utility.rpf
    ShaUnpack("c393737e00113b791ce794bc209dd39336fd298fa35cebc42c11d62fa43b1f5b"), // dlc.rpf/x64/levels/gta5/props/vegetation/v_bush.rpf
    ShaUnpack("353e6ccbea6b75f9aa6f1194b95a4f3b7186bc8dd3192e5473f26520d4af4278"), // dlc.rpf/x64/levels/gta5/props/vegetation/v_cacti.rpf
    ShaUnpack("0267599cb1f674c97b68957908261142a50a5f757557cd59b3c32e853c74165e"), // dlc.rpf/x64/levels/gta5/props/vegetation/v_ext_veg.rpf
    ShaUnpack("4ed81b11acc09cb38ac41d019dc7ae8c659e9d97707aedbb1093fdb679e343fa"), // dlc.rpf/x64/levels/gta5/props/vegetation/v_rocks.rpf
    ShaUnpack("143de179c85c2d815db4ab63268059b7ea11c7ea481a9ff48ceaa20db4dd9f08"), // dlc.rpf/x64/levels/gta5/props/vegetation/v_trees.rpf
    ShaUnpack("3669b41e19b2905b031a0e236554a809743fbd378ef0faa2fb5b83b10b551a72"), // dlc.rpf/x64/levels/patchday2ng/vehiclemods/pcj_mods.rpf
    ShaUnpack("877049660d627a4bc3c2fbe8fe4d95aca74d7b20e65ecbd55d0ce8fb681fa36e"), // dlc.rpf/x64/models/cdimages/streamedpeds_strm.rpf
    ShaUnpack("19c630ee9ab18026417f64b1f5aa4b98fdce1555bf49f3f550cf2eab3531c420"), // dlc.rpf/x64/models/cdimages/weapons.rpf
    // update/x64/dlcpacks/patchday3ng/dlc.rpf
    ShaUnpack("b90936edf1960e319ff5fdd582e381b752d49586b0ab404a064c96a7a189a2af"), // dlc.rpf
    ShaUnpack("be96e6e93387d1920b3df6ab880f684ab3292e4602313dd47a67b973089fff52"), // dlc.rpf/x64/anim/expressions.rpf
    ShaUnpack("dc23d10a745a85640a56a7a040d43c448560e13d41d3819890763f86fef4f791"), // dlc.rpf/x64/anim/cutscene/cuts_bs_2b.rpf
    ShaUnpack("80759b2665900cf60630cc6d37d7154f8350771601a33601b10b6e7132c44d31"), // dlc.rpf/x64/anim/cutscene/cuts_car_steal_2.rpf
    ShaUnpack("bd2c5f3268b68cf99fbcb82a1c9f55d3735d04cca43889efee5e4404824536cd"), // dlc.rpf/x64/anim/cutscene/cuts_family_3.rpf
    ShaUnpack("9be36054c8db708d5d3845f97ee560354dfefa0dd156cdfe6e5287380ee274e2"), // dlc.rpf/x64/anim/cutscene/cuts_family_5.rpf
    ShaUnpack("f455c45bdfbe1e106f5b2ddf94194fbd87c989020c30b964bc0bc18f938aeb31"), // dlc.rpf/x64/anim/cutscene/cuts_josh_1.rpf
    ShaUnpack("6e8180ea73a8cb85c3d3cf5bbe27a518676472b6c18c0580accf3b1cfe450f48"), // dlc.rpf/x64/anim/cutscene/cuts_oddjobs.rpf
    ShaUnpack("f89442c8b50e6ade6cc20c4db6ab494f70c9f3bba05a9b2c7e1d2a61c21d93eb"), // dlc.rpf/x64/anim/cutscene/cuts_prologue.rpf
    ShaUnpack("24c2a54e18d036a02bd46ba22bc972795fc6a6ca9fd62eb2c2e3b8a2d93dc81a"), // dlc.rpf/x64/anim/cutscene/cuts_random_peds.rpf
    ShaUnpack("501fbbd2b44a72a52d0c8adac8bec0b534d21bbf029d4f677d94909b4e0d49f9"), // dlc.rpf/x64/anim/cutscene/cuts_solomon_2.rpf
    ShaUnpack("f7f3927fc2dac44f9577e742f1a96649ff577fb29dd0fe87b9308b5a252fc8c7"), // dlc.rpf/x64/anim/cutscene/cuts_trevor_drive.rpf
    ShaUnpack("53e091d8d2e5d467dccbd27fb229ca79cfe972a2d778ceb9c2de0a31d0345222"), // dlc.rpf/x64/anim/ingame/clip_anim@.rpf
    ShaUnpack("03bb4f107d6018df1030068fd573292deb9790dc78dabffef852d34aaca427f3"), // dlc.rpf/x64/anim/ingame/clip_cellphone@.rpf
    ShaUnpack("20621a424d953fe0db4163ab8f0a08f9df93a3b73c03dd28770b548e3e58288a"), // dlc.rpf/x64/anim/ingame/clip_director@.rpf
    ShaUnpack("09902a39920d7c92d9c8037328c76fe84bf06d444be1e4cad20e67f825718f7d"), // dlc.rpf/x64/anim/ingame/clip_melee@.rpf
    ShaUnpack("b80cd25b5de71f4bbe8af54e8f850cddd67674deb9fd22253d4094cfcbc73830"), // dlc.rpf/x64/anim/ingame/clip_mini@.rpf
    ShaUnpack("18dc6e579e2078cf8aaf8fcc573d25b468b97f47201f6dd8dec2dca6b83e5f69"), // dlc.rpf/x64/anim/ingame/clip_miss.rpf
    ShaUnpack("6c4cedca022a60116ca31cfe53e0de990946761fb3e679020442ef5b735f3f38"), // dlc.rpf/x64/anim/ingame/clip_move_.rpf
    ShaUnpack("6f7a41e4a985730385b0bd97e1a4e87529d1cba2a7eef1b854fab3578a46ba99"), // dlc.rpf/x64/anim/ingame/clip_mp_.rpf
    ShaUnpack("c255c899da1a711a6ceb14b9c94e146e2d43e82b6032141e53d2f2dc837147ad"), // dlc.rpf/x64/anim/ingame/clip_rcm.rpf
    ShaUnpack("d9699f7537571163074d079e9c9945fe6f83e0c7f515d11960681e8b09d2ef46"), // dlc.rpf/x64/anim/ingame/clip_veh@.rpf
    ShaUnpack("475ce182058d39f2bf2a533f1175fe20b9d1a56c6d3601f68021c2b4d14a5857"), // dlc.rpf/x64/anim/ingame/clip_weapons@.rpf
    ShaUnpack("441f3a62b520ac9376222aba80901a64a6c3dbd1a1e309111d99134dd55f1390"), // dlc.rpf/x64/levels/gta5/vehicles.rpf
    ShaUnpack("92865b2ce83083c012d8bef20999075da72ae2fe4db3795c0d02ac8e608fe783"), // dlc.rpf/x64/levels/gta5/_citye/downtown_01/replay_placement.rpf
    ShaUnpack("a3a6211d21e898815c3ca1ec0ae73d400cc2d447199b14df1c6b8a00e61caf89"), // dlc.rpf/x64/levels/gta5/_hills/cityhills_01/ch1_05.rpf
    ShaUnpack("123697869a4085cc2a02f89ca42cdc58962f2e65bdc6aeab62f02443fc347300"), // dlc.rpf/x64/levels/gta5/_hills/country_01/country_01.rpf
    ShaUnpack("c82243eb5cb8469f09574283cebe075e824874cdae46d78dfd559ddde95de6bd"), // dlc.rpf/x64/levels/gta5/_hills/country_01/cs1_11.rpf
    ShaUnpack("83691abc05703ad562e773121318c8af7849a97ef13a165c38d8ea987375babf"), // dlc.rpf/x64/levels/gta5/_hills/country_01/cs1_11b.rpf
    ShaUnpack("7815ca24046881716da45b0cbf5db9c244a2aea0ce882e7638d9e24ed3fc9fe1"), // dlc.rpf/x64/levels/gta5/_hills/country_01/cs1_14.rpf
    ShaUnpack("88e2d04f45a6d7848e6c2104b1e4d868e09910d9545ba654a27ee8610f9111d7"), // dlc.rpf/x64/levels/gta5/_hills/country_01/cs1_lod.rpf
    ShaUnpack("8f2272e82103b304283a0ec3851b137607171b7f3b15327e45f9b316b1a3bd0d"), // dlc.rpf/x64/levels/gta5/_hills/country_02/country_02_metadata.rpf
    ShaUnpack("139c9e13818584bb8a4c397e5e97b0b39975987eeafa142f831f962a47cec334"), // dlc.rpf/x64/levels/gta5/_hills/country_02/cs2_03.rpf
    ShaUnpack("2108ee29a7339207e4ceb437ce5d30c20dc90ceaf6bb7de718076359e95eed23"), // dlc.rpf/x64/levels/gta5/generic/cutsobjects.rpf
    ShaUnpack("0090625d6896a2cf865eae46394375c7c34df10f7114e858e615023965bd293e"), // dlc.rpf/x64/levels/gta5/interiors/dlc_replayeditor_casting.rpf
    ShaUnpack("26453d05bba5540bbae78ec992332aa7371a12171f59a47bb888f483e94a3445"), // dlc.rpf/x64/levels/gta5/interiors/v_int_15.rpf
    ShaUnpack("dff97e7ebadfaa40e7856e5528c4f541cb7086b6e616f584a4281b135a126aef"), // dlc.rpf/x64/levels/gta5/interiors/v_int_2.rpf
    ShaUnpack("55761f924567f498fb030ac6c76a8833d8ce60da15a7549122499c6438bb3f32"), // dlc.rpf/x64/levels/gta5/interiors/v_int_44.rpf
    ShaUnpack("c05615a3bd6003af35f2d9521ea4681839ab48c47d528309be6cbf929834ade2"), // dlc.rpf/x64/levels/gta5/props/lev_des/v_lev_doors.rpf
    ShaUnpack("3287a8f03b8b98811782bde0f83e9d0266856bfefc1be433e1a3577753c340ae"), // dlc.rpf/x64/levels/gta5/props/roadside/v_construction.rpf
    ShaUnpack("4b92b774184993ee204aad812179ac2553be79760603b1a82f29b2cf365d029c"), // dlc.rpf/x64/levels/gta5/props/roadside/v_rubbish.rpf
    ShaUnpack("e0d7746bc425140187eb6eb7fcfab62764e840301bd7298164753794e9606701"), // dlc.rpf/x64/levels/patchday3ng/vehiclemods/ratloader_mods.rpf
    ShaUnpack("7f76013f97a36393da8e171e8929a5af10bacb40c5ab1355a477b74463fdcf17"), // dlc.rpf/x64/levels/patchday3ng/vehiclemods/zentorno_mods.rpf
    ShaUnpack("9bb7d857c0dda828cafe14eaed53ab81c1ae47de6cdbec03e263977a8e8166ac"), // dlc.rpf/x64/models/cdimages/patchday3ng.rpf
    ShaUnpack("234aad773b727a9801c6490afb98798b72ebaabbfcebc1e608ff430eaa34536e"), // dlc.rpf/x64/models/cdimages/patchday3ng_cutspeds.rpf
    ShaUnpack("f3f649003759443817dabfbd27318f3a4e1ed97abcc5deb8f0c5d89917e7347f"), // dlc.rpf/x64/models/cdimages/patchday3ng_f_outfits.rpf
    ShaUnpack("38fcb4ea3a25eeaea070200ec6308b04fa17edd721236eecad3b08348ef08e28"), // dlc.rpf/x64/models/cdimages/patchday3ng_f_outfits_p.rpf
    ShaUnpack("d5ac0a00644d245a069b10abe962afa2061f7734a79ddc3785dd501ff0dc122f"), // dlc.rpf/x64/models/cdimages/patchday3ng_m_outfits.rpf
    ShaUnpack("b52223081fa97c80340525e254b2f830ce9931fb9ec4e07edb17af1df067f257"), // dlc.rpf/x64/models/cdimages/patchday3ng_m_outfits_p.rpf
    ShaUnpack("a2477b79ca6244a16e2325900405795f4ca1df0c1aee0f27564d6abd89f9faa3"), // dlc.rpf/x64/models/cdimages/patchday3ng_p.rpf
    ShaUnpack("6e2f2a231346c1190ca15a7a4272074e4663ff878c3a18cef759a1f8b25b1f97"), // dlc.rpf/x64/models/cdimages/weapons.rpf
    // update/x64/dlcpacks/patchday4ng/dlc.rpf
    ShaUnpack("6ba2bcf63b18c2001957e51b260b9c5262e55fcad10aa93d38e79b9544bed84f"), // dlc.rpf
    ShaUnpack("9b1823c5ce5fbd023dff01102fd7f5ec5f4711d3a01b65dd96fa5c1cc2e70b32"), // dlc.rpf/x64/anim/filters.rpf
    ShaUnpack("ca9a686601ab6468b4fdbe3700e6cbede05ff41839abf814b30c465ce4b5f036"), // dlc.rpf/x64/anim/cutscene/cuts_franklin_1.rpf
    ShaUnpack("e6e411494fb214e3dd0c44d3f99792c1292413ccb9b9198ae3485c08622e5520"), // dlc.rpf/x64/anim/ingame/clip_amb@.rpf
    ShaUnpack("3055ff9b8152b4139beaa35cbaf802643b6d8bf20eef817c5feaac5cdb11fd58"), // dlc.rpf/x64/anim/ingame/clip_anim@.rpf
    ShaUnpack("ed68f9c4edcdbee95b7d7072163a1d11df74db3fb7a2e65998bdcb28897fa94e"), // dlc.rpf/x64/anim/ingame/clip_cover@.rpf
    ShaUnpack("17b0d33cf0fe772d948a3388f8c85ed6efe6989a20cd1440a44156d9c5b4b168"), // dlc.rpf/x64/anim/ingame/clip_miss.rpf
    ShaUnpack("1a2b96a5fa2418ea78729532911aa067a44e1f91be1a885dd03ce6fab265a661"), // dlc.rpf/x64/anim/ingame/clip_move_.rpf
    ShaUnpack("d3fb288a1ae096beb28330cc3b7107f197c2bc3af7e84c329ae7f674a9c1af8f"), // dlc.rpf/x64/anim/ingame/clip_veh@.rpf
    ShaUnpack("6c69326e3614ef463b7747e55fd2fccb643988377f756c439a7d42d73c5a6aa2"), // dlc.rpf/x64/anim/ingame/clip_weapons@.rpf
    ShaUnpack("02774e5b6a428f9e196157e226c232a46c408f48dcd864be436f53e85182c312"), // dlc.rpf/x64/levels/gta5/vehicles.rpf
    ShaUnpack("05436341b6e1f3196259b78a886aca867b757eff13b9fd35fe93509dcac970f1"), // dlc.rpf/x64/levels/gta5/_prologue/prologueint.rpf
    ShaUnpack("2fbb610d6cfe11cbba187428284f1e402647b80ef6da376dcfc74c67ea11dd75"), // dlc.rpf/x64/levels/gta5/generic/procobj.rpf
    ShaUnpack("774793ab7384a62bde943f1418a32aa0cb8b508ee1b9c3cb5dabdf107e3dfdcc"), // dlc.rpf/x64/levels/gta5/interiors/v_int_31a.rpf
    ShaUnpack("d4eb7db5dae180a48bfa9f278e075197d6e0ac69bd682b38345df2f566ea4a63"), // dlc.rpf/x64/levels/gta5/interiors/v_int_55.rpf
    ShaUnpack("3ff2b567fee9186b644714ab7d2d8fd7dcc84ab0cb8022a0d6e8fd7773b433cc"), // dlc.rpf/x64/levels/gta5/interiors/v_int_metro_subway1.rpf
    ShaUnpack("3facaaa24ed069fe887f74c01057d0170445645cd43e0519da7937cdfcb53e21"), // dlc.rpf/x64/levels/gta5/interiors/v_int_metro_subway2.rpf
    ShaUnpack("171fa8ab54a290b68680dc330b31bf6fe54db1f288ede133fad2145d754496ed"), // dlc.rpf/x64/levels/gta5/interiors/v_int_metro_subway3.rpf
    ShaUnpack("53441f16d18fc80c22ef8b9a6dae947652598a495f079a9de7e2520a3257564c"), // dlc.rpf/x64/levels/gta5/interiors/v_int_metro_subway4.rpf
    ShaUnpack("f9379258cdb513bfd7cd61874f77f62b706da9efd93663fac4ae95d2cd5258ca"), // dlc.rpf/x64/levels/gta5/interiors/v_int_metro_subway5.rpf
    ShaUnpack("db32f4057040abf96e4fbbf815c93846d9fed91bfcc77591f1506964625d4413"), // dlc.rpf/x64/levels/gta5/interiors/v_int_metro_subway6.rpf
    ShaUnpack("91f2440b5b2be38187ccaef08dcc3d5d73b8c02899c7eca9c4936a8f6db7e269"), // dlc.rpf/x64/levels/gta5/props/vegetation/v_bush.rpf
    ShaUnpack("627ff98d7258e6572615c8f6f2dfa2c1956e26e1c4383e0db1fda026d82a0ad5"), // dlc.rpf/x64/models/ped_mp_overlay_txds.rpf
    ShaUnpack("a90c7dea27e55f16cf873578ef226f7b347ff414b5b4f040372b95fb1cfecdc8"), // dlc.rpf/x64/models/cdimages/patchday4ng.rpf
    ShaUnpack("35e044c12169df951f1d5f2be91dbd5a41443f080cdef02af033cd98b0138991"), // dlc.rpf/x64/models/cdimages/patchday4ng_cutspeds.rpf
    ShaUnpack("3044fab5a26205ee151d45e5492a04ecf10ec1f65e17823bfb683701db11e2d5"), // dlc.rpf/x64/models/cdimages/patchday4ng_f_outfits.rpf
    ShaUnpack("fd590ce57d969473d426de46698278cd355744418f93b62f07055a07e7c813c4"), // dlc.rpf/x64/models/cdimages/patchday4ng_female.rpf
    ShaUnpack("a3a19c449747d3b69e46eed47c4f40179d27adb0628fce9c34bab4346ef39ac8"), // dlc.rpf/x64/models/cdimages/patchday4ng_male.rpf
    ShaUnpack("529f87273184d2d645078b8569dd03f420f63bb108aba367a71483ea38b93fad"), // dlc.rpf/x64/models/cdimages/patchday4ng_p.rpf
    ShaUnpack("efc901e3e2ae6fad138caf585821b531795f0a1a52ec8761bbb6a7495d8f847b"), // dlc.rpf/x64/models/cdimages/weapons.rpf
    // update/x64/dlcpacks/patchday5ng/dlc.rpf
    ShaUnpack("c8e878be2f49f41c197b0973afd51a62a3b8ee3a918426dfe3e5033fdef83a4f"), // dlc.rpf
    ShaUnpack("cf83e6c334ab173dee5884da08a7570a9fbbcde26dbf74312a69ad5b95c717f7"), // dlc.rpf/x64/anim/ingame/clip_anim@.rpf
    ShaUnpack("9cb3bc6a1adff01d1e5614e8b3f85828f6bbff548328d08a8eeaf34189beadb8"), // dlc.rpf/x64/anim/ingame/clip_melee@.rpf
    ShaUnpack("203a9ac611163837574e3e0a708cde048fd78feccbcc4cae513cb2c7f533eaa5"), // dlc.rpf/x64/anim/ingame/clip_veh@.rpf
    ShaUnpack("92ffe5bbcd6f2208974ae61d55409c801039e70e55425bf7e5ac499aca1801cc"), // dlc.rpf/x64/levels/gta5/vehicles.rpf
    ShaUnpack("21a3d41e763544a1896a26c06282edd03aef614aa9429fc9ecc82c03af8af104"), // dlc.rpf/x64/models/cdimages/patchday5ng_male.rpf
    // update/x64/dlcpacks/patchday6ng/dlc.rpf
    ShaUnpack("43a8f995d42b93f212f1043a0c4ff8038cc9638f459ad77ddb865e8784257ff0"), // dlc.rpf
    ShaUnpack("97333b6da789f52434b2b5b23d754e4921196a812ef049e76671dfb41fb85b77"), // dlc.rpf/x64/anim/ingame/clip_anim@.rpf
    ShaUnpack("562506c9c577e96f64bf62f827920db5e6818d83e455ed2ff22984132e6800f0"), // dlc.rpf/x64/anim/ingame/clip_move_.rpf
    ShaUnpack("0b5b74201a56953c013c32edb7a911b37fbbb095673f39ec9410fe171830c339"), // dlc.rpf/x64/levels/gta5/generic/icons2.rpf
    ShaUnpack("d2b0b19f80ada90d278c201937ec4c50f0e46ab773d4b574cb169d337cc9b0b9"), // dlc.rpf/x64/levels/gta5/props/icons2.rpf
    ShaUnpack("781eab1f7b270670219a82f74fb50d0a0d4dad23a5fe09a8910a3f515fb3a2cf"), // dlc.rpf/x64/models/cdimages/patchday6ng.rpf
    ShaUnpack("da7dce156374c864a611a8b25fd6bf1a2acdda5887b37544dea3ac761b28f7dd"), // dlc.rpf/x64/models/cdimages/patchday6ng_male.rpf
    // update/x64/dlcpacks/patchday7ng/dlc.rpf
    ShaUnpack("e0f91814fcc53da773fb93e6b43852f48578fe485159fcb1db2040aa65b86483"), // dlc.rpf
    ShaUnpack("235101278929b407e9feea178836284665c20769c8526990918e78a4fab4a78e"), // dlc.rpf/x64/anim/ingame/clip_veh@.rpf
    ShaUnpack("433b218f984fe41117e455dcbf8396dd47fbee4bef1b63289a9891495e5ccf01"), // dlc.rpf/x64/anim/ingame/clip_weapons@.rpf
    ShaUnpack("3db40085f83a1eab1286a293a5b5b383307e7819a33647ce32d1daa64e6c5481"), // dlc.rpf/x64/levels/gta5/vehicles.rpf
    ShaUnpack("08af3729153661e49538f20c24707cdd9ca6ace8fb472902dcbbb3046749c234"), // dlc.rpf/x64/models/cdimages/patchday7ng.rpf
    ShaUnpack("e945d3e8fb289dbf5ab5a7319f42c1acd6289c29dc678cb8d17db948ace1a0ce"), // dlc.rpf/x64/models/cdimages/patchday7ng_male.rpf
    ShaUnpack("686429c65e7da8b4f0f9ca323ad81ecb57c63f7700b65094b00984d14f393a3d"), // dlc.rpf/x64/models/cdimages/weapons.rpf
    // update/x64/dlcpacks/patchday8ng/dlc.rpf
    ShaUnpack("98dba4fc7d276c6c9fdd89029370ed449457c1a224405ad7a1be37acd033890f"), // dlc.rpf
    ShaUnpack("b741c56572385b58ea6f5d689e24d66c2a731f5064cfe20a16534cd13fb3a588"), // dlc.rpf/x64/anim/ingame/clip_amb@.rpf
    ShaUnpack("70ae51fc734283376ffe6bea3eb2a20d6f574f686cc07440b8d77c8552c3c84b"), // dlc.rpf/x64/anim/ingame/clip_anim@.rpf
    ShaUnpack("4e26a4c947af39a42b449b6e4f46d5dd2e57e3d26bbfbed7a274dcb1cd8552d3"), // dlc.rpf/x64/anim/ingame/clip_mini@.rpf
    ShaUnpack("690fb25bfb7eb291fac1cfa78121d998097c900f99310ce42ac30b71c5c5e7f2"), // dlc.rpf/x64/anim/ingame/clip_move_.rpf
    ShaUnpack("b396a977370b55a625b23ff75019383b3ec58f282c1c8aeaaf086e66b24ae493"), // dlc.rpf/x64/anim/ingame/clip_mp_.rpf
    ShaUnpack("29e1ecdb7b85d5a0e58cea586f7365cbf60674788e803501e93172d0aa744272"), // dlc.rpf/x64/anim/ingame/clip_switch@.rpf
    ShaUnpack("104fdd2652042059362ec38218382eb4006f4799d8099a1d5a5091933a06157b"), // dlc.rpf/x64/anim/ingame/clip_veh@.rpf
    ShaUnpack("1362a57805db69c42727e72369aa47095287953042f11d3bd214f52ed9b46232"), // dlc.rpf/x64/levels/gta5/vehicles.rpf
    ShaUnpack("ab82e2cd72c9f1b13c3ab14616d42bf401a95512708c40969759e2f2aeeecf14"), // dlc.rpf/x64/levels/gta5/generic/icons3.rpf
    ShaUnpack("ca56773e7769538e45ccae5daa7fa74cb701cac0c81ca02a75ae44a68bba92fb"), // dlc.rpf/x64/levels/gta5/props/roadside/v_storage.rpf
    ShaUnpack("dffe0c00f359b28741b12cac6760c9fb9a7866c6ce70a9f1f30079e9275f922d"), // dlc.rpf/x64/levels/patchday8ng/vehiclemods/lurcher_mods.rpf
    ShaUnpack("c7906546fdfdfd09c8755f203a6791cd4c18cb47b9d16255339fbcfabf6e2d68"), // dlc.rpf/x64/models/cdimages/patchday8ng.rpf
    ShaUnpack("93e2c79b35094406acf71c0a3547cbd0a8409b80b94a8edbc8d221ffc65a075b"), // dlc.rpf/x64/models/cdimages/patchday8ng_female.rpf
    ShaUnpack("437446a2668a7c4e21829184f59c707934186bf0e4a59bee725868794f10692e"), // dlc.rpf/x64/models/cdimages/patchday8ng_male.rpf
    ShaUnpack("2ff6c52c9e5fbb8357e50431c0ccb5a21c73e135c972818059b9ba35f0af3d25"), // dlc.rpf/x64/models/cdimages/weapons.rpf
    // update/x64/dlcpacks/patchday9ng/dlc.rpf
    ShaUnpack("347ec62c4aef1c73a8d1c08c6cdb49b756cca1d1ba24e7c21ee505ce373144ce"), // dlc.rpf
    ShaUnpack("b08d24bd5e5f390444a7155559e7a70c31916c1efca7d7170eca1af06d5e4d87"), // dlc.rpf/x64/anim/ingame/clip_amb@.rpf
    ShaUnpack("7addb7f28750e3d562b2d82c59c3acf53ed89b69c06711b35c674b53d2293cf9"), // dlc.rpf/x64/anim/ingame/clip_anim@.rpf
    ShaUnpack("0f4ab8010da335f055da629adb4ea9eaa9e2c769800c2732ea2dbbbae669146a"), // dlc.rpf/x64/anim/ingame/clip_mini@.rpf
    ShaUnpack("9a0e836e525ddda706d29247c3a0e12a24ee20ccfbb5b1d0fb5e3eba4aa6093c"), // dlc.rpf/x64/anim/ingame/clip_mp_.rpf
    ShaUnpack("d12b66a94d1b9de2539594b921ce2fbd35b5cc24e11c364c8a720fb8fc113a32"), // dlc.rpf/x64/anim/ingame/clip_switch@.rpf
    ShaUnpack("e2e3706cec91d5406be6104b355a0625d5e691b73268ce806d5dd8110062556b"), // dlc.rpf/x64/anim/ingame/clip_veh@.rpf
    ShaUnpack("57e315d84f9844b1d663615cc9d758efcc3f25b7df6ddf7c746b0378360e2997"), // dlc.rpf/x64/levels/gta5/vehicles.rpf
    ShaUnpack("8d9abc2ee604c96ea6d0681db7be24591ddc706db85e4b133932588cfd4a2cb9"), // dlc.rpf/x64/levels/gta5/generic/icons4.rpf
    ShaUnpack("098de0aa03a0bbf601c39d63e98bb7edab4d829f8c7a30bbef32044ac244d413"), // dlc.rpf/x64/levels/patchday9ng/vehiclemods/banshee2_mods.rpf
    ShaUnpack("f762cb1df113c3d4f1f893c8ec4852acf4e80f393d4402a18cb67e7f71fbc943"), // dlc.rpf/x64/levels/patchday9ng/vehiclemods/buccaneer2_mods.rpf
    ShaUnpack("d959b528d9b8553297dfa04071d3a87704bf0d7897e826153a553c0e90b84f64"), // dlc.rpf/x64/levels/patchday9ng/vehiclemods/dubsta_mods.rpf
    ShaUnpack("20d181ea67b292e6cdfc6f4edf8d373115d9cc9cce74599129b6978a052f5d0a"), // dlc.rpf/x64/levels/patchday9ng/vehiclemods/faction2_mods.rpf
    ShaUnpack("bf71589fbe48f974908094cb14492a0d790ccc3dd9cc682a8c35ca3d58be7233"), // dlc.rpf/x64/levels/patchday9ng/vehiclemods/moonbeam2_mods.rpf
    ShaUnpack("ef68b2076694f0fc746a29d2cae44831f3fadb91e180974d0b4322b71dd3c38c"), // dlc.rpf/x64/levels/patchday9ng/vehiclemods/phoenix_mods.rpf
    ShaUnpack("28b95f613af40d8ec313e6d2bfafaff070e5536a8f0d1641f75fba2ec4fe3068"), // dlc.rpf/x64/levels/patchday9ng/vehiclemods/sultanrs_mods.rpf
    ShaUnpack("a51b73bc4faaaf48ef286a19eed08b48678b9c48cab90827d0cab2a2e128d453"), // dlc.rpf/x64/levels/patchday9ng/vehiclemods/supermod1_mods.rpf
    ShaUnpack("17aa543485b21c2c7b73150d2a7a92c96bd6ecd1d88f9129f41d0cd399eb807f"), // dlc.rpf/x64/models/cdimages/patchday9ng.rpf
    ShaUnpack("24bc8d2f250a9a27d831817cdfdd58d9d96ea4cd941b205ff8a1b75e62d7532c"), // dlc.rpf/x64/models/cdimages/patchday9ng_female.rpf
    ShaUnpack("b704b13a1199a0c1391aff7029badc5153be9ef388430b13868f2161bc0fa934"), // dlc.rpf/x64/models/cdimages/patchday9ng_female_p.rpf
    ShaUnpack("ea1a8f5db4a2b0954d15e587266057792017fc1404a9a1a6c8c521eca7806e20"), // dlc.rpf/x64/models/cdimages/patchday9ng_male.rpf
    // update/x64/dlcpacks/patchdayg9ecng/dlc.rpf
    ShaUnpack("6a396ef60204e892868852f81006e46bd88c0fef039d221554b405223e938f8e"), // dlc.rpf
    ShaUnpack("378db65622005eb683c3828927ae5d2eae98a47aa07e8a4e396508eafac6fa30"), // dlc.rpf/x64/levels/gta5/vehicles.rpf
    ShaUnpack("24412f7a0ecc39b693f04ad209c7925f8c05382f4109650739e36e45c36f2d8a"), // dlc.rpf/x64/levels/patchdayg9ecng/vehiclemods/sentinel_mods.rpf
    // update/x64/dlcpacks/mpbattle/dlc1.rpf
    ShaUnpack("1a896d1b73b0c792c2fdecf2c9e0ef9b814512229bd4f9ba5c65f2af494c2190"), // dlc1.rpf
    ShaUnpack("a518bf5b799ab03642d3f26a85a55533ce4dba5526ddb27939340bc13ae25f40"), // dlc1.rpf/x64/prop_battle_lights.rpf
    ShaUnpack("225f2941ae28375ab0e06c52719ad944dc98255afead49c159a85da7f97e4ff2"), // dlc1.rpf/x64/audio/occlusion.rpf
    ShaUnpack("6a1af85852999d990ecb26f137cd6292fc273422dd7ef9158e9e6a8706b57f9d"), // dlc1.rpf/x64/data/effects/ptfx.rpf
    ShaUnpack("99bc7e3de95817f07833c9488b084c0ea7d524beafedd06c43d478356cf055a2"), // dlc1.rpf/x64/data/lang/americandlc.rpf
    ShaUnpack("9ae44f22c595cd710c9ba51054a88093b0b04b7af904da12af79e62ddf658363"), // dlc1.rpf/x64/data/lang/chinesedlc.rpf
    ShaUnpack("9ec2219b8f2d44972c06a3cb647aa2f7ae67f0125d292826eb2c3a9b439a3d7f"), // dlc1.rpf/x64/data/lang/chinesesimpdlc.rpf
    ShaUnpack("3702a05d24ef6fb4b63003a60d35e33332dde6a28d3e015343aacf426314de81"), // dlc1.rpf/x64/data/lang/frenchdlc.rpf
    ShaUnpack("b59e00bf9e769120887f90895c864170198757c44003032fc77d5d764c33314e"), // dlc1.rpf/x64/data/lang/germandlc.rpf
    ShaUnpack("fdd2c8e40a65707e5d37cac88ab67263b4787c39d1fe79eb7146b3248d3c5d8e"), // dlc1.rpf/x64/data/lang/italiandlc.rpf
    ShaUnpack("6c5778863ad7479e0064aa5db476e484443e1d6a0849e1638e09bfe9407cfd36"), // dlc1.rpf/x64/data/lang/japanesedlc.rpf
    ShaUnpack("d0de39324abfb2a1523e3c51e752d375c35d2b57b44d4f7fbf4711b6b42a0451"), // dlc1.rpf/x64/data/lang/koreandlc.rpf
    ShaUnpack("b981f486ba7f91a69f16d2b829586dc6460e089f6387a10948723792e6222ec7"), // dlc1.rpf/x64/data/lang/mexicandlc.rpf
    ShaUnpack("2e18d83496f9b7417fd32e87aad928f141e0a443b279a574741db4020c9a2b9a"), // dlc1.rpf/x64/data/lang/polishdlc.rpf
    ShaUnpack("6a9ef714931c3aa69e47ac916decac7ef6fed23d416bea9f6fcc4935a4646f3d"), // dlc1.rpf/x64/data/lang/portuguesedlc.rpf
    ShaUnpack("8d8e15942f9a1aeb66c99fde1da4d74301f659214b607f1c14d34cee6418fe04"), // dlc1.rpf/x64/data/lang/russiandlc.rpf
    ShaUnpack("6c86efb16f8f4e464b7c6a5867622e3732137ac9082d9ec0ff9231db2f192a3d"), // dlc1.rpf/x64/data/lang/spanishdlc.rpf
    ShaUnpack("c221751772a42e48507495e5a3d6a4a6aa23dc2f05a88ac030f87f20de7a6fc2"), // dlc1.rpf/x64/levels/gta5/mpbattleipl.rpf
    ShaUnpack("5aa48ebce4c1a9e0fcb5960ce915febe44dfae78f7753b7dae42249ad3ad448e"), // dlc1.rpf/x64/levels/gta5/navmeshes.rpf
    ShaUnpack("f06965ae9c6b09b0c821e8ce716b7c5eb62f378e9f6063bbb5a186660080dda2"), // dlc1.rpf/x64/levels/gta5/interiors/int_01_ba.rpf
    ShaUnpack("0cf6417ef1a74a09505ee0f36863c296ce9e58bf7484d5c40ab7029aa165dd55"), // dlc1.rpf/x64/levels/gta5/interiors/int_02_ba.rpf
    ShaUnpack("4c13fd2cf0f83e81c8a574af9efce334510530ff830bb3b56b7d5dafade54171"), // dlc1.rpf/x64/levels/gta5/interiors/int_03_ba.rpf
    ShaUnpack("6843e5ed93f98421ed5b85286f141c295a2580149364db1591c8ca31cc40bbba"), // dlc1.rpf/x64/levels/gta5/interiors/int_placement_ba.rpf
    ShaUnpack("4a6a6b2c076a83f0b9d537d67d5f5cdfff3063a4d228fcac1b6dfead85bac0dd"), // dlc1.rpf/x64/levels/gta5/props/prop_battle_bomb.rpf
    ShaUnpack("7c5f1c18913d875e909a7c4c86ef38925f385609ccc7bb405be860a27819a872"), // dlc1.rpf/x64/levels/gta5/props/prop_battle_crates.rpf
    ShaUnpack("35fb3acc368c11f5f5664386b672f1f374a2dcdfb3c190384ceff5c9e637c73b"), // dlc1.rpf/x64/levels/gta5/props/prop_battle_dj.rpf
    ShaUnpack("d82659963aee435336efa9c6763222a3945a889f769844ce959b9c8fdb585d7a"), // dlc1.rpf/x64/levels/gta5/props/prop_battle_drones.rpf
    ShaUnpack("b5f189207bfbcb6e1e3fa3c796850aef1b1ade942e5c5c02c7fa2a31de8bbd63"), // dlc1.rpf/x64/levels/gta5/props/prop_battle_lights.rpf
    ShaUnpack("96e0dc9cd3bf8f76570f85e8e606afd6a751cc10f1c3d0dedfb5d20e1ac06726"), // dlc1.rpf/x64/levels/gta5/props/prop_battle_lights_02.rpf
    ShaUnpack("44fbe3a860db543436ee85c0baf7ca51922bf6b4d112344dad1942b745182147"), // dlc1.rpf/x64/levels/gta5/props/prop_battle_lights_03.rpf
    ShaUnpack("6a6c886fbf69c28096f3fb3ddd7462dd72b87655e041481b7c495b3599771286"), // dlc1.rpf/x64/levels/gta5/props/prop_battle_lights_04.rpf
    ShaUnpack("b97a5fd7ecf788bf7176c7d416519f18611822b4d67e88f9f70eeee4cf8a476a"), // dlc1.rpf/x64/levels/gta5/props/prop_battle_lights_05.rpf
    ShaUnpack("2c3d6b29a49dcbd9be287fb9e75ac18534ee2aff9543cd097645decf252f07fa"), // dlc1.rpf/x64/levels/gta5/props/prop_battle_lights_content.rpf
    ShaUnpack("628f7e0d211e6a5a96b4396778dedba4a501412b9361017b85601e51e3deed51"), // dlc1.rpf/x64/levels/gta5/props/prop_battle_tent.rpf
    ShaUnpack("ca5b5c973583e739a7e5600f2a14273c911d8092b3bfc68df033ee0a9bda165d"), // dlc1.rpf/x64/levels/gta5/vehicles/mpbattle.rpf
    ShaUnpack("0c58281cf8418624f02c4a96cfd13bbca06781db08f87ec13d171c768535f25c"), // dlc1.rpf/x64/levels/mpbattle/vehiclemods/blimp3_mods.rpf
    ShaUnpack("22342cd1dc2f0854543b859b45aca478931f95367d6c674edf588d4aeaf62c39"), // dlc1.rpf/x64/levels/mpbattle/vehiclemods/freecrawler_mods.rpf
    ShaUnpack("ac3471499f4ed5daf48490abbffdfa9ad6a4ada616c3bf32cc066faff5b0f86b"), // dlc1.rpf/x64/levels/mpbattle/vehiclemods/menacer_mods.rpf
    ShaUnpack("cc9a749994f8c6f225a338e3954c8e13d68d63e295f05d8bc226680ba1a85132"), // dlc1.rpf/x64/levels/mpbattle/vehiclemods/mule4_mods.rpf
    ShaUnpack("a02e511a8f55a8948a7b940df5761318f8b7ad9dfe29049ba0661d220cf2c840"), // dlc1.rpf/x64/levels/mpbattle/vehiclemods/oppressor2_mods.rpf
    ShaUnpack("2efa9f5df2459cc8a197a9c05906d11479fc23b29b67d6973460a006aad07a1f"), // dlc1.rpf/x64/levels/mpbattle/vehiclemods/patriot2_mods.rpf
    ShaUnpack("86e0a6e0bd7cb788b6dc9cd3c4f679a4e0ae0857c3c338a1101beba4a3756187"), // dlc1.rpf/x64/levels/mpbattle/vehiclemods/patriot_mods.rpf
    ShaUnpack("37047aef4efc355ed12b0745644a5a8e1cf099bac89163c59d8be9c413f277e3"), // dlc1.rpf/x64/levels/mpbattle/vehiclemods/pbus2_mods.rpf
    ShaUnpack("e429084361422620a7a56ce30f861c88cefdfe9e2fba22c9fb4c120039fa5361"), // dlc1.rpf/x64/levels/mpbattle/vehiclemods/pounder2_mods.rpf
    ShaUnpack("ec0ce94e02c3e96da6f3313a16f612745a8d8049fbdb8f9ea1e89adb13c96907"), // dlc1.rpf/x64/levels/mpbattle/vehiclemods/scramjet_mods.rpf
    ShaUnpack("ee68a9c4a9398f759356705760e91dd8ce7eebbdcbc53c3d5c331e56149c7e5a"), // dlc1.rpf/x64/levels/mpbattle/vehiclemods/speedo4_mods.rpf
    ShaUnpack("2dfc8dba10ff181a325b278992406b4b1681b7348b0e7b988346f01087de5a57"), // dlc1.rpf/x64/levels/mpbattle/vehiclemods/stafford_mods.rpf
    ShaUnpack("083ccbfeb28184962cd9d474e64103378d1c026a77bdba735617d9f0a74bf33c"), // dlc1.rpf/x64/levels/mpbattle/vehiclemods/strikeforce_mods.rpf
    ShaUnpack("14eafc5ef867a3153cb10641b47353f4aac6a40fa7e0e001b80bf666f96a7e29"), // dlc1.rpf/x64/levels/mpbattle/vehiclemods/swinger_mods.rpf
    ShaUnpack("95e53b4a0ada1bfb3a653e96b6fb508862e6d2b724362aad0711968b271a4b3d"), // dlc1.rpf/x64/levels/mpbattle/vehiclemods/vehlivery_mods.rpf
    ShaUnpack("a878f8a577b4742840082b096073bb1a42d6dae077334acbda0ceca326730ee4"), // dlc1.rpf/x64/models/cdimages/mpbattle_cutspeds.rpf
    ShaUnpack("845089bd6157ad436f094a0e2c67c550810b1c05a4d618c0e2dadb1b7ca2a8b9"), // dlc1.rpf/x64/models/cdimages/mpbattle_female.rpf
    ShaUnpack("c7f39ce3ca1e0368825925b4a0b6bc3b356872e9639574567da4fdd70a03f55d"), // dlc1.rpf/x64/models/cdimages/mpbattle_female_p.rpf
    ShaUnpack("1e6c55af707f6e11a5060a51c18d7dbd6cdade4f364c7c485e311c98c9a9b1b8"), // dlc1.rpf/x64/models/cdimages/mpbattle_male.rpf
    ShaUnpack("5805c24c655c7b46bf60a804b2891551d578f7cca88a4b3cee0e9dd822fb3375"), // dlc1.rpf/x64/models/cdimages/mpbattle_male_p.rpf
    ShaUnpack("e473da333a734c2249d1378b1a5c73af1679df29dc5e53001fd1c74f4a728647"), // dlc1.rpf/x64/models/cdimages/mpbattle_ped_mp_overlay_txds.rpf
    ShaUnpack("87d6b392496c84dfd966d9dd0bfe921c3865f3bb153b2fd46f2650af08972703"), // dlc1.rpf/x64/models/cdimages/weapons.rpf
    ShaUnpack("d1765437931ed258055a7ec6290196e6bc2f42bff3a000d92df4a11dc29c663f"), // dlc1.rpf/x64/models/cdimages/peds/mpbattle.rpf
    ShaUnpack("f41ba2d0e5a97345e3927f9ecefb372bde3cd6f9401846e4cab639affa3e3d45"), // dlc1.rpf/x64/models/cdimages/peds/mpbattle_p.rpf
    // update/x64/dlcpacks/mpheist4/dlc1.rpf
    ShaUnpack("3409bb3e995f65bcf63745aa10a96ee1c41cffcdbe64136e62030c6005678079"), // dlc1.rpf
    ShaUnpack("5c77fb56c4b4b3f13350e8c1678607882759f370b0d93c2951f59c87b47d717a"), // dlc1.rpf/x64/audio/occlusion.rpf
    ShaUnpack("addca5f4d2d0f9e4d88b05f6d604be9c9bdcc7ac62a1808979e3057085c2e237"), // dlc1.rpf/x64/data/effects/ptfx.rpf
    ShaUnpack("3bd68b26ed05eb51fd38287a4ba7177a3585d706ca25e58e1417f60a9bc1a183"), // dlc1.rpf/x64/data/lang/americandlc.rpf
    ShaUnpack("5326bd31a74ab7868a6fc6bbefeda2947ce1b3678c55ea0e06925569f59f2bae"), // dlc1.rpf/x64/data/lang/chinesedlc.rpf
    ShaUnpack("479bec80d4c11e56c1dd679a8cafc5172414e41b26d80421d166398ca6d330e5"), // dlc1.rpf/x64/data/lang/chinesesimpdlc.rpf
    ShaUnpack("30f33751f694a4d126f985956733b50a56df7466d548810c110900736568fb0d"), // dlc1.rpf/x64/data/lang/frenchdlc.rpf
    ShaUnpack("7ef32491fb6c02c4aa62e52514d9eceb3e1b1b15c64e59690ebbdcbe91de829b"), // dlc1.rpf/x64/data/lang/germandlc.rpf
    ShaUnpack("982708aa0fe41d34a1aecfd4620b5681b481ef0a47eef5bdbec6daad1b97e65e"), // dlc1.rpf/x64/data/lang/italiandlc.rpf
    ShaUnpack("abd58fc118a75d01ce8dd6dbeb80eba2c8b80f03937be21723dabf417073063d"), // dlc1.rpf/x64/data/lang/japanesedlc.rpf
    ShaUnpack("d306fecc4162cd1b60d3644f93dc1bd039d8c95d1d3ed786ef7d77227ebc1db6"), // dlc1.rpf/x64/data/lang/koreandlc.rpf
    ShaUnpack("23f804ab54f74ac755008f18b0ff62a5a200e1c54b5add4326e639c8e4f40b60"), // dlc1.rpf/x64/data/lang/mexicandlc.rpf
    ShaUnpack("4c6648b9234900eb65f1684543d02546eb81dc3f5e1c72c59b88fe9b2516d5b6"), // dlc1.rpf/x64/data/lang/polishdlc.rpf
    ShaUnpack("dade79fc87c936cc9e41862bd21e9eb8a1cfe2b94257a7e1a7df2ca1d177933d"), // dlc1.rpf/x64/data/lang/portuguesedlc.rpf
    ShaUnpack("c38fbddca60491e6582fc032eb25b984f8286689a69a1887e60aebcbad5e7aaf"), // dlc1.rpf/x64/data/lang/russiandlc.rpf
    ShaUnpack("2be1c5d893888ac18ae7c7074e62b8f218ec08e4d9d7e6211f106a5d69e98b93"), // dlc1.rpf/x64/data/lang/spanishdlc.rpf
    ShaUnpack("079fcb787591a097a77fdde02bae8e7545d4d6a7f1f004de3a39cc2f8e9122bc"), // dlc1.rpf/x64/levels/gta5/_cityw/additions_01/h4_yacht.rpf
    ShaUnpack("c5892b4eaec608acacccf3e27070581c9c6afc37fba46eb427ec9bf5dc351f4e"), // dlc1.rpf/x64/levels/gta5/_cityw/additions_01/h4_yacht_metadata.rpf
    ShaUnpack("fc7f9c6a146a3ca1e9b4433c40ebb87da58ccb088cb989571830163a8e5f8d9f"), // dlc1.rpf/x64/levels/gta5/generic/mph4_gtxd.rpf
    ShaUnpack("0aba268b605c9f6f7e194fed39da0cd8bafd14fc6c0ad76f02cfc121f3969e6b"), // dlc1.rpf/x64/levels/gta5/interiors/dlc_int_01_h4.rpf
    ShaUnpack("0cae0015e9da1ba33eaa2fb24c31adce148f47e99b914220309c6f526385bbe1"), // dlc1.rpf/x64/levels/gta5/interiors/dlc_int_02_h4.rpf
    ShaUnpack("164af523ebd4a2ad70e13ba3c42b054743d6707e905bd7c5c1b5824a3de0b708"), // dlc1.rpf/x64/levels/gta5/interiors/dlc_int_03_h4.rpf
    ShaUnpack("3c534a2521e6f1cdd5ad3f27a7999db3df5dc66d937b2d865d4c496b570d36c3"), // dlc1.rpf/x64/levels/gta5/interiors/dlc_int_04_h4.rpf
    ShaUnpack("3414454b69202c2c773e005e2c8c16562fe6effe933d0cfb2c38ea71fee344e2"), // dlc1.rpf/x64/levels/gta5/interiors/dlc_int_05_h4.rpf
    ShaUnpack("5467d2a3d21e59eee124cdce0eec2d071d3b721837ff4ad8db7953157aa9f0b7"), // dlc1.rpf/x64/levels/gta5/interiors/int_mp_yacht.rpf
    ShaUnpack("59f1dc6e9c4753811380a7a2e2a0cb7191744738099f64810c9a02d4aa920dae"), // dlc1.rpf/x64/levels/gta5/interiors/int_placement_h4.rpf
    ShaUnpack("f4dd49fbec5f437e45cb58ce0f3f8336272cddbb3045745c342ccc1e3275fe03"), // dlc1.rpf/x64/levels/gta5/mph4_island/mph4_airstrip.rpf
    ShaUnpack("dcf5895772fcbe9c20f646100201d938136dd670fc5c0a1f5c3d282997c4053c"), // dlc1.rpf/x64/levels/gta5/mph4_island/mph4_beach.rpf
    ShaUnpack("c5dca1957cf7662bd0e98bce57b9c310516cf48d87a205a9c3810418b83dd94e"), // dlc1.rpf/x64/levels/gta5/mph4_island/mph4_dock.rpf
    ShaUnpack("f99b753e1f5f4ea16a33acca255058ac06f8f7a9c29b244e80fa6495c652cc08"), // dlc1.rpf/x64/levels/gta5/mph4_island/mph4_island.rpf
    ShaUnpack("cbdbd160a60722c80cfc60bf731865f02a165dc9593aae936567e6215d329847"), // dlc1.rpf/x64/levels/gta5/mph4_island/mph4_island_combine.rpf
    ShaUnpack("ecf5505c15095cfdf85a7498e178e76577a72f82f906d685cb7594319824a754"), // dlc1.rpf/x64/levels/gta5/mph4_island/mph4_island_combine_metadata.rpf
    ShaUnpack("939ee699ff2ee583def899d9d3f8d1516f103a03291732f36cfee70d74909be6"), // dlc1.rpf/x64/levels/gta5/mph4_island/mph4_island_lod.rpf
    ShaUnpack("125d11937e31cb478ec41a6b73b7e1b2efaf4ac9f91914a2b83e34e70e340092"), // dlc1.rpf/x64/levels/gta5/mph4_island/mph4_island_ne_placement.rpf
    ShaUnpack("acbd5082e526796683bc04f1db11648863c79cca4105689a0b592a5b8d06671a"), // dlc1.rpf/x64/levels/gta5/mph4_island/mph4_island_nw_placement.rpf
    ShaUnpack("a6388b46566ed3fc592012d8721dcb76ac0ef9e6cb33c3b87ebe972ffe038a9d"), // dlc1.rpf/x64/levels/gta5/mph4_island/mph4_island_placement.rpf
    ShaUnpack("efc91630cfad0982e4aefd9acf172d3fe2ce1f248b2abf81cbb22a3d038869d8"), // dlc1.rpf/x64/levels/gta5/mph4_island/mph4_island_se_placement.rpf
    ShaUnpack("4b1e329d28c44b04fdb165ba5be7c810d919f0bb7557b15bf7c223c88d9567f5"), // dlc1.rpf/x64/levels/gta5/mph4_island/mph4_island_sw_placement.rpf
    ShaUnpack("bca46bc3ae5591479f48389886f5864da77097d2a22e734b3af722713a6ce58e"), // dlc1.rpf/x64/levels/gta5/mph4_island/mph4_mansion.rpf
    ShaUnpack("d51f980b979b650c8f05f205333f5716765ad53da93def5aeaf90fd8af962e37"), // dlc1.rpf/x64/levels/gta5/mph4_island/mph4_mansion_b.rpf
    ShaUnpack("b09bf9968741d2e32534d53fc4423c24fa4b6a533faff2650c1aa5731037f29b"), // dlc1.rpf/x64/levels/gta5/mph4_island/mph4_wtowers.rpf
    ShaUnpack("fc3ba2abdaae3e22e8b8514b945b7e751922c51f4d8826908ea7c0f55be4dae9"), // dlc1.rpf/x64/levels/gta5/mph4_terrain/mph4_terrain.rpf
    ShaUnpack("119bf37bf73df972362ca4c41b9235703f6d77637b2f0bd3a6a9ce49d3acc85d"), // dlc1.rpf/x64/levels/gta5/mph4_terrain/mph4_terrain_01.rpf
    ShaUnpack("9843cf6ae39d481cbc1e2a6dbb247b2350e7239188c5d11137904cea894043fd"), // dlc1.rpf/x64/levels/gta5/mph4_terrain/mph4_terrain_02.rpf
    ShaUnpack("ef3ca70a183d7a0fad9551beb5fd365da82ec291ec35447953c999122ca192fb"), // dlc1.rpf/x64/levels/gta5/mph4_terrain/mph4_terrain_03.rpf
    ShaUnpack("3ac22a54bcab153a2439fae819aa1c9ca5cab8fa8e3915c198dd6197068e583b"), // dlc1.rpf/x64/levels/gta5/mph4_terrain/mph4_terrain_04.rpf
    // update/x64/dlcpacks/mpheist4/dlc2.rpf
    ShaUnpack("b1a0055504eaeb43e6f351c84888d7eb3923d51f637301b2f7c73b88b286b74d"), // dlc2.rpf
    ShaUnpack("f83b46695744c1105ce77d6a2889f5dbbc4f9dd3fe94f2b9acf3603eb9cee5ec"), // dlc2.rpf/x64/levels/gta5/lodlights.rpf
    ShaUnpack("d9c484b33051f892be94b1eb86f006a09a0f6f24cfcc2f806eeab2d90660edb9"), // dlc2.rpf/x64/levels/gta5/navmeshes.rpf
    ShaUnpack("4604d9b560a8604af985a601a49b1d599e4287e59606e645e19649baa0481ef0"), // dlc2.rpf/x64/levels/gta5/mph4_terrain/mph4_terrain_05.rpf
    ShaUnpack("8fe4de0746bfa17fd4faf76a931ff30fcaca78c13bae66a9310749cd00288760"), // dlc2.rpf/x64/levels/gta5/mph4_terrain/mph4_terrain_06.rpf
    ShaUnpack("41af19a6aa1765cd4087bfd0bfd834faaf0fcc7f859d2f3e6b90e2847f621d6a"), // dlc2.rpf/x64/levels/gta5/mph4_terrain/mph4_terrain_instance_placement.rpf
    ShaUnpack("d7b4fc9c4e57aa46b18d4720d906939e2006d54644180ac03103e74fa6fb90ab"), // dlc2.rpf/x64/levels/gta5/mph4_terrain/mph4_terrain_lod.rpf
    ShaUnpack("2dc6a477c1f49f7717419a808c4710200f21a60a9319ae635379ed5796948907"), // dlc2.rpf/x64/levels/gta5/mph4_terrain/mph4_terrain_metadata.rpf
    ShaUnpack("a8dac2d860ea41a2039d53c2483a640e01a532149496e05a8f491aec760d496e"), // dlc2.rpf/x64/levels/gta5/mph4_terrain/mph4_terrain_occ.rpf
    ShaUnpack("4f1c61813d7ce44ad6f296b811357a901d02d5649242ccf9cd85e52f945d26fd"), // dlc2.rpf/x64/levels/gta5/props/prop_h4_accs_01.rpf
    ShaUnpack("d73fcf59dd3e765479d4ee18f8c807989d86bd5cb582925649bf46dc46107798"), // dlc2.rpf/x64/levels/gta5/props/prop_h4_accs_02.rpf
    ShaUnpack("5a42afccaee74bc9883cf1097f872c51df1fda61ab8904d289ec059b96a3d38e"), // dlc2.rpf/x64/levels/gta5/props/prop_h4_arcade_manhunt.rpf
    ShaUnpack("9334165021bfe709fabb32b98eae0173baaf201ab48789e0117f534b9ec9499c"), // dlc2.rpf/x64/levels/gta5/props/prop_h4_arcade_trophies.rpf
    ShaUnpack("104095c38df28779fea957c5059d6b039c91344bce66baacfa8ac312e006e456"), // dlc2.rpf/x64/levels/gta5/props/prop_h4_casino.rpf
    ShaUnpack("b03415f5fd5b1305c7e8482b8cc136a22a1a011bf12526652b1e166e3ffa101c"), // dlc2.rpf/x64/levels/gta5/props/prop_h4_coke_01.rpf
    ShaUnpack("f8a119fb6980737d5f7878647dbdf9352ac8195695c0035db4dd80793b48d183"), // dlc2.rpf/x64/levels/gta5/props/prop_h4_des_gate.rpf
    ShaUnpack("2b56f2f5c2441c7b9ccc444ff29af941c6136f3f3c2a6fde83e041a435a411f9"), // dlc2.rpf/x64/levels/gta5/props/prop_h4_des_gate_end.rpf
    ShaUnpack("94cc107e7033bc7b7935cccdf8c52a8483af564930b3269578ef8680c0a1d28e"), // dlc2.rpf/x64/levels/gta5/props/prop_h4_dj.rpf
    ShaUnpack("9338ffc4ec3146031c01e748af26ba75b6866af4e07a7e5c0979f0d91ff7414c"), // dlc2.rpf/x64/levels/gta5/props/prop_h4_ground_cover.rpf
    ShaUnpack("6f79b56a3fc481bc77150aeb48de9302199d3917d60c93f3633126f3eafa25a8"), // dlc2.rpf/x64/levels/gta5/props/prop_h4_hatches_01.rpf
    ShaUnpack("e11e76b4dab2881a591184eceed121d03c49103c6adb7fe5b2225cc4e4e2ff17"), // dlc2.rpf/x64/levels/gta5/props/prop_h4_island_01.rpf
    ShaUnpack("9e29679a9b3547922ad12ad11ca499e8d48a24f6ba14334c98970d61b1fa0b08"), // dlc2.rpf/x64/levels/gta5/props/prop_h4_island_02.rpf
    ShaUnpack("14ffce5b2cf8d06a5e0ef892386f2c76ab667ada1996d906f7bab5fbaecb181f"), // dlc2.rpf/x64/levels/gta5/props/prop_h4_island_03.rpf
    ShaUnpack("699d18df444bb63c90a547c46b7cd714923610208a7010fcc55c3764a2990cd1"), // dlc2.rpf/x64/levels/gta5/props/prop_h4_lights.rpf
    ShaUnpack("ee7883d0e9b62019965f0a0aa2889f6063fb3f58c266d78e36fb5ead724200c2"), // dlc2.rpf/x64/levels/gta5/props/prop_h4_lights_02.rpf
    ShaUnpack("81cc50e7e755326e48c5a3b287a27faf8e60a51ce2e795bb9c893433bd3d9dfd"), // dlc2.rpf/x64/levels/gta5/props/prop_h4_lights_03.rpf
    ShaUnpack("662b52ba7a88fac08f6fd66b2453d2b779da765208969d86904db63a64acb1d3"), // dlc2.rpf/x64/levels/gta5/props/prop_h4_lights_04.rpf
    ShaUnpack("4247eb819ef9229c0b8e723e5f3ea70037db0e503e726e28b7df2c9a9c25b6b0"), // dlc2.rpf/x64/levels/gta5/props/prop_h4_lights_05.rpf
    ShaUnpack("7f5e1da91de6d722be5d4ec5ac75d438be423e393fd0ddbc03d45a09f0bceee0"), // dlc2.rpf/x64/levels/gta5/props/prop_h4_lights_content.rpf
    ShaUnpack("1d17307bfbcfcd8367ff0daffd761fab8bc77dc92d6a500eb652fd677c515cb4"), // dlc2.rpf/x64/levels/gta5/props/prop_h4_money_01.rpf
    ShaUnpack("ec2e5a7a619858f672dd328d9f0d62dfa28902259a129a753422ffc6e5465b20"), // dlc2.rpf/x64/levels/gta5/props/prop_h4_office_01.rpf
    ShaUnpack("32e6bfac7506e29d4b9bcd0c9b43a1d8ee43714b28d33ffa401a77e46edd8bd7"), // dlc2.rpf/x64/levels/gta5/props/prop_h4_potted.rpf
    ShaUnpack("28e9e8c0fa5827e0df8470e97df9fc1741ea945f008c3499bc389563156f1da4"), // dlc2.rpf/x64/levels/gta5/props/prop_h4_rock.rpf
    ShaUnpack("b79037e74add08ae2b937d876d0b6f3583665498f521bd372daa2a7496e14589"), // dlc2.rpf/x64/levels/gta5/props/prop_h4_sam.rpf
    ShaUnpack("4bc10bcafc1f97606102477db7124d12ff1306d439235d0e98cdb8976e9b718e"), // dlc2.rpf/x64/levels/gta5/props/prop_h4_sub.rpf
    ShaUnpack("e54572fd47bfb4fb15710812d396c82408cee27d40808e827e811b5483063ef0"), // dlc2.rpf/x64/levels/gta5/props/prop_h4_tables.rpf
    ShaUnpack("d7ed23ad9c4f4d414d0af99a6c9fac9b3dd3535450e30767aec2d8029aeff9dc"), // dlc2.rpf/x64/levels/gta5/props/prop_h4_trees.rpf
    ShaUnpack("24d7fafddceadc8f35a749743cfa427f9ccb3faa3008d6df59c6e38526def78e"), // dlc2.rpf/x64/levels/gta5/props/prop_h4_weed_01.rpf
    ShaUnpack("8a33431a578a91c36482e0de2f7f665b8d863b997db51c93e51b6f2a05195e48"), // dlc2.rpf/x64/levels/gta5/props/props_h4_mines.rpf
    ShaUnpack("dd6afc36956ea580ae915edefed7ce4637056ea9068d1fc3f55dbc180c571298"), // dlc2.rpf/x64/levels/gta5/props/props_sub_int_h4.rpf
    ShaUnpack("9b8cecd79d99463ed1722669bf41962a864033fc90788d8b8d1ecdae585c010c"), // dlc2.rpf/x64/levels/gta5/props/game_assets/prop_game_asset_test.rpf
    ShaUnpack("c916954a8a4a53428cbc0f2886695554c4bbc333febec77903d33d6b7071844d"), // dlc2.rpf/x64/levels/gta5/props/high/int_mp_doors.rpf
    ShaUnpack("0113578c0fe36d1cdc656cfe562c9de616ff95a69ecf921e761b6fcb1d72a78e"), // dlc2.rpf/x64/levels/gta5/props/high/int_mp_h_props.rpf
    ShaUnpack("910c375e43cea42e7710b5b8fa197bca45e4dde6135d0d6f0c52b83a067ddaec"), // dlc2.rpf/x64/levels/gta5/props/yacht/mp_apa_yacht.rpf
    ShaUnpack("0f96f852f919fc8c2b0e3b0e8f353c970766bdf616b1c104597bfc879ddb9503"), // dlc2.rpf/x64/levels/gta5/props/yacht/mp_apa_yacht_jacuzzi.rpf
    ShaUnpack("53a8fd8942357529b70453ef95159635a431ace74ecceb5db2b11343a8769f0c"), // dlc2.rpf/x64/levels/gta5/vehicles/mpheist4.rpf
    ShaUnpack("fb5efa9d0cb708d3c3b780e5c3b5214fdfecd0d8352e0771860f7d0372b51eac"), // dlc2.rpf/x64/levels/mpheist4/vehiclemods/alkonost_mods.rpf
    ShaUnpack("edca06b4f546f35c35e3f8a2c0127fe96fad7cef8bc1e1d777e167e4b88f17a3"), // dlc2.rpf/x64/levels/mpheist4/vehiclemods/annihilator2_mods.rpf
    ShaUnpack("00b746ad02397ef773c6e36cded97361d9f9754b538ef02ca56c371b6a5752b1"), // dlc2.rpf/x64/levels/mpheist4/vehiclemods/italirsx_mods.rpf
    ShaUnpack("794bc7298449bf6c642bece7237e554f79b27556043a24c07f720a091ef2b919"), // dlc2.rpf/x64/levels/mpheist4/vehiclemods/kosatka_mods.rpf
    ShaUnpack("ec72bbe499b81bf6b7ef9f70fa19c3ce4d16c3e6dd7879cbc9b18b3e830f0610"), // dlc2.rpf/x64/levels/mpheist4/vehiclemods/manchez2_mods.rpf
    ShaUnpack("bea016e87d19409f07a3c2971a10f5b437ae84dbeedd62e8c68107b22ab47ac0"), // dlc2.rpf/x64/levels/mpheist4/vehiclemods/seasparrow2_mods.rpf
    ShaUnpack("9dc7e782ac9295d896957fef179364f4318c45f0c50a0cad94722c12b41397d8"), // dlc2.rpf/x64/levels/mpheist4/vehiclemods/seasparrow3_mods.rpf
    ShaUnpack("9daf0386eacfdc434aca44be983e9d9967bcb78782395b722ad623ed115f636f"), // dlc2.rpf/x64/levels/mpheist4/vehiclemods/slamtruck_mods.rpf
    ShaUnpack("2c7f76b48d3bdbefdfa4c5f0824b439cf8d0753bfe29f431c6f2de43c23ce86a"), // dlc2.rpf/x64/levels/mpheist4/vehiclemods/squaddie_mods.rpf
    ShaUnpack("6bfdac2c47a33fe5ca35fad40158e418438ccb296bbc7cf00805b52c6727b67e"), // dlc2.rpf/x64/levels/mpheist4/vehiclemods/toreador_mods.rpf
    ShaUnpack("35049274c060ed625860d01f0e40232083decb008aa78b17b9654abf89e715ff"), // dlc2.rpf/x64/levels/mpheist4/vehiclemods/verus_mods.rpf
    ShaUnpack("f4045fdc6864ac5ddbfd65dfeb1d4f2afedec3361febc7a29b69d4f32767a15e"), // dlc2.rpf/x64/levels/mpheist4/vehiclemods/veto2_mods.rpf
    ShaUnpack("0864858aa5ea495ebef3aee1e5f21413785c7eeddcf75f57b167f17857708651"), // dlc2.rpf/x64/levels/mpheist4/vehiclemods/veto_mods.rpf
    ShaUnpack("e425741d53a4086f325b4e1579a58b66ba11dfd99a2315e717724629049f6cd4"), // dlc2.rpf/x64/levels/mpheist4/vehiclemods/weevil_mods.rpf
    ShaUnpack("12688f74162556021d3e7103aa0f4ad6ba7a73be730effea5b7258b37fe5edf5"), // dlc2.rpf/x64/levels/mpheist4/vehiclemods/winky_mods.rpf
    ShaUnpack("58f98bc35809db36743b4170e768d4dfc7326f4e2b8958dc2b97e5d9fa8d28e4"), // dlc2.rpf/x64/models/cdimages/mpheist4_female.rpf
    ShaUnpack("20f71c1e945ccd3352ff3aac32936b6810c6d96e52f0ae0cda2cb8ebed1c7607"), // dlc2.rpf/x64/models/cdimages/mpheist4_female_p.rpf
    ShaUnpack("236d8dbda21197767f776912b3ee252df440b60f8b3dbf5a3a84349bdfe8dc54"), // dlc2.rpf/x64/models/cdimages/mpheist4_male.rpf
    ShaUnpack("260bf5590962e83309b917e27c4f492746c33f60bd303aa9ffc993820ccda7d1"), // dlc2.rpf/x64/models/cdimages/mpheist4_male_p.rpf
    ShaUnpack("ab50a3a543fabd999d2bf71fc4d1b847a0363e0fe06d8fdb929e82dcc8a5255a"), // dlc2.rpf/x64/models/cdimages/mpheist4_ped_mp_overlay_txds.rpf
    ShaUnpack("6d40d7a7d2a717a16b9e0c4491660147592b2b1d9ee1f05e94002409d2eb3abc"), // dlc2.rpf/x64/models/cdimages/peds/mpheist4.rpf
    ShaUnpack("22aa77d673ccff56139608966b887046abd027f92a8f0e7be6d021f23fbf2575"), // dlc2.rpf/x64/models/cdimages/peds/mpheist4_cutspeds.rpf
    ShaUnpack("981429c6e8283b4176c72eb8cde02cf22e0b1893a8d0cb7cc18f063d2580d0f0"), // dlc2.rpf/x64/models/cdimages/peds/mpheist4_cutspeds_p.rpf
    ShaUnpack("dbe72051ca1e36e06f0cf058674db6950fff07d44e631a11a04f6e78cb033b06"), // dlc2.rpf/x64/models/cdimages/peds/mpheist4_p.rpf
    ShaUnpack("7ad372f29d6de9225763af59e7f7550d8582c34f46c117f17204e678a1e964b0"), // dlc2.rpf/x64/models/cdimages/peds/mpheist4_streamed.rpf
    ShaUnpack("75fa21788089fd73e29138552f474e214b29d06e952f91e09fef8002cdac5713"), // dlc2.rpf/x64/models/cdimages/peds/mpheist4_streamed_p.rpf
    // update/x64/dlcpacks/mpsecurity/dlc1.rpf
    ShaUnpack("925dff66120b24d3bdf6aa8b9bbe3ab47f7f77faa0ec31e31d7f4883105ad085"), // dlc1.rpf
    ShaUnpack("ce4447b6f83448d32a549504019c794c63c2c5e2dd3334bdb58217ae5ed07088"), // dlc1.rpf/x64/audio/occlusion.rpf
    ShaUnpack("43a7c32a4c0e2f311fcb4c82125e7b6b97fc272bfbb710158c523bc3bee190ed"), // dlc1.rpf/x64/data/effects/ptfx.rpf
    ShaUnpack("d1f07543733f5d6b1be8c178046c2a4025543e714f1289e67d3bece543ac33fa"), // dlc1.rpf/x64/data/lang/americandlc.rpf
    ShaUnpack("d34598506e5485325affd42a89a393ec7a93e7c406021001e06ff9848b78c709"), // dlc1.rpf/x64/data/lang/chinesedlc.rpf
    ShaUnpack("31bdf3fc4713e95e9b695cd64da0eb26cd1f285d97ed49c5fa5e5d5859bec361"), // dlc1.rpf/x64/data/lang/chinesesimpdlc.rpf
    ShaUnpack("172d402936985ba97285181b638ce9273b2f63f798364c544d555e77a5b66592"), // dlc1.rpf/x64/data/lang/frenchdlc.rpf
    ShaUnpack("d75d82caa4c8ccc7dfb73e69bcafc9991e1c134b5d8f8ffbd33ee0f3e9e8c015"), // dlc1.rpf/x64/data/lang/germandlc.rpf
    ShaUnpack("8838088213ff5b2b200e52f44b03bd6f97965be9396a33995da4b7b6c8871c6b"), // dlc1.rpf/x64/data/lang/italiandlc.rpf
    ShaUnpack("55e6215a41ba21b24dbd2857261c3ec1f9af8ead4e41c7dc0e5fcc07ba0a00d8"), // dlc1.rpf/x64/data/lang/japanesedlc.rpf
    ShaUnpack("1ade3beccd800f550858564c6baafbcb3bdb96749cf20e3e95b31e332fd1c7ce"), // dlc1.rpf/x64/data/lang/koreandlc.rpf
    ShaUnpack("2184486ba64326e1c6057f45e2382f3409f0bbf6411f2e2581992058e62a70ae"), // dlc1.rpf/x64/data/lang/mexicandlc.rpf
    ShaUnpack("59ab3b608db8f51927e0b8f2677e99e5117f13e6d43e7b4d254e783b012fa2c6"), // dlc1.rpf/x64/data/lang/polishdlc.rpf
    ShaUnpack("2194de501fc85af902eff496d73c13713f46fd8a62cd530b62ba352e4fd7e276"), // dlc1.rpf/x64/data/lang/portuguesedlc.rpf
    ShaUnpack("8c81bdb627146299559cf516c35e3063705a6d1e78d4fca8165196e388c9bbea"), // dlc1.rpf/x64/data/lang/russiandlc.rpf
    ShaUnpack("7674a099b977107f7efd32537ee8a9fb4174ed2d4f7edf7b4946a6b3e3b010d9"), // dlc1.rpf/x64/data/lang/spanishdlc.rpf
    ShaUnpack("e3f255a70cec11d1566049fa29635a008a3786a8b1600f36c05ba78af6d64eea"), // dlc1.rpf/x64/levels/gta5/navmeshes.rpf
    ShaUnpack("e234c69339deaf1f36f961b27e4cd95725076d3a15c7674286b05acd9ae70473"), // dlc1.rpf/x64/levels/gta5/interiors/dlc_int_01_sec.rpf
    ShaUnpack("b1c501c7b8588c74f58a60b58678b28be18e0d177ebb4d78db4215b7c68ecbc9"), // dlc1.rpf/x64/levels/gta5/interiors/dlc_int_02_sec.rpf
    ShaUnpack("560682f94e707e113fa424ee2b6e04ee106a33af63d1d6073a2a4a2bf77b0951"), // dlc1.rpf/x64/levels/gta5/interiors/dlc_int_03_sec.rpf
    ShaUnpack("56c97a275ee6b5bb147a762d2f80e01e26116c28b8ece9a54bb074c70d9e3dde"), // dlc1.rpf/x64/levels/gta5/interiors/dlc_int_04_sec.rpf
    ShaUnpack("1698c2adbd180f2a1518ff9afef97f054145227ed7f9a2e68fe9e40afab8c569"), // dlc1.rpf/x64/levels/gta5/interiors/int_mp_yacht.rpf
    ShaUnpack("5f8e79ea712be0de66675c5a07724a68af7f986ec4c2f7710063aae4fc84e0cb"), // dlc1.rpf/x64/levels/gta5/interiors/int_placement_sec.rpf
    ShaUnpack("edb530883c66df70837ddd928aa45d7a4e888586990c3e641b6c7d561a69bca5"), // dlc1.rpf/x64/levels/gta5/interiors/mpsecurity_yacht.rpf
    ShaUnpack("7f09c065865b1d71f9c004ea65592bd1c491bc02b8501bf63a4b4136626563fc"), // dlc1.rpf/x64/levels/gta5/mpsecurity_additions/fixer_yacht.rpf
    ShaUnpack("d64ae9e3a7b36c15febf39530f2103d4135240354ef6079ab89119801e89dd8b"), // dlc1.rpf/x64/levels/gta5/mpsecurity_additions/fixer_yacht_02.rpf
    ShaUnpack("80a11fa864e03d2bcbd091204ff5dabce9b71913021761c8895edb18a22fd640"), // dlc1.rpf/x64/levels/gta5/mpsecurity_additions/fixer_yacht_metadata.rpf
    ShaUnpack("1a399648d999d0477ba2d0bb54f3fb7e2ad752661d5756fb3db24334a911524b"), // dlc1.rpf/x64/levels/gta5/mpsecurity_additions/mpsecurity_additions.rpf
    ShaUnpack("ec41fb515fd6313a13b1fbe71495856d9042a48fd690caee4a503fea01fa0593"), // dlc1.rpf/x64/levels/gta5/mpsecurity_additions/mpsecurity_additions_cs.rpf
    ShaUnpack("ae0cd5f3283fe24244350311d52d8aca765b0babbf1051a651f06262d892d2e1"), // dlc1.rpf/x64/levels/gta5/mpsecurity_additions/mpsecurity_additions_cs_metadata.rpf
    ShaUnpack("45d933cfef76dab2484d5a14a117ef7a1e0f8476dcf61a8db42cac382c763fcd"), // dlc1.rpf/x64/levels/gta5/mpsecurity_additions/mpsecurity_additions_metadata.rpf
    ShaUnpack("5f96f17b8979da6c1b84f53b06033333a2bd0969eff697ea508c2e374765e865"), // dlc1.rpf/x64/levels/gta5/props/mp_apa_yacht_2012_collision.rpf
    ShaUnpack("68f7441accedcf03e88443f82d11ccc5712858827de4e5b34ae65147294bcddb"), // dlc1.rpf/x64/levels/gta5/props/mp_apa_yacht_buoys.rpf
    ShaUnpack("58828ffb92504d88d10a1aad1ce45f5e6199ad289ac34202b337408c6bb9065a"), // dlc1.rpf/x64/levels/gta5/props/mp_apa_yacht_flags.rpf
    ShaUnpack("76856841433841b438deee6ffd8e8a743f8e3451b54a2817ecacf52c3139beba"), // dlc1.rpf/x64/levels/gta5/props/mp_apa_yacht_new_options.rpf
    ShaUnpack("c516738f649ceb374c00271ee56fec3431e25d098385b0be6079be1cf92bcab7"), // dlc1.rpf/x64/levels/gta5/props/prop_ac_yacht_accs.rpf
    ShaUnpack("133c5bf75fecb8b07e6400a61fcfa64ec66164a68495d77c7e245b91602cca91"), // dlc1.rpf/x64/levels/gta5/props/prop_sf_accs_01.rpf
    ShaUnpack("59e07b9269466a80b9d3a0e711949249fdad91d8dc22db4ccf90609ba529a62f"), // dlc1.rpf/x64/levels/gta5/props/prop_sf_accs_02.rpf
    ShaUnpack("566880c613cd6278dddf71a2b26892c56ec37b5137d12d5b167f67e844cb3a2a"), // dlc1.rpf/x64/levels/gta5/props/prop_sf_agency_art_01.rpf
    ShaUnpack("cc79b0f6b1f3b4a07b7d9541f8681e60fcf7b66ac51fa247a03342e03cf71795"), // dlc1.rpf/x64/levels/gta5/props/prop_sf_agency_art_02.rpf
    ShaUnpack("bcc2420947020b2485f866bf3433c7cf49872625511e068069a154b956122f2a"), // dlc1.rpf/x64/levels/gta5/props/prop_sf_agency_art_03.rpf
    ShaUnpack("1041369c10169a283af4ec5d5364ffcfa8dbfd63399125cc076c23d766a37286"), // dlc1.rpf/x64/levels/gta5/props/prop_sf_des.rpf
    ShaUnpack("17e66f477f5b9feb01688be070be54b19057a8da3ce5fb8ed807358cd074e9e2"), // dlc1.rpf/x64/levels/gta5/props/prop_sf_doors.rpf
    ShaUnpack("ac77d2c69df0f0592a7e83fab553ecaf7f7613d36053274c55e55572990f938b"), // dlc1.rpf/x64/levels/gta5/props/prop_sf_garage_equipment.rpf
    ShaUnpack("4d5bf7926bb9c23c26cf33964223eb08502e13e5c427529a8e3701e007ff7307"), // dlc1.rpf/x64/levels/gta5/props/prop_sf_interior.rpf
    ShaUnpack("1827d8461052739675a467bde6e0324f6f9714003859c647a486cb3c448653bc"), // dlc1.rpf/x64/levels/gta5/props/prop_sf_mission_freemode.rpf
    ShaUnpack("9672f405038bea9da3281879a90efdb83591cd71d5770bea680a64f141decb0c"), // dlc1.rpf/x64/levels/gta5/props/prop_sf_music_equipment.rpf
    ShaUnpack("d38e84eabe243c78cef4ea6100addadac9b37bb65afcb79de84bbbca685a043b"), // dlc1.rpf/x64/levels/gta5/props/prop_sf_music_instruments.rpf
    ShaUnpack("b062957a41507c3f816e45e17dd953235a75723a31e92e9aade3f7a6bac2e0ab"), // dlc1.rpf/x64/levels/gta5/props/prop_sf_penthouse_party.rpf
    ShaUnpack("a9528614fe767a99c060521f5a835a9900c9bf2691fe2653d390f559f6bf13c7"), // dlc1.rpf/x64/levels/gta5/props/prop_sf_rec_studio.rpf
    ShaUnpack("04b6728316d40ed12e60880201d04467fb09d5c1f38b4329e582f1644de56b8e"), // dlc1.rpf/x64/levels/gta5/props/prop_sf_structures.rpf
    ShaUnpack("1d32b1bdd7ed66f234dc22beab29912d7ba530d230c4b937174848bb6ae53895"), // dlc1.rpf/x64/levels/gta5/props/prop_sf_vehicles.rpf
    ShaUnpack("3be7c563c19f47831ccf0efbd091b7c6cb3a919c0580611b9bbbda0e6f4ee8a5"), // dlc1.rpf/x64/levels/gta5/props/high/int_mp_doors.rpf
    ShaUnpack("26cd0c6fa61af48a87ae2e8c68aa1d3f957a337c9ae3a7bb71f7ec32691eeb2b"), // dlc1.rpf/x64/levels/gta5/props/high/int_mp_h_props.rpf
    ShaUnpack("40bc180f980fc31e77101a7850ef86c09c39aa260e49696a861170fcf06a0ae0"), // dlc1.rpf/x64/levels/gta5/props/yacht/mp_apa_crashed_usaf.rpf
    ShaUnpack("1c7dc4fba8e13643bc77c5f23dc50789cc4e49562b176c392faa3fadb745d141"), // dlc1.rpf/x64/levels/gta5/props/yacht/mp_apa_yacht.rpf
    ShaUnpack("7eab96cbf8d4d92c36efd40e31e36f20bcaf3b053498ae5577b385a161fee4df"), // dlc1.rpf/x64/levels/gta5/props/yacht/mp_apa_yacht_door.rpf
    ShaUnpack("4db17d05491860aa6280efc4ea29d7230b72537342264ce8a087bb07c69252f7"), // dlc1.rpf/x64/levels/gta5/props/yacht/mp_apa_yacht_jacuzzi.rpf
    ShaUnpack("18d79ee5ff94ce487a27a435ab005f88702c4239cceefdc2d90db36aa3b8e726"), // dlc1.rpf/x64/levels/gta5/props/yacht/mp_apa_yacht_lightrig.rpf
    ShaUnpack("19bb6aa3125ac0d84e07f4c5490d3b8edf033932dd27be01c9224d2fb8bba766"), // dlc1.rpf/x64/levels/gta5/props/yacht/mp_apa_yacht_text.rpf
    ShaUnpack("a0dd896a99a594e6f5a34b8d2c357a77ad7af326a408a6751ef3dc8139877125"), // dlc1.rpf/x64/levels/gta5/vehicles/mpsecurity.rpf
    ShaUnpack("701148574fc02d104b491a419ce23526d492c3b60d955b45b7404ba781544c34"), // dlc1.rpf/x64/levels/mpsecurity/vehiclemods/astron_mods.rpf
    ShaUnpack("2aa5a3732d2974cd94eb51b3519efbc9f78cc4d1f07c1bd565c5456e99aabb2c"), // dlc1.rpf/x64/levels/mpsecurity/vehiclemods/baller7_mods.rpf
    ShaUnpack("eb20849613378d8ea8394923be09b19f9a9113051c27693568d65815a2b93493"), // dlc1.rpf/x64/levels/mpsecurity/vehiclemods/buffalo4_mods.rpf
    ShaUnpack("2077f511361268d531f5426de91ddd6e0ec414cf9212b5b32a65f2301917a9c7"), // dlc1.rpf/x64/levels/mpsecurity/vehiclemods/champion_mods.rpf
    ShaUnpack("74687435ed03322eb5278bf0e9cb68184a7110a47a06a6d53dff7cebe56b98b2"), // dlc1.rpf/x64/levels/mpsecurity/vehiclemods/cinquemila_mods.rpf
    ShaUnpack("542b9992d2f8f5a041b5ec120a96000cf90d8d6726d0d5b50b00cd8a57c3fe86"), // dlc1.rpf/x64/levels/mpsecurity/vehiclemods/comet7_mods.rpf
    ShaUnpack("00c98cb97bb3802848bde772e5707307530b4dc6adb01d4fc4434137a29ecaab"), // dlc1.rpf/x64/levels/mpsecurity/vehiclemods/deity_mods.rpf
    ShaUnpack("797c361778d6c6f3b9325cba29471e6001f8268dfbe84ca28e1c59230afedaac"), // dlc1.rpf/x64/levels/mpsecurity/vehiclemods/granger2_mods.rpf
    ShaUnpack("d6658748e714a0bdf6bf1d586190febee9a45ad20f9dbbe7d6e109846435f783"), // dlc1.rpf/x64/levels/mpsecurity/vehiclemods/ignus_mods.rpf
    ShaUnpack("76c67dc1b7cb6f9bd736b12e716319b906e5c1debeb777e86bd264ffcec55c20"), // dlc1.rpf/x64/levels/mpsecurity/vehiclemods/iwagen_mods.rpf
    ShaUnpack("31844da1bbb71397aafcc6a50edaa7123f03873d6a21722dc8e0994f11ba3fce"), // dlc1.rpf/x64/levels/mpsecurity/vehiclemods/jubilee_mods.rpf
    ShaUnpack("6a8e3a311fa394aab4f7a2d498f04d419fe68c55be03872141f7d9c6ec361fc4"), // dlc1.rpf/x64/levels/mpsecurity/vehiclemods/patriot3_mods.rpf
    ShaUnpack("8d075842d631f2da91e717d4f5e5e51d4fb946548dc8d780ceeb2c24eb1e07e4"), // dlc1.rpf/x64/levels/mpsecurity/vehiclemods/reever_mods.rpf
    ShaUnpack("9b986465d8e28fb8234bfcd4d0dcc5ce32329679b9e6a8e7de3a1148fdbab145"), // dlc1.rpf/x64/levels/mpsecurity/vehiclemods/zeno_mods.rpf
    ShaUnpack("90ec11be0777f340b5dcc1710788ee17fd1aa8aa0ba42ff561612a34202f6773"), // dlc1.rpf/x64/models/cdimages/mpsecurity_female.rpf
    ShaUnpack("84eafd460c6bf2cc6de661a94500ef37340f25a7b6641cb8bc821df1787536d2"), // dlc1.rpf/x64/models/cdimages/mpsecurity_female_p.rpf
    ShaUnpack("ed11f07a3d49bab45627cd688f866c82ae8a36d08f2ad2d289ec7be8616b25b6"), // dlc1.rpf/x64/models/cdimages/mpsecurity_male.rpf
    ShaUnpack("e766e3c2ed9345ce8d42465ba9fe68b7036166ee9d64cb65b48266ba9a641b87"), // dlc1.rpf/x64/models/cdimages/mpsecurity_male_p.rpf
    ShaUnpack("d42ef2c5c2640c8611ae382d1a3b2ffdf2de87e98914a30b7417198c18cc4059"), // dlc1.rpf/x64/models/cdimages/mpsecurity_ped_mp_overlay_txds.rpf
    ShaUnpack("45d249e32c25d6a2f39b7b1b52e9386cf21876190333a86e9c96a1a918c2a75a"), // dlc1.rpf/x64/models/cdimages/weapons.rpf
    ShaUnpack("4706ece85e71c1d95fdeab5167879acd80794520de761758b75245820b8d9229"), // dlc1.rpf/x64/models/cdimages/peds/mpsecurity.rpf
    ShaUnpack("d41a9d64439b779c730d9e3f448afc25da203c8c567a29ddcf03ef5b7f2c70d6"), // dlc1.rpf/x64/models/cdimages/peds/mpsecurity_cutspeds.rpf
    ShaUnpack("104b901251e5de739ab5b50fa9b8a4022ecb4564f077150eb5f09eaf9c5169fc"), // dlc1.rpf/x64/models/cdimages/peds/mpsecurity_cutspeds_p.rpf
    ShaUnpack("75cd68553e0f9ba5e8279c40859dd86feed406d12ab61425fad98b5772b5a92c"), // dlc1.rpf/x64/models/cdimages/peds/mpsecurity_p.rpf
    ShaUnpack("5615d287138f5d3409e5bf882095991fce9ead9baf50dccbf28d2d8647affe37"), // dlc1.rpf/x64/models/cdimages/peds/mpsecurity_streamed.rpf
    ShaUnpack("8421447cff8f7d97fdb45cf4ee72399578595a52c62c18c815387f0e2525845c"), // dlc1.rpf/x64/models/cdimages/peds/mpsecurity_streamed_p.rpf
    // update/x64/dlcpacks/mptuner/dlc1.rpf
    ShaUnpack("dd554876608b61d4ff063aae26ab1cf1d27c51c3aa3025f736727a9927bbd9ad"), // dlc1.rpf
    ShaUnpack("2c2d565e635afe95c0437f6296fc4e237c16f96f33ce21f4602aeba0342a6362"), // dlc1.rpf/x64/models/cdimages/mptuner_male.rpf
    ShaUnpack("337f23dd51032fd168d08d739395dc2bd55105d02c0a4959dc9513785b51b071"), // dlc1.rpf/x64/models/cdimages/mptuner_male_p.rpf
    ShaUnpack("658aaa449adefebf9552a691ec71eba6c889094525d28d9728f0b4a0429aa8ad"), // dlc1.rpf/x64/models/cdimages/mptuner_ped_mp_overlay_txds.rpf
    // update/x64/dlcpacks/mp2024_01/dlc.rpf
    ShaUnpack("aedd265ee9be89d298abf6b372a9224724b585a3a771b80fee2099fa878b83d0"), // dlc.rpf
    ShaUnpack("9107842e1da2bbb850fa006e6011985aa0b82874aa8966705fa0da0976ef0837"), // dlc.rpf/x64/anim/creaturemetadata.rpf
    ShaUnpack("beab4c46dfa5c53f2be3af279876f9d9b1089b0e1080f6c4a9e70ba1f574cfa9"), // dlc.rpf/x64/anim/expressions.rpf
    ShaUnpack("062bab68e4cf4a71ddf819ecc3631473eec49c261d5cacf294799fa4449b2bef"), // dlc.rpf/x64/anim/cutscene/cuts_sum24_btw.rpf
    ShaUnpack("b04515f06d39cc11e30215f8f29b618b132cba4f90a15f2f620abc6083a590e9"), // dlc.rpf/x64/anim/cutscene/cuts_sum24_btw_dead.rpf
    ShaUnpack("00ef8defea57688989cecb67d689c00361cec307755b196b89757a95631738d9"), // dlc.rpf/x64/anim/cutscene/cuts_sum24_bty3.rpf
    ShaUnpack("7b7365455c3d24e7464e2668dd70b560313a60aabff7a0aeb55ff90db9d02848"), // dlc.rpf/x64/anim/cutscene/cuts_sum24_bty4.rpf
    ShaUnpack("00bcbf704e792ae2b805215c9d00f5f695f7af7088762c0215fcb46432bfccfd"), // dlc.rpf/x64/anim/cutscene/cuts_sum24_bty6.rpf
    ShaUnpack("8ed21cb61d342e4179167b101447c98b254321f43227e147e07519ab3ca35110"), // dlc.rpf/x64/anim/cutscene/cuts_sum24_office.rpf
    ShaUnpack("4b781dfc095b3d786a8e0903c4dfe0e2e07a5ff4488ab352fa278ff0735c8286"), // dlc.rpf/x64/anim/ingame/clip_anim@.rpf
    ShaUnpack("774a7cf82cfe88f469cf3fa2e39b6a6977e21f5e4b461534a271770e9fe8f4f5"), // dlc.rpf/x64/audio/occlusion.rpf
    ShaUnpack("cb5bb44662b7f67bf8322df20b51bb6bc44a72a25181365e0daa4ec994ceb859"), // dlc.rpf/x64/data/effects/ptfx.rpf
    ShaUnpack("e46bb97c214ae0936ac5c66dcb300ed451352dac3ee4aa3a659ec6ad42a5d020"), // dlc.rpf/x64/data/lang/americandlc.rpf
    ShaUnpack("5e78d6e30302533c4ac0c05a24a78370d00791116f8d0a630f1a2341936946f1"), // dlc.rpf/x64/data/lang/chinesedlc.rpf
    ShaUnpack("031959bc087e297ea66ca9bd95713e09d38f220dd53bcde84322acdfe8300135"), // dlc.rpf/x64/data/lang/chinesesimpdlc.rpf
    ShaUnpack("277372cf0287cb5d2efe6967faa4bc2a7e5aeee56773d9a0f3ad69c15121652e"), // dlc.rpf/x64/data/lang/frenchdlc.rpf
    ShaUnpack("272de04685e91601d204e3d34e1d32c5a78b9a4830e601e70593c12bac2b2cb3"), // dlc.rpf/x64/data/lang/germandlc.rpf
    ShaUnpack("ad96b04a41cb16109f05d8e1c184cdfbc52ed4b01dd84b6083a7301f6f537217"), // dlc.rpf/x64/data/lang/italiandlc.rpf
    ShaUnpack("c033c732fd5390703e53acf28866c3eb95a422f38277b9058e4a402bd4f5194c"), // dlc.rpf/x64/data/lang/japanesedlc.rpf
    ShaUnpack("95313fce7dd2fef288d73fc7d8a394a254c4fb72866e7af9d0d8f0b53e68ba76"), // dlc.rpf/x64/data/lang/koreandlc.rpf
    ShaUnpack("8830d46315cd1ec6e797f786ec28c3d82c1ccc71aeb93f2d269b48786bcfa1da"), // dlc.rpf/x64/data/lang/mexicandlc.rpf
    ShaUnpack("30592e2f0170df938998a71b6d18fc79111099435b8a2fe5e4864bdbf363cb54"), // dlc.rpf/x64/data/lang/polishdlc.rpf
    ShaUnpack("7cdbf5b1916c1e42524b1df3b89ddc22ef74800a768e60227e6cda5221e16416"), // dlc.rpf/x64/data/lang/portuguesedlc.rpf
    ShaUnpack("f04b0e882754c477f59ea28ed583d9e41acde8db9bcdf0035e0402457462f577"), // dlc.rpf/x64/data/lang/russiandlc.rpf
    ShaUnpack("9bbad43c2ee6b1784aa082d1e7361828815f50cf6ef42ccac373bdb2b3c44f29"), // dlc.rpf/x64/data/lang/spanishdlc.rpf
    ShaUnpack("2cc94660677dcb5480e8f0e69ca18e02941a3eee61b073e561f841e4ef99c732"), // dlc.rpf/x64/levels/gta5/navmeshes.rpf
    ShaUnpack("d1ffce77db8aee4041035d0a958ff63528b7b98aae38ec4b868508626b1f5b76"), // dlc.rpf/x64/levels/gta5/interiors/int_placement_m24_1.rpf
    ShaUnpack("66fec09e82f1fa379c718c4bb9f6c293872e4856a640aa39ccbab90e6d979f54"), // dlc.rpf/x64/levels/gta5/interiors/m24_1_dlc_int_01.rpf
    ShaUnpack("bfc341dd248bec205a243b6b2db045ca1797364b338b52617b28eaeb9dbd64c4"), // dlc.rpf/x64/levels/gta5/interiors/m24_1_dlc_int_carrier.rpf
    ShaUnpack("9593052db896f5bb8a2402623888aab26b4cde70e9fe44f8da308b8f855be154"), // dlc.rpf/x64/levels/gta5/mp2024_01_additions/mp2024_01_additions.rpf
    ShaUnpack("c4428bf0e7a9ced2b10664e2371bfe2441bf424c842d9f655f17cd58712e44b6"), // dlc.rpf/x64/levels/gta5/mp2024_01_additions/mp2024_01_additions_metadata.rpf
    ShaUnpack("726e071a5dfb445c46bc9636e17833ac9d34d5f51ef279dcaedb5acbd2d318cd"), // dlc.rpf/x64/levels/gta5/props/prop_m24_1_accs_01.rpf
    ShaUnpack("6eb8ec144138177c953c1973d9ad208966d2e26c843e4dd7c7ab7ae3da9eb0ac"), // dlc.rpf/x64/levels/gta5/props/prop_m24_1_accs_02.rpf
    ShaUnpack("8b0e1f378c699557abd219752f6f481f9bd1395d5dae41f528c0344e5a8a5c0f"), // dlc.rpf/x64/levels/gta5/props/prop_m24_1_carrier.rpf
    ShaUnpack("ae01b123113eff5340f96592e58a4817c8f4eaf79adaf09cb4f9e795b59cad46"), // dlc.rpf/x64/levels/gta5/props/prop_m24_1_container.rpf
    ShaUnpack("41042a747a2592f2d3956ebdd36a387c5e3747cf38f7978d8f048111d3738497"), // dlc.rpf/x64/levels/gta5/props/prop_m24_1_deathmatch.rpf
    ShaUnpack("4f0ed664d02702d12d8231b55210aa3f25ee9c101dffdee814d0b5aee513690f"), // dlc.rpf/x64/levels/gta5/props/prop_m24_1_doors.rpf
    ShaUnpack("5c86eb7e93629c88496670de42ee686e26cbd7ea21da1bd696b593b57b93a335"), // dlc.rpf/x64/levels/gta5/props/prop_m24_1_facility.rpf
    ShaUnpack("fd1b4af5ed143cc6f617f562293897bcdf856943cc4c3debe9fe50510ffe5edc"), // dlc.rpf/x64/levels/gta5/props/prop_m24_1_interiors.rpf
    ShaUnpack("af9f718d2ff97b1af7a1568be0dc4d162e0a0fd7334c13bf5778183134fc612a"), // dlc.rpf/x64/levels/gta5/props/prop_m24_1_lev_des.rpf
    ShaUnpack("7bbe23d9c9c0a7650b6c6382cb824dae0b6577497705e14cb97407c60b923134"), // dlc.rpf/x64/levels/gta5/props/prop_m24_1_posters.rpf
    ShaUnpack("4df309c89b801de00c1ba395a939a738d5d859678f98ba4bfa6b7a8d2cd0e5aa"), // dlc.rpf/x64/levels/gta5/props/prop_m24_1_race.rpf
    ShaUnpack("df1ea69540c5d5af91025d376a391e4dfad9ac96c43166cedc4b99ed458ef0aa"), // dlc.rpf/x64/levels/gta5/props/prop_m24_1_trophies.rpf
    ShaUnpack("52f9b7ac7e74500b66923fd4fde2d7d0c48f406a8ae72bfc705dc8160344d092"), // dlc.rpf/x64/levels/gta5/vehicles/mp2024_01.rpf
    ShaUnpack("0ebf172cd3a6e86ee9e794efc6d926a488fe402d04cd1fcd683ca86f7590fa3f"), // dlc.rpf/x64/levels/mp2024_01/vehiclemods/castigator_mods.rpf
    ShaUnpack("a90f4cf413f98affdb02903c146e11bdc7739ed125d8e9a1b4f8353d30caf271"), // dlc.rpf/x64/levels/mp2024_01/vehiclemods/coquette5_mods.rpf
    ShaUnpack("7e1e5b074e9ad270a90deec7ea23c9445c8ee6a609fa70bafcc685af10abcfc6"), // dlc.rpf/x64/levels/mp2024_01/vehiclemods/dominator10_mods.rpf
    ShaUnpack("fea3590199205895896de13f165a2ea3a3f227b09f4c4ba8ca174fea84b850b0"), // dlc.rpf/x64/levels/mp2024_01/vehiclemods/envisage_mods.rpf
    ShaUnpack("cfee85d8c68fdd43aadba58d623209fbe6eecdd7f88bdc9a2623d0e7d7c0da66"), // dlc.rpf/x64/levels/mp2024_01/vehiclemods/eurosx32_mods.rpf
    ShaUnpack("d1ccfb0e58b6ba501780530ee6473e6fcae63b479fb7f6c5e2804a660ee33a3c"), // dlc.rpf/x64/levels/mp2024_01/vehiclemods/niobe_mods.rpf
    ShaUnpack("703c9953c110c52477dc6eaf0fa35ff01d94eb22cbdebaf8036edc8d64be60d2"), // dlc.rpf/x64/levels/mp2024_01/vehiclemods/paragon3_mods.rpf
    ShaUnpack("e8c56d14e856821e152d79f90fe7542d79a8568216b7160f62152d133838eae9"), // dlc.rpf/x64/levels/mp2024_01/vehiclemods/pipistrello_mods.rpf
    ShaUnpack("7fb421eef93d8d12c9e6e37da0db5ecd70326dce9dfed14b196973d53b37ca50"), // dlc.rpf/x64/levels/mp2024_01/vehiclemods/poldominator10_mods.rpf
    ShaUnpack("85689eff5aabc943651229d020a6010b1cd2da0772e9fe9b759d7f78f4c2006a"), // dlc.rpf/x64/levels/mp2024_01/vehiclemods/poldorado_mods.rpf
    ShaUnpack("e77ef26d7c87b7813624b520795db91947c9647b58ce2cfd2bb535f362e67671"), // dlc.rpf/x64/levels/mp2024_01/vehiclemods/polgreenwood_mods.rpf
    ShaUnpack("84eecb980d08124bafb6d5f389e9b14a19bed8d2a53cb9fc9583c865da0c7539"), // dlc.rpf/x64/levels/mp2024_01/vehiclemods/policet3_mods.rpf
    ShaUnpack("38d13f6af55866cf9baaa4140d4c63faaf28c0359ab80a2b75b58c02091bc220"), // dlc.rpf/x64/levels/mp2024_01/vehiclemods/polimpaler5_mods.rpf
    ShaUnpack("77f698fc426abaa2bc959a010dbbbf4f0dc12a27f2c454e1f7520c169129b586"), // dlc.rpf/x64/levels/mp2024_01/vehiclemods/polimpaler6_mods.rpf
    ShaUnpack("4b1a685df3e57f6c8dd457d0a6dfe26dba27c4a72bec347eae116c4f36779634"), // dlc.rpf/x64/levels/mp2024_01/vehiclemods/vehlivery5_mods.rpf
    ShaUnpack("dd7802f2c0c8d8a33b077d46293d8671a5f304cc01dccab0a2f2ef52de62c473"), // dlc.rpf/x64/levels/mp2024_01/vehiclemods/vorschlaghammer_mods.rpf
    ShaUnpack("24c5cab3450c4d867c8e6049857efc8a4c34f3ad21340bc521b543b5c52b218d"), // dlc.rpf/x64/levels/mp2024_01/vehiclemods/yosemite1500_mods.rpf
    ShaUnpack("817879f9d160aac2fc832538c91ede8a54657324ebd98100dedc7aa42835466a"), // dlc.rpf/x64/models/cdimages/mp2024_01_female.rpf
    ShaUnpack("3e34ce6215d59014b5ea8d8464428340bb4f07480595e40a6defda46c3e9bf50"), // dlc.rpf/x64/models/cdimages/mp2024_01_female_p.rpf
    ShaUnpack("e7a13d287d3f8793b4ff941720b9be476e7edeeb29d11b3d1ab6b68e06f8a559"), // dlc.rpf/x64/models/cdimages/mp2024_01_male.rpf
    ShaUnpack("990b399590b0222b22d05e00eba96c131e775e9b744db4a91eaa95945c596ea7"), // dlc.rpf/x64/models/cdimages/mp2024_01_male_p.rpf
    ShaUnpack("59b02e4f6aac2fb7459765defea39b80713963331a55ce075be35c3dc0a253da"), // dlc.rpf/x64/models/cdimages/mp2024_01_ped_mp_overlay_txds.rpf
    ShaUnpack("a314d2823f0df0c073f0072c34076c7478860d9ce1962edbdd3f7c539179f783"), // dlc.rpf/x64/models/cdimages/weapons.rpf
    ShaUnpack("48b7d17c42219d117512788b526dee33536fd13cafebfaed9cfcb32d1d5ab9a6"), // dlc.rpf/x64/models/cdimages/peds/mp2024_01.rpf
    ShaUnpack("5c04a331b3e7c9ce18748d2194797fab8de99afaf63d1e7ff532ad1b20faa870"), // dlc.rpf/x64/models/cdimages/peds/mp2024_01_p.rpf
    // update/x64/dlcpacks/mp2024_01_g9ec/dlc.rpf
    ShaUnpack("f2cfc4d468187481a1d346d3f0f317745e160b608864fad917f30de28d487216"), // dlc.rpf
    ShaUnpack("af66ffd838b890cf38f3c29ff017ab8519cf0ec45874145c70fee23bf139fc7a"), // dlc.rpf/x64/levels/gta5/interiors/int_placement_m24_1_g9ec.rpf
    ShaUnpack("396b5703b4d319dbda5bb567e4c0df059a0ce3855d81dbdce40b3c9ae5416030"), // dlc.rpf/x64/levels/gta5/interiors/m24_1_dlc_int_02.rpf
    ShaUnpack("fce4f241ef96be79e11b5104016bebe6763f2f95b58ab00866ee2f0ef640da9a"), // dlc.rpf/x64/levels/mp2024_01_g9ec/vehiclemods/eurosx32hsw_mods.rpf
    ShaUnpack("596561d6a02c93c6a66b0f43534d852c9bb23dba04aa54c217974d6b5c137706"), // dlc.rpf/x64/levels/mp2024_01_g9ec/vehiclemods/niobehsw_mods.rpf
    // update/x64/dlcpacks/patch2024_01/dlc.rpf
    ShaUnpack("0c11013be78a58cf6b1e7985d484af44858910a59d4fb1ec066e099e7697d557"), // dlc.rpf
    ShaUnpack("c8517b194f505c92bbd23d9f90d0323607cc4e1a5ea1c473348cc220e6aa0586"), // dlc.rpf/x64/anim/ingame/clip_anim@.rpf
    ShaUnpack("93e150626de35c25d8ac22e2192638551ac05f772671cb39b4adf6a2f1d54051"), // dlc.rpf/x64/levels/gta5/vehicles.rpf
    ShaUnpack("6a1aec6c2655b9848e05ffcd565ed230d82b67faece3fb391919544ffa8c6bc2"), // dlc.rpf/x64/levels/patch2024_01/vehiclemods/aleutian_mods.rpf
    ShaUnpack("46d8c00ad22882da9744d163acbaec1b6a04421194b76b76c8958108728b54cf"), // dlc.rpf/x64/levels/patch2024_01/vehiclemods/asterope2_mods.rpf
    ShaUnpack("0c647397a5c3ff4c3b73eb5d9323d431529ac2ace085d1fd311147fc0ad9ba00"), // dlc.rpf/x64/levels/patch2024_01/vehiclemods/baller8_mods.rpf
    ShaUnpack("5a795a9f70d91df299e487009e97566d879c89ba091815673301b98711d3cf96"), // dlc.rpf/x64/levels/patch2024_01/vehiclemods/brigham_mods.rpf
    ShaUnpack("6f578f16f391d224513fd63547aebc08965404ba7d3ebdafed00977efd197acb"), // dlc.rpf/x64/levels/patch2024_01/vehiclemods/buffalo5_mods.rpf
    ShaUnpack("5feba39be2ec42712fe46f84bc789cd884bf560ceae40c1c5754d2f222d651c4"), // dlc.rpf/x64/levels/patch2024_01/vehiclemods/cavalcade3_mods.rpf
    ShaUnpack("944660212bd8159281daaa83151cb35651390ca14c6832a6f8bcae0a10f44a27"), // dlc.rpf/x64/levels/patch2024_01/vehiclemods/coureur_mods.rpf
    ShaUnpack("13ea953dc6a36b414dfcff633acf1ab0ed3de00f80192d8a82321229e5871b6d"), // dlc.rpf/x64/levels/patch2024_01/vehiclemods/dominator9_mods.rpf
    ShaUnpack("04da72d5a3c15d2eccd803f174dd1d24f5c52e96011246c11436112da9a82b32"), // dlc.rpf/x64/levels/patch2024_01/vehiclemods/dorado_mods.rpf
    ShaUnpack("529b07eb905586047207c2bd48eb3a396fb5596db2519e8f5af48f50a0b80301"), // dlc.rpf/x64/levels/patch2024_01/vehiclemods/fr36_mods.rpf
    ShaUnpack("5e9b7f8a330514149c78df6da458255e3934afc348f41f8da447af28957f800f"), // dlc.rpf/x64/levels/patch2024_01/vehiclemods/futo2_mods.rpf
    ShaUnpack("2d1137b7683a90b3d675437f63f5f55e738d33e481fd5e21d8c3787a3ff64de1"), // dlc.rpf/x64/levels/patch2024_01/vehiclemods/impaler5_mods.rpf
    ShaUnpack("c4501eab3f42091725e24ee2a1b15e421f4a67163c9eca514dce8c53c390eecc"), // dlc.rpf/x64/levels/patch2024_01/vehiclemods/monstrociti_mods.rpf
    ShaUnpack("d29d36707e679976c762ebabe3840c8b0bb379162f84b6a2382fa670016d9987"), // dlc.rpf/x64/levels/patch2024_01/vehiclemods/turismo3_mods.rpf
    ShaUnpack("141aa19537a6465bfd39ebdc40a27aa91b9c9b9b908307c24254a5a16a986a88"), // dlc.rpf/x64/levels/patch2024_01/vehiclemods/vigero3_mods.rpf
    ShaUnpack("dc31f0e5eb515c8754cf4a35f66bd0b7ed04c50838d6ce00b945fdac8697f0e1"), // dlc.rpf/x64/models/cdimages/patch2024_01.rpf
    ShaUnpack("270d6819b3a1c1b1c5e4932bcd4707d8c8a01944b7dedf0f12506c0c3051f2ae"), // dlc.rpf/x64/models/cdimages/patch2024_01_female.rpf
    ShaUnpack("c5dd56f698f6007db3d31b424a2c4536aae678c62d484eaf8151fb06ce7e47a5"), // dlc.rpf/x64/models/cdimages/patch2024_01_male.rpf
    // update/x64/dlcpacks/patch2024_01_g9ec/dlc.rpf
    ShaUnpack("9c414c5f8cb6ae31b61fb964943e8510d417d66aeb3c62b31c80bba2a43313e6"), // dlc.rpf
    // update/x64/dlcpacks/mp2024_02/dlc.rpf
    ShaUnpack("1393995ff18c1e48d98a63a65fe43a8360f268d105e9d7997025735aefcf7af8"), // dlc.rpf
    ShaUnpack("6e122fe37d6078e67b2513a756b5d3853a21f31bde45f94358b2d680ab646183"), // dlc.rpf/x64/anim/cutscene/cuts_xm24_factory.rpf
    ShaUnpack("1096825a650b8eb5defb2097b467009ef9a5798a220f4c43c1b89a6bc1340eed"), // dlc.rpf/x64/anim/cutscene/cuts_xm24_mfh.rpf
    ShaUnpack("ff99a67309a0b94d73ab6992a48189777ba6c8d5c7377c84bdf4fd0e77c35ab2"), // dlc.rpf/x64/anim/cutscene/cuts_xm24_mfh1.rpf
    ShaUnpack("77fd7621319b385cd47f943ab7f9d5b3938f26983a8ed8d046fff4536d391ea6"), // dlc.rpf/x64/anim/cutscene/cuts_xm24_mfh6.rpf
    ShaUnpack("276776b7c2a431618b7454bd4576731253283a24cff28f8a090ec6ea989c1ade"), // dlc.rpf/x64/anim/cutscene/cuts_xm24_mfh6_fin.rpf
    ShaUnpack("d07f0082903aab58937a92614f251209c0181b46de9542c5d3df91fd28bc6e2a"), // dlc.rpf/x64/anim/cutscene/cuts_xm24_rob.rpf
    ShaUnpack("17852c6c955973c112fd7ea93b5a24bd961b92d1f21ee69e8f12379a3628b21a"), // dlc.rpf/x64/anim/ingame/clip_anim@.rpf
    ShaUnpack("54c2ba83f455cf947d37dad8e9094edf9595be59cbaf56a524a42a36b35e11ee"), // dlc.rpf/x64/audio/occlusion.rpf
    ShaUnpack("fd1aa25cdfe081e46cbedfd126f04c4147b5673e6577a4e7c150a1af32f0712c"), // dlc.rpf/x64/data/effects/ptfx.rpf
    ShaUnpack("b3ec4bfe777c56441cf3c01f1000399d36649be418a5020ed6d30be187d55d56"), // dlc.rpf/x64/data/lang/americandlc.rpf
    ShaUnpack("e68e4e1fc02b8275c8fc6ae0085f9b00155353eac5d407ba3f53ad9f9eb95cb1"), // dlc.rpf/x64/data/lang/chinesedlc.rpf
    ShaUnpack("b3f29c369619c2df9a878529638bcb354e6a60539cc5a70f38fcb0e39063fd34"), // dlc.rpf/x64/data/lang/chinesesimpdlc.rpf
    ShaUnpack("14ec48c00669452f4a0501b72292840a220e8b7582e29fc0a835f12e75ed0bd7"), // dlc.rpf/x64/data/lang/frenchdlc.rpf
    ShaUnpack("059ae5259642d569db450d1ed8cfeff603c14f2b18eaac273fd5206e2f209374"), // dlc.rpf/x64/data/lang/germandlc.rpf
    ShaUnpack("3fc77a4d72957e0b79f829c7c6abaf8499cf0fe421025806bc955cdbd3010c86"), // dlc.rpf/x64/data/lang/italiandlc.rpf
    ShaUnpack("92ef7089dab6120bbbcd1b42caee14ab1334992b5b687e712ba5e34a5fe7dbc5"), // dlc.rpf/x64/data/lang/japanesedlc.rpf
    ShaUnpack("4126d511759a37c37cf77a54576ab3de902987651821609ca61961dd8b2229b3"), // dlc.rpf/x64/data/lang/koreandlc.rpf
    ShaUnpack("33cbe42579679bc9c909cad39d026d55666862079feb7c6a41d7f0609b264b09"), // dlc.rpf/x64/data/lang/mexicandlc.rpf
    ShaUnpack("ca9d792408698a6d6b6ee210661bff36404a4c5584c4983f16b0443754dd2416"), // dlc.rpf/x64/data/lang/polishdlc.rpf
    ShaUnpack("9c079ec50dd906723f2c946f9a04f33a407c636e69f9a6d60263048aad872cda"), // dlc.rpf/x64/data/lang/portuguesedlc.rpf
    ShaUnpack("8e61c0fc6408f240fb4831c4d769c0174b1b7e8aded821e0070dea66d551f4cb"), // dlc.rpf/x64/data/lang/russiandlc.rpf
    ShaUnpack("4021bf1cc7547efad26b72abe07a04466138e2d256c5284e27fbc9178095e205"), // dlc.rpf/x64/data/lang/spanishdlc.rpf
    ShaUnpack("de4cd3f566256d4279239140c86b5c24d5dc491be40b5515a5704caa5eff0dcf"), // dlc.rpf/x64/levels/gta5/navmeshes.rpf
    ShaUnpack("83f419dc090e87659c60bd51537dc928fd041d70e61a9d85ed5fd937b9f1c969"), // dlc.rpf/x64/levels/gta5/interiors/int_01.rpf
    ShaUnpack("4a45190da97a8c09b37d5010bcbe0356a055c46c136f98109d7d68798869d983"), // dlc.rpf/x64/levels/gta5/interiors/int_02.rpf
    ShaUnpack("d793d299e93e5296ae634738bb6ef2bbbcce3154eec46290b4d6539c07d31fe8"), // dlc.rpf/x64/levels/gta5/interiors/int_03.rpf
    ShaUnpack("5b7373b5a79499f9d9f285e66d347bf177064499db1896676b62dbabffffad49"), // dlc.rpf/x64/levels/gta5/interiors/int_placement.rpf
    ShaUnpack("a72e8a40c7e34d804b8339b38e49f1095b76b7c6f89383a66d9e4cbfcaa692d4"), // dlc.rpf/x64/levels/gta5/mp2024_02_additions/mp2024_02_additions.rpf
    ShaUnpack("50665ef6aa4a6f4bc21c239bbf131f1b100b1f390da118be8dbcae99647b5d14"), // dlc.rpf/x64/levels/gta5/mp2024_02_additions/mp2024_02_additions_metadata.rpf
    ShaUnpack("33591381740bb57825e4d77215634ccf25e883d24291d98901ddbf92d2a5d968"), // dlc.rpf/x64/levels/gta5/props/prop_m24_2_accs_01.rpf
    ShaUnpack("e3186e49fa750330ecf4e5036fad66b71a90676dce7c255b5377a22530536683"), // dlc.rpf/x64/levels/gta5/props/prop_m24_2_accs_02.rpf
    ShaUnpack("b3920bf8506d7f443d214a8d9cb9d569759f84ebee5d34eddc17377a2bc09958"), // dlc.rpf/x64/levels/gta5/props/prop_m24_2_barge_01.rpf
    ShaUnpack("58c06f283729052659f10555e29b21e5e135fdeb695aac0e6d53cc582f315076"), // dlc.rpf/x64/levels/gta5/props/prop_m24_2_basement_01.rpf
    ShaUnpack("1fae3e33fe8fca026aa15dc06b16d7821c7cd5045a57b7782ff5363b73ad5ea7"), // dlc.rpf/x64/levels/gta5/props/prop_m24_2_crates_01.rpf
    ShaUnpack("d2a8cfa38bc4647f516339f1fa385d4b483905a01c7f16d816b3a4f3894efdf8"), // dlc.rpf/x64/levels/gta5/props/prop_m24_2_des_radiomast.rpf
    ShaUnpack("4ae8630dcb7d28d955280841540547bed5e14f1a25dae555c04c4c278ceaea30"), // dlc.rpf/x64/levels/gta5/props/prop_m24_2_des_radiomast_end.rpf
    ShaUnpack("4b5260fd7bbd8145c4492ddb1fe210142c81a399ac4a28c9bae4498e8c0feb97"), // dlc.rpf/x64/levels/gta5/props/prop_m24_2_door_01.rpf
    ShaUnpack("81be7aaf875ca6bae026ec782fbd7b6cb196f19cac1dfc07a9aba6f920c9f19b"), // dlc.rpf/x64/levels/gta5/props/prop_m24_2_dronecabin_01.rpf
    ShaUnpack("d293de9fdc3449b53d970a205645923419e11227f29b544dfb783f1864ebfbbc"), // dlc.rpf/x64/levels/gta5/props/prop_m24_2_ground_01.rpf
    ShaUnpack("cee2b5cf0ab67dca7bff39215fbeb0323cfc6463a5c81a15988a9e815e67ae30"), // dlc.rpf/x64/levels/gta5/props/prop_m24_2_lanterns_01.rpf
    ShaUnpack("a4e9e17f1f078fe64e4de3bc625b9d32deed2bfb5b373b105102fff551167d8a"), // dlc.rpf/x64/levels/gta5/props/prop_m24_2_lev_des_01.rpf
    ShaUnpack("54ff5430f5c5f200fdc828fa4a1a7208acd6f30da0e244070aef5cde6c0313eb"), // dlc.rpf/x64/levels/gta5/props/prop_m24_2_plane_01.rpf
    ShaUnpack("64553de33d04bf3b948f1e5647e1438126be7d23997ee47b3edef22713c981db"), // dlc.rpf/x64/levels/gta5/props/prop_m24_2_race_01.rpf
    ShaUnpack("13a862fc6fd3ba6cf0c0ee7f485408df20bd230ab7e63397decaab4f252bec16"), // dlc.rpf/x64/levels/gta5/props/prop_m24_2_racefence_01.rpf
    ShaUnpack("997c4441b65c376b49d0fef32e84546cf3a33330e3d6a066b02080664cd9d817"), // dlc.rpf/x64/levels/gta5/props/prop_m24_2_racetracks.rpf
    ShaUnpack("e7653caa551d72b85a660b78daddb65b22e04e40f9bf82c49fd1e05af8f80fa7"), // dlc.rpf/x64/levels/gta5/props/prop_m24_2_shipwreck.rpf
    ShaUnpack("134e9bd2d230f1ee351ccf682d7b93c34d1ffa4b31855f94065c154512c5b77a"), // dlc.rpf/x64/levels/gta5/props/prop_m24_2_trophies_01.rpf
    ShaUnpack("8cf09c84e7aaee7e997ca6406e472e40be84d3d399124e4f1195c70614c8172d"), // dlc.rpf/x64/levels/gta5/vehicles/mp2024_02.rpf
    ShaUnpack("26b4ee89e344201a00e90dcd267b92da0eeb37f9bd661b8931e786702b380d07"), // dlc.rpf/x64/levels/mp2024_02/vehiclemods/banshee3_mods.rpf
    ShaUnpack("a020d2b9d9b55568bf2234d17d15194f37d95ef7b0dffbd5eac500710fd1cea5"), // dlc.rpf/x64/levels/mp2024_02/vehiclemods/cargobob5_mods.rpf
    ShaUnpack("e48a48a6075eca800240fc631895f007ba2415b96ef77bf13aa0b450df87f707"), // dlc.rpf/x64/levels/mp2024_02/vehiclemods/chavosv6_mods.rpf
    ShaUnpack("21d230e45c862ac1f4abd382ded035a28f0bdae4f7b3cc576a71bc54a37b587b"), // dlc.rpf/x64/levels/mp2024_02/vehiclemods/coquette6_mods.rpf
    ShaUnpack("4416e0b133e6101f2e27b5f0df3a6e626d79e8a458d718b824a00d73ec89c00d"), // dlc.rpf/x64/levels/mp2024_02/vehiclemods/driftcheburek_mods.rpf
    ShaUnpack("4089c57e96980af09d1dc8185bbe0ac395e1066474cadd37a9854a046750c240"), // dlc.rpf/x64/levels/mp2024_02/vehiclemods/driftfuto2_mods.rpf
    ShaUnpack("e578fbb0df0a98c2de4765b32d7771151e9bafa005f4810e02ecd7089dc09116"), // dlc.rpf/x64/levels/mp2024_02/vehiclemods/driftjester3_mods.rpf
    ShaUnpack("89f48ffee0f7276b37cb36be9124c0025e6a1ec38e88d796c5806a47da4df00b"), // dlc.rpf/x64/levels/mp2024_02/vehiclemods/duster2_mods.rpf
    ShaUnpack("bfd1b465f8f7189ccbbfadcf2e7989782bb11890d16284b0e75d871ec781a399"), // dlc.rpf/x64/levels/mp2024_02/vehiclemods/firebolt_mods.rpf
    ShaUnpack("30e3fdde8ca1137a0db39cec7cd64bbe19af32dfa0f8ba9d4442b31947d8d0b9"), // dlc.rpf/x64/levels/mp2024_02/vehiclemods/polcaracara_mods.rpf
    ShaUnpack("3dbcc27936a0d657d508c9bea3c608dac9d1b7e2c77d1f6f3bef0dd7126bc027"), // dlc.rpf/x64/levels/mp2024_02/vehiclemods/polcoquette4_mods.rpf
    ShaUnpack("a86011abab40bf26ef8416a72b125a546f7fd942f3f211719ea8c2984586351c"), // dlc.rpf/x64/levels/mp2024_02/vehiclemods/polfaction2_mods.rpf
    ShaUnpack("207637b5f8578cadcc51d3db0109724899d7ed9c3a73814837c1b55509539e4d"), // dlc.rpf/x64/levels/mp2024_02/vehiclemods/polterminus_mods.rpf
    ShaUnpack("73c949f653102bb675e0cfdf7ce5cbc495b65c5ad10ace12e212d3b94040e22d"), // dlc.rpf/x64/levels/mp2024_02/vehiclemods/titan2_mods.rpf
    ShaUnpack("544d5d848d9fc77410325ed50e3d1e43c19f09aa8d77204d243fae06688cafcb"), // dlc.rpf/x64/levels/mp2024_02/vehiclemods/uranus_mods.rpf
    ShaUnpack("fc7d27bb199857d54ecee3a2a8453f0ec75cdd9a801b49eb1ef817b32ed60c9f"), // dlc.rpf/x64/levels/mp2024_02/vehiclemods/vehlivery6_mods.rpf
    ShaUnpack("df9e1808b8498852291cb9560cbdd06415d40d7c460abdb5f34e1da50d2843b3"), // dlc.rpf/x64/models/cdimages/mp2024_02_female.rpf
    ShaUnpack("5abae59c4859c169118f5b9dde4515891ca625f6a01a2cfe6030288a6ce2a97f"), // dlc.rpf/x64/models/cdimages/mp2024_02_female_p.rpf
    ShaUnpack("bd0171fdccdbce03f6bed9482e361afca2a6b2b758ed98758a2d206f4f403036"), // dlc.rpf/x64/models/cdimages/mp2024_02_male.rpf
    ShaUnpack("0d795a082d5089ca33d073da585b8a6df4a5e4d4299c765b277eff34fb7d2271"), // dlc.rpf/x64/models/cdimages/mp2024_02_male_p.rpf
    ShaUnpack("84c0ac40123fb9e56c513bf839bd07a32f43cb40476607ace9b4b0f528884f9a"), // dlc.rpf/x64/models/cdimages/mp2024_02_ped_mp_overlay_txds.rpf
    ShaUnpack("2d5b1dd35a78e402f62e89b6337c9b2beb34afc613e25380a3f1ed563c7575a7"), // dlc.rpf/x64/models/cdimages/peds/mp2024_02.rpf
    ShaUnpack("d2d2eebfb74c6054bcda685286c925bb39d889df50a5f07c2e06c2881defc9f7"), // dlc.rpf/x64/models/cdimages/peds/mp2024_02_p.rpf
    // update/x64/dlcpacks/mp2024_02_g9ec/dlc.rpf
    ShaUnpack("45aea6f003d488a56ed74a6715af223814b85430b25a57cd180b3aea4371d7de"), // dlc.rpf
    ShaUnpack("17f486101b585c8a87b18f7c4dc3b732ce01437b9977359e6dc9a8f61b066f68"), // dlc.rpf/x64/levels/mp2024_02_g9ec/vehiclemods/banshee3hsw_mods.rpf
    ShaUnpack("e7a686907d2854e34ad6fb850ede59eea5f146d8613ff02d408679253dbc8a87"), // dlc.rpf/x64/levels/mp2024_02_g9ec/vehiclemods/firebolthsw_mods.rpf
    ShaUnpack("62c0b524a192838db7ceb5d8077238f87c6fd297e4ff4daf1dc11dfcc6a35a9d"), // dlc.rpf/x64/models/cdimages/weapons.rpf
    // update/x64/dlcpacks/patch2024_02/dlc.rpf
    ShaUnpack("006bc2aecbd21e190e889232c7a26ea48aac5b5ca381bc39d3b1ff6a34a7884d"), // dlc.rpf
    ShaUnpack("bcf8f266a4aa900827a0ce90b0e0f35c2de0a25516f5eaae901973ccb201e54a"), // dlc.rpf/x64/levels/gta5/vehicles.rpf
    ShaUnpack("c06fa5848ea36080a133d03f4d562156b2b49f5c37b80e4ba3a481609535a1b5"), // dlc.rpf/x64/levels/patch2024_02/vehiclemods/cavalcade3_mods.rpf
    ShaUnpack("296a3cbea5a0ecd017890bbcfaf6e34e2e5e7620c81574b458722173cbeef6af"), // dlc.rpf/x64/levels/patch2024_02/vehiclemods/coquette5_mods.rpf
    ShaUnpack("6c46c79efb614edff9f27ed3f5f2c834d5b7df5c9cae333ac84f49e62c27c602"), // dlc.rpf/x64/levels/patch2024_02/vehiclemods/dominator10_mods.rpf
    ShaUnpack("8f065dca162e73ddf8f768560daae8e4802fed13b701eab3fbe01009f2a5574b"), // dlc.rpf/x64/levels/patch2024_02/vehiclemods/dorado_mods.rpf
    ShaUnpack("e46b4b6d079fd6eab556ffb3b55dd28e987efe311c58a70dce69a74afa59eb42"), // dlc.rpf/x64/levels/patch2024_02/vehiclemods/envisage_mods.rpf
    ShaUnpack("d84218c5dd6dc98cbff265033be733a10698d28c74bfae22e709919eb01b151c"), // dlc.rpf/x64/levels/patch2024_02/vehiclemods/niobe_mods.rpf
    ShaUnpack("141ddff73fc7d55e126878c482581339267c8f98fb05fede00c34b8eb62079a7"), // dlc.rpf/x64/levels/patch2024_02/vehiclemods/paragon3_mods.rpf
    ShaUnpack("1f7a4883dd537cca0e813e959dfe92e94ea59beff533b20d6ebad2a18e4a07d6"), // dlc.rpf/x64/levels/patch2024_02/vehiclemods/pipistrello_mods.rpf
    ShaUnpack("77ce923d6be7597c5d4011fa6d6f4be50cb9d10acea353f558e8e62449d9214b"), // dlc.rpf/x64/levels/patch2024_02/vehiclemods/poldominator10_mods.rpf
    ShaUnpack("fa44f68b060049882f1981cbee0faac94e75e6dacd40d94872a3d957107348f5"), // dlc.rpf/x64/levels/patch2024_02/vehiclemods/stingertt_mods.rpf
    ShaUnpack("8fe3fcacd1265d9e65452290b5d0f7feaf07fe79f0d0320aa4916ffbd162cf0c"), // dlc.rpf/x64/models/cdimages/patch2024_02_female.rpf
    ShaUnpack("c959c5aa8fea998278a9ed07b942d7f8b21fd129c5afc337fdbf0d081b882938"), // dlc.rpf/x64/models/cdimages/patch2024_02_male.rpf
    // update/x64/dlcpacks/mp2025_01/dlc.rpf
    ShaUnpack("27fafc0b9dab8359ea8aeb8dc2242f643b52ed8cabbd6912e25d18da17377a75"), // dlc.rpf
    ShaUnpack("4c9c2f24211813639c521a5610606c8f1ea67542ed66179d3072f603c7b42bc6"), // dlc.rpf/x64/anim/creaturemetadata.rpf
    ShaUnpack("0fc7a628aa6fbe362177172452b889294edd55d9100778601e35c03ed51f1ff4"), // dlc.rpf/x64/anim/cutscene/cuts_sum25_bt2.rpf
    ShaUnpack("6d60865bf1eb9b59ab5c7eb61e6053f50965e0e530010426b76dbd0933ef6f65"), // dlc.rpf/x64/anim/cutscene/cuts_sum25_cayo.rpf
    ShaUnpack("5a6239d45b6269b2152c53a2400fe9bee70b983b696b009fde464b94c4f4baa0"), // dlc.rpf/x64/anim/cutscene/cuts_sum25_lsia.rpf
    ShaUnpack("4ab34f21658e3ab900a4b1480f884c7e15e2e29165f3221cb96032401114e0fd"), // dlc.rpf/x64/anim/ingame/clip_anim@.rpf
    ShaUnpack("95fde72de1f633dde24a466ea914f9ac687f40a3eef03185802be272dcfee524"), // dlc.rpf/x64/audio/occlusion.rpf
    ShaUnpack("d6fa3f7a6c21c2b8b16235ee4d228bde332161cf105219ac57e8e575ec4b1d37"), // dlc.rpf/x64/data/effects/ptfx.rpf
    ShaUnpack("c944e5ba515c5cc058f804c465547b93d025e48db1e08b3165ed498f5ada7b72"), // dlc.rpf/x64/data/lang/americandlc.rpf
    ShaUnpack("d472912e76e904419155dd0517f87ecc742aa3528cf3a1731b037973ea1675b7"), // dlc.rpf/x64/data/lang/chinesedlc.rpf
    ShaUnpack("6724f870073a4131d7d3e29beb1060f62c16b1e1c3e5cb6bcf12ae36d1b1846e"), // dlc.rpf/x64/data/lang/chinesesimpdlc.rpf
    ShaUnpack("10166eb16576420ff1879d16bf19d9651e36547e61ec6930926ceba985a45f23"), // dlc.rpf/x64/data/lang/frenchdlc.rpf
    ShaUnpack("91ddb1712740af2eb197d42081a3ad32dd53d941ae465cfcf436b9b3418b1f9d"), // dlc.rpf/x64/data/lang/germandlc.rpf
    ShaUnpack("32e3b66b3610d78bda39a74dd7e876f0415b1df07d6f8c4b9be350404b7bb420"), // dlc.rpf/x64/data/lang/italiandlc.rpf
    ShaUnpack("ae4ed30305daa95db8af7768c772881d6d38b5ffa1ac249ddcf436ef37da4c86"), // dlc.rpf/x64/data/lang/japanesedlc.rpf
    ShaUnpack("d9d4d38c56bd7c65554c83273c3dd330058b8971224e77bf68b2f170708b2a3a"), // dlc.rpf/x64/data/lang/koreandlc.rpf
    ShaUnpack("3798eb3fdae3ca28239e04534d3b96835c3dfee291d093f806d8a776bacd210b"), // dlc.rpf/x64/data/lang/mexicandlc.rpf
    ShaUnpack("10c1eea715815ec7450355957709109263b38e46e0881e9e046275988b49e2cf"), // dlc.rpf/x64/data/lang/polishdlc.rpf
    ShaUnpack("bd047c78d2d9c26d8dcced994691bf0ab2176bd5845ea417dd5457640435eb5c"), // dlc.rpf/x64/data/lang/portuguesedlc.rpf
    ShaUnpack("3065a2417b9b59b21645b7241a3a8525b0dc886b173d8491d1f6f73930f903ec"), // dlc.rpf/x64/data/lang/russiandlc.rpf
    ShaUnpack("afdfe70c54528864cf0cd2b488ae290b979887c4cc5f89abd248dc8b8ff7a9b1"), // dlc.rpf/x64/data/lang/spanishdlc.rpf
    ShaUnpack("54d955c5cfa917612331ce34fda2bc2bd14c268d2e7951bbc18e3371abad8a6c"), // dlc.rpf/x64/levels/gta5/navmeshes.rpf
    ShaUnpack("cc82fd93293ab098a3951a83834f68493ee222dfa046becd9fc44c94571e5e41"), // dlc.rpf/x64/levels/gta5/interiors/int_01.rpf
    ShaUnpack("2c6946fbb95626e7315d6d6f093e8b457aa562b3b8f0457509cca99dd7b14522"), // dlc.rpf/x64/levels/gta5/interiors/int_02_03.rpf
    ShaUnpack("f78488fe2fcab5c0b4d1c7d06ef9123fbc9339b6c7990331e4dc5c147056a66d"), // dlc.rpf/x64/levels/gta5/interiors/int_04.rpf
    ShaUnpack("fdc5dbb07c6d16341946689978f545b4830e14b5ff0829fc11e7d67a185213a1"), // dlc.rpf/x64/levels/gta5/interiors/int_05.rpf
    ShaUnpack("2fc31beb09592f8851e5b6a79ba9af794774ada99fc4ffcfca2cd1cac68226e3"), // dlc.rpf/x64/levels/gta5/interiors/int_placement.rpf
    ShaUnpack("0a27807f0012f1904165ce309bebd0b71371bfa74377ad2a4bf3954072e5b115"), // dlc.rpf/x64/levels/gta5/mp2025_01_additions/mp2025_01_additions.rpf
    ShaUnpack("5e56bd8b61481ae7b7ca6cfba23618cb004aa15225e86ae5e0da6bfebf84b1fe"), // dlc.rpf/x64/levels/gta5/mp2025_01_additions/mp2025_01_additions_metadata.rpf
    ShaUnpack("dce91b4245ade6aa81833b7b88dfa78ec49e79c1c95e1e9f2a569c0c2eaa6629"), // dlc.rpf/x64/levels/gta5/props/prop_m25_1_accs_01.rpf
    ShaUnpack("d17174efa37c8f514face7f75191c97adea7a6a5ac2a260a1a444121df5ff5f6"), // dlc.rpf/x64/levels/gta5/props/prop_m25_1_cash_01.rpf
    ShaUnpack("61069e38e061a0eb5a137122d1641fcc44c2ebc6f3b8ae21b26a6a0fb4e83050"), // dlc.rpf/x64/levels/gta5/props/prop_m25_1_crane_01.rpf
    ShaUnpack("30d621f0f8c956662ff7d42c148bd9b64f3f6be8f166aa44ec320627d76555e1"), // dlc.rpf/x64/levels/gta5/props/prop_m25_1_crates_01.rpf
    ShaUnpack("44d1b91fe4a82df813c22c03544541dda22b413feff7d3f150e2b0766235886b"), // dlc.rpf/x64/levels/gta5/props/prop_m25_1_door_01.rpf
    ShaUnpack("dbaafb7f9b8cdfdbe2be982dd56c92e4a810096a83b5bdadb3af90c77b16536d"), // dlc.rpf/x64/levels/gta5/props/prop_m25_1_interior_01.rpf
    ShaUnpack("b1fbe91c38ab114458c04b8dac2302ba380e26c63df91225cc46f857f58eebdc"), // dlc.rpf/x64/levels/gta5/props/prop_m25_1_lev_des_01.rpf
    ShaUnpack("eb7ef8bf47c53638bc008135b4639fe5b78b112279225d4cc0f815e9dcf5b35b"), // dlc.rpf/x64/levels/gta5/props/prop_m25_1_survival_01.rpf
    ShaUnpack("2cbeb58c4efebb6a7873a804db24cddbe79b53bbf686399c44a59ad772125a94"), // dlc.rpf/x64/levels/gta5/props/prop_m25_1_weed_01.rpf
    ShaUnpack("e479b2f3de5e5e317c6b92226a37d3aeeb5f0a1ad661c4a179bdf847c0a06ab8"), // dlc.rpf/x64/levels/gta5/props/prop_m25_1_wreckage_01.rpf
    ShaUnpack("580ea278293eb028528b7f3cd97944e04d4e7a01184c78a451fe004db61e3400"), // dlc.rpf/x64/levels/gta5/vehicles/mp2025_01.rpf
    ShaUnpack("e5bfe899106c9b2f4e2b72a409e72ceab6ecac0ecc49238dabb54746d98cd053"), // dlc.rpf/x64/levels/mp2025_01/vehiclemods/cheetah3_mods.rpf
    ShaUnpack("094d6e885b90d9e0070e55d584fb1809dd3fb576a523bcbb37b3bbea28a288b6"), // dlc.rpf/x64/levels/mp2025_01/vehiclemods/driftchavosv6_mods.rpf
    ShaUnpack("ca780895645dec066cd2c9969c3dd1fb91a95c61b3de1cfa1e9399b33c0272cb"), // dlc.rpf/x64/levels/mp2025_01/vehiclemods/driftdominator10_mods.rpf
    ShaUnpack("fc1929599de7621f995ded523287fc1bb870b46cc0baf56a6d1c55d82dd8eb70"), // dlc.rpf/x64/levels/mp2025_01/vehiclemods/driftgauntlet4_mods.rpf
    ShaUnpack("ec265bc4c44bf5c9ed0b96489db1b2e8b5fe18e8183547932934ed89672cdde2"), // dlc.rpf/x64/levels/mp2025_01/vehiclemods/drifthardy_mods.rpf
    ShaUnpack("61885b640f90ff56bd381db698732e34e50ea3f8bd283e550257249678a9750a"), // dlc.rpf/x64/levels/mp2025_01/vehiclemods/driftl352_mods.rpf
    ShaUnpack("25215cf7acd28d48b6cb9e6c591f3b16f115d88382aa3db004bd914b2fb3cdae"), // dlc.rpf/x64/levels/mp2025_01/vehiclemods/everon3_mods.rpf
    ShaUnpack("a0bbdf50e026d4e24721b93188ac57b30d084bcbb72e3861721ea2eec5bf665d"), // dlc.rpf/x64/levels/mp2025_01/vehiclemods/hardy_mods.rpf
    ShaUnpack("e1914cc7ec9dcfc111aecd3d87ce295e95ee28bd98c508c4210f307455c0a998"), // dlc.rpf/x64/levels/mp2025_01/vehiclemods/l352_mods.rpf
    ShaUnpack("74e4f2e2ea50485be0329eaf16b311f0e845e180d84e8edb1253311f1b4c885d"), // dlc.rpf/x64/levels/mp2025_01/vehiclemods/minimus_mods.rpf
    ShaUnpack("41b8964c87fc08ff1f641cd938e7cb7fd11e5d2f362d615fed06aaa3c587ad9b"), // dlc.rpf/x64/levels/mp2025_01/vehiclemods/policeb2_mods.rpf
    ShaUnpack("b4d33fd11f9adc96027edf695ddd2e07d1422c35031131334acb3e92e8254a73"), // dlc.rpf/x64/levels/mp2025_01/vehiclemods/rapidgt4_mods.rpf
    ShaUnpack("fca0594a0c5a242f1ba0ef560c48bd39bc208ecb1307df24df00f2abc5a0e1d6"), // dlc.rpf/x64/levels/mp2025_01/vehiclemods/sentinel5_mods.rpf
    ShaUnpack("fcd8d893b6f87dc22d59788905a1ca8a2f10de28ab9932bcf42c140b6ad1badd"), // dlc.rpf/x64/levels/mp2025_01/vehiclemods/suzume_mods.rpf
    ShaUnpack("135e1285bdd1f765c59064fd3934a801451d3e8db5c5a4baf0843af7c923de4f"), // dlc.rpf/x64/levels/mp2025_01/vehiclemods/tampa4_mods.rpf
    ShaUnpack("5348b9a95c4dbdcc05017600317355e1304b082270f59027baf100cb2aadc036"), // dlc.rpf/x64/levels/mp2025_01/vehiclemods/vehlivery7_mods.rpf
    ShaUnpack("a04bc9ea6ebb102781d882ab1d10837614ccab6af7ac88d38b27a3db5d403595"), // dlc.rpf/x64/levels/mp2025_01/vehiclemods/woodlander_mods.rpf
    ShaUnpack("46ed15957d0fb5e9a5e329cde079bb8ba231cd4c1d12c721e472483af4dfd7e3"), // dlc.rpf/x64/models/cdimages/mp2025_01_female.rpf
    ShaUnpack("8b4af24b32c46595abf059e869777d48d08d0d0a50966bba00ccabd7772de1d8"), // dlc.rpf/x64/models/cdimages/mp2025_01_female_p.rpf
    ShaUnpack("59718d54177fa054ff2fdc2c3c28dc19703a5762b8bb1ddb47092fac77f8be25"), // dlc.rpf/x64/models/cdimages/mp2025_01_male.rpf
    ShaUnpack("64b41762f51ccb3216fcdfb723b69476d56f99c88077fd53fbc712550640068f"), // dlc.rpf/x64/models/cdimages/mp2025_01_male_p.rpf
    ShaUnpack("cdf1b04a9a7013e056e271d9abcb8b0c3e7d438f1a2cb786a832e9cbf57ef119"), // dlc.rpf/x64/models/cdimages/weapons.rpf
    ShaUnpack("340955dc99ab29b272099a30a06611940a5660c44f5928048f35c82a80255aa4"), // dlc.rpf/x64/models/cdimages/peds/mp2025_01.rpf
    ShaUnpack("16a70d95a9d194b8fec19214e0b57ac3718a6d211b14a8cae8d5ae4d80e8091f"), // dlc.rpf/x64/models/cdimages/peds/mp2025_01_cutspeds.rpf
    ShaUnpack("0a8b3d31b11934d5d07a8f914a9aa87c36ff619edc01fd48dea17ba934ca2791"), // dlc.rpf/x64/models/cdimages/peds/mp2025_01_cutspeds_p.rpf
    ShaUnpack("b6e077baa41cbc06673e7ed2fce8f74674bcc61bce24c99afffd21024032fd53"), // dlc.rpf/x64/models/cdimages/peds/mp2025_01_p.rpf
    // update/x64/dlcpacks/mp2025_01_G9EC/dlc.rpf
    ShaUnpack("59dad76ce218c4776a949891ae79330d7cf3361a275b33561ea3d76b546e9fe7"), // dlc.rpf
    ShaUnpack("e4335b6f10b11191fb7c07f5194bed9e5ce5a7e017c98a729659ae5fc325f4df"), // dlc.rpf/x64/levels/mp2025_01_g9ec/vehiclemods/tampa4hsw_mods.rpf
    ShaUnpack("51ef776b7e85bc8501f856d29d510ffd90e0fcdb8fe8251fea5c207f3eb4dfc7"), // dlc.rpf/x64/levels/mp2025_01_g9ec/vehiclemods/woodlanderhsw_mods.rpf
    // update/x64/dlcpacks/patch2025_01/dlc.rpf
    ShaUnpack("920534bbecc770c3e5e087a6cc46e2de01c508edee165300d7387bed0dabf37c"), // dlc.rpf
    ShaUnpack("7e69696d8fa870db3e4eaa98424dec0ee4a8979a15f9a22e00ff3eaf3c69bcf8"), // dlc.rpf/x64/anim/ingame/clip_anim@.rpf
    ShaUnpack("133bb9a8d30debfb7c1b92aece2d28f9abc15e285ab632e96cb09d7f28f41b0b"), // dlc.rpf/x64/levels/gta5/vehicles.rpf
    ShaUnpack("2e06bb7d5591ced0356a7a161eaf1953555b8d91e6cbc088444ae6532467208b"), // dlc.rpf/x64/levels/patch2025_01/vehiclemods/aleutian_mods.rpf
    ShaUnpack("669544fb81cf6e885c00e4ebf780e5e26c1d273ce124c65478da0787051157ff"), // dlc.rpf/x64/levels/patch2025_01/vehiclemods/chavosv6_mods.rpf
    ShaUnpack("784992fa5ba0958cafcd94314ac965e4c973a132568fe2007e523c969ff11789"), // dlc.rpf/x64/levels/patch2025_01/vehiclemods/cheburek_mods.rpf
    ShaUnpack("ec93e60273f5ff09588c1588b9f7ab565001ef11fd8480805fdeaf5c7bbf4830"), // dlc.rpf/x64/levels/patch2025_01/vehiclemods/coquette6_mods.rpf
    ShaUnpack("108b317092ae5fa898b9546a694496600b75873191c4b0fe82ed67333080d733"), // dlc.rpf/x64/levels/patch2025_01/vehiclemods/driftcheburek_mods.rpf
    ShaUnpack("cbad95a16d58203c404eba64c6b8a0ad1cb264fcdb8a6772b585b3fade4cb249"), // dlc.rpf/x64/levels/patch2025_01/vehiclemods/driftfuto2_mods.rpf
    ShaUnpack("5ebe4c20daad0434557c94d3377432c6fe34362c9c881af59160001819e4d51c"), // dlc.rpf/x64/levels/patch2025_01/vehiclemods/duster2_mods.rpf
    ShaUnpack("32b5365044613839d430e50f587d0d26772d7b99760245829a2f7992b2f77bba"), // dlc.rpf/x64/levels/patch2025_01/vehiclemods/eurosx32_mods.rpf
    ShaUnpack("5c5d2a62ac1324d03eadf5eaf98ecc9837942f61444aab35909493c0ac5ccacf"), // dlc.rpf/x64/levels/patch2025_01/vehiclemods/firebolt_mods.rpf
    ShaUnpack("895d803466d6582f9f5f57c3124a96281a39b89f53c88db9b3624058db870e12"), // dlc.rpf/x64/levels/patch2025_01/vehiclemods/growler_mods.rpf
    ShaUnpack("fdd616e3f310551885a434ccc9c543b0135a3332840acb9a60492d141c6b7279"), // dlc.rpf/x64/levels/patch2025_01/vehiclemods/jester3_mods.rpf
    ShaUnpack("8d41e63ee53c7572a522a3c47af49a51932aaa07c4c83736306b7ef281f7825c"), // dlc.rpf/x64/levels/patch2025_01/vehiclemods/paragon3_mods.rpf
    ShaUnpack("1a75b1c306f50fc208d2665b6c3c87255a0420b756c783066b8ed1e702d95232"), // dlc.rpf/x64/levels/patch2025_01/vehiclemods/polterminus_mods.rpf
    ShaUnpack("ed0af9ab0052e81524c236c7eadcd772ff262d7d6ab9c03f3ba55b4e749033fc"), // dlc.rpf/x64/levels/patch2025_01/vehiclemods/terminus_mods.rpf
    ShaUnpack("bfb75d9f71c5fc1e01b2d2cdd49be2dbabd72d702e96092333432a90595dd3b3"), // dlc.rpf/x64/levels/patch2025_01/vehiclemods/tropos_mods.rpf
    ShaUnpack("41c506763e7ce48b33b25c39dffce4a0d1806877a45261de8bb63a4e0a9bd180"), // dlc.rpf/x64/levels/patch2025_01/vehiclemods/uranus_mods.rpf
    ShaUnpack("633a8eb48dd0404d9bc62fbe03f2536946a56898d37211eb254e9275108c2fc7"), // dlc.rpf/x64/levels/patch2025_01/vehiclemods/vehlivery5_mods.rpf
    ShaUnpack("bb5819d2b8827ad5c2f5c4b43f3dd3b6e061996078a22d5c80bd31582202fdc6"), // dlc.rpf/x64/levels/patch2025_01/vehiclemods/vehlivery6_mods.rpf
    ShaUnpack("8bcd74b5726e2eabfb64b03269d8aa76332b999f518ebaaa68c323f691b33f9f"), // dlc.rpf/x64/levels/patch2025_01/vehiclemods/vigero3_mods.rpf
    ShaUnpack("1e14d52e0cef031e1267613f1a065da2b1745cef7dfcf7940bce8c1f671583cf"), // dlc.rpf/x64/levels/patch2025_01/vehiclemods/vorschlaghammer_mods.rpf
    ShaUnpack("324bde7c08f471fb0bb6ede9811285d757b6a560a7a9abd4a19745c9be8897ef"), // dlc.rpf/x64/levels/patch2025_01/vehiclemods/yosemite1500_mods.rpf
    ShaUnpack("d62571619d9e2c4d4f7339bb309a711dd2e390b9cbeaa6e895d7f63bf5ec81cb"), // dlc.rpf/x64/models/cdimages/patch2025_01.rpf
    ShaUnpack("2db07e34ca7b759c00750d5537e2f02405e724666574310f78acb5c899abba96"), // dlc.rpf/x64/models/cdimages/patch2025_01_female.rpf
    ShaUnpack("837d4019321146f78953bcc724b563f3febf814395a8275681770b02d4269b4c"), // dlc.rpf/x64/models/cdimages/patch2025_01_male.rpf
};

}}
