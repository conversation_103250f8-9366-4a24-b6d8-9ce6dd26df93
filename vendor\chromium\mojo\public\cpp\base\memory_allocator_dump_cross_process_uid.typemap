# Copyright 2017 The Chromium Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

mojom = "//mojo/public/mojom/base/memory_allocator_dump_cross_process_uid.mojom"
public_headers = [ "//base/trace_event/memory_allocator_dump_guid.h" ]
traits_headers = [ "//mojo/public/cpp/base/memory_allocator_dump_cross_process_uid_mojom_traits.h" ]
sources = [
  "//mojo/public/cpp/base/memory_allocator_dump_cross_process_uid_mojom_traits.cc",
  "//mojo/public/cpp/base/memory_allocator_dump_cross_process_uid_mojom_traits.h",
]

type_mappings = [ "mojo_base.mojom.MemoryAllocatorDumpCrossProcessUid=::base::trace_event::MemoryAllocatorDumpGuid" ]
