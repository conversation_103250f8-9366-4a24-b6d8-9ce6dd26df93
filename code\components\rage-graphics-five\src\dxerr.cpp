//--------------------------------------------------------------------------------------
// File: DXErr.cpp
//
// DirectX <PERSON>rror Library
//
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
//--------------------------------------------------------------------------------------
// This version only supports UNICODE.
#include "StdInc.h"
#include "dxerr.h"

#include <cstdio>

#include <ddraw.h>
#include <d3d10_1.h>
#include <d3d11.h>
#include <wincodec.h>
#include <d2d1.h>
#include <dwrite.h>

#define XAUDIO2_E_INVALID_CALL          0x88960001
#define XAUDIO2_E_XMA_DECODER_ERROR     0x88960002
#define XAUDIO2_E_XAPO_CREATION_FAILED  0x88960003
#define XAUDIO2_E_DEVICE_INVALIDATED    0x88960004

#define XAPO_E_FORMAT_UNSUPPORTED MAKE_HRESULT(SEVERITY_ERROR, 0x897, 0x01)

#define DXUTERR_NODIRECT3D              MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x0901)
#define DXUTERR_NOCOMPATIBLEDEVICES     MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x0902)
#define DXUTERR_MEDIANOTFOUND           MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x0903)
#define DXUTERR_NONZEROREFCOUNT         MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x0904)
#define DXUTERR_CREATINGDEVICE          MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x0905)
#define DXUTERR_RESETTINGDEVICE         MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x0906)
#define DXUTERR_CREATINGDEVICEOBJECTS   MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x0907)
#define DXUTERR_RESETTINGDEVICEOBJECTS  MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x0908)
#define DXUTERR_INCORRECTVERSION        MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x0909)
#define DXUTERR_DEVICEREMOVED           MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x090A)

//-----------------------------------------------------------------------------
#define BUFFER_SIZE 3000

#pragma warning( disable : 6001 6221 )

//--------------------------------------------------------------------------------------
#define  CHK_ERR(hrchk, strOut) \
        case static_cast<HRESULT>(hrchk): \
             return L##strOut;

#define  CHK_ERRA(hrchk) \
        case static_cast<HRESULT>(hrchk): \
             return L## #hrchk;

#define HRESULT_FROM_WIN32b(x) ((HRESULT)(x) <= 0 ? ((HRESULT)(x)) : ((HRESULT) (((x) & 0x0000FFFF) | (FACILITY_WIN32 << 16) | 0x80000000)))

#define  CHK_ERR_WIN32A(hrchk) \
        case HRESULT_FROM_WIN32b(hrchk): \
        case hrchk: \
             return L## #hrchk;

#define  CHK_ERR_WIN32_ONLY(hrchk, strOut) \
        case HRESULT_FROM_WIN32b(hrchk): \
             return L##strOut;

//-----------------------------------------------------
const WCHAR* WINAPI DXGetErrorStringW( _In_ HRESULT hr )
{
   switch(hr)
   {
// Commmented out codes are actually alises for other codes

// -------------------------------------------------------------
// Common Win32 error codes
// -------------------------------------------------------------
        CHK_ERRA(S_OK)
        CHK_ERRA(S_FALSE)

        CHK_ERRA(E_UNEXPECTED)
        CHK_ERRA(E_NOTIMPL)
        CHK_ERRA(E_OUTOFMEMORY)
        CHK_ERRA(E_INVALIDARG)
        CHK_ERRA(E_NOINTERFACE)
        CHK_ERRA(E_POINTER)
        CHK_ERRA(E_HANDLE)
        CHK_ERRA(E_ABORT)
        CHK_ERRA(E_FAIL)
        CHK_ERRA(E_ACCESSDENIED)
        CHK_ERRA(E_PENDING)
        CHK_ERRA(CO_E_INIT_TLS)
        CHK_ERRA(CO_E_INIT_SHARED_ALLOCATOR)
        CHK_ERRA(CO_E_INIT_MEMORY_ALLOCATOR)
        CHK_ERRA(CO_E_INIT_CLASS_CACHE)
        CHK_ERRA(CO_E_INIT_RPC_CHANNEL)
        CHK_ERRA(CO_E_INIT_TLS_SET_CHANNEL_CONTROL)
        CHK_ERRA(CO_E_INIT_TLS_CHANNEL_CONTROL)
        CHK_ERRA(CO_E_INIT_UNACCEPTED_USER_ALLOCATOR)
        CHK_ERRA(CO_E_INIT_SCM_MUTEX_EXISTS)
        CHK_ERRA(CO_E_INIT_SCM_FILE_MAPPING_EXISTS)
        CHK_ERRA(CO_E_INIT_SCM_MAP_VIEW_OF_FILE)
        CHK_ERRA(CO_E_INIT_SCM_EXEC_FAILURE)
        CHK_ERRA(CO_E_INIT_ONLY_SINGLE_THREADED)
        CHK_ERRA(CO_E_CANT_REMOTE)
        CHK_ERRA(CO_E_BAD_SERVER_NAME)
        CHK_ERRA(CO_E_WRONG_SERVER_IDENTITY)
        CHK_ERRA(CO_E_OLE1DDE_DISABLED)
        CHK_ERRA(CO_E_RUNAS_SYNTAX)
        CHK_ERRA(CO_E_CREATEPROCESS_FAILURE)
        CHK_ERRA(CO_E_RUNAS_CREATEPROCESS_FAILURE)
        CHK_ERRA(CO_E_RUNAS_LOGON_FAILURE)
        CHK_ERRA(CO_E_LAUNCH_PERMSSION_DENIED)
        CHK_ERRA(CO_E_START_SERVICE_FAILURE)
        CHK_ERRA(CO_E_REMOTE_COMMUNICATION_FAILURE)
        CHK_ERRA(CO_E_SERVER_START_TIMEOUT)
        CHK_ERRA(CO_E_CLSREG_INCONSISTENT)
        CHK_ERRA(CO_E_IIDREG_INCONSISTENT)
        CHK_ERRA(CO_E_NOT_SUPPORTED)
        CHK_ERRA(CO_E_RELOAD_DLL)
        CHK_ERRA(CO_E_MSI_ERROR)
        CHK_ERRA(OLE_E_FIRST)
        CHK_ERRA(OLE_E_LAST)
        CHK_ERRA(OLE_S_FIRST)
        CHK_ERRA(OLE_S_LAST)
//        CHK_ERRA(OLE_E_OLEVERB)
        CHK_ERRA(OLE_E_ADVF)
        CHK_ERRA(OLE_E_ENUM_NOMORE)
        CHK_ERRA(OLE_E_ADVISENOTSUPPORTED)
        CHK_ERRA(OLE_E_NOCONNECTION)
        CHK_ERRA(OLE_E_NOTRUNNING)
        CHK_ERRA(OLE_E_NOCACHE)
        CHK_ERRA(OLE_E_BLANK)
        CHK_ERRA(OLE_E_CLASSDIFF)
        CHK_ERRA(OLE_E_CANT_GETMONIKER)
        CHK_ERRA(OLE_E_CANT_BINDTOSOURCE)
        CHK_ERRA(OLE_E_STATIC)
        CHK_ERRA(OLE_E_PROMPTSAVECANCELLED)
        CHK_ERRA(OLE_E_INVALIDRECT)
        CHK_ERRA(OLE_E_WRONGCOMPOBJ)
        CHK_ERRA(OLE_E_INVALIDHWND)
        CHK_ERRA(OLE_E_NOT_INPLACEACTIVE)
        CHK_ERRA(OLE_E_CANTCONVERT)
        CHK_ERRA(OLE_E_NOSTORAGE)
        CHK_ERRA(DV_E_FORMATETC)
        CHK_ERRA(DV_E_DVTARGETDEVICE)
        CHK_ERRA(DV_E_STGMEDIUM)
        CHK_ERRA(DV_E_STATDATA)
        CHK_ERRA(DV_E_LINDEX)
        CHK_ERRA(DV_E_TYMED)
        CHK_ERRA(DV_E_CLIPFORMAT)
        CHK_ERRA(DV_E_DVASPECT)
        CHK_ERRA(DV_E_DVTARGETDEVICE_SIZE)
        CHK_ERRA(DV_E_NOIVIEWOBJECT)
        CHK_ERRA(_HRESULT_TYPEDEF_(DRAGDROP_E_FIRST))
        CHK_ERRA(_HRESULT_TYPEDEF_(DRAGDROP_E_LAST))
        CHK_ERRA(_HRESULT_TYPEDEF_(DRAGDROP_S_FIRST))
        CHK_ERRA(_HRESULT_TYPEDEF_(DRAGDROP_S_LAST))
//        CHK_ERRA(DRAGDROP_E_NOTREGISTERED)
        CHK_ERRA(DRAGDROP_E_ALREADYREGISTERED)
        CHK_ERRA(DRAGDROP_E_INVALIDHWND)
        CHK_ERRA(_HRESULT_TYPEDEF_(CLASSFACTORY_E_FIRST))
        CHK_ERRA(_HRESULT_TYPEDEF_(CLASSFACTORY_E_LAST))
        CHK_ERRA(CLASSFACTORY_S_FIRST)
        CHK_ERRA(CLASSFACTORY_S_LAST)
//        CHK_ERRA(CLASS_E_NOAGGREGATION)
        CHK_ERRA(CLASS_E_CLASSNOTAVAILABLE)
        CHK_ERRA(CLASS_E_NOTLICENSED)
        CHK_ERRA(_HRESULT_TYPEDEF_(MARSHAL_E_FIRST))
        CHK_ERRA(_HRESULT_TYPEDEF_(MARSHAL_E_LAST))
        CHK_ERRA(MARSHAL_S_FIRST)
        CHK_ERRA(MARSHAL_S_LAST)
        CHK_ERRA(_HRESULT_TYPEDEF_(DATA_E_FIRST))
        CHK_ERRA(_HRESULT_TYPEDEF_(DATA_E_LAST))
        CHK_ERRA(DATA_S_FIRST)
        CHK_ERRA(DATA_S_LAST)
        CHK_ERRA(_HRESULT_TYPEDEF_(VIEW_E_FIRST))
        CHK_ERRA(_HRESULT_TYPEDEF_(VIEW_E_LAST))
        CHK_ERRA(VIEW_S_FIRST)
        CHK_ERRA(VIEW_S_LAST)
//        CHK_ERRA(VIEW_E_DRAW)
        CHK_ERRA(_HRESULT_TYPEDEF_(REGDB_E_FIRST))
        CHK_ERRA(_HRESULT_TYPEDEF_(REGDB_E_LAST))
        CHK_ERRA(REGDB_S_FIRST)
        CHK_ERRA(REGDB_S_LAST)
//        CHK_ERRA(REGDB_E_READREGDB)
        CHK_ERRA(REGDB_E_WRITEREGDB)
        CHK_ERRA(REGDB_E_KEYMISSING)
        CHK_ERRA(REGDB_E_INVALIDVALUE)
        CHK_ERRA(REGDB_E_CLASSNOTREG)
        CHK_ERRA(REGDB_E_IIDNOTREG)
        CHK_ERRA(_HRESULT_TYPEDEF_(CAT_E_FIRST))
        CHK_ERRA(_HRESULT_TYPEDEF_(CAT_E_LAST))
//        CHK_ERRA(CAT_E_CATIDNOEXIST)
//        CHK_ERRA(CAT_E_NODESCRIPTION)
        CHK_ERRA(_HRESULT_TYPEDEF_(CS_E_FIRST))
        CHK_ERRA(_HRESULT_TYPEDEF_(CS_E_LAST))
//        CHK_ERRA(CS_E_PACKAGE_NOTFOUND)
        CHK_ERRA(CS_E_NOT_DELETABLE)
        CHK_ERRA(CS_E_CLASS_NOTFOUND)
        CHK_ERRA(CS_E_INVALID_VERSION)
        CHK_ERRA(CS_E_NO_CLASSSTORE)
        CHK_ERRA(CS_E_OBJECT_NOTFOUND)
        CHK_ERRA(CS_E_OBJECT_ALREADY_EXISTS)
        CHK_ERRA(CS_E_INVALID_PATH)
        CHK_ERRA(CS_E_NETWORK_ERROR)
        CHK_ERRA(CS_E_ADMIN_LIMIT_EXCEEDED)
        CHK_ERRA(CS_E_SCHEMA_MISMATCH)
//        CHK_ERRA(CS_E_INTERNAL_ERROR)
        CHK_ERRA(_HRESULT_TYPEDEF_(CACHE_E_FIRST))
        CHK_ERRA(_HRESULT_TYPEDEF_(CACHE_E_LAST))
        CHK_ERRA(CACHE_S_FIRST)
        CHK_ERRA(CACHE_S_LAST)
//        CHK_ERRA(CACHE_E_NOCACHE_UPDATED)
        CHK_ERRA(_HRESULT_TYPEDEF_(OLEOBJ_E_FIRST))
        CHK_ERRA(_HRESULT_TYPEDEF_(OLEOBJ_E_LAST))
        CHK_ERRA(OLEOBJ_S_FIRST)
        CHK_ERRA(OLEOBJ_S_LAST)
//        CHK_ERRA(OLEOBJ_E_NOVERBS)
        CHK_ERRA(OLEOBJ_E_INVALIDVERB)
        CHK_ERRA(_HRESULT_TYPEDEF_(CLIENTSITE_E_FIRST))
        CHK_ERRA(_HRESULT_TYPEDEF_(CLIENTSITE_E_LAST))
        CHK_ERRA(CLIENTSITE_S_FIRST)
        CHK_ERRA(CLIENTSITE_S_LAST)
        CHK_ERRA(INPLACE_E_NOTUNDOABLE)
        CHK_ERRA(INPLACE_E_NOTOOLSPACE)
//        CHK_ERRA(INPLACE_E_FIRST)
        CHK_ERRA(_HRESULT_TYPEDEF_(INPLACE_E_LAST))
        CHK_ERRA(INPLACE_S_FIRST)
        CHK_ERRA(INPLACE_S_LAST)
        CHK_ERRA(_HRESULT_TYPEDEF_(ENUM_E_FIRST))
        CHK_ERRA(_HRESULT_TYPEDEF_(ENUM_E_LAST))
        CHK_ERRA(ENUM_S_FIRST)
        CHK_ERRA(ENUM_S_LAST)
        CHK_ERRA(_HRESULT_TYPEDEF_(CONVERT10_E_FIRST))
        CHK_ERRA(_HRESULT_TYPEDEF_(CONVERT10_E_LAST))
        CHK_ERRA(CONVERT10_S_FIRST)
        CHK_ERRA(CONVERT10_S_LAST)
//        CHK_ERRA(CONVERT10_E_OLESTREAM_GET)
        CHK_ERRA(CONVERT10_E_OLESTREAM_PUT)
        CHK_ERRA(CONVERT10_E_OLESTREAM_FMT)
        CHK_ERRA(CONVERT10_E_OLESTREAM_BITMAP_TO_DIB)
        CHK_ERRA(CONVERT10_E_STG_FMT)
        CHK_ERRA(CONVERT10_E_STG_NO_STD_STREAM)
        CHK_ERRA(CONVERT10_E_STG_DIB_TO_BITMAP)
        CHK_ERRA(_HRESULT_TYPEDEF_(CLIPBRD_E_FIRST))
        CHK_ERRA(_HRESULT_TYPEDEF_(CLIPBRD_E_LAST))
        CHK_ERRA(CLIPBRD_S_FIRST)
        CHK_ERRA(CLIPBRD_S_LAST)
//        CHK_ERRA(CLIPBRD_E_CANT_OPEN)
        CHK_ERRA(CLIPBRD_E_CANT_EMPTY)
        CHK_ERRA(CLIPBRD_E_CANT_SET)
        CHK_ERRA(CLIPBRD_E_BAD_DATA)
        CHK_ERRA(CLIPBRD_E_CANT_CLOSE)
        CHK_ERRA(_HRESULT_TYPEDEF_(MK_E_FIRST))
        CHK_ERRA(_HRESULT_TYPEDEF_(MK_E_LAST))
        CHK_ERRA(MK_S_FIRST)
        CHK_ERRA(MK_S_LAST)
//        CHK_ERRA(MK_E_CONNECTMANUALLY)
        CHK_ERRA(MK_E_EXCEEDEDDEADLINE)
        CHK_ERRA(MK_E_NEEDGENERIC)
        CHK_ERRA(MK_E_UNAVAILABLE)
        CHK_ERRA(MK_E_SYNTAX)
        CHK_ERRA(MK_E_NOOBJECT)
        CHK_ERRA(MK_E_INVALIDEXTENSION)
        CHK_ERRA(MK_E_INTERMEDIATEINTERFACENOTSUPPORTED)
        CHK_ERRA(MK_E_NOTBINDABLE)
        CHK_ERRA(MK_E_NOTBOUND)
        CHK_ERRA(MK_E_CANTOPENFILE)
        CHK_ERRA(MK_E_MUSTBOTHERUSER)
        CHK_ERRA(MK_E_NOINVERSE)
        CHK_ERRA(MK_E_NOSTORAGE)
        CHK_ERRA(MK_E_NOPREFIX)
//        CHK_ERRA(MK_E_ENUMERATION_FAILED)
        CHK_ERRA(CO_E_NOTINITIALIZED)
        CHK_ERRA(CO_E_ALREADYINITIALIZED)
        CHK_ERRA(CO_E_CANTDETERMINECLASS)
        CHK_ERRA(CO_E_CLASSSTRING)
        CHK_ERRA(CO_E_IIDSTRING)
        CHK_ERRA(CO_E_APPNOTFOUND)
        CHK_ERRA(CO_E_APPSINGLEUSE)
        CHK_ERRA(CO_E_ERRORINAPP)
        CHK_ERRA(CO_E_DLLNOTFOUND)
        CHK_ERRA(CO_E_ERRORINDLL)
        CHK_ERRA(CO_E_WRONGOSFORAPP)
        CHK_ERRA(CO_E_OBJNOTREG)
        CHK_ERRA(CO_E_OBJISREG)
        CHK_ERRA(CO_E_OBJNOTCONNECTED)
        CHK_ERRA(CO_E_APPDIDNTREG)
        CHK_ERRA(CO_E_RELEASED)
//        CHK_ERRA(OLE_S_USEREG)
        CHK_ERRA(OLE_S_STATIC)
        CHK_ERRA(OLE_S_MAC_CLIPFORMAT)
//        CHK_ERRA(DRAGDROP_S_DROP)
        CHK_ERRA(DRAGDROP_S_CANCEL)
        CHK_ERRA(DRAGDROP_S_USEDEFAULTCURSORS)
//        CHK_ERRA(DATA_S_SAMEFORMATETC)
//        CHK_ERRA(VIEW_S_ALREADY_FROZEN)
//        CHK_ERRA(CACHE_S_FORMATETC_NOTSUPPORTED)
        CHK_ERRA(CACHE_S_SAMECACHE)
        CHK_ERRA(CACHE_S_SOMECACHES_NOTUPDATED)
//        CHK_ERRA(OLEOBJ_S_INVALIDVERB)
        CHK_ERRA(OLEOBJ_S_CANNOT_DOVERB_NOW)
        CHK_ERRA(OLEOBJ_S_INVALIDHWND)
//        CHK_ERRA(INPLACE_S_TRUNCATED)
//        CHK_ERRA(CONVERT10_S_NO_PRESENTATION)
        CHK_ERRA(MK_S_REDUCED_TO_SELF)
        CHK_ERRA(MK_S_ME)
        CHK_ERRA(MK_S_HIM)
        CHK_ERRA(MK_S_US)
        CHK_ERRA(MK_S_MONIKERALREADYREGISTERED)
        CHK_ERRA(CO_E_CLASS_CREATE_FAILED)
        CHK_ERRA(CO_E_SCM_ERROR)
        CHK_ERRA(CO_E_SCM_RPC_FAILURE)
        CHK_ERRA(CO_E_BAD_PATH)
        CHK_ERRA(CO_E_SERVER_EXEC_FAILURE)
        CHK_ERRA(CO_E_OBJSRV_RPC_FAILURE)
        CHK_ERRA(MK_E_NO_NORMALIZED)
        CHK_ERRA(CO_E_SERVER_STOPPING)
        CHK_ERRA(MEM_E_INVALID_ROOT)
        CHK_ERRA(MEM_E_INVALID_LINK)
        CHK_ERRA(MEM_E_INVALID_SIZE)
        CHK_ERRA(CO_S_NOTALLINTERFACES)
        CHK_ERRA(DISP_E_UNKNOWNINTERFACE)
        CHK_ERRA(DISP_E_MEMBERNOTFOUND)
        CHK_ERRA(DISP_E_PARAMNOTFOUND)
        CHK_ERRA(DISP_E_TYPEMISMATCH)
        CHK_ERRA(DISP_E_UNKNOWNNAME)
        CHK_ERRA(DISP_E_NONAMEDARGS)
        CHK_ERRA(DISP_E_BADVARTYPE)
        CHK_ERRA(DISP_E_EXCEPTION)
        CHK_ERRA(DISP_E_OVERFLOW)
        CHK_ERRA(DISP_E_BADINDEX)
        CHK_ERRA(DISP_E_UNKNOWNLCID)
        CHK_ERRA(DISP_E_ARRAYISLOCKED)
        CHK_ERRA(DISP_E_BADPARAMCOUNT)
        CHK_ERRA(DISP_E_PARAMNOTOPTIONAL)
        CHK_ERRA(DISP_E_BADCALLEE)
        CHK_ERRA(DISP_E_NOTACOLLECTION)
        CHK_ERRA(DISP_E_DIVBYZERO)
        CHK_ERRA(DISP_E_BUFFERTOOSMALL)
        CHK_ERRA(TYPE_E_BUFFERTOOSMALL)
        CHK_ERRA(TYPE_E_FIELDNOTFOUND)
        CHK_ERRA(TYPE_E_INVDATAREAD)
        CHK_ERRA(TYPE_E_UNSUPFORMAT)
        CHK_ERRA(TYPE_E_REGISTRYACCESS)
        CHK_ERRA(TYPE_E_LIBNOTREGISTERED)
        CHK_ERRA(TYPE_E_UNDEFINEDTYPE)
        CHK_ERRA(TYPE_E_QUALIFIEDNAMEDISALLOWED)
        CHK_ERRA(TYPE_E_INVALIDSTATE)
        CHK_ERRA(TYPE_E_WRONGTYPEKIND)
        CHK_ERRA(TYPE_E_ELEMENTNOTFOUND)
        CHK_ERRA(TYPE_E_AMBIGUOUSNAME)
        CHK_ERRA(TYPE_E_NAMECONFLICT)
        CHK_ERRA(TYPE_E_UNKNOWNLCID)
        CHK_ERRA(TYPE_E_DLLFUNCTIONNOTFOUND)
        CHK_ERRA(TYPE_E_BADMODULEKIND)
        CHK_ERRA(TYPE_E_SIZETOOBIG)
        CHK_ERRA(TYPE_E_DUPLICATEID)
        CHK_ERRA(TYPE_E_INVALIDID)
        CHK_ERRA(TYPE_E_TYPEMISMATCH)
        CHK_ERRA(TYPE_E_OUTOFBOUNDS)
        CHK_ERRA(TYPE_E_IOERROR)
        CHK_ERRA(TYPE_E_CANTCREATETMPFILE)
        CHK_ERRA(TYPE_E_CANTLOADLIBRARY)
        CHK_ERRA(TYPE_E_INCONSISTENTPROPFUNCS)
        CHK_ERRA(TYPE_E_CIRCULARTYPE)
        CHK_ERRA(STG_E_INVALIDFUNCTION)
        CHK_ERRA(STG_E_FILENOTFOUND)
        CHK_ERRA(STG_E_PATHNOTFOUND)
        CHK_ERRA(STG_E_TOOMANYOPENFILES)
        CHK_ERRA(STG_E_ACCESSDENIED)
        CHK_ERRA(STG_E_INVALIDHANDLE)
        CHK_ERRA(STG_E_INSUFFICIENTMEMORY)
        CHK_ERRA(STG_E_INVALIDPOINTER)
        CHK_ERRA(STG_E_NOMOREFILES)
        CHK_ERRA(STG_E_DISKISWRITEPROTECTED)
        CHK_ERRA(STG_E_SEEKERROR)
        CHK_ERRA(STG_E_WRITEFAULT)
        CHK_ERRA(STG_E_READFAULT)
        CHK_ERRA(STG_E_SHAREVIOLATION)
        CHK_ERRA(STG_E_LOCKVIOLATION)
        CHK_ERRA(STG_E_FILEALREADYEXISTS)
        CHK_ERRA(STG_E_INVALIDPARAMETER)
        CHK_ERRA(STG_E_MEDIUMFULL)
        CHK_ERRA(STG_E_PROPSETMISMATCHED)
        CHK_ERRA(STG_E_ABNORMALAPIEXIT)
        CHK_ERRA(STG_E_INVALIDHEADER)
        CHK_ERRA(STG_E_INVALIDNAME)
        CHK_ERRA(STG_E_UNKNOWN)
        CHK_ERRA(STG_E_UNIMPLEMENTEDFUNCTION)
        CHK_ERRA(STG_E_INVALIDFLAG)
        CHK_ERRA(STG_E_INUSE)
        CHK_ERRA(STG_E_NOTCURRENT)
        CHK_ERRA(STG_E_REVERTED)
        CHK_ERRA(STG_E_CANTSAVE)
        CHK_ERRA(STG_E_OLDFORMAT)
        CHK_ERRA(STG_E_OLDDLL)
        CHK_ERRA(STG_E_SHAREREQUIRED)
        CHK_ERRA(STG_E_NOTFILEBASEDSTORAGE)
        CHK_ERRA(STG_E_EXTANTMARSHALLINGS)
        CHK_ERRA(STG_E_DOCFILECORRUPT)
        CHK_ERRA(STG_E_BADBASEADDRESS)
        CHK_ERRA(STG_E_DOCFILETOOLARGE)
        CHK_ERRA(STG_E_NOTSIMPLEFORMAT)
        CHK_ERRA(STG_E_INCOMPLETE)
        CHK_ERRA(STG_E_TERMINATED)
        CHK_ERRA(STG_S_CONVERTED)
        CHK_ERRA(STG_S_BLOCK)
        CHK_ERRA(STG_S_RETRYNOW)
        CHK_ERRA(STG_S_MONITORING)
        CHK_ERRA(STG_S_MULTIPLEOPENS)
        CHK_ERRA(STG_S_CONSOLIDATIONFAILED)
        CHK_ERRA(STG_S_CANNOTCONSOLIDATE)
        CHK_ERRA(RPC_E_CALL_REJECTED)
        CHK_ERRA(RPC_E_CALL_CANCELED)
        CHK_ERRA(RPC_E_CANTPOST_INSENDCALL)
        CHK_ERRA(RPC_E_CANTCALLOUT_INASYNCCALL)
        CHK_ERRA(RPC_E_CANTCALLOUT_INEXTERNALCALL)
        CHK_ERRA(RPC_E_CONNECTION_TERMINATED)
        CHK_ERRA(RPC_E_SERVER_DIED)
        CHK_ERRA(RPC_E_CLIENT_DIED)
        CHK_ERRA(RPC_E_INVALID_DATAPACKET)
        CHK_ERRA(RPC_E_CANTTRANSMIT_CALL)
        CHK_ERRA(RPC_E_CLIENT_CANTMARSHAL_DATA)
        CHK_ERRA(RPC_E_CLIENT_CANTUNMARSHAL_DATA)
        CHK_ERRA(RPC_E_SERVER_CANTMARSHAL_DATA)
        CHK_ERRA(RPC_E_SERVER_CANTUNMARSHAL_DATA)
        CHK_ERRA(RPC_E_INVALID_DATA)
        CHK_ERRA(RPC_E_INVALID_PARAMETER)
        CHK_ERRA(RPC_E_CANTCALLOUT_AGAIN)
        CHK_ERRA(RPC_E_SERVER_DIED_DNE)
        CHK_ERRA(RPC_E_SYS_CALL_FAILED)
        CHK_ERRA(RPC_E_OUT_OF_RESOURCES)
        CHK_ERRA(RPC_E_ATTEMPTED_MULTITHREAD)
        CHK_ERRA(RPC_E_NOT_REGISTERED)
        CHK_ERRA(RPC_E_FAULT)
        CHK_ERRA(RPC_E_SERVERFAULT)
        CHK_ERRA(RPC_E_CHANGED_MODE)
        CHK_ERRA(RPC_E_INVALIDMETHOD)
        CHK_ERRA(RPC_E_DISCONNECTED)
        CHK_ERRA(RPC_E_RETRY)
        CHK_ERRA(RPC_E_SERVERCALL_RETRYLATER)
        CHK_ERRA(RPC_E_SERVERCALL_REJECTED)
        CHK_ERRA(RPC_E_INVALID_CALLDATA)
        CHK_ERRA(RPC_E_CANTCALLOUT_ININPUTSYNCCALL)
        CHK_ERRA(RPC_E_WRONG_THREAD)
        CHK_ERRA(RPC_E_THREAD_NOT_INIT)
        CHK_ERRA(RPC_E_VERSION_MISMATCH)
        CHK_ERRA(RPC_E_INVALID_HEADER)
        CHK_ERRA(RPC_E_INVALID_EXTENSION)
        CHK_ERRA(RPC_E_INVALID_IPID)
        CHK_ERRA(RPC_E_INVALID_OBJECT)
        CHK_ERRA(RPC_S_CALLPENDING)
        CHK_ERRA(RPC_S_WAITONTIMER)
        CHK_ERRA(RPC_E_CALL_COMPLETE)
        CHK_ERRA(RPC_E_UNSECURE_CALL)
        CHK_ERRA(RPC_E_TOO_LATE)
        CHK_ERRA(RPC_E_NO_GOOD_SECURITY_PACKAGES)
        CHK_ERRA(RPC_E_ACCESS_DENIED)
        CHK_ERRA(RPC_E_REMOTE_DISABLED)
        CHK_ERRA(RPC_E_INVALID_OBJREF)
        CHK_ERRA(RPC_E_NO_CONTEXT)
        CHK_ERRA(RPC_E_TIMEOUT)
        CHK_ERRA(RPC_E_NO_SYNC)
        CHK_ERRA(RPC_E_FULLSIC_REQUIRED)
        CHK_ERRA(RPC_E_INVALID_STD_NAME)
        CHK_ERRA(CO_E_FAILEDTOIMPERSONATE)
        CHK_ERRA(CO_E_FAILEDTOGETSECCTX)
        CHK_ERRA(CO_E_FAILEDTOOPENTHREADTOKEN)
        CHK_ERRA(CO_E_FAILEDTOGETTOKENINFO)
        CHK_ERRA(CO_E_TRUSTEEDOESNTMATCHCLIENT)
        CHK_ERRA(CO_E_FAILEDTOQUERYCLIENTBLANKET)
        CHK_ERRA(CO_E_FAILEDTOSETDACL)
        CHK_ERRA(CO_E_ACCESSCHECKFAILED)
        CHK_ERRA(CO_E_NETACCESSAPIFAILED)
        CHK_ERRA(CO_E_WRONGTRUSTEENAMESYNTAX)
        CHK_ERRA(CO_E_INVALIDSID)
        CHK_ERRA(CO_E_CONVERSIONFAILED)
        CHK_ERRA(CO_E_NOMATCHINGSIDFOUND)
        CHK_ERRA(CO_E_LOOKUPACCSIDFAILED)
        CHK_ERRA(CO_E_NOMATCHINGNAMEFOUND)
        CHK_ERRA(CO_E_LOOKUPACCNAMEFAILED)
        CHK_ERRA(CO_E_SETSERLHNDLFAILED)
        CHK_ERRA(CO_E_FAILEDTOGETWINDIR)
        CHK_ERRA(CO_E_PATHTOOLONG)
        CHK_ERRA(CO_E_FAILEDTOGENUUID)
        CHK_ERRA(CO_E_FAILEDTOCREATEFILE)
        CHK_ERRA(CO_E_FAILEDTOCLOSEHANDLE)
        CHK_ERRA(CO_E_EXCEEDSYSACLLIMIT)
        CHK_ERRA(CO_E_ACESINWRONGORDER)
        CHK_ERRA(CO_E_INCOMPATIBLESTREAMVERSION)
        CHK_ERRA(CO_E_FAILEDTOOPENPROCESSTOKEN)
        CHK_ERRA(CO_E_DECODEFAILED)
        CHK_ERRA(CO_E_ACNOTINITIALIZED)
        CHK_ERRA(RPC_E_UNEXPECTED)
        CHK_ERRA(NTE_BAD_UID)
        CHK_ERRA(NTE_BAD_HASH)
        CHK_ERRA(NTE_BAD_KEY)
        CHK_ERRA(NTE_BAD_LEN)
        CHK_ERRA(NTE_BAD_DATA)
        CHK_ERRA(NTE_BAD_SIGNATURE)
        CHK_ERRA(NTE_BAD_VER)
        CHK_ERRA(NTE_BAD_ALGID)
        CHK_ERRA(NTE_BAD_FLAGS)
        CHK_ERRA(NTE_BAD_TYPE)
        CHK_ERRA(NTE_BAD_KEY_STATE)
        CHK_ERRA(NTE_BAD_HASH_STATE)
        CHK_ERRA(NTE_NO_KEY)
        CHK_ERRA(NTE_NO_MEMORY)
        CHK_ERRA(NTE_EXISTS)
        CHK_ERRA(NTE_PERM)
        CHK_ERRA(NTE_NOT_FOUND)
        CHK_ERRA(NTE_DOUBLE_ENCRYPT)
        CHK_ERRA(NTE_BAD_PROVIDER)
        CHK_ERRA(NTE_BAD_PROV_TYPE)
        CHK_ERRA(NTE_BAD_PUBLIC_KEY)
        CHK_ERRA(NTE_BAD_KEYSET)
        CHK_ERRA(NTE_PROV_TYPE_NOT_DEF)
        CHK_ERRA(NTE_PROV_TYPE_ENTRY_BAD)
        CHK_ERRA(NTE_KEYSET_NOT_DEF)
        CHK_ERRA(NTE_KEYSET_ENTRY_BAD)
        CHK_ERRA(NTE_PROV_TYPE_NO_MATCH)
        CHK_ERRA(NTE_SIGNATURE_FILE_BAD)
        CHK_ERRA(NTE_PROVIDER_DLL_FAIL)
        CHK_ERRA(NTE_PROV_DLL_NOT_FOUND)
        CHK_ERRA(NTE_BAD_KEYSET_PARAM)
        CHK_ERRA(NTE_FAIL)
        CHK_ERRA(NTE_SYS_ERR)
        CHK_ERRA(NTE_SILENT_CONTEXT)
        CHK_ERRA(NTE_TOKEN_KEYSET_STORAGE_FULL)
        CHK_ERRA(CRYPT_E_MSG_ERROR)
        CHK_ERRA(CRYPT_E_UNKNOWN_ALGO)
        CHK_ERRA(CRYPT_E_OID_FORMAT)
        CHK_ERRA(CRYPT_E_INVALID_MSG_TYPE)
        CHK_ERRA(CRYPT_E_UNEXPECTED_ENCODING)
        CHK_ERRA(CRYPT_E_AUTH_ATTR_MISSING)
        CHK_ERRA(CRYPT_E_HASH_VALUE)
        CHK_ERRA(CRYPT_E_INVALID_INDEX)
        CHK_ERRA(CRYPT_E_ALREADY_DECRYPTED)
        CHK_ERRA(CRYPT_E_NOT_DECRYPTED)
        CHK_ERRA(CRYPT_E_RECIPIENT_NOT_FOUND)
        CHK_ERRA(CRYPT_E_CONTROL_TYPE)
        CHK_ERRA(CRYPT_E_ISSUER_SERIALNUMBER)
        CHK_ERRA(CRYPT_E_SIGNER_NOT_FOUND)
        CHK_ERRA(CRYPT_E_ATTRIBUTES_MISSING)
        CHK_ERRA(CRYPT_E_STREAM_MSG_NOT_READY)
        CHK_ERRA(CRYPT_E_STREAM_INSUFFICIENT_DATA)
        CHK_ERRA(CRYPT_E_BAD_LEN)
        CHK_ERRA(CRYPT_E_BAD_ENCODE)
        CHK_ERRA(CRYPT_E_FILE_ERROR)
        CHK_ERRA(CRYPT_E_NOT_FOUND)
        CHK_ERRA(CRYPT_E_EXISTS)
        CHK_ERRA(CRYPT_E_NO_PROVIDER)
        CHK_ERRA(CRYPT_E_SELF_SIGNED)
        CHK_ERRA(CRYPT_E_DELETED_PREV)
        CHK_ERRA(CRYPT_E_NO_MATCH)
        CHK_ERRA(CRYPT_E_UNEXPECTED_MSG_TYPE)
        CHK_ERRA(CRYPT_E_NO_KEY_PROPERTY)
        CHK_ERRA(CRYPT_E_NO_DECRYPT_CERT)
        CHK_ERRA(CRYPT_E_BAD_MSG)
        CHK_ERRA(CRYPT_E_NO_SIGNER)
        CHK_ERRA(CRYPT_E_PENDING_CLOSE)
        CHK_ERRA(CRYPT_E_REVOKED)
        CHK_ERRA(CRYPT_E_NO_REVOCATION_DLL)
        CHK_ERRA(CRYPT_E_NO_REVOCATION_CHECK)
        CHK_ERRA(CRYPT_E_REVOCATION_OFFLINE)
        CHK_ERRA(CRYPT_E_NOT_IN_REVOCATION_DATABASE)
        CHK_ERRA(CRYPT_E_INVALID_NUMERIC_STRING)
        CHK_ERRA(CRYPT_E_INVALID_PRINTABLE_STRING)
        CHK_ERRA(CRYPT_E_INVALID_IA5_STRING)
        CHK_ERRA(CRYPT_E_INVALID_X500_STRING)
        CHK_ERRA(CRYPT_E_NOT_CHAR_STRING)
        CHK_ERRA(CRYPT_E_FILERESIZED)
        CHK_ERRA(CRYPT_E_SECURITY_SETTINGS)
        CHK_ERRA(CRYPT_E_NO_VERIFY_USAGE_DLL)
        CHK_ERRA(CRYPT_E_NO_VERIFY_USAGE_CHECK)
        CHK_ERRA(CRYPT_E_VERIFY_USAGE_OFFLINE)
        CHK_ERRA(CRYPT_E_NOT_IN_CTL)
        CHK_ERRA(CRYPT_E_NO_TRUSTED_SIGNER)
        CHK_ERRA(CRYPT_E_OSS_ERROR)
        CHK_ERRA(OSS_MORE_BUF)
        CHK_ERRA(OSS_NEGATIVE_UINTEGER)
        CHK_ERRA(OSS_PDU_RANGE)
        CHK_ERRA(OSS_MORE_INPUT)
        CHK_ERRA(OSS_DATA_ERROR)
        CHK_ERRA(OSS_BAD_ARG)
        CHK_ERRA(OSS_BAD_VERSION)
        CHK_ERRA(OSS_OUT_MEMORY)
        CHK_ERRA(OSS_PDU_MISMATCH)
        CHK_ERRA(OSS_LIMITED)
        CHK_ERRA(OSS_BAD_PTR)
        CHK_ERRA(OSS_BAD_TIME)
        CHK_ERRA(OSS_INDEFINITE_NOT_SUPPORTED)
        CHK_ERRA(OSS_MEM_ERROR)
        CHK_ERRA(OSS_BAD_TABLE)
        CHK_ERRA(OSS_TOO_LONG)
        CHK_ERRA(OSS_CONSTRAINT_VIOLATED)
        CHK_ERRA(OSS_FATAL_ERROR)
        CHK_ERRA(OSS_ACCESS_SERIALIZATION_ERROR)
        CHK_ERRA(OSS_NULL_TBL)
        CHK_ERRA(OSS_NULL_FCN)
        CHK_ERRA(OSS_BAD_ENCRULES)
        CHK_ERRA(OSS_UNAVAIL_ENCRULES)
        CHK_ERRA(OSS_CANT_OPEN_TRACE_WINDOW)
        CHK_ERRA(OSS_UNIMPLEMENTED)
        CHK_ERRA(OSS_OID_DLL_NOT_LINKED)
        CHK_ERRA(OSS_CANT_OPEN_TRACE_FILE)
        CHK_ERRA(OSS_TRACE_FILE_ALREADY_OPEN)
        CHK_ERRA(OSS_TABLE_MISMATCH)
        CHK_ERRA(OSS_TYPE_NOT_SUPPORTED)
        CHK_ERRA(OSS_REAL_DLL_NOT_LINKED)
        CHK_ERRA(OSS_REAL_CODE_NOT_LINKED)
        CHK_ERRA(OSS_OUT_OF_RANGE)
        CHK_ERRA(OSS_COPIER_DLL_NOT_LINKED)
        CHK_ERRA(OSS_CONSTRAINT_DLL_NOT_LINKED)
        CHK_ERRA(OSS_COMPARATOR_DLL_NOT_LINKED)
        CHK_ERRA(OSS_COMPARATOR_CODE_NOT_LINKED)
        CHK_ERRA(OSS_MEM_MGR_DLL_NOT_LINKED)
        CHK_ERRA(OSS_PDV_DLL_NOT_LINKED)
        CHK_ERRA(OSS_PDV_CODE_NOT_LINKED)
        CHK_ERRA(OSS_API_DLL_NOT_LINKED)
        CHK_ERRA(OSS_BERDER_DLL_NOT_LINKED)
        CHK_ERRA(OSS_PER_DLL_NOT_LINKED)
        CHK_ERRA(OSS_OPEN_TYPE_ERROR)
        CHK_ERRA(OSS_MUTEX_NOT_CREATED)
        CHK_ERRA(OSS_CANT_CLOSE_TRACE_FILE)
        CHK_ERRA(CERTSRV_E_BAD_REQUESTSUBJECT)
        CHK_ERRA(CERTSRV_E_NO_REQUEST)
        CHK_ERRA(CERTSRV_E_BAD_REQUESTSTATUS)
        CHK_ERRA(CERTSRV_E_PROPERTY_EMPTY)
        CHK_ERRA(CERTSRV_E_INVALID_CA_CERTIFICATE)
        CHK_ERRA(CERTSRV_E_UNSUPPORTED_CERT_TYPE)
        CHK_ERRA(CERTSRV_E_NO_CERT_TYPE)
        CHK_ERRA(TRUST_E_SYSTEM_ERROR)
        CHK_ERRA(TRUST_E_NO_SIGNER_CERT)
        CHK_ERRA(TRUST_E_COUNTER_SIGNER)
        CHK_ERRA(TRUST_E_CERT_SIGNATURE)
        CHK_ERRA(TRUST_E_TIME_STAMP)
        CHK_ERRA(TRUST_E_BAD_DIGEST)
        CHK_ERRA(TRUST_E_BASIC_CONSTRAINTS)
        CHK_ERRA(TRUST_E_FINANCIAL_CRITERIA)
//        CHK_ERRA(NTE_OP_OK)
        CHK_ERRA(TRUST_E_PROVIDER_UNKNOWN)
        CHK_ERRA(TRUST_E_ACTION_UNKNOWN)
        CHK_ERRA(TRUST_E_SUBJECT_FORM_UNKNOWN)
        CHK_ERRA(TRUST_E_SUBJECT_NOT_TRUSTED)
        CHK_ERRA(DIGSIG_E_ENCODE)
        CHK_ERRA(DIGSIG_E_DECODE)
        CHK_ERRA(DIGSIG_E_EXTENSIBILITY)
        CHK_ERRA(DIGSIG_E_CRYPTO)
        CHK_ERRA(PERSIST_E_SIZEDEFINITE)
        CHK_ERRA(PERSIST_E_SIZEINDEFINITE)
        CHK_ERRA(PERSIST_E_NOTSELFSIZING)
        CHK_ERRA(TRUST_E_NOSIGNATURE)
        CHK_ERRA(CERT_E_EXPIRED)
        CHK_ERRA(CERT_E_VALIDITYPERIODNESTING)
        CHK_ERRA(CERT_E_ROLE)
        CHK_ERRA(CERT_E_PATHLENCONST)
        CHK_ERRA(CERT_E_CRITICAL)
        CHK_ERRA(CERT_E_PURPOSE)
        CHK_ERRA(CERT_E_ISSUERCHAINING)
        CHK_ERRA(CERT_E_MALFORMED)
        CHK_ERRA(CERT_E_UNTRUSTEDROOT)
        CHK_ERRA(CERT_E_CHAINING)
        CHK_ERRA(TRUST_E_FAIL)
        CHK_ERRA(CERT_E_REVOKED)
        CHK_ERRA(CERT_E_UNTRUSTEDTESTROOT)
        CHK_ERRA(CERT_E_REVOCATION_FAILURE)
        CHK_ERRA(CERT_E_CN_NO_MATCH)
        CHK_ERRA(CERT_E_WRONG_USAGE)
        CHK_ERRA(SPAPI_E_EXPECTED_SECTION_NAME)
        CHK_ERRA(SPAPI_E_BAD_SECTION_NAME_LINE)
        CHK_ERRA(SPAPI_E_SECTION_NAME_TOO_LONG)
        CHK_ERRA(SPAPI_E_GENERAL_SYNTAX)
        CHK_ERRA(SPAPI_E_WRONG_INF_STYLE)
        CHK_ERRA(SPAPI_E_SECTION_NOT_FOUND)
        CHK_ERRA(SPAPI_E_LINE_NOT_FOUND)
        CHK_ERRA(SPAPI_E_NO_BACKUP)
        CHK_ERRA(SPAPI_E_NO_ASSOCIATED_CLASS)
        CHK_ERRA(SPAPI_E_CLASS_MISMATCH)
        CHK_ERRA(SPAPI_E_DUPLICATE_FOUND)
        CHK_ERRA(SPAPI_E_NO_DRIVER_SELECTED)
        CHK_ERRA(SPAPI_E_KEY_DOES_NOT_EXIST)
        CHK_ERRA(SPAPI_E_INVALID_DEVINST_NAME)
        CHK_ERRA(SPAPI_E_INVALID_CLASS)
        CHK_ERRA(SPAPI_E_DEVINST_ALREADY_EXISTS)
        CHK_ERRA(SPAPI_E_DEVINFO_NOT_REGISTERED)
        CHK_ERRA(SPAPI_E_INVALID_REG_PROPERTY)
        CHK_ERRA(SPAPI_E_NO_INF)
        CHK_ERRA(SPAPI_E_NO_SUCH_DEVINST)
        CHK_ERRA(SPAPI_E_CANT_LOAD_CLASS_ICON)
        CHK_ERRA(SPAPI_E_INVALID_CLASS_INSTALLER)
        CHK_ERRA(SPAPI_E_DI_DO_DEFAULT)
        CHK_ERRA(SPAPI_E_DI_NOFILECOPY)
        CHK_ERRA(SPAPI_E_INVALID_HWPROFILE)
        CHK_ERRA(SPAPI_E_NO_DEVICE_SELECTED)
        CHK_ERRA(SPAPI_E_DEVINFO_LIST_LOCKED)
        CHK_ERRA(SPAPI_E_DEVINFO_DATA_LOCKED)
        CHK_ERRA(SPAPI_E_DI_BAD_PATH)
        CHK_ERRA(SPAPI_E_NO_CLASSINSTALL_PARAMS)
        CHK_ERRA(SPAPI_E_FILEQUEUE_LOCKED)
        CHK_ERRA(SPAPI_E_BAD_SERVICE_INSTALLSECT)
        CHK_ERRA(SPAPI_E_NO_CLASS_DRIVER_LIST)
        CHK_ERRA(SPAPI_E_NO_ASSOCIATED_SERVICE)
        CHK_ERRA(SPAPI_E_NO_DEFAULT_DEVICE_INTERFACE)
        CHK_ERRA(SPAPI_E_DEVICE_INTERFACE_ACTIVE)
        CHK_ERRA(SPAPI_E_DEVICE_INTERFACE_REMOVED)
        CHK_ERRA(SPAPI_E_BAD_INTERFACE_INSTALLSECT)
        CHK_ERRA(SPAPI_E_NO_SUCH_INTERFACE_CLASS)
        CHK_ERRA(SPAPI_E_INVALID_REFERENCE_STRING)
        CHK_ERRA(SPAPI_E_INVALID_MACHINENAME)
        CHK_ERRA(SPAPI_E_REMOTE_COMM_FAILURE)
        CHK_ERRA(SPAPI_E_MACHINE_UNAVAILABLE)
        CHK_ERRA(SPAPI_E_NO_CONFIGMGR_SERVICES)
        CHK_ERRA(SPAPI_E_INVALID_PROPPAGE_PROVIDER)
        CHK_ERRA(SPAPI_E_NO_SUCH_DEVICE_INTERFACE)
        CHK_ERRA(SPAPI_E_DI_POSTPROCESSING_REQUIRED)
        CHK_ERRA(SPAPI_E_INVALID_COINSTALLER)
        CHK_ERRA(SPAPI_E_NO_COMPAT_DRIVERS)
        CHK_ERRA(SPAPI_E_NO_DEVICE_ICON)
        CHK_ERRA(SPAPI_E_INVALID_INF_LOGCONFIG)
        CHK_ERRA(SPAPI_E_DI_DONT_INSTALL)
        CHK_ERRA(SPAPI_E_INVALID_FILTER_DRIVER)
        CHK_ERRA(SPAPI_E_NON_WINDOWS_NT_DRIVER)
        CHK_ERRA(SPAPI_E_NON_WINDOWS_DRIVER)
        CHK_ERRA(SPAPI_E_NO_CATALOG_FOR_OEM_INF)
        CHK_ERRA(SPAPI_E_DEVINSTALL_QUEUE_NONNATIVE)
        CHK_ERRA(SPAPI_E_ERROR_NOT_INSTALLED)
//        CHK_ERRA(SCARD_S_SUCCESS)
        CHK_ERRA(SCARD_F_INTERNAL_ERROR)
        CHK_ERRA(SCARD_E_CANCELLED)
        CHK_ERRA(SCARD_E_INVALID_HANDLE)
        CHK_ERRA(SCARD_E_INVALID_PARAMETER)
        CHK_ERRA(SCARD_E_INVALID_TARGET)
        CHK_ERRA(SCARD_E_NO_MEMORY)
        CHK_ERRA(SCARD_F_WAITED_TOO_LONG)
        CHK_ERRA(SCARD_E_INSUFFICIENT_BUFFER)
        CHK_ERRA(SCARD_E_UNKNOWN_READER)
        CHK_ERRA(SCARD_E_TIMEOUT)
        CHK_ERRA(SCARD_E_SHARING_VIOLATION)
        CHK_ERRA(SCARD_E_NO_SMARTCARD)
        CHK_ERRA(SCARD_E_UNKNOWN_CARD)
        CHK_ERRA(SCARD_E_CANT_DISPOSE)
        CHK_ERRA(SCARD_E_PROTO_MISMATCH)
        CHK_ERRA(SCARD_E_NOT_READY)
        CHK_ERRA(SCARD_E_INVALID_VALUE)
        CHK_ERRA(SCARD_E_SYSTEM_CANCELLED)
        CHK_ERRA(SCARD_F_COMM_ERROR)
        CHK_ERRA(SCARD_F_UNKNOWN_ERROR)
        CHK_ERRA(SCARD_E_INVALID_ATR)
        CHK_ERRA(SCARD_E_NOT_TRANSACTED)
        CHK_ERRA(SCARD_E_READER_UNAVAILABLE)
        CHK_ERRA(SCARD_P_SHUTDOWN)
        CHK_ERRA(SCARD_E_PCI_TOO_SMALL)
        CHK_ERRA(SCARD_E_READER_UNSUPPORTED)
        CHK_ERRA(SCARD_E_DUPLICATE_READER)
        CHK_ERRA(SCARD_E_CARD_UNSUPPORTED)
        CHK_ERRA(SCARD_E_NO_SERVICE)
        CHK_ERRA(SCARD_E_SERVICE_STOPPED)
        CHK_ERRA(SCARD_E_UNEXPECTED)
        CHK_ERRA(SCARD_E_ICC_INSTALLATION)
        CHK_ERRA(SCARD_E_ICC_CREATEORDER)
        CHK_ERRA(SCARD_E_UNSUPPORTED_FEATURE)
        CHK_ERRA(SCARD_E_DIR_NOT_FOUND)
        CHK_ERRA(SCARD_E_FILE_NOT_FOUND)
        CHK_ERRA(SCARD_E_NO_DIR)
        CHK_ERRA(SCARD_E_NO_FILE)
        CHK_ERRA(SCARD_E_NO_ACCESS)
        CHK_ERRA(SCARD_E_WRITE_TOO_MANY)
        CHK_ERRA(SCARD_E_BAD_SEEK)
        CHK_ERRA(SCARD_E_INVALID_CHV)
        CHK_ERRA(SCARD_E_UNKNOWN_RES_MNG)
        CHK_ERRA(SCARD_E_NO_SUCH_CERTIFICATE)
        CHK_ERRA(SCARD_E_CERTIFICATE_UNAVAILABLE)
        CHK_ERRA(SCARD_E_NO_READERS_AVAILABLE)
        CHK_ERRA(SCARD_E_COMM_DATA_LOST)
        CHK_ERRA(SCARD_W_UNSUPPORTED_CARD)
        CHK_ERRA(SCARD_W_UNRESPONSIVE_CARD)
        CHK_ERRA(SCARD_W_UNPOWERED_CARD)
        CHK_ERRA(SCARD_W_RESET_CARD)
        CHK_ERRA(SCARD_W_REMOVED_CARD)
        CHK_ERRA(SCARD_W_SECURITY_VIOLATION)
        CHK_ERRA(SCARD_W_WRONG_CHV)
        CHK_ERRA(SCARD_W_CHV_BLOCKED)
        CHK_ERRA(SCARD_W_EOF)
        CHK_ERRA(SCARD_W_CANCELLED_BY_USER)
        CHK_ERR_WIN32_ONLY(ERROR_INVALID_FUNCTION, "ERROR_INVALID_FUNCTION")
        CHK_ERR_WIN32A(ERROR_FILE_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_PATH_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_TOO_MANY_OPEN_FILES)
        CHK_ERRA(ERROR_ACCESS_DENIED)
        CHK_ERRA(ERROR_INVALID_HANDLE)
        CHK_ERR_WIN32A(ERROR_ARENA_TRASHED)
        CHK_ERR_WIN32A(ERROR_NOT_ENOUGH_MEMORY)
        CHK_ERR_WIN32A(ERROR_INVALID_BLOCK)
        CHK_ERR_WIN32A(ERROR_BAD_ENVIRONMENT)
        CHK_ERR_WIN32A(ERROR_BAD_FORMAT)
        CHK_ERR_WIN32A(ERROR_INVALID_ACCESS)
        CHK_ERR_WIN32A(ERROR_INVALID_DATA)
        CHK_ERRA(ERROR_OUTOFMEMORY)
        CHK_ERR_WIN32A(ERROR_INVALID_DRIVE)
        CHK_ERR_WIN32A(ERROR_CURRENT_DIRECTORY)
        CHK_ERR_WIN32A(ERROR_NOT_SAME_DEVICE)
        CHK_ERR_WIN32A(ERROR_NO_MORE_FILES)
        CHK_ERR_WIN32A(ERROR_WRITE_PROTECT)
        CHK_ERR_WIN32A(ERROR_BAD_UNIT)
        CHK_ERR_WIN32A(ERROR_NOT_READY)
        CHK_ERR_WIN32A(ERROR_BAD_COMMAND)
        CHK_ERR_WIN32A(ERROR_CRC)
        CHK_ERR_WIN32A(ERROR_BAD_LENGTH)
        CHK_ERR_WIN32A(ERROR_SEEK)
        CHK_ERR_WIN32A(ERROR_NOT_DOS_DISK)
        CHK_ERR_WIN32A(ERROR_SECTOR_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_OUT_OF_PAPER)
        CHK_ERR_WIN32A(ERROR_WRITE_FAULT)
        CHK_ERR_WIN32A(ERROR_READ_FAULT)
        CHK_ERR_WIN32A(ERROR_GEN_FAILURE)
        CHK_ERR_WIN32A(ERROR_SHARING_VIOLATION)
        CHK_ERR_WIN32A(ERROR_LOCK_VIOLATION)
        CHK_ERR_WIN32A(ERROR_WRONG_DISK)
        CHK_ERR_WIN32A(ERROR_SHARING_BUFFER_EXCEEDED)
        CHK_ERR_WIN32A(ERROR_HANDLE_EOF)
        CHK_ERR_WIN32A(ERROR_HANDLE_DISK_FULL)
        CHK_ERR_WIN32A(ERROR_NOT_SUPPORTED)
        CHK_ERR_WIN32A(ERROR_REM_NOT_LIST)
        CHK_ERR_WIN32A(ERROR_DUP_NAME)
        CHK_ERR_WIN32A(ERROR_BAD_NETPATH)
        CHK_ERR_WIN32A(ERROR_NETWORK_BUSY)
        CHK_ERR_WIN32A(ERROR_DEV_NOT_EXIST)
        CHK_ERR_WIN32A(ERROR_TOO_MANY_CMDS)
        CHK_ERR_WIN32A(ERROR_ADAP_HDW_ERR)
        CHK_ERR_WIN32A(ERROR_BAD_NET_RESP)
        CHK_ERR_WIN32A(ERROR_UNEXP_NET_ERR)
        CHK_ERR_WIN32A(ERROR_BAD_REM_ADAP)
        CHK_ERR_WIN32A(ERROR_PRINTQ_FULL)
        CHK_ERR_WIN32A(ERROR_NO_SPOOL_SPACE)
        CHK_ERR_WIN32A(ERROR_PRINT_CANCELLED)
        CHK_ERR_WIN32A(ERROR_NETNAME_DELETED)
        CHK_ERR_WIN32A(ERROR_NETWORK_ACCESS_DENIED)
        CHK_ERR_WIN32A(ERROR_BAD_DEV_TYPE)
        CHK_ERR_WIN32A(ERROR_BAD_NET_NAME)
        CHK_ERR_WIN32A(ERROR_TOO_MANY_NAMES)
        CHK_ERR_WIN32A(ERROR_TOO_MANY_SESS)
        CHK_ERR_WIN32A(ERROR_SHARING_PAUSED)
        CHK_ERR_WIN32A(ERROR_REQ_NOT_ACCEP)
        CHK_ERR_WIN32A(ERROR_REDIR_PAUSED)
        CHK_ERR_WIN32A(ERROR_FILE_EXISTS)
        CHK_ERR_WIN32A(ERROR_CANNOT_MAKE)
        CHK_ERR_WIN32A(ERROR_FAIL_I24)
        CHK_ERR_WIN32A(ERROR_OUT_OF_STRUCTURES)
        CHK_ERR_WIN32A(ERROR_ALREADY_ASSIGNED)
        CHK_ERR_WIN32A(ERROR_INVALID_PASSWORD)
        CHK_ERRA(ERROR_INVALID_PARAMETER)
        CHK_ERR_WIN32A(ERROR_NET_WRITE_FAULT)
        CHK_ERR_WIN32A(ERROR_NO_PROC_SLOTS)
        CHK_ERR_WIN32A(ERROR_TOO_MANY_SEMAPHORES)
        CHK_ERR_WIN32A(ERROR_EXCL_SEM_ALREADY_OWNED)
        CHK_ERR_WIN32A(ERROR_SEM_IS_SET)
        CHK_ERR_WIN32A(ERROR_TOO_MANY_SEM_REQUESTS)
        CHK_ERR_WIN32A(ERROR_INVALID_AT_INTERRUPT_TIME)
        CHK_ERR_WIN32A(ERROR_SEM_OWNER_DIED)
        CHK_ERR_WIN32A(ERROR_SEM_USER_LIMIT)
        CHK_ERR_WIN32A(ERROR_DISK_CHANGE)
        CHK_ERR_WIN32A(ERROR_DRIVE_LOCKED)
        CHK_ERR_WIN32A(ERROR_BROKEN_PIPE)
        CHK_ERR_WIN32A(ERROR_OPEN_FAILED)
        CHK_ERR_WIN32A(ERROR_BUFFER_OVERFLOW)
        CHK_ERR_WIN32A(ERROR_DISK_FULL)
        CHK_ERR_WIN32A(ERROR_NO_MORE_SEARCH_HANDLES)
        CHK_ERR_WIN32A(ERROR_INVALID_TARGET_HANDLE)
        CHK_ERR_WIN32A(ERROR_INVALID_CATEGORY)
        CHK_ERR_WIN32A(ERROR_INVALID_VERIFY_SWITCH)
        CHK_ERR_WIN32A(ERROR_BAD_DRIVER_LEVEL)
        CHK_ERR_WIN32A(ERROR_CALL_NOT_IMPLEMENTED)
        CHK_ERR_WIN32A(ERROR_SEM_TIMEOUT)
        CHK_ERR_WIN32A(ERROR_INSUFFICIENT_BUFFER)
        CHK_ERR_WIN32A(ERROR_INVALID_NAME)
        CHK_ERR_WIN32A(ERROR_INVALID_LEVEL)
        CHK_ERR_WIN32A(ERROR_NO_VOLUME_LABEL)
        CHK_ERR_WIN32A(ERROR_MOD_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_PROC_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_WAIT_NO_CHILDREN)
        CHK_ERR_WIN32A(ERROR_CHILD_NOT_COMPLETE)
        CHK_ERR_WIN32A(ERROR_DIRECT_ACCESS_HANDLE)
        CHK_ERR_WIN32A(ERROR_NEGATIVE_SEEK)
        CHK_ERR_WIN32A(ERROR_SEEK_ON_DEVICE)
        CHK_ERR_WIN32A(ERROR_IS_JOIN_TARGET)
        CHK_ERR_WIN32A(ERROR_IS_JOINED)
        CHK_ERR_WIN32A(ERROR_IS_SUBSTED)
        CHK_ERR_WIN32A(ERROR_NOT_JOINED)
        CHK_ERR_WIN32A(ERROR_NOT_SUBSTED)
        CHK_ERR_WIN32A(ERROR_JOIN_TO_JOIN)
        CHK_ERR_WIN32A(ERROR_SUBST_TO_SUBST)
        CHK_ERR_WIN32A(ERROR_JOIN_TO_SUBST)
        CHK_ERR_WIN32A(ERROR_SUBST_TO_JOIN)
        CHK_ERR_WIN32A(ERROR_BUSY_DRIVE)
        CHK_ERR_WIN32A(ERROR_SAME_DRIVE)
        CHK_ERR_WIN32A(ERROR_DIR_NOT_ROOT)
        CHK_ERR_WIN32A(ERROR_DIR_NOT_EMPTY)
        CHK_ERR_WIN32A(ERROR_IS_SUBST_PATH)
        CHK_ERR_WIN32A(ERROR_IS_JOIN_PATH)
        CHK_ERR_WIN32A(ERROR_PATH_BUSY)
        CHK_ERR_WIN32A(ERROR_IS_SUBST_TARGET)
        CHK_ERR_WIN32A(ERROR_SYSTEM_TRACE)
        CHK_ERR_WIN32A(ERROR_INVALID_EVENT_COUNT)
        CHK_ERR_WIN32A(ERROR_TOO_MANY_MUXWAITERS)
        CHK_ERR_WIN32A(ERROR_INVALID_LIST_FORMAT)
        CHK_ERR_WIN32A(ERROR_LABEL_TOO_LONG)
        CHK_ERR_WIN32A(ERROR_TOO_MANY_TCBS)
        CHK_ERR_WIN32A(ERROR_SIGNAL_REFUSED)
        CHK_ERR_WIN32A(ERROR_DISCARDED)
        CHK_ERR_WIN32A(ERROR_NOT_LOCKED)
        CHK_ERR_WIN32A(ERROR_BAD_THREADID_ADDR)
        CHK_ERR_WIN32A(ERROR_BAD_ARGUMENTS)
        CHK_ERR_WIN32A(ERROR_BAD_PATHNAME)
        CHK_ERR_WIN32A(ERROR_SIGNAL_PENDING)
        CHK_ERR_WIN32A(ERROR_MAX_THRDS_REACHED)
        CHK_ERR_WIN32A(ERROR_LOCK_FAILED)
        CHK_ERR_WIN32A(ERROR_BUSY)
        CHK_ERR_WIN32A(ERROR_CANCEL_VIOLATION)
        CHK_ERR_WIN32A(ERROR_ATOMIC_LOCKS_NOT_SUPPORTED)
        CHK_ERR_WIN32A(ERROR_INVALID_SEGMENT_NUMBER)
        CHK_ERR_WIN32A(ERROR_INVALID_ORDINAL)
        CHK_ERR_WIN32A(ERROR_ALREADY_EXISTS)
        CHK_ERR_WIN32A(ERROR_INVALID_FLAG_NUMBER)
        CHK_ERR_WIN32A(ERROR_SEM_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_INVALID_STARTING_CODESEG)
        CHK_ERR_WIN32A(ERROR_INVALID_STACKSEG)
        CHK_ERR_WIN32A(ERROR_INVALID_MODULETYPE)
        CHK_ERR_WIN32A(ERROR_INVALID_EXE_SIGNATURE)
        CHK_ERR_WIN32A(ERROR_EXE_MARKED_INVALID)
        CHK_ERR_WIN32A(ERROR_BAD_EXE_FORMAT)
        CHK_ERR_WIN32A(ERROR_ITERATED_DATA_EXCEEDS_64k)
        CHK_ERR_WIN32A(ERROR_INVALID_MINALLOCSIZE)
        CHK_ERR_WIN32A(ERROR_DYNLINK_FROM_INVALID_RING)
        CHK_ERR_WIN32A(ERROR_IOPL_NOT_ENABLED)
        CHK_ERR_WIN32A(ERROR_INVALID_SEGDPL)
        CHK_ERR_WIN32A(ERROR_AUTODATASEG_EXCEEDS_64k)
        CHK_ERRA(ERROR_RING2SEG_MUST_BE_MOVABLE)
        CHK_ERRA(ERROR_RELOC_CHAIN_XEEDS_SEGLIM)
        CHK_ERR_WIN32A(ERROR_INFLOOP_IN_RELOC_CHAIN)
        CHK_ERR_WIN32A(ERROR_ENVVAR_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_NO_SIGNAL_SENT)
        CHK_ERR_WIN32A(ERROR_FILENAME_EXCED_RANGE)
        CHK_ERR_WIN32A(ERROR_RING2_STACK_IN_USE)
        CHK_ERR_WIN32A(ERROR_META_EXPANSION_TOO_LONG)
        CHK_ERR_WIN32A(ERROR_INVALID_SIGNAL_NUMBER)
        CHK_ERR_WIN32A(ERROR_THREAD_1_INACTIVE)
        CHK_ERR_WIN32A(ERROR_LOCKED)
        CHK_ERR_WIN32A(ERROR_TOO_MANY_MODULES)
        CHK_ERR_WIN32A(ERROR_NESTING_NOT_ALLOWED)
        CHK_ERR_WIN32A(ERROR_EXE_MACHINE_TYPE_MISMATCH)
        CHK_ERR_WIN32A(ERROR_BAD_PIPE)
        CHK_ERR_WIN32A(ERROR_PIPE_BUSY)
        CHK_ERR_WIN32A(ERROR_NO_DATA)
        CHK_ERR_WIN32A(ERROR_PIPE_NOT_CONNECTED)
        CHK_ERR_WIN32A(ERROR_MORE_DATA)
        CHK_ERR_WIN32A(ERROR_VC_DISCONNECTED)
        CHK_ERR_WIN32A(ERROR_INVALID_EA_NAME)
        CHK_ERR_WIN32A(ERROR_EA_LIST_INCONSISTENT)
        CHK_ERR_WIN32A(WAIT_TIMEOUT)
        CHK_ERR_WIN32A(ERROR_NO_MORE_ITEMS)
        CHK_ERR_WIN32A(ERROR_CANNOT_COPY)
        CHK_ERR_WIN32A(ERROR_DIRECTORY)
        CHK_ERR_WIN32A(ERROR_EAS_DIDNT_FIT)
        CHK_ERR_WIN32A(ERROR_EA_FILE_CORRUPT)
        CHK_ERR_WIN32A(ERROR_EA_TABLE_FULL)
        CHK_ERR_WIN32A(ERROR_INVALID_EA_HANDLE)
        CHK_ERR_WIN32A(ERROR_EAS_NOT_SUPPORTED)
        CHK_ERR_WIN32A(ERROR_NOT_OWNER)
        CHK_ERR_WIN32A(ERROR_TOO_MANY_POSTS)
        CHK_ERR_WIN32A(ERROR_PARTIAL_COPY)
        CHK_ERR_WIN32A(ERROR_OPLOCK_NOT_GRANTED)
        CHK_ERR_WIN32A(ERROR_INVALID_OPLOCK_PROTOCOL)
        CHK_ERR_WIN32A(ERROR_MR_MID_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_INVALID_ADDRESS)
        CHK_ERR_WIN32A(ERROR_ARITHMETIC_OVERFLOW)
        CHK_ERR_WIN32A(ERROR_PIPE_CONNECTED)
        CHK_ERR_WIN32A(ERROR_PIPE_LISTENING)
        CHK_ERR_WIN32A(ERROR_EA_ACCESS_DENIED)
        CHK_ERR_WIN32A(ERROR_OPERATION_ABORTED)
        CHK_ERR_WIN32A(ERROR_IO_INCOMPLETE)
        CHK_ERR_WIN32A(ERROR_IO_PENDING)
        CHK_ERR_WIN32A(ERROR_NOACCESS)
        CHK_ERR_WIN32A(ERROR_SWAPERROR)
        CHK_ERR_WIN32A(ERROR_STACK_OVERFLOW)
        CHK_ERR_WIN32A(ERROR_INVALID_MESSAGE)
        CHK_ERR_WIN32A(ERROR_CAN_NOT_COMPLETE)
        CHK_ERR_WIN32A(ERROR_INVALID_FLAGS)
        CHK_ERR_WIN32A(ERROR_UNRECOGNIZED_VOLUME)
        CHK_ERR_WIN32A(ERROR_FILE_INVALID)
        CHK_ERR_WIN32A(ERROR_FULLSCREEN_MODE)
        CHK_ERR_WIN32A(ERROR_NO_TOKEN)
        CHK_ERR_WIN32A(ERROR_BADDB)
        CHK_ERR_WIN32A(ERROR_BADKEY)
        CHK_ERR_WIN32A(ERROR_CANTOPEN)
        CHK_ERR_WIN32A(ERROR_CANTREAD)
        CHK_ERR_WIN32A(ERROR_CANTWRITE)
        CHK_ERR_WIN32A(ERROR_REGISTRY_RECOVERED)
        CHK_ERR_WIN32A(ERROR_REGISTRY_CORRUPT)
        CHK_ERR_WIN32A(ERROR_REGISTRY_IO_FAILED)
        CHK_ERR_WIN32A(ERROR_NOT_REGISTRY_FILE)
        CHK_ERR_WIN32A(ERROR_KEY_DELETED)
        CHK_ERR_WIN32A(ERROR_NO_LOG_SPACE)
        CHK_ERR_WIN32A(ERROR_KEY_HAS_CHILDREN)
        CHK_ERR_WIN32A(ERROR_CHILD_MUST_BE_VOLATILE)
        CHK_ERR_WIN32A(ERROR_NOTIFY_ENUM_DIR)
        CHK_ERR_WIN32A(ERROR_DEPENDENT_SERVICES_RUNNING)
        CHK_ERR_WIN32A(ERROR_INVALID_SERVICE_CONTROL)
        CHK_ERR_WIN32A(ERROR_SERVICE_REQUEST_TIMEOUT)
        CHK_ERR_WIN32A(ERROR_SERVICE_NO_THREAD)
        CHK_ERR_WIN32A(ERROR_SERVICE_DATABASE_LOCKED)
        CHK_ERR_WIN32A(ERROR_SERVICE_ALREADY_RUNNING)
        CHK_ERR_WIN32A(ERROR_INVALID_SERVICE_ACCOUNT)
        CHK_ERR_WIN32A(ERROR_SERVICE_DISABLED)
        CHK_ERR_WIN32A(ERROR_CIRCULAR_DEPENDENCY)
        CHK_ERR_WIN32A(ERROR_SERVICE_DOES_NOT_EXIST)
        CHK_ERR_WIN32A(ERROR_SERVICE_CANNOT_ACCEPT_CTRL)
        CHK_ERR_WIN32A(ERROR_SERVICE_NOT_ACTIVE)
        CHK_ERR_WIN32A(ERROR_FAILED_SERVICE_CONTROLLER_CONNECT)
        CHK_ERR_WIN32A(ERROR_EXCEPTION_IN_SERVICE)
        CHK_ERR_WIN32A(ERROR_DATABASE_DOES_NOT_EXIST)
        CHK_ERR_WIN32A(ERROR_SERVICE_SPECIFIC_ERROR)
        CHK_ERR_WIN32A(ERROR_PROCESS_ABORTED)
        CHK_ERR_WIN32A(ERROR_SERVICE_DEPENDENCY_FAIL)
        CHK_ERR_WIN32A(ERROR_SERVICE_LOGON_FAILED)
        CHK_ERR_WIN32A(ERROR_SERVICE_START_HANG)
        CHK_ERR_WIN32A(ERROR_INVALID_SERVICE_LOCK)
        CHK_ERR_WIN32A(ERROR_SERVICE_MARKED_FOR_DELETE)
        CHK_ERR_WIN32A(ERROR_SERVICE_EXISTS)
        CHK_ERR_WIN32A(ERROR_ALREADY_RUNNING_LKG)
        CHK_ERR_WIN32A(ERROR_SERVICE_DEPENDENCY_DELETED)
        CHK_ERR_WIN32A(ERROR_BOOT_ALREADY_ACCEPTED)
        CHK_ERR_WIN32A(ERROR_SERVICE_NEVER_STARTED)
        CHK_ERR_WIN32A(ERROR_DUPLICATE_SERVICE_NAME)
        CHK_ERR_WIN32A(ERROR_DIFFERENT_SERVICE_ACCOUNT)
        CHK_ERR_WIN32A(ERROR_CANNOT_DETECT_DRIVER_FAILURE)
        CHK_ERR_WIN32A(ERROR_CANNOT_DETECT_PROCESS_ABORT)
        CHK_ERR_WIN32A(ERROR_NO_RECOVERY_PROGRAM)
        CHK_ERR_WIN32A(ERROR_SERVICE_NOT_IN_EXE)
        CHK_ERR_WIN32A(ERROR_END_OF_MEDIA)
        CHK_ERR_WIN32A(ERROR_FILEMARK_DETECTED)
        CHK_ERR_WIN32A(ERROR_BEGINNING_OF_MEDIA)
        CHK_ERR_WIN32A(ERROR_SETMARK_DETECTED)
        CHK_ERR_WIN32A(ERROR_NO_DATA_DETECTED)
        CHK_ERR_WIN32A(ERROR_PARTITION_FAILURE)
        CHK_ERR_WIN32A(ERROR_INVALID_BLOCK_LENGTH)
        CHK_ERR_WIN32A(ERROR_DEVICE_NOT_PARTITIONED)
        CHK_ERR_WIN32A(ERROR_UNABLE_TO_LOCK_MEDIA)
        CHK_ERR_WIN32A(ERROR_UNABLE_TO_UNLOAD_MEDIA)
        CHK_ERR_WIN32A(ERROR_MEDIA_CHANGED)
        CHK_ERR_WIN32A(ERROR_BUS_RESET)
        CHK_ERR_WIN32A(ERROR_NO_MEDIA_IN_DRIVE)
        CHK_ERR_WIN32A(ERROR_NO_UNICODE_TRANSLATION)
        CHK_ERR_WIN32A(ERROR_DLL_INIT_FAILED)
        CHK_ERR_WIN32A(ERROR_SHUTDOWN_IN_PROGRESS)
        CHK_ERR_WIN32A(ERROR_NO_SHUTDOWN_IN_PROGRESS)
        CHK_ERR_WIN32A(ERROR_IO_DEVICE)
        CHK_ERR_WIN32A(ERROR_SERIAL_NO_DEVICE)
        CHK_ERR_WIN32A(ERROR_IRQ_BUSY)
        CHK_ERR_WIN32A(ERROR_MORE_WRITES)
        CHK_ERR_WIN32A(ERROR_COUNTER_TIMEOUT)
        CHK_ERR_WIN32A(ERROR_FLOPPY_ID_MARK_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_FLOPPY_WRONG_CYLINDER)
        CHK_ERR_WIN32A(ERROR_FLOPPY_UNKNOWN_ERROR)
        CHK_ERR_WIN32A(ERROR_FLOPPY_BAD_REGISTERS)
        CHK_ERR_WIN32A(ERROR_DISK_RECALIBRATE_FAILED)
        CHK_ERR_WIN32A(ERROR_DISK_OPERATION_FAILED)
        CHK_ERR_WIN32A(ERROR_DISK_RESET_FAILED)
        CHK_ERR_WIN32A(ERROR_EOM_OVERFLOW)
        CHK_ERR_WIN32A(ERROR_NOT_ENOUGH_SERVER_MEMORY)
        CHK_ERR_WIN32A(ERROR_POSSIBLE_DEADLOCK)
        CHK_ERR_WIN32A(ERROR_MAPPED_ALIGNMENT)
        CHK_ERR_WIN32A(ERROR_SET_POWER_STATE_VETOED)
        CHK_ERR_WIN32A(ERROR_SET_POWER_STATE_FAILED)
        CHK_ERR_WIN32A(ERROR_TOO_MANY_LINKS)
        CHK_ERR_WIN32A(ERROR_OLD_WIN_VERSION)
        CHK_ERR_WIN32A(ERROR_APP_WRONG_OS)
        CHK_ERR_WIN32A(ERROR_SINGLE_INSTANCE_APP)
        CHK_ERR_WIN32A(ERROR_RMODE_APP)
        CHK_ERR_WIN32A(ERROR_INVALID_DLL)
        CHK_ERR_WIN32A(ERROR_NO_ASSOCIATION)
        CHK_ERR_WIN32A(ERROR_DDE_FAIL)
        CHK_ERR_WIN32A(ERROR_DLL_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_NO_MORE_USER_HANDLES)
        CHK_ERR_WIN32A(ERROR_MESSAGE_SYNC_ONLY)
        CHK_ERR_WIN32A(ERROR_SOURCE_ELEMENT_EMPTY)
        CHK_ERR_WIN32A(ERROR_DESTINATION_ELEMENT_FULL)
        CHK_ERR_WIN32A(ERROR_ILLEGAL_ELEMENT_ADDRESS)
        CHK_ERR_WIN32A(ERROR_MAGAZINE_NOT_PRESENT)
        CHK_ERR_WIN32A(ERROR_DEVICE_REINITIALIZATION_NEEDED)
        CHK_ERR_WIN32A(ERROR_DEVICE_REQUIRES_CLEANING)
        CHK_ERR_WIN32A(ERROR_DEVICE_DOOR_OPEN)
        CHK_ERR_WIN32A(ERROR_DEVICE_NOT_CONNECTED)
        CHK_ERR_WIN32A(ERROR_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_NO_MATCH)
        CHK_ERR_WIN32A(ERROR_SET_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_POINT_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_NO_TRACKING_SERVICE)
        CHK_ERR_WIN32A(ERROR_NO_VOLUME_ID)
        CHK_ERR_WIN32A(ERROR_UNABLE_TO_REMOVE_REPLACED)
        CHK_ERR_WIN32A(ERROR_UNABLE_TO_MOVE_REPLACEMENT)
        CHK_ERR_WIN32A(ERROR_UNABLE_TO_MOVE_REPLACEMENT_2)
        CHK_ERR_WIN32A(ERROR_JOURNAL_DELETE_IN_PROGRESS)
        CHK_ERR_WIN32A(ERROR_JOURNAL_NOT_ACTIVE)
        CHK_ERR_WIN32A(ERROR_POTENTIAL_FILE_FOUND)
        CHK_ERR_WIN32A(ERROR_BAD_DEVICE)
        CHK_ERR_WIN32A(ERROR_CONNECTION_UNAVAIL)
        CHK_ERR_WIN32A(ERROR_DEVICE_ALREADY_REMEMBERED)
        CHK_ERR_WIN32A(ERROR_NO_NET_OR_BAD_PATH)
        CHK_ERR_WIN32A(ERROR_BAD_PROVIDER)
        CHK_ERR_WIN32A(ERROR_CANNOT_OPEN_PROFILE)
        CHK_ERR_WIN32A(ERROR_BAD_PROFILE)
        CHK_ERR_WIN32A(ERROR_NOT_CONTAINER)
        CHK_ERR_WIN32A(ERROR_EXTENDED_ERROR)
        CHK_ERR_WIN32A(ERROR_INVALID_GROUPNAME)
        CHK_ERR_WIN32A(ERROR_INVALID_COMPUTERNAME)
        CHK_ERR_WIN32A(ERROR_INVALID_EVENTNAME)
        CHK_ERR_WIN32A(ERROR_INVALID_DOMAINNAME)
        CHK_ERR_WIN32A(ERROR_INVALID_SERVICENAME)
        CHK_ERR_WIN32A(ERROR_INVALID_NETNAME)
        CHK_ERR_WIN32A(ERROR_INVALID_SHARENAME)
        CHK_ERR_WIN32A(ERROR_INVALID_PASSWORDNAME)
        CHK_ERR_WIN32A(ERROR_INVALID_MESSAGENAME)
        CHK_ERR_WIN32A(ERROR_INVALID_MESSAGEDEST)
        CHK_ERR_WIN32A(ERROR_SESSION_CREDENTIAL_CONFLICT)
        CHK_ERR_WIN32A(ERROR_REMOTE_SESSION_LIMIT_EXCEEDED)
        CHK_ERR_WIN32A(ERROR_DUP_DOMAINNAME)
        CHK_ERR_WIN32A(ERROR_NO_NETWORK)
        CHK_ERR_WIN32A(ERROR_CANCELLED)
        CHK_ERR_WIN32A(ERROR_USER_MAPPED_FILE)
        CHK_ERR_WIN32A(ERROR_CONNECTION_REFUSED)
        CHK_ERR_WIN32A(ERROR_GRACEFUL_DISCONNECT)
        CHK_ERR_WIN32A(ERROR_ADDRESS_ALREADY_ASSOCIATED)
        CHK_ERR_WIN32A(ERROR_ADDRESS_NOT_ASSOCIATED)
        CHK_ERR_WIN32A(ERROR_CONNECTION_INVALID)
        CHK_ERR_WIN32A(ERROR_CONNECTION_ACTIVE)
        CHK_ERR_WIN32A(ERROR_NETWORK_UNREACHABLE)
        CHK_ERR_WIN32A(ERROR_HOST_UNREACHABLE)
        CHK_ERR_WIN32A(ERROR_PROTOCOL_UNREACHABLE)
        CHK_ERR_WIN32A(ERROR_PORT_UNREACHABLE)
        CHK_ERR_WIN32A(ERROR_REQUEST_ABORTED)
        CHK_ERR_WIN32A(ERROR_CONNECTION_ABORTED)
        CHK_ERR_WIN32A(ERROR_RETRY)
        CHK_ERR_WIN32A(ERROR_CONNECTION_COUNT_LIMIT)
        CHK_ERR_WIN32A(ERROR_LOGIN_TIME_RESTRICTION)
        CHK_ERR_WIN32A(ERROR_LOGIN_WKSTA_RESTRICTION)
        CHK_ERR_WIN32A(ERROR_INCORRECT_ADDRESS)
        CHK_ERR_WIN32A(ERROR_ALREADY_REGISTERED)
        CHK_ERR_WIN32A(ERROR_SERVICE_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_NOT_AUTHENTICATED)
        CHK_ERR_WIN32A(ERROR_NOT_LOGGED_ON)
        CHK_ERR_WIN32A(ERROR_CONTINUE)
        CHK_ERR_WIN32A(ERROR_ALREADY_INITIALIZED)
        CHK_ERR_WIN32A(ERROR_NO_MORE_DEVICES)
        CHK_ERR_WIN32A(ERROR_NO_SUCH_SITE)
        CHK_ERR_WIN32A(ERROR_DOMAIN_CONTROLLER_EXISTS)
        CHK_ERR_WIN32A(ERROR_NOT_ALL_ASSIGNED)
        CHK_ERR_WIN32A(ERROR_SOME_NOT_MAPPED)
        CHK_ERR_WIN32A(ERROR_NO_QUOTAS_FOR_ACCOUNT)
        CHK_ERR_WIN32A(ERROR_LOCAL_USER_SESSION_KEY)
        CHK_ERR_WIN32A(ERROR_NULL_LM_PASSWORD)
        CHK_ERR_WIN32A(ERROR_UNKNOWN_REVISION)
        CHK_ERR_WIN32A(ERROR_REVISION_MISMATCH)
        CHK_ERR_WIN32A(ERROR_INVALID_OWNER)
        CHK_ERR_WIN32A(ERROR_INVALID_PRIMARY_GROUP)
        CHK_ERR_WIN32A(ERROR_NO_IMPERSONATION_TOKEN)
        CHK_ERR_WIN32A(ERROR_CANT_DISABLE_MANDATORY)
        CHK_ERR_WIN32A(ERROR_NO_LOGON_SERVERS)
        CHK_ERR_WIN32A(ERROR_NO_SUCH_LOGON_SESSION)
        CHK_ERR_WIN32A(ERROR_NO_SUCH_PRIVILEGE)
        CHK_ERR_WIN32A(ERROR_PRIVILEGE_NOT_HELD)
        CHK_ERR_WIN32A(ERROR_INVALID_ACCOUNT_NAME)
        CHK_ERR_WIN32A(ERROR_USER_EXISTS)
        CHK_ERR_WIN32A(ERROR_NO_SUCH_USER)
        CHK_ERR_WIN32A(ERROR_GROUP_EXISTS)
        CHK_ERR_WIN32A(ERROR_NO_SUCH_GROUP)
        CHK_ERR_WIN32A(ERROR_MEMBER_IN_GROUP)
        CHK_ERR_WIN32A(ERROR_MEMBER_NOT_IN_GROUP)
        CHK_ERR_WIN32A(ERROR_LAST_ADMIN)
        CHK_ERR_WIN32A(ERROR_WRONG_PASSWORD)
        CHK_ERR_WIN32A(ERROR_ILL_FORMED_PASSWORD)
        CHK_ERR_WIN32A(ERROR_PASSWORD_RESTRICTION)
        CHK_ERR_WIN32A(ERROR_LOGON_FAILURE)
        CHK_ERR_WIN32A(ERROR_ACCOUNT_RESTRICTION)
        CHK_ERR_WIN32A(ERROR_INVALID_LOGON_HOURS)
        CHK_ERR_WIN32A(ERROR_INVALID_WORKSTATION)
        CHK_ERR_WIN32A(ERROR_PASSWORD_EXPIRED)
        CHK_ERR_WIN32A(ERROR_ACCOUNT_DISABLED)
        CHK_ERR_WIN32A(ERROR_NONE_MAPPED)
        CHK_ERR_WIN32A(ERROR_TOO_MANY_LUIDS_REQUESTED)
        CHK_ERR_WIN32A(ERROR_LUIDS_EXHAUSTED)
        CHK_ERR_WIN32A(ERROR_INVALID_SUB_AUTHORITY)
        CHK_ERR_WIN32A(ERROR_INVALID_ACL)
        CHK_ERR_WIN32A(ERROR_INVALID_SID)
        CHK_ERR_WIN32A(ERROR_INVALID_SECURITY_DESCR)
        CHK_ERR_WIN32A(ERROR_BAD_INHERITANCE_ACL)
        CHK_ERR_WIN32A(ERROR_SERVER_DISABLED)
        CHK_ERR_WIN32A(ERROR_SERVER_NOT_DISABLED)
        CHK_ERR_WIN32A(ERROR_INVALID_ID_AUTHORITY)
        CHK_ERR_WIN32A(ERROR_ALLOTTED_SPACE_EXCEEDED)
        CHK_ERR_WIN32A(ERROR_INVALID_GROUP_ATTRIBUTES)
        CHK_ERR_WIN32A(ERROR_BAD_IMPERSONATION_LEVEL)
        CHK_ERR_WIN32A(ERROR_CANT_OPEN_ANONYMOUS)
        CHK_ERR_WIN32A(ERROR_BAD_VALIDATION_CLASS)
        CHK_ERR_WIN32A(ERROR_BAD_TOKEN_TYPE)
        CHK_ERR_WIN32A(ERROR_NO_SECURITY_ON_OBJECT)
        CHK_ERR_WIN32A(ERROR_CANT_ACCESS_DOMAIN_INFO)
        CHK_ERR_WIN32A(ERROR_INVALID_SERVER_STATE)
        CHK_ERR_WIN32A(ERROR_INVALID_DOMAIN_STATE)
        CHK_ERR_WIN32A(ERROR_INVALID_DOMAIN_ROLE)
        CHK_ERR_WIN32A(ERROR_NO_SUCH_DOMAIN)
        CHK_ERR_WIN32A(ERROR_DOMAIN_EXISTS)
        CHK_ERR_WIN32A(ERROR_DOMAIN_LIMIT_EXCEEDED)
        CHK_ERR_WIN32A(ERROR_INTERNAL_DB_CORRUPTION)
        CHK_ERR_WIN32A(ERROR_INTERNAL_ERROR)
        CHK_ERR_WIN32A(ERROR_GENERIC_NOT_MAPPED)
        CHK_ERR_WIN32A(ERROR_BAD_DESCRIPTOR_FORMAT)
        CHK_ERR_WIN32A(ERROR_NOT_LOGON_PROCESS)
        CHK_ERR_WIN32A(ERROR_LOGON_SESSION_EXISTS)
        CHK_ERR_WIN32A(ERROR_NO_SUCH_PACKAGE)
        CHK_ERR_WIN32A(ERROR_BAD_LOGON_SESSION_STATE)
        CHK_ERR_WIN32A(ERROR_LOGON_SESSION_COLLISION)
        CHK_ERR_WIN32A(ERROR_INVALID_LOGON_TYPE)
        CHK_ERR_WIN32A(ERROR_CANNOT_IMPERSONATE)
        CHK_ERR_WIN32A(ERROR_RXACT_INVALID_STATE)
        CHK_ERR_WIN32A(ERROR_RXACT_COMMIT_FAILURE)
        CHK_ERR_WIN32A(ERROR_SPECIAL_ACCOUNT)
        CHK_ERR_WIN32A(ERROR_SPECIAL_GROUP)
        CHK_ERR_WIN32A(ERROR_SPECIAL_USER)
        CHK_ERR_WIN32A(ERROR_MEMBERS_PRIMARY_GROUP)
        CHK_ERR_WIN32A(ERROR_TOKEN_ALREADY_IN_USE)
        CHK_ERR_WIN32A(ERROR_NO_SUCH_ALIAS)
        CHK_ERR_WIN32A(ERROR_MEMBER_NOT_IN_ALIAS)
        CHK_ERR_WIN32A(ERROR_MEMBER_IN_ALIAS)
        CHK_ERR_WIN32A(ERROR_ALIAS_EXISTS)
        CHK_ERR_WIN32A(ERROR_LOGON_NOT_GRANTED)
        CHK_ERR_WIN32A(ERROR_TOO_MANY_SECRETS)
        CHK_ERR_WIN32A(ERROR_SECRET_TOO_LONG)
        CHK_ERR_WIN32A(ERROR_INTERNAL_DB_ERROR)
        CHK_ERR_WIN32A(ERROR_TOO_MANY_CONTEXT_IDS)
        CHK_ERR_WIN32A(ERROR_LOGON_TYPE_NOT_GRANTED)
        CHK_ERR_WIN32A(ERROR_NT_CROSS_ENCRYPTION_REQUIRED)
        CHK_ERR_WIN32A(ERROR_NO_SUCH_MEMBER)
        CHK_ERR_WIN32A(ERROR_INVALID_MEMBER)
        CHK_ERR_WIN32A(ERROR_TOO_MANY_SIDS)
        CHK_ERR_WIN32A(ERROR_LM_CROSS_ENCRYPTION_REQUIRED)
        CHK_ERR_WIN32A(ERROR_NO_INHERITANCE)
        CHK_ERR_WIN32A(ERROR_FILE_CORRUPT)
        CHK_ERR_WIN32A(ERROR_DISK_CORRUPT)
        CHK_ERR_WIN32A(ERROR_NO_USER_SESSION_KEY)
        CHK_ERR_WIN32A(ERROR_LICENSE_QUOTA_EXCEEDED)
        CHK_ERR_WIN32A(ERROR_WRONG_TARGET_NAME)
        CHK_ERR_WIN32A(ERROR_MUTUAL_AUTH_FAILED)
        CHK_ERR_WIN32A(ERROR_TIME_SKEW)
        CHK_ERR_WIN32A(ERROR_INVALID_WINDOW_HANDLE)
        CHK_ERR_WIN32A(ERROR_INVALID_MENU_HANDLE)
        CHK_ERR_WIN32A(ERROR_INVALID_CURSOR_HANDLE)
        CHK_ERR_WIN32A(ERROR_INVALID_ACCEL_HANDLE)
        CHK_ERR_WIN32A(ERROR_INVALID_HOOK_HANDLE)
        CHK_ERR_WIN32A(ERROR_INVALID_DWP_HANDLE)
        CHK_ERR_WIN32A(ERROR_TLW_WITH_WSCHILD)
        CHK_ERR_WIN32A(ERROR_CANNOT_FIND_WND_CLASS)
        CHK_ERR_WIN32A(ERROR_WINDOW_OF_OTHER_THREAD)
        CHK_ERR_WIN32A(ERROR_HOTKEY_ALREADY_REGISTERED)
        CHK_ERR_WIN32A(ERROR_CLASS_ALREADY_EXISTS)
        CHK_ERR_WIN32A(ERROR_CLASS_DOES_NOT_EXIST)
        CHK_ERR_WIN32A(ERROR_CLASS_HAS_WINDOWS)
        CHK_ERR_WIN32A(ERROR_INVALID_INDEX)
        CHK_ERR_WIN32A(ERROR_INVALID_ICON_HANDLE)
        CHK_ERR_WIN32A(ERROR_PRIVATE_DIALOG_INDEX)
        CHK_ERR_WIN32A(ERROR_LISTBOX_ID_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_NO_WILDCARD_CHARACTERS)
        CHK_ERR_WIN32A(ERROR_CLIPBOARD_NOT_OPEN)
        CHK_ERR_WIN32A(ERROR_HOTKEY_NOT_REGISTERED)
        CHK_ERR_WIN32A(ERROR_WINDOW_NOT_DIALOG)
        CHK_ERR_WIN32A(ERROR_CONTROL_ID_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_INVALID_COMBOBOX_MESSAGE)
        CHK_ERR_WIN32A(ERROR_WINDOW_NOT_COMBOBOX)
        CHK_ERR_WIN32A(ERROR_INVALID_EDIT_HEIGHT)
        CHK_ERR_WIN32A(ERROR_DC_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_INVALID_HOOK_FILTER)
        CHK_ERR_WIN32A(ERROR_INVALID_FILTER_PROC)
        CHK_ERR_WIN32A(ERROR_HOOK_NEEDS_HMOD)
        CHK_ERR_WIN32A(ERROR_GLOBAL_ONLY_HOOK)
        CHK_ERR_WIN32A(ERROR_JOURNAL_HOOK_SET)
        CHK_ERR_WIN32A(ERROR_HOOK_NOT_INSTALLED)
        CHK_ERR_WIN32A(ERROR_INVALID_LB_MESSAGE)
        CHK_ERR_WIN32A(ERROR_SETCOUNT_ON_BAD_LB)
        CHK_ERR_WIN32A(ERROR_LB_WITHOUT_TABSTOPS)
        CHK_ERR_WIN32A(ERROR_DESTROY_OBJECT_OF_OTHER_THREAD)
        CHK_ERR_WIN32A(ERROR_CHILD_WINDOW_MENU)
        CHK_ERR_WIN32A(ERROR_NO_SYSTEM_MENU)
        CHK_ERR_WIN32A(ERROR_INVALID_MSGBOX_STYLE)
        CHK_ERR_WIN32A(ERROR_INVALID_SPI_VALUE)
        CHK_ERR_WIN32A(ERROR_SCREEN_ALREADY_LOCKED)
        CHK_ERR_WIN32A(ERROR_HWNDS_HAVE_DIFF_PARENT)
        CHK_ERR_WIN32A(ERROR_NOT_CHILD_WINDOW)
        CHK_ERR_WIN32A(ERROR_INVALID_GW_COMMAND)
        CHK_ERR_WIN32A(ERROR_INVALID_THREAD_ID)
        CHK_ERR_WIN32A(ERROR_NON_MDICHILD_WINDOW)
        CHK_ERR_WIN32A(ERROR_POPUP_ALREADY_ACTIVE)
        CHK_ERR_WIN32A(ERROR_NO_SCROLLBARS)
        CHK_ERR_WIN32A(ERROR_INVALID_SCROLLBAR_RANGE)
        CHK_ERR_WIN32A(ERROR_INVALID_SHOWWIN_COMMAND)
        CHK_ERR_WIN32A(ERROR_NO_SYSTEM_RESOURCES)
        CHK_ERR_WIN32A(ERROR_NONPAGED_SYSTEM_RESOURCES)
        CHK_ERR_WIN32A(ERROR_PAGED_SYSTEM_RESOURCES)
        CHK_ERR_WIN32A(ERROR_WORKING_SET_QUOTA)
        CHK_ERR_WIN32A(ERROR_PAGEFILE_QUOTA)
        CHK_ERR_WIN32A(ERROR_COMMITMENT_LIMIT)
        CHK_ERR_WIN32A(ERROR_MENU_ITEM_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_INVALID_KEYBOARD_HANDLE)
        CHK_ERR_WIN32A(ERROR_HOOK_TYPE_NOT_ALLOWED)
        CHK_ERR_WIN32A(ERROR_REQUIRES_INTERACTIVE_WINDOWSTATION)
        CHK_ERR_WIN32A(ERROR_TIMEOUT)
        CHK_ERR_WIN32A(ERROR_INVALID_MONITOR_HANDLE)
        CHK_ERR_WIN32A(ERROR_EVENTLOG_FILE_CORRUPT)
        CHK_ERR_WIN32A(ERROR_EVENTLOG_CANT_START)
        CHK_ERR_WIN32A(ERROR_LOG_FILE_FULL)
        CHK_ERR_WIN32A(ERROR_EVENTLOG_FILE_CHANGED)
        CHK_ERR_WIN32A(ERROR_INSTALL_USEREXIT)
        CHK_ERR_WIN32A(ERROR_INSTALL_FAILURE)
        CHK_ERR_WIN32A(ERROR_INSTALL_SUSPEND)
        CHK_ERR_WIN32A(ERROR_UNKNOWN_PRODUCT)
        CHK_ERR_WIN32A(ERROR_UNKNOWN_FEATURE)
        CHK_ERR_WIN32A(ERROR_UNKNOWN_COMPONENT)
        CHK_ERR_WIN32A(ERROR_UNKNOWN_PROPERTY)
        CHK_ERR_WIN32A(ERROR_INVALID_HANDLE_STATE)
        CHK_ERR_WIN32A(ERROR_BAD_CONFIGURATION)
        CHK_ERR_WIN32A(ERROR_INDEX_ABSENT)
        CHK_ERR_WIN32A(ERROR_INSTALL_SOURCE_ABSENT)
        CHK_ERR_WIN32A(ERROR_PRODUCT_UNINSTALLED)
        CHK_ERR_WIN32A(ERROR_BAD_QUERY_SYNTAX)
        CHK_ERR_WIN32A(ERROR_INVALID_FIELD)
        CHK_ERR_WIN32A(ERROR_DEVICE_REMOVED)
        CHK_ERR_WIN32A(RPC_S_INVALID_STRING_BINDING)
        CHK_ERR_WIN32A(RPC_S_WRONG_KIND_OF_BINDING)
        CHK_ERR_WIN32A(RPC_S_INVALID_BINDING)
        CHK_ERR_WIN32A(RPC_S_PROTSEQ_NOT_SUPPORTED)
        CHK_ERR_WIN32A(RPC_S_INVALID_RPC_PROTSEQ)
        CHK_ERR_WIN32A(RPC_S_INVALID_STRING_UUID)
        CHK_ERR_WIN32A(RPC_S_INVALID_ENDPOINT_FORMAT)
        CHK_ERR_WIN32A(RPC_S_INVALID_NET_ADDR)
        CHK_ERR_WIN32A(RPC_S_NO_ENDPOINT_FOUND)
        CHK_ERR_WIN32A(RPC_S_INVALID_TIMEOUT)
        CHK_ERR_WIN32A(RPC_S_OBJECT_NOT_FOUND)
        CHK_ERR_WIN32A(RPC_S_ALREADY_REGISTERED)
        CHK_ERR_WIN32A(RPC_S_TYPE_ALREADY_REGISTERED)
        CHK_ERR_WIN32A(RPC_S_ALREADY_LISTENING)
        CHK_ERR_WIN32A(RPC_S_NO_PROTSEQS_REGISTERED)
        CHK_ERR_WIN32A(RPC_S_NOT_LISTENING)
        CHK_ERR_WIN32A(RPC_S_UNKNOWN_MGR_TYPE)
        CHK_ERR_WIN32A(RPC_S_UNKNOWN_IF)
        CHK_ERR_WIN32A(RPC_S_NO_BINDINGS)
        CHK_ERR_WIN32A(RPC_S_NO_PROTSEQS)
        CHK_ERR_WIN32A(RPC_S_CANT_CREATE_ENDPOINT)
        CHK_ERR_WIN32A(RPC_S_OUT_OF_RESOURCES)
        CHK_ERR_WIN32A(RPC_S_SERVER_UNAVAILABLE)
        CHK_ERR_WIN32A(RPC_S_SERVER_TOO_BUSY)
        CHK_ERR_WIN32A(RPC_S_INVALID_NETWORK_OPTIONS)
        CHK_ERR_WIN32A(RPC_S_NO_CALL_ACTIVE)
        CHK_ERR_WIN32A(RPC_S_CALL_FAILED)
        CHK_ERR_WIN32A(RPC_S_CALL_FAILED_DNE)
        CHK_ERR_WIN32A(RPC_S_PROTOCOL_ERROR)
        CHK_ERR_WIN32A(RPC_S_UNSUPPORTED_TRANS_SYN)
        CHK_ERR_WIN32A(RPC_S_UNSUPPORTED_TYPE)
        CHK_ERR_WIN32A(RPC_S_INVALID_TAG)
        CHK_ERR_WIN32A(RPC_S_INVALID_BOUND)
        CHK_ERR_WIN32A(RPC_S_NO_ENTRY_NAME)
        CHK_ERR_WIN32A(RPC_S_INVALID_NAME_SYNTAX)
        CHK_ERR_WIN32A(RPC_S_UNSUPPORTED_NAME_SYNTAX)
        CHK_ERR_WIN32A(RPC_S_UUID_NO_ADDRESS)
        CHK_ERR_WIN32A(RPC_S_DUPLICATE_ENDPOINT)
        CHK_ERR_WIN32A(RPC_S_UNKNOWN_AUTHN_TYPE)
        CHK_ERR_WIN32A(RPC_S_MAX_CALLS_TOO_SMALL)
        CHK_ERR_WIN32A(RPC_S_STRING_TOO_LONG)
        CHK_ERR_WIN32A(RPC_S_PROTSEQ_NOT_FOUND)
        CHK_ERR_WIN32A(RPC_S_PROCNUM_OUT_OF_RANGE)
        CHK_ERR_WIN32A(RPC_S_BINDING_HAS_NO_AUTH)
        CHK_ERR_WIN32A(RPC_S_UNKNOWN_AUTHN_SERVICE)
        CHK_ERR_WIN32A(RPC_S_UNKNOWN_AUTHN_LEVEL)
        CHK_ERR_WIN32A(RPC_S_INVALID_AUTH_IDENTITY)
        CHK_ERR_WIN32A(RPC_S_UNKNOWN_AUTHZ_SERVICE)
        CHK_ERR_WIN32A(EPT_S_INVALID_ENTRY)
        CHK_ERR_WIN32A(EPT_S_CANT_PERFORM_OP)
        CHK_ERR_WIN32A(EPT_S_NOT_REGISTERED)
        CHK_ERR_WIN32A(RPC_S_NOTHING_TO_EXPORT)
        CHK_ERR_WIN32A(RPC_S_INCOMPLETE_NAME)
        CHK_ERR_WIN32A(RPC_S_INVALID_VERS_OPTION)
        CHK_ERR_WIN32A(RPC_S_NO_MORE_MEMBERS)
        CHK_ERR_WIN32A(RPC_S_NOT_ALL_OBJS_UNEXPORTED)
        CHK_ERR_WIN32A(RPC_S_INTERFACE_NOT_FOUND)
        CHK_ERR_WIN32A(RPC_S_ENTRY_ALREADY_EXISTS)
        CHK_ERR_WIN32A(RPC_S_ENTRY_NOT_FOUND)
        CHK_ERR_WIN32A(RPC_S_NAME_SERVICE_UNAVAILABLE)
        CHK_ERR_WIN32A(RPC_S_INVALID_NAF_ID)
        CHK_ERR_WIN32A(RPC_S_CANNOT_SUPPORT)
        CHK_ERR_WIN32A(RPC_S_NO_CONTEXT_AVAILABLE)
        CHK_ERR_WIN32A(RPC_S_INTERNAL_ERROR)
        CHK_ERR_WIN32A(RPC_S_ZERO_DIVIDE)
        CHK_ERR_WIN32A(RPC_S_ADDRESS_ERROR)
        CHK_ERR_WIN32A(RPC_S_FP_DIV_ZERO)
        CHK_ERR_WIN32A(RPC_S_FP_UNDERFLOW)
        CHK_ERR_WIN32A(RPC_S_FP_OVERFLOW)
        CHK_ERR_WIN32A(RPC_X_NO_MORE_ENTRIES)
        CHK_ERR_WIN32A(RPC_X_SS_CHAR_TRANS_OPEN_FAIL)
        CHK_ERR_WIN32A(RPC_X_SS_CHAR_TRANS_SHORT_FILE)
        CHK_ERR_WIN32A(RPC_X_SS_IN_NULL_CONTEXT)
        CHK_ERR_WIN32A(RPC_X_SS_CONTEXT_DAMAGED)
        CHK_ERR_WIN32A(RPC_X_SS_HANDLES_MISMATCH)
        CHK_ERR_WIN32A(RPC_X_SS_CANNOT_GET_CALL_HANDLE)
        CHK_ERR_WIN32A(RPC_X_NULL_REF_POINTER)
        CHK_ERR_WIN32A(RPC_X_ENUM_VALUE_OUT_OF_RANGE)
        CHK_ERR_WIN32A(RPC_X_BYTE_COUNT_TOO_SMALL)
        CHK_ERR_WIN32A(RPC_X_BAD_STUB_DATA)
        CHK_ERR_WIN32A(ERROR_INVALID_USER_BUFFER)
        CHK_ERR_WIN32A(ERROR_UNRECOGNIZED_MEDIA)
        CHK_ERR_WIN32A(ERROR_NO_TRUST_LSA_SECRET)
        CHK_ERR_WIN32A(ERROR_NO_TRUST_SAM_ACCOUNT)
        CHK_ERR_WIN32A(ERROR_TRUSTED_DOMAIN_FAILURE)
        CHK_ERR_WIN32A(ERROR_TRUSTED_RELATIONSHIP_FAILURE)
        CHK_ERR_WIN32A(ERROR_TRUST_FAILURE)
        CHK_ERR_WIN32A(RPC_S_CALL_IN_PROGRESS)
        CHK_ERR_WIN32A(ERROR_NETLOGON_NOT_STARTED)
        CHK_ERR_WIN32A(ERROR_ACCOUNT_EXPIRED)
        CHK_ERR_WIN32A(ERROR_REDIRECTOR_HAS_OPEN_HANDLES)
        CHK_ERR_WIN32A(ERROR_PRINTER_DRIVER_ALREADY_INSTALLED)
        CHK_ERR_WIN32A(ERROR_UNKNOWN_PORT)
        CHK_ERR_WIN32A(ERROR_UNKNOWN_PRINTER_DRIVER)
        CHK_ERR_WIN32A(ERROR_UNKNOWN_PRINTPROCESSOR)
        CHK_ERR_WIN32A(ERROR_INVALID_SEPARATOR_FILE)
        CHK_ERR_WIN32A(ERROR_INVALID_PRIORITY)
        CHK_ERR_WIN32A(ERROR_INVALID_PRINTER_NAME)
        CHK_ERR_WIN32A(ERROR_PRINTER_ALREADY_EXISTS)
        CHK_ERR_WIN32A(ERROR_INVALID_PRINTER_COMMAND)
        CHK_ERR_WIN32A(ERROR_INVALID_DATATYPE)
        CHK_ERR_WIN32A(ERROR_INVALID_ENVIRONMENT)
        CHK_ERR_WIN32A(RPC_S_NO_MORE_BINDINGS)
        CHK_ERR_WIN32A(ERROR_NOLOGON_INTERDOMAIN_TRUST_ACCOUNT)
        CHK_ERR_WIN32A(ERROR_NOLOGON_WORKSTATION_TRUST_ACCOUNT)
        CHK_ERR_WIN32A(ERROR_NOLOGON_SERVER_TRUST_ACCOUNT)
        CHK_ERR_WIN32A(ERROR_DOMAIN_TRUST_INCONSISTENT)
        CHK_ERR_WIN32A(ERROR_SERVER_HAS_OPEN_HANDLES)
        CHK_ERR_WIN32A(ERROR_RESOURCE_DATA_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_RESOURCE_TYPE_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_RESOURCE_NAME_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_RESOURCE_LANG_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_NOT_ENOUGH_QUOTA)
        CHK_ERR_WIN32A(RPC_S_NO_INTERFACES)
        CHK_ERR_WIN32A(RPC_S_CALL_CANCELLED)
        CHK_ERR_WIN32A(RPC_S_BINDING_INCOMPLETE)
        CHK_ERR_WIN32A(RPC_S_COMM_FAILURE)
        CHK_ERR_WIN32A(RPC_S_UNSUPPORTED_AUTHN_LEVEL)
        CHK_ERR_WIN32A(RPC_S_NO_PRINC_NAME)
        CHK_ERR_WIN32A(RPC_S_NOT_RPC_ERROR)
        CHK_ERR_WIN32A(RPC_S_UUID_LOCAL_ONLY)
        CHK_ERR_WIN32A(RPC_S_SEC_PKG_ERROR)
        CHK_ERR_WIN32A(RPC_S_NOT_CANCELLED)
        CHK_ERR_WIN32A(RPC_X_INVALID_ES_ACTION)
        CHK_ERR_WIN32A(RPC_X_WRONG_ES_VERSION)
        CHK_ERR_WIN32A(RPC_X_WRONG_STUB_VERSION)
        CHK_ERR_WIN32A(RPC_X_INVALID_PIPE_OBJECT)
        CHK_ERR_WIN32A(RPC_X_WRONG_PIPE_ORDER)
        CHK_ERR_WIN32A(RPC_X_WRONG_PIPE_VERSION)
        CHK_ERR_WIN32A(RPC_S_GROUP_MEMBER_NOT_FOUND)
        CHK_ERR_WIN32A(EPT_S_CANT_CREATE)
        CHK_ERR_WIN32A(RPC_S_INVALID_OBJECT)
        CHK_ERR_WIN32A(ERROR_INVALID_TIME)
        CHK_ERR_WIN32A(ERROR_INVALID_FORM_NAME)
        CHK_ERR_WIN32A(ERROR_INVALID_FORM_SIZE)
        CHK_ERR_WIN32A(ERROR_ALREADY_WAITING)
        CHK_ERR_WIN32A(ERROR_PRINTER_DELETED)
        CHK_ERR_WIN32A(ERROR_INVALID_PRINTER_STATE)
        CHK_ERR_WIN32A(ERROR_PASSWORD_MUST_CHANGE)
        CHK_ERR_WIN32A(ERROR_DOMAIN_CONTROLLER_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_ACCOUNT_LOCKED_OUT)
        CHK_ERR_WIN32A(OR_INVALID_OXID)
        CHK_ERR_WIN32A(OR_INVALID_OID)
        CHK_ERR_WIN32A(OR_INVALID_SET)
        CHK_ERR_WIN32A(RPC_S_SEND_INCOMPLETE)
        CHK_ERR_WIN32A(RPC_S_INVALID_ASYNC_HANDLE)
        CHK_ERR_WIN32A(RPC_S_INVALID_ASYNC_CALL)
        CHK_ERR_WIN32A(RPC_X_PIPE_CLOSED)
        CHK_ERR_WIN32A(RPC_X_PIPE_DISCIPLINE_ERROR)
        CHK_ERR_WIN32A(RPC_X_PIPE_EMPTY)
        CHK_ERR_WIN32A(ERROR_NO_SITENAME)
        CHK_ERR_WIN32A(ERROR_CANT_ACCESS_FILE)
        CHK_ERR_WIN32A(ERROR_CANT_RESOLVE_FILENAME)
        CHK_ERR_WIN32A(ERROR_INVALID_PIXEL_FORMAT)
        CHK_ERR_WIN32A(ERROR_BAD_DRIVER)
        CHK_ERR_WIN32A(ERROR_INVALID_WINDOW_STYLE)
        CHK_ERR_WIN32A(ERROR_METAFILE_NOT_SUPPORTED)
        CHK_ERR_WIN32A(ERROR_TRANSFORM_NOT_SUPPORTED)
        CHK_ERR_WIN32A(ERROR_CLIPPING_NOT_SUPPORTED)
        CHK_ERR_WIN32A(ERROR_INVALID_CMM)
        CHK_ERR_WIN32A(ERROR_INVALID_PROFILE)
        CHK_ERR_WIN32A(ERROR_TAG_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_TAG_NOT_PRESENT)
        CHK_ERR_WIN32A(ERROR_DUPLICATE_TAG)
        CHK_ERR_WIN32A(ERROR_PROFILE_NOT_ASSOCIATED_WITH_DEVICE)
        CHK_ERR_WIN32A(ERROR_PROFILE_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_INVALID_COLORSPACE)
        CHK_ERR_WIN32A(ERROR_ICM_NOT_ENABLED)
        CHK_ERR_WIN32A(ERROR_DELETING_ICM_XFORM)
        CHK_ERR_WIN32A(ERROR_INVALID_TRANSFORM)
        CHK_ERR_WIN32A(ERROR_COLORSPACE_MISMATCH)
        CHK_ERR_WIN32A(ERROR_INVALID_COLORINDEX)
        CHK_ERR_WIN32A(ERROR_CONNECTED_OTHER_PASSWORD)
        CHK_ERR_WIN32A(ERROR_BAD_USERNAME)
        CHK_ERR_WIN32A(ERROR_NOT_CONNECTED)
        CHK_ERR_WIN32A(ERROR_OPEN_FILES)
        CHK_ERR_WIN32A(ERROR_ACTIVE_CONNECTIONS)
        CHK_ERR_WIN32A(ERROR_DEVICE_IN_USE)
        CHK_ERR_WIN32A(ERROR_UNKNOWN_PRINT_MONITOR)
        CHK_ERR_WIN32A(ERROR_PRINTER_DRIVER_IN_USE)
        CHK_ERR_WIN32A(ERROR_SPOOL_FILE_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_SPL_NO_STARTDOC)
        CHK_ERR_WIN32A(ERROR_SPL_NO_ADDJOB)
        CHK_ERR_WIN32A(ERROR_PRINT_PROCESSOR_ALREADY_INSTALLED)
        CHK_ERR_WIN32A(ERROR_PRINT_MONITOR_ALREADY_INSTALLED)
        CHK_ERR_WIN32A(ERROR_INVALID_PRINT_MONITOR)
        CHK_ERR_WIN32A(ERROR_PRINT_MONITOR_IN_USE)
        CHK_ERR_WIN32A(ERROR_PRINTER_HAS_JOBS_QUEUED)
        CHK_ERR_WIN32A(ERROR_SUCCESS_REBOOT_REQUIRED)
        CHK_ERR_WIN32A(ERROR_SUCCESS_RESTART_REQUIRED)
        CHK_ERR_WIN32A(ERROR_PRINTER_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_WINS_INTERNAL)
        CHK_ERR_WIN32A(ERROR_CAN_NOT_DEL_LOCAL_WINS)
        CHK_ERR_WIN32A(ERROR_STATIC_INIT)
        CHK_ERR_WIN32A(ERROR_INC_BACKUP)
        CHK_ERR_WIN32A(ERROR_FULL_BACKUP)
        CHK_ERR_WIN32A(ERROR_REC_NON_EXISTENT)
        CHK_ERR_WIN32A(ERROR_RPL_NOT_ALLOWED)
        CHK_ERR_WIN32A(ERROR_DHCP_ADDRESS_CONFLICT)
        CHK_ERR_WIN32A(ERROR_WMI_GUID_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_WMI_INSTANCE_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_WMI_ITEMID_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_WMI_TRY_AGAIN)
        CHK_ERR_WIN32A(ERROR_WMI_DP_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_WMI_UNRESOLVED_INSTANCE_REF)
        CHK_ERR_WIN32A(ERROR_WMI_ALREADY_ENABLED)
        CHK_ERR_WIN32A(ERROR_WMI_GUID_DISCONNECTED)
        CHK_ERR_WIN32A(ERROR_WMI_SERVER_UNAVAILABLE)
        CHK_ERR_WIN32A(ERROR_WMI_DP_FAILED)
        CHK_ERR_WIN32A(ERROR_WMI_INVALID_MOF)
        CHK_ERR_WIN32A(ERROR_WMI_INVALID_REGINFO)
        CHK_ERR_WIN32A(ERROR_WMI_ALREADY_DISABLED)
        CHK_ERR_WIN32A(ERROR_WMI_READ_ONLY)
        CHK_ERR_WIN32A(ERROR_WMI_SET_FAILURE)
        CHK_ERR_WIN32A(ERROR_INVALID_MEDIA)
        CHK_ERR_WIN32A(ERROR_INVALID_LIBRARY)
        CHK_ERR_WIN32A(ERROR_INVALID_MEDIA_POOL)
        CHK_ERR_WIN32A(ERROR_DRIVE_MEDIA_MISMATCH)
        CHK_ERR_WIN32A(ERROR_MEDIA_OFFLINE)
        CHK_ERR_WIN32A(ERROR_LIBRARY_OFFLINE)
        CHK_ERR_WIN32A(ERROR_EMPTY)
        CHK_ERR_WIN32A(ERROR_NOT_EMPTY)
        CHK_ERR_WIN32A(ERROR_MEDIA_UNAVAILABLE)
        CHK_ERR_WIN32A(ERROR_RESOURCE_DISABLED)
        CHK_ERR_WIN32A(ERROR_INVALID_CLEANER)
        CHK_ERR_WIN32A(ERROR_UNABLE_TO_CLEAN)
        CHK_ERR_WIN32A(ERROR_OBJECT_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_DATABASE_FAILURE)
        CHK_ERR_WIN32A(ERROR_DATABASE_FULL)
        CHK_ERR_WIN32A(ERROR_MEDIA_INCOMPATIBLE)
        CHK_ERR_WIN32A(ERROR_RESOURCE_NOT_PRESENT)
        CHK_ERR_WIN32A(ERROR_INVALID_OPERATION)
        CHK_ERR_WIN32A(ERROR_MEDIA_NOT_AVAILABLE)
        CHK_ERR_WIN32A(ERROR_DEVICE_NOT_AVAILABLE)
        CHK_ERR_WIN32A(ERROR_REQUEST_REFUSED)
        CHK_ERR_WIN32A(ERROR_INVALID_DRIVE_OBJECT)
        CHK_ERR_WIN32A(ERROR_LIBRARY_FULL)
        CHK_ERR_WIN32A(ERROR_MEDIUM_NOT_ACCESSIBLE)
        CHK_ERR_WIN32A(ERROR_UNABLE_TO_LOAD_MEDIUM)
        CHK_ERR_WIN32A(ERROR_UNABLE_TO_INVENTORY_DRIVE)
        CHK_ERR_WIN32A(ERROR_UNABLE_TO_INVENTORY_SLOT)
        CHK_ERR_WIN32A(ERROR_UNABLE_TO_INVENTORY_TRANSPORT)
        CHK_ERR_WIN32A(ERROR_TRANSPORT_FULL)
        CHK_ERR_WIN32A(ERROR_CONTROLLING_IEPORT)
        CHK_ERR_WIN32A(ERROR_UNABLE_TO_EJECT_MOUNTED_MEDIA)
        CHK_ERR_WIN32A(ERROR_CLEANER_SLOT_SET)
        CHK_ERR_WIN32A(ERROR_CLEANER_SLOT_NOT_SET)
        CHK_ERR_WIN32A(ERROR_CLEANER_CARTRIDGE_SPENT)
        CHK_ERR_WIN32A(ERROR_UNEXPECTED_OMID)
        CHK_ERR_WIN32A(ERROR_CANT_DELETE_LAST_ITEM)
        CHK_ERR_WIN32A(ERROR_MESSAGE_EXCEEDS_MAX_SIZE)
        CHK_ERR_WIN32A(ERROR_FILE_OFFLINE)
        CHK_ERR_WIN32A(ERROR_REMOTE_STORAGE_NOT_ACTIVE)
        CHK_ERR_WIN32A(ERROR_REMOTE_STORAGE_MEDIA_ERROR)
        CHK_ERR_WIN32A(ERROR_NOT_A_REPARSE_POINT)
        CHK_ERR_WIN32A(ERROR_REPARSE_ATTRIBUTE_CONFLICT)
        CHK_ERR_WIN32A(ERROR_INVALID_REPARSE_DATA)
        CHK_ERR_WIN32A(ERROR_DEPENDENT_RESOURCE_EXISTS)
        CHK_ERR_WIN32A(ERROR_DEPENDENCY_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_DEPENDENCY_ALREADY_EXISTS)
        CHK_ERR_WIN32A(ERROR_RESOURCE_NOT_ONLINE)
        CHK_ERR_WIN32A(ERROR_HOST_NODE_NOT_AVAILABLE)
        CHK_ERR_WIN32A(ERROR_RESOURCE_NOT_AVAILABLE)
        CHK_ERR_WIN32A(ERROR_RESOURCE_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_SHUTDOWN_CLUSTER)
        CHK_ERR_WIN32A(ERROR_CANT_EVICT_ACTIVE_NODE)
        CHK_ERR_WIN32A(ERROR_OBJECT_ALREADY_EXISTS)
        CHK_ERR_WIN32A(ERROR_OBJECT_IN_LIST)
        CHK_ERR_WIN32A(ERROR_GROUP_NOT_AVAILABLE)
        CHK_ERR_WIN32A(ERROR_GROUP_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_GROUP_NOT_ONLINE)
        CHK_ERR_WIN32A(ERROR_HOST_NODE_NOT_RESOURCE_OWNER)
        CHK_ERR_WIN32A(ERROR_HOST_NODE_NOT_GROUP_OWNER)
        CHK_ERR_WIN32A(ERROR_RESMON_CREATE_FAILED)
        CHK_ERR_WIN32A(ERROR_RESMON_ONLINE_FAILED)
        CHK_ERR_WIN32A(ERROR_RESOURCE_ONLINE)
        CHK_ERR_WIN32A(ERROR_QUORUM_RESOURCE)
        CHK_ERR_WIN32A(ERROR_NOT_QUORUM_CAPABLE)
        CHK_ERR_WIN32A(ERROR_CLUSTER_SHUTTING_DOWN)
        CHK_ERR_WIN32A(ERROR_INVALID_STATE)
        CHK_ERR_WIN32A(ERROR_RESOURCE_PROPERTIES_STORED)
        CHK_ERR_WIN32A(ERROR_NOT_QUORUM_CLASS)
        CHK_ERR_WIN32A(ERROR_CORE_RESOURCE)
        CHK_ERR_WIN32A(ERROR_QUORUM_RESOURCE_ONLINE_FAILED)
        CHK_ERR_WIN32A(ERROR_QUORUMLOG_OPEN_FAILED)
        CHK_ERR_WIN32A(ERROR_CLUSTERLOG_CORRUPT)
        CHK_ERR_WIN32A(ERROR_CLUSTERLOG_RECORD_EXCEEDS_MAXSIZE)
        CHK_ERR_WIN32A(ERROR_CLUSTERLOG_EXCEEDS_MAXSIZE)
        CHK_ERR_WIN32A(ERROR_CLUSTERLOG_CHKPOINT_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_CLUSTERLOG_NOT_ENOUGH_SPACE)
        CHK_ERR_WIN32A(ERROR_ENCRYPTION_FAILED)
        CHK_ERR_WIN32A(ERROR_DECRYPTION_FAILED)
        CHK_ERR_WIN32A(ERROR_FILE_ENCRYPTED)
        CHK_ERR_WIN32A(ERROR_NO_RECOVERY_POLICY)
        CHK_ERR_WIN32A(ERROR_NO_EFS)
        CHK_ERR_WIN32A(ERROR_WRONG_EFS)
        CHK_ERR_WIN32A(ERROR_NO_USER_KEYS)
        CHK_ERR_WIN32A(ERROR_FILE_NOT_ENCRYPTED)
        CHK_ERR_WIN32A(ERROR_NOT_EXPORT_FORMAT)
        CHK_ERR_WIN32A(ERROR_NO_BROWSER_SERVERS_FOUND)
        CHK_ERR_WIN32A(ERROR_CTX_WINSTATION_NAME_INVALID)
        CHK_ERR_WIN32A(ERROR_CTX_INVALID_PD)
        CHK_ERR_WIN32A(ERROR_CTX_PD_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_CTX_WD_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_CTX_CANNOT_MAKE_EVENTLOG_ENTRY)
        CHK_ERR_WIN32A(ERROR_CTX_SERVICE_NAME_COLLISION)
        CHK_ERR_WIN32A(ERROR_CTX_CLOSE_PENDING)
        CHK_ERR_WIN32A(ERROR_CTX_NO_OUTBUF)
        CHK_ERR_WIN32A(ERROR_CTX_MODEM_INF_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_CTX_INVALID_MODEMNAME)
        CHK_ERR_WIN32A(ERROR_CTX_MODEM_RESPONSE_ERROR)
        CHK_ERR_WIN32A(ERROR_CTX_MODEM_RESPONSE_TIMEOUT)
        CHK_ERR_WIN32A(ERROR_CTX_MODEM_RESPONSE_NO_CARRIER)
        CHK_ERR_WIN32A(ERROR_CTX_MODEM_RESPONSE_NO_DIALTONE)
        CHK_ERR_WIN32A(ERROR_CTX_MODEM_RESPONSE_BUSY)
        CHK_ERR_WIN32A(ERROR_CTX_MODEM_RESPONSE_VOICE)
        CHK_ERR_WIN32A(ERROR_CTX_TD_ERROR)
        CHK_ERR_WIN32A(ERROR_CTX_WINSTATION_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_CTX_WINSTATION_ALREADY_EXISTS)
        CHK_ERR_WIN32A(ERROR_CTX_WINSTATION_BUSY)
        CHK_ERR_WIN32A(ERROR_CTX_BAD_VIDEO_MODE)
        CHK_ERR_WIN32A(ERROR_CTX_GRAPHICS_INVALID)
        CHK_ERR_WIN32A(ERROR_CTX_LOGON_DISABLED)
        CHK_ERR_WIN32A(ERROR_CTX_NOT_CONSOLE)
        CHK_ERR_WIN32A(ERROR_CTX_CLIENT_QUERY_TIMEOUT)
        CHK_ERR_WIN32A(ERROR_CTX_CONSOLE_DISCONNECT)
        CHK_ERR_WIN32A(ERROR_CTX_CONSOLE_CONNECT)
        CHK_ERR_WIN32A(ERROR_CTX_SHADOW_DENIED)
        CHK_ERR_WIN32A(ERROR_CTX_WINSTATION_ACCESS_DENIED)
        CHK_ERR_WIN32A(ERROR_CTX_INVALID_WD)
        CHK_ERR_WIN32A(ERROR_CTX_SHADOW_INVALID)
        CHK_ERR_WIN32A(ERROR_CTX_SHADOW_DISABLED)
        CHK_ERR_WIN32A(FRS_ERR_INVALID_API_SEQUENCE)
        CHK_ERR_WIN32A(FRS_ERR_STARTING_SERVICE)
        CHK_ERR_WIN32A(FRS_ERR_STOPPING_SERVICE)
        CHK_ERR_WIN32A(FRS_ERR_INTERNAL_API)
        CHK_ERR_WIN32A(FRS_ERR_INTERNAL)
        CHK_ERR_WIN32A(FRS_ERR_SERVICE_COMM)
        CHK_ERR_WIN32A(FRS_ERR_INSUFFICIENT_PRIV)
        CHK_ERR_WIN32A(FRS_ERR_AUTHENTICATION)
        CHK_ERR_WIN32A(FRS_ERR_PARENT_INSUFFICIENT_PRIV)
        CHK_ERR_WIN32A(FRS_ERR_PARENT_AUTHENTICATION)
        CHK_ERR_WIN32A(FRS_ERR_CHILD_TO_PARENT_COMM)
        CHK_ERR_WIN32A(FRS_ERR_PARENT_TO_CHILD_COMM)
        CHK_ERR_WIN32A(FRS_ERR_SYSVOL_POPULATE)
        CHK_ERR_WIN32A(FRS_ERR_SYSVOL_POPULATE_TIMEOUT)
        CHK_ERR_WIN32A(FRS_ERR_SYSVOL_IS_BUSY)
        CHK_ERR_WIN32A(FRS_ERR_SYSVOL_DEMOTE)
        CHK_ERR_WIN32A(FRS_ERR_INVALID_SERVICE_PARAMETER)
//        CHK_ERR_WIN32A(DS_S_SUCCESS)
        CHK_ERR_WIN32A(ERROR_DS_NOT_INSTALLED)
        CHK_ERR_WIN32A(ERROR_DS_MEMBERSHIP_EVALUATED_LOCALLY)
        CHK_ERR_WIN32A(ERROR_DS_NO_ATTRIBUTE_OR_VALUE)
        CHK_ERR_WIN32A(ERROR_DS_INVALID_ATTRIBUTE_SYNTAX)
        CHK_ERR_WIN32A(ERROR_DS_ATTRIBUTE_TYPE_UNDEFINED)
        CHK_ERR_WIN32A(ERROR_DS_ATTRIBUTE_OR_VALUE_EXISTS)
        CHK_ERR_WIN32A(ERROR_DS_BUSY)
        CHK_ERR_WIN32A(ERROR_DS_UNAVAILABLE)
        CHK_ERR_WIN32A(ERROR_DS_NO_RIDS_ALLOCATED)
        CHK_ERR_WIN32A(ERROR_DS_NO_MORE_RIDS)
        CHK_ERR_WIN32A(ERROR_DS_INCORRECT_ROLE_OWNER)
        CHK_ERR_WIN32A(ERROR_DS_RIDMGR_INIT_ERROR)
        CHK_ERR_WIN32A(ERROR_DS_OBJ_CLASS_VIOLATION)
        CHK_ERR_WIN32A(ERROR_DS_CANT_ON_NON_LEAF)
        CHK_ERR_WIN32A(ERROR_DS_CANT_ON_RDN)
        CHK_ERR_WIN32A(ERROR_DS_CANT_MOD_OBJ_CLASS)
        CHK_ERR_WIN32A(ERROR_DS_CROSS_DOM_MOVE_ERROR)
        CHK_ERR_WIN32A(ERROR_DS_GC_NOT_AVAILABLE)
        CHK_ERR_WIN32A(ERROR_SHARED_POLICY)
        CHK_ERR_WIN32A(ERROR_POLICY_OBJECT_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_POLICY_ONLY_IN_DS)
        CHK_ERR_WIN32A(ERROR_PROMOTION_ACTIVE)
        CHK_ERR_WIN32A(ERROR_NO_PROMOTION_ACTIVE)
        CHK_ERR_WIN32A(ERROR_DS_OPERATIONS_ERROR)
        CHK_ERR_WIN32A(ERROR_DS_PROTOCOL_ERROR)
        CHK_ERR_WIN32A(ERROR_DS_TIMELIMIT_EXCEEDED)
        CHK_ERR_WIN32A(ERROR_DS_SIZELIMIT_EXCEEDED)
        CHK_ERR_WIN32A(ERROR_DS_ADMIN_LIMIT_EXCEEDED)
        CHK_ERR_WIN32A(ERROR_DS_COMPARE_FALSE)
        CHK_ERR_WIN32A(ERROR_DS_COMPARE_TRUE)
        CHK_ERR_WIN32A(ERROR_DS_AUTH_METHOD_NOT_SUPPORTED)
        CHK_ERR_WIN32A(ERROR_DS_STRONG_AUTH_REQUIRED)
        CHK_ERR_WIN32A(ERROR_DS_INAPPROPRIATE_AUTH)
        CHK_ERR_WIN32A(ERROR_DS_AUTH_UNKNOWN)
        CHK_ERR_WIN32A(ERROR_DS_REFERRAL)
        CHK_ERR_WIN32A(ERROR_DS_UNAVAILABLE_CRIT_EXTENSION)
        CHK_ERR_WIN32A(ERROR_DS_CONFIDENTIALITY_REQUIRED)
        CHK_ERR_WIN32A(ERROR_DS_INAPPROPRIATE_MATCHING)
        CHK_ERR_WIN32A(ERROR_DS_CONSTRAINT_VIOLATION)
        CHK_ERR_WIN32A(ERROR_DS_NO_SUCH_OBJECT)
        CHK_ERR_WIN32A(ERROR_DS_ALIAS_PROBLEM)
        CHK_ERR_WIN32A(ERROR_DS_INVALID_DN_SYNTAX)
        CHK_ERR_WIN32A(ERROR_DS_IS_LEAF)
        CHK_ERR_WIN32A(ERROR_DS_ALIAS_DEREF_PROBLEM)
        CHK_ERR_WIN32A(ERROR_DS_UNWILLING_TO_PERFORM)
        CHK_ERR_WIN32A(ERROR_DS_LOOP_DETECT)
        CHK_ERR_WIN32A(ERROR_DS_NAMING_VIOLATION)
        CHK_ERR_WIN32A(ERROR_DS_OBJECT_RESULTS_TOO_LARGE)
        CHK_ERR_WIN32A(ERROR_DS_AFFECTS_MULTIPLE_DSAS)
        CHK_ERR_WIN32A(ERROR_DS_SERVER_DOWN)
        CHK_ERR_WIN32A(ERROR_DS_LOCAL_ERROR)
        CHK_ERR_WIN32A(ERROR_DS_ENCODING_ERROR)
        CHK_ERR_WIN32A(ERROR_DS_DECODING_ERROR)
        CHK_ERR_WIN32A(ERROR_DS_FILTER_UNKNOWN)
        CHK_ERR_WIN32A(ERROR_DS_PARAM_ERROR)
        CHK_ERR_WIN32A(ERROR_DS_NOT_SUPPORTED)
        CHK_ERR_WIN32A(ERROR_DS_NO_RESULTS_RETURNED)
        CHK_ERR_WIN32A(ERROR_DS_CONTROL_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_DS_CLIENT_LOOP)
        CHK_ERR_WIN32A(ERROR_DS_REFERRAL_LIMIT_EXCEEDED)
        CHK_ERR_WIN32A(ERROR_DS_ROOT_MUST_BE_NC)
        CHK_ERR_WIN32A(ERROR_DS_ADD_REPLICA_INHIBITED)
        CHK_ERR_WIN32A(ERROR_DS_ATT_NOT_DEF_IN_SCHEMA)
        CHK_ERR_WIN32A(ERROR_DS_MAX_OBJ_SIZE_EXCEEDED)
        CHK_ERR_WIN32A(ERROR_DS_OBJ_STRING_NAME_EXISTS)
        CHK_ERR_WIN32A(ERROR_DS_NO_RDN_DEFINED_IN_SCHEMA)
        CHK_ERR_WIN32A(ERROR_DS_RDN_DOESNT_MATCH_SCHEMA)
        CHK_ERR_WIN32A(ERROR_DS_NO_REQUESTED_ATTS_FOUND)
        CHK_ERR_WIN32A(ERROR_DS_USER_BUFFER_TO_SMALL)
        CHK_ERR_WIN32A(ERROR_DS_ATT_IS_NOT_ON_OBJ)
        CHK_ERR_WIN32A(ERROR_DS_ILLEGAL_MOD_OPERATION)
        CHK_ERR_WIN32A(ERROR_DS_OBJ_TOO_LARGE)
        CHK_ERR_WIN32A(ERROR_DS_BAD_INSTANCE_TYPE)
        CHK_ERR_WIN32A(ERROR_DS_MASTERDSA_REQUIRED)
        CHK_ERR_WIN32A(ERROR_DS_OBJECT_CLASS_REQUIRED)
        CHK_ERR_WIN32A(ERROR_DS_MISSING_REQUIRED_ATT)
        CHK_ERR_WIN32A(ERROR_DS_ATT_NOT_DEF_FOR_CLASS)
        CHK_ERR_WIN32A(ERROR_DS_ATT_ALREADY_EXISTS)
        CHK_ERR_WIN32A(ERROR_DS_CANT_ADD_ATT_VALUES)
        CHK_ERR_WIN32A(ERROR_DS_SINGLE_VALUE_CONSTRAINT)
        CHK_ERR_WIN32A(ERROR_DS_RANGE_CONSTRAINT)
        CHK_ERR_WIN32A(ERROR_DS_ATT_VAL_ALREADY_EXISTS)
        CHK_ERR_WIN32A(ERROR_DS_CANT_REM_MISSING_ATT)
        CHK_ERR_WIN32A(ERROR_DS_CANT_REM_MISSING_ATT_VAL)
        CHK_ERR_WIN32A(ERROR_DS_ROOT_CANT_BE_SUBREF)
        CHK_ERR_WIN32A(ERROR_DS_NO_CHAINING)
        CHK_ERR_WIN32A(ERROR_DS_NO_CHAINED_EVAL)
        CHK_ERR_WIN32A(ERROR_DS_NO_PARENT_OBJECT)
        CHK_ERR_WIN32A(ERROR_DS_PARENT_IS_AN_ALIAS)
        CHK_ERR_WIN32A(ERROR_DS_CANT_MIX_MASTER_AND_REPS)
        CHK_ERR_WIN32A(ERROR_DS_CHILDREN_EXIST)
        CHK_ERR_WIN32A(ERROR_DS_OBJ_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_DS_ALIASED_OBJ_MISSING)
        CHK_ERR_WIN32A(ERROR_DS_BAD_NAME_SYNTAX)
        CHK_ERR_WIN32A(ERROR_DS_ALIAS_POINTS_TO_ALIAS)
        CHK_ERR_WIN32A(ERROR_DS_CANT_DEREF_ALIAS)
        CHK_ERR_WIN32A(ERROR_DS_OUT_OF_SCOPE)
        CHK_ERR_WIN32A(ERROR_DS_CANT_DELETE_DSA_OBJ)
        CHK_ERR_WIN32A(ERROR_DS_GENERIC_ERROR)
        CHK_ERR_WIN32A(ERROR_DS_DSA_MUST_BE_INT_MASTER)
        CHK_ERR_WIN32A(ERROR_DS_CLASS_NOT_DSA)
        CHK_ERR_WIN32A(ERROR_DS_INSUFF_ACCESS_RIGHTS)
        CHK_ERR_WIN32A(ERROR_DS_ILLEGAL_SUPERIOR)
        CHK_ERR_WIN32A(ERROR_DS_ATTRIBUTE_OWNED_BY_SAM)
        CHK_ERR_WIN32A(ERROR_DS_NAME_TOO_MANY_PARTS)
        CHK_ERR_WIN32A(ERROR_DS_NAME_TOO_LONG)
        CHK_ERR_WIN32A(ERROR_DS_NAME_VALUE_TOO_LONG)
        CHK_ERR_WIN32A(ERROR_DS_NAME_UNPARSEABLE)
        CHK_ERR_WIN32A(ERROR_DS_NAME_TYPE_UNKNOWN)
        CHK_ERR_WIN32A(ERROR_DS_NOT_AN_OBJECT)
        CHK_ERR_WIN32A(ERROR_DS_SEC_DESC_TOO_SHORT)
        CHK_ERR_WIN32A(ERROR_DS_SEC_DESC_INVALID)
        CHK_ERR_WIN32A(ERROR_DS_NO_DELETED_NAME)
        CHK_ERR_WIN32A(ERROR_DS_SUBREF_MUST_HAVE_PARENT)
        CHK_ERR_WIN32A(ERROR_DS_NCNAME_MUST_BE_NC)
        CHK_ERR_WIN32A(ERROR_DS_CANT_ADD_SYSTEM_ONLY)
        CHK_ERR_WIN32A(ERROR_DS_CLASS_MUST_BE_CONCRETE)
        CHK_ERR_WIN32A(ERROR_DS_INVALID_DMD)
        CHK_ERR_WIN32A(ERROR_DS_OBJ_GUID_EXISTS)
        CHK_ERR_WIN32A(ERROR_DS_NOT_ON_BACKLINK)
        CHK_ERR_WIN32A(ERROR_DS_NO_CROSSREF_FOR_NC)
        CHK_ERR_WIN32A(ERROR_DS_SHUTTING_DOWN)
        CHK_ERR_WIN32A(ERROR_DS_UNKNOWN_OPERATION)
        CHK_ERR_WIN32A(ERROR_DS_INVALID_ROLE_OWNER)
        CHK_ERR_WIN32A(ERROR_DS_COULDNT_CONTACT_FSMO)
        CHK_ERR_WIN32A(ERROR_DS_CROSS_NC_DN_RENAME)
        CHK_ERR_WIN32A(ERROR_DS_CANT_MOD_SYSTEM_ONLY)
        CHK_ERR_WIN32A(ERROR_DS_REPLICATOR_ONLY)
        CHK_ERR_WIN32A(ERROR_DS_OBJ_CLASS_NOT_DEFINED)
        CHK_ERR_WIN32A(ERROR_DS_OBJ_CLASS_NOT_SUBCLASS)
        CHK_ERR_WIN32A(ERROR_DS_NAME_REFERENCE_INVALID)
        CHK_ERR_WIN32A(ERROR_DS_CROSS_REF_EXISTS)
        CHK_ERR_WIN32A(ERROR_DS_CANT_DEL_MASTER_CROSSREF)
        CHK_ERR_WIN32A(ERROR_DS_SUBTREE_NOTIFY_NOT_NC_HEAD)
        CHK_ERR_WIN32A(ERROR_DS_NOTIFY_FILTER_TOO_COMPLEX)
        CHK_ERR_WIN32A(ERROR_DS_DUP_RDN)
        CHK_ERR_WIN32A(ERROR_DS_DUP_OID)
        CHK_ERR_WIN32A(ERROR_DS_DUP_MAPI_ID)
        CHK_ERR_WIN32A(ERROR_DS_DUP_SCHEMA_ID_GUID)
        CHK_ERR_WIN32A(ERROR_DS_DUP_LDAP_DISPLAY_NAME)
        CHK_ERR_WIN32A(ERROR_DS_SEMANTIC_ATT_TEST)
        CHK_ERR_WIN32A(ERROR_DS_SYNTAX_MISMATCH)
        CHK_ERR_WIN32A(ERROR_DS_EXISTS_IN_MUST_HAVE)
        CHK_ERR_WIN32A(ERROR_DS_EXISTS_IN_MAY_HAVE)
        CHK_ERR_WIN32A(ERROR_DS_NONEXISTENT_MAY_HAVE)
        CHK_ERR_WIN32A(ERROR_DS_NONEXISTENT_MUST_HAVE)
        CHK_ERR_WIN32A(ERROR_DS_AUX_CLS_TEST_FAIL)
        CHK_ERR_WIN32A(ERROR_DS_NONEXISTENT_POSS_SUP)
        CHK_ERR_WIN32A(ERROR_DS_SUB_CLS_TEST_FAIL)
        CHK_ERR_WIN32A(ERROR_DS_BAD_RDN_ATT_ID_SYNTAX)
        CHK_ERR_WIN32A(ERROR_DS_EXISTS_IN_AUX_CLS)
        CHK_ERR_WIN32A(ERROR_DS_EXISTS_IN_SUB_CLS)
        CHK_ERR_WIN32A(ERROR_DS_EXISTS_IN_POSS_SUP)
        CHK_ERR_WIN32A(ERROR_DS_RECALCSCHEMA_FAILED)
        CHK_ERR_WIN32A(ERROR_DS_TREE_DELETE_NOT_FINISHED)
        CHK_ERR_WIN32A(ERROR_DS_CANT_DELETE)
        CHK_ERR_WIN32A(ERROR_DS_ATT_SCHEMA_REQ_ID)
        CHK_ERR_WIN32A(ERROR_DS_BAD_ATT_SCHEMA_SYNTAX)
        CHK_ERR_WIN32A(ERROR_DS_CANT_CACHE_ATT)
        CHK_ERR_WIN32A(ERROR_DS_CANT_CACHE_CLASS)
        CHK_ERR_WIN32A(ERROR_DS_CANT_REMOVE_ATT_CACHE)
        CHK_ERR_WIN32A(ERROR_DS_CANT_REMOVE_CLASS_CACHE)
        CHK_ERR_WIN32A(ERROR_DS_CANT_RETRIEVE_DN)
        CHK_ERR_WIN32A(ERROR_DS_MISSING_SUPREF)
        CHK_ERR_WIN32A(ERROR_DS_CANT_RETRIEVE_INSTANCE)
        CHK_ERR_WIN32A(ERROR_DS_CODE_INCONSISTENCY)
        CHK_ERR_WIN32A(ERROR_DS_DATABASE_ERROR)
        CHK_ERR_WIN32A(ERROR_DS_GOVERNSID_MISSING)
        CHK_ERR_WIN32A(ERROR_DS_MISSING_EXPECTED_ATT)
        CHK_ERR_WIN32A(ERROR_DS_NCNAME_MISSING_CR_REF)
        CHK_ERR_WIN32A(ERROR_DS_SECURITY_CHECKING_ERROR)
        CHK_ERR_WIN32A(ERROR_DS_SCHEMA_NOT_LOADED)
        CHK_ERR_WIN32A(ERROR_DS_SCHEMA_ALLOC_FAILED)
        CHK_ERR_WIN32A(ERROR_DS_ATT_SCHEMA_REQ_SYNTAX)
        CHK_ERR_WIN32A(ERROR_DS_GCVERIFY_ERROR)
        CHK_ERR_WIN32A(ERROR_DS_DRA_SCHEMA_MISMATCH)
        CHK_ERR_WIN32A(ERROR_DS_CANT_FIND_DSA_OBJ)
        CHK_ERR_WIN32A(ERROR_DS_CANT_FIND_EXPECTED_NC)
        CHK_ERR_WIN32A(ERROR_DS_CANT_FIND_NC_IN_CACHE)
        CHK_ERR_WIN32A(ERROR_DS_CANT_RETRIEVE_CHILD)
        CHK_ERR_WIN32A(ERROR_DS_SECURITY_ILLEGAL_MODIFY)
        CHK_ERR_WIN32A(ERROR_DS_CANT_REPLACE_HIDDEN_REC)
        CHK_ERR_WIN32A(ERROR_DS_BAD_HIERARCHY_FILE)
        CHK_ERR_WIN32A(ERROR_DS_BUILD_HIERARCHY_TABLE_FAILED)
        CHK_ERR_WIN32A(ERROR_DS_CONFIG_PARAM_MISSING)
        CHK_ERR_WIN32A(ERROR_DS_COUNTING_AB_INDICES_FAILED)
        CHK_ERR_WIN32A(ERROR_DS_HIERARCHY_TABLE_MALLOC_FAILED)
        CHK_ERR_WIN32A(ERROR_DS_INTERNAL_FAILURE)
        CHK_ERR_WIN32A(ERROR_DS_UNKNOWN_ERROR)
        CHK_ERR_WIN32A(ERROR_DS_ROOT_REQUIRES_CLASS_TOP)
        CHK_ERR_WIN32A(ERROR_DS_REFUSING_FSMO_ROLES)
        CHK_ERR_WIN32A(ERROR_DS_MISSING_FSMO_SETTINGS)
        CHK_ERR_WIN32A(ERROR_DS_UNABLE_TO_SURRENDER_ROLES)
        CHK_ERR_WIN32A(ERROR_DS_DRA_GENERIC)
        CHK_ERR_WIN32A(ERROR_DS_DRA_INVALID_PARAMETER)
        CHK_ERR_WIN32A(ERROR_DS_DRA_BUSY)
        CHK_ERR_WIN32A(ERROR_DS_DRA_BAD_DN)
        CHK_ERR_WIN32A(ERROR_DS_DRA_BAD_NC)
        CHK_ERR_WIN32A(ERROR_DS_DRA_DN_EXISTS)
        CHK_ERR_WIN32A(ERROR_DS_DRA_INTERNAL_ERROR)
        CHK_ERR_WIN32A(ERROR_DS_DRA_INCONSISTENT_DIT)
        CHK_ERR_WIN32A(ERROR_DS_DRA_CONNECTION_FAILED)
        CHK_ERR_WIN32A(ERROR_DS_DRA_BAD_INSTANCE_TYPE)
        CHK_ERR_WIN32A(ERROR_DS_DRA_OUT_OF_MEM)
        CHK_ERR_WIN32A(ERROR_DS_DRA_MAIL_PROBLEM)
        CHK_ERR_WIN32A(ERROR_DS_DRA_REF_ALREADY_EXISTS)
        CHK_ERR_WIN32A(ERROR_DS_DRA_REF_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_DS_DRA_OBJ_IS_REP_SOURCE)
        CHK_ERR_WIN32A(ERROR_DS_DRA_DB_ERROR)
        CHK_ERR_WIN32A(ERROR_DS_DRA_NO_REPLICA)
        CHK_ERR_WIN32A(ERROR_DS_DRA_ACCESS_DENIED)
        CHK_ERR_WIN32A(ERROR_DS_DRA_NOT_SUPPORTED)
        CHK_ERR_WIN32A(ERROR_DS_DRA_RPC_CANCELLED)
        CHK_ERR_WIN32A(ERROR_DS_DRA_SOURCE_DISABLED)
        CHK_ERR_WIN32A(ERROR_DS_DRA_SINK_DISABLED)
        CHK_ERR_WIN32A(ERROR_DS_DRA_NAME_COLLISION)
        CHK_ERR_WIN32A(ERROR_DS_DRA_SOURCE_REINSTALLED)
        CHK_ERR_WIN32A(ERROR_DS_DRA_MISSING_PARENT)
        CHK_ERR_WIN32A(ERROR_DS_DRA_PREEMPTED)
        CHK_ERR_WIN32A(ERROR_DS_DRA_ABANDON_SYNC)
        CHK_ERR_WIN32A(ERROR_DS_DRA_SHUTDOWN)
        CHK_ERR_WIN32A(ERROR_DS_DRA_INCOMPATIBLE_PARTIAL_SET)
        CHK_ERR_WIN32A(ERROR_DS_DRA_SOURCE_IS_PARTIAL_REPLICA)
        CHK_ERR_WIN32A(ERROR_DS_DRA_EXTN_CONNECTION_FAILED)
        CHK_ERR_WIN32A(ERROR_DS_INSTALL_SCHEMA_MISMATCH)
        CHK_ERR_WIN32A(ERROR_DS_DUP_LINK_ID)
        CHK_ERR_WIN32A(ERROR_DS_NAME_ERROR_RESOLVING)
        CHK_ERR_WIN32A(ERROR_DS_NAME_ERROR_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_DS_NAME_ERROR_NOT_UNIQUE)
        CHK_ERR_WIN32A(ERROR_DS_NAME_ERROR_NO_MAPPING)
        CHK_ERR_WIN32A(ERROR_DS_NAME_ERROR_DOMAIN_ONLY)
        CHK_ERR_WIN32A(ERROR_DS_NAME_ERROR_NO_SYNTACTICAL_MAPPING)
        CHK_ERR_WIN32A(ERROR_DS_CONSTRUCTED_ATT_MOD)
        CHK_ERR_WIN32A(ERROR_DS_WRONG_OM_OBJ_CLASS)
        CHK_ERR_WIN32A(ERROR_DS_DRA_REPL_PENDING)
        CHK_ERR_WIN32A(DNS_ERROR_RESPONSE_CODES_BASE)
//        CHK_ERR_WIN32A(DNS_ERROR_MASK)
        CHK_ERR_WIN32A(DNS_ERROR_RCODE_FORMAT_ERROR)
        CHK_ERR_WIN32A(DNS_ERROR_RCODE_SERVER_FAILURE)
        CHK_ERR_WIN32A(DNS_ERROR_RCODE_NAME_ERROR)
        CHK_ERR_WIN32A(DNS_ERROR_RCODE_NOT_IMPLEMENTED)
        CHK_ERR_WIN32A(DNS_ERROR_RCODE_REFUSED)
        CHK_ERR_WIN32A(DNS_ERROR_RCODE_YXDOMAIN)
        CHK_ERR_WIN32A(DNS_ERROR_RCODE_YXRRSET)
        CHK_ERR_WIN32A(DNS_ERROR_RCODE_NXRRSET)
        CHK_ERR_WIN32A(DNS_ERROR_RCODE_NOTAUTH)
        CHK_ERR_WIN32A(DNS_ERROR_RCODE_NOTZONE)
        CHK_ERR_WIN32A(DNS_ERROR_RCODE_BADSIG)
        CHK_ERR_WIN32A(DNS_ERROR_RCODE_BADKEY)
        CHK_ERR_WIN32A(DNS_ERROR_RCODE_BADTIME)
        CHK_ERR_WIN32A(DNS_ERROR_PACKET_FMT_BASE)
        CHK_ERR_WIN32A(DNS_INFO_NO_RECORDS)
        CHK_ERR_WIN32A(DNS_ERROR_BAD_PACKET)
        CHK_ERR_WIN32A(DNS_ERROR_NO_PACKET)
        CHK_ERR_WIN32A(DNS_ERROR_RCODE)
        CHK_ERR_WIN32A(DNS_ERROR_UNSECURE_PACKET)
        CHK_ERR_WIN32A(DNS_ERROR_GENERAL_API_BASE)
        CHK_ERR_WIN32A(DNS_ERROR_INVALID_TYPE)
        CHK_ERR_WIN32A(DNS_ERROR_INVALID_IP_ADDRESS)
        CHK_ERR_WIN32A(DNS_ERROR_INVALID_PROPERTY)
        CHK_ERR_WIN32A(DNS_ERROR_TRY_AGAIN_LATER)
        CHK_ERR_WIN32A(DNS_ERROR_NOT_UNIQUE)
        CHK_ERR_WIN32A(DNS_ERROR_NON_RFC_NAME)
        CHK_ERR_WIN32A(DNS_STATUS_FQDN)
        CHK_ERR_WIN32A(DNS_STATUS_DOTTED_NAME)
        CHK_ERR_WIN32A(DNS_STATUS_SINGLE_PART_NAME)
        CHK_ERR_WIN32A(DNS_ERROR_ZONE_BASE)
        CHK_ERR_WIN32A(DNS_ERROR_ZONE_DOES_NOT_EXIST)
        CHK_ERR_WIN32A(DNS_ERROR_NO_ZONE_INFO)
        CHK_ERR_WIN32A(DNS_ERROR_INVALID_ZONE_OPERATION)
        CHK_ERR_WIN32A(DNS_ERROR_ZONE_CONFIGURATION_ERROR)
        CHK_ERR_WIN32A(DNS_ERROR_ZONE_HAS_NO_SOA_RECORD)
        CHK_ERR_WIN32A(DNS_ERROR_ZONE_HAS_NO_NS_RECORDS)
        CHK_ERR_WIN32A(DNS_ERROR_ZONE_LOCKED)
        CHK_ERR_WIN32A(DNS_ERROR_ZONE_CREATION_FAILED)
        CHK_ERR_WIN32A(DNS_ERROR_ZONE_ALREADY_EXISTS)
        CHK_ERR_WIN32A(DNS_ERROR_AUTOZONE_ALREADY_EXISTS)
        CHK_ERR_WIN32A(DNS_ERROR_INVALID_ZONE_TYPE)
        CHK_ERR_WIN32A(DNS_ERROR_SECONDARY_REQUIRES_MASTER_IP)
        CHK_ERR_WIN32A(DNS_ERROR_ZONE_NOT_SECONDARY)
        CHK_ERR_WIN32A(DNS_ERROR_NEED_SECONDARY_ADDRESSES)
        CHK_ERR_WIN32A(DNS_ERROR_WINS_INIT_FAILED)
        CHK_ERR_WIN32A(DNS_ERROR_NEED_WINS_SERVERS)
        CHK_ERR_WIN32A(DNS_ERROR_NBSTAT_INIT_FAILED)
        CHK_ERR_WIN32A(DNS_ERROR_SOA_DELETE_INVALID)
        CHK_ERR_WIN32A(DNS_ERROR_DATAFILE_BASE)
        CHK_ERR_WIN32A(DNS_ERROR_PRIMARY_REQUIRES_DATAFILE)
        CHK_ERR_WIN32A(DNS_ERROR_INVALID_DATAFILE_NAME)
        CHK_ERR_WIN32A(DNS_ERROR_DATAFILE_OPEN_FAILURE)
        CHK_ERR_WIN32A(DNS_ERROR_FILE_WRITEBACK_FAILED)
        CHK_ERR_WIN32A(DNS_ERROR_DATAFILE_PARSING)
        CHK_ERR_WIN32A(DNS_ERROR_DATABASE_BASE)
        CHK_ERR_WIN32A(DNS_ERROR_RECORD_DOES_NOT_EXIST)
        CHK_ERR_WIN32A(DNS_ERROR_RECORD_FORMAT)
        CHK_ERR_WIN32A(DNS_ERROR_NODE_CREATION_FAILED)
        CHK_ERR_WIN32A(DNS_ERROR_UNKNOWN_RECORD_TYPE)
        CHK_ERR_WIN32A(DNS_ERROR_RECORD_TIMED_OUT)
        CHK_ERR_WIN32A(DNS_ERROR_NAME_NOT_IN_ZONE)
        CHK_ERR_WIN32A(DNS_ERROR_CNAME_LOOP)
        CHK_ERR_WIN32A(DNS_ERROR_NODE_IS_CNAME)
        CHK_ERR_WIN32A(DNS_ERROR_CNAME_COLLISION)
        CHK_ERR_WIN32A(DNS_ERROR_RECORD_ONLY_AT_ZONE_ROOT)
        CHK_ERR_WIN32A(DNS_ERROR_RECORD_ALREADY_EXISTS)
        CHK_ERR_WIN32A(DNS_ERROR_SECONDARY_DATA)
        CHK_ERR_WIN32A(DNS_ERROR_NO_CREATE_CACHE_DATA)
        CHK_ERR_WIN32A(DNS_ERROR_NAME_DOES_NOT_EXIST)
        CHK_ERR_WIN32A(DNS_WARNING_PTR_CREATE_FAILED)
        CHK_ERR_WIN32A(DNS_WARNING_DOMAIN_UNDELETED)
        CHK_ERR_WIN32A(DNS_ERROR_DS_UNAVAILABLE)
        CHK_ERR_WIN32A(DNS_ERROR_DS_ZONE_ALREADY_EXISTS)
        CHK_ERR_WIN32A(DNS_ERROR_NO_BOOTFILE_IF_DS_ZONE)
        CHK_ERR_WIN32A(DNS_ERROR_OPERATION_BASE)
        CHK_ERR_WIN32A(DNS_INFO_AXFR_COMPLETE)
        CHK_ERR_WIN32A(DNS_ERROR_AXFR)
        CHK_ERR_WIN32A(DNS_INFO_ADDED_LOCAL_WINS)
        CHK_ERR_WIN32A(DNS_ERROR_SECURE_BASE)
        CHK_ERR_WIN32A(DNS_STATUS_CONTINUE_NEEDED)
        CHK_ERR_WIN32A(DNS_ERROR_SETUP_BASE)
        CHK_ERR_WIN32A(DNS_ERROR_NO_TCPIP)
        CHK_ERR_WIN32A(DNS_ERROR_NO_DNS_SERVERS)
        CHK_ERR_WIN32A(WSABASEERR)
        CHK_ERR_WIN32A(WSAEINTR)
        CHK_ERR_WIN32A(WSAEBADF)
        CHK_ERR_WIN32A(WSAEACCES)
        CHK_ERR_WIN32A(WSAEFAULT)
        CHK_ERR_WIN32A(WSAEINVAL)
        CHK_ERR_WIN32A(WSAEMFILE)
        CHK_ERR_WIN32A(WSAEWOULDBLOCK)
        CHK_ERR_WIN32A(WSAEINPROGRESS)
        CHK_ERR_WIN32A(WSAEALREADY)
        CHK_ERR_WIN32A(WSAENOTSOCK)
        CHK_ERR_WIN32A(WSAEDESTADDRREQ)
        CHK_ERR_WIN32A(WSAEMSGSIZE)
        CHK_ERR_WIN32A(WSAEPROTOTYPE)
        CHK_ERR_WIN32A(WSAENOPROTOOPT)
        CHK_ERR_WIN32A(WSAEPROTONOSUPPORT)
        CHK_ERR_WIN32A(WSAESOCKTNOSUPPORT)
        CHK_ERR_WIN32A(WSAEOPNOTSUPP)
        CHK_ERR_WIN32A(WSAEPFNOSUPPORT)
        CHK_ERR_WIN32A(WSAEAFNOSUPPORT)
        CHK_ERR_WIN32A(WSAEADDRINUSE)
        CHK_ERR_WIN32A(WSAEADDRNOTAVAIL)
        CHK_ERR_WIN32A(WSAENETDOWN)
        CHK_ERR_WIN32A(WSAENETUNREACH)
        CHK_ERR_WIN32A(WSAENETRESET)
        CHK_ERR_WIN32A(WSAECONNABORTED)
        CHK_ERR_WIN32A(WSAECONNRESET)
        CHK_ERR_WIN32A(WSAENOBUFS)
        CHK_ERR_WIN32A(WSAEISCONN)
        CHK_ERR_WIN32A(WSAENOTCONN)
        CHK_ERR_WIN32A(WSAESHUTDOWN)
        CHK_ERR_WIN32A(WSAETOOMANYREFS)
        CHK_ERR_WIN32A(WSAETIMEDOUT)
        CHK_ERR_WIN32A(WSAECONNREFUSED)
        CHK_ERR_WIN32A(WSAELOOP)
        CHK_ERR_WIN32A(WSAENAMETOOLONG)
        CHK_ERR_WIN32A(WSAEHOSTDOWN)
        CHK_ERR_WIN32A(WSAEHOSTUNREACH)
        CHK_ERR_WIN32A(WSAENOTEMPTY)
        CHK_ERR_WIN32A(WSAEPROCLIM)
        CHK_ERR_WIN32A(WSAEUSERS)
        CHK_ERR_WIN32A(WSAEDQUOT)
        CHK_ERR_WIN32A(WSAESTALE)
        CHK_ERR_WIN32A(WSAEREMOTE)
        CHK_ERR_WIN32A(WSASYSNOTREADY)
        CHK_ERR_WIN32A(WSAVERNOTSUPPORTED)
        CHK_ERR_WIN32A(WSANOTINITIALISED)
        CHK_ERR_WIN32A(WSAEDISCON)
        CHK_ERR_WIN32A(WSAENOMORE)
        CHK_ERR_WIN32A(WSAECANCELLED)
        CHK_ERR_WIN32A(WSAEINVALIDPROCTABLE)
        CHK_ERR_WIN32A(WSAEINVALIDPROVIDER)
        CHK_ERR_WIN32A(WSAEPROVIDERFAILEDINIT)
        CHK_ERR_WIN32A(WSASYSCALLFAILURE)
        CHK_ERR_WIN32A(WSASERVICE_NOT_FOUND)
        CHK_ERR_WIN32A(WSATYPE_NOT_FOUND)
        CHK_ERR_WIN32A(WSA_E_NO_MORE)
        CHK_ERR_WIN32A(WSA_E_CANCELLED)
        CHK_ERR_WIN32A(WSAEREFUSED)
        CHK_ERR_WIN32A(WSAHOST_NOT_FOUND)
        CHK_ERR_WIN32A(WSATRY_AGAIN)
        CHK_ERR_WIN32A(WSANO_RECOVERY)
        CHK_ERR_WIN32A(WSANO_DATA)
        CHK_ERR_WIN32A(WSA_QOS_RECEIVERS)
        CHK_ERR_WIN32A(WSA_QOS_SENDERS)
        CHK_ERR_WIN32A(WSA_QOS_NO_SENDERS)
        CHK_ERR_WIN32A(WSA_QOS_NO_RECEIVERS)
        CHK_ERR_WIN32A(WSA_QOS_REQUEST_CONFIRMED)
        CHK_ERR_WIN32A(WSA_QOS_ADMISSION_FAILURE)
        CHK_ERR_WIN32A(WSA_QOS_POLICY_FAILURE)
        CHK_ERR_WIN32A(WSA_QOS_BAD_STYLE)
        CHK_ERR_WIN32A(WSA_QOS_BAD_OBJECT)
        CHK_ERR_WIN32A(WSA_QOS_TRAFFIC_CTRL_ERROR)
        CHK_ERR_WIN32A(WSA_QOS_GENERIC_ERROR)

        CHK_ERRA(CO_E_ATTEMPT_TO_CREATE_OUTSIDE_CLIENT_CONTEXT)
        CHK_ERRA(CO_E_SERVER_PAUSED)
        CHK_ERRA(CO_E_SERVER_NOT_PAUSED)
        CHK_ERRA(CO_E_CLASS_DISABLED)
        CHK_ERRA(CO_E_CLRNOTAVAILABLE)
        CHK_ERRA(CO_E_ASYNC_WORK_REJECTED)
        CHK_ERRA(CO_E_SERVER_INIT_TIMEOUT)
        CHK_ERRA(CO_E_NO_SECCTX_IN_ACTIVATE)
        CHK_ERRA(CO_E_TRACKER_CONFIG)
        CHK_ERRA(CO_E_THREADPOOL_CONFIG)
        CHK_ERRA(CO_E_SXS_CONFIG)
        CHK_ERRA(CO_E_MALFORMED_SPN)
        CHK_ERRA(REGDB_E_BADTHREADINGMODEL)
//        CHK_ERRA(EVENT_E_FIRST)
//        CHK_ERRA(EVENT_E_LAST)
//        CHK_ERRA(EVENT_S_FIRST)
//        CHK_ERRA(EVENT_S_LAST)
//        CHK_ERRA(EVENT_S_SOME_SUBSCRIBERS_FAILED)
//        CHK_ERRA(EVENT_E_ALL_SUBSCRIBERS_FAILED)
        CHK_ERRA(EVENT_S_NOSUBSCRIBERS)
//        CHK_ERRA(EVENT_E_QUERYSYNTAX)
//        CHK_ERRA(EVENT_E_QUERYFIELD)
//        CHK_ERRA(EVENT_E_INTERNALEXCEPTION)
//        CHK_ERRA(EVENT_E_INTERNALERROR)
//        CHK_ERRA(EVENT_E_INVALID_PER_USER_SID)
//        CHK_ERRA(EVENT_E_USER_EXCEPTION)
//        CHK_ERRA(EVENT_E_TOO_MANY_METHODS)
//        CHK_ERRA(EVENT_E_MISSING_EVENTCLASS)
//        CHK_ERRA(EVENT_E_NOT_ALL_REMOVED)
//        CHK_ERRA(EVENT_E_COMPLUS_NOT_INSTALLED)
//        CHK_ERRA(EVENT_E_CANT_MODIFY_OR_DELETE_UNCONFIGURED_OBJECT)
//        CHK_ERRA(EVENT_E_CANT_MODIFY_OR_DELETE_CONFIGURED_OBJECT)
//        CHK_ERRA(EVENT_E_INVALID_EVENT_CLASS_PARTITION)
//        CHK_ERRA(EVENT_E_PER_USER_SID_NOT_LOGGED_ON)
        CHK_ERRA(_HRESULT_TYPEDEF_(CONTEXT_E_FIRST))
        CHK_ERRA(_HRESULT_TYPEDEF_(CONTEXT_E_LAST))
        CHK_ERRA(CONTEXT_S_FIRST)
        CHK_ERRA(CONTEXT_S_LAST)
        CHK_ERRA(CONTEXT_E_ABORTED)
        CHK_ERRA(CONTEXT_E_ABORTING)
        CHK_ERRA(CONTEXT_E_NOCONTEXT)
//        CHK_ERRA(CONTEXT_E_WOULD_DEADLOCK)
        CHK_ERRA(CONTEXT_E_SYNCH_TIMEOUT)
        CHK_ERRA(CONTEXT_E_OLDREF)
        CHK_ERRA(CONTEXT_E_ROLENOTFOUND)
        CHK_ERRA(CONTEXT_E_TMNOTAVAILABLE)
        CHK_ERRA(CO_E_ACTIVATIONFAILED)
        CHK_ERRA(CO_E_ACTIVATIONFAILED_EVENTLOGGED)
        CHK_ERRA(CO_E_ACTIVATIONFAILED_CATALOGERROR)
        CHK_ERRA(CO_E_ACTIVATIONFAILED_TIMEOUT)
        CHK_ERRA(CO_E_INITIALIZATIONFAILED)
        CHK_ERRA(CONTEXT_E_NOJIT)
        CHK_ERRA(CONTEXT_E_NOTRANSACTION)
        CHK_ERRA(CO_E_THREADINGMODEL_CHANGED)
        CHK_ERRA(CO_E_NOIISINTRINSICS)
        CHK_ERRA(CO_E_NOCOOKIES)
        CHK_ERRA(CO_E_DBERROR)
        CHK_ERRA(CO_E_NOTPOOLED)
        CHK_ERRA(CO_E_NOTCONSTRUCTED)
        CHK_ERRA(CO_E_NOSYNCHRONIZATION)
//        CHK_ERRA(CO_E_ISOLEVELMISMATCH)
        CHK_ERRA(SCHED_S_TASK_READY)
        CHK_ERRA(SCHED_S_TASK_RUNNING)
        CHK_ERRA(SCHED_S_TASK_DISABLED)
        CHK_ERRA(SCHED_S_TASK_HAS_NOT_RUN)
        CHK_ERRA(SCHED_S_TASK_NO_MORE_RUNS)
        CHK_ERRA(SCHED_S_TASK_NOT_SCHEDULED)
        CHK_ERRA(SCHED_S_TASK_TERMINATED)
        CHK_ERRA(SCHED_S_TASK_NO_VALID_TRIGGERS)
        CHK_ERRA(SCHED_S_EVENT_TRIGGER)
        CHK_ERRA(SCHED_E_TRIGGER_NOT_FOUND)
        CHK_ERRA(SCHED_E_TASK_NOT_READY)
        CHK_ERRA(SCHED_E_TASK_NOT_RUNNING)
        CHK_ERRA(SCHED_E_SERVICE_NOT_INSTALLED)
        CHK_ERRA(SCHED_E_CANNOT_OPEN_TASK)
        CHK_ERRA(SCHED_E_INVALID_TASK)
        CHK_ERRA(SCHED_E_ACCOUNT_INFORMATION_NOT_SET)
        CHK_ERRA(SCHED_E_ACCOUNT_NAME_NOT_FOUND)
        CHK_ERRA(SCHED_E_ACCOUNT_DBASE_CORRUPT)
        CHK_ERRA(SCHED_E_NO_SECURITY_SERVICES)
        CHK_ERRA(SCHED_E_UNKNOWN_OBJECT_VERSION)
        CHK_ERRA(SCHED_E_UNSUPPORTED_ACCOUNT_OPTION)
        CHK_ERRA(SCHED_E_SERVICE_NOT_RUNNING)
        CHK_ERRA(CO_S_MACHINENAMENOTFOUND)
        CHK_ERRA(STG_E_STATUS_COPY_PROTECTION_FAILURE)
        CHK_ERRA(STG_E_CSS_AUTHENTICATION_FAILURE)
        CHK_ERRA(STG_E_CSS_KEY_NOT_PRESENT)
        CHK_ERRA(STG_E_CSS_KEY_NOT_ESTABLISHED)
        CHK_ERRA(STG_E_CSS_SCRAMBLED_SECTOR)
        CHK_ERRA(STG_E_CSS_REGION_MISMATCH)
        CHK_ERRA(STG_E_RESETS_EXHAUSTED)
        CHK_ERRA(CO_E_CANCEL_DISABLED)
        CHK_ERRA(ERROR_AUDITING_DISABLED)
        CHK_ERRA(ERROR_ALL_SIDS_FILTERED)
        CHK_ERRA(NTE_TEMPORARY_PROFILE)
        CHK_ERRA(NTE_FIXEDPARAMETER)
        CHK_ERRA(SEC_E_INSUFFICIENT_MEMORY)
        CHK_ERRA(SEC_E_INVALID_HANDLE)
        CHK_ERRA(SEC_E_UNSUPPORTED_FUNCTION)
        CHK_ERRA(SEC_E_TARGET_UNKNOWN)
        CHK_ERRA(SEC_E_INTERNAL_ERROR)
        CHK_ERRA(SEC_E_SECPKG_NOT_FOUND)
        CHK_ERRA(SEC_E_NOT_OWNER)
        CHK_ERRA(SEC_E_CANNOT_INSTALL)
        CHK_ERRA(SEC_E_INVALID_TOKEN)
        CHK_ERRA(SEC_E_CANNOT_PACK)
        CHK_ERRA(SEC_E_QOP_NOT_SUPPORTED)
        CHK_ERRA(SEC_E_NO_IMPERSONATION)
        CHK_ERRA(SEC_E_LOGON_DENIED)
        CHK_ERRA(SEC_E_UNKNOWN_CREDENTIALS)
        CHK_ERRA(SEC_E_NO_CREDENTIALS)
        CHK_ERRA(SEC_E_MESSAGE_ALTERED)
        CHK_ERRA(SEC_E_OUT_OF_SEQUENCE)
        CHK_ERRA(SEC_E_NO_AUTHENTICATING_AUTHORITY)
        CHK_ERRA(SEC_I_CONTINUE_NEEDED)
        CHK_ERRA(SEC_I_COMPLETE_NEEDED)
        CHK_ERRA(SEC_I_COMPLETE_AND_CONTINUE)
        CHK_ERRA(SEC_I_LOCAL_LOGON)
        CHK_ERRA(SEC_E_BAD_PKGID)
        CHK_ERRA(SEC_E_CONTEXT_EXPIRED)
        CHK_ERRA(SEC_I_CONTEXT_EXPIRED)
        CHK_ERRA(SEC_E_INCOMPLETE_MESSAGE)
        CHK_ERRA(SEC_E_INCOMPLETE_CREDENTIALS)
        CHK_ERRA(SEC_E_BUFFER_TOO_SMALL)
        CHK_ERRA(SEC_I_INCOMPLETE_CREDENTIALS)
        CHK_ERRA(SEC_I_RENEGOTIATE)
        CHK_ERRA(SEC_E_WRONG_PRINCIPAL)
        CHK_ERRA(SEC_I_NO_LSA_CONTEXT)
        CHK_ERRA(SEC_E_TIME_SKEW)
        CHK_ERRA(SEC_E_UNTRUSTED_ROOT)
        CHK_ERRA(SEC_E_ILLEGAL_MESSAGE)
        CHK_ERRA(SEC_E_CERT_UNKNOWN)
        CHK_ERRA(SEC_E_CERT_EXPIRED)
        CHK_ERRA(SEC_E_ENCRYPT_FAILURE)
        CHK_ERRA(SEC_E_DECRYPT_FAILURE)
        CHK_ERRA(SEC_E_ALGORITHM_MISMATCH)
        CHK_ERRA(SEC_E_SECURITY_QOS_FAILED)
        CHK_ERRA(SEC_E_UNFINISHED_CONTEXT_DELETED)
        CHK_ERRA(SEC_E_NO_TGT_REPLY)
        CHK_ERRA(SEC_E_NO_IP_ADDRESSES)
        CHK_ERRA(SEC_E_WRONG_CREDENTIAL_HANDLE)
        CHK_ERRA(SEC_E_CRYPTO_SYSTEM_INVALID)
        CHK_ERRA(SEC_E_MAX_REFERRALS_EXCEEDED)
        CHK_ERRA(SEC_E_MUST_BE_KDC)
        CHK_ERRA(SEC_E_STRONG_CRYPTO_NOT_SUPPORTED)
        CHK_ERRA(SEC_E_TOO_MANY_PRINCIPALS)
        CHK_ERRA(SEC_E_NO_PA_DATA)
        CHK_ERRA(SEC_E_PKINIT_NAME_MISMATCH)
        CHK_ERRA(SEC_E_SMARTCARD_LOGON_REQUIRED)
        CHK_ERRA(SEC_E_SHUTDOWN_IN_PROGRESS)
        CHK_ERRA(SEC_E_KDC_INVALID_REQUEST)
        CHK_ERRA(SEC_E_KDC_UNABLE_TO_REFER)
        CHK_ERRA(SEC_E_KDC_UNKNOWN_ETYPE)
        CHK_ERRA(SEC_E_UNSUPPORTED_PREAUTH)
        CHK_ERRA(SEC_E_DELEGATION_REQUIRED)
        CHK_ERRA(SEC_E_BAD_BINDINGS)
        CHK_ERRA(SEC_E_MULTIPLE_ACCOUNTS)
        CHK_ERRA(SEC_E_NO_KERB_KEY)
//        CHK_ERRA(SEC_E_CERT_WRONG_USAGE)
//        CHK_ERRA(SEC_E_DOWNGRADE_DETECTED)
        CHK_ERRA(SEC_E_SMARTCARD_CERT_REVOKED)
        CHK_ERRA(SEC_E_ISSUING_CA_UNTRUSTED)
        CHK_ERRA(SEC_E_REVOCATION_OFFLINE_C)
        CHK_ERRA(SEC_E_PKINIT_CLIENT_FAILURE)
        CHK_ERRA(SEC_E_SMARTCARD_CERT_EXPIRED)
//        CHK_ERRA(SEC_E_NO_SPM)
//        CHK_ERRA(SEC_E_NOT_SUPPORTED)
        CHK_ERRA(CRYPT_I_NEW_PROTECTION_REQUIRED)
        CHK_ERRA(CRYPT_E_MISSING_PUBKEY_PARA)
        CHK_ERRA(CRYPT_E_ASN1_ERROR)
        CHK_ERRA(CRYPT_E_ASN1_INTERNAL)
        CHK_ERRA(CRYPT_E_ASN1_EOD)
        CHK_ERRA(CRYPT_E_ASN1_CORRUPT)
        CHK_ERRA(CRYPT_E_ASN1_LARGE)
        CHK_ERRA(CRYPT_E_ASN1_CONSTRAINT)
        CHK_ERRA(CRYPT_E_ASN1_MEMORY)
        CHK_ERRA(CRYPT_E_ASN1_OVERFLOW)
        CHK_ERRA(CRYPT_E_ASN1_BADPDU)
        CHK_ERRA(CRYPT_E_ASN1_BADARGS)
        CHK_ERRA(CRYPT_E_ASN1_BADREAL)
        CHK_ERRA(CRYPT_E_ASN1_BADTAG)
        CHK_ERRA(CRYPT_E_ASN1_CHOICE)
        CHK_ERRA(CRYPT_E_ASN1_RULE)
        CHK_ERRA(CRYPT_E_ASN1_UTF8)
        CHK_ERRA(CRYPT_E_ASN1_PDU_TYPE)
        CHK_ERRA(CRYPT_E_ASN1_NYI)
        CHK_ERRA(CRYPT_E_ASN1_EXTENDED)
        CHK_ERRA(CRYPT_E_ASN1_NOEOD)
        CHK_ERRA(CERTSRV_E_SERVER_SUSPENDED)
        CHK_ERRA(CERTSRV_E_ENCODING_LENGTH)
        CHK_ERRA(CERTSRV_E_ROLECONFLICT)
        CHK_ERRA(CERTSRV_E_RESTRICTEDOFFICER)
        CHK_ERRA(CERTSRV_E_KEY_ARCHIVAL_NOT_CONFIGURED)
        CHK_ERRA(CERTSRV_E_NO_VALID_KRA)
        CHK_ERRA(CERTSRV_E_BAD_REQUEST_KEY_ARCHIVAL)
        CHK_ERRA(CERTSRV_E_NO_CAADMIN_DEFINED)
        CHK_ERRA(CERTSRV_E_BAD_RENEWAL_CERT_ATTRIBUTE)
        CHK_ERRA(CERTSRV_E_NO_DB_SESSIONS)
        CHK_ERRA(CERTSRV_E_ALIGNMENT_FAULT)
        CHK_ERRA(CERTSRV_E_ENROLL_DENIED)
        CHK_ERRA(CERTSRV_E_TEMPLATE_DENIED)
//        CHK_ERRA(CERTSRV_E_DOWNLEVEL_DC_SSL_OR_UPGRADE)
        CHK_ERRA(CERTSRV_E_TEMPLATE_CONFLICT)
        CHK_ERRA(CERTSRV_E_SUBJECT_ALT_NAME_REQUIRED)
        CHK_ERRA(CERTSRV_E_ARCHIVED_KEY_REQUIRED)
        CHK_ERRA(CERTSRV_E_SMIME_REQUIRED)
        CHK_ERRA(CERTSRV_E_BAD_RENEWAL_SUBJECT)
        CHK_ERRA(CERTSRV_E_BAD_TEMPLATE_VERSION)
        CHK_ERRA(CERTSRV_E_TEMPLATE_POLICY_REQUIRED)
        CHK_ERRA(CERTSRV_E_SIGNATURE_POLICY_REQUIRED)
        CHK_ERRA(CERTSRV_E_SIGNATURE_COUNT)
        CHK_ERRA(CERTSRV_E_SIGNATURE_REJECTED)
        CHK_ERRA(CERTSRV_E_ISSUANCE_POLICY_REQUIRED)
        CHK_ERRA(CERTSRV_E_SUBJECT_UPN_REQUIRED)
        CHK_ERRA(CERTSRV_E_SUBJECT_DIRECTORY_GUID_REQUIRED)
        CHK_ERRA(CERTSRV_E_SUBJECT_DNS_REQUIRED)
        CHK_ERRA(CERTSRV_E_ARCHIVED_KEY_UNEXPECTED)
        CHK_ERRA(CERTSRV_E_KEY_LENGTH)
//        CHK_ERRA(CERTSRV_E_SUBJECT_EMAIL_REQUIRED)
//        CHK_ERRA(CERTSRV_E_UNKNOWN_CERT_TYPE)
//        CHK_ERRA(CERTSRV_E_CERT_TYPE_OVERLAP)
        CHK_ERRA(XENROLL_E_KEY_NOT_EXPORTABLE)
        CHK_ERRA(XENROLL_E_CANNOT_ADD_ROOT_CERT)
        CHK_ERRA(XENROLL_E_RESPONSE_KA_HASH_NOT_FOUND)
        CHK_ERRA(XENROLL_E_RESPONSE_UNEXPECTED_KA_HASH)
        CHK_ERRA(XENROLL_E_RESPONSE_KA_HASH_MISMATCH)
        CHK_ERRA(XENROLL_E_KEYSPEC_SMIME_MISMATCH)
        CHK_ERRA(MSSIPOTF_E_OUTOFMEMRANGE)
        CHK_ERRA(MSSIPOTF_E_CANTGETOBJECT)
        CHK_ERRA(MSSIPOTF_E_NOHEADTABLE)
        CHK_ERRA(MSSIPOTF_E_BAD_MAGICNUMBER)
        CHK_ERRA(MSSIPOTF_E_BAD_OFFSET_TABLE)
        CHK_ERRA(MSSIPOTF_E_TABLE_TAGORDER)
        CHK_ERRA(MSSIPOTF_E_TABLE_LONGWORD)
        CHK_ERRA(MSSIPOTF_E_BAD_FIRST_TABLE_PLACEMENT)
        CHK_ERRA(MSSIPOTF_E_TABLES_OVERLAP)
        CHK_ERRA(MSSIPOTF_E_TABLE_PADBYTES)
        CHK_ERRA(MSSIPOTF_E_FILETOOSMALL)
        CHK_ERRA(MSSIPOTF_E_TABLE_CHECKSUM)
        CHK_ERRA(MSSIPOTF_E_FILE_CHECKSUM)
        CHK_ERRA(MSSIPOTF_E_FAILED_POLICY)
        CHK_ERRA(MSSIPOTF_E_FAILED_HINTS_CHECK)
        CHK_ERRA(MSSIPOTF_E_NOT_OPENTYPE)
        CHK_ERRA(MSSIPOTF_E_FILE)
        CHK_ERRA(MSSIPOTF_E_CRYPT)
        CHK_ERRA(MSSIPOTF_E_BADVERSION)
        CHK_ERRA(MSSIPOTF_E_DSIG_STRUCTURE)
        CHK_ERRA(MSSIPOTF_E_PCONST_CHECK)
        CHK_ERRA(MSSIPOTF_E_STRUCTURE)
        CHK_ERRA(TRUST_E_EXPLICIT_DISTRUST)
        CHK_ERRA(CERT_E_UNTRUSTEDCA)
        CHK_ERRA(CERT_E_INVALID_POLICY)
        CHK_ERRA(CERT_E_INVALID_NAME)
        CHK_ERRA(SPAPI_E_NOT_DISABLEABLE)
        CHK_ERRA(SPAPI_E_CANT_REMOVE_DEVINST)
        CHK_ERRA(SPAPI_E_INVALID_TARGET)
        CHK_ERRA(SPAPI_E_DRIVER_NONNATIVE)
        CHK_ERRA(SPAPI_E_IN_WOW64)
        CHK_ERRA(SPAPI_E_SET_SYSTEM_RESTORE_POINT)
        CHK_ERRA(SPAPI_E_INCORRECTLY_COPIED_INF)
        CHK_ERRA(SPAPI_E_SCE_DISABLED)
        CHK_ERRA(SCARD_E_NO_KEY_CONTAINER)
        CHK_ERRA(SCARD_W_CARD_NOT_AUTHENTICATED)
        CHK_ERRA(COMADMIN_E_OBJECTERRORS)
        CHK_ERRA(COMADMIN_E_OBJECTINVALID)
        CHK_ERRA(COMADMIN_E_KEYMISSING)
        CHK_ERRA(COMADMIN_E_ALREADYINSTALLED)
        CHK_ERRA(COMADMIN_E_APP_FILE_WRITEFAIL)
        CHK_ERRA(COMADMIN_E_APP_FILE_READFAIL)
        CHK_ERRA(COMADMIN_E_APP_FILE_VERSION)
        CHK_ERRA(COMADMIN_E_BADPATH)
        CHK_ERRA(COMADMIN_E_APPLICATIONEXISTS)
        CHK_ERRA(COMADMIN_E_ROLEEXISTS)
        CHK_ERRA(COMADMIN_E_CANTCOPYFILE)
        CHK_ERRA(COMADMIN_E_NOUSER)
        CHK_ERRA(COMADMIN_E_INVALIDUSERIDS)
        CHK_ERRA(COMADMIN_E_NOREGISTRYCLSID)
        CHK_ERRA(COMADMIN_E_BADREGISTRYPROGID)
        CHK_ERRA(COMADMIN_E_AUTHENTICATIONLEVEL)
        CHK_ERRA(COMADMIN_E_USERPASSWDNOTVALID)
        CHK_ERRA(COMADMIN_E_CLSIDORIIDMISMATCH)
        CHK_ERRA(COMADMIN_E_REMOTEINTERFACE)
        CHK_ERRA(COMADMIN_E_DLLREGISTERSERVER)
        CHK_ERRA(COMADMIN_E_NOSERVERSHARE)
        CHK_ERRA(COMADMIN_E_DLLLOADFAILED)
        CHK_ERRA(COMADMIN_E_BADREGISTRYLIBID)
        CHK_ERRA(COMADMIN_E_APPDIRNOTFOUND)
        CHK_ERRA(COMADMIN_E_REGISTRARFAILED)
        CHK_ERRA(COMADMIN_E_COMPFILE_DOESNOTEXIST)
        CHK_ERRA(COMADMIN_E_COMPFILE_LOADDLLFAIL)
        CHK_ERRA(COMADMIN_E_COMPFILE_GETCLASSOBJ)
        CHK_ERRA(COMADMIN_E_COMPFILE_CLASSNOTAVAIL)
        CHK_ERRA(COMADMIN_E_COMPFILE_BADTLB)
        CHK_ERRA(COMADMIN_E_COMPFILE_NOTINSTALLABLE)
        CHK_ERRA(COMADMIN_E_NOTCHANGEABLE)
        CHK_ERRA(COMADMIN_E_NOTDELETEABLE)
        CHK_ERRA(COMADMIN_E_SESSION)
        CHK_ERRA(COMADMIN_E_COMP_MOVE_LOCKED)
        CHK_ERRA(COMADMIN_E_COMP_MOVE_BAD_DEST)
        CHK_ERRA(COMADMIN_E_REGISTERTLB)
        CHK_ERRA(COMADMIN_E_SYSTEMAPP)
        CHK_ERRA(COMADMIN_E_COMPFILE_NOREGISTRAR)
        CHK_ERRA(COMADMIN_E_COREQCOMPINSTALLED)
        CHK_ERRA(COMADMIN_E_SERVICENOTINSTALLED)
        CHK_ERRA(COMADMIN_E_PROPERTYSAVEFAILED)
        CHK_ERRA(COMADMIN_E_OBJECTEXISTS)
        CHK_ERRA(COMADMIN_E_COMPONENTEXISTS)
        CHK_ERRA(COMADMIN_E_REGFILE_CORRUPT)
        CHK_ERRA(COMADMIN_E_PROPERTY_OVERFLOW)
        CHK_ERRA(COMADMIN_E_NOTINREGISTRY)
        CHK_ERRA(COMADMIN_E_OBJECTNOTPOOLABLE)
        CHK_ERRA(COMADMIN_E_APPLID_MATCHES_CLSID)
        CHK_ERRA(COMADMIN_E_ROLE_DOES_NOT_EXIST)
        CHK_ERRA(COMADMIN_E_START_APP_NEEDS_COMPONENTS)
        CHK_ERRA(COMADMIN_E_REQUIRES_DIFFERENT_PLATFORM)
        CHK_ERRA(COMADMIN_E_CAN_NOT_EXPORT_APP_PROXY)
        CHK_ERRA(COMADMIN_E_CAN_NOT_START_APP)
        CHK_ERRA(COMADMIN_E_CAN_NOT_EXPORT_SYS_APP)
        CHK_ERRA(COMADMIN_E_CANT_SUBSCRIBE_TO_COMPONENT)
        CHK_ERRA(COMADMIN_E_EVENTCLASS_CANT_BE_SUBSCRIBER)
        CHK_ERRA(COMADMIN_E_LIB_APP_PROXY_INCOMPATIBLE)
        CHK_ERRA(COMADMIN_E_BASE_PARTITION_ONLY)
        CHK_ERRA(COMADMIN_E_START_APP_DISABLED)
        CHK_ERRA(COMADMIN_E_CAT_DUPLICATE_PARTITION_NAME)
        CHK_ERRA(COMADMIN_E_CAT_INVALID_PARTITION_NAME)
        CHK_ERRA(COMADMIN_E_CAT_PARTITION_IN_USE)
        CHK_ERRA(COMADMIN_E_FILE_PARTITION_DUPLICATE_FILES)
        CHK_ERRA(COMADMIN_E_CAT_IMPORTED_COMPONENTS_NOT_ALLOWED)
        CHK_ERRA(COMADMIN_E_AMBIGUOUS_APPLICATION_NAME)
        CHK_ERRA(COMADMIN_E_AMBIGUOUS_PARTITION_NAME)
        CHK_ERRA(COMADMIN_E_REGDB_NOTINITIALIZED)
        CHK_ERRA(COMADMIN_E_REGDB_NOTOPEN)
        CHK_ERRA(COMADMIN_E_REGDB_SYSTEMERR)
        CHK_ERRA(COMADMIN_E_REGDB_ALREADYRUNNING)
        CHK_ERRA(COMADMIN_E_MIG_VERSIONNOTSUPPORTED)
        CHK_ERRA(COMADMIN_E_MIG_SCHEMANOTFOUND)
        CHK_ERRA(COMADMIN_E_CAT_BITNESSMISMATCH)
        CHK_ERRA(COMADMIN_E_CAT_UNACCEPTABLEBITNESS)
        CHK_ERRA(COMADMIN_E_CAT_WRONGAPPBITNESS)
        CHK_ERRA(COMADMIN_E_CAT_PAUSE_RESUME_NOT_SUPPORTED)
        CHK_ERRA(COMADMIN_E_CAT_SERVERFAULT)
        CHK_ERRA(COMQC_E_APPLICATION_NOT_QUEUED)
        CHK_ERRA(COMQC_E_NO_QUEUEABLE_INTERFACES)
        CHK_ERRA(COMQC_E_QUEUING_SERVICE_NOT_AVAILABLE)
        CHK_ERRA(COMQC_E_NO_IPERSISTSTREAM)
        CHK_ERRA(COMQC_E_BAD_MESSAGE)
        CHK_ERRA(COMQC_E_UNAUTHENTICATED)
        CHK_ERRA(COMQC_E_UNTRUSTED_ENQUEUER)
        CHK_ERRA(MSDTC_E_DUPLICATE_RESOURCE)
        CHK_ERRA(COMADMIN_E_OBJECT_PARENT_MISSING)
        CHK_ERRA(COMADMIN_E_OBJECT_DOES_NOT_EXIST)
        CHK_ERRA(COMADMIN_E_APP_NOT_RUNNING)
        CHK_ERRA(COMADMIN_E_INVALID_PARTITION)
        CHK_ERRA(COMADMIN_E_SVCAPP_NOT_POOLABLE_OR_RECYCLABLE)
        CHK_ERRA(COMADMIN_E_USER_IN_SET)
        CHK_ERRA(COMADMIN_E_CANTRECYCLELIBRARYAPPS)
        CHK_ERRA(COMADMIN_E_CANTRECYCLESERVICEAPPS)
        CHK_ERRA(COMADMIN_E_PROCESSALREADYRECYCLED)
        CHK_ERRA(COMADMIN_E_PAUSEDPROCESSMAYNOTBERECYCLED)
        CHK_ERRA(COMADMIN_E_CANTMAKEINPROCSERVICE)
        CHK_ERRA(COMADMIN_E_PROGIDINUSEBYCLSID)
        CHK_ERRA(COMADMIN_E_DEFAULT_PARTITION_NOT_IN_SET)
        CHK_ERRA(COMADMIN_E_RECYCLEDPROCESSMAYNOTBEPAUSED)
        CHK_ERRA(COMADMIN_E_PARTITION_ACCESSDENIED)
        CHK_ERRA(COMADMIN_E_PARTITION_MSI_ONLY)
        CHK_ERRA(COMADMIN_E_LEGACYCOMPS_NOT_ALLOWED_IN_1_0_FORMAT)
        CHK_ERRA(COMADMIN_E_LEGACYCOMPS_NOT_ALLOWED_IN_NONBASE_PARTITIONS)
        CHK_ERRA(COMADMIN_E_COMP_MOVE_SOURCE)
        CHK_ERRA(COMADMIN_E_COMP_MOVE_DEST)
        CHK_ERRA(COMADMIN_E_COMP_MOVE_PRIVATE)
        CHK_ERRA(COMADMIN_E_BASEPARTITION_REQUIRED_IN_SET)
        CHK_ERRA(COMADMIN_E_CANNOT_ALIAS_EVENTCLASS)
        CHK_ERRA(COMADMIN_E_PRIVATE_ACCESSDENIED)
        CHK_ERRA(COMADMIN_E_SAFERINVALID)
        CHK_ERRA(COMADMIN_E_REGISTRY_ACCESSDENIED)
//        CHK_ERRA(COMADMIN_E_PARTITIONS_DISABLED)
//        CHK_ERR_WIN32A(ERROR_EXE_CANNOT_MODIFY_SIGNED_BINARY)
//        CHK_ERR_WIN32A(ERROR_EXE_CANNOT_MODIFY_STRONG_SIGNED_BINARY)
        CHK_ERR_WIN32A(ERROR_DISK_TOO_FRAGMENTED)
        CHK_ERR_WIN32A(ERROR_DELETE_PENDING)
//        CHK_ERR_WIN32A(ERROR_SCOPE_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_NOT_SAFEBOOT_SERVICE)
        CHK_ERR_WIN32A(ERROR_JOURNAL_ENTRY_DELETED)
        CHK_ERR_WIN32A(ERROR_ONLY_IF_CONNECTED)
        CHK_ERR_WIN32A(ERROR_OVERRIDE_NOCHANGES)
        CHK_ERR_WIN32A(ERROR_BAD_USER_PROFILE)
        CHK_ERR_WIN32A(ERROR_NOT_SUPPORTED_ON_SBS)
        CHK_ERR_WIN32A(ERROR_SERVER_SHUTDOWN_IN_PROGRESS)
        CHK_ERR_WIN32A(ERROR_HOST_DOWN)
        CHK_ERR_WIN32A(ERROR_NON_ACCOUNT_SID)
        CHK_ERR_WIN32A(ERROR_NON_DOMAIN_SID)
        CHK_ERR_WIN32A(ERROR_APPHELP_BLOCK)
        CHK_ERR_WIN32A(ERROR_ACCESS_DISABLED_BY_POLICY)
        CHK_ERR_WIN32A(ERROR_REG_NAT_CONSUMPTION)
        CHK_ERR_WIN32A(ERROR_CSCSHARE_OFFLINE)
        CHK_ERR_WIN32A(ERROR_PKINIT_FAILURE)
        CHK_ERR_WIN32A(ERROR_SMARTCARD_SUBSYSTEM_FAILURE)
        CHK_ERR_WIN32A(ERROR_DOWNGRADE_DETECTED)
        CHK_ERR_WIN32A(ERROR_MACHINE_LOCKED)
        CHK_ERR_WIN32A(ERROR_CALLBACK_SUPPLIED_INVALID_DATA)
        CHK_ERR_WIN32A(ERROR_SYNC_FOREGROUND_REFRESH_REQUIRED)
        CHK_ERR_WIN32A(ERROR_DRIVER_BLOCKED)
        CHK_ERR_WIN32A(ERROR_INVALID_IMPORT_OF_NON_DLL)
//        CHK_ERR_WIN32A(ERROR_ACCESS_DISABLED_WEBBLADE)
//        CHK_ERR_WIN32A(ERROR_ACCESS_DISABLED_WEBBLADE_TAMPER)
//        CHK_ERR_WIN32A(ERROR_RECOVERY_FAILURE)
//        CHK_ERR_WIN32A(ERROR_ALREADY_FIBER)
//        CHK_ERR_WIN32A(ERROR_ALREADY_THREAD)
//        CHK_ERR_WIN32A(ERROR_STACK_BUFFER_OVERRUN)
//        CHK_ERR_WIN32A(ERROR_PARAMETER_QUOTA_EXCEEDED)
//        CHK_ERR_WIN32A(ERROR_DEBUGGER_INACTIVE)
//        CHK_ERR_WIN32A(ERROR_DELAY_LOAD_FAILED)
        CHK_ERR_WIN32A(ERROR_CURRENT_DOMAIN_NOT_ALLOWED)
        CHK_ERR_WIN32A(ERROR_INSTALL_SERVICE_FAILURE)
        CHK_ERR_WIN32A(ERROR_INSTALL_PACKAGE_VERSION)
        CHK_ERR_WIN32A(ERROR_INSTALL_ALREADY_RUNNING)
        CHK_ERR_WIN32A(ERROR_INSTALL_PACKAGE_OPEN_FAILED)
        CHK_ERR_WIN32A(ERROR_INSTALL_PACKAGE_INVALID)
        CHK_ERR_WIN32A(ERROR_INSTALL_UI_FAILURE)
        CHK_ERR_WIN32A(ERROR_INSTALL_LOG_FAILURE)
        CHK_ERR_WIN32A(ERROR_INSTALL_LANGUAGE_UNSUPPORTED)
        CHK_ERR_WIN32A(ERROR_INSTALL_TRANSFORM_FAILURE)
        CHK_ERR_WIN32A(ERROR_INSTALL_PACKAGE_REJECTED)
        CHK_ERR_WIN32A(ERROR_FUNCTION_NOT_CALLED)
        CHK_ERR_WIN32A(ERROR_FUNCTION_FAILED)
        CHK_ERR_WIN32A(ERROR_INVALID_TABLE)
        CHK_ERR_WIN32A(ERROR_DATATYPE_MISMATCH)
        CHK_ERR_WIN32A(ERROR_UNSUPPORTED_TYPE)
        CHK_ERR_WIN32A(ERROR_CREATE_FAILED)
        CHK_ERR_WIN32A(ERROR_INSTALL_TEMP_UNWRITABLE)
        CHK_ERR_WIN32A(ERROR_INSTALL_PLATFORM_UNSUPPORTED)
        CHK_ERR_WIN32A(ERROR_INSTALL_NOTUSED)
        CHK_ERR_WIN32A(ERROR_PATCH_PACKAGE_OPEN_FAILED)
        CHK_ERR_WIN32A(ERROR_PATCH_PACKAGE_INVALID)
        CHK_ERR_WIN32A(ERROR_PATCH_PACKAGE_UNSUPPORTED)
        CHK_ERR_WIN32A(ERROR_PRODUCT_VERSION)
        CHK_ERR_WIN32A(ERROR_INVALID_COMMAND_LINE)
        CHK_ERR_WIN32A(ERROR_INSTALL_REMOTE_DISALLOWED)
        CHK_ERR_WIN32A(ERROR_SUCCESS_REBOOT_INITIATED)
        CHK_ERR_WIN32A(ERROR_PATCH_TARGET_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_PATCH_PACKAGE_REJECTED)
        CHK_ERR_WIN32A(ERROR_INSTALL_TRANSFORM_REJECTED)
//        CHK_ERR_WIN32A(ERROR_INSTALL_REMOTE_PROHIBITED)
        CHK_ERR_WIN32A(RPC_S_ENTRY_TYPE_MISMATCH)
        CHK_ERR_WIN32A(RPC_S_NOT_ALL_OBJS_EXPORTED)
        CHK_ERR_WIN32A(RPC_S_INTERFACE_NOT_EXPORTED)
        CHK_ERR_WIN32A(RPC_S_PROFILE_NOT_ADDED)
        CHK_ERR_WIN32A(RPC_S_PRF_ELT_NOT_ADDED)
        CHK_ERR_WIN32A(RPC_S_PRF_ELT_NOT_REMOVED)
        CHK_ERR_WIN32A(RPC_S_GRP_ELT_NOT_ADDED)
        CHK_ERR_WIN32A(RPC_S_GRP_ELT_NOT_REMOVED)
        CHK_ERR_WIN32A(ERROR_KM_DRIVER_BLOCKED)
        CHK_ERR_WIN32A(ERROR_CONTEXT_EXPIRED)
//        CHK_ERR_WIN32A(ERROR_PER_USER_TRUST_QUOTA_EXCEEDED)
//        CHK_ERR_WIN32A(ERROR_ALL_USER_TRUST_QUOTA_EXCEEDED)
//        CHK_ERR_WIN32A(ERROR_USER_DELETE_TRUST_QUOTA_EXCEEDED)
        CHK_ERR_WIN32A(ERROR_CONNECTED_OTHER_PASSWORD_DEFAULT)
        CHK_ERR_WIN32A(ERROR_PRINTER_DRIVER_WARNED)
        CHK_ERR_WIN32A(ERROR_PRINTER_DRIVER_BLOCKED)
        CHK_ERR_WIN32A(ERROR_VOLUME_CONTAINS_SYS_FILES)
        CHK_ERR_WIN32A(ERROR_INDIGENOUS_TYPE)
        CHK_ERR_WIN32A(ERROR_NO_SUPPORTING_DRIVES)
        CHK_ERR_WIN32A(ERROR_CLEANER_CARTRIDGE_INSTALLED)
        CHK_ERR_WIN32A(ERROR_REPARSE_TAG_INVALID)
        CHK_ERR_WIN32A(ERROR_REPARSE_TAG_MISMATCH)
        CHK_ERR_WIN32A(ERROR_VOLUME_NOT_SIS_ENABLED)
        CHK_ERR_WIN32A(ERROR_QUORUM_OWNER_ALIVE)
        CHK_ERR_WIN32A(ERROR_NETWORK_NOT_AVAILABLE)
        CHK_ERR_WIN32A(ERROR_NODE_NOT_AVAILABLE)
        CHK_ERR_WIN32A(ERROR_ALL_NODES_NOT_AVAILABLE)
        CHK_ERR_WIN32A(ERROR_RESOURCE_FAILED)
        CHK_ERR_WIN32A(ERROR_CLUSTER_INVALID_NODE)
        CHK_ERR_WIN32A(ERROR_CLUSTER_NODE_EXISTS)
        CHK_ERR_WIN32A(ERROR_CLUSTER_JOIN_IN_PROGRESS)
        CHK_ERR_WIN32A(ERROR_CLUSTER_NODE_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_CLUSTER_LOCAL_NODE_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_CLUSTER_NETWORK_EXISTS)
        CHK_ERR_WIN32A(ERROR_CLUSTER_NETWORK_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_CLUSTER_NETINTERFACE_EXISTS)
        CHK_ERR_WIN32A(ERROR_CLUSTER_NETINTERFACE_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_CLUSTER_INVALID_REQUEST)
        CHK_ERR_WIN32A(ERROR_CLUSTER_INVALID_NETWORK_PROVIDER)
        CHK_ERR_WIN32A(ERROR_CLUSTER_NODE_DOWN)
        CHK_ERR_WIN32A(ERROR_CLUSTER_NODE_UNREACHABLE)
        CHK_ERR_WIN32A(ERROR_CLUSTER_NODE_NOT_MEMBER)
        CHK_ERR_WIN32A(ERROR_CLUSTER_JOIN_NOT_IN_PROGRESS)
        CHK_ERR_WIN32A(ERROR_CLUSTER_INVALID_NETWORK)
        CHK_ERR_WIN32A(ERROR_CLUSTER_NODE_UP)
        CHK_ERR_WIN32A(ERROR_CLUSTER_IPADDR_IN_USE)
        CHK_ERR_WIN32A(ERROR_CLUSTER_NODE_NOT_PAUSED)
        CHK_ERR_WIN32A(ERROR_CLUSTER_NO_SECURITY_CONTEXT)
        CHK_ERR_WIN32A(ERROR_CLUSTER_NETWORK_NOT_INTERNAL)
        CHK_ERR_WIN32A(ERROR_CLUSTER_NODE_ALREADY_UP)
        CHK_ERR_WIN32A(ERROR_CLUSTER_NODE_ALREADY_DOWN)
        CHK_ERR_WIN32A(ERROR_CLUSTER_NETWORK_ALREADY_ONLINE)
        CHK_ERR_WIN32A(ERROR_CLUSTER_NETWORK_ALREADY_OFFLINE)
        CHK_ERR_WIN32A(ERROR_CLUSTER_NODE_ALREADY_MEMBER)
        CHK_ERR_WIN32A(ERROR_CLUSTER_LAST_INTERNAL_NETWORK)
        CHK_ERR_WIN32A(ERROR_CLUSTER_NETWORK_HAS_DEPENDENTS)
        CHK_ERR_WIN32A(ERROR_INVALID_OPERATION_ON_QUORUM)
        CHK_ERR_WIN32A(ERROR_DEPENDENCY_NOT_ALLOWED)
        CHK_ERR_WIN32A(ERROR_CLUSTER_NODE_PAUSED)
        CHK_ERR_WIN32A(ERROR_NODE_CANT_HOST_RESOURCE)
        CHK_ERR_WIN32A(ERROR_CLUSTER_NODE_NOT_READY)
        CHK_ERR_WIN32A(ERROR_CLUSTER_NODE_SHUTTING_DOWN)
        CHK_ERR_WIN32A(ERROR_CLUSTER_JOIN_ABORTED)
        CHK_ERR_WIN32A(ERROR_CLUSTER_INCOMPATIBLE_VERSIONS)
        CHK_ERR_WIN32A(ERROR_CLUSTER_MAXNUM_OF_RESOURCES_EXCEEDED)
        CHK_ERR_WIN32A(ERROR_CLUSTER_SYSTEM_CONFIG_CHANGED)
        CHK_ERR_WIN32A(ERROR_CLUSTER_RESOURCE_TYPE_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_CLUSTER_RESTYPE_NOT_SUPPORTED)
        CHK_ERR_WIN32A(ERROR_CLUSTER_RESNAME_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_CLUSTER_NO_RPC_PACKAGES_REGISTERED)
        CHK_ERR_WIN32A(ERROR_CLUSTER_OWNER_NOT_IN_PREFLIST)
        CHK_ERR_WIN32A(ERROR_CLUSTER_DATABASE_SEQMISMATCH)
        CHK_ERR_WIN32A(ERROR_RESMON_INVALID_STATE)
        CHK_ERR_WIN32A(ERROR_CLUSTER_GUM_NOT_LOCKER)
        CHK_ERR_WIN32A(ERROR_QUORUM_DISK_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_DATABASE_BACKUP_CORRUPT)
        CHK_ERR_WIN32A(ERROR_CLUSTER_NODE_ALREADY_HAS_DFS_ROOT)
        CHK_ERR_WIN32A(ERROR_RESOURCE_PROPERTY_UNCHANGEABLE)
        CHK_ERR_WIN32A(ERROR_CLUSTER_MEMBERSHIP_INVALID_STATE)
        CHK_ERR_WIN32A(ERROR_CLUSTER_QUORUMLOG_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_CLUSTER_MEMBERSHIP_HALT)
        CHK_ERR_WIN32A(ERROR_CLUSTER_INSTANCE_ID_MISMATCH)
        CHK_ERR_WIN32A(ERROR_CLUSTER_NETWORK_NOT_FOUND_FOR_IP)
        CHK_ERR_WIN32A(ERROR_CLUSTER_PROPERTY_DATA_TYPE_MISMATCH)
        CHK_ERR_WIN32A(ERROR_CLUSTER_EVICT_WITHOUT_CLEANUP)
        CHK_ERR_WIN32A(ERROR_CLUSTER_PARAMETER_MISMATCH)
        CHK_ERR_WIN32A(ERROR_NODE_CANNOT_BE_CLUSTERED)
        CHK_ERR_WIN32A(ERROR_CLUSTER_WRONG_OS_VERSION)
        CHK_ERR_WIN32A(ERROR_CLUSTER_CANT_CREATE_DUP_CLUSTER_NAME)
//        CHK_ERR_WIN32A(ERROR_CLUSCFG_ALREADY_COMMITTED)
//        CHK_ERR_WIN32A(ERROR_CLUSCFG_ROLLBACK_FAILED)
//        CHK_ERR_WIN32A(ERROR_CLUSCFG_SYSTEM_DISK_DRIVE_LETTER_CONFLICT)
//        CHK_ERR_WIN32A(ERROR_CLUSTER_OLD_VERSION)
//        CHK_ERR_WIN32A(ERROR_CLUSTER_MISMATCHED_COMPUTER_ACCT_NAME)
        CHK_ERR_WIN32A(ERROR_FILE_READ_ONLY)
        CHK_ERR_WIN32A(ERROR_DIR_EFS_DISALLOWED)
        CHK_ERR_WIN32A(ERROR_EFS_SERVER_NOT_TRUSTED)
        CHK_ERR_WIN32A(ERROR_BAD_RECOVERY_POLICY)
        CHK_ERR_WIN32A(ERROR_EFS_ALG_BLOB_TOO_BIG)
        CHK_ERR_WIN32A(ERROR_VOLUME_NOT_SUPPORT_EFS)
        CHK_ERR_WIN32A(ERROR_EFS_DISABLED)
        CHK_ERR_WIN32A(ERROR_EFS_VERSION_NOT_SUPPORT)
        CHK_ERR_WIN32A(SCHED_E_SERVICE_NOT_LOCALSYSTEM)
        CHK_ERR_WIN32A(ERROR_CTX_CLIENT_LICENSE_IN_USE)
        CHK_ERR_WIN32A(ERROR_CTX_CLIENT_LICENSE_NOT_SET)
        CHK_ERR_WIN32A(ERROR_CTX_LICENSE_NOT_AVAILABLE)
        CHK_ERR_WIN32A(ERROR_CTX_LICENSE_CLIENT_INVALID)
        CHK_ERR_WIN32A(ERROR_CTX_LICENSE_EXPIRED)
        CHK_ERR_WIN32A(ERROR_CTX_SHADOW_NOT_RUNNING)
        CHK_ERR_WIN32A(ERROR_CTX_SHADOW_ENDED_BY_MODE_CHANGE)
//        CHK_ERR_WIN32A(ERROR_ACTIVATION_COUNT_EXCEEDED)
        CHK_ERR_WIN32A(ERROR_DS_SORT_CONTROL_MISSING)
        CHK_ERR_WIN32A(ERROR_DS_OFFSET_RANGE_ERROR)
        CHK_ERR_WIN32A(ERROR_DS_OBJECT_BEING_REMOVED)
        CHK_ERR_WIN32A(ERROR_DS_DS_REQUIRED)
        CHK_ERR_WIN32A(ERROR_DS_INVALID_LDAP_DISPLAY_NAME)
        CHK_ERR_WIN32A(ERROR_DS_NON_BASE_SEARCH)
        CHK_ERR_WIN32A(ERROR_DS_CANT_RETRIEVE_ATTS)
        CHK_ERR_WIN32A(ERROR_DS_BACKLINK_WITHOUT_LINK)
        CHK_ERR_WIN32A(ERROR_DS_EPOCH_MISMATCH)
        CHK_ERR_WIN32A(ERROR_DS_SRC_NAME_MISMATCH)
        CHK_ERR_WIN32A(ERROR_DS_SRC_AND_DST_NC_IDENTICAL)
        CHK_ERR_WIN32A(ERROR_DS_DST_NC_MISMATCH)
        CHK_ERR_WIN32A(ERROR_DS_NOT_AUTHORITIVE_FOR_DST_NC)
        CHK_ERR_WIN32A(ERROR_DS_SRC_GUID_MISMATCH)
        CHK_ERR_WIN32A(ERROR_DS_CANT_MOVE_DELETED_OBJECT)
        CHK_ERR_WIN32A(ERROR_DS_PDC_OPERATION_IN_PROGRESS)
        CHK_ERR_WIN32A(ERROR_DS_CROSS_DOMAIN_CLEANUP_REQD)
        CHK_ERR_WIN32A(ERROR_DS_ILLEGAL_XDOM_MOVE_OPERATION)
        CHK_ERR_WIN32A(ERROR_DS_CANT_WITH_ACCT_GROUP_MEMBERSHPS)
        CHK_ERR_WIN32A(ERROR_DS_NC_MUST_HAVE_NC_PARENT)
        CHK_ERR_WIN32A(ERROR_DS_CR_IMPOSSIBLE_TO_VALIDATE)
        CHK_ERR_WIN32A(ERROR_DS_DST_DOMAIN_NOT_NATIVE)
        CHK_ERR_WIN32A(ERROR_DS_MISSING_INFRASTRUCTURE_CONTAINER)
        CHK_ERR_WIN32A(ERROR_DS_CANT_MOVE_ACCOUNT_GROUP)
        CHK_ERR_WIN32A(ERROR_DS_CANT_MOVE_RESOURCE_GROUP)
        CHK_ERR_WIN32A(ERROR_DS_INVALID_SEARCH_FLAG)
        CHK_ERR_WIN32A(ERROR_DS_NO_TREE_DELETE_ABOVE_NC)
        CHK_ERR_WIN32A(ERROR_DS_COULDNT_LOCK_TREE_FOR_DELETE)
        CHK_ERR_WIN32A(ERROR_DS_COULDNT_IDENTIFY_OBJECTS_FOR_TREE_DELETE)
        CHK_ERR_WIN32A(ERROR_DS_SAM_INIT_FAILURE)
        CHK_ERR_WIN32A(ERROR_DS_SENSITIVE_GROUP_VIOLATION)
        CHK_ERR_WIN32A(ERROR_DS_CANT_MOD_PRIMARYGROUPID)
        CHK_ERR_WIN32A(ERROR_DS_ILLEGAL_BASE_SCHEMA_MOD)
        CHK_ERR_WIN32A(ERROR_DS_NONSAFE_SCHEMA_CHANGE)
        CHK_ERR_WIN32A(ERROR_DS_SCHEMA_UPDATE_DISALLOWED)
        CHK_ERR_WIN32A(ERROR_DS_CANT_CREATE_UNDER_SCHEMA)
        CHK_ERR_WIN32A(ERROR_DS_INSTALL_NO_SRC_SCH_VERSION)
        CHK_ERR_WIN32A(ERROR_DS_INSTALL_NO_SCH_VERSION_IN_INIFILE)
        CHK_ERR_WIN32A(ERROR_DS_INVALID_GROUP_TYPE)
        CHK_ERR_WIN32A(ERROR_DS_NO_NEST_GLOBALGROUP_IN_MIXEDDOMAIN)
        CHK_ERR_WIN32A(ERROR_DS_NO_NEST_LOCALGROUP_IN_MIXEDDOMAIN)
        CHK_ERR_WIN32A(ERROR_DS_GLOBAL_CANT_HAVE_LOCAL_MEMBER)
        CHK_ERR_WIN32A(ERROR_DS_GLOBAL_CANT_HAVE_UNIVERSAL_MEMBER)
        CHK_ERR_WIN32A(ERROR_DS_UNIVERSAL_CANT_HAVE_LOCAL_MEMBER)
        CHK_ERR_WIN32A(ERROR_DS_GLOBAL_CANT_HAVE_CROSSDOMAIN_MEMBER)
        CHK_ERR_WIN32A(ERROR_DS_LOCAL_CANT_HAVE_CROSSDOMAIN_LOCAL_MEMBER)
        CHK_ERR_WIN32A(ERROR_DS_HAVE_PRIMARY_MEMBERS)
        CHK_ERR_WIN32A(ERROR_DS_STRING_SD_CONVERSION_FAILED)
        CHK_ERR_WIN32A(ERROR_DS_NAMING_MASTER_GC)
        CHK_ERR_WIN32A(ERROR_DS_DNS_LOOKUP_FAILURE)
        CHK_ERR_WIN32A(ERROR_DS_COULDNT_UPDATE_SPNS)
        CHK_ERR_WIN32A(ERROR_DS_CANT_RETRIEVE_SD)
        CHK_ERR_WIN32A(ERROR_DS_KEY_NOT_UNIQUE)
        CHK_ERR_WIN32A(ERROR_DS_WRONG_LINKED_ATT_SYNTAX)
        CHK_ERR_WIN32A(ERROR_DS_SAM_NEED_BOOTKEY_PASSWORD)
        CHK_ERR_WIN32A(ERROR_DS_SAM_NEED_BOOTKEY_FLOPPY)
        CHK_ERR_WIN32A(ERROR_DS_CANT_START)
        CHK_ERR_WIN32A(ERROR_DS_INIT_FAILURE)
        CHK_ERR_WIN32A(ERROR_DS_NO_PKT_PRIVACY_ON_CONNECTION)
        CHK_ERR_WIN32A(ERROR_DS_SOURCE_DOMAIN_IN_FOREST)
        CHK_ERR_WIN32A(ERROR_DS_DESTINATION_DOMAIN_NOT_IN_FOREST)
        CHK_ERR_WIN32A(ERROR_DS_DESTINATION_AUDITING_NOT_ENABLED)
        CHK_ERR_WIN32A(ERROR_DS_CANT_FIND_DC_FOR_SRC_DOMAIN)
        CHK_ERR_WIN32A(ERROR_DS_SRC_OBJ_NOT_GROUP_OR_USER)
        CHK_ERR_WIN32A(ERROR_DS_SRC_SID_EXISTS_IN_FOREST)
        CHK_ERR_WIN32A(ERROR_DS_SRC_AND_DST_OBJECT_CLASS_MISMATCH)
        CHK_ERR_WIN32A(ERROR_SAM_INIT_FAILURE)
        CHK_ERR_WIN32A(ERROR_DS_DRA_SCHEMA_INFO_SHIP)
        CHK_ERR_WIN32A(ERROR_DS_DRA_SCHEMA_CONFLICT)
        CHK_ERR_WIN32A(ERROR_DS_DRA_EARLIER_SCHEMA_CONFLICT)
        CHK_ERR_WIN32A(ERROR_DS_DRA_OBJ_NC_MISMATCH)
        CHK_ERR_WIN32A(ERROR_DS_NC_STILL_HAS_DSAS)
        CHK_ERR_WIN32A(ERROR_DS_GC_REQUIRED)
        CHK_ERR_WIN32A(ERROR_DS_LOCAL_MEMBER_OF_LOCAL_ONLY)
        CHK_ERR_WIN32A(ERROR_DS_NO_FPO_IN_UNIVERSAL_GROUPS)
        CHK_ERR_WIN32A(ERROR_DS_CANT_ADD_TO_GC)
        CHK_ERR_WIN32A(ERROR_DS_NO_CHECKPOINT_WITH_PDC)
        CHK_ERR_WIN32A(ERROR_DS_SOURCE_AUDITING_NOT_ENABLED)
        CHK_ERR_WIN32A(ERROR_DS_CANT_CREATE_IN_NONDOMAIN_NC)
        CHK_ERR_WIN32A(ERROR_DS_INVALID_NAME_FOR_SPN)
        CHK_ERR_WIN32A(ERROR_DS_FILTER_USES_CONTRUCTED_ATTRS)
        CHK_ERR_WIN32A(ERROR_DS_UNICODEPWD_NOT_IN_QUOTES)
        CHK_ERR_WIN32A(ERROR_DS_MACHINE_ACCOUNT_QUOTA_EXCEEDED)
        CHK_ERR_WIN32A(ERROR_DS_MUST_BE_RUN_ON_DST_DC)
        CHK_ERR_WIN32A(ERROR_DS_SRC_DC_MUST_BE_SP4_OR_GREATER)
        CHK_ERR_WIN32A(ERROR_DS_CANT_TREE_DELETE_CRITICAL_OBJ)
        CHK_ERR_WIN32A(ERROR_DS_INIT_FAILURE_CONSOLE)
        CHK_ERR_WIN32A(ERROR_DS_SAM_INIT_FAILURE_CONSOLE)
        CHK_ERR_WIN32A(ERROR_DS_FOREST_VERSION_TOO_HIGH)
        CHK_ERR_WIN32A(ERROR_DS_DOMAIN_VERSION_TOO_HIGH)
        CHK_ERR_WIN32A(ERROR_DS_FOREST_VERSION_TOO_LOW)
        CHK_ERR_WIN32A(ERROR_DS_DOMAIN_VERSION_TOO_LOW)
        CHK_ERR_WIN32A(ERROR_DS_INCOMPATIBLE_VERSION)
        CHK_ERR_WIN32A(ERROR_DS_LOW_DSA_VERSION)
        CHK_ERR_WIN32A(ERROR_DS_NO_BEHAVIOR_VERSION_IN_MIXEDDOMAIN)
        CHK_ERR_WIN32A(ERROR_DS_NOT_SUPPORTED_SORT_ORDER)
        CHK_ERR_WIN32A(ERROR_DS_NAME_NOT_UNIQUE)
        CHK_ERR_WIN32A(ERROR_DS_MACHINE_ACCOUNT_CREATED_PRENT4)
        CHK_ERR_WIN32A(ERROR_DS_OUT_OF_VERSION_STORE)
        CHK_ERR_WIN32A(ERROR_DS_INCOMPATIBLE_CONTROLS_USED)
        CHK_ERR_WIN32A(ERROR_DS_NO_REF_DOMAIN)
        CHK_ERR_WIN32A(ERROR_DS_RESERVED_LINK_ID)
        CHK_ERR_WIN32A(ERROR_DS_LINK_ID_NOT_AVAILABLE)
        CHK_ERR_WIN32A(ERROR_DS_AG_CANT_HAVE_UNIVERSAL_MEMBER)
        CHK_ERR_WIN32A(ERROR_DS_MODIFYDN_DISALLOWED_BY_INSTANCE_TYPE)
        CHK_ERR_WIN32A(ERROR_DS_NO_OBJECT_MOVE_IN_SCHEMA_NC)
        CHK_ERR_WIN32A(ERROR_DS_MODIFYDN_DISALLOWED_BY_FLAG)
        CHK_ERR_WIN32A(ERROR_DS_MODIFYDN_WRONG_GRANDPARENT)
        CHK_ERR_WIN32A(ERROR_DS_NAME_ERROR_TRUST_REFERRAL)
        CHK_ERR_WIN32A(ERROR_NOT_SUPPORTED_ON_STANDARD_SERVER)
        CHK_ERR_WIN32A(ERROR_DS_CANT_ACCESS_REMOTE_PART_OF_AD)
        CHK_ERR_WIN32A(ERROR_DS_CR_IMPOSSIBLE_TO_VALIDATE_V2)
        CHK_ERR_WIN32A(ERROR_DS_THREAD_LIMIT_EXCEEDED)
        CHK_ERR_WIN32A(ERROR_DS_NOT_CLOSEST)
        CHK_ERR_WIN32A(ERROR_DS_CANT_DERIVE_SPN_WITHOUT_SERVER_REF)
        CHK_ERR_WIN32A(ERROR_DS_SINGLE_USER_MODE_FAILED)
        CHK_ERR_WIN32A(ERROR_DS_NTDSCRIPT_SYNTAX_ERROR)
        CHK_ERR_WIN32A(ERROR_DS_NTDSCRIPT_PROCESS_ERROR)
        CHK_ERR_WIN32A(ERROR_DS_DIFFERENT_REPL_EPOCHS)
        CHK_ERR_WIN32A(ERROR_DS_DRS_EXTENSIONS_CHANGED)
        CHK_ERR_WIN32A(ERROR_DS_REPLICA_SET_CHANGE_NOT_ALLOWED_ON_DISABLED_CR)
        CHK_ERR_WIN32A(ERROR_DS_NO_MSDS_INTID)
        CHK_ERR_WIN32A(ERROR_DS_DUP_MSDS_INTID)
        CHK_ERR_WIN32A(ERROR_DS_EXISTS_IN_RDNATTID)
        CHK_ERR_WIN32A(ERROR_DS_AUTHORIZATION_FAILED)
        CHK_ERR_WIN32A(ERROR_DS_INVALID_SCRIPT)
        CHK_ERR_WIN32A(ERROR_DS_REMOTE_CROSSREF_OP_FAILED)
//        CHK_ERR_WIN32A(ERROR_DS_CROSS_REF_BUSY)
//        CHK_ERR_WIN32A(ERROR_DS_CANT_DERIVE_SPN_FOR_DELETED_DOMAIN)
//        CHK_ERR_WIN32A(ERROR_DS_CANT_DEMOTE_WITH_WRITEABLE_NC)
//        CHK_ERR_WIN32A(ERROR_DS_DUPLICATE_ID_FOUND)
//        CHK_ERR_WIN32A(ERROR_DS_INSUFFICIENT_ATTR_TO_CREATE_OBJECT)
//        CHK_ERR_WIN32A(ERROR_DS_GROUP_CONVERSION_ERROR)
//        CHK_ERR_WIN32A(ERROR_DS_CANT_MOVE_APP_BASIC_GROUP)
//        CHK_ERR_WIN32A(ERROR_DS_CANT_MOVE_APP_QUERY_GROUP)
//        CHK_ERR_WIN32A(ERROR_DS_ROLE_NOT_VERIFIED)
//        CHK_ERR_WIN32A(ERROR_DS_WKO_CONTAINER_CANNOT_BE_SPECIAL)
//        CHK_ERR_WIN32A(ERROR_DS_DOMAIN_RENAME_IN_PROGRESS)
//        CHK_ERR_WIN32A(ERROR_DS_EXISTING_AD_CHILD_NC)
//        CHK_ERR_WIN32A(DNS_ERROR_INVALID_NAME_CHAR)
//        CHK_ERR_WIN32A(DNS_ERROR_NUMERIC_NAME)
//        CHK_ERR_WIN32A(DNS_ERROR_NOT_ALLOWED_ON_ROOT_SERVER)
//        CHK_ERR_WIN32A(DNS_ERROR_NOT_ALLOWED_UNDER_DELEGATION)
//        CHK_ERR_WIN32A(DNS_ERROR_CANNOT_FIND_ROOT_HINTS)
//        CHK_ERR_WIN32A(DNS_ERROR_INCONSISTENT_ROOT_HINTS)
        CHK_ERR_WIN32A(DNS_ERROR_FORWARDER_ALREADY_EXISTS)
        CHK_ERR_WIN32A(DNS_ERROR_ZONE_REQUIRES_MASTER_IP)
        CHK_ERR_WIN32A(DNS_ERROR_ZONE_IS_SHUTDOWN)
        CHK_ERR_WIN32A(DNS_ERROR_DP_BASE)
        CHK_ERR_WIN32A(DNS_ERROR_DP_DOES_NOT_EXIST)
        CHK_ERR_WIN32A(DNS_ERROR_DP_ALREADY_EXISTS)
        CHK_ERR_WIN32A(DNS_ERROR_DP_NOT_ENLISTED)
        CHK_ERR_WIN32A(DNS_ERROR_DP_ALREADY_ENLISTED)
//        CHK_ERR_WIN32A(DNS_ERROR_DP_NOT_AVAILABLE)
        CHK_ERR_WIN32A(WSA_QOS_ESERVICETYPE)
        CHK_ERR_WIN32A(WSA_QOS_EFLOWSPEC)
        CHK_ERR_WIN32A(WSA_QOS_EPROVSPECBUF)
        CHK_ERR_WIN32A(WSA_QOS_EFILTERSTYLE)
        CHK_ERR_WIN32A(WSA_QOS_EFILTERTYPE)
        CHK_ERR_WIN32A(WSA_QOS_EFILTERCOUNT)
        CHK_ERR_WIN32A(WSA_QOS_EOBJLENGTH)
        CHK_ERR_WIN32A(WSA_QOS_EFLOWCOUNT)
        CHK_ERR_WIN32A(WSA_QOS_EUNKOWNPSOBJ)
        CHK_ERR_WIN32A(WSA_QOS_EPOLICYOBJ)
        CHK_ERR_WIN32A(WSA_QOS_EFLOWDESC)
        CHK_ERR_WIN32A(WSA_QOS_EPSFLOWSPEC)
        CHK_ERR_WIN32A(WSA_QOS_EPSFILTERSPEC)
        CHK_ERR_WIN32A(WSA_QOS_ESDMODEOBJ)
        CHK_ERR_WIN32A(WSA_QOS_ESHAPERATEOBJ)
        CHK_ERR_WIN32A(WSA_QOS_RESERVED_PETYPE)
        CHK_ERR_WIN32A(ERROR_SXS_SECTION_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_SXS_CANT_GEN_ACTCTX)
        CHK_ERR_WIN32A(ERROR_SXS_INVALID_ACTCTXDATA_FORMAT)
        CHK_ERR_WIN32A(ERROR_SXS_ASSEMBLY_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_SXS_MANIFEST_FORMAT_ERROR)
        CHK_ERR_WIN32A(ERROR_SXS_MANIFEST_PARSE_ERROR)
        CHK_ERR_WIN32A(ERROR_SXS_ACTIVATION_CONTEXT_DISABLED)
        CHK_ERR_WIN32A(ERROR_SXS_KEY_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_SXS_VERSION_CONFLICT)
        CHK_ERR_WIN32A(ERROR_SXS_WRONG_SECTION_TYPE)
        CHK_ERR_WIN32A(ERROR_SXS_THREAD_QUERIES_DISABLED)
        CHK_ERR_WIN32A(ERROR_SXS_PROCESS_DEFAULT_ALREADY_SET)
        CHK_ERR_WIN32A(ERROR_SXS_UNKNOWN_ENCODING_GROUP)
        CHK_ERR_WIN32A(ERROR_SXS_UNKNOWN_ENCODING)
        CHK_ERR_WIN32A(ERROR_SXS_INVALID_XML_NAMESPACE_URI)
        CHK_ERR_WIN32A(ERROR_SXS_ROOT_MANIFEST_DEPENDENCY_NOT_INSTALLED)
        CHK_ERR_WIN32A(ERROR_SXS_LEAF_MANIFEST_DEPENDENCY_NOT_INSTALLED)
        CHK_ERR_WIN32A(ERROR_SXS_INVALID_ASSEMBLY_IDENTITY_ATTRIBUTE)
        CHK_ERR_WIN32A(ERROR_SXS_MANIFEST_MISSING_REQUIRED_DEFAULT_NAMESPACE)
        CHK_ERR_WIN32A(ERROR_SXS_MANIFEST_INVALID_REQUIRED_DEFAULT_NAMESPACE)
        CHK_ERR_WIN32A(ERROR_SXS_PRIVATE_MANIFEST_CROSS_PATH_WITH_REPARSE_POINT)
        CHK_ERR_WIN32A(ERROR_SXS_DUPLICATE_DLL_NAME)
        CHK_ERR_WIN32A(ERROR_SXS_DUPLICATE_WINDOWCLASS_NAME)
        CHK_ERR_WIN32A(ERROR_SXS_DUPLICATE_CLSID)
        CHK_ERR_WIN32A(ERROR_SXS_DUPLICATE_IID)
        CHK_ERR_WIN32A(ERROR_SXS_DUPLICATE_TLBID)
        CHK_ERR_WIN32A(ERROR_SXS_DUPLICATE_PROGID)
        CHK_ERR_WIN32A(ERROR_SXS_DUPLICATE_ASSEMBLY_NAME)
        CHK_ERR_WIN32A(ERROR_SXS_FILE_HASH_MISMATCH)
        CHK_ERR_WIN32A(ERROR_SXS_POLICY_PARSE_ERROR)
        CHK_ERR_WIN32A(ERROR_SXS_XML_E_MISSINGQUOTE)
        CHK_ERR_WIN32A(ERROR_SXS_XML_E_COMMENTSYNTAX)
        CHK_ERR_WIN32A(ERROR_SXS_XML_E_BADSTARTNAMECHAR)
        CHK_ERR_WIN32A(ERROR_SXS_XML_E_BADNAMECHAR)
        CHK_ERR_WIN32A(ERROR_SXS_XML_E_BADCHARINSTRING)
        CHK_ERR_WIN32A(ERROR_SXS_XML_E_XMLDECLSYNTAX)
        CHK_ERR_WIN32A(ERROR_SXS_XML_E_BADCHARDATA)
        CHK_ERR_WIN32A(ERROR_SXS_XML_E_MISSINGWHITESPACE)
        CHK_ERR_WIN32A(ERROR_SXS_XML_E_EXPECTINGTAGEND)
        CHK_ERR_WIN32A(ERROR_SXS_XML_E_MISSINGSEMICOLON)
        CHK_ERR_WIN32A(ERROR_SXS_XML_E_UNBALANCEDPAREN)
        CHK_ERR_WIN32A(ERROR_SXS_XML_E_INTERNALERROR)
        CHK_ERR_WIN32A(ERROR_SXS_XML_E_UNEXPECTED_WHITESPACE)
        CHK_ERR_WIN32A(ERROR_SXS_XML_E_INCOMPLETE_ENCODING)
        CHK_ERR_WIN32A(ERROR_SXS_XML_E_MISSING_PAREN)
        CHK_ERR_WIN32A(ERROR_SXS_XML_E_EXPECTINGCLOSEQUOTE)
        CHK_ERR_WIN32A(ERROR_SXS_XML_E_MULTIPLE_COLONS)
        CHK_ERR_WIN32A(ERROR_SXS_XML_E_INVALID_DECIMAL)
        CHK_ERR_WIN32A(ERROR_SXS_XML_E_INVALID_HEXIDECIMAL)
        CHK_ERR_WIN32A(ERROR_SXS_XML_E_INVALID_UNICODE)
        CHK_ERR_WIN32A(ERROR_SXS_XML_E_WHITESPACEORQUESTIONMARK)
        CHK_ERR_WIN32A(ERROR_SXS_XML_E_UNEXPECTEDENDTAG)
        CHK_ERR_WIN32A(ERROR_SXS_XML_E_UNCLOSEDTAG)
        CHK_ERR_WIN32A(ERROR_SXS_XML_E_DUPLICATEATTRIBUTE)
        CHK_ERR_WIN32A(ERROR_SXS_XML_E_MULTIPLEROOTS)
        CHK_ERR_WIN32A(ERROR_SXS_XML_E_INVALIDATROOTLEVEL)
        CHK_ERR_WIN32A(ERROR_SXS_XML_E_BADXMLDECL)
        CHK_ERR_WIN32A(ERROR_SXS_XML_E_MISSINGROOT)
        CHK_ERR_WIN32A(ERROR_SXS_XML_E_UNEXPECTEDEOF)
        CHK_ERR_WIN32A(ERROR_SXS_XML_E_BADPEREFINSUBSET)
        CHK_ERR_WIN32A(ERROR_SXS_XML_E_UNCLOSEDSTARTTAG)
        CHK_ERR_WIN32A(ERROR_SXS_XML_E_UNCLOSEDENDTAG)
        CHK_ERR_WIN32A(ERROR_SXS_XML_E_UNCLOSEDSTRING)
        CHK_ERR_WIN32A(ERROR_SXS_XML_E_UNCLOSEDCOMMENT)
        CHK_ERR_WIN32A(ERROR_SXS_XML_E_UNCLOSEDDECL)
        CHK_ERR_WIN32A(ERROR_SXS_XML_E_UNCLOSEDCDATA)
        CHK_ERR_WIN32A(ERROR_SXS_XML_E_RESERVEDNAMESPACE)
        CHK_ERR_WIN32A(ERROR_SXS_XML_E_INVALIDENCODING)
        CHK_ERR_WIN32A(ERROR_SXS_XML_E_INVALIDSWITCH)
        CHK_ERR_WIN32A(ERROR_SXS_XML_E_BADXMLCASE)
        CHK_ERR_WIN32A(ERROR_SXS_XML_E_INVALID_STANDALONE)
        CHK_ERR_WIN32A(ERROR_SXS_XML_E_UNEXPECTED_STANDALONE)
        CHK_ERR_WIN32A(ERROR_SXS_XML_E_INVALID_VERSION)
        CHK_ERR_WIN32A(ERROR_SXS_XML_E_MISSINGEQUALS)
        CHK_ERR_WIN32A(ERROR_SXS_PROTECTION_RECOVERY_FAILED)
        CHK_ERR_WIN32A(ERROR_SXS_PROTECTION_PUBLIC_KEY_TOO_SHORT)
        CHK_ERR_WIN32A(ERROR_SXS_PROTECTION_CATALOG_NOT_VALID)
        CHK_ERR_WIN32A(ERROR_SXS_UNTRANSLATABLE_HRESULT)
        CHK_ERR_WIN32A(ERROR_SXS_PROTECTION_CATALOG_FILE_MISSING)
        CHK_ERR_WIN32A(ERROR_SXS_MISSING_ASSEMBLY_IDENTITY_ATTRIBUTE)
        CHK_ERR_WIN32A(ERROR_SXS_INVALID_ASSEMBLY_IDENTITY_ATTRIBUTE_NAME)
        CHK_ERR_WIN32A(ERROR_IPSEC_QM_POLICY_EXISTS)
        CHK_ERR_WIN32A(ERROR_IPSEC_QM_POLICY_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_IPSEC_QM_POLICY_IN_USE)
        CHK_ERR_WIN32A(ERROR_IPSEC_MM_POLICY_EXISTS)
        CHK_ERR_WIN32A(ERROR_IPSEC_MM_POLICY_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_IPSEC_MM_POLICY_IN_USE)
        CHK_ERR_WIN32A(ERROR_IPSEC_MM_FILTER_EXISTS)
        CHK_ERR_WIN32A(ERROR_IPSEC_MM_FILTER_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_IPSEC_TRANSPORT_FILTER_EXISTS)
        CHK_ERR_WIN32A(ERROR_IPSEC_TRANSPORT_FILTER_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_IPSEC_MM_AUTH_EXISTS)
        CHK_ERR_WIN32A(ERROR_IPSEC_MM_AUTH_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_IPSEC_MM_AUTH_IN_USE)
        CHK_ERR_WIN32A(ERROR_IPSEC_DEFAULT_MM_POLICY_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_IPSEC_DEFAULT_MM_AUTH_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_IPSEC_DEFAULT_QM_POLICY_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_IPSEC_TUNNEL_FILTER_EXISTS)
        CHK_ERR_WIN32A(ERROR_IPSEC_TUNNEL_FILTER_NOT_FOUND)
        CHK_ERR_WIN32A(ERROR_IPSEC_MM_FILTER_PENDING_DELETION)
        CHK_ERR_WIN32A(ERROR_IPSEC_TRANSPORT_FILTER_PENDING_DELETION)
        CHK_ERR_WIN32A(ERROR_IPSEC_TUNNEL_FILTER_PENDING_DELETION)
        CHK_ERR_WIN32A(ERROR_IPSEC_MM_POLICY_PENDING_DELETION)
        CHK_ERR_WIN32A(ERROR_IPSEC_MM_AUTH_PENDING_DELETION)
        CHK_ERR_WIN32A(ERROR_IPSEC_QM_POLICY_PENDING_DELETION)
//        CHK_ERR_WIN32A(WARNING_IPSEC_MM_POLICY_PRUNED)
//        CHK_ERR_WIN32A(WARNING_IPSEC_QM_POLICY_PRUNED)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_NEG_STATUS_BEGIN)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_AUTH_FAIL)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_ATTRIB_FAIL)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_NEGOTIATION_PENDING)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_GENERAL_PROCESSING_ERROR)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_TIMED_OUT)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_NO_CERT)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_SA_DELETED)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_SA_REAPED)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_MM_ACQUIRE_DROP)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_QM_ACQUIRE_DROP)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_QUEUE_DROP_MM)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_QUEUE_DROP_NO_MM)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_DROP_NO_RESPONSE)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_MM_DELAY_DROP)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_QM_DELAY_DROP)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_ERROR)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_CRL_FAILED)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_INVALID_KEY_USAGE)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_INVALID_CERT_TYPE)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_NO_PRIVATE_KEY)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_DH_FAIL)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_INVALID_HEADER)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_NO_POLICY)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_INVALID_SIGNATURE)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_KERBEROS_ERROR)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_NO_PUBLIC_KEY)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_PROCESS_ERR)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_PROCESS_ERR_SA)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_PROCESS_ERR_PROP)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_PROCESS_ERR_TRANS)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_PROCESS_ERR_KE)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_PROCESS_ERR_ID)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_PROCESS_ERR_CERT)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_PROCESS_ERR_CERT_REQ)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_PROCESS_ERR_HASH)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_PROCESS_ERR_SIG)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_PROCESS_ERR_NONCE)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_PROCESS_ERR_NOTIFY)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_PROCESS_ERR_DELETE)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_PROCESS_ERR_VENDOR)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_INVALID_PAYLOAD)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_LOAD_SOFT_SA)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_SOFT_SA_TORN_DOWN)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_INVALID_COOKIE)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_NO_PEER_CERT)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_PEER_CRL_FAILED)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_POLICY_CHANGE)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_NO_MM_POLICY)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_NOTCBPRIV)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_SECLOADFAIL)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_FAILSSPINIT)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_FAILQUERYSSP)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_SRVACQFAIL)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_SRVQUERYCRED)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_GETSPIFAIL)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_INVALID_FILTER)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_OUT_OF_MEMORY)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_ADD_UPDATE_KEY_FAILED)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_INVALID_POLICY)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_UNKNOWN_DOI)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_INVALID_SITUATION)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_DH_FAILURE)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_INVALID_GROUP)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_ENCRYPT)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_DECRYPT)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_POLICY_MATCH)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_UNSUPPORTED_ID)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_INVALID_HASH)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_INVALID_HASH_ALG)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_INVALID_HASH_SIZE)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_INVALID_ENCRYPT_ALG)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_INVALID_AUTH_ALG)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_INVALID_SIG)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_LOAD_FAILED)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_RPC_DELETE)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_BENIGN_REINIT)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_INVALID_RESPONDER_LIFETIME_NOTIFY)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_INVALID_CERT_KEYLEN)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_MM_LIMIT)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_NEGOTIATION_DISABLED)
        CHK_ERR_WIN32A(ERROR_IPSEC_IKE_NEG_STATUS_END)

// -------------------------------------------------------------
// ddraw.h error codes
// -------------------------------------------------------------
        CHK_ERRA(DDERR_ALREADYINITIALIZED)
        CHK_ERRA(DDERR_CANNOTATTACHSURFACE)
        CHK_ERRA(DDERR_CANNOTDETACHSURFACE)
        CHK_ERRA(DDERR_CURRENTLYNOTAVAIL)
        CHK_ERRA(DDERR_EXCEPTION)
//      CHK_ERRA(DDERR_GENERIC)
        CHK_ERRA(DDERR_HEIGHTALIGN)
        CHK_ERRA(DDERR_INCOMPATIBLEPRIMARY)
        CHK_ERRA(DDERR_INVALIDCAPS)
        CHK_ERRA(DDERR_INVALIDCLIPLIST)
        CHK_ERRA(DDERR_INVALIDMODE)
        CHK_ERRA(DDERR_INVALIDOBJECT)
//        CHK_ERRA(DDERR_INVALIDPARAMS)
        CHK_ERRA(DDERR_INVALIDPIXELFORMAT)
        CHK_ERRA(DDERR_INVALIDRECT)
        CHK_ERRA(DDERR_LOCKEDSURFACES)
        CHK_ERRA(DDERR_NO3D)
        CHK_ERRA(DDERR_NOALPHAHW)
        CHK_ERRA(DDERR_NOSTEREOHARDWARE)
        CHK_ERRA(DDERR_NOSURFACELEFT)
        CHK_ERRA(DDERR_NOCLIPLIST)
        CHK_ERRA(DDERR_NOCOLORCONVHW)
        CHK_ERRA(DDERR_NOCOOPERATIVELEVELSET)
        CHK_ERRA(DDERR_NOCOLORKEY)
        CHK_ERRA(DDERR_NOCOLORKEYHW)
        CHK_ERRA(DDERR_NODIRECTDRAWSUPPORT)
        CHK_ERRA(DDERR_NOEXCLUSIVEMODE)
        CHK_ERRA(DDERR_NOFLIPHW)
        CHK_ERRA(DDERR_NOGDI)
        CHK_ERRA(DDERR_NOMIRRORHW)
        CHK_ERRA(DDERR_NOTFOUND)
        CHK_ERRA(DDERR_NOOVERLAYHW)
        CHK_ERRA(DDERR_OVERLAPPINGRECTS)
        CHK_ERRA(DDERR_NORASTEROPHW)
        CHK_ERRA(DDERR_NOROTATIONHW)
        CHK_ERRA(DDERR_NOSTRETCHHW)
        CHK_ERRA(DDERR_NOT4BITCOLOR)
        CHK_ERRA(DDERR_NOT4BITCOLORINDEX)
        CHK_ERRA(DDERR_NOT8BITCOLOR)
        CHK_ERRA(DDERR_NOTEXTUREHW)
        CHK_ERRA(DDERR_NOVSYNCHW)
        CHK_ERRA(DDERR_NOZBUFFERHW)
        CHK_ERRA(DDERR_NOZOVERLAYHW)
        CHK_ERRA(DDERR_OUTOFCAPS)
//        CHK_ERRA(DDERR_OUTOFMEMORY)
//        CHK_ERRA(DDERR_OUTOFVIDEOMEMORY)
        CHK_ERRA(DDERR_OVERLAYCANTCLIP)
        CHK_ERRA(DDERR_OVERLAYCOLORKEYONLYONEACTIVE)
        CHK_ERRA(DDERR_PALETTEBUSY)
        CHK_ERRA(DDERR_COLORKEYNOTSET)
        CHK_ERRA(DDERR_SURFACEALREADYATTACHED)
        CHK_ERRA(DDERR_SURFACEALREADYDEPENDENT)
        CHK_ERRA(DDERR_SURFACEBUSY)
        CHK_ERRA(DDERR_CANTLOCKSURFACE)
        CHK_ERRA(DDERR_SURFACEISOBSCURED)
        CHK_ERRA(DDERR_SURFACELOST)
        CHK_ERRA(DDERR_SURFACENOTATTACHED)
        CHK_ERRA(DDERR_TOOBIGHEIGHT)
        CHK_ERRA(DDERR_TOOBIGSIZE)
        CHK_ERRA(DDERR_TOOBIGWIDTH)
//        CHK_ERRA(DDERR_UNSUPPORTED)
        CHK_ERRA(DDERR_UNSUPPORTEDFORMAT)
        CHK_ERRA(DDERR_UNSUPPORTEDMASK)
        CHK_ERRA(DDERR_INVALIDSTREAM)
        CHK_ERRA(DDERR_VERTICALBLANKINPROGRESS)
        CHK_ERRA(DDERR_WASSTILLDRAWING)
        CHK_ERRA(DDERR_DDSCAPSCOMPLEXREQUIRED)
        CHK_ERRA(DDERR_XALIGN)
        CHK_ERRA(DDERR_INVALIDDIRECTDRAWGUID)
        CHK_ERRA(DDERR_DIRECTDRAWALREADYCREATED)
        CHK_ERRA(DDERR_NODIRECTDRAWHW)
        CHK_ERRA(DDERR_PRIMARYSURFACEALREADYEXISTS)
        CHK_ERRA(DDERR_NOEMULATION)
        CHK_ERRA(DDERR_REGIONTOOSMALL)
        CHK_ERRA(DDERR_CLIPPERISUSINGHWND)
        CHK_ERRA(DDERR_NOCLIPPERATTACHED)
        CHK_ERRA(DDERR_NOHWND)
        CHK_ERRA(DDERR_HWNDSUBCLASSED)
        CHK_ERRA(DDERR_HWNDALREADYSET)
        CHK_ERRA(DDERR_NOPALETTEATTACHED)
        CHK_ERRA(DDERR_NOPALETTEHW)
        CHK_ERRA(DDERR_BLTFASTCANTCLIP)
        CHK_ERRA(DDERR_NOBLTHW)
        CHK_ERRA(DDERR_NODDROPSHW)
        CHK_ERRA(DDERR_OVERLAYNOTVISIBLE)
        CHK_ERRA(DDERR_NOOVERLAYDEST)
        CHK_ERRA(DDERR_INVALIDPOSITION)
        CHK_ERRA(DDERR_NOTAOVERLAYSURFACE)
        CHK_ERRA(DDERR_EXCLUSIVEMODEALREADYSET)
        CHK_ERRA(DDERR_NOTFLIPPABLE)
        CHK_ERRA(DDERR_CANTDUPLICATE)
        CHK_ERRA(DDERR_NOTLOCKED)
        CHK_ERRA(DDERR_CANTCREATEDC)
        CHK_ERRA(DDERR_NODC)
        CHK_ERRA(DDERR_WRONGMODE)
        CHK_ERRA(DDERR_IMPLICITLYCREATED)
        CHK_ERRA(DDERR_NOTPALETTIZED)
        CHK_ERRA(DDERR_UNSUPPORTEDMODE)
        CHK_ERRA(DDERR_NOMIPMAPHW)
        CHK_ERRA(DDERR_INVALIDSURFACETYPE)
        CHK_ERRA(DDERR_NOOPTIMIZEHW)
        CHK_ERRA(DDERR_NOTLOADED)
        CHK_ERRA(DDERR_NOFOCUSWINDOW)
        CHK_ERRA(DDERR_NOTONMIPMAPSUBLEVEL)
        CHK_ERRA(DDERR_DCALREADYCREATED)
        CHK_ERRA(DDERR_NONONLOCALVIDMEM)
        CHK_ERRA(DDERR_CANTPAGELOCK)
        CHK_ERRA(DDERR_CANTPAGEUNLOCK)
        CHK_ERRA(DDERR_NOTPAGELOCKED)
        CHK_ERRA(DDERR_MOREDATA)
        CHK_ERRA(DDERR_EXPIRED)
        CHK_ERRA(DDERR_TESTFINISHED)
        CHK_ERRA(DDERR_NEWMODE)
        CHK_ERRA(DDERR_D3DNOTINITIALIZED)
        CHK_ERRA(DDERR_VIDEONOTACTIVE)
        CHK_ERRA(DDERR_NOMONITORINFORMATION)
        CHK_ERRA(DDERR_NODRIVERSUPPORT)
        CHK_ERRA(DDERR_DEVICEDOESNTOWNSURFACE)

// -------------------------------------------------------------
// d3d10.h error codes
// -------------------------------------------------------------
        CHK_ERRA(D3D10_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS)
        CHK_ERRA(D3D10_ERROR_FILE_NOT_FOUND)

// -------------------------------------------------------------
// dxgi.h error codes
// -------------------------------------------------------------
        CHK_ERRA(DXGI_STATUS_OCCLUDED)
        CHK_ERRA(DXGI_STATUS_CLIPPED)
        CHK_ERRA(DXGI_STATUS_NO_REDIRECTION)
        CHK_ERRA(DXGI_STATUS_NO_DESKTOP_ACCESS)
        CHK_ERRA(DXGI_STATUS_GRAPHICS_VIDPN_SOURCE_IN_USE)
        CHK_ERRA(DXGI_STATUS_MODE_CHANGED)
        CHK_ERRA(DXGI_STATUS_MODE_CHANGE_IN_PROGRESS)
        CHK_ERRA(DXGI_ERROR_INVALID_CALL)
        CHK_ERRA(DXGI_ERROR_NOT_FOUND)
        CHK_ERRA(DXGI_ERROR_MORE_DATA)
        CHK_ERRA(DXGI_ERROR_UNSUPPORTED)
        CHK_ERRA(DXGI_ERROR_DEVICE_REMOVED)
        CHK_ERRA(DXGI_ERROR_DEVICE_HUNG)
        CHK_ERRA(DXGI_ERROR_DEVICE_RESET)
        CHK_ERRA(DXGI_ERROR_WAS_STILL_DRAWING)
        CHK_ERRA(DXGI_ERROR_FRAME_STATISTICS_DISJOINT)
        CHK_ERRA(DXGI_ERROR_GRAPHICS_VIDPN_SOURCE_IN_USE)
        CHK_ERRA(DXGI_ERROR_DRIVER_INTERNAL_ERROR)
        CHK_ERRA(DXGI_ERROR_NONEXCLUSIVE)
        CHK_ERRA(DXGI_ERROR_NOT_CURRENTLY_AVAILABLE)
        CHK_ERRA(DXGI_ERROR_REMOTE_CLIENT_DISCONNECTED)
        CHK_ERRA(DXGI_ERROR_REMOTE_OUTOFMEMORY)

// -------------------------------------------------------------
// d3d11.h error codes
// -------------------------------------------------------------
        CHK_ERRA(D3D11_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS)
        CHK_ERRA(D3D11_ERROR_FILE_NOT_FOUND)
        CHK_ERRA(D3D11_ERROR_TOO_MANY_UNIQUE_VIEW_OBJECTS)
        CHK_ERRA(D3D11_ERROR_DEFERRED_CONTEXT_MAP_WITHOUT_INITIAL_DISCARD)

// -------------------------------------------------------------
// Direct2D error codes
// -------------------------------------------------------------
        CHK_ERRA(D2DERR_UNSUPPORTED_PIXEL_FORMAT)
//        CHK_ERRA(D2DERR_INSUFFICIENT_BUFFER)
        CHK_ERRA(D2DERR_WRONG_STATE)
        CHK_ERRA(D2DERR_NOT_INITIALIZED)
        CHK_ERRA(D2DERR_UNSUPPORTED_OPERATION)
        CHK_ERRA(D2DERR_SCANNER_FAILED)
        CHK_ERRA(D2DERR_SCREEN_ACCESS_DENIED)
        CHK_ERRA(D2DERR_DISPLAY_STATE_INVALID)
        CHK_ERRA(D2DERR_ZERO_VECTOR)
        CHK_ERRA(D2DERR_INTERNAL_ERROR)
        CHK_ERRA(D2DERR_DISPLAY_FORMAT_NOT_SUPPORTED)
        CHK_ERRA(D2DERR_INVALID_CALL)
        CHK_ERRA(D2DERR_NO_HARDWARE_DEVICE)
        CHK_ERRA(D2DERR_RECREATE_TARGET)
        CHK_ERRA(D2DERR_TOO_MANY_SHADER_ELEMENTS)
        CHK_ERRA(D2DERR_SHADER_COMPILE_FAILED)
        CHK_ERRA(D2DERR_MAX_TEXTURE_SIZE_EXCEEDED)
        CHK_ERRA(D2DERR_UNSUPPORTED_VERSION)
        CHK_ERRA(D2DERR_BAD_NUMBER)
        CHK_ERRA(D2DERR_WRONG_FACTORY)
        CHK_ERRA(D2DERR_LAYER_ALREADY_IN_USE)
        CHK_ERRA(D2DERR_POP_CALL_DID_NOT_MATCH_PUSH)
//        CHK_ERRA(D2DERR_WRONG_RESOURCE_DOMAIN)
        CHK_ERRA(D2DERR_PUSH_POP_UNBALANCED)
        CHK_ERRA(D2DERR_RENDER_TARGET_HAS_LAYER_OR_CLIPRECT)
        CHK_ERRA(D2DERR_INCOMPATIBLE_BRUSH_TYPES)
        CHK_ERRA(D2DERR_WIN32_ERROR)
        CHK_ERRA(D2DERR_TARGET_NOT_GDI_COMPATIBLE)
        CHK_ERRA(D2DERR_TEXT_EFFECT_IS_WRONG_TYPE)
        CHK_ERRA(D2DERR_TEXT_RENDERER_NOT_RELEASED)
//        CHK_ERRA(D2DERR_EXCEEDS_MAX_BITMAP_SIZE)

// -------------------------------------------------------------
// DirectWrite error codes
// -------------------------------------------------------------
        CHK_ERRA(DWRITE_E_FILEFORMAT)
        CHK_ERRA(DWRITE_E_UNEXPECTED)
        CHK_ERRA(DWRITE_E_NOFONT)
        CHK_ERRA(DWRITE_E_FILENOTFOUND)
        CHK_ERRA(DWRITE_E_FILEACCESS)
        CHK_ERRA(DWRITE_E_FONTCOLLECTIONOBSOLETE)
        CHK_ERRA(DWRITE_E_ALREADYREGISTERED)

// -------------------------------------------------------------
// WIC error codes
// -------------------------------------------------------------
        CHK_ERRA(WINCODEC_ERR_WRONGSTATE)
        CHK_ERRA(WINCODEC_ERR_VALUEOUTOFRANGE)
        CHK_ERRA(WINCODEC_ERR_UNKNOWNIMAGEFORMAT)
        CHK_ERRA(WINCODEC_ERR_UNSUPPORTEDVERSION)
        CHK_ERRA(WINCODEC_ERR_NOTINITIALIZED)
        CHK_ERRA(WINCODEC_ERR_ALREADYLOCKED)
        CHK_ERRA(WINCODEC_ERR_PROPERTYNOTFOUND)
        CHK_ERRA(WINCODEC_ERR_PROPERTYNOTSUPPORTED)
        CHK_ERRA(WINCODEC_ERR_PROPERTYSIZE)
        CHK_ERRA(WINCODEC_ERR_CODECPRESENT)
        CHK_ERRA(WINCODEC_ERR_CODECNOTHUMBNAIL)
        CHK_ERRA(WINCODEC_ERR_PALETTEUNAVAILABLE)
        CHK_ERRA(WINCODEC_ERR_CODECTOOMANYSCANLINES)
        CHK_ERRA(WINCODEC_ERR_INTERNALERROR)
        CHK_ERRA(WINCODEC_ERR_SOURCERECTDOESNOTMATCHDIMENSIONS)
        CHK_ERRA(WINCODEC_ERR_COMPONENTNOTFOUND)
        CHK_ERRA(WINCODEC_ERR_IMAGESIZEOUTOFRANGE)
        CHK_ERRA(WINCODEC_ERR_TOOMUCHMETADATA)
        CHK_ERRA(WINCODEC_ERR_BADIMAGE)
        CHK_ERRA(WINCODEC_ERR_BADHEADER)
        CHK_ERRA(WINCODEC_ERR_FRAMEMISSING)
        CHK_ERRA(WINCODEC_ERR_BADMETADATAHEADER)
        CHK_ERRA(WINCODEC_ERR_BADSTREAMDATA)
        CHK_ERRA(WINCODEC_ERR_STREAMWRITE)
        CHK_ERRA(WINCODEC_ERR_STREAMREAD)
        CHK_ERRA(WINCODEC_ERR_STREAMNOTAVAILABLE)
//        CHK_ERRA(WINCODEC_ERR_UNSUPPORTEDPIXELFORMAT)
        CHK_ERRA(WINCODEC_ERR_UNSUPPORTEDOPERATION)
        CHK_ERRA(WINCODEC_ERR_INVALIDREGISTRATION)
        CHK_ERRA(WINCODEC_ERR_COMPONENTINITIALIZEFAILURE)
        CHK_ERRA(WINCODEC_ERR_INSUFFICIENTBUFFER)
        CHK_ERRA(WINCODEC_ERR_DUPLICATEMETADATAPRESENT)
        CHK_ERRA(WINCODEC_ERR_PROPERTYUNEXPECTEDTYPE)
        CHK_ERRA(WINCODEC_ERR_UNEXPECTEDSIZE)
        CHK_ERRA(WINCODEC_ERR_INVALIDQUERYREQUEST)
        CHK_ERRA(WINCODEC_ERR_UNEXPECTEDMETADATATYPE)
        CHK_ERRA(WINCODEC_ERR_REQUESTONLYVALIDATMETADATAROOT)
        CHK_ERRA(WINCODEC_ERR_INVALIDQUERYCHARACTER)
        CHK_ERRA(WINCODEC_ERR_WIN32ERROR)
        CHK_ERRA(WINCODEC_ERR_INVALIDPROGRESSIVELEVEL)

// -------------------------------------------------------------
// DXUT error codes
// -------------------------------------------------------------
        CHK_ERRA(DXUTERR_NODIRECT3D)
        CHK_ERRA(DXUTERR_NOCOMPATIBLEDEVICES)
        CHK_ERRA(DXUTERR_MEDIANOTFOUND)
        CHK_ERRA(DXUTERR_NONZEROREFCOUNT)
        CHK_ERRA(DXUTERR_CREATINGDEVICE)
        CHK_ERRA(DXUTERR_RESETTINGDEVICE)
        CHK_ERRA(DXUTERR_CREATINGDEVICEOBJECTS)
        CHK_ERRA(DXUTERR_RESETTINGDEVICEOBJECTS)
        CHK_ERRA(DXUTERR_INCORRECTVERSION)
        CHK_ERRA(DXUTERR_DEVICEREMOVED)

// -------------------------------------------------------------
// xaudio2.h error codes
// -------------------------------------------------------------
        CHK_ERRA(_HRESULT_TYPEDEF_(XAUDIO2_E_INVALID_CALL))
        CHK_ERRA(_HRESULT_TYPEDEF_(XAUDIO2_E_XMA_DECODER_ERROR))
        CHK_ERRA(_HRESULT_TYPEDEF_(XAUDIO2_E_XAPO_CREATION_FAILED))
        CHK_ERRA(_HRESULT_TYPEDEF_(XAUDIO2_E_DEVICE_INVALIDATED))

// -------------------------------------------------------------
// xapo.h error codes
// -------------------------------------------------------------
        CHK_ERRA(XAPO_E_FORMAT_UNSUPPORTED)

        default: return L"Unknown error.";
    }
}

//--------------------------------------------------------------------------------------
#undef CHK_ERR
#undef CHK_ERRA
#undef HRESULT_FROM_WIN32b
#undef CHK_ERR_WIN32A
#undef CHK_ERR_WIN32_ONLY

#define  CHK_ERRA(hrchk) \
        case static_cast<HRESULT>(hrchk): \
             wcscpy_s( desc, count, L## #hrchk ); break;

#define  CHK_ERR(hrchk, strOut) \
        case static_cast<HRESULT>(hrchk): \
             wcscpy_s( desc, count, L##strOut ); break;


//--------------------------------------------------------------------------------------
void WINAPI DXGetErrorDescriptionW( _In_ HRESULT hr, _Out_cap_(count) WCHAR* desc, _In_ size_t count )
{
    if ( !count )
        return;

    *desc = 0;

    // First try to see if FormatMessage knows this hr
    LPWSTR errorText = nullptr;

    DWORD result = FormatMessageW( FORMAT_MESSAGE_FROM_SYSTEM | FORMAT_MESSAGE_IGNORE_INSERTS| FORMAT_MESSAGE_ALLOCATE_BUFFER,
                                   nullptr, static_cast<DWORD>(hr),
                                   MAKELANGID(LANG_NEUTRAL, SUBLANG_DEFAULT), reinterpret_cast<LPWSTR>(&errorText), 0, nullptr );

    if (result > 0 && errorText)
    {
        wcscpy_s( desc, count, errorText );

        if ( errorText )
            LocalFree( errorText );

        return;
    }

    switch (hr)
    {
// Commmented out codes are actually alises for other codes

// -------------------------------------------------------------
// ddraw.h error codes
// -------------------------------------------------------------
        CHK_ERR(DDERR_ALREADYINITIALIZED, "This object is already initialized")
        CHK_ERR(DDERR_CANNOTATTACHSURFACE, "This surface can not be attached to the requested surface.")
        CHK_ERR(DDERR_CANNOTDETACHSURFACE, "This surface can not be detached from the requested surface.")
        CHK_ERR(DDERR_CURRENTLYNOTAVAIL, "Support is currently not available.")
        CHK_ERR(DDERR_EXCEPTION, "An exception was encountered while performing the requested operation")
//      CHK_ERR(DDERR_GENERIC, "DDERR_GENERIC")
        CHK_ERR(DDERR_HEIGHTALIGN, "Height of rectangle provided is not a multiple of reqd alignment")
        CHK_ERR(DDERR_INCOMPATIBLEPRIMARY, "Unable to match primary surface creation request with existing primary surface.")
        CHK_ERR(DDERR_INVALIDCAPS, "One or more of the caps bits passed to the callback are incorrect.")
        CHK_ERR(DDERR_INVALIDCLIPLIST, "DirectDraw does not support provided Cliplist.")
        CHK_ERR(DDERR_INVALIDMODE, "DirectDraw does not support the requested mode")
        CHK_ERR(DDERR_INVALIDOBJECT, "DirectDraw received a pointer that was an invalid DIRECTDRAW object.")
//        CHK_ERR(DDERR_INVALIDPARAMS, "DDERR_INVALIDPARAMS")
        CHK_ERR(DDERR_INVALIDPIXELFORMAT, "pixel format was invalid as specified")
        CHK_ERR(DDERR_INVALIDRECT, "Rectangle provided was invalid.")
        CHK_ERR(DDERR_LOCKEDSURFACES, "Operation could not be carried out because one or more surfaces are locked")
        CHK_ERR(DDERR_NO3D, "There is no 3D present.")
        CHK_ERR(DDERR_NOALPHAHW, "Operation could not be carried out because there is no alpha accleration hardware present or available.")
        CHK_ERR(DDERR_NOSTEREOHARDWARE, "Operation could not be carried out because there is no stereo hardware present or available.")
        CHK_ERR(DDERR_NOSURFACELEFT, "Operation could not be carried out because there is no hardware present which supports stereo surfaces")
        CHK_ERR(DDERR_NOCLIPLIST, "no clip list available")
        CHK_ERR(DDERR_NOCOLORCONVHW, "Operation could not be carried out because there is no color conversion hardware present or available.")
        CHK_ERR(DDERR_NOCOOPERATIVELEVELSET, "Create function called without DirectDraw object method SetCooperativeLevel being called.")
        CHK_ERR(DDERR_NOCOLORKEY, "Surface doesn't currently have a color key")
        CHK_ERR(DDERR_NOCOLORKEYHW, "Operation could not be carried out because there is no hardware support of the dest color key.")
        CHK_ERR(DDERR_NODIRECTDRAWSUPPORT, "No DirectDraw support possible with current display driver")
        CHK_ERR(DDERR_NOEXCLUSIVEMODE, "Operation requires the application to have exclusive mode but the application does not have exclusive mode.")
        CHK_ERR(DDERR_NOFLIPHW, "Flipping visible surfaces is not supported.")
        CHK_ERR(DDERR_NOGDI, "There is no GDI present.")
        CHK_ERR(DDERR_NOMIRRORHW, "Operation could not be carried out because there is no hardware present or available.")
        CHK_ERR(DDERR_NOTFOUND, "Requested item was not found")
        CHK_ERR(DDERR_NOOVERLAYHW, "Operation could not be carried out because there is no overlay hardware present or available.")
        CHK_ERR(DDERR_OVERLAPPINGRECTS, "Operation could not be carried out because the source and destination rectangles are on the same surface and overlap each other.")
        CHK_ERR(DDERR_NORASTEROPHW, "Operation could not be carried out because there is no appropriate raster op hardware present or available.")
        CHK_ERR(DDERR_NOROTATIONHW, "Operation could not be carried out because there is no rotation hardware present or available.")
        CHK_ERR(DDERR_NOSTRETCHHW, "Operation could not be carried out because there is no hardware support for stretching")
        CHK_ERR(DDERR_NOT4BITCOLOR, "DirectDrawSurface is not in 4 bit color palette and the requested operation requires 4 bit color palette.")
        CHK_ERR(DDERR_NOT4BITCOLORINDEX, "DirectDrawSurface is not in 4 bit color index palette and the requested operation requires 4 bit color index palette.")
        CHK_ERR(DDERR_NOT8BITCOLOR, "DirectDraw Surface is not in 8 bit color mode and the requested operation requires 8 bit color.")
        CHK_ERR(DDERR_NOTEXTUREHW, "Operation could not be carried out because there is no texture mapping hardware present or available.")
        CHK_ERR(DDERR_NOVSYNCHW, "Operation could not be carried out because there is no hardware support for vertical blank synchronized operations.")
        CHK_ERR(DDERR_NOZBUFFERHW, "Operation could not be carried out because there is no hardware support for zbuffer blting.")
        CHK_ERR(DDERR_NOZOVERLAYHW, "Overlay surfaces could not be z layered based on their BltOrder because the hardware does not support z layering of overlays.")
        CHK_ERR(DDERR_OUTOFCAPS, "The hardware needed for the requested operation has already been allocated.")
//        CHK_ERR(DDERR_OUTOFMEMORY, "DDERR_OUTOFMEMORY")
//        CHK_ERR(DDERR_OUTOFVIDEOMEMORY, "DDERR_OUTOFVIDEOMEMORY")
        CHK_ERR(DDERR_OVERLAYCANTCLIP, "hardware does not support clipped overlays")
        CHK_ERR(DDERR_OVERLAYCOLORKEYONLYONEACTIVE, "Can only have ony color key active at one time for overlays")
        CHK_ERR(DDERR_PALETTEBUSY, "Access to this palette is being refused because the palette is already locked by another thread.")
        CHK_ERR(DDERR_COLORKEYNOTSET, "No src color key specified for this operation.")
        CHK_ERR(DDERR_SURFACEALREADYATTACHED, "This surface is already attached to the surface it is being attached to.")
        CHK_ERR(DDERR_SURFACEALREADYDEPENDENT, "This surface is already a dependency of the surface it is being made a dependency of.")
        CHK_ERR(DDERR_SURFACEBUSY, "Access to this surface is being refused because the surface is already locked by another thread.")
        CHK_ERR(DDERR_CANTLOCKSURFACE, "Access to this surface is being refused because no driver exists which can supply a pointer to the surface. This is most likely to happen when attempting to lock the primary surface when no DCI provider is present. Will also happen on attempts to lock an optimized surface.")
        CHK_ERR(DDERR_SURFACEISOBSCURED, "Access to Surface refused because Surface is obscured.")
        CHK_ERR(DDERR_SURFACELOST, "Access to this surface is being refused because the surface is gone. The DIRECTDRAWSURFACE object representing this surface should have Restore called on it.")
        CHK_ERR(DDERR_SURFACENOTATTACHED, "The requested surface is not attached.")
        CHK_ERR(DDERR_TOOBIGHEIGHT, "Height requested by DirectDraw is too large.")
        CHK_ERR(DDERR_TOOBIGSIZE, "Size requested by DirectDraw is too large --  The individual height and width are OK.")
        CHK_ERR(DDERR_TOOBIGWIDTH, "Width requested by DirectDraw is too large.")
//        CHK_ERR(DDERR_UNSUPPORTED, "DDERR_UNSUPPORTED")
        CHK_ERR(DDERR_UNSUPPORTEDFORMAT, "Pixel format requested is unsupported by DirectDraw")
        CHK_ERR(DDERR_UNSUPPORTEDMASK, "Bitmask in the pixel format requested is unsupported by DirectDraw")
        CHK_ERR(DDERR_INVALIDSTREAM, "The specified stream contains invalid data")
        CHK_ERR(DDERR_VERTICALBLANKINPROGRESS, "vertical blank is in progress")
        CHK_ERR(DDERR_WASSTILLDRAWING, "Was still drawing")
        CHK_ERR(DDERR_DDSCAPSCOMPLEXREQUIRED, "The specified surface type requires specification of the COMPLEX flag")
        CHK_ERR(DDERR_XALIGN, "Rectangle provided was not horizontally aligned on reqd. boundary")
        CHK_ERR(DDERR_INVALIDDIRECTDRAWGUID, "The GUID passed to DirectDrawCreate is not a valid DirectDraw driver identifier.")
        CHK_ERR(DDERR_DIRECTDRAWALREADYCREATED, "A DirectDraw object representing this driver has already been created for this process.")
        CHK_ERR(DDERR_NODIRECTDRAWHW, "A hardware only DirectDraw object creation was attempted but the driver did not support any hardware.")
        CHK_ERR(DDERR_PRIMARYSURFACEALREADYEXISTS, "this process already has created a primary surface")
        CHK_ERR(DDERR_NOEMULATION, "software emulation not available.")
        CHK_ERR(DDERR_REGIONTOOSMALL, "region passed to Clipper::GetClipList is too small.")
        CHK_ERR(DDERR_CLIPPERISUSINGHWND, "an attempt was made to set a clip list for a clipper objec that is already monitoring an hwnd.")
        CHK_ERR(DDERR_NOCLIPPERATTACHED, "No clipper object attached to surface object")
        CHK_ERR(DDERR_NOHWND, "Clipper notification requires an HWND or no HWND has previously been set as the CooperativeLevel HWND.")
        CHK_ERR(DDERR_HWNDSUBCLASSED, "HWND used by DirectDraw CooperativeLevel has been subclassed, this prevents DirectDraw from restoring state.")
        CHK_ERR(DDERR_HWNDALREADYSET, "The CooperativeLevel HWND has already been set. It can not be reset while the process has surfaces or palettes created.")
        CHK_ERR(DDERR_NOPALETTEATTACHED, "No palette object attached to this surface.")
        CHK_ERR(DDERR_NOPALETTEHW, "No hardware support for 16 or 256 color palettes.")
        CHK_ERR(DDERR_BLTFASTCANTCLIP, "If a clipper object is attached to the source surface passed into a BltFast call.")
        CHK_ERR(DDERR_NOBLTHW, "No blter.")
        CHK_ERR(DDERR_NODDROPSHW, "No DirectDraw ROP hardware.")
        CHK_ERR(DDERR_OVERLAYNOTVISIBLE, "returned when GetOverlayPosition is called on a hidden overlay")
        CHK_ERR(DDERR_NOOVERLAYDEST, "returned when GetOverlayPosition is called on a overlay that UpdateOverlay has never been called on to establish a destionation.")
        CHK_ERR(DDERR_INVALIDPOSITION, "returned when the position of the overlay on the destionation is no longer legal for that destionation.")
        CHK_ERR(DDERR_NOTAOVERLAYSURFACE, "returned when an overlay member is called for a non-overlay surface")
        CHK_ERR(DDERR_EXCLUSIVEMODEALREADYSET, "An attempt was made to set the cooperative level when it was already set to exclusive.")
        CHK_ERR(DDERR_NOTFLIPPABLE, "An attempt has been made to flip a surface that is not flippable.")
        CHK_ERR(DDERR_CANTDUPLICATE, "Can't duplicate primary & 3D surfaces, or surfaces that are implicitly created.")
        CHK_ERR(DDERR_NOTLOCKED, "Surface was not locked.  An attempt to unlock a surface that was not locked at all, or by this process, has been attempted.")
        CHK_ERR(DDERR_CANTCREATEDC, "Windows can not create any more DCs, or a DC was requested for a paltte-indexed surface when the surface had no palette AND the display mode was not palette-indexed (in this case DirectDraw cannot select a proper palette into the DC)")
        CHK_ERR(DDERR_NODC, "No DC was ever created for this surface.")
        CHK_ERR(DDERR_WRONGMODE, "This surface can not be restored because it was created in a different mode.")
        CHK_ERR(DDERR_IMPLICITLYCREATED, "This surface can not be restored because it is an implicitly created surface.")
        CHK_ERR(DDERR_NOTPALETTIZED, "The surface being used is not a palette-based surface")
        CHK_ERR(DDERR_UNSUPPORTEDMODE, "The display is currently in an unsupported mode")
        CHK_ERR(DDERR_NOMIPMAPHW, "Operation could not be carried out because there is no mip-map texture mapping hardware present or available.")
        CHK_ERR(DDERR_INVALIDSURFACETYPE, "The requested action could not be performed because the surface was of the wrong type.")
        CHK_ERR(DDERR_NOOPTIMIZEHW, "Device does not support optimized surfaces, therefore no video memory optimized surfaces")
        CHK_ERR(DDERR_NOTLOADED, "Surface is an optimized surface, but has not yet been allocated any memory")
        CHK_ERR(DDERR_NOFOCUSWINDOW, "Attempt was made to create or set a device window without first setting the focus window")
        CHK_ERR(DDERR_NOTONMIPMAPSUBLEVEL, "Attempt was made to set a palette on a mipmap sublevel")
        CHK_ERR(DDERR_DCALREADYCREATED, "A DC has already been returned for this surface. Only one DC can be retrieved per surface.")
        CHK_ERR(DDERR_NONONLOCALVIDMEM, "An attempt was made to allocate non-local video memory from a device that does not support non-local video memory.")
        CHK_ERR(DDERR_CANTPAGELOCK, "The attempt to page lock a surface failed.")
        CHK_ERR(DDERR_CANTPAGEUNLOCK, "The attempt to page unlock a surface failed.")
        CHK_ERR(DDERR_NOTPAGELOCKED, "An attempt was made to page unlock a surface with no outstanding page locks.")
        CHK_ERR(DDERR_MOREDATA, "There is more data available than the specified buffer size could hold")
        CHK_ERR(DDERR_EXPIRED, "The data has expired and is therefore no longer valid.")
        CHK_ERR(DDERR_TESTFINISHED, "The mode test has finished executing.")
        CHK_ERR(DDERR_NEWMODE, "The mode test has switched to a new mode.")
        CHK_ERR(DDERR_D3DNOTINITIALIZED, "D3D has not yet been initialized.")
        CHK_ERR(DDERR_VIDEONOTACTIVE, "The video port is not active")
        CHK_ERR(DDERR_NOMONITORINFORMATION, "The monitor does not have EDID data.")
        CHK_ERR(DDERR_NODRIVERSUPPORT, "The driver does not enumerate display mode refresh rates.")
        CHK_ERR(DDERR_DEVICEDOESNTOWNSURFACE, "Surfaces created by one direct draw device cannot be used directly by another direct draw device.")

// -------------------------------------------------------------
// d3d10.h error codes
// -------------------------------------------------------------
        CHK_ERR(D3D10_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS, "There are too many unique state objects.")
        CHK_ERR(D3D10_ERROR_FILE_NOT_FOUND, "File not found")

// -------------------------------------------------------------
// dxgi.h error codes
// -------------------------------------------------------------
        CHK_ERR(DXGI_STATUS_OCCLUDED, "The target window or output has been occluded. The application should suspend rendering operations if possible.")
        CHK_ERR(DXGI_STATUS_CLIPPED, "Target window is clipped.")
        CHK_ERR(DXGI_STATUS_NO_REDIRECTION, "")
        CHK_ERR(DXGI_STATUS_NO_DESKTOP_ACCESS, "No access to desktop.")
        CHK_ERR(DXGI_STATUS_GRAPHICS_VIDPN_SOURCE_IN_USE, "")
        CHK_ERR(DXGI_STATUS_MODE_CHANGED, "Display mode has changed")
        CHK_ERR(DXGI_STATUS_MODE_CHANGE_IN_PROGRESS, "Display mode is changing")
        CHK_ERR(DXGI_ERROR_INVALID_CALL, "The application has made an erroneous API call that it had enough information to avoid. This error is intended to denote that the application should be altered to avoid the error. Use of the debug version of the DXGI.DLL will provide run-time debug output with further information.")
        CHK_ERR(DXGI_ERROR_NOT_FOUND, "The item requested was not found. For GetPrivateData calls, this means that the specified GUID had not been previously associated with the object.")
        CHK_ERR(DXGI_ERROR_MORE_DATA, "The specified size of the destination buffer is too small to hold the requested data.")
        CHK_ERR(DXGI_ERROR_UNSUPPORTED, "Unsupported.")
        CHK_ERR(DXGI_ERROR_DEVICE_REMOVED, "Hardware device removed.")
        CHK_ERR(DXGI_ERROR_DEVICE_HUNG, "Device hung due to badly formed commands.")
        CHK_ERR(DXGI_ERROR_DEVICE_RESET, "Device reset due to a badly formed commant.")
        CHK_ERR(DXGI_ERROR_WAS_STILL_DRAWING, "Was still drawing.")
        CHK_ERR(DXGI_ERROR_FRAME_STATISTICS_DISJOINT, "The requested functionality is not supported by the device or the driver.")
        CHK_ERR(DXGI_ERROR_GRAPHICS_VIDPN_SOURCE_IN_USE, "The requested functionality is not supported by the device or the driver.")
        CHK_ERR(DXGI_ERROR_DRIVER_INTERNAL_ERROR, "An internal driver error occurred.")
        CHK_ERR(DXGI_ERROR_NONEXCLUSIVE, "The application attempted to perform an operation on an DXGI output that is only legal after the output has been claimed for exclusive owenership.")
        CHK_ERR(DXGI_ERROR_NOT_CURRENTLY_AVAILABLE, "The requested functionality is not supported by the device or the driver.")
        CHK_ERR(DXGI_ERROR_REMOTE_CLIENT_DISCONNECTED, "Remote desktop client disconnected.")
        CHK_ERR(DXGI_ERROR_REMOTE_OUTOFMEMORY, "Remote desktop client is out of memory.")

// -------------------------------------------------------------
// d3d11.h error codes
// -------------------------------------------------------------
        CHK_ERR(D3D11_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS, "There are too many unique state objects.")
        CHK_ERR(D3D11_ERROR_FILE_NOT_FOUND, "File not found")
        CHK_ERR(D3D11_ERROR_TOO_MANY_UNIQUE_VIEW_OBJECTS, "Therea are too many unique view objects.")
        CHK_ERR(D3D11_ERROR_DEFERRED_CONTEXT_MAP_WITHOUT_INITIAL_DISCARD, "Deferred context requires Map-Discard usage pattern")

// -------------------------------------------------------------
// Direct2D error codes
// -------------------------------------------------------------
        CHK_ERR(D2DERR_UNSUPPORTED_PIXEL_FORMAT, "The pixel format is not supported.")
//        CHK_ERR(D2DERR_INSUFFICIENT_BUFFER, "The supplied buffer was too small to accomodate the data.")
        CHK_ERR(D2DERR_WRONG_STATE, "The object was not in the correct state to process the method.")
        CHK_ERR(D2DERR_NOT_INITIALIZED, "The object has not yet been initialized.")
        CHK_ERR(D2DERR_UNSUPPORTED_OPERATION, "The requested opertion is not supported.")
        CHK_ERR(D2DERR_SCANNER_FAILED, "The geomery scanner failed to process the data.")
        CHK_ERR(D2DERR_SCREEN_ACCESS_DENIED, "D2D could not access the screen.")
        CHK_ERR(D2DERR_DISPLAY_STATE_INVALID, "A valid display state could not be determined.")
        CHK_ERR(D2DERR_ZERO_VECTOR, "The supplied vector is zero.")
        CHK_ERR(D2DERR_INTERNAL_ERROR, "An internal error (D2D bug) occurred. On checked builds, we would assert.")
        CHK_ERR(D2DERR_DISPLAY_FORMAT_NOT_SUPPORTED, "The display format we need to render is not supported by the hardware device.")
        CHK_ERR(D2DERR_INVALID_CALL, "A call to this method is invalid.")
        CHK_ERR(D2DERR_NO_HARDWARE_DEVICE, "No HW rendering device is available for this operation.")
        CHK_ERR(D2DERR_RECREATE_TARGET, "here has been a presentation error that may be recoverable. The caller needs to recreate, rerender the entire frame, and reattempt present.")
        CHK_ERR(D2DERR_TOO_MANY_SHADER_ELEMENTS, "Shader construction failed because it was too complex.")
        CHK_ERR(D2DERR_SHADER_COMPILE_FAILED, "Shader compilation failed.")
        CHK_ERR(D2DERR_MAX_TEXTURE_SIZE_EXCEEDED, "Requested DX surface size exceeded maximum texture size.")
        CHK_ERR(D2DERR_UNSUPPORTED_VERSION, "The requested D2D version is not supported.")
        CHK_ERR(D2DERR_BAD_NUMBER, "Invalid number.")
        CHK_ERR(D2DERR_WRONG_FACTORY, "Objects used together must be created from the same factory instance.")
        CHK_ERR(D2DERR_LAYER_ALREADY_IN_USE, "A layer resource can only be in use once at any point in time.")
        CHK_ERR(D2DERR_POP_CALL_DID_NOT_MATCH_PUSH, "The pop call did not match the corresponding push call")
//        CHK_ERR(D2DERR_WRONG_RESOURCE_DOMAIN, "The resource was realized on the wrong render target")
        CHK_ERR(D2DERR_PUSH_POP_UNBALANCED, "The push and pop calls were unbalanced")
        CHK_ERR(D2DERR_RENDER_TARGET_HAS_LAYER_OR_CLIPRECT, "Attempt to copy from a render target while a layer or clip rect is applied")
        CHK_ERR(D2DERR_INCOMPATIBLE_BRUSH_TYPES, "The brush types are incompatible for the call.")
        CHK_ERR(D2DERR_WIN32_ERROR, "An unknown win32 failure occurred.")
        CHK_ERR(D2DERR_TARGET_NOT_GDI_COMPATIBLE, "The render target is not compatible with GDI")
        CHK_ERR(D2DERR_TEXT_EFFECT_IS_WRONG_TYPE, "A text client drawing effect object is of the wrong type")
        CHK_ERR(D2DERR_TEXT_RENDERER_NOT_RELEASED, "The application is holding a reference to the IDWriteTextRenderer interface after the corresponding DrawText or DrawTextLayout call has returned. The IDWriteTextRenderer instance will be zombied.")
//        CHK_ERR(D2DERR_EXCEEDS_MAX_BITMAP_SIZE, "The requested size is larger than the guaranteed supported texture size.")

// -------------------------------------------------------------
// DirectWrite error codes
// -------------------------------------------------------------
        CHK_ERR(DWRITE_E_FILEFORMAT, "Indicates an error in an input file such as a font file.")
        CHK_ERR(DWRITE_E_UNEXPECTED, "Indicates an error originating in DirectWrite code, which is not expected to occur but is safe to recover from.")
        CHK_ERR(DWRITE_E_NOFONT, "Indicates the specified font does not exist.")
        CHK_ERR(DWRITE_E_FILENOTFOUND, "A font file could not be opened because the file, directory, network location, drive, or other storage location does not exist or is unavailable.")
        CHK_ERR(DWRITE_E_FILEACCESS, "A font file exists but could not be opened due to access denied, sharing violation, or similar error.")
        CHK_ERR(DWRITE_E_FONTCOLLECTIONOBSOLETE, "A font collection is obsolete due to changes in the system.")
        CHK_ERR(DWRITE_E_ALREADYREGISTERED, "The given interface is already registered.")

// -------------------------------------------------------------
// WIC error codes
// -------------------------------------------------------------
        CHK_ERR(WINCODEC_ERR_WRONGSTATE, "WIC object in incorrect state.")
        CHK_ERR(WINCODEC_ERR_VALUEOUTOFRANGE, "WIC Value out of range.")
        CHK_ERR(WINCODEC_ERR_UNKNOWNIMAGEFORMAT, "Encountered unexpected value or setting in WIC image format.")
        CHK_ERR(WINCODEC_ERR_UNSUPPORTEDVERSION, "Unsupported WINCODEC_SD_VERSION passed to WIC factory.")
        CHK_ERR(WINCODEC_ERR_NOTINITIALIZED, "WIC component not initialized.")
        CHK_ERR(WINCODEC_ERR_ALREADYLOCKED, "WIC bitmap object already locked.")
        CHK_ERR(WINCODEC_ERR_PROPERTYNOTFOUND, "WIC property not found.")
        CHK_ERR(WINCODEC_ERR_PROPERTYNOTSUPPORTED, "WIC property not supported.")
        CHK_ERR(WINCODEC_ERR_PROPERTYSIZE, "Invalid property size")
        CHK_ERRA(WINCODEC_ERR_CODECPRESENT) // not currently used by WIC
        CHK_ERRA(WINCODEC_ERR_CODECNOTHUMBNAIL) // not currently used by WIC
        CHK_ERR(WINCODEC_ERR_PALETTEUNAVAILABLE, "Required palette data not available.")
        CHK_ERR(WINCODEC_ERR_CODECTOOMANYSCANLINES, "More scanlines requested than are available in WIC bitmap.")
        CHK_ERR(WINCODEC_ERR_INTERNALERROR, "Unexpected internal error in WIC.")
        CHK_ERR(WINCODEC_ERR_SOURCERECTDOESNOTMATCHDIMENSIONS, "Source WIC rectangle does not match bitmap dimensions.")
        CHK_ERR(WINCODEC_ERR_COMPONENTNOTFOUND, "WIC component not found.")
        CHK_ERR(WINCODEC_ERR_IMAGESIZEOUTOFRANGE, "Image size beyond expected boundaries for WIC codec." )
        CHK_ERR(WINCODEC_ERR_TOOMUCHMETADATA, "Image metadata size beyond expected boundaries for WIC codec.")
        CHK_ERR(WINCODEC_ERR_BADIMAGE, "WIC image is corrupted.")
        CHK_ERR(WINCODEC_ERR_BADHEADER, "Invalid header found in WIC image.")
        CHK_ERR(WINCODEC_ERR_FRAMEMISSING, "Expected bitmap frame data not found in WIC image." )
        CHK_ERR(WINCODEC_ERR_BADMETADATAHEADER, "Invalid metadata header found in WIC image.")
        CHK_ERR(WINCODEC_ERR_BADSTREAMDATA, "Invalid stream data found in WIC image.")
        CHK_ERR(WINCODEC_ERR_STREAMWRITE, "WIC operation on write stream failed.")
        CHK_ERR(WINCODEC_ERR_STREAMREAD, "WIC operation on read stream failed.")
        CHK_ERR(WINCODEC_ERR_STREAMNOTAVAILABLE, "Required stream is not available." )
//        CHK_ERRA(WINCODEC_ERR_UNSUPPORTEDPIXELFORMAT)
        CHK_ERR(WINCODEC_ERR_UNSUPPORTEDOPERATION, "This operation is not supported by WIC." )
        CHK_ERR(WINCODEC_ERR_INVALIDREGISTRATION, "Error occurred reading WIC codec registry keys.")
        CHK_ERR(WINCODEC_ERR_COMPONENTINITIALIZEFAILURE, "Failed initializing WIC codec.")
        CHK_ERR(WINCODEC_ERR_INSUFFICIENTBUFFER, "Not enough buffer space available for WIC operation.")
        CHK_ERR(WINCODEC_ERR_DUPLICATEMETADATAPRESENT, "Duplicate metadata detected in WIC image.")
        CHK_ERR(WINCODEC_ERR_PROPERTYUNEXPECTEDTYPE, "Unexpected property type in WIC image.")
        CHK_ERR(WINCODEC_ERR_UNEXPECTEDSIZE, "Unexpected value size in WIC metadata.")
        CHK_ERR(WINCODEC_ERR_INVALIDQUERYREQUEST, "Invalid metadata query.")
        CHK_ERR(WINCODEC_ERR_UNEXPECTEDMETADATATYPE, "Unexpected metadata type encountered in WIC image.")
        CHK_ERR(WINCODEC_ERR_REQUESTONLYVALIDATMETADATAROOT, "Operation only valid on meatadata root.")
        CHK_ERR(WINCODEC_ERR_INVALIDQUERYCHARACTER, "Invalid character in WIC metadata query.")
        CHK_ERR(WINCODEC_ERR_WIN32ERROR, "General Win32 error encountered during WIC operation.")
        CHK_ERR(WINCODEC_ERR_INVALIDPROGRESSIVELEVEL, "Invalid level for progressive WIC image decode.")

// -------------------------------------------------------------
// DXUT error codes
// -------------------------------------------------------------
        CHK_ERR(DXUTERR_NODIRECT3D, "Could not initialize Direct3D.")
        CHK_ERR(DXUTERR_NOCOMPATIBLEDEVICES, "No device could be found with the specified device settings.")
        CHK_ERR(DXUTERR_MEDIANOTFOUND, "A media file could not be found.")
        CHK_ERR(DXUTERR_NONZEROREFCOUNT, "The device interface has a non-zero reference count, meaning that some objects were not released.")
        CHK_ERR(DXUTERR_CREATINGDEVICE, "An error occurred when attempting to create a device.")
        CHK_ERR(DXUTERR_RESETTINGDEVICE, "An error occurred when attempting to reset a device.")
        CHK_ERR(DXUTERR_CREATINGDEVICEOBJECTS, "An error occurred in the device create callback function.")
        CHK_ERR(DXUTERR_RESETTINGDEVICEOBJECTS, "An error occurred in the device reset callback function.")
        CHK_ERR(DXUTERR_INCORRECTVERSION, "Incorrect version of Direct3D or D3DX.")
        CHK_ERR(DXUTERR_DEVICEREMOVED, "The device was removed.")

// -------------------------------------------------------------
// xaudio2.h error codes
// -------------------------------------------------------------
        CHK_ERR(_HRESULT_TYPEDEF_(XAUDIO2_E_INVALID_CALL), "Invalid XAudio2 API call or arguments")
        CHK_ERR(_HRESULT_TYPEDEF_(XAUDIO2_E_XMA_DECODER_ERROR), "Hardware XMA decoder error")
        CHK_ERR(_HRESULT_TYPEDEF_(XAUDIO2_E_XAPO_CREATION_FAILED), "Failed to create an audio effect")
        CHK_ERR(_HRESULT_TYPEDEF_(XAUDIO2_E_DEVICE_INVALIDATED), "Device invalidated (unplugged, disabled, etc)")

// -------------------------------------------------------------
// xapo.h error codes
// -------------------------------------------------------------
        CHK_ERR(XAPO_E_FORMAT_UNSUPPORTED, "Requested audio format unsupported.")

        default: wcscpy_s( desc, count, L"Unknown error." ); break;
    }
}

//-----------------------------------------------------------------------------
HRESULT WINAPI DXTraceW( _In_z_ const WCHAR* strFile, _In_ DWORD dwLine, _In_ HRESULT hr,
                         _In_opt_ const WCHAR* strMsg, _In_ bool bPopMsgBox )
{
    WCHAR strBufferLine[128];
    WCHAR strBufferError[256];
    WCHAR strBuffer[BUFFER_SIZE];

    swprintf_s( strBufferLine, 128, L"%lu", dwLine );
    if( strFile )
    {
       swprintf_s( strBuffer, BUFFER_SIZE, L"%ls(%ls): ", strFile, strBufferLine );
       OutputDebugStringW( strBuffer );
    }

    size_t nMsgLen = (strMsg) ? wcsnlen_s( strMsg, 1024 ) : 0;
    if( nMsgLen > 0 )
    {
        OutputDebugStringW( strMsg );
        OutputDebugStringW( L" " );
    }

    swprintf_s( strBufferError, 256, L"%ls (0x%0.8x)", DXGetErrorStringW(hr), hr );
    swprintf_s( strBuffer, BUFFER_SIZE, L"hr=%ls", strBufferError );
    OutputDebugStringW( strBuffer );

    OutputDebugStringW( L"\n" );

    if( bPopMsgBox )
    {
        WCHAR strBufferFile[MAX_PATH];
        wcscpy_s( strBufferFile, MAX_PATH, L"" );
        if( strFile )
            wcscpy_s( strBufferFile, MAX_PATH, strFile );

        WCHAR strBufferMsg[1024];
        wcscpy_s( strBufferMsg, 1024, L"" );
        if( nMsgLen > 0 )
            swprintf_s( strBufferMsg, 1024, L"Calling: %ls\n", strMsg );

        swprintf_s( strBuffer, BUFFER_SIZE, L"File: %ls\nLine: %ls\nError Code: %ls\n%lsDo you want to debug the application?",
                    strBufferFile, strBufferLine, strBufferError, strBufferMsg );

        int nResult = MessageBoxW( GetForegroundWindow(), strBuffer, L"Unexpected error encountered", MB_YESNO | MB_ICONERROR );
        if( nResult == IDYES )
            DebugBreak();
    }

    return hr;
}
