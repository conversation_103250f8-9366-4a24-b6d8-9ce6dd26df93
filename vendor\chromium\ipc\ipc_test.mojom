// Copyright 2016 The Chromium Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

module IPC.mojom;

interface SimpleTestDriver {
  ExpectValue(int32 value);

  [Sync]
  GetExpectedValue() => (int32 value);

  [Sync]
  RequestValue() => (int32 value);

  RequestQuit() => ();
};

interface SimpleTestClient {
  [Sync]
  RequestValue() => (int32 value);
};

interface PingReceiver {
  Ping() => ();
};

struct TestStruct {};

interface TestStructPasser {
  Pass(TestStruct s);
};

interface IndirectTestDriver {
  GetPingReceiver(pending_associated_receiver<PingReceiver> receiver);
};

interface Reflector {
  Ping(string value) => (string value);
  [Sync]
  SyncPing(string value) => (string response);
  Quit();
};

interface AssociatedInterfaceVendor {
  GetTestInterface(pending_associated_receiver<SimpleTestDriver> receiver);
};

interface InterfacePassingTestDriver {
  Init() => ();
  GetPingReceiver(array<pending_receiver<PingReceiver>> receiver) => ();
  GetAssociatedPingReceiver(
      array<pending_associated_receiver<PingReceiver>> receiver) => ();
  Quit();
};
