# Copyright 2016 The Chromium Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

# Contains flags that we'd like all Chromium .apks to use.

# Not needed for Android and saves a bit of processing time.
-dontpreverify

# Keep line number information, useful for stack traces.
-keepattributes SourceFile,LineNumberTable

# Keep all CREATOR fields within Parcelable that are kept.
-keepclassmembers class * implements android.os.Parcelable {
  public static *** CREATOR;
}

# Keep all default constructors for used Fragments. Required since they are
# called reflectively when fragments are reinflated after the app is killed.
-keepclassmembers class * extends android.app.Fragment {
  public <init>();
}
-keepclassmembers class * extends android.support.v4.app.Fragment {
  public <init>();
}
-keepclassmembers class * extends androidx.fragment.app.Fragment {
  public <init>();
}

# Don't obfuscate Parcelables as they might be marshalled outside Chrome.
# If we annotated all Parcelables that get put into Bundles other than
# for saveInstanceState (e.g. PendingIntents), then we could actually keep the
# names of just those ones. For now, we'll just keep them all.
-keepnames class * implements android.os.Parcelable {}

# Keep all enum values and valueOf methods. See
# http://proguard.sourceforge.net/index.html#manual/examples.html
# for the reason for this. Also, see http://crbug.com/248037.
-keepclassmembers enum * {
    public static **[] values();
}

# Allows Proguard freedom in removing these log related calls.
-assumenosideeffects class android.util.Log {
  static *** d(...);
  static *** v(...);
  static boolean isLoggable(...);
}

# The following chart was created on July 20, 2016, to decide on 3 optimization
# passes for Chrome.
# optimization passes | time | .dex size | dirty memory per process
# -----------------------------------------------------------------
#          1          | 0:48 |  5805676  |         488972
#          2          | 1:07 |  5777376  |         487092
#          3          | 1:24 |  5772192  |         486596
#          4          | 1:42 |  5771124  |         486484
#          5          | 1:56 |  5770504  |         486432
-optimizationpasses 3

# Horizontal class merging marginally increases dex size (as of Mar 2018).
-optimizations !class/merging/horizontal

# Allowing Proguard to change modifiers. This change shrinks the .dex size by
# ~1%, and reduces the method count by ~4%.
-allowaccessmodification

# Workaround for crbug/1002847. Methods of BaseGmsClient are incorrectly
# removed even though they are required for the derived class GmsClient
# to correctly implement Api$Client.
# TODO: remove once crbug/1002847 resolved.
-keep public class com.google.android.gms.common.internal.BaseGmsClient {
  public void disconnect();
  public void dump(java.lang.String,java.io.FileDescriptor,java.io.PrintWriter,java.lang.String[]);
  public int getMinApkVersion();
  public boolean requiresSignIn();
}

