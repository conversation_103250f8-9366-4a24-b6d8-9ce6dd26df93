"ARTFeedbackInterface",
"ARTFeedbackInterfaceGta",
"ASFSinkWriterWrapper",
"AddTargetIterator",
"Allocator",
"AllotGameTransactionHttpTask",
"AllotRecoupTransaction",
"AmbientModelSetMounter",
"AudioClipDictionary",
"AutoIdDesc__",
"AutoIdDescriptor",
"AutoIdDescriptor_T",
"AutoState",
"AxisDefinition",
"BGScriptInfo",
"BackgroundScripts",
"BaseCS",
"BaseFilter",
"BaseModelInfoBoneIndices",
"BasePlayerCardDataManager",
"BaseShopItem",
"BaseShopPedApparel",
"BaseSocialClubMenu",
"BindTaskData",
"BlurCS",
"BoneModifier",
"BuildPsoVisitor",
"BulletApplier",
"BuyItemGameTransactionHttpTask",
"BuyPropertyGameTransactionHttpTask",
"BuyVehicleGameTransactionHttpTask",
"CAICurvePoint",
"CAIHandlingInfo",
"CAIHandlingInfoMgr",
"CActionDefinition",
"CActionInfoDataFileMounter",
"CActionResult",
"CActivePlayerCardDataManager",
"CAdminInvite",
"CAgitatedAction",
"CAgitatedActionAnger",
"CAgitatedActionApplyAgitation",
"CAgitatedActionCallPolice",
"CAgitatedActionChangeResponse",
"CAgitatedActionClearHash",
"CAgitatedActionConditional",
"CAgitatedActionConfront",
"CAgitatedActionEnterVehicle",
"CAgitatedActionExitVehicle",
"CAgitatedActionFace",
"CAgitatedActionFear",
"CAgitatedActionFight",
"CAgitatedActionFlee",
"CAgitatedActionFlipOff",
"CAgitatedActionFollow",
"CAgitatedActionHurryAway",
"CAgitatedActionIgnoreForcedAudioFailures",
"CAgitatedActionMakeAggressiveDriver",
"CAgitatedActionMulti",
"CAgitatedActionReportCrime",
"CAgitatedActionReset",
"CAgitatedActionSay",
"CAgitatedActionSayAgitator",
"CAgitatedActionSetFleeAmbientClips",
"CAgitatedActionSetFleeMoveBlendRatio",
"CAgitatedActionSetHash",
"CAgitatedActionStopVehicle",
"CAgitatedActionTurnOnSiren",
"CAgitatedCondition",
"CAgitatedConditionAnd",
"CAgitatedConditionCanCallPolice",
"CAgitatedConditionCanFight",
"CAgitatedConditionCanHurryAway",
"CAgitatedConditionCanStepOutOfVehicle",
"CAgitatedConditionCanWalkAway",
"CAgitatedConditionCheckBraveryFlags",
"CAgitatedConditionHasBeenHostileFor",
"CAgitatedConditionHasContext",
"CAgitatedConditionHasFriendsNearby",
"CAgitatedConditionHasLeader",
"CAgitatedConditionHasLeaderBeenFightingFor",
"CAgitatedConditionHasPavement",
"CAgitatedConditionHasVehicle",
"CAgitatedConditionIntruderLeft",
"CAgitatedConditionIsAGunPulled",
"CAgitatedConditionIsAgitatorArmed",
"CAgitatedConditionIsAgitatorEnteringVehicle",
"CAgitatedConditionIsAgitatorInOurTerritory",
"CAgitatedConditionIsAgitatorInVehicle",
"CAgitatedConditionIsAgitatorInjured",
"CAgitatedConditionIsAgitatorMovingAway",
"CAgitatedConditionIsAngry",
"CAgitatedConditionIsArgumentative",
"CAgitatedConditionIsAvoiding",
"CAgitatedConditionIsBecomingArmed",
"CAgitatedConditionIsBumped",
"CAgitatedConditionIsBumpedByVehicle",
"CAgitatedConditionIsBumpedInVehicle",
"CAgitatedConditionIsCallingPolice",
"CAgitatedConditionIsConfrontational",
"CAgitatedConditionIsConfronting",
"CAgitatedConditionIsContext",
"CAgitatedConditionIsDodged",
"CAgitatedConditionIsDodgedVehicle",
"CAgitatedConditionIsDrivingVehicle",
"CAgitatedConditionIsExitingScenario",
"CAgitatedConditionIsFacing",
"CAgitatedConditionIsFearful",
"CAgitatedConditionIsFighting",
"CAgitatedConditionIsFleeing",
"CAgitatedConditionIsFlippingOff",
"CAgitatedConditionIsFollowing",
"CAgitatedConditionIsFriendlyTalking",
"CAgitatedConditionIsGettingUp",
"CAgitatedConditionIsGriefing",
"CAgitatedConditionIsGunAimedAt",
"CAgitatedConditionIsHarassed",
"CAgitatedConditionIsHash",
"CAgitatedConditionIsHostile",
"CAgitatedConditionIsHurryingAway",
"CAgitatedConditionIsInVehicle",
"CAgitatedConditionIsInjured",
"CAgitatedConditionIsInsulted",
"CAgitatedConditionIsIntervene",
"CAgitatedConditionIsIntimidate",
"CAgitatedConditionIsLastAgitationApplied",
"CAgitatedConditionIsLawEnforcement",
"CAgitatedConditionIsLeaderAgitated",
"CAgitatedConditionIsLeaderFighting",
"CAgitatedConditionIsLeaderInState",
"CAgitatedConditionIsLeaderStill",
"CAgitatedConditionIsLeaderTalking",
"CAgitatedConditionIsLeaderUsingResponse",
"CAgitatedConditionIsLoitering",
"CAgitatedConditionIsMale",
"CAgitatedConditionIsOutsideClosestDistance",
"CAgitatedConditionIsOutsideDistance",
"CAgitatedConditionIsPlayingAmbientsInScenario",
"CAgitatedConditionIsProvoked",
"CAgitatedConditionIsRanting",
"CAgitatedConditionIsReadyForScenarioResponse",
"CAgitatedConditionIsSirenOn",
"CAgitatedConditionIsStanding",
"CAgitatedConditionIsSwimming",
"CAgitatedConditionIsTalking",
"CAgitatedConditionIsTargetDoingAMeleeMove",
"CAgitatedConditionIsTerritoryIntruded",
"CAgitatedConditionIsUsingRagdoll",
"CAgitatedConditionIsUsingScenario",
"CAgitatedConditionIsUsingTerritoryScenario",
"CAgitatedConditionIsWandering",
"CAgitatedConditionMulti",
"CAgitatedConditionNot",
"CAgitatedConditionOr",
"CAgitatedConditionRandom",
"CAgitatedConditionTimeout",
"CAgitatedConditionWasLeaderHit",
"CAgitatedConditionWasRecentlyBumpedWhenStill",
"CAgitatedConditionWasUsingTerritorialScenario",
"CAgitatedManager",
"CAgitatedScenarioExit",
"CAgitatedScenarioFastCowardExit",
"CAgitatedScenarioFleeExit",
"CAgitatedScenarioNormalCowardExit",
"CAgitatedSetWaterSurvivalTime",
"CAircraftDamage",
"CAircraftDamageBase",
"CAlterWantedLevelEvent",
"CAmbientAudioManager",
"CAmbientModelSet",
"CAmbientModelSetFilter",
"CAmbientModelSetFilterForScenario",
"CAmbientModelVariations",
"CAmbulanceDepartmentDispatch",
"CAmbulanceOrder",
"CAmmoInfo",
"CAmmoProjectileInfo",
"CAmmoRocketInfo",
"CAmmoThrownInfo",
"CAnimRequestArrayHandler",
"CAnimScene",
"CAnimSceneBoolean",
"CAnimSceneCamera",
"CAnimSceneClip",
"CAnimSceneClipSet",
"CAnimSceneCreateObjectEvent",
"CAnimSceneCreatePedEvent",
"CAnimSceneCreateVehicleEvent",
"CAnimSceneDictionary",
"CAnimSceneEntity",
"CAnimSceneEntityHandle",
"CAnimSceneEvent",
"CAnimSceneEventList",
"CAnimSceneForceMotionStateEvent",
"CAnimSceneHelperBase",
"CAnimSceneInternalLoopEvent",
"CAnimSceneLeadInData",
"CAnimSceneManager",
"CAnimSceneMatrix",
"CAnimSceneObject",
"CAnimScenePed",
"CAnimScenePlayAnimEvent",
"CAnimScenePlayCameraAnimEvent",
"CAnimScenePlaySceneEvent",
"CAnimScenePlayVfxEvent",
"CAnimScenePlaybackList",
"CAnimSceneSection",
"CAnimSceneVehicle",
"CAnimatedBuilding",
"CApp",
"CAppDataRequest",
"CApplyDamage",
"CArea",
"CArmIkSolver",
"CArmyVehicleDispatch",
"CArrestIncident",
"CArrowData",
"CAudioAttr",
"CAudioBankRequestEvent",
"CAudioCollisionInfo",
"CAudioRequest",
"CAudioSettings",
"CAutogyro",
"CAutomobile",
"CAutomobileCreationDataNode",
"CAutomobileSyncTree",
"CAutomobileSyncTreeBase",
"CBUTaskBase",
"CBaseArchetypeDef",
"CBaseCapsuleInfo",
"CBaseElementLocation",
"CBaseElementLocationsMap",
"CBaseElements",
"CBaseIkManager",
"CBaseModelInfo",
"CBaseStatsSaveStructure",
"CBaseSubHandlingData",
"CBasicEntityInteriorPrototype",
"CBasicEntityPrototype",
"CBasicEntityReflectionPrototype",
"CBike",
"CBikeGameStateDataNode",
"CBikeHandlingData",
"CBikeLeanAngleHelper",
"CBikeSyncTree",
"CBipedCapsuleInfo",
"CBirdCapsuleInfo",
"CBlimp",
"CBlockingOfNonTemporaryEventsParameters",
"CBlowUpVehicleEvent",
"CBmx",
"CBoat",
"CBoatChaseDirector",
"CBoatGameStateDataNode",
"CBoatHandlingData",
"CBoatSyncTree",
"CBodyLookIkSolver",
"CBodyLookIkSolverProxy",
"CBodyRecoilIkSolver",
"CBountyPresenceEvent",
"CBrawlingStyleMetaDataFileMounter",
"CBrokenAndHiddenComponentFlagsExtension",
"CBufferInfo",
"CBuilding",
"CBuoyancyAttr",
"CCSMEntityTracker",
"CCachePlayerHeadBlendDataEvent",
"CCamInterp",
"CCarGen",
"CCarHandlingData",
"CCarHornEvent",
"CChangeRadioStationEvent",
"CClanPlayerCardDataManager",
"CClearAreaEvent",
"CClearAreaRequest",
"CClearPedTasksEvent",
"CClip",
"CClipDictionaryStoreInterface",
"CClipPoseHelper",
"CClonedAchieveHeadingInfo",
"CClonedAimAndThrowProjectileInfo",
"CClonedAimGunBlindFireInfo",
"CClonedAimGunOnFootInfo",
"CClonedAimGunScriptedInfo",
"CClonedAimGunVehicleDriveByInfo",
"CClonedAmbulancePatrolInfo",
"CClonedAnimatedAttachInfo",
"CClonedBombInfo",
"CClonedClearLookAtInfo",
"CClonedClimbLadderInfo",
"CClonedCombatClosestTargetInAreaInfo",
"CClonedCombatTaskInfo",
"CClonedComplexEvasiveStepInfo",
"CClonedControlTaskEscapeBlastInfo",
"CClonedControlTaskFlyAwayInfo",
"CClonedControlTaskScenarioFleeInfo",
"CClonedControlTaskSmartFleeInfo",
"CClonedControlTaskVehicleMountedWeaponInfo",
"CClonedControlVehicleInfo",
"CClonedCoverInfo",
"CClonedCoverIntroInfo",
"CClonedCowerInfo",
"CClonedCrawlInfo",
"CClonedCrouchInfo",
"CClonedDamageElectricInfo",
"CClonedDiveToGroundInfo",
"CClonedDoNothingInfo",
"CClonedDriveCarWanderInfo",
"CClonedDyingDeadInfo",
"CClonedFallAndGetUpInfo",
"CClonedFallInfo",
"CClonedFallOverInfo",
"CClonedForceMotionStateInfo",
"CClonedGeneralSweepInfo",
"CClonedGetUpInfo",
"CClonedGoToPointAimingInfo",
"CClonedGoToPointAndStandStillTimedInfo",
"CClonedGoToPointAnyMeansInfo",
"CClonedGunInfo",
"CClonedHandsUpInfo",
"CClonedHeliPassengerRappelInfo",
"CClonedLeaveAnyCarInfo",
"CClonedLookAtInfo",
"CClonedMotionInCoverInfo",
"CClonedMountThrowProjectileInfo",
"CClonedMoveFollowNavMeshInfo",
"CClonedNMBalanceInfo",
"CClonedNMBraceInfo",
"CClonedNMBuoyancyInfo",
"CClonedNMControlInfo",
"CClonedNMElectrocuteInfo",
"CClonedNMExposionInfo",
"CClonedNMFallDownInfo",
"CClonedNMFlinchInfo",
"CClonedNMHighFallInfo",
"CClonedNMInjuredOnGroundInfo",
"CClonedNMJumpRollInfo",
"CClonedNMOnFireInfo",
"CClonedNMPoseInfo",
"CClonedNMPrototypeInfo",
"CClonedNMRelaxInfo",
"CClonedNMRiverRapidsInfo",
"CClonedNMShotInfo",
"CClonedNMSimpleInfo",
"CClonedNMSitInfo",
"CClonedNMThroughWindscreenInfo",
"CClonedParachuteInfo",
"CClonedParachuteObjectInfo",
"CClonedPauseInfo",
"CClonedPlaneChaseInfo",
"CClonedRageRagdollInfo",
"CClonedRappelDownWallInfo",
"CClonedReactAimWeaponInfo",
"CClonedReactAndFleeInfo",
"CClonedReactToExplosionInfo",
"CClonedReviveInfo",
"CClonedScriptClipInfo",
"CClonedScriptedAnimationInfo",
"CClonedSeatShuffleInfo",
"CClonedSeekEntityAimingInfo",
"CClonedSeekEntityInfoBase",
"CClonedSeekEntityLastNavMeshIntersectionInfo",
"CClonedSeekEntityOffsetFixedInfo",
"CClonedSeekEntityOffsetRotateInfo",
"CClonedSeekEntityRadiusAngleInfo",
"CClonedSeekEntityStandardInfo",
"CClonedSetAndGuardAreaInfo",
"CClonedSetBlockingOfNonTemporaryEventsInfo",
"CClonedSetPedDefensiveAreaInfo",
"CClonedSlideToCoordInfo",
"CClonedSlopeScrambleInfo",
"CClonedStandGuardInfo",
"CClonedStayInCoverInfo",
"CClonedSwapWeaponInfo",
"CClonedSynchronisedSceneInfo",
"CClonedTaskAmbientClipsInfo",
"CClonedTaskArrestPed2Info",
"CClonedTaskDropDownInfo",
"CClonedTaskJumpInfo",
"CClonedTaskMeleeInfo",
"CClonedTaskMeleeResultInfo",
"CClonedTaskMoVEScriptedInfo",
"CClonedTaskReloadGunInfo",
"CClonedTaskSequenceInfo",
"CClonedTaskSharkAttackInfo",
"CClonedTaskUnalertedInfo",
"CClonedTaskVaultInfo",
"CClonedThreatResponseInfo",
"CClonedThrowProjectileInfo",
"CClonedTurnToFaceEntityOrCoordInfo",
"CClonedUseCoverInfo",
"CClonedVehicleGunInfo",
"CClonedVehicleProjectileInfo",
"CClonedWanderInfo",
"CClonedWeaponBlockedInfo",
"CClonedWritheInfo",
"CClosestPositionRequest",
"CCombatDirector",
"CCombatInfoDataFileMounter",
"CCombatSituation",
"CCombatSituationEscalate",
"CCombatSituationFallBack",
"CCombatSituationLull",
"CCombatSituationNormal",
"CCompEntity",
"CCompEntityModelInfo",
"CCompositeEntityAnimation",
"CCompositeEntityArchetypeDef",
"CCompositeEntityEffectsSetting",
"CCompressFileForSendingJob",
"CCompressedPedDamageSet",
"CConditionalAnimManager",
"CContextMenu",
"CControl",
"CConvertToScriptEntityEvent",
"CConvertibleRoofWindowInfo",
"CCoronaCrewsInviteMenu",
"CCoronaFriendsInviteMenu",
"CCoronaJoinedPlayersMenu",
"CCoronaLastJobInviteMenu",
"CCoronaMatchedPlayersInviteMenu",
"CCoronaPlayersInviteMenu",
"CCoverFinder",
"CCoverFinderFSM",
"CCoverPointFilterBase",
"CCoverPointFilterDefensiveArea",
"CCoverPointFilterTaskCombat",
"CCoverPointStatusHelper",
"CCreatureMetaData",
"CCrewDetailMenu",
"CCrewMenu",
"CCrimeInfoManager",
"CCrimeInformationManager",
"CCullVolumeBoxDesc",
"CCullVolumeCapsuleDesc",
"CCullVolumeDesc",
"CCurve",
"CCurveSet",
"CCustomShaderEffectAnimUV",
"CCustomShaderEffectAnimUVType",
"CCustomShaderEffectCable",
"CCustomShaderEffectCableType",
"CCustomShaderEffectGrass",
"CCustomShaderEffectGrassType",
"CCustomShaderEffectInterior",
"CCustomShaderEffectInteriorType",
"CCustomShaderEffectMirror",
"CCustomShaderEffectMirrorType",
"CCustomShaderEffectPed",
"CCustomShaderEffectPedType",
"CCustomShaderEffectProp",
"CCustomShaderEffectPropType",
"CCustomShaderEffectTint",
"CCustomShaderEffectTintType",
"CCustomShaderEffectTree",
"CCustomShaderEffectTreeType",
"CCustomShaderEffectVehicle",
"CCustomShaderEffectVehicleType",
"CCustomShaderEffectWeapon",
"CCustomShaderEffectWeaponType",
"CCutSceneAnimMgrEntity",
"CCutSceneAnimatedParticleEffect",
"CCutSceneAnimatedPropEntity",
"CCutSceneAnimatedWeaponEntity",
"CCutSceneAssetMgrEntity",
"CCutSceneAudioEntity",
"CCutSceneBinkOverlayEntity",
"CCutSceneBlockingBoundsEntity",
"CCutSceneCameraEntity",
"CCutSceneDecalEntity",
"CCutSceneFadeEntity",
"CCutSceneFixupBoundsEntity",
"CCutSceneHiddenBoundsEntity",
"CCutSceneLightEntity",
"CCutSceneParticleEffect",
"CCutSceneParticleEffectsEntity",
"CCutSceneRayFireEntity",
"CCutSceneScaleformOverlayEntity",
"CCutSceneSingleRequestStreamingInfo",
"CCutSceneStreamingInfo",
"CCutSceneSubtitleEntity",
"CCutSceneTriggeredParticleEffect",
"CCutsceneAnimatedActorEntity",
"CCutsceneAnimatedModelEntity",
"CCutsceneAnimatedVehicleEntity",
"CCutsceneMenu",
"CCutsceneModelRequestStreamingInfo",
"CCutsceneStore",
"CCutsceneVariationStreamingInfo",
"CDLCItypFileMounter",
"CDLCScriptDataMounter",
"CDataFileMountInterface",
"CDecalAttr",
"CDecalCallbacks",
"CDecodeTextureFromBlobWorkJob",
"CDecoratorInterface",
"CDefensiveArea",
"CDispatchAdvancedSpawnHelper",
"CDispatchBasicSpawnHelper",
"CDispatchService",
"CDispatchSpawnHelper",
"CDisplayTextBaseClass",
"CDisplayTextFourSubstringsThreeNumbers",
"CDisplayTextOneSubstring",
"CDisplayTextZeroOrOneNumbers",
"CDistantLODLight",
"CDoor",
"CDoorBreakEvent",
"CDoorCreationDataNode",
"CDoorExtension",
"CDoorMovementDataNode",
"CDoorScanner",
"CDoorScriptGameStateDataNode",
"CDoorScriptInfoDataNode",
"CDoorSyncData",
"CDoorSyncTree",
"CDoorSystemData",
"CDownloadableTextureManager",
"CDrawCommandBuffer",
"CDrawListMgr",
"CDummyObject",
"CDynamicCoverHelper",
"CDynamicEntity",
"CDynamicEntityFragmentDrawHandler",
"CDynamicEntityGameStateDataNode",
"CDynamicEntitySkinnedDrawHandler",
"CDynamicEntitySyncTreeBase",
"CEntity",
"CEntityAreaStatusEvent",
"CEntityBasicDrawHandler",
"CEntityBatch",
"CEntityBatchBase",
"CEntityBatchDrawHandler",
"CEntityBendableDrawHandler",
"CEntityDef",
"CEntityDrawDataPedProps",
"CEntityDrawDataPedPropsBase",
"CEntityDrawHandler",
"CEntityFragDrawHandler",
"CEntityInstancedBasicDrawHandler",
"CEntityInstancedBendableDrawHandler",
"CEntityInstancedFragDrawHandler",
"CEntityOrientationDataNode",
"CEntityScanner",
"CEntityScriptGameStateDataNode",
"CEntityScriptInfoDataNode",
"CEntitySeekPosCalculator",
"CEntitySeekPosCalculatorLastNavMeshIntersection",
"CEntitySeekPosCalculatorRadiusAngleOffset",
"CEntitySeekPosCalculatorStandard",
"CEntitySeekPosCalculatorXYOffsetFixed",
"CEntitySeekPosCalculatorXYOffsetRotated",
"CEntitySyncTreeBase",
"CEvent",
"CEventAcquaintancePed",
"CEventAcquaintancePedDead",
"CEventAcquaintancePedDislike",
"CEventAcquaintancePedHate",
"CEventAcquaintancePedLike",
"CEventAcquaintancePedWanted",
"CEventAgitated",
"CEventAgitatedAction",
"CEventCallForCover",
"CEventCarUndriveable",
"CEventClimbLadderOnRoute",
"CEventClimbNavMeshOnRoute",
"CEventCombatTaunt",
"CEventCommunicateEvent",
"CEventCopCarBeingStolen",
"CEventCrimeCryForHelp",
"CEventCrimeReported",
"CEventDamage",
"CEventDataDecisionMaker",
"CEventDataFileMounter",
"CEventDataResponseAggressiveRubberneck",
"CEventDataResponseDeferToScenarioPointFlags",
"CEventDataResponseFriendlyAimedAt",
"CEventDataResponseFriendlyNearMiss",
"CEventDataResponsePlayerDeath",
"CEventDataResponsePoliceTaskWanted",
"CEventDataResponseSwatTaskWanted",
"CEventDataResponseTask",
"CEventDataResponseTaskAgitated",
"CEventDataResponseTaskCombat",
"CEventDataResponseTaskCower",
"CEventDataResponseTaskCrouch",
"CEventDataResponseTaskDuckAndCover",
"CEventDataResponseTaskEscapeBlast",
"CEventDataResponseTaskEvasiveStep",
"CEventDataResponseTaskExhaustedFlee",
"CEventDataResponseTaskExplosion",
"CEventDataResponseTaskFlee",
"CEventDataResponseTaskFlyAway",
"CEventDataResponseTaskGrowlAndFlee",
"CEventDataResponseTaskGunAimedAt",
"CEventDataResponseTaskHandsUp",
"CEventDataResponseTaskHeadTrack",
"CEventDataResponseTaskLeaveCarAndFlee",
"CEventDataResponseTaskScenarioFlee",
"CEventDataResponseTaskSharkAttack",
"CEventDataResponseTaskShockingEventBackAway",
"CEventDataResponseTaskShockingEventGoto",
"CEventDataResponseTaskShockingEventHurryAway",
"CEventDataResponseTaskShockingEventReact",
"CEventDataResponseTaskShockingEventReactToAircraft",
"CEventDataResponseTaskShockingEventStopAndStare",
"CEventDataResponseTaskShockingEventThreatResponse",
"CEventDataResponseTaskShockingEventWatch",
"CEventDataResponseTaskShockingNiceCar",
"CEventDataResponseTaskShockingPoliceInvestigate",
"CEventDataResponseTaskThreat",
"CEventDataResponseTaskTurnToFace",
"CEventDataResponseTaskWalkAway",
"CEventDataResponseTaskWalkRoundEntity",
"CEventDataResponseTaskWalkRoundFire",
"CEventDeadPedFound",
"CEventDeath",
"CEventDecisionMakerResponse",
"CEventDisturbance",
"CEventDraggedOutCar",
"CEventEditableResponse",
"CEventEncroachingPed",
"CEventEntityDamaged",
"CEventEntityDestroyed",
"CEventExplosion",
"CEventExplosionHeard",
"CEventFireNearby",
"CEventFootStepHeard",
"CEventFriendlyAimedAt",
"CEventFriendlyFireNearMiss",
"CEventGetOutOfWater",
"CEventGivePedTask",
"CEventGroupScriptAI",
"CEventGroupScriptNetwork",
"CEventGunAimedAt",
"CEventGunShot",
"CEventGunShotBulletImpact",
"CEventGunShotWhizzedBy",
"CEventHelpAmbientFriend",
"CEventHurtTransition",
"CEventInAir",
"CEventInfo",
"CEventInfoBase",
"CEventInjuredCryForHelp",
"CEventLeaderEnteredCarAsDriver",
"CEventLeaderExitedCarAsDriver",
"CEventLeaderHolsteredWeapon",
"CEventLeaderLeftCover",
"CEventLeaderUnholsteredWeapon",
"CEventMeleeAction",
"CEventMustLeaveBoat",
"CEventNetworkAdminInvited",
"CEventNetworkAttemptHostMigration",
"CEventNetworkBail",
"CEventNetworkCashTransactionLog",
"CEventNetworkCheatTriggered",
"CEventNetworkClanInviteReceived",
"CEventNetworkClanJoined",
"CEventNetworkClanKicked",
"CEventNetworkClanLeft",
"CEventNetworkClanRankChanged",
"CEventNetworkCloudEvent",
"CEventNetworkCloudFileResponse",
"CEventNetworkEmailReceivedEvent",
"CEventNetworkEndMatch",
"CEventNetworkEndSession",
"CEventNetworkEntityDamage",
"CEventNetworkFindSession",
"CEventNetworkFollowInviteReceived",
"CEventNetworkHostMigration",
"CEventNetworkHostSession",
"CEventNetworkIncrementStat",
"CEventNetworkInviteAccepted",
"CEventNetworkInviteConfirmed",
"CEventNetworkInviteRejected",
"CEventNetworkJoinSession",
"CEventNetworkJoinSessionResponse",
"CEventNetworkOnlinePermissionsUpdated",
"CEventNetworkPedLeftBehind",
"CEventNetworkPickupRespawned",
"CEventNetworkPlayerArrest",
"CEventNetworkPlayerCollectedAmbientPickup",
"CEventNetworkPlayerCollectedPickup",
"CEventNetworkPlayerCollectedPortablePickup",
"CEventNetworkPlayerDroppedPortablePickup",
"CEventNetworkPlayerJoinScript",
"CEventNetworkPlayerLeftScript",
"CEventNetworkPlayerScript",
"CEventNetworkPlayerSession",
"CEventNetworkPlayerSpawn",
"CEventNetworkPresenceInvite",
"CEventNetworkPresenceInviteRemoved",
"CEventNetworkPresenceInviteReply",
"CEventNetworkPresenceTriggerEvent",
"CEventNetworkPresence_StatUpdate",
"CEventNetworkPrimaryClanChanged",
"CEventNetworkRequestDelay",
"CEventNetworkRosChanged",
"CEventNetworkScAdminPlayerUpdated",
"CEventNetworkScAdminReceivedCash",
"CEventNetworkScriptEvent",
"CEventNetworkSessionEvent",
"CEventNetworkShopTransaction",
"CEventNetworkSignInStateChanged",
"CEventNetworkSocialClubAccountLinked",
"CEventNetworkSpectateLocal",
"CEventNetworkStartMatch",
"CEventNetworkStartSession",
"CEventNetworkStorePlayerLeft",
"CEventNetworkSummon",
"CEventNetworkSystemServiceEvent",
"CEventNetworkTextMessageReceived",
"CEventNetworkTimedExplosion",
"CEventNetworkTransitionEvent",
"CEventNetworkTransitionGamerInstruction",
"CEventNetworkTransitionMemberJoined",
"CEventNetworkTransitionMemberLeft",
"CEventNetworkTransitionParameterChanged",
"CEventNetworkTransitionStarted",
"CEventNetworkTransitionStringChanged",
"CEventNetworkVehicleUndrivable",
"CEventNetworkVoiceConnectionRequested",
"CEventNetworkVoiceConnectionResponse",
"CEventNetworkVoiceConnectionTerminated",
"CEventNetworkVoiceSessionEnded",
"CEventNetworkVoiceSessionStarted",
"CEventNetworkWithData",
"CEventNetwork_InboxMsgReceived",
"CEventNewTask",
"CEventObjectCollision",
"CEventOnFire",
"CEventOpenDoor",
"CEventPedCollisionWithPed",
"CEventPedCollisionWithPlayer",
"CEventPedEnteredMyVehicle",
"CEventPedJackingMyVehicle",
"CEventPedOnCarRoof",
"CEventPedSeenDeadPed",
"CEventPlayerCollisionWithPed",
"CEventPlayerDeath",
"CEventPlayerUnableToEnterVehicle",
"CEventPotentialBeWalkedInto",
"CEventPotentialBlast",
"CEventPotentialGetRunOver",
"CEventPotentialWalkIntoVehicle",
"CEventProvidingCover",
"CEventRanOverPed",
"CEventReactionEnemyPed",
"CEventReactionInvestigateDeadPed",
"CEventReactionInvestigateThreat",
"CEventRequestHelp",
"CEventRequestHelpWithConfrontation",
"CEventRespondedToThreat",
"CEventScanner",
"CEventScenarioForceAction",
"CEventScriptCommand",
"CEventScriptWithData",
"CEventShocking",
"CEventShockingBicycleCrash",
"CEventShockingBicycleOnPavement",
"CEventShockingCarAlarm",
"CEventShockingCarChase",
"CEventShockingCarCrash",
"CEventShockingCarOnCar",
"CEventShockingCarPileUp",
"CEventShockingDangerousAnimal",
"CEventShockingDeadBody",
"CEventShockingDrivingOnPavement",
"CEventShockingEngineRevved",
"CEventShockingExplosion",
"CEventShockingFire",
"CEventShockingGunFight",
"CEventShockingGunshotFired",
"CEventShockingHelicopterOverhead",
"CEventShockingHornSounded",
"CEventShockingInDangerousVehicle",
"CEventShockingInjuredPed",
"CEventShockingMadDriver",
"CEventShockingMadDriverBicycle",
"CEventShockingMadDriverExtreme",
"CEventShockingMugging",
"CEventShockingNonViolentWeaponAimedAt",
"CEventShockingParachuterOverhead",
"CEventShockingPedKnockedIntoByPlayer",
"CEventShockingPedRunOver",
"CEventShockingPedShot",
"CEventShockingPlaneFlyby",
"CEventShockingPotentialBlast",
"CEventShockingPropertyDamage",
"CEventShockingRunningPed",
"CEventShockingRunningStampede",
"CEventShockingSeenCarStolen",
"CEventShockingSeenConfrontation",
"CEventShockingSeenGangFight",
"CEventShockingSeenInsult",
"CEventShockingSeenMeleeAction",
"CEventShockingSeenNiceCar",
"CEventShockingSeenPedKilled",
"CEventShockingSiren",
"CEventShockingStudioBomb",
"CEventShockingVehicleTowed",
"CEventShockingVisibleWeapon",
"CEventShockingWeaponThreat",
"CEventShockingWeirdPed",
"CEventShockingWeirdPedApproaching",
"CEventShoutBlockingLos",
"CEventShoutTargetPosition",
"CEventShovePed",
"CEventSoundBase",
"CEventStatChangedValue",
"CEventStaticCountReachedMax",
"CEventStuckInAir",
"CEventSuspiciousActivity",
"CEventSwitch2NM",
"CEventUnidentifiedPed",
"CEventVehicleCollision",
"CEventVehicleDamage",
"CEventVehicleDamageWeapon",
"CEventVehicleOnFire",
"CEventWrithe",
"CExplosionAttr",
"CExplosionEvent",
"CExplosionFileMounter",
"CExpressionExtension",
"CExtensionDef",
"CExtensionDefAudioCollisionSettings",
"CExtensionDefAudioEmitter",
"CExtensionDefBuoyancy",
"CExtensionDefClimbHandHold",
"CExtensionDefDecal",
"CExtensionDefDoor",
"CExtensionDefExplosionEffect",
"CExtensionDefExpression",
"CExtensionDefLadder",
"CExtensionDefLight",
"CExtensionDefLightEffect",
"CExtensionDefLightShaft",
"CExtensionDefParticleEffect",
"CExtensionDefProcObject",
"CExtensionDefScript",
"CExtensionDefScriptChild",
"CExtensionDefScrollbars",
"CExtensionDefSpawnPoint",
"CExtensionDefSpawnPointOverride",
"CExtensionDefSwayableEffect",
"CExtensionDefWalkDontWalk",
"CExtensionDefWindDisturbance",
"CExtraContentFileMounter",
"CExtraContentManager",
"CExtraMetaDataFileMounter",
"CFacebook",
"CFileDataProvider",
"CFileViewFilter",
"CFindNearestCarNodeHelper",
"CFingerOfGodPresenceEvent",
"CFireDepartmentDispatch",
"CFireEvent",
"CFireIncident",
"CFireOrder",
"CFiringPatternInfo",
"CFishCapsuleInfo",
"CFixedVehicleWeapon",
"CFleeInVehicleTransitions",
"CFleeOnFootTransitions",
"CFleeSkiingTransitions",
"CFloodFillRequest",
"CFlyingHandlingData",
"CFollowInvite",
"CForceSessionUpdatePresenceEvent",
"CFriendClanData",
"CFriendCrewCreatedPresenceEvent",
"CFriendCrewJoinedPresenceEvent",
"CFriendListMenuDataPaginator",
"CFriendPlayerCardDataManager",
"CFriendsMenuMP",
"CFriendsMenuSP",
"CGTABoxStreamerInterfaceNew",
"CGTAStaticBoundsStoreInterface",
"CGadgetParachute",
"CGadgetSkis",
"CGalleryMenu",
"CGameArrayMgr",
"CGameAwardPresenceEvent",
"CGameClockEvent",
"CGameConfig",
"CGameHostBroadcastDataHandler",
"CGameInvite",
"CGameInviteCancel",
"CGameInviteReply",
"CGamePlayerBroadcastDataHandler_Local",
"CGamePlayerBroadcastDataHandler_Remote",
"CGameScriptHandler",
"CGameScriptHandlerMgr",
"CGameScriptHandlerNetComponent",
"CGameScriptHandlerNetwork",
"CGameScriptId",
"CGameScriptObjInfo",
"CGameTempAllocator",
"CGameTriggerEvent",
"CGameWeatherEvent",
"CGangDispatch",
"CGangOrder",
"CGarageOccupiedStatusEvent",
"CGestureManager",
"CGetupProbeHelper",
"CGiveControlEvent",
"CGivePedScriptedTaskEvent",
"CGivePedSequenceTaskEvent",
"CGivePickupRewardsEvent",
"CGiveWeaponEvent",
"CGlobalFlagsDataNode",
"CGradient",
"CGraphicsSettings",
"CGrassBatch",
"CGrassBatchDrawHandler",
"CGridRequest",
"CGroup",
"CGroupClanData",
"CGtaAnimManager",
"CGtaGameInterface",
"CGtaRenderThreadGameInterface",
"CGtaSceneInterface",
"CGtaStreamingInterface",
"CHandlingData",
"CHandlingObject",
"CHeli",
"CHeliControlDataNode",
"CHeliHealthDataNode",
"CHeliIntelligence",
"CHeliSyncTree",
"CHudTunablesListener",
"CIkManager",
"CIkSolverProxy",
"CImposedImageHandler",
"CIncident",
"CIncidentEntityEvent",
"CIncidentsArrayHandler",
"CInfoMenu",
"CInformFriendsEvent",
"CInformGroupEvent",
"CInformSilencedGunShotEvent",
"CInjuryIncident",
"CInteriorAudioSettings",
"CInteriorInst",
"CInteriorProxy",
"CInteriorProxyFileMounter",
"CInterpEventInfo",
"CInterpolator",
"CInventoryListener",
"CIplCullboxFileMounter",
"CIsVisibleExtension",
"CItemInfo",
"CItemSet",
"CJoinQueueRequest",
"CJoinQueueUpdate",
"CKeyMappingMenu",
"CLODLight",
"CLadderInfo",
"CLadderMetadata",
"CLandingGearDamage",
"CLandingGearPartBase",
"CLandingGearPartPhysical",
"CLandingGearPartPhysicalRot",
"CLayoutNode",
"CLegIkSolver",
"CLegIkSolverProxy",
"CLevelData",
"CLightAttr",
"CLightAttrDef",
"CLightEntity",
"CLightExtension",
"CLightShaftAttr",
"CLineOfSightRequest",
"CLoadOutItem",
"CLoadOutRandom",
"CLoadOutWeapon",
"CLobbyMenu",
"CMPApparelData",
"CMPFriendPlayerCardDataManager",
"CMPOutfits",
"CMPOutfitsData",
"CMPOutfitsMap",
"CMPPlayerListMenu",
"CMapData",
"CMapDataContents",
"CMapMenu",
"CMapTypes",
"CMapTypesContents",
"CMarkAsNoLongerNeededEvent",
"CMenuBase",
"CMigrationDataNode",
"CMiloInterior",
"CMiloRoom",
"CMipSwitcher",
"CMissionVerifiedPresenceEvent",
"CMloArchetypeDef",
"CMloEntitySet",
"CMloInstanceDef",
"CMloModelInfo",
"CMloPortalDef",
"CMloRoomDef",
"CMloTimeCycleModifier",
"CModelInfoStreamingModule",
"CModifyVehicleLockWorldStateDataEvent",
"CMontageElementHandleBase",
"CMoveAnimatedBuilding",
"CMoveAnimatedBuildingPooledObject",
"CMoveObject",
"CMoveObjectPooledObject",
"CMovePed",
"CMovePedPooledObject",
"CMoveVehicle",
"CMoveVehiclePooledObject",
"CMultiTxdRelationship",
"CMultiplayerStatsSaveStructure",
"CMusicClipHandle",
"CNavMesh",
"CNetBlenderBoat",
"CNetBlenderHeli",
"CNetBlenderPed",
"CNetBlenderPhysical",
"CNetBlenderTrain",
"CNetBlenderVehicle",
"CNetGamePlayer",
"CNetGamePlayerDataMsg",
"CNetShopTransaction",
"CNetShopTransactionBase",
"CNetShopTransactionBasket",
"CNetworkBandwidthManager",
"CNetworkCarGenWorldStateData",
"CNetworkCheckExeSizeEvent",
"CNetworkCrcHashCheckEvent",
"CNetworkCrewDataMgr",
"CNetworkEntityAreaWorldStateData",
"CNetworkIncrementStatEvent",
"CNetworkInfoChangeEvent",
"CNetworkLeaderboardMgr",
"CNetworkLogFileAccess",
"CNetworkNewsStoryMgr",
"CNetworkObjectMgr",
"CNetworkPedSeenDeadPedEvent",
"CNetworkPlayerMgr",
"CNetworkPopGroupOverrideWorldStateData",
"CNetworkPopMultiplierAreaWorldStateData",
"CNetworkPtFXEvent",
"CNetworkPtFXWorldStateData",
"CNetworkReadLeaderboards",
"CNetworkRespondedToThreatEvent",
"CNetworkRoadNodeWorldStateData",
"CNetworkRopeWorldStateData",
"CNetworkSCNewsStoryRequest",
"CNetworkScenarioBlockingAreaWorldStateData",
"CNetworkShoutTargetPositionEvent",
"CNetworkSpecialFireEquippedWeaponEvent",
"CNetworkSyncDataUL",
"CNetworkSyncDataULBase",
"CNetworkTrainReportEvent",
"CNetworkTrainRequestEvent",
"CNetworkTunablesListener",
"CNetworkVehiclePlayerLockingWorldState",
"CNetworkWorldStateData",
"CNetworkWriteLeaderboards",
"CNewsItemPresenceEvent",
"CNmBlendOutBlendItem",
"CNmBlendOutItem",
"CNmBlendOutMotionStateItem",
"CNmBlendOutPoseItem",
"CNmBlendOutReactionItem",
"CNmBlendOutSet",
"CNmBlendOutSetManager",
"CNmMessage",
"CNmParameter",
"CNmParameterBool",
"CNmParameterFloat",
"CNmParameterInt",
"CNmParameterRandomFloat",
"CNmParameterRandomInt",
"CNmParameterResetMessage",
"CNmParameterString",
"CNmParameterVector",
"CNmTuningSet",
"CNonPhysicalPlayerData",
"CObjInterp",
"CObject",
"CObjectCoverExtension",
"CObjectCreationDataNode",
"CObjectDrawHandler",
"CObjectFragmentDrawHandler",
"CObjectGameStateDataNode",
"CObjectIntelligence",
"CObjectScanner",
"CObjectScriptGameStateDataNode",
"CObjectSectorPosNode",
"CObjectSyncData",
"CObjectSyncTree",
"COnFootArmedCoverOnlyTransitions",
"COnFootArmedTransitions",
"COnFootUnarmedTransitions",
"COrder",
"COrdersArrayHandler",
"CParticleAttr",
"CPartyClanData",
"CPartyMenu",
"CPartyPlayerCardDataManager",
"CPathFind",
"CPathNodeRouteSearchHelper",
"CPathRegion",
"CPathRequest",
"CPathServerGameInterfaceGta",
"CPathServerRequestBase",
"CPathServerThread",
"CPatrolLink",
"CPatrolNode",
"CPauseStoreMenu",
"CPauseVideoEditorMenu",
"CPed",
"CPedAIDataNode",
"CPedAppearanceDataNode",
"CPedAttachDataNode",
"CPedBigPrototype",
"CPedBlenderData",
"CPedBlenderDataAnimatedRagdollFallback",
"CPedBlenderDataFirstPersonMode",
"CPedBlenderDataInWater",
"CPedBlenderDataOnFoot",
"CPedBlenderDataTennis",
"CPedBlenderDataUsingParachute",
"CPedClothCollision",
"CPedCompExpressionData",
"CPedComponentReservationDataNode",
"CPedConversationLineEvent",
"CPedCreationDataNode",
"CPedDamageDataMounter",
"CPedDamageDecalInfo",
"CPedDamagePackEntry",
"CPedDamageResponse",
"CPedDamageSet",
"CPedDamageSetBase",
"CPedDecorationsDataFileMounter",
"CPedDrawHandler",
"CPedFactory",
"CPedFormation",
"CPedFormation_Arrowhead",
"CPedFormation_FollowInLine",
"CPedFormation_LineAbreast",
"CPedFormation_Loose",
"CPedFormation_Pair",
"CPedFormation_Single",
"CPedFormation_SurroundFacingAhead",
"CPedFormation_SurroundFacingInwards",
"CPedFormation_V",
"CPedGameStateDataNode",
"CPedGenNavMeshIteratorAmbient",
"CPedGroupsArrayHandler",
"CPedHeadBlendData",
"CPedHealthDataNode",
"CPedIntelligenceFactory",
"CPedInterp",
"CPedInventory",
"CPedInventoryDataNode",
"CPedModelInfo",
"CPedModelMetaDataFileMounter",
"CPedMovementDataNode",
"CPedMovementGroupDataNode",
"CPedOrientationDataNode",
"CPedPlayPainEvent",
"CPedPropExpressionData",
"CPedScanner",
"CPedScriptCreationDataNode",
"CPedScriptGameStateDataNode",
"CPedSectorPosMapNode",
"CPedSectorPosNavMeshNode",
"CPedStreamRenderGfx",
"CPedStreamRequestGfx",
"CPedSyncData",
"CPedSyncTree",
"CPedSyncTreeBase",
"CPedTaskManager",
"CPedTaskSequenceDataNode",
"CPedTaskSpecificDataNode",
"CPedTaskTreeDataNode",
"CPedVariationStreamFileMounter",
"CPedWeaponManager",
"CPhysical",
"CPhysicalAngVelocityDataNode",
"CPhysicalAttachDataNode",
"CPhysicalGameStateDataNode",
"CPhysicalHealthDataNode",
"CPhysicalMigrationDataNode",
"CPhysicalScriptGameStateDataNode",
"CPhysicalScriptMigrationDataNode",
"CPhysicalSyncTreeBase",
"CPhysicalVelocityDataNode",
"CPickup",
"CPickupActionAudio",
"CPickupActionData",
"CPickupActionGroup",
"CPickupActionPadShake",
"CPickupActionVfx",
"CPickupCreationDataNode",
"CPickupData",
"CPickupDataManagerMounter",
"CPickupDestroyedEvent",
"CPickupInterp",
"CPickupPlacementCreationDataNode",
"CPickupPlacementStateDataNode",
"CPickupPlacementSyncData",
"CPickupPlacementSyncTree",
"CPickupRewardAmmo",
"CPickupRewardArmour",
"CPickupRewardBulletMP",
"CPickupRewardData",
"CPickupRewardFireworkMP",
"CPickupRewardGrenadeLauncherMP",
"CPickupRewardHealth",
"CPickupRewardHealthVariable",
"CPickupRewardMissileMP",
"CPickupRewardMoneyFixed",
"CPickupRewardMoneyVariable",
"CPickupRewardStat",
"CPickupRewardStatVariable",
"CPickupRewardVehicleFix",
"CPickupRewardWeapon",
"CPickupScriptGameStateNode",
"CPickupSectorPosNode",
"CPickupSyncData",
"CPickupSyncTree",
"CPlane",
"CPlaneControlDataNode",
"CPlaneGameStateDataNode",
"CPlaneIntelligence",
"CPlaneSyncTree",
"CPlaySoundEvent",
"CPlayerAmbientModelStreamingNode",
"CPlayerAppearanceDataNode",
"CPlayerCameraDataNode",
"CPlayerCardStatEvent",
"CPlayerCreationDataNode",
"CPlayerExtendedGameStateNode",
"CPlayerGameStateDataNode",
"CPlayerGamerDataNode",
"CPlayerInfo",
"CPlayerListMenu",
"CPlayerListMenuDataPaginator",
"CPlayerListMenuPage",
"CPlayerPedGroupDataNode",
"CPlayerPedTargeting",
"CPlayerSectorPosNode",
"CPlayerSwitchEstablishingShotMetadata",
"CPlayerSwitchEstablishingShotMetadataStore",
"CPlayerSwitchMgrBase",
"CPlayerSwitchMgrLong",
"CPlayerSwitchMgrShort",
"CPlayerSyncData",
"CPlayerSyncTree",
"CPlayerTauntEvent",
"CPlayerWantedAndLOSDataNode",
"CPlayersMenu",
"CPoliceAutomobileDispatch",
"CPoliceBoatDispatch",
"CPoliceHelicopterDispatch",
"CPoliceOrder",
"CPoliceRidersDispatch",
"CPoliceRoadBlockDispatch",
"CPoliceVehicleRequest",
"CPopulationDataFileMounter",
"CPortalInst",
"CPortalTracker",
"CPortalTrackerBase",
"CPortalVisTracker",
"CPosixTimeStampForMultiplayerSaves",
"CPrioritizedClipSetRequest",
"CProcObjAttr",
"CProfileStatsRecords",
"CProjectBaseSyncParentNode",
"CProjectSyncTree",
"CProjectile",
"CProjectileRocket",
"CProjectileThrown",
"CPropManagementHelper",
"CPropeller",
"CPropellerBlurred",
"CProximityMigrateableSyncTreeBase",
"CPtFxCallbacks",
"CPtFxColnBase",
"CPtFxColnBound",
"CPtFxColnPlane",
"CPtFxDrawHandler",
"CPtFxGPUManager",
"CPtFxGPURenderSetup",
"CPtFxSortedEntity",
"CPtfxShadowDrawToAllCascades",
"CQuadBike",
"CQuadrupedCapsuleInfo",
"CRTStructureDataToBeSaved",
"CRTStructureDataToBeSaved_MultiplayerCharacter",
"CRTStructureDataToBeSaved_MultiplayerCommon",
"CRTStructureDataToBeSaved_SinglePlayer",
"CRadioWheel",
"CRagdollRequestEvent",
"CRawClipFileDataProvider",
"CRawClipFileView",
"CRelationshipGroup",
"CRemoteScriptInfoEvent",
"CRemoteScriptLeaveEvent",
"CRemoveAllWeaponsEvent",
"CRemovePedFromPedGroupEvent",
"CRemoveStickyBombEvent",
"CRemoveWeaponEvent",
"CRenderPhase",
"CRenderPhaseCascadeShadows",
"CRenderPhaseCloudGeneration",
"CRenderPhaseDeferredLighting_LightsToScreen",
"CRenderPhaseDeferredLighting_SceneToGBuffer",
"CRenderPhaseDrawScene",
"CRenderPhaseFrontEnd",
"CRenderPhaseHeight",
"CRenderPhaseHud",
"CRenderPhaseLensDistortion",
"CRenderPhaseMirrorReflection",
"CRenderPhaseParaboloidShadow",
"CRenderPhasePedDamageUpdate",
"CRenderPhasePhoneModel",
"CRenderPhasePhoneScreen",
"CRenderPhasePreRenderViewport",
"CRenderPhaseRainUpdate",
"CRenderPhaseReflection",
"CRenderPhaseScanned",
"CRenderPhaseScript2d",
"CRenderPhaseTimeBars",
"CRenderPhaseWaterReflection",
"CRenderPhaseWaterSurface",
"CReplayClipScanner",
"CReplayExtensionsController",
"CReplayInterface",
"CReplayInterfaceCamera",
"CReplayInterfaceGame",
"CReplayInterfaceObject",
"CReplayInterfacePed",
"CReplayInterfacePickup",
"CReplayInterfaceVeh",
"CReplayPlaybackController",
"CReplayPreloader",
"CReplayPreplayer",
"CReportCashSpawnEvent",
"CReportMenu",
"CRequestControlEvent",
"CRequestDetachmentEvent",
"CRequestDoorEvent",
"CRequestMapPickupEvent",
"CRequestNetworkSyncedSceneEvent",
"CRequestPhoneExplosionEvent",
"CRequestPickupEvent",
"CRespawnPlayerPedEvent",
"CRewardedVehicleExtension",
"CRoadBlock",
"CRoadBlockSpikeStrip",
"CRoadBlockVehicles",
"CRootSlopeFixupIkSolver",
"CRotaryWingAircraft",
"CRsRef",
"CSGameManager",
"CSManagerBase",
"CSPClusterFSMWrapper",
"CSPFriendPlayerCardDataManager",
"CSPPlayerListMenu",
"CSSHandler",
"CSSStringBuilder",
"CSSTextFormatLoader",
"CSaveGameBuffer",
"CSaveGameBuffer_MultiplayerCharacter",
"CSaveGameBuffer_MultiplayerCommon",
"CSaveGameBuffer_SinglePlayer",
"CSavegameFrontEnd",
"CSavegamePhotoMugshotUploader",
"CSavegameQueuedOperation",
"CSavegameQueuedOperation_Autosave",
"CSavegameQueuedOperation_CheckFileExists",
"CSavegameQueuedOperation_CreateSortedListOfLocalPhotos",
"CSavegameQueuedOperation_DeleteFile",
"CSavegameQueuedOperation_DeleteReplayClips",
"CSavegameQueuedOperation_DeleteReplayFile",
"CSavegameQueuedOperation_EnumerateReplayFiles",
"CSavegameQueuedOperation_LoadLocalPhoto",
"CSavegameQueuedOperation_LoadMontage",
"CSavegameQueuedOperation_LoadMostRecentSave",
"CSavegameQueuedOperation_LoadPhotoForMissionCreator",
"CSavegameQueuedOperation_LoadReplayClip",
"CSavegameQueuedOperation_LoadReplayHeader",
"CSavegameQueuedOperation_MPStats_Load",
"CSavegameQueuedOperation_MPStats_Save",
"CSavegameQueuedOperation_ManualLoad",
"CSavegameQueuedOperation_ManualSave",
"CSavegameQueuedOperation_MissionRepeatLoad",
"CSavegameQueuedOperation_MissionRepeatSave",
"CSavegameQueuedOperation_PhotoSave",
"CSavegameQueuedOperation_ReplayUpdateFavourites",
"CSavegameQueuedOperation_SaveLocalPhoto",
"CSavegameQueuedOperation_SaveMontage",
"CSavegameQueuedOperation_SavePhotoForMissionCreator",
"CSavegameQueuedOperation_UpdateMetadataOfLocalAndCloudPhoto",
"CSavegameQueuedOperation_UploadLocalPhotoToCloud",
"CSavegameQueuedOperation_UploadMugshot",
"CScaleformColourClass",
"CScaleformMovieCreationTask",
"CScaleformPauseMenuLUTClass",
"CScaleformPreallocationDataFileMounter",
"CScenarioActionCombatExit",
"CScenarioActionConditionCanDoQuickBlendout",
"CScenarioActionConditionCloseOrRecent",
"CScenarioActionConditionCurrentlyRespondingToOtherEvent",
"CScenarioActionConditionEvent",
"CScenarioActionConditionForceAction",
"CScenarioActionConditionHasCowardReact",
"CScenarioActionConditionHasShockingReact",
"CScenarioActionConditionInRange",
"CScenarioActionConditionIsACopPed",
"CScenarioActionConditionIsAGangPed",
"CScenarioActionConditionIsASecurityPed",
"CScenarioActionConditionNot",
"CScenarioActionConditionResponseTask",
"CScenarioActionConditionResponseType",
"CScenarioActionCowardExitThenRespondToEvent",
"CScenarioActionFlee",
"CScenarioActionHeadTrack",
"CScenarioActionImmediateExit",
"CScenarioActionManager",
"CScenarioActionNormalExit",
"CScenarioActionNormalExitThenRespondToEvent",
"CScenarioActionScriptExit",
"CScenarioActionShockReaction",
"CScenarioActionThreatResponseExit",
"CScenarioClipHelper",
"CScenarioCondition",
"CScenarioConditionAffluence",
"CScenarioConditionAlert",
"CScenarioConditionAmbientEventDirection",
"CScenarioConditionAmbientEventTypeCheck",
"CScenarioConditionArePedConfigFlagsSet",
"CScenarioConditionArePedConfigFlagsSetOnOtherPed",
"CScenarioConditionAttachedToPropOfType",
"CScenarioConditionBraveryFlagSet",
"CScenarioConditionCanPlayInCarIdle",
"CScenarioConditionCanStartNewPhoneConversation",
"CScenarioConditionCrouched",
"CScenarioConditionDistanceToPlayer",
"CScenarioConditionEquippedWeapon",
"CScenarioConditionFullyInIdle",
"CScenarioConditionHasComponentWithFlag",
"CScenarioConditionHasHighHeels",
"CScenarioConditionHasNoProp",
"CScenarioConditionHasParachute",
"CScenarioConditionHasProp",
"CScenarioConditionHeadbobMusicGenre",
"CScenarioConditionHealth",
"CScenarioConditionInCover",
"CScenarioConditionInInterior",
"CScenarioConditionInStationaryVehicleScenario",
"CScenarioConditionInVehicleOfType",
"CScenarioConditionInVehicleSeat",
"CScenarioConditionIsHeadbobbingToRadioMusicEnabled",
"CScenarioConditionIsMale",
"CScenarioConditionIsMultiplayerGame",
"CScenarioConditionIsPanicking",
"CScenarioConditionIsPlayer",
"CScenarioConditionIsPlayerInMultiplayerGame",
"CScenarioConditionIsPlayerTired",
"CScenarioConditionIsRadioPlaying",
"CScenarioConditionIsRadioPlayingMusic",
"CScenarioConditionIsReaction",
"CScenarioConditionIsSwat",
"CScenarioConditionIsTwoHandedWeaponEquipped",
"CScenarioConditionJustGotUp",
"CScenarioConditionModel",
"CScenarioConditionMovementModeType",
"CScenarioConditionOnFootClipSet",
"CScenarioConditionOnStraightPath",
"CScenarioConditionOutOfBreath",
"CScenarioConditionPedHeading",
"CScenarioConditionPhoneConversationAvailable",
"CScenarioConditionPhoneConversationStarting",
"CScenarioConditionPlayerHasSpaceForIdle",
"CScenarioConditionPlayingAnim",
"CScenarioConditionRaining",
"CScenarioConditionRoleInSyncedScene",
"CScenarioConditionSet",
"CScenarioConditionSetOr",
"CScenarioConditionSnowing",
"CScenarioConditionSpeed",
"CScenarioConditionSunny",
"CScenarioConditionTechSavvy",
"CScenarioConditionTime",
"CScenarioConditionWet",
"CScenarioConditionWindy",
"CScenarioConditionWorldPosWithinSphere",
"CScenarioConditionWorldSet",
"CScenarioFinder",
"CScenarioInfoManager",
"CScenarioLookAtInfo",
"CScenarioMoveBetweenInfo",
"CScenarioParkedVehicleInfo",
"CScenarioPhoneConversationInProgress",
"CScenarioPointManager",
"CScenarioSkiLiftInfo",
"CScenarioSkiingInfo",
"CScenarioWanderingInRadiusInfo",
"CScenarioWanderingInfo",
"CSceneStreamer",
"CSceneStreamerBase",
"CSceneStreamerMgr",
"CScriptArrayDataVerifyEvent",
"CScriptBrainFileMounter",
"CScriptEntityStateChangeEvent",
"CScriptIncident",
"CScriptMenu",
"CScriptMetadata",
"CScriptOp",
"CScriptResource_Animation",
"CScriptResource_Camera",
"CScriptResource_Checkpoint",
"CScriptResource_ClipSet",
"CScriptResource_ClothDictionary",
"CScriptResource_Coverpoint",
"CScriptResource_CutFile",
"CScriptResource_CutScene",
"CScriptResource_DispatchTimeBetweenSpawnAttempts",
"CScriptResource_DispatchTimeBetweenSpawnAttemptsMultiplier",
"CScriptResource_Drawable",
"CScriptResource_DrawableDictionary",
"CScriptResource_Fire",
"CScriptResource_FragDictionary",
"CScriptResource_ItemSet",
"CScriptResource_MLO",
"CScriptResource_Model",
"CScriptResource_MovementMode_Asset",
"CScriptResource_MovieMeshSet",
"CScriptResource_PTFX",
"CScriptResource_PTFX_Asset",
"CScriptResource_PatrolRoute",
"CScriptResource_PedGroup",
"CScriptResource_PopScheduleVehicleModelOverride",
"CScriptResource_RadarBlip",
"CScriptResource_RelGroup",
"CScriptResource_Rope",
"CScriptResource_ScaleformMovie",
"CScriptResource_ScenarioBlockingArea",
"CScriptResource_SequenceTask",
"CScriptResource_SpeedZone",
"CScriptResource_StreamedScript",
"CScriptResource_SyncedScene",
"CScriptResource_TextureDictionary",
"CScriptResource_VehicleCombatAvoidanceArea",
"CScriptResource_VehicleRecording",
"CScriptResource_Vehicle_Asset",
"CScriptResource_Weapon_Asset",
"CScriptShapeTestResults",
"CScriptStreamingResource",
"CScriptWorldStateEvent",
"CScriptedCoronaPlayerCardDataManager",
"CScriptedCoronaPlayerListDataManager",
"CScriptedCoverPoint",
"CScriptedDirectorPlayerCardDataManager",
"CScriptedGameEvent",
"CScriptedPlayerCardDataManager",
"CScriptedPlayersMenu",
"CScrollBarAttr",
"CSeaPlaneExtension",
"CSeaPlaneHandlingData",
"CSearchLight",
"CSectorDataNode",
"CSectorPositionDataNode",
"CSelectionWheel",
"CSendKickVotesEvent",
"CSerialisedFSMTaskInfo",
"CSetLightingConstants",
"CSetPedFacialIdleAnimOverride",
"CSetPedRagdollBlockFlagParameters",
"CSetVehicleExclusiveDriver",
"CSetVehicleLockState",
"CSettingOfDriveTaskCruiseSpeed",
"CSettingOfLookAtEntity",
"CSettingOfPedRelationshipGroupHashParameters",
"CSettingOfPlaneMinHeightAboveTerrainParameters",
"CSettingOfTaskVehicleTempAction",
"CSettings",
"CSettingsManager",
"CSettingsMenu",
"CShaderVariableComponent",
"CShapeTestBatchDesc",
"CShapeTestBoundDesc",
"CShapeTestBoundingBoxDesc",
"CShapeTestCapsuleDesc",
"CShapeTestDesc",
"CShapeTestProbeDesc",
"CShapeTestSphereDesc",
"CShopData",
"CSocialClubInboxMgr",
"CSpawnPoint",
"CSpawnPointOverrideExtension",
"CSpinnerData",
"CStartNetworkPedArrestEvent",
"CStartNetworkPedUncuffEvent",
"CStartNetworkSyncedSceneEvent",
"CStartProjectileEvent",
"CStatUpdatePresenceEvent",
"CStatsDisplayListFileMounter",
"CStatsMenu",
"CStatsSaveStructure",
"CStatsUIListFileMounter",
"CStealVehicleCrime",
"CStickyBombsArrayHandler",
"CStopNetworkSyncedSceneEvent",
"CStopSoundEvent",
"CStreamedScripts",
"CStreamingCleanup",
"CStreamingRequestList",
"CSubmarine",
"CSubmarineCar",
"CSubmarineControlDataNode",
"CSubmarineGameStateDataNode",
"CSubmarineHandlingData",
"CSubmarineSyncTree",
"CSwatAutomobileDispatch",
"CSwatHelicopterDispatch",
"CSwatOrder",
"CSyncDataBase",
"CSyncDataLogger",
"CSyncDataReader",
"CSyncDataSizeCalculator",
"CSyncDataWriter",
"CSyncedSceneAudioInterface",
"CSystemSettings",
"CTacticalAnalysis",
"CTargettingDifficultyInfo",
"CTask",
"CTaskAdvance",
"CTaskAffectSecondaryBehaviour",
"CTaskAggressiveRubberneck",
"CTaskAgitated",
"CTaskAgitatedAction",
"CTaskAimAndThrowProjectile",
"CTaskAimFromGround",
"CTaskAimGun",
"CTaskAimGunBlindFire",
"CTaskAimGunFromCoverIntro",
"CTaskAimGunFromCoverOutro",
"CTaskAimGunOnFoot",
"CTaskAimGunScripted",
"CTaskAimGunVehicleDriveBy",
"CTaskAimSweep",
"CTaskAmbientClips",
"CTaskAmbientLookAtEvent",
"CTaskAmbulancePatrol",
"CTaskAnimatedAttach",
"CTaskAnimatedFallback",
"CTaskAnimatedHitByExplosion",
"CTaskArmy",
"CTaskArrestPed",
"CTaskArrestPed2",
"CTaskBirdLocomotion",
"CTaskBlendFromNM",
"CTaskBoatChase",
"CTaskBoatCombat",
"CTaskBoatStrafe",
"CTaskBomb",
"CTaskBringVehicleToHalt",
"CTaskBusted",
"CTaskCallPolice",
"CTaskCarDrive",
"CTaskCarDriveWander",
"CTaskCarReactToVehicleCollision",
"CTaskCarReactToVehicleCollisionGetOut",
"CTaskCarSetTempAction",
"CTaskCharge",
"CTaskChat",
"CTaskChatScenario",
"CTaskClearLookAt",
"CTaskClimbLadder",
"CTaskClimbLadderFully",
"CTaskClip",
"CTaskCloseVehicleDoorFromInside",
"CTaskCloseVehicleDoorFromOutside",
"CTaskCombat",
"CTaskCombatAdditionalTask",
"CTaskCombatClosestTargetInArea",
"CTaskCombatFlank",
"CTaskCombatMounted",
"CTaskCombatMountedTransitions",
"CTaskCombatRoll",
"CTaskCombatSeekCover",
"CTaskComplex",
"CTaskComplexControlMovement",
"CTaskComplexEvasiveStep",
"CTaskComplexGetOffBoat",
"CTaskComplexOnFire",
"CTaskComplexStuckInAir",
"CTaskConfront",
"CTaskControlVehicle",
"CTaskConversationHelper",
"CTaskCoupleScenario",
"CTaskCover",
"CTaskCower",
"CTaskCowerScenario",
"CTaskCrawl",
"CTaskCrouch",
"CTaskCrouchToggle",
"CTaskCutScene",
"CTaskDamageElectric",
"CTaskDeadBodyScenario",
"CTaskDetonator",
"CTaskDiveToGround",
"CTaskDoNothing",
"CTaskDraggedToSafety",
"CTaskDraggingToSafety",
"CTaskDropDown",
"CTaskDuckAndCover",
"CTaskDyingDead",
"CTaskEnterCover",
"CTaskEnterVehicle",
"CTaskEnterVehicleAlign",
"CTaskEnterVehicleSeat",
"CTaskEscapeBlast",
"CTaskExhaustedFlee",
"CTaskExitCover",
"CTaskExitVehicle",
"CTaskExitVehicleSeat",
"CTaskFSMClone",
"CTaskFall",
"CTaskFallAndGetUp",
"CTaskFallOver",
"CTaskFirePatrol",
"CTaskFishLocomotion",
"CTaskFlightlessBirdLocomotion",
"CTaskFlyAway",
"CTaskFlyingWander",
"CTaskFollowLeaderAnyMeans",
"CTaskFollowLeaderInFormation",
"CTaskFollowWaypointRecording",
"CTaskForceMotionState",
"CTaskGangPatrol",
"CTaskGeneralSweep",
"CTaskGetOffTrain",
"CTaskGetOnTrain",
"CTaskGetOutOfWater",
"CTaskGetUp",
"CTaskGetUpAndStandStill",
"CTaskGoToAndClimbLadder",
"CTaskGoToCarDoorAndStandStill",
"CTaskGoToPointAiming",
"CTaskGoToPointAnyMeans",
"CTaskGoToScenario",
"CTaskGrowlAndFlee",
"CTaskGun",
"CTaskHandsUp",
"CTaskHeliChase",
"CTaskHeliCombat",
"CTaskHeliOrderResponse",
"CTaskHeliPassengerRappel",
"CTaskHelicopterStrafe",
"CTaskHelperFSM",
"CTaskHitWall",
"CTaskHumanLocomotion",
"CTaskInCover",
"CTaskInVehicleBasic",
"CTaskInVehicleSeatShuffle",
"CTaskInfo",
"CTaskIntimidate",
"CTaskInvestigate",
"CTaskJump",
"CTaskJumpVault",
"CTaskLeaveAnyCar",
"CTaskList",
"CTaskManager",
"CTaskMelee",
"CTaskMeleeActionResult",
"CTaskMeleeUpperbodyAnims",
"CTaskMoVEScripted",
"CTaskMobilePhone",
"CTaskMotionAiming",
"CTaskMotionAimingTransition",
"CTaskMotionBase",
"CTaskMotionBasicLocomotionLowLod",
"CTaskMotionDiving",
"CTaskMotionDrunk",
"CTaskMotionInAutomobile",
"CTaskMotionInCover",
"CTaskMotionInTurret",
"CTaskMotionInVehicle",
"CTaskMotionOnBicycle",
"CTaskMotionOnBicycleController",
"CTaskMotionParachuting",
"CTaskMotionPed",
"CTaskMotionPedLowLod",
"CTaskMotionStrafing",
"CTaskMotionSwimming",
"CTaskMotionTennis",
"CTaskMountThrowProjectile",
"CTaskMoveBetweenPointsScenario",
"CTaskMoveInterface",
"CTaskMoveToTacticalPoint",
"CTaskMoveWithinAttackWindow",
"CTaskMoveWithinDefensiveArea",
"CTaskNMBalance",
"CTaskNMBehaviour",
"CTaskNMBrace",
"CTaskNMBuoyancy",
"CTaskNMControl",
"CTaskNMDangle",
"CTaskNMDraggingToSafety",
"CTaskNMDrunk",
"CTaskNMElectrocute",
"CTaskNMExplosion",
"CTaskNMFallDown",
"CTaskNMFlinch",
"CTaskNMGenericAttach",
"CTaskNMHighFall",
"CTaskNMInjuredOnGround",
"CTaskNMJumpRollFromRoadVehicle",
"CTaskNMOnFire",
"CTaskNMPose",
"CTaskNMPrototype",
"CTaskNMRelax",
"CTaskNMRiverRapids",
"CTaskNMScriptControl",
"CTaskNMShot",
"CTaskNMSimple",
"CTaskNMSit",
"CTaskNMThroughWindscreen",
"CTaskNetworkClone",
"CTaskOpenDoor",
"CTaskOpenVehicleDoorFromOutside",
"CTaskParachute",
"CTaskParachuteObject",
"CTaskPatrol",
"CTaskPause",
"CTaskPlaneChase",
"CTaskPlayerDrive",
"CTaskPlayerIdles",
"CTaskPlayerOnFoot",
"CTaskPlayerWeapon",
"CTaskPolice",
"CTaskPoliceOrderResponse",
"CTaskPoliceWantedResponse",
"CTaskPursueCriminal",
"CTaskQuadLocomotion",
"CTaskRageRagdoll",
"CTaskRappel",
"CTaskReactAimWeapon",
"CTaskReactAndFlee",
"CTaskReactInDirection",
"CTaskReactToBeingAskedToLeaveVehicle",
"CTaskReactToBeingJacked",
"CTaskReactToBuddyShot",
"CTaskReactToDeadPed",
"CTaskReactToExplosion",
"CTaskReactToGunAimedAt",
"CTaskReactToImminentExplosion",
"CTaskReactToPursuit",
"CTaskReloadGun",
"CTaskRepositionMove",
"CTaskRevive",
"CTaskRideTrain",
"CTaskRunClip",
"CTaskRunNamedClip",
"CTaskSayAudio",
"CTaskScenario",
"CTaskScenarioFlee",
"CTaskScriptedAnimation",
"CTaskSearch",
"CTaskSearchBase",
"CTaskSearchForUnknownThreat",
"CTaskSearchInAutomobile",
"CTaskSearchInBoat",
"CTaskSearchInHeli",
"CTaskSearchInVehicleBase",
"CTaskSearchOnFoot",
"CTaskSeekEntityAiming",
"CTaskSeparate",
"CTaskSetAndGuardArea",
"CTaskSetBlockingOfNonTemporaryEvents",
"CTaskSetCharDecisionMaker",
"CTaskSetPedDefensiveArea",
"CTaskSetPedInVehicle",
"CTaskSetPedOutOfVehicle",
"CTaskSharkAttack",
"CTaskShellShocked",
"CTaskShockingEvent",
"CTaskShockingEventBackAway",
"CTaskShockingEventGoto",
"CTaskShockingEventHurryAway",
"CTaskShockingEventReact",
"CTaskShockingEventReactToAircraft",
"CTaskShockingEventStopAndStare",
"CTaskShockingEventThreatResponse",
"CTaskShockingEventWatch",
"CTaskShockingNiceCarPicture",
"CTaskShockingPoliceInvestigate",
"CTaskShootAtTarget",
"CTaskShootOutTire",
"CTaskShove",
"CTaskShovePed",
"CTaskShoved",
"CTaskSidestep",
"CTaskSlideToCoord",
"CTaskSlopeScramble",
"CTaskSmartFlee",
"CTaskSmashCarWindow",
"CTaskStandGuard",
"CTaskStandGuardFSM",
"CTaskStayInCover",
"CTaskStealVehicle",
"CTaskStealthKill",
"CTaskSubmarineChase",
"CTaskSubmarineCombat",
"CTaskSwapWeapon",
"CTaskSwat",
"CTaskSwatFollowInLine",
"CTaskSwatGoToStagingArea",
"CTaskSwatOrderResponse",
"CTaskSwatWantedResponse",
"CTaskSwimmingWander",
"CTaskSynchronizedScene",
"CTaskTakeOffHelmet",
"CTaskTakeOffPedVariation",
"CTaskTargetUnreachable",
"CTaskTargetUnreachableInExterior",
"CTaskTargetUnreachableInInterior",
"CTaskThreatResponse",
"CTaskThrowProjectile",
"CTaskToHurtTransit",
"CTaskTrainBase",
"CTaskTree",
"CTaskTreeClone",
"CTaskTreeMotion",
"CTaskTreeMovement",
"CTaskTreePed",
"CTaskTriggerLookAt",
"CTaskTryToGrabVehicleDoor",
"CTaskTurnToFaceEntityOrCoord",
"CTaskUnalerted",
"CTaskUseClimbOnRoute",
"CTaskUseDropDownOnRoute",
"CTaskUseLadderOnRoute",
"CTaskUseScenario",
"CTaskUseScenarioEntityExtension",
"CTaskUseSequence",
"CTaskUseVehicleScenario",
"CTaskVariedAimPose",
"CTaskVault",
"CTaskVehicleAnimation",
"CTaskVehicleApproach",
"CTaskVehicleAttack",
"CTaskVehicleAttackTank",
"CTaskVehicleBlock",
"CTaskVehicleBlockBackAndForth",
"CTaskVehicleBlockBrakeInFront",
"CTaskVehicleBlockCruiseInFront",
"CTaskVehicleBoostUseSteeringAngle",
"CTaskVehicleBrake",
"CTaskVehicleBurnout",
"CTaskVehicleChase",
"CTaskVehicleCircle",
"CTaskVehicleCombat",
"CTaskVehicleConvertibleRoof",
"CTaskVehicleCrash",
"CTaskVehicleCruiseBoat",
"CTaskVehicleCruiseNew",
"CTaskVehicleDeadDriver",
"CTaskVehicleEscort",
"CTaskVehicleFSM",
"CTaskVehicleFlee",
"CTaskVehicleFleeAirborne",
"CTaskVehicleFleeBoat",
"CTaskVehicleFlyDirection",
"CTaskVehicleFollow",
"CTaskVehicleFollowRecording",
"CTaskVehicleFollowWaypointRecording",
"CTaskVehicleGoForward",
"CTaskVehicleGoTo",
"CTaskVehicleGoToAutomobileNew",
"CTaskVehicleGoToBoat",
"CTaskVehicleGoToHelicopter",
"CTaskVehicleGoToNavmesh",
"CTaskVehicleGoToPlane",
"CTaskVehicleGoToPointAutomobile",
"CTaskVehicleGoToPointWithAvoidanceAutomobile",
"CTaskVehicleGoToSubmarine",
"CTaskVehicleGotoLongRange",
"CTaskVehicleGun",
"CTaskVehicleHandBrake",
"CTaskVehicleHeadonCollision",
"CTaskVehicleHeliProtect",
"CTaskVehicleHover",
"CTaskVehicleLand",
"CTaskVehicleLandPlane",
"CTaskVehicleMissionBase",
"CTaskVehicleMountedWeapon",
"CTaskVehicleNoDriver",
"CTaskVehicleParkNew",
"CTaskVehiclePassengerExit",
"CTaskVehiclePersuit",
"CTaskVehiclePlaneChase",
"CTaskVehiclePlayerDrive",
"CTaskVehiclePlayerDriveAutogyro",
"CTaskVehiclePlayerDriveAutomobile",
"CTaskVehiclePlayerDriveBike",
"CTaskVehiclePlayerDriveBoat",
"CTaskVehiclePlayerDriveDiggerArm",
"CTaskVehiclePlayerDriveHeli",
"CTaskVehiclePlayerDrivePlane",
"CTaskVehiclePlayerDriveRotaryWingAircraft",
"CTaskVehiclePlayerDriveSubmarine",
"CTaskVehiclePlayerDriveSubmarineCar",
"CTaskVehiclePlayerDriveTrain",
"CTaskVehiclePoliceBehaviour",
"CTaskVehiclePoliceBehaviourBoat",
"CTaskVehiclePoliceBehaviourHelicopter",
"CTaskVehicleProjectile",
"CTaskVehiclePullAlongside",
"CTaskVehiclePullOver",
"CTaskVehiclePursue",
"CTaskVehicleRam",
"CTaskVehicleReactToCopSiren",
"CTaskVehicleRevEngine",
"CTaskVehicleReverse",
"CTaskVehicleSerialiser",
"CTaskVehicleSerialiserBase",
"CTaskVehicleShotTire",
"CTaskVehicleSpinOut",
"CTaskVehicleStop",
"CTaskVehicleSurfaceInSubmarine",
"CTaskVehicleSwerve",
"CTaskVehicleTempAction",
"CTaskVehicleThreePointTurn",
"CTaskVehicleTransformToSubmarine",
"CTaskVehicleTurn",
"CTaskVehicleWait",
"CTaskWaitForCondition",
"CTaskWaitForSteppingOut",
"CTaskWalkAway",
"CTaskWander",
"CTaskWanderInArea",
"CTaskWanderingInRadiusScenario",
"CTaskWanderingScenario",
"CTaskWeapon",
"CTaskWeaponBlocked",
"CTaskWitness",
"CTaskWrithe",
"CTexLodInterface",
"CTextInputBox",
"CTextMessageEvent",
"CTextOverlayHandle",
"CTimeArchetypeDef",
"CTimeCycleModifier",
"CTimeModelInfo",
"CTorsoIkSolver",
"CTorsoReactIkSolver",
"CTorsoVehicleIkSolver",
"CTorsoVehicleIkSolverProxy",
"CTournamentInvite",
"CTrackedEventInfo",
"CTrackedEventInfoBase",
"CTrailer",
"CTrailerHandlingData",
"CTrailerLegs",
"CTrain",
"CTrainCloudListener",
"CTrainConfigFileMounter",
"CTrainGameStateDataNode",
"CTrainSyncTree",
"CTreePrototype",
"CTuning",
"CTurret",
"CTurretPhysical",
"CTxdRelationship",
"CUGCStatUpdatePresenceEvent",
"CUIReplayScaleformController",
"CUnderwaterArmedTransitions",
"CUpdateFxnEvent",
"CUpdateNetworkSyncedSceneEvent",
"CUpdatePlayerScarsEvent",
"CVFXBloodFileMounter",
"CVFXScriptFileMounter",
"CVehInterp",
"CVehicle",
"CVehicleAngVelocityDataNode",
"CVehicleAppearanceDataNode",
"CVehicleChaseDirector",
"CVehicleColorsDataFileMounter",
"CVehicleCombatAvoidanceArea",
"CVehicleComponentControlEvent",
"CVehicleComponentReservationDataNode",
"CVehicleConditionEntryPointHasOpenableDoor",
"CVehicleConditionRoofState",
"CVehicleControlDataNode",
"CVehicleCreationDataNode",
"CVehicleDamageStatusDataNode",
"CVehicleDrawHandler",
"CVehicleExtrasFileMounter",
"CVehicleFactory",
"CVehicleGadget",
"CVehicleGadgetArticulatedDiggerArm",
"CVehicleGadgetBoatBoom",
"CVehicleGadgetBombBay",
"CVehicleGadgetDataNode",
"CVehicleGadgetDiggerArm",
"CVehicleGadgetForks",
"CVehicleGadgetHandlerFrame",
"CVehicleGadgetParkingSensor",
"CVehicleGadgetPickUpRope",
"CVehicleGadgetPickUpRopeWithHook",
"CVehicleGadgetPickUpRopeWithMagnet",
"CVehicleGadgetThresher",
"CVehicleGadgetTowArm",
"CVehicleGadgetWithJointsBase",
"CVehicleGameStateDataNode",
"CVehicleGlassComponentEntity",
"CVehicleGlassDrawHandler",
"CVehicleHandlingFileMounter",
"CVehicleHealthDataNode",
"CVehicleIntelligence",
"CVehicleIntelligenceFactory",
"CVehicleLeanHelper",
"CVehicleMetaDataFileMounter",
"CVehicleMetadataFileMounter",
"CVehicleModelInfo",
"CVehicleModelInfoBumperCollision",
"CVehicleModelInfoBuoyancy",
"CVehicleModelInfoDoors",
"CVehicleModelInfoRagdollActivation",
"CVehicleModelInfoVarGlobal",
"CVehiclePopulationTuning",
"CVehicleProximityMigrationDataNode",
"CVehicleRadar",
"CVehicleRecording",
"CVehicleRecordingStreamingModule",
"CVehicleScanner",
"CVehicleScriptGameStateDataNode",
"CVehicleSteeringDataNode",
"CVehicleStreamRenderGfx",
"CVehicleStreamRequestGfx",
"CVehicleSyncData",
"CVehicleSyncTree",
"CVehicleTaskDataNode",
"CVehicleTaskManager",
"CVehicleTracks",
"CVehicleTrailerAttachPoint",
"CVehicleVariationDataFileMounter",
"CVehicleWaterCannon",
"CVehicleWeaponBattery",
"CVehicleWeaponHandlingData",
"CVehicleWeaponMgr",
"CVfxExplosionFileMounter",
"CVfxVehicleInfoFileMounter",
"CVideoEditorTimelineThumbnailManager",
"CVideoFileDataProvider",
"CVideoFileView",
"CVideoProjectFileDataProvider",
"CVideoProjectFileView",
"CVideoSettings",
"CVideoUploadManager",
"CVideoUploadManager_Custom",
"CVideoUploadTunablesListener",
"CViewport",
"CViewportFrontend3DScene",
"CViewportGame",
"CViewportPrimaryOrtho",
"CVisualEffectsFileMounter",
"CVoiceDrivenMouthMovementFinishedEvent",
"CVoiceSessionInvite",
"CVoiceSessionResponse",
"CWantedHelicopterDispatch",
"CWantedIncident",
"CWarningMessage",
"CWaterData",
"CWaypointRecordingRoute",
"CWaypointRecordingStreamingInterface",
"CWeapon",
"CWeaponAnimationsDataFileMounter",
"CWeaponComponent",
"CWeaponComponentClipInfo",
"CWeaponComponentDataFileMounter",
"CWeaponComponentFlashLightInfo",
"CWeaponComponentGroupInfo",
"CWeaponComponentInfo",
"CWeaponComponentLaserSightInfo",
"CWeaponComponentProgrammableTargeting",
"CWeaponComponentProgrammableTargetingInfo",
"CWeaponComponentReloadData",
"CWeaponComponentReloadLoopedData",
"CWeaponComponentScope",
"CWeaponComponentScopeInfo",
"CWeaponComponentSuppressorInfo",
"CWeaponController",
"CWeaponControllerFixed",
"CWeaponControllerPlayer",
"CWeaponDamageEvent",
"CWeaponInfo",
"CWeaponInfoBlob",
"CWeaponInfoDataFileMounter",
"CWeaponMetaDataFileMounter",
"CWeaponModelInfo",
"CWeaponObserver",
"CWeaponSwapData",
"CWeaponWheel",
"CWheelInstanceData_UsageInfo",
"CWindDisturbanceAttr",
"CWitnessInformationManager",
"CWorldGridOwnerArrayHandler",
"CWorldPointAttr",
"CZonedAssets",
"CacheCatalogParsableInfo",
"CacheFileData",
"CacheParsableInfo",
"Calibration",
"Calibrations",
"CatalogCacheListener",
"ChangeHandler",
"ClanOpData",
"ClipData",
"ClipUID",
"ClipUserData",
"ClonedTakeOffPedVariationInfo",
"CloudCacheListener",
"CloudFileDeleter",
"CloudFileLoadListener",
"CloudFileSaveListener",
"CloudHatFragContainer",
"CloudHatFragLayer",
"CloudHatManager",
"CloudHatSettings",
"CloudListController",
"CloudListener",
"CloudManagerMemAllocator",
"Cmd",
"CmdActivate",
"CmdChangeAttrs",
"CmdDestroy",
"CmdEstablish",
"CmdHost",
"CmdHostOffline",
"CmdJoin",
"CmdLeave",
"CmdMigrate",
"CmdModifyPresenceFlags",
"CmdSendInvites",
"CmdUpdateScAdvertisement",
"CommonScriptInfo",
"ComponentDescription",
"ConditionalAnimationsMounter",
"ConflictList",
"ControlSettings",
"CreatePlayerAppearanceTransactionHttpTask",
"CreateTask",
"CrewMetaDataCloudFile",
"CurveGenerator",
"CutSceneManager",
"Cxn",
"DNameStatusNode",
"Data",
"DataInterface",
"DatafileCloudWatcher",
"DeferredTask",
"Definition",
"DefinitionList",
"DeleteFileWorker",
"DeleteFromCloudOp",
"DeletePropertiesCallback",
"DeleteSlotTransaction",
"DeleteSlotTransactionHttpTask",
"DestroyPropertiesCallback",
"DestroyTagsCallback",
"DetachUpdateObservers",
"DeviceSettings",
"DisposeHandler",
"DocumentText",
"DynamicBankListBuilderFn",
"DynamicMapping",
"DynamicMappingList",
"DynamicMappings",
"DynamicPauseMenu",
"EarlyLeaveTask",
"EarnGameTransactionHttpTask",
"EarnLimitedServiceGameTransactionHttpTask",
"EndTask",
"FacialFilter",
"Factory",
"FactoryPooled",
"FallbackMachineHashStrategy",
"FallbackMachineHashStrategyDuplicate",
"FileStorePC",
"Filter",
"FinalizationCallback",
"FlushIterator",
"FontDisposeHandler",
"FriendsReaderTask",
"FuncTreeItem",
"GASActionBuffer",
"GASActionBufferData",
"GASDoAction",
"GASDoInitAction",
"GASExecuteTag",
"GASFnCall",
"GASLocalFrame",
"GASObjectInterface",
"GASPrototypeBase",
"GASRefCountCollector",
"GASStringManager",
"GASValueProperty",
"GAcquireInterface",
"GBoolFormatter",
"GDefaultAcquireInterface",
"GFmtResource",
"GFormatter",
"GFxASCSSFileLoader",
"GFxASMouseListener",
"GFxASXMLFileLoader",
"GFxASXMLRootNode",
"GFxAmpFunctionDesc",
"GFxAmpFunctionTreeStats",
"GFxAmpMemFragReport",
"GFxAmpMemItem",
"GFxAmpMovieFunctionStats",
"GFxAmpProfileFrame",
"GFxAmpViewStats",
"GFxButtonCharacterDef",
"GFxCSSFileLoaderAndParserImpl",
"GFxCompactedFont",
"GFxConstShapeCharacterDef",
"GFxConstShapeNoStyles",
"GFxConstShapeWithStyles",
"GFxDrawText",
"GFxDrawTextImpl",
"GFxDrawingContext",
"GFxEditTextCharacterDef",
"GFxExternalLibPtr",
"GFxFont",
"GFxFontCacheManager",
"GFxFontCompactor",
"GFxFontData",
"GFxFontDataCompactedGfx",
"GFxFontDataCompactedSwf",
"GFxFontGlyphPacker",
"GFxFontLib",
"GFxFontManager",
"GFxFontMap",
"GFxFontResource",
"GFxFontResourceCreator",
"GFxFunctionHandler",
"GFxGradientData",
"GFxGradientImageResourceCreator",
"GFxGradientImageResourceKey",
"GFxImageCreator",
"GFxImageFileInfo",
"GFxImageFileInfoKeyData",
"GFxImageFileKeyInterface",
"GFxImageFileResourceCreator",
"GFxImageResource",
"GFxImageResourceCreator",
"GFxInitImportActions",
"GFxKeyboardState",
"GFxLineStyle",
"GFxLoadCSSTask",
"GFxLoadQueueEntryMT",
"GFxLoadQueueEntryMT_LoadCSS",
"GFxLoadQueueEntryMT_LoadMovie",
"GFxLoadQueueEntryMT_LoadXML",
"GFxLoadStates",
"GFxLoadUpdateSync",
"GFxLoadXMLTask",
"GFxLoader",
"GFxLoaderTask",
"GFxLogBase",
"GFxMeshCache",
"GFxMeshCacheLimit",
"GFxMeshCacheManager",
"GFxMeshSet",
"GFxMorphCharacterDef",
"GFxMovieBindProcess",
"GFxMovieDataDefFileKeyData",
"GFxMovieDataDefFileKeyInterface",
"GFxMovieDefBindStates",
"GFxMovieDefImplKey",
"GFxMovieDefImplKeyInterface",
"GFxMovieImageLoadTask",
"GFxMoviePreloadTask",
"GFxParseControl",
"GFxPlaceObject",
"GFxPlaceObject2",
"GFxPlaceObject2a",
"GFxPlaceObject3",
"GFxPlaceObjectUnpacked",
"GFxRemoveObject",
"GFxRemoveObject2",
"GFxRenderConfig",
"GFxRenderGen",
"GFxRenderGenShape",
"GFxRenderGenStroker",
"GFxResource",
"GFxResourceFileInfo",
"GFxResourceLib",
"GFxResourceReport",
"GFxResourceWeakLib",
"GFxScale9GridInfo",
"GFxSetBackgroundColor",
"GFxShapeBase",
"GFxShapeCharacterDef",
"GFxShapeNoStyles",
"GFxShapeWithStyles",
"GFxSpriteDef",
"GFxState",
"GFxStateBag",
"GFxStaticTextCharacterDef",
"GFxStream",
"GFxStyledText",
"GFxSubImageResource",
"GFxSubImageResourceCreator",
"GFxSubImageResourceInfo",
"GFxSystemFontResourceKey",
"GFxSystemFontResourceKeyInterface",
"GFxTask",
"GFxTextAllocator",
"GFxTextClipboard",
"GFxTextDocView",
"GFxTextEditorKit",
"GFxTextFilter",
"GFxTextHTMLImageTagDesc",
"GFxTextImageDesc",
"GFxTextKeyMap",
"GFxTextureGlyph",
"GFxTextureGlyphData",
"GFxVertexInterface",
"GFxVertexInterface_XY16i",
"GFxVertexInterface_XY16iC32",
"GFxVertexInterface_XY16iCF32",
"GFxVertexInterface_XY32f",
"GFxVirtualPathIterator",
"GFxXMLDOMBuilder",
"GFxXMLDocument",
"GFxXMLElementNode",
"GFxXMLFileLoaderAndParserImpl",
"GFxXMLFileLoaderImpl",
"GFxXMLNode",
"GFxXMLParserHandler",
"GFxXMLPrefix",
"GFxXMLTextNode",
"GFxZlibSupport",
"GMemoryHeap",
"GMemoryHeapPT",
"GMsgFormat",
"GMutex_AreadyLockedAcquireInterface",
"GRefCountBaseGC",
"GRefCountBaseNTS",
"GRefCountCollector",
"GRefCountImplCore",
"GRefCountWeakSupportImpl",
"GRenderer",
"GResourceFormatter",
"GStrFormatter",
"GSwitchFormatter",
"GSysAllocBase",
"GSysAllocGranulator",
"GSysAllocWrapper",
"GTexture",
"GWaitable",
"GameServerPresenceEvent",
"GameTransactionDeleteSlotTask",
"GameTransactionItems",
"GameTransactionSessionMgr",
"GetCatalogGameTransactionHttpTask",
"GetTokenAndRunHTTPTask",
"GtaThread",
"HeapLimit",
"HeartBeatGameTransactionHttpTask",
"Holder",
"ICommonDataOperations",
"IDrawListPrototype",
"IListener",
"IMachineHashStrategy",
"IMachineHashStrategyDuplicate",
"IReplayExtensionsController",
"IScriptEntityStateParametersBase",
"InFrame",
"InitStruct",
"InputGroupDefinition",
"InputList",
"InputMapperAssignment",
"InputSettings",
"InstanceBucket",
"InstanceBucket_LOD2",
"InvokeCallback",
"KeyInfo",
"KeyInterface",
"Layout",
"LimitHandler",
"LoadFromCloudOp",
"LoadTaskData",
"LocalInvokeCallback",
"M",
"MA",
"Mapping",
"MappingList",
"MappingSettings",
"MatchFilter",
"MediaBufferBase",
"MediaBufferBlob",
"MediaBufferBlobNV12FromRGB32",
"MediaBufferExternalBlob",
"MemberVisitor",
"MemoryContext",
"MemoryContextImpl",
"MemoryFootprint",
"MemoryManager",
"MemoryProfileLocation",
"MemoryProfileLocationList",
"MemoryProfileModuleStat",
"MetricACQUIRED_HIDDEN_PACKAGE",
"MetricACQUIRED_WEAPON",
"MetricACTIVITY_DONE",
"MetricAMBIENT_MISSION_CRATE_CREATED",
"MetricARREST",
"MetricAUD_STATION_DETUNED",
"MetricAUD_TRACK_TAGGED",
"MetricAWARD_XP",
"MetricAddPresenceInvite",
"MetricBENCHMARK_FPS",
"MetricBENCHMARK_P",
"MetricCASH_CREATED",
"MetricCHARACTER_SKILLS",
"MetricCHEAT",
"MetricCLOTH_CHANGE",
"MetricCUTSCENE",
"MetricCheatCRCCompromised",
"MetricCheatCRCNotReplied",
"MetricCheatCRCRequestFlood",
"MetricCheatEXESize",
"MetricCheatScript",
"MetricDEATH",
"MetricDIRECTOR_MODE",
"MetricDirtyCloudRead",
"MetricDirtyProfileStatRead",
"MetricEARNED_ACHIEVEMENT",
"MetricEMERGENCY_SVCS",
"MetricENTERED_SOLO_SESSION",
"MetricEXIT_GAME",
"MetricFRIEND_ACTIVITY_DONE",
"MetricFailureToSynchProfileStats",
"MetricGAMETYPE_GENERIC",
"MetricGARAGETAMPER",
"MetricHEIST_SAVE_CHEAT",
"MetricHourlyXP",
"MetricIDLEKICK",
"MetricInfoChange",
"MetricJOBBENDED",
"MetricJOBLTS_ENDED",
"MetricJOBLTS_ROUND_ENDED",
"MetricJOB_ENDED",
"MetricJOB_STARTED",
"MetricLEAVE_JOBCHAIN",
"MetricLOADGAME",
"MetricMISSION_CHECKPOINT",
"MetricMISSION_OVER",
"MetricMatchmakingQueryResults",
"MetricMatchmakingQueryStart",
"MetricMpSession",
"MetricNETWORK_BAIL",
"MetricNETWORK_KICKED",
"MetricNEWGAME",
"MetricNPC_INVITE",
"MetricPCHARDWARE_CPU",
"MetricPCHARDWARE_GPU",
"MetricPCHARDWARE_MEM",
"MetricPCHARDWARE_MOBO",
"MetricPCHARDWARE_OS",
"MetricPCHARDWARE_OTHER",
"MetricPCSETTINGS",
"MetricPLAYER_INJURED",
"MetricPOST_RACE_CHECKPOINT",
"MetricPROP_CHANGE",
"MetricPlayStatString",
"MetricPlayerCoords",
"MetricQUICKFIX_TOOL",
"MetricRANDOMMISSION_DONE",
"MetricRANK_UP",
"MetricRDEV",
"MetricREMOVEAWARD_XP",
"MetricREPORTER",
"MetricROS_BET",
"MetricRQA",
"MetricReadStatsByGamer2",
"MetricReadStatsByGroups",
"MetricRelayUsage",
"MetricRemoteCheat",
"MetricRemoteInfoChange",
"MetricRemoteInfoChange2",
"MetricRemoteInfoChange3",
"MetricSESSION_BECAME_HOST",
"MetricSESSION_HOSTED",
"MetricSESSION_JOINED",
"MetricSESSION_LEFT",
"MetricSPAWN",
"MetricSTALL_DETECTED",
"MetricSTART_GAME",
"MetricSTART_OFFLINEMODE",
"MetricServiceFailed",
"MetricShopping",
"MetricTV_SHOW",
"MetricTamper",
"MetricUSED_VOICE_CHAT",
"MetricVEHICLE_DIST_DRIVEN",
"MetricVIDEO_EDITOR_SAVE",
"MetricVIDEO_EDITOR_UPLOAD",
"MetricVehicle",
"MetricWANTED_LEVEL",
"MetricWEAPON_MOD_CHANGE",
"MetricWEATHER",
"MetricWEBSITE_VISITED",
"MetricXP_LOSS",
"MouseSettings",
"NMBehavior",
"NMBehaviorInst",
"NMBehaviorPool",
"NMExtraTunables",
"NMParam",
"NMTaskSimpleTunableEntry",
"NMTuningSetEntry",
"NMValue",
"NMValueBool",
"NMValueFloat",
"NMValueInt",
"NMValueString",
"NMValueVector3",
"NULLGameTransactionHttpTask",
"NatTraversal",
"NetAction",
"NetQueryFunctions",
"NetworkBaseConfig",
"NetworkBettingCloudManager",
"NetworkClanOp",
"NetworkClanOp_FriendEventData",
"NetworkClanOp_InviteRequestData",
"NetworkCrewEmblemMgr",
"NetworkGameConfig",
"NetworkGameFilter",
"NmRs1DofEffector",
"NmRs1DofEffectorInputBlendable",
"NmRs1DofEffectorInputWrapper",
"NmRs3DofEffector",
"NmRs3DofEffectorInputBlendable",
"NmRs3DofEffectorInputWrapper",
"NmRsArmInputBlendable",
"NmRsArmInputWrapper",
"NmRsBody",
"NmRsCBUAnimPose",
"NmRsCBUArmsWindmill",
"NmRsCBUArmsWindmillAdaptive",
"NmRsCBUBalancerCollisionsReaction",
"NmRsCBUBodyBalance",
"NmRsCBUBodyFoetal",
"NmRsCBUBodyWrithe",
"NmRsCBUBraceForImpact",
"NmRsCBUBuoyancy",
"NmRsCBUCarried",
"NmRsCBUCatchFall",
"NmRsCBUDangle",
"NmRsCBUDynamicBalancer",
"NmRsCBUElectrocute",
"NmRsCBUFallOverWall",
"NmRsCBUFlinch",
"NmRsCBUGrab",
"NmRsCBUHeadLook",
"NmRsCBUInjuredOnGround",
"NmRsCBUOnFire",
"NmRsCBUPedal",
"NmRsCBUPointArm",
"NmRsCBUPointGun",
"NmRsCBURollDownStairs",
"NmRsCBURollUp",
"NmRsCBUShot",
"NmRsCBUSpineTwist",
"NmRsCBUStaggerFall",
"NmRsCBUTeeter",
"NmRsCBUYanked",
"NmRsCharacter",
"NmRsEffectorBase",
"NmRsEffectorInputWrapper",
"NmRsEngine",
"NmRsHumanArm",
"NmRsHumanBody",
"NmRsHumanLeg",
"NmRsHumanSpine",
"NmRsIKInputWrapper",
"NmRsInputWrapperBase",
"NmRsLegInputBlendable",
"NmRsLegInputWrapper",
"NmRsLimb",
"NmRsSetStiffnessInputWrapper",
"NmRsSpineInputBlendable",
"NmRsSpineInputWrapper",
"NmRsStopAllInputWrapper",
"NodeCommonDataOperations",
"ObjVisitor",
"PNTriangleTessellationGlobals",
"PTFX_ReplayStreamingRequest",
"PagedCloudText",
"PedHeadshotTextureExporter",
"PlaceHolder",
"PostMessageTask",
"PresenceMessageV1",
"PresenceMessageV2",
"PrimitiveBase",
"PrimitiveBox",
"PrimitiveCapsule",
"PrimitiveCylinder",
"PrimitiveSphere",
"PrimitiveTriangle",
"ProfileV2",
"ProjectData",
"PropDescription",
"PropertiesCallback",
"ProxyObserver",
"PurchGameTransactionHttpTask",
"PurchTransaction",
"QueryTask",
"RagdollFilter",
"RageDirect3DDevice11",
"RageDirect3DDeviceContext11",
"ReadVideoBlockWorker",
"Receiver",
"RecordingTask",
"RecordingTaskWindows",
"RecoupGameTransactionHttpTask",
"RefreshCredTask",
"RegisterTask",
"RelatedInputs",
"RenderPhaseSeeThrough",
"ReplayBicycleExtension",
"ReplayEntityExtension",
"ReplayExtensionBase",
"ReplayGlassExtension",
"ReplayHUDOverlayExtension",
"ReplayObjectExtension",
"ReplayParachuteExtension",
"ReplayPedExtension",
"ReplayReticuleExtension",
"ReplayStreamingRequest",
"ReplayTrafficLightExtension",
"ReplayVehicleExtension",
"ReplayVehicleWithWheelsExtension",
"ReportStatsTask",
"ResWorkItem",
"ResourceData",
"ResourceSlot",
"ResourceVisitor",
"RestrictionTags",
"RgscDelegate",
"RightHandWheelBones",
"RopeVertices",
"SCTourManager",
"SGeneralMovieData",
"SGeneralPauseDataConfig",
"SMBIOSMachineHashStrategyDuplicate",
"SRLMounter",
"SaveHeaderDataWorker",
"SaveToCloudOp",
"ScenarioInfoMounter",
"ScenarioPointMounter",
"SellVehicleGameTransactionHttpTask",
"SessionRestartGameTransactionHttpTask",
"SessionScriptRestartGameTransactionHttpTask",
"SessionStartGameTransactionHttpTask",
"SessionStartTransaction",
"Settings",
"ShaderVarFrameFilter",
"ShopPedApparel",
"ShopPedComponent",
"ShopPedOutfit",
"ShopPedProp",
"ShopVehicleData",
"ShopVehicleDataArray",
"ShopVehicleMod",
"ShopWeaponComponent",
"ShoreLineQuadTreeIntersectFunction",
"ShoreLinesQuadTreeUpdateFunction",
"SocialClubLegalsMenu",
"SocialClubMenu",
"SoftRasterizer",
"Source",
"SpendEarnTransaction",
"SpendGameTransactionHttpTask",
"SpendLimitedServiceGameTransactionHttpTask",
"SpendVehicleModTransactionHttpTask",
"StartTask",
"StateMachine",
"StaticFrameFilter",
"StaticInstanceBuffer",
"SveFileObject",
"SyncObserver",
"SyncTagObserver",
"TagsCallback",
"TattooShopItem",
"TattooShopItemArray",
"TessellationVars",
"TextDocumentListener",
"TextureEventHandler",
"TimeCycleFileMounter",
"TimedTuning",
"TrafficLightInfos",
"TriangleShape",
"Tunables",
"TunablesListener",
"TunnelSocket",
"TunnelerResult",
"UICrewLBPage",
"UICrewLBPaginator",
"UIDataPageBase",
"UIFriendsClanPage",
"UIFriendsClanPaginator",
"UIGeneralCrewPage",
"UIGeneralCrewPaginator",
"UIInvitePage",
"UIInvitePaginator",
"UIOpenClanPage",
"UIPaginator",
"UIPaginatorBase",
"UIRequestPage",
"UIRequestPaginator",
"UgcCreateRequestNode",
"UgcRequestNode",
"UnapprovedCameraLists",
"UtilityScriptInfo",
"VehicleAvoidanceTask",
"VehicleFragImpulseFunction",
"VehicleFragImpulseRange",
"VehicleGoToTask",
"VehicleIntelligenceProcessTask",
"VehicleInterpolationTask",
"VfxLightningCloudBurstCommonSettings",
"VfxLightningCloudBurstSettings",
"VfxLightningDirectionalBurstSettings",
"VfxLightningKeyFrames",
"VfxLightningSettings",
"VfxLightningStrikeForkPointSettings",
"VfxLightningStrikeSettings",
"VfxLightningStrikeSplitPointSettings",
"VfxLightningStrikeVariationSettings",
"VfxLightningTimeCycleSettings",
"VideoEncoderClassFactory",
"VideoPlaybackThumbnailManagerAdapter",
"VideoRecordingDataProviderAdapter",
"VideoRecordingInterface",
"VideoRecordingInterfaceWindows",
"VideoUploadPolicyMenu",
"VoiceChatAudioProvider",
"WaterSimInterp",
"WeaponShopItem",
"WeaponShopItemArray",
"WideStringStorage",
"Win32MachineHashStrategyDuplicate",
"WorkItem",
"Worker",
"XMLAttributeStringBuilder",
"XMLPrefixQuerier",
"XmlLog",
"XmlStdOutLog",
"ZonesQuadTreeUpdateFunction",
"_Func_base",
"_Func_impl",
"_Generic_error_category",
"_Iostream_error_category",
"_System_error_category",
"__non_rtti_object",
"`anonymous",
"`private:",
"`public:",
"`void",
"aiMeshStore",
"aiNavMeshStore",
"aiNavMeshStoreInterfaceGta",
"aiSplitArray",
"aiTask",
"aiTaskManager",
"aiTaskStateTransitionTable",
"aiTaskTree",
"arrayDataVerifyEvent",
"atDNetEventNode",
"atDNetObjectNode",
"atDNode",
"atDScriptObjectNode",
"atDTransactionNode",
"atReferenceCounter",
"atSingleton",
"audAmbientAudioEntity",
"audAmbientRadioEmitter",
"audAutomationSound",
"audBicycleAudioEntity",
"audBiquadFilterEffect",
"audBoatAudioEntity",
"audCachedBrokenGlassTestResults",
"audCachedCarLandingTestResults",
"audCachedMultipleTestResults",
"audCarAudioEntity",
"audCategoryManager",
"audCollapsingStereoSound",
"audCollisionAudioEntity",
"audCrossfadeSound",
"audCurveRepository",
"audCutTrack",
"audCutTrackCutScene",
"audCutTrackSynchedScene",
"audCutsceneAudioEntity",
"audDecoder",
"audDecoderAdpcm",
"audDecoderPcm",
"audDelayEffect",
"audDelayEffect4",
"audDirectionalAmbienceManager",
"audDirectionalSound",
"audDoorAudioEntity",
"audDspEffect",
"audDynamicEntitySound",
"audDynamicMixer",
"audEarlyReflectionEffect",
"audEmitterAudioEntity",
"audEntity",
"audEntityRadioEmitter",
"audEnvelopeSound",
"audEnvironmentGroupInterface",
"audEnvironmentGroupManager",
"audEnvironmentSound",
"audExplosionAudioEntity",
"audExternalStreamSound",
"audFadeInRadioAction",
"audFadeOutRadioAction",
"audFireAudioEntity",
"audFluctuatorSound",
"audForceRadioTrackAction",
"audFrontendAudioEntity",
"audGameObjectManager",
"audGlassAudioEntity",
"audGrainPlayer",
"audGranularEngineComponent",
"audGranularSound",
"audGranularSubmix",
"audGranularWobble",
"audGunFightConductor",
"audGunFightConductorDynamicMixing",
"audGunFireAudioEntity",
"audHeliAudioEntity",
"audIfSound",
"audInteractiveMusicManager",
"audKineticSound",
"audLoopingSound",
"audMathOperationSound",
"audMediaReaderPlatform",
"audMetadataDataFileMounter",
"audMixerDevice",
"audModularSynthSound",
"audMultitrackSound",
"audMusicAction",
"audObjectAudioEntity",
"audObjectModifiedInterface",
"audOnStopSound",
"audParameterTransformSound",
"audPcmSource",
"audPedAudioEntity",
"audPlaceableTracker",
"audPlaneAudioEntity",
"audPoliceScanner",
"audRadioAudioEntity",
"audRadioDjSpeechAction",
"audRadioEmitter",
"audRandomizedSound",
"audRetriggeredOverlappedSound",
"audReverbEffect4",
"audScannerManager",
"audScriptAudioEntity",
"audSequentialOverlapSound",
"audSequentialSound",
"audSetMoodAction",
"audShoreLine",
"audShoreLineLake",
"audShoreLineOcean",
"audShoreLinePool",
"audShoreLineRiver",
"audSimpleSound",
"audSound",
"audSoundBase",
"audSoundFactory",
"audSoundProcessHierarchyFn",
"audSoundRecorderInterface",
"audSourceSubmix",
"audSpeechAudioEntity",
"audSpeechManager",
"audSpeechSound",
"audStartOneShotAction",
"audStartTrackAction",
"audStaticRadioEmitter",
"audStopOneShotAction",
"audStopTrackAction",
"audStreamPlayer",
"audStreamingSound",
"audSwitchSound",
"audTracker",
"audTrailerAudioEntity",
"audTrainAudioEntity",
"audTwinLoopSound",
"audUnderwaterEffect",
"audVariableBlockSound",
"audVariableCurveSound",
"audVariableDelayEffect",
"audVariablePrintValueSound",
"audVehicleAudioEntity",
"audVehicleConductor",
"audVehicleConductorDynamicMixing",
"audVehicleConvertibleRoof",
"audVehicleDigger",
"audVehicleElectricEngine",
"audVehicleEngine",
"audVehicleEngineComponent",
"audVehicleForks",
"audVehicleGadget",
"audVehicleGadgetMagnet",
"audVehicleGrapplingHook",
"audVehicleHandlerFrame",
"audVehicleReflectionsEntity",
"audVehicleTowTruckArm",
"audVehicleTransmission",
"audVehicleTurbo",
"audVehicleTurret",
"audVoiceChatAudioEntity",
"audVoiceChatAudioProvider",
"audWMFStreamReader",
"audWMStreamReader",
"audWavePlayer",
"audWeaponAudioEntity",
"audWeaponInventoryListener",
"audWeatherAudioEntity",
"audWrapperSound",
"bad_alloc",
"bad_cast",
"bad_exception",
"bad_typeid",
"bgConfig",
"bgCrackStarMap",
"bgCrackType",
"bgFloat2VarData",
"bgFloat4VarData",
"bgFloatVarData",
"bgGlassSize",
"bgGlassTypeConfig",
"bgGpuBuffers_Imp",
"bgPhysics",
"bgRagePhysics",
"bgShaderVars",
"bgSimplePhysics",
"bgTextureVarData",
"btAxisSweep3",
"btOverlappingPairCache",
"cCommerceCategoryData",
"cCommerceConsumableManager",
"cCommerceConsumableTransaction",
"cCommerceItemData",
"cCommerceManager",
"cCommerceProductData",
"cCommerceSaxReader",
"cCommerceStringTable",
"cCommerceUtil",
"cSCSCommerceConsumableManager",
"cScsCommerceConsumableTransaction",
"cStoreTexture",
"cStoreTextureManager",
"camAimCamera",
"camAimCameraMetadata",
"camAnimSceneDirector",
"camAnimSceneDirectorMetadata",
"camAnimatedCamera",
"camAnimatedCameraMetadata",
"camAnimatedFrameShaker",
"camAnimatedShakeMetadata",
"camBaseCamera",
"camBaseCameraMetadata",
"camBaseCinematicContext",
"camBaseCinematicTrackingCamera",
"camBaseCinematicTrackingCameraMetadata",
"camBaseDirector",
"camBaseFrameShaker",
"camBaseObject",
"camBaseObjectMetadata",
"camBaseSplineCamera",
"camBaseSplineCameraMetadata",
"camCameraManShot",
"camCatchUpHelper",
"camCatchUpHelperMetadata",
"camCinematicAnimatedCamera",
"camCinematicAnimatedCameraMetadata",
"camCinematicBustedContext",
"camCinematicBustedContextMetadata",
"camCinematicBustedShot",
"camCinematicBustedShotMetadata",
"camCinematicCamManCamera",
"camCinematicCameraManCameraMetadata",
"camCinematicCameraManShotMetadata",
"camCinematicCameraOperatorShakeSettings",
"camCinematicCameraOperatorShakeTurbulenceSettings",
"camCinematicCameraOperatorShakeUncertaintySettings",
"camCinematicContextMetadata",
"camCinematicCraningCameraManShotMetadata",
"camCinematicDirector",
"camCinematicDirectorMetadata",
"camCinematicDirectorMetadataAssistedAimingSettings",
"camCinematicFallFromHeliContext",
"camCinematicFallFromHeliContextMetadata",
"camCinematicFallFromHeliShot",
"camCinematicFallFromHeliShotMetadata",
"camCinematicFirstPersonIdleCamera",
"camCinematicFirstPersonIdleCameraMetadata",
"camCinematicGroupCamera",
"camCinematicGroupCameraMetadata",
"camCinematicHeliChaseCamera",
"camCinematicHeliChaseCameraMetadata",
"camCinematicHeliTrackingShotMetadata",
"camCinematicIdleCamera",
"camCinematicIdleCameraMetadata",
"camCinematicIdleShots",
"camCinematicInTrainAtStationContext",
"camCinematicInTrainAtStationContextMetadata",
"camCinematicInTrainContext",
"camCinematicInTrainContextMetadata",
"camCinematicInTrainShotMetadata",
"camCinematicInVehicleContext",
"camCinematicInVehicleContextMetadata",
"camCinematicInVehicleConvertibleRoofContext",
"camCinematicInVehicleConvertibleRoofContextMetadata",
"camCinematicInVehicleCrashContext",
"camCinematicInVehicleCrashContextMetadata",
"camCinematicInVehicleCrashShot",
"camCinematicInVehicleCrashShotMetadata",
"camCinematicInVehicleFirstPersonContext",
"camCinematicInVehicleFirstPersonContextMetadata",
"camCinematicInVehicleMultiplayerPassengerContext",
"camCinematicInVehicleMultiplayerPassengerContextMetadata",
"camCinematicInVehicleOverriddenFirstPersonContext",
"camCinematicInVehicleOverriddenFirstPersonContextMetadata",
"camCinematicInVehicleWantedContext",
"camCinematicInVehicleWantedContextMetadata",
"camCinematicMeleeShotMetadata",
"camCinematicMissileKillContext",
"camCinematicMissileKillContextMetadata",
"camCinematicMissileKillShot",
"camCinematicMissileKillShotMetadata",
"camCinematicMountedCamera",
"camCinematicMountedCameraMetadata",
"camCinematicMountedCameraMetadataFirstPersonPitchOffset",
"camCinematicMountedCameraMetadataFirstPersonRoll",
"camCinematicMountedCameraMetadataLeadingLookSettings",
"camCinematicMountedCameraMetadataLookAroundSettings",
"camCinematicMountedCameraMetadataMovementOnAccelerationSettings",
"camCinematicMountedCameraMetadataOrientationSpring",
"camCinematicMountedCameraMetadataRelativePitchScalingToThrottle",
"camCinematicMountedPartCamera",
"camCinematicMountedPartCameraMetadata",
"camCinematicOnFootAssistedAimingContext",
"camCinematicOnFootAssistedAimingContextMetadata",
"camCinematicOnFootAssistedAimingKillShot",
"camCinematicOnFootAssistedAimingKillShotMetadata",
"camCinematicOnFootAssistedAimingReactionShot",
"camCinematicOnFootAssistedAimingReactionShotMetadata",
"camCinematicOnFootFirstPersonIdleShot",
"camCinematicOnFootFirstPersonIdleShotMetadata",
"camCinematicOnFootIdleContext",
"camCinematicOnFootIdleContextMetadata",
"camCinematicOnFootIdleShot",
"camCinematicOnFootIdleShotMetadata",
"camCinematicOnFootMeleeContext",
"camCinematicOnFootMeleeContextMetadata",
"camCinematicOnFootMeleeShot",
"camCinematicOnFootMeleeShotMetadata",
"camCinematicOnFootSpectatingContext",
"camCinematicOnFootSpectatingContextMetadata",
"camCinematicOnFootSpectatingShot",
"camCinematicOnFootSpectatingShotMetadata",
"camCinematicParachuteCameraManShot",
"camCinematicParachuteCameraManShotMetadata",
"camCinematicParachuteContext",
"camCinematicParachuteContextMetadata",
"camCinematicParachuteHeliShot",
"camCinematicParachuteHeliShotMetadata",
"camCinematicPassengerShot",
"camCinematicPedCloseUpCamera",
"camCinematicPedCloseUpCameraMetadata",
"camCinematicPoliceCarMountedShotMetadata",
"camCinematicPoliceExitVehicleShotMetadata",
"camCinematicPoliceHeliMountedShotMetadata",
"camCinematicPoliceInCoverShotMetadata",
"camCinematicPoliceRoadBlockShot",
"camCinematicPoliceRoadBlockShotMetadata",
"camCinematicPositionCamera",
"camCinematicPositionCameraMetadata",
"camCinematicScriptContext",
"camCinematicScriptContextMetadata",
"camCinematicScriptRaceCheckPointShot",
"camCinematicScriptRaceCheckPointShotMetadata",
"camCinematicScriptedMissionCreatorFailContext",
"camCinematicScriptedMissionCreatorFailContextMetadata",
"camCinematicScriptedRaceCheckPointContext",
"camCinematicScriptedRaceCheckPointContextMetadata",
"camCinematicShotMetadata",
"camCinematicShots",
"camCinematicSpectatorNewsChannelContext",
"camCinematicSpectatorNewsChannelContextMetadata",
"camCinematicStuntCamera",
"camCinematicStuntCameraMetadata",
"camCinematicStuntJumpContext",
"camCinematicStuntJumpContextMetadata",
"camCinematicStuntJumpShot",
"camCinematicStuntJumpShotMetadata",
"camCinematicTrainPassengerShotMetadata",
"camCinematicTrainRoofMountedShotMetadata",
"camCinematicTrainRoofShot",
"camCinematicTrainStationShot",
"camCinematicTrainStationShotMetadata",
"camCinematicTrainTrackCamera",
"camCinematicTrainTrackShot",
"camCinematicTrainTrackShotMetadata",
"camCinematicTrainTrackingCameraMetadata",
"camCinematicTwoShotCamera",
"camCinematicTwoShotCameraMetadata",
"camCinematicVehicleBonnetShot",
"camCinematicVehicleBonnetShotMetadata",
"camCinematicVehicleConvertibleRoofShot",
"camCinematicVehicleConvertibleRoofShotMetadata",
"camCinematicVehicleGroupShot",
"camCinematicVehicleGroupShotMetadata",
"camCinematicVehicleLowOrbitCamera",
"camCinematicVehicleLowOrbitCameraMetadata",
"camCinematicVehicleLowOrbitShotMetadata",
"camCinematicVehicleOrbitCamera",
"camCinematicVehicleOrbitCameraInitalSettings",
"camCinematicVehicleOrbitCameraMetadata",
"camCinematicVehicleOrbitShotMetadata",
"camCinematicVehiclePartShot",
"camCinematicVehiclePartShotMetadata",
"camCinematicVehicleTrackingCamera",
"camCinematicVehicleTrackingCameraMetadata",
"camCinematicWaterCrashCamera",
"camCinematicWaterCrashCameraMetadata",
"camCinematicWaterCrashContext",
"camCinematicWaterCrashContextMetadata",
"camCinematicWaterCrashShot",
"camCinematicWaterCrashShotMetadata",
"camCollision",
"camCollisionMetadata",
"camCollisionMetadataBuoyancySettings",
"camCollisionMetadataClippingAvoidance",
"camCollisionMetadataOcclusionSweep",
"camCollisionMetadataOrbitDistanceDamping",
"camCollisionMetadataPathFinding",
"camCollisionMetadataPullBackTowardsCollision",
"camCollisionMetadataPushBeyondEntitiesIfClipping",
"camCollisionMetadataRotationTowardsLos",
"camControlHelper",
"camControlHelperMetaDataPrecisionAimSettings",
"camControlHelperMetadata",
"camControlHelperMetadataLookAround",
"camControlHelperMetadataViewModeSettings",
"camControlHelperMetadataViewModes",
"camControlHelperMetadataZoom",
"camCraningCameraManShot",
"camCustomTimedSplineCamera",
"camCustomTimedSplineCameraMetadata",
"camCutsceneDirector",
"camCutsceneDirectorMetadata",
"camDebugDirector",
"camDebugDirectorMetadata",
"camDepthOfFieldSettingsMetadata",
"camEnvelope",
"camEnvelopeMetadata",
"camFactoryHelper",
"camFactoryHelperBase",
"camFactoryHelperPooled",
"camFirstPersonAimCamera",
"camFirstPersonAimCameraMetadata",
"camFirstPersonAimCameraMetadataHeadingCorrection",
"camFirstPersonHeadTrackingAimCamera",
"camFirstPersonHeadTrackingAimCameraMetadata",
"camFirstPersonPedAimCamera",
"camFirstPersonPedAimCameraMetadata",
"camFirstPersonShooterCamera",
"camFirstPersonShooterCameraMetadata",
"camFirstPersonShooterCameraMetadataCoverSettings",
"camFirstPersonShooterCameraMetadataOrientationSpring",
"camFirstPersonShooterCameraMetadataOrientationSpringLite",
"camFirstPersonShooterCameraMetadataRelativeAttachOrientationSettings",
"camFirstPersonShooterCameraMetadataSprintBreakOutSettings",
"camFirstPersonShooterCameraMetadataStickyAim",
"camFollowCamera",
"camFollowCameraMetadata",
"camFollowCameraMetadataFollowOrientationConing",
"camFollowCameraMetadataHighAltitudeZoomSettings",
"camFollowCameraMetadataPullAroundSettings",
"camFollowCameraMetadataRollSettings",
"camFollowObjectCamera",
"camFollowObjectCameraMetadata",
"camFollowParachuteCamera",
"camFollowParachuteCameraMetadata",
"camFollowParachuteCameraMetadataCustomSettings",
"camFollowPedCamera",
"camFollowPedCameraMetadata",
"camFollowPedCameraMetadataAssistedMovementAlignment",
"camFollowPedCameraMetadataCustomViewModeSettings",
"camFollowPedCameraMetadataDivingShakeSettings",
"camFollowPedCameraMetadataHighFallShakeSettings",
"camFollowPedCameraMetadataLadderAlignment",
"camFollowPedCameraMetadataOrbitPitchLimitsForOverheadCollision",
"camFollowPedCameraMetadataPushBeyondNearbyVehiclesInRagdollSettings",
"camFollowPedCameraMetadataRappellingAlignment",
"camFollowPedCameraMetadataRunningShakeSettings",
"camFollowPedCameraMetadataSwimmingShakeSettings",
"camFollowVehicleCamera",
"camFollowVehicleCameraMetadata",
"camFollowVehicleCameraMetadataDuckUnderOverheadCollisionSettings",
"camFollowVehicleCameraMetadataDuckUnderOverheadCollisionSettingsCapsuleSettings",
"camFollowVehicleCameraMetadataHandBrakeSwingSettings",
"camFollowVehicleCameraMetadataHighSpeedShakeSettings",
"camFollowVehicleCameraMetadataHighSpeedZoomSettings",
"camFollowVehicleCameraMetadataVerticalFlightModeSettings",
"camFollowVehicleCameraMetadataWaterEntryShakeSettings",
"camFrame",
"camFrameInterpolator",
"camFreeCameraMetadata",
"camGameplayDirector",
"camGameplayDirectorMetadata",
"camGameplayDirectorMetadataExplosionShakeSettings",
"camGameplayDirectorMetadataVehicleCustomSettings",
"camHeliTrackingShot",
"camHintHelper",
"camHintHelperMetadata",
"camHintHelperMetadataPivotPositionAdditive",
"camInconsistentBehaviourZoomHelper",
"camInconsistentBehaviourZoomHelperAirborneSettings",
"camInconsistentBehaviourZoomHelperBaseSettings",
"camInconsistentBehaviourZoomHelperDetectFastCameraTurnSettings",
"camInconsistentBehaviourZoomHelperDetectSuddenMovementSettings",
"camInconsistentBehaviourZoomHelperLosSettings",
"camInconsistentBehaviourZoomHelperMetadata",
"camLongSwoopSwitchHelper",
"camLongSwoopSwitchHelperMetadata",
"camLookAheadHelper",
"camLookAheadHelperMetadata",
"camLookAtDampingHelper",
"camLookAtDampingHelperMetadata",
"camMarketingAToBCameraMetadata",
"camMarketingDirector",
"camMarketingDirectorMetadata",
"camMarketingDirectorMetadataMode",
"camMarketingFreeCameraMetadata",
"camMarketingFreeCameraMetadataInputResponse",
"camMarketingMountedCameraMetadata",
"camMarketingOrbitCameraMetadata",
"camMarketingStickyCameraMetadata",
"camMetadataStore",
"camMotionBlurSettingsMetadata",
"camNearClipScanner",
"camNearClipScannerMetadata",
"camOscillatingFrameShaker",
"camOscillator",
"camOscillatorMetadata",
"camPoliceCarMountedShot",
"camPoliceExitVehicleShot",
"camPoliceHeliMountedShot",
"camPoliceInCoverShot",
"camPreferredShotSelectionType",
"camReplayBaseCamera",
"camReplayBaseCameraMetadata",
"camReplayBaseCameraMetadataCollisionSettings",
"camReplayBaseCameraMetadataInputResponse",
"camReplayDirector",
"camReplayDirectorMetadata",
"camReplayFreeCamera",
"camReplayFreeCameraMetadata",
"camReplayPresetCamera",
"camReplayPresetCameraMetadata",
"camReplayRecordedCamera",
"camReplayRecordedCameraMetadata",
"camRoundedSplineCamera",
"camRoundedSplineCameraMetadata",
"camScriptDirector",
"camScriptDirectorMetadata",
"camScriptedCamera",
"camScriptedCameraMetadata",
"camScriptedFlyCamera",
"camScriptedFlyCameraMetadata",
"camScriptedFlyCameraMetadataInputResponse",
"camSeatSpecificCameras",
"camShakeMetadata",
"camShakeMetadataFrameComponent",
"camShortRotationSwitchHelper",
"camShortRotationSwitchHelperMetadata",
"camShortTranslationSwitchHelper",
"camShortTranslationSwitchHelperMetadata",
"camShortZoomInOutSwitchHelper",
"camShortZoomInOutSwitchHelperMetadata",
"camShortZoomToHeadSwitchHelper",
"camShortZoomToHeadSwitchHelperMetadata",
"camSmoothedSplineCamera",
"camSmoothedSplineCameraMetadata",
"camSpeedRelativeShakeSettingsMetadata",
"camSpringMount",
"camSpringMountMetadata",
"camSwitchCamera",
"camSwitchCameraMetadata",
"camSwitchDirector",
"camSwitchDirectorMetadata",
"camSyncedSceneDirector",
"camSyncedSceneDirectorMetadata",
"camThirdPersonAimCamera",
"camThirdPersonAimCameraMetadata",
"camThirdPersonCamera",
"camThirdPersonCameraMetadata",
"camThirdPersonCameraMetadataBasePivotPosition",
"camThirdPersonCameraMetadataBasePivotPositionRollSettings",
"camThirdPersonCameraMetadataBuoyancySettings",
"camThirdPersonCameraMetadataCollisionFallBackPosition",
"camThirdPersonCameraMetadataCustomBoundingBoxSettings",
"camThirdPersonCameraMetadataLookOverSettings",
"camThirdPersonCameraMetadataPivotOverBoungingBoxSettings",
"camThirdPersonCameraMetadataPivotPosition",
"camThirdPersonCameraMetadataQuadrupedalHeightSpring",
"camThirdPersonCameraMetadataStealthZoomSettings",
"camThirdPersonCameraMetadataVehicleOnTopOfVehicleCollisionSettings",
"camThirdPersonFrameInterpolator",
"camThirdPersonPedAimCamera",
"camThirdPersonPedAimCameraMetadata",
"camThirdPersonPedAimCameraMetadataLockOnOrbitDistanceSettings",
"camThirdPersonPedAimCameraMetadataLockOnTargetDampingSettings",
"camThirdPersonPedAimInCoverCamera",
"camThirdPersonPedAimInCoverCameraMetadata",
"camThirdPersonPedAimInCoverCameraMetadataAimingSettings",
"camThirdPersonPedAimInCoverCameraMetadataLowCoverSettings",
"camThirdPersonPedAssistedAimCamera",
"camThirdPersonPedAssistedAimCameraCinematicMomentSettings",
"camThirdPersonPedAssistedAimCameraInCoverSettings",
"camThirdPersonPedAssistedAimCameraLockOnAlignmentSettings",
"camThirdPersonPedAssistedAimCameraMetadata",
"camThirdPersonPedAssistedAimCameraPivotScalingSettings",
"camThirdPersonPedAssistedAimCameraPlayerFramingSettings",
"camThirdPersonPedAssistedAimCameraRecoilShakeScalingSettings",
"camThirdPersonPedAssistedAimCameraRunningShakeSettings",
"camThirdPersonPedAssistedAimCameraShakeActivityScalingSettings",
"camThirdPersonPedAssistedAimCameraShootingFocusSettings",
"camThirdPersonPedMeleeAimCamera",
"camThirdPersonPedMeleeAimCameraMetadata",
"camThirdPersonVehicleAimCamera",
"camThirdPersonVehicleAimCameraMetadata",
"camTimedSplineCamera",
"camTimedSplineCameraMetadata",
"camVehicleCustomSettingsMetadata",
"camVehicleCustomSettingsMetadataAdditionalBoundScalingVehicleSettings",
"camVehicleCustomSettingsMetadataDoorAlignmentSettings",
"camVehicleCustomSettingsMetadataExitSeatPhaseForCameraExitSettings",
"camVehicleCustomSettingsMetadataInvalidCinematcShotsRefsForVehicleSettings",
"camVehicleCustomSettingsMetadataMultiplayerPassengerCameraHashSettings",
"camVehicleCustomSettingsMetadataSeatSpecficCameras",
"camVehicleLowOrbitShot",
"camVehicleOrbitShot",
"charNode",
"characterCloth",
"characterClothController",
"characterClothDebug",
"clothBridgeSimGfx",
"clothController",
"clothInstance",
"clothInstanceTaskManager",
"clothInstanceTuning",
"clothManager",
"clothVertexBlend",
"crAnimDictionary",
"crAnimation",
"crClip",
"crClipAnimation",
"crClipAnimationExpression",
"crClipAnimations",
"crClipDictionary",
"crCreatureComponent",
"crCreatureComponentCamera",
"crCreatureComponentEvent",
"crCreatureComponentExtraDofs",
"crCreatureComponentLight",
"crCreatureComponentMover",
"crCreatureComponentParticleEffect",
"crCreatureComponentPhysical",
"crCreatureComponentRoot",
"crCreatureComponentShaderVars",
"crCreatureComponentSkeleton",
"crCreatureComponentTelemetry",
"crExpressions",
"crFrameBuffer",
"crFrameBufferFrameData",
"crFrameCompositor",
"crFrameCompositorFrame",
"crFrameDataInitializer",
"crFrameDataInitializerAnimation",
"crFrameDataInitializerBoneAndMover",
"crFrameDataInitializerCreature",
"crFrameDataInitializerDofs",
"crFrameDataInitializerFrameData",
"crFrameDataInitializerMover",
"crFrameFilter",
"crFrameFilterBone",
"crFrameFilterBoneBasic",
"crFrameFilterBoneMultiWeight",
"crFrameFilterDictionaryStore",
"crFrameFilterMover",
"crFrameFilterMultiWeight",
"crFrameFilterTrackMultiWeight",
"crIKSolverArms",
"crIKSolverBase",
"crIKSolverLegs",
"crJointData",
"crProperty",
"crPropertyAttribute",
"crPropertyAttributeBitSet",
"crPropertyAttributeBool",
"crPropertyAttributeData",
"crPropertyAttributeFloat",
"crPropertyAttributeHashString",
"crPropertyAttributeInt",
"crPropertyAttributeMatrix34",
"crPropertyAttributeQuaternion",
"crPropertyAttributeSituation",
"crPropertyAttributeString",
"crPropertyAttributeVector3",
"crPropertyAttributeVector4",
"crSkeletonData",
"crTag",
"crWeightModifier",
"crWeightModifierSlowInSlowOut",
"crWeightSet",
"crmtDestructor",
"crmtIterator",
"crmtNode",
"crmtNodeAddN",
"crmtNodeAddSubtract",
"crmtNodeAnimation",
"crmtNodeBlend",
"crmtNodeBlendN",
"crmtNodeCapture",
"crmtNodeClip",
"crmtNodeExpression",
"crmtNodeExtrapolate",
"crmtNodeFilter",
"crmtNodeFrame",
"crmtNodeIdentity",
"crmtNodeIk",
"crmtNodeInvalid",
"crmtNodeJointLimit",
"crmtNodeMerge",
"crmtNodeMergeN",
"crmtNodeMirror",
"crmtNodeN",
"crmtNodePair",
"crmtNodePairWeighted",
"crmtNodeParent",
"crmtNodePm",
"crmtNodePose",
"crmtNodeProxy",
"crmtObserver",
"crmtObserverTyped",
"crmtRefreshIterator",
"crmtRequest",
"crmtRequestBlend",
"crmtRequestCapture",
"crmtRequestClip",
"crmtRequestExpression",
"crmtRequestFilter",
"crmtRequestFrame",
"crmtRequestIk",
"crmtRequestMerge",
"crmtRequestPose",
"crmtRequestSource",
"crmtSynchronizer",
"crmtSynchronizerPhase",
"crmtSynchronizerTag",
"crpmAngle",
"crpmAngleRadians",
"crpmMotionController",
"crpmMotionControllerClip",
"crpmMotionControllerRc",
"crpmParameterSampler",
"crpmParameterSamplerClipName",
"crpmParameterSamplerClipProperty",
"crpmParameterSamplerCombiner",
"crpmParameterSamplerContainer",
"crpmParameterSamplerDirectional",
"crpmParameterSamplerLocomotion",
"crpmParameterSamplerPhysical",
"crpmParameterSamplerSpatial",
"cutfAnimatedLightObject",
"cutfAnimatedParticleEffectObject",
"cutfAnimationManagerObject",
"cutfAssetManagerObject",
"cutfAttachmentEventArgs",
"cutfAttribute",
"cutfAttributeBool",
"cutfAttributeFloat",
"cutfAttributeInt",
"cutfAttributeString",
"cutfAudioObject",
"cutfBlockingBoundsObject",
"cutfBoolValueEventArgs",
"cutfCameraCutCharacterLightParams",
"cutfCameraCutEventArgs",
"cutfCameraCutTimeOfDayDofModifier",
"cutfCameraObject",
"cutfCascadeShadowEventArgs",
"cutfCutsceneFile2",
"cutfDecalEventArgs",
"cutfDecalObject",
"cutfEvent",
"cutfEventArgs",
"cutfEventArgsDef",
"cutfEventArgsList",
"cutfEventDef",
"cutfEventDefs",
"cutfEventObject",
"cutfFinalNameEventArgs",
"cutfFinalNamedObject",
"cutfFindModelObject",
"cutfFixupModelObject",
"cutfFloatBoolValueEventArgs",
"cutfFloatValueEventArgs",
"cutfHiddenModelObject",
"cutfLightObject",
"cutfLoadSceneEventArgs",
"cutfModelObject",
"cutfNameEventArgs",
"cutfNamedAnimatedObject",
"cutfNamedAnimatedStreamedObject",
"cutfNamedObject",
"cutfNamedStreamedObject",
"cutfObject",
"cutfObjectIdEvent",
"cutfObjectIdEventArgs",
"cutfObjectIdListEvent",
"cutfObjectIdListEventArgs",
"cutfObjectIdNameEventArgs",
"cutfObjectIdPartialHashEventArgs",
"cutfObjectVariationEventArgs",
"cutfOverlayObject",
"cutfParticleEffectObject",
"cutfPedModelObject",
"cutfPlayParticleEffectEventArgs",
"cutfPropModelObject",
"cutfRayfireObject",
"cutfRemovalBoundsObject",
"cutfScreenFadeEventArgs",
"cutfScreenFadeObject",
"cutfSubtitleEventArgs",
"cutfSubtitleObject",
"cutfTriggerLightEffectEventArgs",
"cutfTwoFloatValuesEventArgs",
"cutfVehicleExtraEventArgs",
"cutfVehicleModelObject",
"cutfVehicleVariationEventArgs",
"cutfWeaponModelObject",
"cutsAnimationEventArgs",
"cutsAnimationManagerEntity",
"cutsClipEventArgs",
"cutsDictionaryLoadedEventArgs",
"cutsDualAnimationEventArgs",
"cutsDualClipAnimEventArgs",
"cutsDualClipEventArgs",
"cutsEntity",
"cutsManager",
"cutsUniqueEntity",
"cutsUpdateEventArgs",
"datBase",
"datSaxReader",
"datSerialize",
"decalCallbacks",
"decalShader",
"decalShader_Dynamic",
"decalShader_Static",
"dlDrawCommandBuffer",
"dlDrawListMgr",
"environmentCloth",
"error_category",
"evtInstance",
"evtSet",
"exception",
"fiAsciiTokenizer",
"fiBaseTokenizer",
"fiCachedDevice",
"fiDevice",
"fiDeviceCloud",
"fiDeviceCount",
"fiDeviceCrc",
"fiDeviceGrowBuffer",
"fiDeviceLocal",
"fiDeviceMemory",
"fiDeviceRelative",
"fiDeviceReplay",
"fiDeviceTcpIp",
"fiEncryptingDevice",
"fiPackfile",
"fiSerialize",
"fiTokenizer",
"fragAnimEventInstance",
"fragCacheAllocator",
"fragCacheManager",
"fragDamageEventInstance",
"fragDrawable",
"fragInst",
"fragInstGta",
"fragInstNM",
"fragInstNMGta",
"fragManager",
"fragPaneFrameBrokenEventInstance",
"fragPhysicsLOD",
"fragPhysicsLODGroup",
"fragSnuffEventInstance",
"fragTuneBreakPreset",
"fragTuneStruct",
"fragType",
"fragTypeChild",
"fwAltSkeletonExtension",
"fwAnimDirector",
"fwAnimDirectorComponent",
"fwAnimDirectorComponentCreature",
"fwAnimDirectorComponentCreaturePooledObject",
"fwAnimDirectorComponentExpressions",
"fwAnimDirectorComponentExpressionsPooledObject",
"fwAnimDirectorComponentFacialRig",
"fwAnimDirectorComponentFacialRigPooledObject",
"fwAnimDirectorComponentMotionTree",
"fwAnimDirectorComponentMotionTreePooledObject",
"fwAnimDirectorComponentMove",
"fwAnimDirectorComponentMovePooledObject",
"fwAnimDirectorComponentRagDoll",
"fwAnimDirectorComponentRagDollPooledObject",
"fwAnimDirectorComponentSyncedScene",
"fwAnimDirectorComponentSyncedScenePooledObject",
"fwAnimDirectorPooledObject",
"fwAnimManager",
"fwArchetype",
"fwArchetypeDef",
"fwArchetypeDynamicFactory",
"fwArchetypeExtensionFactory",
"fwArchetypeStreamingModule",
"fwAssetRscStore",
"fwAssetStore",
"fwAudioAnimHandlerInterface",
"fwAudioEntity",
"fwBoxStreamer",
"fwBoxStreamerInterfaceNew",
"fwBoxStreamerVariable",
"fwCameraRelativeExtension",
"fwClipDictionaryBuildMetadata",
"fwClipDictionaryStore",
"fwClipDictionaryStoreGameInterface",
"fwClipItem",
"fwClipItemWithProps",
"fwClipRpfBuildMetadata",
"fwClipSet",
"fwClipSetWithGetup",
"fwClothCollisionsExtension",
"fwClothStore",
"fwConfig",
"fwConfigManager",
"fwCustomShaderEffect",
"fwCustomShaderEffectBaseType",
"fwDecorator",
"fwDecoratorExtension",
"fwDecoratorInterface",
"fwDirectedClipItemWithProps",
"fwDrawData",
"fwDrawableStore",
"fwDwdStore",
"fwEntity",
"fwEntityDef",
"fwEvent",
"fwEventQueue",
"fwExtensibleBase",
"fwExtension",
"fwExtensionDef",
"fwExtensionPoolFactory",
"fwExtraTextFiles",
"fwExtraTextPackage",
"fwFactoryBase",
"fwFragmentStore",
"fwFsm",
"fwGameInterface",
"fwIBAllocator",
"fwInstancedMapData",
"fwMapData",
"fwMapDataContents",
"fwMapDataStore",
"fwMapTypes",
"fwMapTypesContents",
"fwMapTypesStore",
"fwMetaDataStore",
"fwMove",
"fwMoveNetworkDefs",
"fwMoveNetworkInterface",
"fwMoveNetworkPlayer",
"fwNavMeshStoreInterface",
"fwNetworkDefStore",
"fwPathServerGameInterface",
"fwPathServerThread",
"fwPedGenNavMeshIterator",
"fwPoolInstanceBuffer",
"fwPoseMatcherStore",
"fwQuadTreeFn",
"fwRecoilEffect",
"fwRefAwareBaseImpl",
"fwRenderPhase",
"fwRenderThreadGameInterface",
"fwRenderThreadInterface",
"fwSceneInterface",
"fwSceneUpdateExtension",
"fwScriptGuid",
"fwStaticBoundsStore",
"fwStaticBoundsStoreInterface",
"fwSyncedSceneAudioInterface",
"fwTexLodInterface",
"fwTextAsset",
"fwTextDatabaseEntry",
"fwTextDatabaseSource",
"fwTextField",
"fwTextStore",
"fwTxdStore",
"fwWantedLightEffect",
"fwWaypointRecordingRoute",
"fwWorldRepBase",
"fwWorldRepMulti",
"gameSkeleton",
"grcAdapter",
"grcAdapterD3D11",
"grcAdapterManager",
"grcAdapterManagerD3D11",
"grcBufferEditor",
"grcBufferedStaticInstanceBufferList",
"grcComputeProgram",
"grcDomainProgram",
"grcFragmentProgram",
"grcGeometryProgram",
"grcHullProgram",
"grcIndexBuffer",
"grcIndexBufferD3D11",
"grcInstanceBuffer",
"grcInstanceBufferBasic",
"grcInstanceBufferList",
"grcMaterialLibrary",
"grcProgram",
"grcRenderTarget",
"grcRenderTargetDX11",
"grcRenderTargetPC",
"grcResourceCache",
"grcSetup",
"grcStaticInstanceBufferList",
"grcTexture",
"grcTextureDX11",
"grcTextureFactory",
"grcTextureFactoryDX11",
"grcTextureFactoryPC",
"grcTexturePC",
"grcTextureReference",
"grcTextureReferenceBase",
"grcVec4BufferInstanceBufferList",
"grcVertexBuffer",
"grcVertexBufferD3D11",
"grcVertexBufferEditor",
"grcVertexProgram",
"grmGeometry",
"grmGeometryQB",
"grmModel",
"grmModelFactory",
"grmShaderFactory",
"grmShaderFactoryStandard",
"grmShaderGroup",
"gtaDrawable",
"gtaFragType",
"iReplayInterface",
"ioActuatorEffect",
"ioAcutatorDevice",
"ioConstantLightEffect",
"ioLightDevice",
"ioLightEffect",
"ioLogitechLedDevice",
"ioPad",
"ioPadActuatorDevice",
"ioRumbleEffect",
"length_error",
"logic_error",
"miniheapAllocator",
"mvActiveTransition",
"mvExitParameter",
"mvMotionWeb",
"mvNetwork",
"mvNetworkDef",
"mvNodeState",
"mvNodeStateMachine",
"mvNodeStateRoot",
"mvNodeSubNetwork",
"mvObserver",
"mvRequestStateRoot",
"mvSetFromParameterBuffer",
"mvSubNetwork",
"mvSyncObserver",
"mvSyncTagObserver",
"mvSynchronizerPhaseAdd",
"mvSynchronizerPhaseBlend",
"mvSynchronizerPhaseMerge",
"mvSynchronizerPhaseNway",
"mvSynchronizerPhaseNwayAdd",
"mvSynchronizerTag",
"mvSynchronizerTagAdd",
"mvSynchronizerTagBlend",
"mvSynchronizerTagMerge",
"mvSynchronizerTagNway",
"mvSynchronizerTagNwayAdd",
"mvTransitionFrameFilter",
"mvUpdateEventFromClipTag",
"mvUpdateParameterBuffer",
"mvUpdateParameterFromEvent",
"mvUpdateParameterFromOperation",
"mvUpdateProxyParameter",
"mvWeightModifierSlowInSlowOut",
"naAnimHandler",
"naAudioEntity",
"naEnvironmentGroup",
"naEnvironmentGroupManager",
"naOcclusionPortalInfo",
"netArrayHandler",
"netArrayHandlerBase",
"netArrayIdentifierBase",
"netArrayManager",
"netBandwidthMgr",
"netBlender",
"netBlenderLinInterp",
"netBroadcastDataArrayIdentifier",
"netCatalog",
"netCatalogBaseItem",
"netCatalogCache",
"netCatalogGeneralItem",
"netCatalogInventoryItem",
"netCatalogOnlyItem",
"netCatalogOnlyItemWithStat",
"netCatalogPackedStatInventoryItem",
"netCatalogServiceItem",
"netCatalogServiceLimitedItem",
"netCatalogServiceWithThresholdItem",
"netCloudRequestDeleteMemberFile",
"netCloudRequestGetCrewFile",
"netCloudRequestGetFile",
"netCloudRequestGetGlobalFile",
"netCloudRequestGetMemberFile",
"netCloudRequestGetTitleFile",
"netCloudRequestGetUgcFile",
"netCloudRequestHelper",
"netCloudRequestPostMemberFile",
"netConnection",
"netConnectionManager",
"netDefferedGamePresenceEventIntf",
"netEventAckReceived",
"netEventBandwidthExceeded",
"netEventBase",
"netEventConnectionClosed",
"netEventConnectionError",
"netEventConnectionEstablished",
"netEventConnectionReassigned",
"netEventConnectionRequested",
"netEventFrameReceived",
"netEventOutOfMemory",
"netFireAndForgetTask",
"netGameEvent",
"netGameObjectBase",
"netGameObjectWrapper",
"netGamePresenceEvent",
"netHostBroadcastDataHandlerBase",
"netHttpFilter",
"netINodeDataAccessor",
"netInventoryBaseItem",
"netInventoryPackedStatsItem",
"netLeaderboardRead",
"netLeaderboardReadByGamerByPlatformTask",
"netLeaderboardReadClanMembersByRadius",
"netLeaderboardReadClanMembersByRank",
"netLeaderboardReadClanMembersByRow",
"netLeaderboardReadClanMembersByScoreFloat",
"netLeaderboardReadClanMembersByScoreInt",
"netLeaderboardReadClansByRadius",
"netLeaderboardReadClansByRank",
"netLeaderboardReadClansByRow",
"netLeaderboardReadClansByScoreFloat",
"netLeaderboardReadClansByScoreInt",
"netLeaderboardReadGamersByRadius",
"netLeaderboardReadGamersByRank",
"netLeaderboardReadGamersByRow",
"netLeaderboardReadGamersByScoreFloat",
"netLeaderboardReadGamersByScoreInt",
"netLeaderboardReadGamersInGroupsByRank",
"netLeaderboardReadGamersInGroupsByScoreFloat",
"netLeaderboardReadGamersInGroupsByScoreInt",
"netLeaderboardReadGroupMembersByRadius",
"netLeaderboardReadGroupMembersByRow",
"netLeaderboardReadGroupsByRadius",
"netLeaderboardReadGroupsByRank",
"netLeaderboardReadGroupsByRow",
"netLeaderboardReadMgr",
"netLeaderboardWriteMgr",
"netLoadCloudFileTask",
"netLoadCloudFileWorkItem",
"netLocalizedStringInCloudRequest",
"netLogFileAccessInterface",
"netLogSplitter",
"netLogStub",
"netLoggingInterface",
"netObject",
"netObjectMgrBase",
"netPlayer",
"netPlayerBroadcastDataHandlerBase_Local",
"netPlayerMgrBase",
"netRelayEventAddressChanged",
"netRelayEventAddressObtained",
"netRelayEventMessageReceived",
"netRelayEventPacketReceived",
"netSCGamerDataMgr",
"netScriptBroadcastDataHandlerBase",
"netSharedArrayHandler",
"netSharedArrayHandlerWithElementScope",
"netSyncDataBase",
"netSyncDataNode",
"netSyncDataUnitBase",
"netSyncDataUnit_Dynamic",
"netSyncDataUnit_Static",
"netSyncData_Dynamic",
"netSyncData_Static",
"netSyncData_Static_NoBuffers",
"netSyncNodeBase",
"netSyncParentNode",
"netSyncTree",
"netSyncTreeTargetObject",
"netTask",
"netTaskBase",
"netTimeSync",
"netVSocket",
"nonPhysicalPlayerDataBase",
"objectIdFreedEvent",
"objectIdRequestEvent",
"out_of_range",
"pDNameNode",
"pairNode",
"parBuildTreeVisitor",
"parDeepCopyVisitor",
"parDeleteAllVisitor",
"parFindTotalSizeVisitor",
"parHasherVisitor",
"parHasherVisitorHT",
"parInitVisitor",
"parInstanceVisitor",
"parMember",
"parMemberArray",
"parMemberAtBinaryMapGenericInterface",
"parMemberAtBinaryMapGenericIterator",
"parMemberBitset",
"parMemberEnum",
"parMemberMap",
"parMemberMapInterface",
"parMemberMapInterface_atMap",
"parMemberMapIterator",
"parMemberMapIterator_atMap",
"parMemberMatrix",
"parMemberPlacementVisitor",
"parMemberSimple",
"parMemberString",
"parMemberStruct",
"parMemberVector",
"parStream",
"parStreamIn",
"parStreamInRbf",
"parStreamInXml",
"parStreamOutRbf",
"parStreamOutXml",
"parStructure",
"pcharNode",
"pgArray",
"pgBase",
"pgBaseMetaDataDebugNameType",
"pgBaseMetaDataType",
"pgBasePlatformNeutral",
"pgDictionary",
"pgRawStreamer",
"phArchetype",
"phArchetypeDamp",
"phArchetypePhys",
"phArticulatedBodyType",
"phArticulatedCollider",
"phBound",
"phBoundBVH",
"phBoundBase",
"phBoundBox",
"phBoundCapsule",
"phBoundComposite",
"phBoundCylinder",
"phBoundDisc",
"phBoundGeometry",
"phBoundPlane",
"phBoundPolyhedron",
"phBoundSphere",
"phBroadPhase",
"phClothData",
"phClothDataDebug",
"phClothVerletBehavior",
"phCollider",
"phConstraintAttachment",
"phConstraintBase",
"phConstraintCylindrical",
"phConstraintDistance",
"phConstraintFixed",
"phConstraintFixedRotation",
"phConstraintHalfSpace",
"phConstraintHinge",
"phConstraintPrismatic",
"phConstraintRotation",
"phConstraintSpherical",
"phContactMgr",
"phEnvClothVerletBehavior",
"phGlassInst",
"phInst",
"phInstBehavior",
"phInstBehaviorExplosionGta",
"phInstBreakable",
"phInstGta",
"phJoint",
"phJoint1Dof",
"phJoint1DofType",
"phJoint3Dof",
"phJoint3DofType",
"phJointType",
"phLevelBase",
"phLevelNodeTree",
"phMaterial",
"phMaterialGta",
"phMaterialMgr",
"phMaterialMgrGta",
"phMorphController",
"phPrismaticJoint",
"phPrismaticJointType",
"phSimulator",
"phVerletCloth",
"phVerletClothCustomBounds",
"phWind",
"phWindDirExplosion",
"phWindDirExplosionGroup",
"phWindDisturbanceBase",
"phWindDisturbanceGroupBase",
"phWindDownwash",
"phWindDownwashGroup",
"phWindExplosion",
"phWindExplosionGroup",
"phWindHemisphere",
"phWindHemisphereGroup",
"phWindSphere",
"phWindSphereGroup",
"phWindThermal",
"phWindThermalGroup",
"phfwFragInst",
"phfwInst",
"playerDataMsg",
"psoResourceData",
"ptfxAssetStore",
"ptfxCallbacks",
"ptfxScriptInfo",
"ptxBehaviour",
"ptxDomain",
"ptxDomainAttractor",
"ptxDomainBox",
"ptxDomainCylinder",
"ptxDomainSphere",
"ptxEffectRule",
"ptxEffectSpawner",
"ptxEmitterRule",
"ptxEvent",
"ptxEventEmitter",
"ptxFxList",
"ptxKeyframeProp",
"ptxManager",
"ptxMultipleDrawInterface",
"ptxParticleRule",
"ptxRenderSetup",
"ptxShaderInst",
"ptxShaderTemplate",
"ptxShaderVar",
"ptxShaderVarKeyframe",
"ptxShaderVarTexture",
"ptxShaderVarVector",
"ptxTechniqueDesc",
"ptxTimeLine",
"ptxd_Model",
"ptxd_Sprite",
"ptxd_Trail",
"ptxgpuBase",
"ptxgpuDrop",
"ptxgpuDropEmitterSettings",
"ptxgpuDropRenderSettings",
"ptxgpuDropRenderShader",
"ptxgpuDropUpdateShader",
"ptxgpuRenderBaseShader",
"ptxgpuShader",
"ptxu_Acceleration",
"ptxu_Age",
"ptxu_AnimateTexture",
"ptxu_Attractor",
"ptxu_Collision",
"ptxu_Colour",
"ptxu_Dampening",
"ptxu_Decal",
"ptxu_DecalPool",
"ptxu_FogVolume",
"ptxu_Light",
"ptxu_Liquid",
"ptxu_MatrixWeight",
"ptxu_Noise",
"ptxu_Pause",
"ptxu_River",
"ptxu_Rotation",
"ptxu_Size",
"ptxu_Velocity",
"ptxu_Wind",
"ptxu_ZCull",
"queryFunctions",
"replayGlassManager",
"replaySoundRecorder",
"rlAchievementInfo",
"rlCancelSaveMigration",
"rlCashTransactionPackUSDEInfoTableRequestTask",
"rlClanEvent",
"rlClanEventFriendFounded",
"rlClanEventFriendJoined",
"rlClanEventInviteRecieved",
"rlClanEventJoined",
"rlClanEventKicked",
"rlClanEventLeft",
"rlClanEventMemberRankChange",
"rlClanEventMetadataChanged",
"rlClanEventNotifyDescChanged",
"rlClanEventNotifyJoinRequest",
"rlClanEventPrimaryClanChanged",
"rlClanEventSynchedMemberships",
"rlClanRosBaseTask",
"rlClanRosDeleteInviteTask",
"rlClanRosDeleteJoinRequestTask",
"rlClanRosGetAllTask",
"rlClanRosGetDescTask",
"rlClanRosGetDescsTask",
"rlClanRosGetInvitesTask",
"rlClanRosGetInvitesTaskBase",
"rlClanRosGetMembersTask",
"rlClanRosGetMembershipForTask",
"rlClanRosGetMembershipTaskBase",
"rlClanRosGetMetadataForClanTask",
"rlClanRosGetMineTask",
"rlClanRosGetPrimaryClansTask",
"rlClanRosGetRecievedJoinRequestsTask",
"rlClanRosInviteTask",
"rlClanRosJoinRequestTask",
"rlClanRosJoinTask",
"rlClanRosLeaveTask",
"rlClanRosRefreshMineTask",
"rlClanRosSetPrimaryTask",
"rlCloudDeleteFileTask",
"rlCloudGetFileTask",
"rlCloudPostFileTask",
"rlCommunityStatHistoryFixedResult",
"rlCommunityStatHistoryFixedRow",
"rlCommunityStatHistoryResultBase",
"rlCommunityStatHistoryRowBase",
"rlCommunityStatsReadStatHistorySetTask",
"rlCommunityStatsReadStatsTask",
"rlComputeHMACTask",
"rlConsumeContentUnlock",
"rlFacebookGetAppInfoAndPlatformAccesTokenTask",
"rlFindGamerSessionsTask",
"rlFindGamersTask",
"rlFindSessionsTask",
"rlFireAndForgetTask",
"rlFriendEvent",
"rlFriendEventListChanged",
"rlFriendsManagerReadyEvent",
"rlGetAvailableSavesToMigrate",
"rlGetGamerStateTask",
"rlHttpLlnwUploadPhotoTask",
"rlHttpTask",
"rlInboxDeleteMessagesTask",
"rlInboxGetMessagesTask",
"rlInboxPostMessageTask",
"rlInboxPublishMessageTask",
"rlInboxPublishToManyFriendsTask",
"rlLeaderboard2CommonReadTask",
"rlLeaderboard2ReadByClanTask",
"rlLeaderboard2ReadByGamerByPlatformTask",
"rlLeaderboard2ReadByGamerTask",
"rlLeaderboard2ReadByGroupTask",
"rlLeaderboard2ReadByRankTask",
"rlLeaderboard2ReadClansByRadiusTask",
"rlLeaderboard2ReadClansByScoreTask",
"rlLeaderboard2ReadGamersByRadiusTask",
"rlLeaderboard2ReadGamersByScoreTask",
"rlLeaderboard2ReadGroupsByRadiusTask",
"rlLeaderboard2WriteTask",
"rlMetric",
"rlMigrateSaveByRockstarAccount",
"rlMigrateSaveStatus",
"rlPcEvent",
"rlPcEventFriendStatusChanged",
"rlPcEventGameInviteAccepted",
"rlPcEventJoinedViaPresence",
"rlPcEventRosTicketChanged",
"rlPcEventSignInStateChanged",
"rlPcEventSocialClubMessage",
"rlPcPipeCreateSignInTransferDataWorkItem",
"rlPcPipeSignInMessage",
"rlPcPipeSignOutMessage",
"rlPcPipeSigninTask",
"rlPcPipeTicketChangedMessage",
"rlPcPipeWorkItem",
"rlPresenceEvent",
"rlPresenceEventInviteAccepted",
"rlPresenceEventJoinedViaPresence",
"rlPresenceEventNetworkStatusChanged",
"rlPresenceEventProfileChanged",
"rlPresenceEventScMessage",
"rlPresenceEventSigninStatusChanged",
"rlPresencePublishToManyFriendsTask",
"rlProfileStatsConditionalSynchronizeGroupsTask",
"rlProfileStatsDirtyIterator",
"rlProfileStatsEvent",
"rlProfileStatsFixedRecord",
"rlProfileStatsFlushStatEvent",
"rlProfileStatsFlushTask",
"rlProfileStatsRecordBase",
"rlProfileStatsResetByGroupTask",
"rlProfileStatsSynchronizeGroupEvent",
"rlProfileStatsSynchronizeStatEvent",
"rlProfileStatsWriteStatsTask",
"rlRosConductorRouteTask",
"rlRosCreateTicketTask",
"rlRosCredentialsChangingTask",
"rlRosEntitlementLoadOfflineTask",
"rlRosEvent",
"rlRosEventCanReadAchievements",
"rlRosEventGetCredentialsResult",
"rlRosEventLinkChanged",
"rlRosEventOnlineStatusChanged",
"rlRosEventRetrievedGeoLocInfo",
"rlRosFacebookGetAppInfoTask",
"rlRosFacebookHasAppPermissionsTask",
"rlRosFacebookOpenGraphTask",
"rlRosFinaliseSteamPurchase",
"rlRosGetEntitlementBlock",
"rlRosGetEntitlements",
"rlRosGetGeolocationInfoTask",
"rlRosGetYoutubeAccessTokenTask",
"rlRosHttpFilter",
"rlRosHttpTask",
"rlRosSaxReader",
"rlRosTelemetryImmediateSubmissionTask",
"rlRosTelemetrySubmissionTask",
"rlRosWriteStatsTask",
"rlScAchievementsAwardAchievementTask",
"rlScAchievementsGetAchievementsTask",
"rlScAddLicensePlateRequestTask",
"rlScChangeNoInsertLicensePlateInfoRequestTask",
"rlScCheckEmailTask",
"rlScCheckNicknameTask",
"rlScCheckPasswordTask",
"rlScCheckTextTask",
"rlScCreateAccountTask",
"rlScGetAlternateNicknamesTask",
"rlScGetLicensePlateInfoRequestTask",
"rlScGetScAuthTokenTask",
"rlScIsValidLicensePlateRequestTask",
"rlScMatchmakingAdvertiseTask",
"rlScMatchmakingFindTask",
"rlScMatchmakingUnadvertiseAllTask",
"rlScMatchmakingUnadvertiseTask",
"rlScMatchmakingUpdateTask",
"rlScRegisterComplaintTask",
"rlScRequestResetPasswordTask",
"rlScSignOutTask",
"rlScTaskBase",
"rlSession",
"rlSessionByGamerHandleTask",
"rlSessionFindSocialTask",
"rlSessionQueryDetailTask",
"rlSessionQueryHostDetailTask",
"rlSteamVerifyOwnershipTask",
"rlTaskBase",
"rlTelemetryHeader",
"rlUgcCheckTextTask",
"rlUgcCopyContentTask",
"rlUgcCreateContentTask",
"rlUgcPublishTask",
"rlUgcQueryContentCreatorsTask",
"rlUgcQueryContentTask",
"rlUgcSetBookmarkedTask",
"rlUgcSetDeletedTask",
"rlUgcSetPlayerDataTask",
"rlUgcTaskBase",
"rlUgcUpdateContentTask",
"rlUnlocksRosReadTask",
"rlWorker",
"rlYoutubeDataApiTask",
"rlYoutubeGetChannelInfoTask",
"rlYoutubeGetResumableUploadUrlTask",
"rlYoutubeGetUploadStatusTask",
"rlYoutubeGetVideoInfoTask",
"rlYoutubeShowAccountLinkUiTask",
"rlYoutubeUploadChunkTask",
"rlYoutubeUploadTask",
"rmcDrawable",
"rmcDrawableBase",
"rmcInstanceDataBase",
"rmcInstanceData_ConstBufferUpdateDesc",
"rmcInstanceWheelData",
"rmcRopeDrawable",
"ropeData",
"ropeDataManager",
"ropeManager",
"ropeMesh",
"ropeReins",
"ropeThin",
"ropeThin4",
"ropeThinWire32",
"ropeWire32",
"ropeWire6",
"sBoolStatData",
"sCloudFile",
"sDateStatData",
"sFloatStatData",
"sInt64StatData",
"sIntStatData",
"sLabelStatData",
"sLevelData",
"sObfFloatStatData",
"sObfInt64StatData",
"sObfIntStatData",
"sObfUns64StatData",
"sPackedStatData",
"sPlayerStatInfo",
"sPosStatData",
"sProfileSettingStatData",
"sSprintControlData",
"sStatData",
"sStatsMetadataTuning",
"sStringStatData",
"sUIStatData",
"sUIStatDataMP",
"sUIStatDataSP",
"sUIStatDataSPDetailed",
"sUns16StatData",
"sUns32StatData",
"sUns64BaseStatData",
"sUns64StatData",
"sUns8StatData",
"sUserIdStatData",
"sWeaponWheelMemory",
"scrProgram",
"scrThread",
"scriptHandler",
"scriptHandlerMgr",
"scriptHandlerNetComponent",
"scriptHandlerObject",
"scriptId",
"scriptIdBase",
"scriptObjInfo",
"scriptObjInfoBase",
"scriptResource",
"sfCallGameFromFlash",
"sfFSCommandHandler",
"sfFileOpener",
"sfImageCreator",
"sfPreallocatedMemoryWrapper",
"sfRageMemoryWrapper",
"sfRenderer",
"sfScaleformManager",
"sfTexture",
"sfTweenStarLiteClass",
"snAddRemoteGamerTask",
"snChangeAttributesTask",
"snConnectToPeerTask",
"snDestroyTask",
"snDropGamersTask",
"snEndTask",
"snEstablishSessionTask",
"snEvent",
"snEventAddedGamer",
"snEventJoinFailed",
"snEventRemovedGamer",
"snEventSessionAttrsChanged",
"snEventSessionDestroyed",
"snEventSessionEnded",
"snEventSessionHosted",
"snEventSessionJoined",
"snEventSessionMigrateEnd",
"snEventSessionMigrateStart",
"snEventSessionStarted",
"snGuestRegisterForMatchTask",
"snGuestStartTask",
"snHandleJoinRequestTask",
"snHostSessionTask",
"snHostStartTask",
"snJoinGamersToRlineTask",
"snJoinSessionTask",
"snLeaveGamersFromRlineTask",
"snMigrateSessionTask",
"snModifyPresenceFlagsTask",
"snRegisterRemoteGamersForMatchTask",
"snSendInvitesTask",
"spdGrid2D",
"strPackfileStreamingModule",
"strStreamingAllocator",
"strStreamingInterface",
"strStreamingModule",
"strStreamingModuleMgr",
"sveArray",
"sveBool",
"sveDict",
"sveFile",
"sveFloat",
"sveInt",
"sveNode",
"sveString",
"sveVec3",
"synthCoreDspEffect",
"synthCorePcmSource",
"sysEmptyUserList",
"sysMemAllocator",
"sysMemBuddyAllocator",
"sysMemContainer",
"sysMemFixedAllocator",
"sysMemGrowBuddyAllocator",
"sysMemMultiAllocator",
"sysMemSimpleAllocator",
"sysMemTinyAllocator",
"sysServiceEvent",
"sysTimeManager",
"sysUserList",
"type_info",
"updateBase",
"updateElement",
"updateGroup",
"verletTaskManager",
"AABB",
"ACQUIRING_TARGET",
"AGGRESSIVE",
"ALIGN",
"ALL_CLEAR",
"ALPHA",
"AMBIENCE_BANK_MAP_AUTOGENERATED",
"AMBIENT_ZONE_LIST",
"AMMO_FLAREGUN",
"ARMY",
"ARREST",
"AWARD",
"AWD_CAR_BOMBS_ENEMY_KILLS",
"Activate",
"AircraftFlames",
"Alpha",
"AmbientLights",
"Amount",
"AnimBlackboard",
"AnimatedBuilding",
"Arrested",
"AttachmentExtension",
"Attributes",
"Audio",
"Audio",
"Average",
"BACK",
"BASE",
"BIKE",
"BIRD",
"BLEND",
"BLOCKED_TO_LINEAR_VOLUME",
"BOAT",
"BUILDING",
"BUMP",
"BUSTED",
"BackgroundScripts",
"Backup",
"Base",
"Bike",
"Block",
"Blue",
"Boat",
"Body",
"BoneId",
"Bonnet",
"Bottom",
"Building",
"Bumpiness",
"CAICurvePoint",
"CAIHandlingInfo",
"CALL_METHOD_ON_MOVIE",
"CAMERA",
"CActionManager",
"CAgitatedManager",
"CAmbientAnimationManager",
"CAmbientAudioManager",
"CAmbientLookAt",
"CAmbientModelSetManager",
"CAnimBlackboard",
"CAppDataMgr",
"CArmIkSolver",
"CAssistedMovementRouteStore",
"CBoatChaseDirector",
"CBodyLookIkSolver",
"CBodyLookIkSolverProxy",
"CBodyRecoilIkSolver",
"CBullet",
"CBullet::sBulletInstance",
"CBuses",
"CBusySpinner",
"CBusySpinner",
"CCS_TITLE_UPDATE_DLC_METADATA",
"CCS_TITLE_UPDATE_DLC_PATCH",
"CCS_TITLE_UPDATE_STARTUP",
"CCS_TITLE_UPDATE_STREAMING",
"CCS_TITLE_UPDATE_TEXT",
"CCS_TITLE_UPDATE_WEAPON_PATCH",
"CCheat",
"CClimbHandHoldDetected",
"CClipDictionaryStoreInterface",
"CClock",
"CCollectionInfo",
"CCombatDirector",
"CCombatInfo",
"CCombatInfoMgr",
"CCombatSituation",
"CCompEntity",
"CConditionalAnimManager",
"CControlMgr",
"CControllerLabelMgr",
"CCover",
"CCoverFinder",
"CCredits",
"CCrimeInformationManager",
"CCullZones",
"CDLCScript",
"CDecoratorInterface",
"CDefaultCrimeInfo",
"CDispatchData",
"CDoorExtension",
"CDoorSyncData",
"CEvent",
"CEventDataManager",
"CEventDecisionMaker",
"CEventDecisionMakerModifiableComponent",
"CEventDecisionMakerResponseDynamic",
"CEventNetwork",
"CExpensiveProcessDistributer",
"CExplosionManager",
"CExtraContent",
"CExtraContentWrapper",
"CExtraContentWrapper::Shutdown",
"CExtraContentWrapper::ShutdownStart",
"CExtraMetadataMgr",
"CExtraMetadataMgr::ClassInit",
"CExtraMetadataMgr::ClassShutdown",
"CExtraMetadataMgr::ShutdownDLCMetaFiles",
"CFlyingVehicleAvoidanceManager",
"CFocusEntityMgr",
"CFrontendStatsMgr",
"CGameLogic",
"CGameScriptHandler",
"CGameScriptHandlerNetComponent",
"CGameScriptHandlerNetwork",
"CGameScriptResource",
"CGameSituation",
"CGameStreamMgr",
"CGameWorld",
"CGameWorld",
"CGameWorld",
"CGameWorld",
"CGameWorldHeightMap",
"CGameWorldWaterHeight",
"CGarages",
"CGenericGameStorage",
"CGestureManager",
"CGps",
"CGrabHelper",
"CGtaAnimManager",
"CHIMP",
"CHandlingDataMgr",
"CHandlingObject",
"CINEMATIC",
"CIVFEMALE",
"CIVMALE",
"CInstanceListAssetLoader::Init",
"CInstanceListAssetLoader::Shutdown",
"CInventoryItem",
"CIplCullBox",
"CJunctions",
"CLEAR_HELP_TEXT",
"CLIMB_LARGE",
"CLIMB_SMALL",
"CLIP",
"CLODLightManager",
"CLODLights",
"CLOTH",
"CLadderMetadataManager",
"CLegIkSolver",
"CLegIkSolverProxy",
"CLightExtension",
"CLoadingScreens",
"CM_WARN_HDR",
"CMapAreas",
"CMapZoneManager",
"CMessages",
"CMiniMap",
"CModelInfo",
"CModelInfo::Init",
"CMoveAnimatedBuilding",
"CMoveObject",
"CMovePed",
"CMoveVehicle",
"CMovieMeshManager",
"CMultiplayerGamerTagHud",
"CNamedPatrolRoute",
"CNetBlenderPed",
"CNetBlenderPhysical",
"CNetObjDoor",
"CNetObjObject",
"CNetObjPed",
"CNetObjPed::CNetTennisMotionData",
"CNetObjPhysical",
"CNetObjPickup",
"CNetObjPickupPlacement",
"CNetObjVehicle",
"CNetRespawnMgr",
"CNetViewPortWrapper",
"CNetwork",
"CNetworkCarGenWorldStateData",
"CNetworkDamageTracker",
"CNetworkPopGroupOverrideWorldStateData",
"CNetworkPopMultiplierAreaWorldStateData",
"CNetworkPtFXWorldStateData",
"CNetworkRoadNodeWorldStateData",
"CNetworkRopeWorldStateData",
"CNetworkScenarioBlockingAreaWorldStateData",
"CNetworkTelemetry",
"CNetworkVehiclePlayerLockingWorldState",
"CNewHud",
"CNonPhysicalPlayerData",
"CODED_WEAPON_SOUNDS",
"COLLISION",
"COLOR",
"COMBAT",
"COMMEND_SPAM_TIMEOUT",
"COMMEND_SPAM_TIMEOUT_DISABLED",
"COMMEND_TIMEOUT",
"COMMEND_TIMEOUT_DISABLED",
"COUGH",
"COWER",
"CObjectPopulationNY",
"CObjectSyncData",
"COcclusion",
"CParaboloidShadow",
"CPathFind",
"CPathNodeRouteSearchHelper",
"CPathServer::InitBeforeMapLoaded",
"CPathServer::InitSession",
"CPathServer::ShutdownSession",
"CPathZoneManager",
"CPatrolLink",
"CPatrolNode",
"CPatrolRoutes",
"CPauseMenu",
"CPed",
"CPedAILodManager",
"CPedGeometryAnalyser",
"CPedModelInfo",
"CPedPopulation",
"CPedPopulation::ResetPerFrameScriptedMultipiers",
"CPedPropsMgr",
"CPedSyncData",
"CPedVariationStream",
"CPedVariationStream",
"CPendingScriptObjInfo",
"CPerformance",
"CPhoneMgr",
"CPhotoManager",
"CPhysics",
"CPickup",
"CPickupData",
"CPickupDataManager",
"CPickupManager",
"CPickupPlacement",
"CPickupPlacementCustomScriptData",
"CPickupPlacementSyncData",
"CPickupSyncData",
"CPlantMgr",
"CPlayStats",
"CPlayerInfo",
"CPlayerSwitch",
"CPlayerSyncData",
"CPopCycle",
"CPopZones",
"CPopulationStreaming",
"CPopulationStreamingWrapper",
"CPortal",
"CPortalTracker",
"CPostScan",
"CPrioritizedClipSetBucket",
"CPrioritizedClipSetRequest",
"CPrioritizedClipSetRequestManager",
"CPrioritizedClipSetStreamer",
"CProcObjectMan",
"CProceduralInfo",
"CProfileSettings",
"CPropManagementHelper",
"CRandomEventManager",
"CRecentlyPilotedAircraft",
"CRegenerationInfo",
"CRelationshipGroup",
"CRenderPhaseCascadeShadowsInterface",
"CRenderTargetMgr",
"CRenderThreadInterface",
"CRenderer",
"CReportMenu",
"CRestart",
"CRiots",
"CRoadBlock",
"CRootSlopeFixupIkSolver",
"CSM_ST_BOX3x3",
"CSM_ST_BOX4x4",
"CSM_ST_TWOTAP",
"CSPClusterFSMWrapper",
"CScaleformMgr",
"CScenarioActionManager",
"CScenarioClusterSpawnedTrackingData",
"CScenarioInfo",
"CScenarioManager",
"CScenarioManager::ResetExclusiveScenarioGroup",
"CScenarioPoint",
"CScenarioPointChainUseInfo",
"CScenarioPointExtraData",
"CScenarioPointManager",
"CScenarioPointManagerInitSession",
"CScene",
"CSceneStreamerMgr::PreScanUpdate",
"CScriptAreas",
"CScriptCars",
"CScriptDebug",
"CScriptEntities",
"CScriptEntityExtension",
"CScriptHud",
"CScriptPedAIBlips",
"CScriptPeds",
"CScriptedGunTaskMetadataMgr",
"CShaderHairSort",
"CShaderLib",
"CSituationalClipSetStreamer",
"CSky",
"CSlownessZonesManager",
"CSpawnPointOverrideExtension",
"CSprite2d",
"CStaticBoundsStore",
"CStatsMgr",
"CStreaming",
"CStreamingRequestList",
"CStuntJump",
"CStuntJumpManager",
"CTVPlaylistManager",
"CTacticalAnalysis",
"CTask",
"CTaskClassInfoManager",
"CTaskConversationHelper",
"CTaskRecover",
"CTaskSequenceList",
"CTaskUseScenarioEntityExtension",
"CTaskVehicleSerialiserBase",
"CTexLod",
"CText",
"CThePopMultiplierAreas",
"CTheScripts",
"CTimeCycle",
"CTorsoIkSolver",
"CTorsoReactIkSolver",
"CTorsoVehicleIkSolver",
"CTorsoVehicleIkSolverProxy",
"CTrafficLights",
"CTrain",
"CTuningManager",
"CUserDisplay",
"CVehicleAILodManager",
"CVehicleBadgeData",
"CVehicleChaseDirector",
"CVehicleClipRequestHelper",
"CVehicleCombatAvoidanceArea",
"CVehicleDeformation",
"CVehicleMetadataMgr",
"CVehicleModelInfo",
"CVehiclePopulation",
"CVehicleRecordingMgr",
"CVehicleStreamRenderGfx",
"CVehicleStreamRequestGfx",
"CVehicleSyncData",
"CVehicleVariationInstance",
"CVisualEffects",
"CWarpManager",
"CWaypointRecording",
"CWeapon",
"CWeaponComponent",
"CWeaponComponentInfo",
"CWeaponManager",
"CWitnessInformationManager",
"CWorldPoints",
"CZonedAssetManager",
"Camera",
"Category",
"Chassis",
"Chimp",
"Clip",
"Color",
"Colors",
"Colour",
"Common",
"CompEntity",
"Content",
"Contents",
"CreateFinalScreenRenderPhaseList",
"Credits",
"Curves",
"CutSceneManager",
"CutSceneManager",
"CutSceneManager",
"CutSceneManagerWrapper",
"DEATH_GARGLE",
"DEATH_HIGH_LONG",
"DEATH_HIGH_MEDIUM",
"DEATH_HIGH_SHORT",
"DEATH_LOW",
"DEER",
"DEFAULT",
"DELETE_SAVEGAME",
"DISABLED",
"DISABLE_SCTV_FRIENDS_SPECTATE",
"DISABLE_SCTV_GTAO_TV",
"DOOR",
"DROP",
"Data",
"Decorator",
"DecoratorExtension",
"Default",
"Delay",
"Dependencies",
"Description",
"Detail",
"DiffuseColorTint",
"DiffuseTex",
"DiffuseTex2",
"Direction",
"DirtColor",
"DirtLevelMod",
"DirtTex",
"DisableSniperAudio",
"Disabled",
"Division",
"Door",
"Dummy",
"ENGINE",
"ENTITY",
"EQUAL_POWER_FALL_CROSSFADE",
"EQUAL_POWER_RISE_CROSSFADE",
"ERROR_INCORRECT_FUNDS",
"ERROR_INSUFFICIENT_FUNDS",
"ERROR_INSUFFICIENT_INVENTORY",
"ERROR_INVALID_CATALOG_VERSION",
"ERROR_INVALID_DATA",
"ERROR_INVALID_ITEM",
"ERROR_INVALID_PERMISSIONS",
"ERROR_NOT_IMPLEMENTED",
"ERROR_SUCCESS",
"ERROR_SYSTEM_ERROR",
"ERROR_UNDEFINED",
"EXHALE_CYCLING",
"EXPLOSION_INFO_PLANE",
"EXP_TAG_GRENADE",
"Entity",
"Entity",
"EntityBatch",
"Event",
"Exclusions",
"Exhaust",
"ExplosionType",
"FAST",
"FINISHED",
"FIRE",
"FIREMAN",
"FISH",
"FORCE",
"FRANKLIN",
"FRONTEND",
"FacialClipSetGroupManager",
"Fast",
"Fastest",
"Fire",
"FireManager",
"FirstPersonProp",
"FirstPersonProp",
"FirstPersonPropCam",
"Flags",
"Focus",
"Foot",
"Force",
"FrontEnd",
"GALLERY",
"GAME",
"GANG",
"GENERIC",
"GENERIC_CURSE_MED",
"GENERIC_GREET",
"GENERIC_HI",
"GENERIC_SHOCKED_HIGH",
"GENERIC_SHOCKED_MED",
"GET_WANTED_LEVEL",
"GLASS",
"GLOBAL",
"GROUP_EARLY_ON",
"GROUP_MAP",
"GROUP_MAP_SP",
"GROUP_ON_DEMAND",
"GROUP_STARTUP",
"GUN_BEG",
"GUN_COOL",
"Game",
"Game",
"Game",
"GamePlayerBroadcastDataHandler_Remote",
"Garages",
"General",
"Generic",
"GenericGameStoragePhotoGallery",
"Global",
"GrassBatch",
"Green",
"Group",
"HAND",
"HASH",
"HEAD",
"HEADER",
"HEIGHT",
"HELI",
"HELIS_EXPLODED",
"HIDE_ACCEPTBUTTON",
"HIGH_FALL",
"HUD_AWARDS",
"HUD_COLOUR_FREEMODE",
"HUD_FRONTEND_DEFAULT_SOUNDSET",
"Header",
"Heavy",
"Heel",
"Height",
"Heli",
"IDLE",
"INCIDENTS",
"INFO",
"INSTANCESTORE",
"INSTANCESTORE",
"INSTANCESTORE",
"INSTRUCTIONAL_BUTTONS",
"INT64",
"INTERIOR",
"INTIMIDATE_RESP",
"INVALID",
"Identifier",
"ImposedTxdCleanup",
"Impulse",
"Incidents",
"IndependentMoverExpression",
"InitSystem",
"InstanceBuffer",
"InteriorInst",
"InteriorProxy",
"Intro",
"Invalid",
"InvalidState",
"ItemSet",
"Items",
"JUMP",
"JoinRequest",
"KICK_TIMEOUT",
"KICK_TIMEOUT_DISABLED",
"Keys",
"Kick",
"Known",
"LEFT",
"LINEAR_RISE",
"LOCATION",
"LOCKED",
"Landing",
"Left",
"Length",
"Light",
"LightEntity",
"LightEntityMgr",
"LightObject",
"Lights",
"Lights",
"Location",
"Locked",
"Loop",
"LoopId",
"MALE",
"MEDIC",
"MEDIUM",
"MELEE",
"MELEE_LARGE_GRUNT",
"MELEE_SMALL_GRUNT",
"MICHAEL",
"MISSION",
"MONEY_EARN_ROCKSTAR_AWARD",
"MONEY_SPENT_BETTING",
"MONEY_SPENT_CONTACT_SERVICE",
"MONEY_SPENT_DROPPED_STOLEN",
"MONEY_SPENT_HEALTHCARE",
"MONEY_SPENT_JOB_ACTIVITY",
"MONEY_SPENT_PROPERTY_UTIL",
"MONEY_SPENT_ROCKSTAR_AWARD",
"MONEY_SPENT_STYLE_ENT",
"MONEY_SPENT_VEH_MAINTENANCE",
"MONEY_SPENT_WEAPON_ARMOR",
"MP_GLOBAL",
"MTLION",
"MapDataLoadedNode",
"Markers",
"Matrix",
"Medium",
"Memory",
"MeshBlendManager",
"Misc",
"Misc",
"ModularSynth",
"MusicAction",
"MusicEvent",
"NG_first",
"NONE",
"NORMAL",
"NO_RESULTS",
"NULL",
"Name",
"NameHash",
"NavMeshRoute",
"NetShopTransactions",
"NetworkEntityAreas",
"NewHud",
"NextGen",
"None",
"Normal",
"Null",
"OBJECT",
"ORDERS",
"Object",
"ObjectIntelligence",
"Occlusion",
"Occlusion",
"Occlusion",
"OcclusionInteriorInfo",
"OcclusionPathNode",
"OcclusionPortalEntity",
"OcclusionPortalInfo",
"Orders",
"OverrideMicrophoneSettings",
"OverridePlayerGroundMaterial",
"Owner",
"PAIN_FRANKLIN",
"PAIN_GARGLE",
"PAIN_HIGH",
"PAIN_LOW",
"PAIN_MICHAEL",
"PAIN_SHOVE",
"PAIN_TREVOR",
"PAIN_WHEEZE",
"PED_RANT",
"PLANE",
"PLATFORM",
"PLAYER",
"POSITION",
"PROP",
"PUSH",
"PatrolRoute",
"PauseMenu",
"Ped",
"PedHeadShotManager",
"PedHeadshotManager",
"PedIntelligence",
"PedPopulation",
"PedProp",
"PedProp",
"Peds",
"Percentage",
"Phase",
"Plane",
"PlantsMgr::UpdateBegin",
"PlantsMgr::UpdateEnd",
"Player",
"PointRoute",
"Population",
"PortalInst",
"Position",
"PostFX",
"PostFx",
"PreSet",
"PricePaid",
"Prioritized",
"Proc",
"ProcessAfterCameraUpdate",
"ProcessAfterMovement",
"ProcessPedsEarlyAfterCameraUpdate",
"QuadTreeNodes",
"RABBIT",
"REPORT_SPAM_TIMEOUT",
"REPORT_SPAM_TIMEOUT_DISABLED",
"REPORT_TIMEOUT",
"REPORT_TIMEOUT_DISABLED",
"RIGHT",
"RL_CLAN_UNKNOWN",
"RP_WARN_HDR",
"RP_WARN_MISS_HDR",
"Radius",
"Rain",
"Random",
"Range",
"Rate",
"Rectangle",
"Render",
"Render",
"ResetSceneLights",
"Right",
"Rotation",
"Run",
"SCREAM_SHOCKED",
"SCRIPT",
"SCRIPT_TASK_EXIT_COVER",
"SCRIPT_TASK_GO_TO_COORD_ANY_MEANS_EXTRA_PARAMS",
"SCRIPT_TASK_PUT_PED_DIRECTLY_INTO_MELEE",
"SEE_WEIRDO",
"SELECT_STORAGE_DEVICE",
"SETTINGS",
"SET_DISPLAY_CONFIG",
"SHARK",
"SIZE",
"SLOT_UNARMED",
"SLOW",
"SMALL_DOG",
"SMALL_MAMMAL",
"SM_LCNONE",
"SNEEZE",
"SPACE_RANGER",
"SPHERE",
"SP_MOST_FAVORITE_STATION",
"START",
"STORE",
"STRENGTH",
"SWAT",
"SWITCH",
"Scale",
"Script",
"ScriptHud",
"ScriptShapeTestResult",
"Sequencer",
"Settings",
"ShaderLib::Update",
"ShapeTestTaskData",
"Situational",
"Size",
"Slot",
"Slow",
"Slowest",
"Small",
"Sniper",
"SocialClubMenu",
"Sounds",
"SpecularTex",
"SpeechSound",
"StatValue",
"Stop",
"StreamPed",
"StreamPed",
"Streaming",
"Streaming",
"Strength",
"Strings",
"Switch",
"System",
"TANGENT",
"TARGETED_LOCK",
"TARGET_ACQUIRED",
"TASK",
"TAXI",
"TAXI_HAIL",
"TCPTemplate",
"TCPTemplateRelative",
"THROW",
"TRACKID",
"TRAILER",
"TREVOR",
"TargetRatio",
"Targetting",
"TaskSequenceInfo",
"Template",
"Text",
"Time",
"TimeOfPreDelay",
"Title",
"TrafficLightInfos",
"Trailer",
"TrevorRageIsOverriden",
"Trigger",
"TurnRate",
"Type",
"UI3DDrawManager",
"UIName",
"UIWorldIconManager",
"UnderWaterStreamOverriden",
"Unicode",
"Update",
"Update",
"UpperbodyFeathered_NoLefttArm_filter",
"UpperbodyFeathered_NoRightArm_filter",
"Upperbody_filter",
"Used",
"VEHICLE",
"VEHICLE_DEFAULT",
"Value",
"Values",
"VehPopulation",
"Vehicle",
"Vehicle",
"Velocity",
"Version",
"Video",
"VideoPlayback",
"VideoPlaybackThumbnailManager",
"Viewport",
"ViewportSystemInit",
"ViewportSystemInitLevel",
"ViewportSystemShutdown",
"ViewportSystemShutdownLevel",
"Visibility",
"Visual",
"WANDER",
"WAPClip",
"WAPFlshLasr",
"WAPGrip",
"WAPScop",
"WAPSupp",
"WARNING",
"WATER",
"WATER_CANNON",
"WEAPON",
"WHIMPER",
"WORLD_HUMAN_TOURIST_MOBILE_CAR",
"Warning",
"WarningScreen",
"Water",
"Water",
"Water",
"WaterHeightSim",
"Wheels",
"Wins",
"World",
"XMAS",
"Zone",
"_NatType",
"_alpha",
"_rotation",
"_xrotation",
"_xscale",
"_yrotation",
"_yscale",
"_zscale",
"activate",
"align",
"alpha",
"amount",
"angle",
"anim",
"apply",
"arrayUpdateInfo",
"atDNetEventNode",
"atDNetObjectNode",
"atDScriptObjectNode",
"atDTransactionNode",
"attributes",
"audNorthAudioEngine",
"audNorthAudioEngineDLC",
"audio",
"autoplay",
"barracks",
"base",
"bicycle",
"bike",
"blue",
"boat",
"bodyshell",
"bonnet",
"boot",
"bottom",
"break_extra_1",
"break_extra_10",
"break_extra_2",
"break_extra_3",
"break_extra_4",
"break_extra_5",
"break_extra_6",
"break_extra_7",
"break_extra_8",
"break_extra_9",
"bump",
"bumper_f",
"bumper_r",
"cStoreScreenMgr",
"camBaseCamera",
"camBaseFrameShaker",
"camBaseSwitchHelper",
"camCatchUpHelper",
"camCollision",
"camControlHelper",
"camEnvelope",
"camFrameInterpolator",
"camHintHelper",
"camInconsistentBehaviourZoomHelper",
"camLookAheadHelper",
"camLookAtDampingHelper",
"camManager",
"camOscillator",
"camSplineNode",
"camSpringMount",
"camThirdPersonFrameInterpolator",
"camera",
"carrec",
"catalog",
"category",
"chassis",
"clip",
"cloud",
"color",
"color1",
"color2",
"color3",
"color4",
"complete",
"component",
"configureBullets",
"content",
"corona",
"create",
"damage",
"data",
"decorators",
"default",
"delay",
"depth",
"description",
"diffuseTex",
"direction",
"directional",
"dist",
"distance",
"duration",
"ease",
"empty",
"enabled",
"engine",
"envEffScale0",
"event",
"events",
"exhaust",
"exhaust_10",
"exhaust_11",
"exhaust_12",
"exhaust_13",
"exhaust_14",
"exhaust_15",
"exhaust_16",
"exhaust_2",
"exhaust_3",
"exhaust_4",
"exhaust_5",
"exhaust_6",
"exhaust_7",
"exhaust_8",
"exhaust_9",
"extra_1",
"extra_11",
"extra_12",
"extra_2",
"extra_3",
"extra_4",
"extra_5",
"extra_6",
"extra_7",
"extra_8",
"extra_9",
"falloff",
"filename",
"fog_col_g",
"force",
"forks_l",
"forks_u",
"fragInstGta",
"fragInstNMGta",
"franklin",
"fwAnimDirector",
"fwAnimDirectorComponentCreature",
"fwAnimDirectorComponentExpressions",
"fwAnimDirectorComponentFacialRig",
"fwAnimDirectorComponentMotionTree",
"fwAnimDirectorComponentMove",
"fwAnimDirectorComponentRagDoll",
"fwAnimDirectorComponentSyncedScene",
"fwClipSetManager",
"fwClothCollisionsExtension",
"fwClothStore",
"fwContainerLod",
"fwDrawawableStoreWrapper",
"fwDwdStore",
"fwDwdStoreWrapper",
"fwDynamicArchetypeComponent",
"fwDynamicEntityComponent",
"fwExpressionSetManager",
"fwFacialClipSetGroupManager",
"fwFragmentStoreWrapper",
"fwMapTypesStore",
"fwMapTypesStore",
"fwMapTypesStore",
"fwMatrixTransform",
"fwMetaDataStore",
"fwQuaternionTransform",
"fwRoomSceneGraphNode",
"fwScriptGuid",
"fwSimpleTransform",
"fwTimer",
"fwTxdStore",
"game",
"generic",
"global",
"grab",
"graphics",
"green",
"group",
"gtav",
"hand",
"handlebars",
"hash",
"head",
"headlight_l",
"headlight_r",
"height",
"heli",
"hour",
"hub_lf",
"hub_rf",
"idle",
"impulse",
"inbox",
"index",
"indicator_lf",
"indicator_lr",
"indicator_rf",
"indicator_rr",
"int64",
"int_mp_h_04",
"intensity",
"interior",
"invalid",
"items",
"jetmax",
"kTrackBoneTranslation",
"landing",
"left",
"legs",
"length",
"level",
"light",
"loadMovie",
"location",
"mass",
"matrix",
"medium",
"melee",
"michael",
"misc_1",
"misc_2",
"misc_a",
"misc_b",
"misc_c",
"misc_d",
"misc_e",
"misc_f",
"misc_g",
"misc_h",
"misc_i",
"misc_j",
"misc_k",
"misc_l",
"misc_m",
"misc_n",
"misc_o",
"misc_p",
"misc_q",
"misc_r",
"misc_s",
"misc_t",
"misc_u",
"misc_v",
"misc_w",
"misc_x",
"misc_y",
"misc_z",
"mod_col_1",
"mod_col_2",
"mod_col_3",
"mod_col_4",
"mod_col_5",
"models",
"modifier",
"mods",
"move_f@fat@a",
"mp_crewpalette",
"mp_curr_gamemode",
"mp_facial",
"naEnvironmentGroup",
"name",
"nameHash",
"namehash",
"neon_b",
"neon_f",
"neon_l",
"neon_r",
"netGameEvent",
"none",
"normal",
"not",
"null",
"object",
"offset",
"onComplete",
"onCompleteArgs",
"onCompleteScope",
"outro_0",
"overlay",
"params",
"parent",
"participantData",
"path",
"perfClearingHouse",
"perfClearingHouse",
"phInstGta",
"phase",
"physics_glasses",
"physics_hat",
"physics_hat2",
"plane",
"platform",
"platform:/textures/frontend",
"player",
"player_zero",
"position",
"postFX",
"postfx",
"postion",
"price",
"priority",
"prologue",
"push",
"radius",
"rain",
"random",
"randomSeed",
"reassignObjectInfo",
"rectangle",
"reverse",
"rhino",
"right",
"ropeData",
"rotate",
"rotation",
"rudder",
"sanchez",
"scale",
"script",
"settings",
"size",
"skydome",
"slod_human",
"slod_large_quadped",
"slod_small_quadped",
"slot",
"speed",
"sphere",
"spin",
"spine",
"spine+",
"spread",
"start",
"steering",
"stop",
"store",
"strStreamingEngine::SubmitDeferredAsyncPlacementRequests",
"strength",
"swingarm",
"syncMessageInfo",
"syncMessageInfoNode",
"system",
"taillight_l",
"taillight_r",
"task",
"tcBox",
"temperature",
"template",
"terrain_grid",
"text",
"time",
"timer",
"title",
"titleList",
"trailer",
"trevor",
"type",
"underwater",
"update",
"upperBodyReaction",
"v_dark",
"value",
"values",
"variation",
"vehicle",
"vehicle_licenseplate",
"vehicle_mesh",
"vehicle_mesh_enveff",
"vehicle_paint1",
"vehicle_paint1_enveff",
"vehicle_paint2",
"vehicle_paint2_enveff",
"vehicle_paint3",
"vehicle_paint3_enveff",
"vehicle_paint4",
"vehicle_paint4_enveff",
"vehicle_paint5",
"vehicle_paint5_enveff",
"vehicle_paint6",
"vehicle_paint6_enveff",
"vehicle_vehglass",
"vehicles",
"version",
"video",
"water",
"weapon_normal_spec_cutout_palette",
"window_lf",
"window_lr",
"window_rf",
"window_rr",
"windscreen_r",
"wing_lf",
"wing_rf",
"wptrec",
"xi:include",
