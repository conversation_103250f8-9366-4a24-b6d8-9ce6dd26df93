.root {
  display: flex;
  flex-direction: column;

  gap: ui.offset('large');

  width: ui.viewport-width();
  height: ui.viewport-height();

  overflow-y: scroll;

  abbr {
    position: relative;
    text-decoration: none;
    border-bottom: dotted 1px ui.color('fg', 700);

    &:hover {
      &::before {
        opacity: 1;
      }
    }

    &::before {
      opacity: 0;
      pointer-events: none;

      display: block;
      content: attr(title);

      position: absolute;

      left: ui.q(-1);
      bottom: 100%;

      width: 100%;

      padding: ui.q();

      color: ui.color('bg');
      background: ui.color('fg');

      z-index: 1000;
    }
  }

  section {
    display: flex;
    flex-direction: column;

    gap: ui.offset('large');
    padding: ui.offset('large');

    @include ui.backdrop;
    @include ui.border-radius;

    & > div {
      display: flex;
      flex-direction: column;

      line-height: 1.4;
    }
  }

  aside {
    position: relative;

    --uhh: #{ui.offset('normal')};

    padding: ui.offset('small') 0;

    &::after {
      position: absolute;

      display: block;
      content: '';

      top: 0;
      left: ui.negative(ui.offset('large'));
      bottom: 0;

      width: ui.q(.5);

      background: ui.color('fg', 500);
    }

    &::before {
      display: block;
      content: 'Example';

      margin-bottom: ui.offset('small');

      color: ui.color('fg', 600);
      @include ui.font-size('xsmall');
    }
  }
}

.square {
  display: flex;
  align-items: center;
  justify-content: center;

  width: ui.q(10);
  height: ui.q(10);
  @include ui.border-radius('small');

  border: solid 1px ui.color('bg');

  --c: #{ui.color('bg', 'pure', .75)};
  text-shadow: 0 0 2.5px var(--c), 0 0 5px var(--c), 0 0 5px var(--c);
}

.row {
  display: flex;

  gap: ui.q();
}

.offset {
  width: 100%;
  background-color: ui.color('success', 600);
}
