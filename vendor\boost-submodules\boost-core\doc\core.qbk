[/
Copyright 2014 <PERSON>
(<EMAIL>)

Distributed under the Boost Software License,
Version 1.0. (See accompanying file LICENSE_1_0.txt
or copy at http://boost.org/LICENSE_1_0.txt)
]

[library Boost.Core
  [quickbook 1.6]
  [id core]
  [copyright 2014 <PERSON>]
  [copyright 2014 <PERSON>]
  [copyright 2014 <PERSON><PERSON>]
  [dirname core]
  [license Distributed under the
    [@http://boost.org/LICENSE_1_0.txt Boost Software License,
      Version 1.0].
  ]
]

[template simplesect[title]
[block '''<simplesect><title>'''[title]'''</title>''']]

[template endsimplesect[]
[block '''</simplesect>''']]

[section Introduction]

The Boost.Core library is a collection of core utilities. The
criteria for inclusion is that the utility component be:

* simple,
* used by other Boost libraries, and
* not dependent on any other Boost modules except Core
  itself, Config, Assert, StaticAssert, or ThrowException.

[endsect]

[include changes.qbk]

[include addressof.qbk]
[include alignof.qbk]
[include allocator_access.qbk]
[include allocator_traits.qbk]
[include bit.qbk]
[include checked_delete.qbk]
[include cmath.qbk]
[include data.qbk]
[include default_allocator.qbk]
[include demangle.qbk]
[include empty_value.qbk]
[include enable_if.qbk]
[include exchange.qbk]
[include explicit_operator_bool.qbk]
[include first_scalar.qbk]
[include functor.qbk]
[include identity.qbk]
[include ignore_unused.qbk]
[include is_same.qbk]
[include launder.qbk]
[include lightweight_test.qbk]
[include make_span.qbk]
[include max_align.qbk]
[include memory_resource.qbk]
[include no_exceptions_support.qbk]
[include noinit_adaptor.qbk]
[include noncopyable.qbk]
[include null_deleter.qbk]
[include fclose_deleter.qbk]
[include nvp.qbk]
[include pointer_traits.qbk]
[include quick_exit.qbk]
[include ref.qbk]
[include scoped_enum.qbk]
[include serialization.qbk]
[include size.qbk]
[include span.qbk]
[include swap.qbk]
[include typeinfo.qbk]
[include type_name.qbk]
[include snprintf.qbk]
[include uncaught_exceptions.qbk]
[include use_default.qbk]
[include verbose_terminate_handler.qbk]
[include yield_primitives.qbk]
