# Copyright 2017 The Chromium Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

mojom = "//mojo/public/mojom/base/process_id.mojom"
public_headers = [ "//base/process/process_handle.h" ]
traits_headers = [ "//mojo/public/cpp/base/process_id_mojom_traits.h" ]
sources = [
  "//mojo/public/cpp/base/process_id_mojom_traits.cc",
  "//mojo/public/cpp/base/process_id_mojom_traits.h",
]

type_mappings =
    [ "mojo_base.mojom.ProcessId=::base::ProcessId[copyable_pass_by_value]" ]
