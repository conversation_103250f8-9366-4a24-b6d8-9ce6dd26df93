import React, { useState } from 'react';
import { observer } from 'mobx-react-lite';
import { AuthStep, useCustomAuthService } from 'cfx/apps/mpMenu/services/customAuth/customAuth.service';
import { getVietnameseErrorMessage } from 'cfx/apps/mpMenu/services/customAuth/errorMessages';

import s from './ModernLoginModal.module.scss';

interface ModernLoginModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const ModernLoginModal = observer(function ModernLoginModal({ isOpen, onClose }: ModernLoginModalProps) {
  const authService = useCustomAuthService();
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    rememberMe: false
  });
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  if (!isOpen) return null;

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Email is invalid';
    }

    if (!formData.password.trim()) {
      newErrors.password = 'Password is required';
    } else if (formData.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    authService.clearError();

    try {
      await authService.login(formData.email, formData.password);
      onClose();
    } catch (error) {
      console.error('Login failed:', error);
    }
  };

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleRegisterClick = () => {
    authService.setStep(AuthStep.REGISTER);
  };

  return (
    <div className={s.overlay} onClick={onClose}>
      <div className={s.modal} onClick={(e) => e.stopPropagation()}>
        <button className={s.closeButton} onClick={onClose}>
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>

        <div className={s.modalContent}>
          <h2 className={s.title}>Login</h2>

          <form onSubmit={handleSubmit} className={s.form}>
            <div className={s.inputGroup}>
              <div className={s.inputWrapper}>
                <input
                  type="email"
                  placeholder="Email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  className={`${s.input} ${errors.email ? s.error : ''}`}
                  disabled={authService.isLoading}
                />
                <div className={s.inputIcon}>
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z" />
                  </svg>
                </div>
              </div>
              {errors.email && <span className={s.errorMessage}>{errors.email}</span>}
            </div>

            <div className={s.inputGroup}>
              <div className={s.inputWrapper}>
                <input
                  type="password"
                  placeholder="Password"
                  value={formData.password}
                  onChange={(e) => handleInputChange('password', e.target.value)}
                  className={`${s.input} ${errors.password ? s.error : ''}`}
                  disabled={authService.isLoading}
                />
                <div className={s.inputIcon}>
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M18,8h-1V6c0-2.76-2.24-5-5-5S7,3.24,7,6v2H6c-1.1,0-2,0.9-2,2v10c0,1.1,0.9,2,2,2h12c1.1,0,2-0.9,2-2V10C20,8.9,19.1,8,18,8z M12,17c-1.1,0-2-0.9-2-2s0.9-2,2-2s2,0.9,2,2S13.1,17,12,17z M15.1,8H8.9V6c0-1.71,1.39-3.1,3.1-3.1s3.1,1.39,3.1,3.1V8z"/>
                  </svg>
                </div>
              </div>
              {errors.password && <span className={s.errorMessage}>{errors.password}</span>}
            </div>

            <div className={s.checkboxRow}>
              <label className={s.checkboxLabel}>
                <input
                  type="checkbox"
                  checked={formData.rememberMe}
                  onChange={(e) => handleInputChange('rememberMe', e.target.checked)}
                  className={s.checkbox}
                />
                <span className={s.checkboxText}>Remember me</span>
              </label>
              <button type="button" className={s.forgotPassword}>
                Forgot Password?
              </button>
            </div>

            {authService.error && (
              <div className={s.errorMessage}>
                {getVietnameseErrorMessage(authService.error)}
              </div>
            )}

            <button
              type="submit"
              className={s.loginButton}
              disabled={authService.isLoading}
            >
              {authService.isLoading ? (
                <div className={s.loadingIndicator}>
                  <div className={s.spinner}></div>
                  Loading...
                </div>
              ) : (
                'Login'
              )}
            </button>

            <div className={s.registerSection}>
              <span className={s.registerText}>Don't have an account? </span>
              <button
                type="button"
                className={s.registerLink}
                onClick={handleRegisterClick}
                disabled={authService.isLoading}
              >
                Register
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
});
