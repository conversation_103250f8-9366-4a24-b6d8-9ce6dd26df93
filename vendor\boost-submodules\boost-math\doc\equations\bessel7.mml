<math  xmlns="http://www.w3.org/1998/Math/MathML" display="block" ><mrow >                                <msubsup><mrow ><mi >Y</mi> </mrow><mrow ><mi >&#x03BD;</mi></mrow><mrow ><mi >&#x2032;</mi></mrow></msubsup ><mrow ><mo class="MathClass-open">(</mo><mrow><mi >z</mi></mrow><mo class="MathClass-close">)</mo></mrow> <mo class="MathClass-rel">=</mo> <mfrac><mrow ><mi >&#x03BD;</mi></mrow> <mrow ><mi >z</mi></mrow></mfrac><msub><mrow ><mi >Y</mi> </mrow><mrow ><mi >&#x03BD;</mi></mrow></msub ><mrow ><mo class="MathClass-open">(</mo><mrow><mi >z</mi></mrow><mo class="MathClass-close">)</mo></mrow> <mo class="MathClass-bin">&#x2212;</mo> <msub><mrow ><mi >Y</mi> </mrow><mrow ><mi >&#x03BD;</mi><mo class="MathClass-bin">+</mo><mn>1</mn></mrow></msub ><mrow ><mo class="MathClass-open">(</mo><mrow><mi >z</mi></mrow><mo class="MathClass-close">)</mo></mrow></mrow></math>