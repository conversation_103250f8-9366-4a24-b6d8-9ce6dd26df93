//
// Copyright (c) 2009-2011 <PERSON><PERSON><PERSON> (Tonkikh)
//
// Distributed under the Boost Software License, Version 1.0.
// https://www.boost.org/LICENSE_1_0.txt

/*!
\page faq Frequently Asked Questions

-   \anchor faq_bad_cast <b>Some Boost.Locale functions throw \c std::bad_cast exception?</b>
    \n
    \n
    \b Answer: You probably try to use an incorrect \c std::locale object. All Boost.Locale tools rely on \c std::locale object's facets.
    The locale object should be generated with the \ref boost::locale::generator "generator" class and then passed to
    the function or alternatively global locale should be set using the \c std::locale::global() function such that
    global locale (and default created one) would have the required facets.
-   \anchor faq_number <b>I have installed global locale, but when I try to write something to a stream I still get the wrong output?</b>
    For example:
    \code
    #include <boost/locale.hpp>
    #include <iostream>
    int main()
    {
        boost::locale::generator gen;
        std::locale::global(gen(""));
        std::cout << boost::locale::as::date << std::time(0) << std::endl;
    }
    \endcode
    Prints a number instead of a date.
    \n
    \b Answer: You forget to imbue the locale to the stream. Changing the global locale does not affect the
    locale in existing \c iostream objects. Thus, because \c std::out and other global streams were created
    before changing the global locale, Boost.Locale manipulators have no effect. You need to write:
    \code
    #include <boost/locale.hpp>
    #include <iostream>
    int main()
    {
        boost::locale::generator gen;
        std::locale l = gen("");
        std::locale::global(l);
        std::cout.imbue(l);
        std::cout << boost::locale::as::date << std::time(0) << std::endl;
    }
    \endcode

*/
