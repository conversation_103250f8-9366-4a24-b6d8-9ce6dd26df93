# Copyright 2018 The Chromium Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

mojom = "//mojo/public/mojom/base/values.mojom"
public_headers = [ "//base/values.h" ]
traits_headers = [ "//mojo/public/cpp/base/values_mojom_traits.h" ]
public_deps = [
  "//base",
  "//mojo/public/cpp/base:shared_typemap_traits",
]
type_mappings = [
  "mojo_base.mojom.Value=::base::Value[move_only]",
  "mojo_base.mojom.DictionaryValue=::base::Value[move_only]",
  "mojo_base.mojom.ListValue=::base::Value[move_only]",
]
