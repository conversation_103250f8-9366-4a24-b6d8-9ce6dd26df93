<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="generator" content="Docutils 0.11: http://docutils.sourceforge.net/" />
<title>Iterator Traits</title>
<meta name="author" content="<PERSON>" />
<meta name="organization" content="Boost Consulting" />
<meta name="copyright" content="Copyright David <PERSON> 2004." />
<link rel="stylesheet" href="../../../rst.css" type="text/css" />
</head>
<body>
<div class="document" id="iterator-traits">
<h1 class="title">Iterator Traits</h1>
<table class="docinfo" frame="void" rules="none">
<col class="docinfo-name" />
<col class="docinfo-content" />
<tbody valign="top">
<tr><th class="docinfo-name">Author:</th>
<td>David Abrahams</td></tr>
<tr><th class="docinfo-name">Contact:</th>
<td><a class="first last reference external" href="mailto:dave&#64;boost-consulting.com">dave&#64;boost-consulting.com</a></td></tr>
<tr><th class="docinfo-name">Organization:</th>
<td><a class="first last reference external" href="http://www.boost-consulting.com">Boost Consulting</a></td></tr>
<tr><th class="docinfo-name">Copyright:</th>
<td>Copyright David Abrahams 2004.</td></tr>
</tbody>
</table>
<!-- Distributed under the Boost -->
<!-- Software License, Version 1.0. (See accompanying -->
<!-- file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt) -->
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">abstract:</th><td class="field-body">Header <tt class="docutils literal">&lt;boost/iterator/iterator_traits.hpp&gt;</tt> provides
the ability to access an iterator's associated types using
MPL-compatible <a class="reference external" href="../../mpl/doc/index.html#metafunctions">metafunctions</a>.</td>
</tr>
</tbody>
</table>
<div class="section" id="overview">
<h1>Overview</h1>
<p><tt class="docutils literal"><span class="pre">std::iterator_traits</span></tt> provides access to five associated types
of any iterator: its <tt class="docutils literal">value_type</tt>, <tt class="docutils literal">reference</tt>, <tt class="docutils literal">pointer</tt>,
<tt class="docutils literal">iterator_category</tt>, and <tt class="docutils literal">difference_type</tt>.  Unfortunately,
such a &quot;multi-valued&quot; traits template can be difficult to use in a
metaprogramming context.  <tt class="docutils literal">&lt;boost/iterator/iterator_traits.hpp&gt;</tt>
provides access to these types using a standard <a class="reference external" href="../../mpl/doc/index.html#metafunctions">metafunctions</a>.</p>
</div>
<div class="section" id="summary">
<h1>Summary</h1>
<p>Header <tt class="docutils literal">&lt;boost/iterator/iterator_traits.hpp&gt;</tt>:</p>
<pre class="literal-block">
template &lt;class Iterator&gt;
struct iterator_value
{
    typedef typename
      std::iterator_traits&lt;Iterator&gt;::value_type
    type;
};

template &lt;class Iterator&gt;
struct iterator_reference
{
    typedef typename
      std::iterator_traits&lt;Iterator&gt;::reference
    type;
};


template &lt;class Iterator&gt;
struct iterator_pointer
{
    typedef typename
      std::iterator_traits&lt;Iterator&gt;::pointer
    type;
};

template &lt;class Iterator&gt;
struct iterator_difference
{
    typedef typename
      detail::iterator_traits&lt;Iterator&gt;::difference_type
    type;
};

template &lt;class Iterator&gt;
struct iterator_category
{
    typedef typename
      detail::iterator_traits&lt;Iterator&gt;::iterator_category
    type;
};
</pre>
</div>
</div>
<div class="footer">
<hr class="footer" />
<a class="reference external" href="iterator_traits.rst">View document source</a>.
Generated by <a class="reference external" href="http://docutils.sourceforge.net/">Docutils</a> from <a class="reference external" href="http://docutils.sourceforge.net/rst.html">reStructuredText</a> source.

</div>
</body>
</html>
