# Copyright 2018 The Chromium Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//build/config/chrome_build.gni")
import("//build/config/locales.gni")

# This template defines a rule to generate localized string .rc and .h files
# that can be embedded directly into a library or executable if desired
#
# Parameters
#
#   grd_files_info
#       List of tuples that contains information about the all the .grd files
#       from which to extract the strings.
#       Each tuple contains 4 elements:
#         1. The folder containing the grd file.
#         2. The name of the grd file without an extension.
#         3. The path relative to the grd file where the xtb files are stored.
#         4. The expected locales of the xtb files available in the xtb
#            relative path (can use default_embedded_i18_locales defined in
#            this file). This is just the locale portion of the filename so
#            if a file like google_strings_en-US.xtb is expected then this
#            list should contain "en-US".
#
#   output_file_name_base
#       The base name of the generated header / rc file that will contain the
#       extracted string information. The files will be output to:
#       "$target_gen_dir/$output_file_name_base.h" and
#       "$target_gen_dir/$output_file_name_base.rc".
#
#   first_resource_id (optional)
#       The starting resource ID for the generated string resources.
#       Defaults to 1600.
#
#   extractor_datafile (optional)
#       The python file to execute that contains definition of the specific strings
#       to extract from the source .grd. This file can contain an array STRING_IDS
#       that lists all the IDs that will be extracted for all brand targets.
#       It can optionally also contain a dictionary MODE_SPECIFIC_STRINGS that
#       contains mode specific strings for certain brand targets.
#       MODE_SPECIFIC_STRINGS is only valid if STRING_IDS is specified.
#       Will not be passed to the generation script if it is undefined.
#
#   branding (optional)
#       The branding used to determine specific string extractions.
#       Will not be passed to the generation script if it is undefined.
#
#   deps (optional)
#   visibility (optional)

# Some locales exist in |all_chrome_locales| but don't generally have an xtb file on disk
# and some translations don't exist in |all_chrome_locales| but do generally have an xtb
# file on disk.
default_embedded_i18_locales = all_chrome_locales - [
                                 "en-US",
                                 "he",
                                 "nb",
                               ] +
                               [
                                 "iw",
                                 "no",
                               ]

template("generate_embedded_i18n") {
  assert(defined(invoker.grd_files_info),
         "Grd file information must be defined for $target_name")
  assert(defined(invoker.output_file_name_base),
         "Output file name base must be defined for $target_name")

  if (defined(invoker.extractor_datafile)) {
    extractor_datafile = invoker.extractor_datafile
  }

  grd_files_info = invoker.grd_files_info
  output_file_name_base = invoker.output_file_name_base

  if (defined(invoker.branding)) {
    branding = invoker.branding
  }

  first_resource_id = "1600"
  if (defined(invoker.first_resource_id)) {
    first_resource_id = invoker.first_resource_id
  }

  action(target_name) {
    script = "//base/win/embedded_i18n/create_string_rc.py"
    inputs = []

    output_dir = rebase_path(target_gen_dir, root_build_dir)

    output_header = "$target_gen_dir/$output_file_name_base.h"
    output_rc = "$target_gen_dir/$output_file_name_base.rc"

    outputs = [
      output_header,
      output_rc,
    ]

    args = [
      "--header-file",
      "$output_dir/$output_file_name_base.h",
      "--rc-file",
      "$output_dir/$output_file_name_base.rc",
      "--first-resource-id",
      first_resource_id,
    ]

    foreach(grd_file_info, grd_files_info) {
      grdfile_folder = grd_file_info[0]
      grdfile_name = grd_file_info[1]
      xtb_relative_path = grd_file_info[2]
      grd_file = "$grdfile_folder/$grdfile_name.grd"
      resource_path = "$grdfile_folder/$xtb_relative_path"
      inputs += [ grd_file ]

      args += [
        "-i",
        rebase_path(grd_file, root_build_dir),
        "-r",
        xtb_relative_path,
      ]

      # Lists must be reset to empty before being assigned a new list
      xtb_locales = []
      xtb_locales = grd_file_info[3]

      foreach(locale, xtb_locales) {
        expected_xtb_input = "${resource_path}/${grdfile_name}_${locale}.xtb"
        args += [
          "-x",
          rebase_path(expected_xtb_input, root_build_dir),
        ]
        inputs += [ expected_xtb_input ]
      }
    }

    if (defined(extractor_datafile)) {
      inputs += [ extractor_datafile ]

      args += [
        "--extract-datafile",
        rebase_path(extractor_datafile, root_build_dir),
      ]
    }

    if (defined(branding)) {
      args += [
        "-b",
        branding,
      ]
    }

    forward_variables_from(invoker, [ "deps" ])
    forward_variables_from(invoker, [ "visibility" ])
  }
}
