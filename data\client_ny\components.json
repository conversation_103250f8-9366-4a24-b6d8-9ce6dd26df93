["citizen:game:ipc", "citizen:game:main", "citizen:resources:client", "citizen:resources:core", "citizen:resources:gta", "citizen:dev<PERSON><PERSON>", "citizen:scripting:core", "citizen:game:ipc", "citizen:level-loader:ny", "citizen:resources-metadata:lua", "citizen:legacy-net:resources", "citizen:scripting:lua", "vfs:core", "vfs:impl:rage", "http-client", "debug:net", "net:base", "net:tcp-server", "net:http-server", "nui:core", "nui:gsclient", "nui:profiles", "nui:resources", "ros:patches:ny", "rage:allocator:ny", "rage:graphics:ny", "rage:device:ny", "rage:input:ny", "citizen:playernames:ny", "rage:nutsnbolts:ny", "rage:scripting:ny", "extra-natives:ny", "gta:mission-cleanup:ny", "gta:game:ny", "gta:net:ny", "gta:core:ny", "gta:streaming:ny", "scripting:gta", "font-renderer", "conhost:v2", "devcon", "net", "profiles", "steam", "glue", "scrbind:base", "adhesive"]