#
# Copyright 2011 <PERSON><PERSON><PERSON>
# Copyright 2022 <PERSON>
#
# Distributed under the Boost Software License, Version 1.0.
# https://www.boost.org/LICENSE_1_0.txt

import config : requires ;
import ../../predef/check/predef : check : predef-check ;

project : requirements
    <library>/boost/locale//boost_locale
    <warnings>extra
    <toolset>msvc:<warnings-as-errors>on
    <toolset>gcc:<cxxflags>-Wzero-as-null-pointer-constant
    [ predef-check "BOOST_COMP_GNUC >= 8.0.0" : : <cxxflags>-Wsuggest-override ]
    [ predef-check "BOOST_COMP_GNUC >= 8.0.0" and "BOOST_COMP_GNUC < 12" : : <warnings-as-errors>on ]
    <toolset>clang-5:<warnings-as-errors>on
    [ predef-check "BOOST_COMP_CLANG >= 5" : : <cxxflags>-Wzero-as-null-pointer-constant ]
    [ predef-check "BOOST_COMP_CLANG >= 9" and "BOOST_COMP_CLANG < 14" : : <warnings-as-errors>on ]
    <include>.
    # Make sure we get all defines we need
    # Otherwise we would have problem knowing
    # what backends are actually in use
    <use>../build//build_flags
    [ requires
      cxx11_char16_t
      cxx11_char32_t
    ]
  ;

# Configuration Information
run show_config.cpp
    : : : <test-info>always_show_run_output ;
# Shared
run test_catalog.cpp ;
run test_encoding.cpp ;
run test_utf.cpp ;
run test_util.cpp test_helpers.cpp ;
run test_date_time.cpp ;
run test_ios_info.cpp ;
run test_ios_prop.cpp ;
run test_codecvt.cpp ;
run test_codepage_converter.cpp ;
run test_stream_io.cpp ;
run test_message.cpp : $(BOOST_ROOT)/libs/locale/test ;
run test_generator.cpp ;
# icu
run test_collate.cpp ;
run test_convert.cpp ;
run test_boundary.cpp ;
run test_formatting.cpp : $(BOOST_ROOT)/libs/locale/test ;
run test_icu_vs_os_timezone.cpp ;
# winapi
run test_winapi_collate.cpp ;
run test_winapi_convert.cpp ;
run test_winapi_formatting.cpp ;
# posix
run test_posix_collate.cpp ;
run test_posix_convert.cpp ;
run test_posix_formatting.cpp ;
# std
run test_std_collate.cpp ;
run test_std_convert.cpp ;
run test_std_formatting.cpp ;
