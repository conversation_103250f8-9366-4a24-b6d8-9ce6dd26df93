// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.201201.7

#ifndef WINRT_FAST_FORWARD_H
#define WINRT_FAST_FORWARD_H
#include <cstddef>
#include <atomic>

#define WINRT_IMPL_STRING_1(expression) #expression
#define WINRT_IMPL_STRING(expression) WINRT_IMPL_STRING_1(expression)

#if !defined(WINRT_FAST_ABI_SIZE)
#define WINRT_FAST_ABI_SIZE 0
#endif

static_assert(WINRT_FAST_ABI_SIZE >= 0);

#pragma detect_mismatch("WINRT_FAST_ABI_SIZE", WINRT_IMPL_STRING(WINRT_FAST_ABI_SIZE))

namespace winrt::impl
{
    // Thunk definitions are in arch-specific assembly sources
    extern "C" void __stdcall winrt_ff_thunk6();
    extern "C" void __stdcall winrt_ff_thunk7();
    extern "C" void __stdcall winrt_ff_thunk8();
    extern "C" void __stdcall winrt_ff_thunk9();
    extern "C" void __stdcall winrt_ff_thunk10();
    extern "C" void __stdcall winrt_ff_thunk11();
    extern "C" void __stdcall winrt_ff_thunk12();
    extern "C" void __stdcall winrt_ff_thunk13();
    extern "C" void __stdcall winrt_ff_thunk14();
    extern "C" void __stdcall winrt_ff_thunk15();
    extern "C" void __stdcall winrt_ff_thunk16();
    extern "C" void __stdcall winrt_ff_thunk17();
    extern "C" void __stdcall winrt_ff_thunk18();
    extern "C" void __stdcall winrt_ff_thunk19();
    extern "C" void __stdcall winrt_ff_thunk20();
    extern "C" void __stdcall winrt_ff_thunk21();
    extern "C" void __stdcall winrt_ff_thunk22();
    extern "C" void __stdcall winrt_ff_thunk23();
    extern "C" void __stdcall winrt_ff_thunk24();
    extern "C" void __stdcall winrt_ff_thunk25();
    extern "C" void __stdcall winrt_ff_thunk26();
    extern "C" void __stdcall winrt_ff_thunk27();
    extern "C" void __stdcall winrt_ff_thunk28();
    extern "C" void __stdcall winrt_ff_thunk29();
    extern "C" void __stdcall winrt_ff_thunk30();
    extern "C" void __stdcall winrt_ff_thunk31();
    extern "C" void __stdcall winrt_ff_thunk32();
    extern "C" void __stdcall winrt_ff_thunk33();
    extern "C" void __stdcall winrt_ff_thunk34();
    extern "C" void __stdcall winrt_ff_thunk35();
    extern "C" void __stdcall winrt_ff_thunk36();
    extern "C" void __stdcall winrt_ff_thunk37();
    extern "C" void __stdcall winrt_ff_thunk38();
    extern "C" void __stdcall winrt_ff_thunk39();
    extern "C" void __stdcall winrt_ff_thunk40();
    extern "C" void __stdcall winrt_ff_thunk41();
    extern "C" void __stdcall winrt_ff_thunk42();
    extern "C" void __stdcall winrt_ff_thunk43();
    extern "C" void __stdcall winrt_ff_thunk44();
    extern "C" void __stdcall winrt_ff_thunk45();
    extern "C" void __stdcall winrt_ff_thunk46();
    extern "C" void __stdcall winrt_ff_thunk47();
    extern "C" void __stdcall winrt_ff_thunk48();
    extern "C" void __stdcall winrt_ff_thunk49();
    extern "C" void __stdcall winrt_ff_thunk50();
    extern "C" void __stdcall winrt_ff_thunk51();
    extern "C" void __stdcall winrt_ff_thunk52();
    extern "C" void __stdcall winrt_ff_thunk53();
    extern "C" void __stdcall winrt_ff_thunk54();
    extern "C" void __stdcall winrt_ff_thunk55();
    extern "C" void __stdcall winrt_ff_thunk56();
    extern "C" void __stdcall winrt_ff_thunk57();
    extern "C" void __stdcall winrt_ff_thunk58();
    extern "C" void __stdcall winrt_ff_thunk59();
    extern "C" void __stdcall winrt_ff_thunk60();
    extern "C" void __stdcall winrt_ff_thunk61();
    extern "C" void __stdcall winrt_ff_thunk62();
    extern "C" void __stdcall winrt_ff_thunk63();
    extern "C" void __stdcall winrt_ff_thunk64();
    extern "C" void __stdcall winrt_ff_thunk65();
    extern "C" void __stdcall winrt_ff_thunk66();
    extern "C" void __stdcall winrt_ff_thunk67();
    extern "C" void __stdcall winrt_ff_thunk68();
    extern "C" void __stdcall winrt_ff_thunk69();
    extern "C" void __stdcall winrt_ff_thunk70();
    extern "C" void __stdcall winrt_ff_thunk71();
    extern "C" void __stdcall winrt_ff_thunk72();
    extern "C" void __stdcall winrt_ff_thunk73();
    extern "C" void __stdcall winrt_ff_thunk74();
    extern "C" void __stdcall winrt_ff_thunk75();
    extern "C" void __stdcall winrt_ff_thunk76();
    extern "C" void __stdcall winrt_ff_thunk77();
    extern "C" void __stdcall winrt_ff_thunk78();
    extern "C" void __stdcall winrt_ff_thunk79();
    extern "C" void __stdcall winrt_ff_thunk80();
    extern "C" void __stdcall winrt_ff_thunk81();
    extern "C" void __stdcall winrt_ff_thunk82();
    extern "C" void __stdcall winrt_ff_thunk83();
    extern "C" void __stdcall winrt_ff_thunk84();
    extern "C" void __stdcall winrt_ff_thunk85();
    extern "C" void __stdcall winrt_ff_thunk86();
    extern "C" void __stdcall winrt_ff_thunk87();
    extern "C" void __stdcall winrt_ff_thunk88();
    extern "C" void __stdcall winrt_ff_thunk89();
    extern "C" void __stdcall winrt_ff_thunk90();
    extern "C" void __stdcall winrt_ff_thunk91();
    extern "C" void __stdcall winrt_ff_thunk92();
    extern "C" void __stdcall winrt_ff_thunk93();
    extern "C" void __stdcall winrt_ff_thunk94();
    extern "C" void __stdcall winrt_ff_thunk95();
    extern "C" void __stdcall winrt_ff_thunk96();
    extern "C" void __stdcall winrt_ff_thunk97();
    extern "C" void __stdcall winrt_ff_thunk98();
    extern "C" void __stdcall winrt_ff_thunk99();
    extern "C" void __stdcall winrt_ff_thunk100();
    extern "C" void __stdcall winrt_ff_thunk101();
    extern "C" void __stdcall winrt_ff_thunk102();
    extern "C" void __stdcall winrt_ff_thunk103();
    extern "C" void __stdcall winrt_ff_thunk104();
    extern "C" void __stdcall winrt_ff_thunk105();
    extern "C" void __stdcall winrt_ff_thunk106();
    extern "C" void __stdcall winrt_ff_thunk107();
    extern "C" void __stdcall winrt_ff_thunk108();
    extern "C" void __stdcall winrt_ff_thunk109();
    extern "C" void __stdcall winrt_ff_thunk110();
    extern "C" void __stdcall winrt_ff_thunk111();
    extern "C" void __stdcall winrt_ff_thunk112();
    extern "C" void __stdcall winrt_ff_thunk113();
    extern "C" void __stdcall winrt_ff_thunk114();
    extern "C" void __stdcall winrt_ff_thunk115();
    extern "C" void __stdcall winrt_ff_thunk116();
    extern "C" void __stdcall winrt_ff_thunk117();
    extern "C" void __stdcall winrt_ff_thunk118();
    extern "C" void __stdcall winrt_ff_thunk119();
    extern "C" void __stdcall winrt_ff_thunk120();
    extern "C" void __stdcall winrt_ff_thunk121();
    extern "C" void __stdcall winrt_ff_thunk122();
    extern "C" void __stdcall winrt_ff_thunk123();
    extern "C" void __stdcall winrt_ff_thunk124();
    extern "C" void __stdcall winrt_ff_thunk125();
    extern "C" void __stdcall winrt_ff_thunk126();
    extern "C" void __stdcall winrt_ff_thunk127();
    extern "C" void __stdcall winrt_ff_thunk128();
    extern "C" void __stdcall winrt_ff_thunk129();
    extern "C" void __stdcall winrt_ff_thunk130();
    extern "C" void __stdcall winrt_ff_thunk131();
    extern "C" void __stdcall winrt_ff_thunk132();
    extern "C" void __stdcall winrt_ff_thunk133();
    extern "C" void __stdcall winrt_ff_thunk134();
    extern "C" void __stdcall winrt_ff_thunk135();
    extern "C" void __stdcall winrt_ff_thunk136();
    extern "C" void __stdcall winrt_ff_thunk137();
    extern "C" void __stdcall winrt_ff_thunk138();
    extern "C" void __stdcall winrt_ff_thunk139();
    extern "C" void __stdcall winrt_ff_thunk140();
    extern "C" void __stdcall winrt_ff_thunk141();
    extern "C" void __stdcall winrt_ff_thunk142();
    extern "C" void __stdcall winrt_ff_thunk143();
    extern "C" void __stdcall winrt_ff_thunk144();
    extern "C" void __stdcall winrt_ff_thunk145();
    extern "C" void __stdcall winrt_ff_thunk146();
    extern "C" void __stdcall winrt_ff_thunk147();
    extern "C" void __stdcall winrt_ff_thunk148();
    extern "C" void __stdcall winrt_ff_thunk149();
    extern "C" void __stdcall winrt_ff_thunk150();
    extern "C" void __stdcall winrt_ff_thunk151();
    extern "C" void __stdcall winrt_ff_thunk152();
    extern "C" void __stdcall winrt_ff_thunk153();
    extern "C" void __stdcall winrt_ff_thunk154();
    extern "C" void __stdcall winrt_ff_thunk155();
    extern "C" void __stdcall winrt_ff_thunk156();
    extern "C" void __stdcall winrt_ff_thunk157();
    extern "C" void __stdcall winrt_ff_thunk158();
    extern "C" void __stdcall winrt_ff_thunk159();
    extern "C" void __stdcall winrt_ff_thunk160();
    extern "C" void __stdcall winrt_ff_thunk161();
    extern "C" void __stdcall winrt_ff_thunk162();
    extern "C" void __stdcall winrt_ff_thunk163();
    extern "C" void __stdcall winrt_ff_thunk164();
    extern "C" void __stdcall winrt_ff_thunk165();
    extern "C" void __stdcall winrt_ff_thunk166();
    extern "C" void __stdcall winrt_ff_thunk167();
    extern "C" void __stdcall winrt_ff_thunk168();
    extern "C" void __stdcall winrt_ff_thunk169();
    extern "C" void __stdcall winrt_ff_thunk170();
    extern "C" void __stdcall winrt_ff_thunk171();
    extern "C" void __stdcall winrt_ff_thunk172();
    extern "C" void __stdcall winrt_ff_thunk173();
    extern "C" void __stdcall winrt_ff_thunk174();
    extern "C" void __stdcall winrt_ff_thunk175();
    extern "C" void __stdcall winrt_ff_thunk176();
    extern "C" void __stdcall winrt_ff_thunk177();
    extern "C" void __stdcall winrt_ff_thunk178();
    extern "C" void __stdcall winrt_ff_thunk179();
    extern "C" void __stdcall winrt_ff_thunk180();
    extern "C" void __stdcall winrt_ff_thunk181();
    extern "C" void __stdcall winrt_ff_thunk182();
    extern "C" void __stdcall winrt_ff_thunk183();
    extern "C" void __stdcall winrt_ff_thunk184();
    extern "C" void __stdcall winrt_ff_thunk185();
    extern "C" void __stdcall winrt_ff_thunk186();
    extern "C" void __stdcall winrt_ff_thunk187();
    extern "C" void __stdcall winrt_ff_thunk188();
    extern "C" void __stdcall winrt_ff_thunk189();
    extern "C" void __stdcall winrt_ff_thunk190();
    extern "C" void __stdcall winrt_ff_thunk191();
    extern "C" void __stdcall winrt_ff_thunk192();
    extern "C" void __stdcall winrt_ff_thunk193();
    extern "C" void __stdcall winrt_ff_thunk194();
    extern "C" void __stdcall winrt_ff_thunk195();
    extern "C" void __stdcall winrt_ff_thunk196();
    extern "C" void __stdcall winrt_ff_thunk197();
    extern "C" void __stdcall winrt_ff_thunk198();
    extern "C" void __stdcall winrt_ff_thunk199();
    extern "C" void __stdcall winrt_ff_thunk200();
    extern "C" void __stdcall winrt_ff_thunk201();
    extern "C" void __stdcall winrt_ff_thunk202();
    extern "C" void __stdcall winrt_ff_thunk203();
    extern "C" void __stdcall winrt_ff_thunk204();
    extern "C" void __stdcall winrt_ff_thunk205();
    extern "C" void __stdcall winrt_ff_thunk206();
    extern "C" void __stdcall winrt_ff_thunk207();
    extern "C" void __stdcall winrt_ff_thunk208();
    extern "C" void __stdcall winrt_ff_thunk209();
    extern "C" void __stdcall winrt_ff_thunk210();
    extern "C" void __stdcall winrt_ff_thunk211();
    extern "C" void __stdcall winrt_ff_thunk212();
    extern "C" void __stdcall winrt_ff_thunk213();
    extern "C" void __stdcall winrt_ff_thunk214();
    extern "C" void __stdcall winrt_ff_thunk215();
    extern "C" void __stdcall winrt_ff_thunk216();
    extern "C" void __stdcall winrt_ff_thunk217();
    extern "C" void __stdcall winrt_ff_thunk218();
    extern "C" void __stdcall winrt_ff_thunk219();
    extern "C" void __stdcall winrt_ff_thunk220();
    extern "C" void __stdcall winrt_ff_thunk221();
    extern "C" void __stdcall winrt_ff_thunk222();
    extern "C" void __stdcall winrt_ff_thunk223();
    extern "C" void __stdcall winrt_ff_thunk224();
    extern "C" void __stdcall winrt_ff_thunk225();
    extern "C" void __stdcall winrt_ff_thunk226();
    extern "C" void __stdcall winrt_ff_thunk227();
    extern "C" void __stdcall winrt_ff_thunk228();
    extern "C" void __stdcall winrt_ff_thunk229();
    extern "C" void __stdcall winrt_ff_thunk230();
    extern "C" void __stdcall winrt_ff_thunk231();
    extern "C" void __stdcall winrt_ff_thunk232();
    extern "C" void __stdcall winrt_ff_thunk233();
    extern "C" void __stdcall winrt_ff_thunk234();
    extern "C" void __stdcall winrt_ff_thunk235();
    extern "C" void __stdcall winrt_ff_thunk236();
    extern "C" void __stdcall winrt_ff_thunk237();
    extern "C" void __stdcall winrt_ff_thunk238();
    extern "C" void __stdcall winrt_ff_thunk239();
    extern "C" void __stdcall winrt_ff_thunk240();
    extern "C" void __stdcall winrt_ff_thunk241();
    extern "C" void __stdcall winrt_ff_thunk242();
    extern "C" void __stdcall winrt_ff_thunk243();
    extern "C" void __stdcall winrt_ff_thunk244();
    extern "C" void __stdcall winrt_ff_thunk245();
    extern "C" void __stdcall winrt_ff_thunk246();
    extern "C" void __stdcall winrt_ff_thunk247();
    extern "C" void __stdcall winrt_ff_thunk248();
    extern "C" void __stdcall winrt_ff_thunk249();
    extern "C" void __stdcall winrt_ff_thunk250();
    extern "C" void __stdcall winrt_ff_thunk251();
    extern "C" void __stdcall winrt_ff_thunk252();
    extern "C" void __stdcall winrt_ff_thunk253();
    extern "C" void __stdcall winrt_ff_thunk254();
    extern "C" void __stdcall winrt_ff_thunk255();
    extern "C" void __stdcall winrt_ff_thunk256();
    extern "C" void __stdcall winrt_ff_thunk257();
    extern "C" void __stdcall winrt_ff_thunk258();
    extern "C" void __stdcall winrt_ff_thunk259();
    extern "C" void __stdcall winrt_ff_thunk260();
    extern "C" void __stdcall winrt_ff_thunk261();
    extern "C" void __stdcall winrt_ff_thunk262();
    extern "C" void __stdcall winrt_ff_thunk263();
    extern "C" void __stdcall winrt_ff_thunk264();
    extern "C" void __stdcall winrt_ff_thunk265();
    extern "C" void __stdcall winrt_ff_thunk266();
    extern "C" void __stdcall winrt_ff_thunk267();
    extern "C" void __stdcall winrt_ff_thunk268();
    extern "C" void __stdcall winrt_ff_thunk269();
    extern "C" void __stdcall winrt_ff_thunk270();
    extern "C" void __stdcall winrt_ff_thunk271();
    extern "C" void __stdcall winrt_ff_thunk272();
    extern "C" void __stdcall winrt_ff_thunk273();
    extern "C" void __stdcall winrt_ff_thunk274();
    extern "C" void __stdcall winrt_ff_thunk275();
    extern "C" void __stdcall winrt_ff_thunk276();
    extern "C" void __stdcall winrt_ff_thunk277();
    extern "C" void __stdcall winrt_ff_thunk278();
    extern "C" void __stdcall winrt_ff_thunk279();
    extern "C" void __stdcall winrt_ff_thunk280();
    extern "C" void __stdcall winrt_ff_thunk281();
    extern "C" void __stdcall winrt_ff_thunk282();
    extern "C" void __stdcall winrt_ff_thunk283();
    extern "C" void __stdcall winrt_ff_thunk284();
    extern "C" void __stdcall winrt_ff_thunk285();
    extern "C" void __stdcall winrt_ff_thunk286();
    extern "C" void __stdcall winrt_ff_thunk287();
    extern "C" void __stdcall winrt_ff_thunk288();
    extern "C" void __stdcall winrt_ff_thunk289();
    extern "C" void __stdcall winrt_ff_thunk290();
    extern "C" void __stdcall winrt_ff_thunk291();
    extern "C" void __stdcall winrt_ff_thunk292();
    extern "C" void __stdcall winrt_ff_thunk293();
    extern "C" void __stdcall winrt_ff_thunk294();
    extern "C" void __stdcall winrt_ff_thunk295();
    extern "C" void __stdcall winrt_ff_thunk296();
    extern "C" void __stdcall winrt_ff_thunk297();
    extern "C" void __stdcall winrt_ff_thunk298();
    extern "C" void __stdcall winrt_ff_thunk299();
    extern "C" void __stdcall winrt_ff_thunk300();
    extern "C" void __stdcall winrt_ff_thunk301();
    extern "C" void __stdcall winrt_ff_thunk302();
    extern "C" void __stdcall winrt_ff_thunk303();
    extern "C" void __stdcall winrt_ff_thunk304();
    extern "C" void __stdcall winrt_ff_thunk305();
    extern "C" void __stdcall winrt_ff_thunk306();
    extern "C" void __stdcall winrt_ff_thunk307();
    extern "C" void __stdcall winrt_ff_thunk308();
    extern "C" void __stdcall winrt_ff_thunk309();
    extern "C" void __stdcall winrt_ff_thunk310();
    extern "C" void __stdcall winrt_ff_thunk311();
    extern "C" void __stdcall winrt_ff_thunk312();
    extern "C" void __stdcall winrt_ff_thunk313();
    extern "C" void __stdcall winrt_ff_thunk314();
    extern "C" void __stdcall winrt_ff_thunk315();
    extern "C" void __stdcall winrt_ff_thunk316();
    extern "C" void __stdcall winrt_ff_thunk317();
    extern "C" void __stdcall winrt_ff_thunk318();
    extern "C" void __stdcall winrt_ff_thunk319();
    extern "C" void __stdcall winrt_ff_thunk320();
    extern "C" void __stdcall winrt_ff_thunk321();
    extern "C" void __stdcall winrt_ff_thunk322();
    extern "C" void __stdcall winrt_ff_thunk323();
    extern "C" void __stdcall winrt_ff_thunk324();
    extern "C" void __stdcall winrt_ff_thunk325();
    extern "C" void __stdcall winrt_ff_thunk326();
    extern "C" void __stdcall winrt_ff_thunk327();
    extern "C" void __stdcall winrt_ff_thunk328();
    extern "C" void __stdcall winrt_ff_thunk329();
    extern "C" void __stdcall winrt_ff_thunk330();
    extern "C" void __stdcall winrt_ff_thunk331();
    extern "C" void __stdcall winrt_ff_thunk332();
    extern "C" void __stdcall winrt_ff_thunk333();
    extern "C" void __stdcall winrt_ff_thunk334();
    extern "C" void __stdcall winrt_ff_thunk335();
    extern "C" void __stdcall winrt_ff_thunk336();
    extern "C" void __stdcall winrt_ff_thunk337();
    extern "C" void __stdcall winrt_ff_thunk338();
    extern "C" void __stdcall winrt_ff_thunk339();
    extern "C" void __stdcall winrt_ff_thunk340();
    extern "C" void __stdcall winrt_ff_thunk341();
    extern "C" void __stdcall winrt_ff_thunk342();
    extern "C" void __stdcall winrt_ff_thunk343();
    extern "C" void __stdcall winrt_ff_thunk344();
    extern "C" void __stdcall winrt_ff_thunk345();
    extern "C" void __stdcall winrt_ff_thunk346();
    extern "C" void __stdcall winrt_ff_thunk347();
    extern "C" void __stdcall winrt_ff_thunk348();
    extern "C" void __stdcall winrt_ff_thunk349();
    extern "C" void __stdcall winrt_ff_thunk350();
    extern "C" void __stdcall winrt_ff_thunk351();
    extern "C" void __stdcall winrt_ff_thunk352();
    extern "C" void __stdcall winrt_ff_thunk353();
    extern "C" void __stdcall winrt_ff_thunk354();
    extern "C" void __stdcall winrt_ff_thunk355();
    extern "C" void __stdcall winrt_ff_thunk356();
    extern "C" void __stdcall winrt_ff_thunk357();
    extern "C" void __stdcall winrt_ff_thunk358();
    extern "C" void __stdcall winrt_ff_thunk359();
    extern "C" void __stdcall winrt_ff_thunk360();
    extern "C" void __stdcall winrt_ff_thunk361();
    extern "C" void __stdcall winrt_ff_thunk362();
    extern "C" void __stdcall winrt_ff_thunk363();
    extern "C" void __stdcall winrt_ff_thunk364();
    extern "C" void __stdcall winrt_ff_thunk365();
    extern "C" void __stdcall winrt_ff_thunk366();
    extern "C" void __stdcall winrt_ff_thunk367();
    extern "C" void __stdcall winrt_ff_thunk368();
    extern "C" void __stdcall winrt_ff_thunk369();
    extern "C" void __stdcall winrt_ff_thunk370();
    extern "C" void __stdcall winrt_ff_thunk371();
    extern "C" void __stdcall winrt_ff_thunk372();
    extern "C" void __stdcall winrt_ff_thunk373();
    extern "C" void __stdcall winrt_ff_thunk374();
    extern "C" void __stdcall winrt_ff_thunk375();
    extern "C" void __stdcall winrt_ff_thunk376();
    extern "C" void __stdcall winrt_ff_thunk377();
    extern "C" void __stdcall winrt_ff_thunk378();
    extern "C" void __stdcall winrt_ff_thunk379();
    extern "C" void __stdcall winrt_ff_thunk380();
    extern "C" void __stdcall winrt_ff_thunk381();
    extern "C" void __stdcall winrt_ff_thunk382();
    extern "C" void __stdcall winrt_ff_thunk383();
    extern "C" void __stdcall winrt_ff_thunk384();
    extern "C" void __stdcall winrt_ff_thunk385();
    extern "C" void __stdcall winrt_ff_thunk386();
    extern "C" void __stdcall winrt_ff_thunk387();
    extern "C" void __stdcall winrt_ff_thunk388();
    extern "C" void __stdcall winrt_ff_thunk389();
    extern "C" void __stdcall winrt_ff_thunk390();
    extern "C" void __stdcall winrt_ff_thunk391();
    extern "C" void __stdcall winrt_ff_thunk392();
    extern "C" void __stdcall winrt_ff_thunk393();
    extern "C" void __stdcall winrt_ff_thunk394();
    extern "C" void __stdcall winrt_ff_thunk395();
    extern "C" void __stdcall winrt_ff_thunk396();
    extern "C" void __stdcall winrt_ff_thunk397();
    extern "C" void __stdcall winrt_ff_thunk398();
    extern "C" void __stdcall winrt_ff_thunk399();
    extern "C" void __stdcall winrt_ff_thunk400();
    extern "C" void __stdcall winrt_ff_thunk401();
    extern "C" void __stdcall winrt_ff_thunk402();
    extern "C" void __stdcall winrt_ff_thunk403();
    extern "C" void __stdcall winrt_ff_thunk404();
    extern "C" void __stdcall winrt_ff_thunk405();
    extern "C" void __stdcall winrt_ff_thunk406();
    extern "C" void __stdcall winrt_ff_thunk407();
    extern "C" void __stdcall winrt_ff_thunk408();
    extern "C" void __stdcall winrt_ff_thunk409();
    extern "C" void __stdcall winrt_ff_thunk410();
    extern "C" void __stdcall winrt_ff_thunk411();
    extern "C" void __stdcall winrt_ff_thunk412();
    extern "C" void __stdcall winrt_ff_thunk413();
    extern "C" void __stdcall winrt_ff_thunk414();
    extern "C" void __stdcall winrt_ff_thunk415();
    extern "C" void __stdcall winrt_ff_thunk416();
    extern "C" void __stdcall winrt_ff_thunk417();
    extern "C" void __stdcall winrt_ff_thunk418();
    extern "C" void __stdcall winrt_ff_thunk419();
    extern "C" void __stdcall winrt_ff_thunk420();
    extern "C" void __stdcall winrt_ff_thunk421();
    extern "C" void __stdcall winrt_ff_thunk422();
    extern "C" void __stdcall winrt_ff_thunk423();
    extern "C" void __stdcall winrt_ff_thunk424();
    extern "C" void __stdcall winrt_ff_thunk425();
    extern "C" void __stdcall winrt_ff_thunk426();
    extern "C" void __stdcall winrt_ff_thunk427();
    extern "C" void __stdcall winrt_ff_thunk428();
    extern "C" void __stdcall winrt_ff_thunk429();
    extern "C" void __stdcall winrt_ff_thunk430();
    extern "C" void __stdcall winrt_ff_thunk431();
    extern "C" void __stdcall winrt_ff_thunk432();
    extern "C" void __stdcall winrt_ff_thunk433();
    extern "C" void __stdcall winrt_ff_thunk434();
    extern "C" void __stdcall winrt_ff_thunk435();
    extern "C" void __stdcall winrt_ff_thunk436();
    extern "C" void __stdcall winrt_ff_thunk437();
    extern "C" void __stdcall winrt_ff_thunk438();
    extern "C" void __stdcall winrt_ff_thunk439();
    extern "C" void __stdcall winrt_ff_thunk440();
    extern "C" void __stdcall winrt_ff_thunk441();
    extern "C" void __stdcall winrt_ff_thunk442();
    extern "C" void __stdcall winrt_ff_thunk443();
    extern "C" void __stdcall winrt_ff_thunk444();
    extern "C" void __stdcall winrt_ff_thunk445();
    extern "C" void __stdcall winrt_ff_thunk446();
    extern "C" void __stdcall winrt_ff_thunk447();
    extern "C" void __stdcall winrt_ff_thunk448();
    extern "C" void __stdcall winrt_ff_thunk449();
    extern "C" void __stdcall winrt_ff_thunk450();
    extern "C" void __stdcall winrt_ff_thunk451();
    extern "C" void __stdcall winrt_ff_thunk452();
    extern "C" void __stdcall winrt_ff_thunk453();
    extern "C" void __stdcall winrt_ff_thunk454();
    extern "C" void __stdcall winrt_ff_thunk455();
    extern "C" void __stdcall winrt_ff_thunk456();
    extern "C" void __stdcall winrt_ff_thunk457();
    extern "C" void __stdcall winrt_ff_thunk458();
    extern "C" void __stdcall winrt_ff_thunk459();
    extern "C" void __stdcall winrt_ff_thunk460();
    extern "C" void __stdcall winrt_ff_thunk461();
    extern "C" void __stdcall winrt_ff_thunk462();
    extern "C" void __stdcall winrt_ff_thunk463();
    extern "C" void __stdcall winrt_ff_thunk464();
    extern "C" void __stdcall winrt_ff_thunk465();
    extern "C" void __stdcall winrt_ff_thunk466();
    extern "C" void __stdcall winrt_ff_thunk467();
    extern "C" void __stdcall winrt_ff_thunk468();
    extern "C" void __stdcall winrt_ff_thunk469();
    extern "C" void __stdcall winrt_ff_thunk470();
    extern "C" void __stdcall winrt_ff_thunk471();
    extern "C" void __stdcall winrt_ff_thunk472();
    extern "C" void __stdcall winrt_ff_thunk473();
    extern "C" void __stdcall winrt_ff_thunk474();
    extern "C" void __stdcall winrt_ff_thunk475();
    extern "C" void __stdcall winrt_ff_thunk476();
    extern "C" void __stdcall winrt_ff_thunk477();
    extern "C" void __stdcall winrt_ff_thunk478();
    extern "C" void __stdcall winrt_ff_thunk479();
    extern "C" void __stdcall winrt_ff_thunk480();
    extern "C" void __stdcall winrt_ff_thunk481();
    extern "C" void __stdcall winrt_ff_thunk482();
    extern "C" void __stdcall winrt_ff_thunk483();
    extern "C" void __stdcall winrt_ff_thunk484();
    extern "C" void __stdcall winrt_ff_thunk485();
    extern "C" void __stdcall winrt_ff_thunk486();
    extern "C" void __stdcall winrt_ff_thunk487();
    extern "C" void __stdcall winrt_ff_thunk488();
    extern "C" void __stdcall winrt_ff_thunk489();
    extern "C" void __stdcall winrt_ff_thunk490();
    extern "C" void __stdcall winrt_ff_thunk491();
    extern "C" void __stdcall winrt_ff_thunk492();
    extern "C" void __stdcall winrt_ff_thunk493();
    extern "C" void __stdcall winrt_ff_thunk494();
    extern "C" void __stdcall winrt_ff_thunk495();
    extern "C" void __stdcall winrt_ff_thunk496();
    extern "C" void __stdcall winrt_ff_thunk497();
    extern "C" void __stdcall winrt_ff_thunk498();
    extern "C" void __stdcall winrt_ff_thunk499();
    extern "C" void __stdcall winrt_ff_thunk500();
    extern "C" void __stdcall winrt_ff_thunk501();
    extern "C" void __stdcall winrt_ff_thunk502();
    extern "C" void __stdcall winrt_ff_thunk503();
    extern "C" void __stdcall winrt_ff_thunk504();
    extern "C" void __stdcall winrt_ff_thunk505();
    extern "C" void __stdcall winrt_ff_thunk506();
    extern "C" void __stdcall winrt_ff_thunk507();
    extern "C" void __stdcall winrt_ff_thunk508();
    extern "C" void __stdcall winrt_ff_thunk509();
    extern "C" void __stdcall winrt_ff_thunk510();
    extern "C" void __stdcall winrt_ff_thunk511();
    extern "C" void __stdcall winrt_ff_thunk512();
    extern "C" void __stdcall winrt_ff_thunk513();
    extern "C" void __stdcall winrt_ff_thunk514();
    extern "C" void __stdcall winrt_ff_thunk515();
    extern "C" void __stdcall winrt_ff_thunk516();
    extern "C" void __stdcall winrt_ff_thunk517();
    extern "C" void __stdcall winrt_ff_thunk518();
    extern "C" void __stdcall winrt_ff_thunk519();
    extern "C" void __stdcall winrt_ff_thunk520();
    extern "C" void __stdcall winrt_ff_thunk521();
    extern "C" void __stdcall winrt_ff_thunk522();
    extern "C" void __stdcall winrt_ff_thunk523();
    extern "C" void __stdcall winrt_ff_thunk524();
    extern "C" void __stdcall winrt_ff_thunk525();
    extern "C" void __stdcall winrt_ff_thunk526();
    extern "C" void __stdcall winrt_ff_thunk527();
    extern "C" void __stdcall winrt_ff_thunk528();
    extern "C" void __stdcall winrt_ff_thunk529();
    extern "C" void __stdcall winrt_ff_thunk530();
    extern "C" void __stdcall winrt_ff_thunk531();
    extern "C" void __stdcall winrt_ff_thunk532();
    extern "C" void __stdcall winrt_ff_thunk533();
    extern "C" void __stdcall winrt_ff_thunk534();
    extern "C" void __stdcall winrt_ff_thunk535();
    extern "C" void __stdcall winrt_ff_thunk536();
    extern "C" void __stdcall winrt_ff_thunk537();
    extern "C" void __stdcall winrt_ff_thunk538();
    extern "C" void __stdcall winrt_ff_thunk539();
    extern "C" void __stdcall winrt_ff_thunk540();
    extern "C" void __stdcall winrt_ff_thunk541();
    extern "C" void __stdcall winrt_ff_thunk542();
    extern "C" void __stdcall winrt_ff_thunk543();
    extern "C" void __stdcall winrt_ff_thunk544();
    extern "C" void __stdcall winrt_ff_thunk545();
    extern "C" void __stdcall winrt_ff_thunk546();
    extern "C" void __stdcall winrt_ff_thunk547();
    extern "C" void __stdcall winrt_ff_thunk548();
    extern "C" void __stdcall winrt_ff_thunk549();
    extern "C" void __stdcall winrt_ff_thunk550();
    extern "C" void __stdcall winrt_ff_thunk551();
    extern "C" void __stdcall winrt_ff_thunk552();
    extern "C" void __stdcall winrt_ff_thunk553();
    extern "C" void __stdcall winrt_ff_thunk554();
    extern "C" void __stdcall winrt_ff_thunk555();
    extern "C" void __stdcall winrt_ff_thunk556();
    extern "C" void __stdcall winrt_ff_thunk557();
    extern "C" void __stdcall winrt_ff_thunk558();
    extern "C" void __stdcall winrt_ff_thunk559();
    extern "C" void __stdcall winrt_ff_thunk560();
    extern "C" void __stdcall winrt_ff_thunk561();
    extern "C" void __stdcall winrt_ff_thunk562();
    extern "C" void __stdcall winrt_ff_thunk563();
    extern "C" void __stdcall winrt_ff_thunk564();
    extern "C" void __stdcall winrt_ff_thunk565();
    extern "C" void __stdcall winrt_ff_thunk566();
    extern "C" void __stdcall winrt_ff_thunk567();
    extern "C" void __stdcall winrt_ff_thunk568();
    extern "C" void __stdcall winrt_ff_thunk569();
    extern "C" void __stdcall winrt_ff_thunk570();
    extern "C" void __stdcall winrt_ff_thunk571();
    extern "C" void __stdcall winrt_ff_thunk572();
    extern "C" void __stdcall winrt_ff_thunk573();
    extern "C" void __stdcall winrt_ff_thunk574();
    extern "C" void __stdcall winrt_ff_thunk575();
    extern "C" void __stdcall winrt_ff_thunk576();
    extern "C" void __stdcall winrt_ff_thunk577();
    extern "C" void __stdcall winrt_ff_thunk578();
    extern "C" void __stdcall winrt_ff_thunk579();
    extern "C" void __stdcall winrt_ff_thunk580();
    extern "C" void __stdcall winrt_ff_thunk581();
    extern "C" void __stdcall winrt_ff_thunk582();
    extern "C" void __stdcall winrt_ff_thunk583();
    extern "C" void __stdcall winrt_ff_thunk584();
    extern "C" void __stdcall winrt_ff_thunk585();
    extern "C" void __stdcall winrt_ff_thunk586();
    extern "C" void __stdcall winrt_ff_thunk587();
    extern "C" void __stdcall winrt_ff_thunk588();
    extern "C" void __stdcall winrt_ff_thunk589();
    extern "C" void __stdcall winrt_ff_thunk590();
    extern "C" void __stdcall winrt_ff_thunk591();
    extern "C" void __stdcall winrt_ff_thunk592();
    extern "C" void __stdcall winrt_ff_thunk593();
    extern "C" void __stdcall winrt_ff_thunk594();
    extern "C" void __stdcall winrt_ff_thunk595();
    extern "C" void __stdcall winrt_ff_thunk596();
    extern "C" void __stdcall winrt_ff_thunk597();
    extern "C" void __stdcall winrt_ff_thunk598();
    extern "C" void __stdcall winrt_ff_thunk599();
    extern "C" void __stdcall winrt_ff_thunk600();
    extern "C" void __stdcall winrt_ff_thunk601();
    extern "C" void __stdcall winrt_ff_thunk602();
    extern "C" void __stdcall winrt_ff_thunk603();
    extern "C" void __stdcall winrt_ff_thunk604();
    extern "C" void __stdcall winrt_ff_thunk605();
    extern "C" void __stdcall winrt_ff_thunk606();
    extern "C" void __stdcall winrt_ff_thunk607();
    extern "C" void __stdcall winrt_ff_thunk608();
    extern "C" void __stdcall winrt_ff_thunk609();
    extern "C" void __stdcall winrt_ff_thunk610();
    extern "C" void __stdcall winrt_ff_thunk611();
    extern "C" void __stdcall winrt_ff_thunk612();
    extern "C" void __stdcall winrt_ff_thunk613();
    extern "C" void __stdcall winrt_ff_thunk614();
    extern "C" void __stdcall winrt_ff_thunk615();
    extern "C" void __stdcall winrt_ff_thunk616();
    extern "C" void __stdcall winrt_ff_thunk617();
    extern "C" void __stdcall winrt_ff_thunk618();
    extern "C" void __stdcall winrt_ff_thunk619();
    extern "C" void __stdcall winrt_ff_thunk620();
    extern "C" void __stdcall winrt_ff_thunk621();
    extern "C" void __stdcall winrt_ff_thunk622();
    extern "C" void __stdcall winrt_ff_thunk623();
    extern "C" void __stdcall winrt_ff_thunk624();
    extern "C" void __stdcall winrt_ff_thunk625();
    extern "C" void __stdcall winrt_ff_thunk626();
    extern "C" void __stdcall winrt_ff_thunk627();
    extern "C" void __stdcall winrt_ff_thunk628();
    extern "C" void __stdcall winrt_ff_thunk629();
    extern "C" void __stdcall winrt_ff_thunk630();
    extern "C" void __stdcall winrt_ff_thunk631();
    extern "C" void __stdcall winrt_ff_thunk632();
    extern "C" void __stdcall winrt_ff_thunk633();
    extern "C" void __stdcall winrt_ff_thunk634();
    extern "C" void __stdcall winrt_ff_thunk635();
    extern "C" void __stdcall winrt_ff_thunk636();
    extern "C" void __stdcall winrt_ff_thunk637();
    extern "C" void __stdcall winrt_ff_thunk638();
    extern "C" void __stdcall winrt_ff_thunk639();
    extern "C" void __stdcall winrt_ff_thunk640();
    extern "C" void __stdcall winrt_ff_thunk641();
    extern "C" void __stdcall winrt_ff_thunk642();
    extern "C" void __stdcall winrt_ff_thunk643();
    extern "C" void __stdcall winrt_ff_thunk644();
    extern "C" void __stdcall winrt_ff_thunk645();
    extern "C" void __stdcall winrt_ff_thunk646();
    extern "C" void __stdcall winrt_ff_thunk647();
    extern "C" void __stdcall winrt_ff_thunk648();
    extern "C" void __stdcall winrt_ff_thunk649();
    extern "C" void __stdcall winrt_ff_thunk650();
    extern "C" void __stdcall winrt_ff_thunk651();
    extern "C" void __stdcall winrt_ff_thunk652();
    extern "C" void __stdcall winrt_ff_thunk653();
    extern "C" void __stdcall winrt_ff_thunk654();
    extern "C" void __stdcall winrt_ff_thunk655();
    extern "C" void __stdcall winrt_ff_thunk656();
    extern "C" void __stdcall winrt_ff_thunk657();
    extern "C" void __stdcall winrt_ff_thunk658();
    extern "C" void __stdcall winrt_ff_thunk659();
    extern "C" void __stdcall winrt_ff_thunk660();
    extern "C" void __stdcall winrt_ff_thunk661();
    extern "C" void __stdcall winrt_ff_thunk662();
    extern "C" void __stdcall winrt_ff_thunk663();
    extern "C" void __stdcall winrt_ff_thunk664();
    extern "C" void __stdcall winrt_ff_thunk665();
    extern "C" void __stdcall winrt_ff_thunk666();
    extern "C" void __stdcall winrt_ff_thunk667();
    extern "C" void __stdcall winrt_ff_thunk668();
    extern "C" void __stdcall winrt_ff_thunk669();
    extern "C" void __stdcall winrt_ff_thunk670();
    extern "C" void __stdcall winrt_ff_thunk671();
    extern "C" void __stdcall winrt_ff_thunk672();
    extern "C" void __stdcall winrt_ff_thunk673();
    extern "C" void __stdcall winrt_ff_thunk674();
    extern "C" void __stdcall winrt_ff_thunk675();
    extern "C" void __stdcall winrt_ff_thunk676();
    extern "C" void __stdcall winrt_ff_thunk677();
    extern "C" void __stdcall winrt_ff_thunk678();
    extern "C" void __stdcall winrt_ff_thunk679();
    extern "C" void __stdcall winrt_ff_thunk680();
    extern "C" void __stdcall winrt_ff_thunk681();
    extern "C" void __stdcall winrt_ff_thunk682();
    extern "C" void __stdcall winrt_ff_thunk683();
    extern "C" void __stdcall winrt_ff_thunk684();
    extern "C" void __stdcall winrt_ff_thunk685();
    extern "C" void __stdcall winrt_ff_thunk686();
    extern "C" void __stdcall winrt_ff_thunk687();
    extern "C" void __stdcall winrt_ff_thunk688();
    extern "C" void __stdcall winrt_ff_thunk689();
    extern "C" void __stdcall winrt_ff_thunk690();
    extern "C" void __stdcall winrt_ff_thunk691();
    extern "C" void __stdcall winrt_ff_thunk692();
    extern "C" void __stdcall winrt_ff_thunk693();
    extern "C" void __stdcall winrt_ff_thunk694();
    extern "C" void __stdcall winrt_ff_thunk695();
    extern "C" void __stdcall winrt_ff_thunk696();
    extern "C" void __stdcall winrt_ff_thunk697();
    extern "C" void __stdcall winrt_ff_thunk698();
    extern "C" void __stdcall winrt_ff_thunk699();
    extern "C" void __stdcall winrt_ff_thunk700();
    extern "C" void __stdcall winrt_ff_thunk701();
    extern "C" void __stdcall winrt_ff_thunk702();
    extern "C" void __stdcall winrt_ff_thunk703();
    extern "C" void __stdcall winrt_ff_thunk704();
    extern "C" void __stdcall winrt_ff_thunk705();
    extern "C" void __stdcall winrt_ff_thunk706();
    extern "C" void __stdcall winrt_ff_thunk707();
    extern "C" void __stdcall winrt_ff_thunk708();
    extern "C" void __stdcall winrt_ff_thunk709();
    extern "C" void __stdcall winrt_ff_thunk710();
    extern "C" void __stdcall winrt_ff_thunk711();
    extern "C" void __stdcall winrt_ff_thunk712();
    extern "C" void __stdcall winrt_ff_thunk713();
    extern "C" void __stdcall winrt_ff_thunk714();
    extern "C" void __stdcall winrt_ff_thunk715();
    extern "C" void __stdcall winrt_ff_thunk716();
    extern "C" void __stdcall winrt_ff_thunk717();
    extern "C" void __stdcall winrt_ff_thunk718();
    extern "C" void __stdcall winrt_ff_thunk719();
    extern "C" void __stdcall winrt_ff_thunk720();
    extern "C" void __stdcall winrt_ff_thunk721();
    extern "C" void __stdcall winrt_ff_thunk722();
    extern "C" void __stdcall winrt_ff_thunk723();
    extern "C" void __stdcall winrt_ff_thunk724();
    extern "C" void __stdcall winrt_ff_thunk725();
    extern "C" void __stdcall winrt_ff_thunk726();
    extern "C" void __stdcall winrt_ff_thunk727();
    extern "C" void __stdcall winrt_ff_thunk728();
    extern "C" void __stdcall winrt_ff_thunk729();
    extern "C" void __stdcall winrt_ff_thunk730();
    extern "C" void __stdcall winrt_ff_thunk731();
    extern "C" void __stdcall winrt_ff_thunk732();
    extern "C" void __stdcall winrt_ff_thunk733();
    extern "C" void __stdcall winrt_ff_thunk734();
    extern "C" void __stdcall winrt_ff_thunk735();
    extern "C" void __stdcall winrt_ff_thunk736();
    extern "C" void __stdcall winrt_ff_thunk737();
    extern "C" void __stdcall winrt_ff_thunk738();
    extern "C" void __stdcall winrt_ff_thunk739();
    extern "C" void __stdcall winrt_ff_thunk740();
    extern "C" void __stdcall winrt_ff_thunk741();
    extern "C" void __stdcall winrt_ff_thunk742();
    extern "C" void __stdcall winrt_ff_thunk743();
    extern "C" void __stdcall winrt_ff_thunk744();
    extern "C" void __stdcall winrt_ff_thunk745();
    extern "C" void __stdcall winrt_ff_thunk746();
    extern "C" void __stdcall winrt_ff_thunk747();
    extern "C" void __stdcall winrt_ff_thunk748();
    extern "C" void __stdcall winrt_ff_thunk749();
    extern "C" void __stdcall winrt_ff_thunk750();
    extern "C" void __stdcall winrt_ff_thunk751();
    extern "C" void __stdcall winrt_ff_thunk752();
    extern "C" void __stdcall winrt_ff_thunk753();
    extern "C" void __stdcall winrt_ff_thunk754();
    extern "C" void __stdcall winrt_ff_thunk755();
    extern "C" void __stdcall winrt_ff_thunk756();
    extern "C" void __stdcall winrt_ff_thunk757();
    extern "C" void __stdcall winrt_ff_thunk758();
    extern "C" void __stdcall winrt_ff_thunk759();
    extern "C" void __stdcall winrt_ff_thunk760();
    extern "C" void __stdcall winrt_ff_thunk761();
    extern "C" void __stdcall winrt_ff_thunk762();
    extern "C" void __stdcall winrt_ff_thunk763();
    extern "C" void __stdcall winrt_ff_thunk764();
    extern "C" void __stdcall winrt_ff_thunk765();
    extern "C" void __stdcall winrt_ff_thunk766();
    extern "C" void __stdcall winrt_ff_thunk767();
    extern "C" void __stdcall winrt_ff_thunk768();
    extern "C" void __stdcall winrt_ff_thunk769();
    extern "C" void __stdcall winrt_ff_thunk770();
    extern "C" void __stdcall winrt_ff_thunk771();
    extern "C" void __stdcall winrt_ff_thunk772();
    extern "C" void __stdcall winrt_ff_thunk773();
    extern "C" void __stdcall winrt_ff_thunk774();
    extern "C" void __stdcall winrt_ff_thunk775();
    extern "C" void __stdcall winrt_ff_thunk776();
    extern "C" void __stdcall winrt_ff_thunk777();
    extern "C" void __stdcall winrt_ff_thunk778();
    extern "C" void __stdcall winrt_ff_thunk779();
    extern "C" void __stdcall winrt_ff_thunk780();
    extern "C" void __stdcall winrt_ff_thunk781();
    extern "C" void __stdcall winrt_ff_thunk782();
    extern "C" void __stdcall winrt_ff_thunk783();
    extern "C" void __stdcall winrt_ff_thunk784();
    extern "C" void __stdcall winrt_ff_thunk785();
    extern "C" void __stdcall winrt_ff_thunk786();
    extern "C" void __stdcall winrt_ff_thunk787();
    extern "C" void __stdcall winrt_ff_thunk788();
    extern "C" void __stdcall winrt_ff_thunk789();
    extern "C" void __stdcall winrt_ff_thunk790();
    extern "C" void __stdcall winrt_ff_thunk791();
    extern "C" void __stdcall winrt_ff_thunk792();
    extern "C" void __stdcall winrt_ff_thunk793();
    extern "C" void __stdcall winrt_ff_thunk794();
    extern "C" void __stdcall winrt_ff_thunk795();
    extern "C" void __stdcall winrt_ff_thunk796();
    extern "C" void __stdcall winrt_ff_thunk797();
    extern "C" void __stdcall winrt_ff_thunk798();
    extern "C" void __stdcall winrt_ff_thunk799();
    extern "C" void __stdcall winrt_ff_thunk800();
    extern "C" void __stdcall winrt_ff_thunk801();
    extern "C" void __stdcall winrt_ff_thunk802();
    extern "C" void __stdcall winrt_ff_thunk803();
    extern "C" void __stdcall winrt_ff_thunk804();
    extern "C" void __stdcall winrt_ff_thunk805();
    extern "C" void __stdcall winrt_ff_thunk806();
    extern "C" void __stdcall winrt_ff_thunk807();
    extern "C" void __stdcall winrt_ff_thunk808();
    extern "C" void __stdcall winrt_ff_thunk809();
    extern "C" void __stdcall winrt_ff_thunk810();
    extern "C" void __stdcall winrt_ff_thunk811();
    extern "C" void __stdcall winrt_ff_thunk812();
    extern "C" void __stdcall winrt_ff_thunk813();
    extern "C" void __stdcall winrt_ff_thunk814();
    extern "C" void __stdcall winrt_ff_thunk815();
    extern "C" void __stdcall winrt_ff_thunk816();
    extern "C" void __stdcall winrt_ff_thunk817();
    extern "C" void __stdcall winrt_ff_thunk818();
    extern "C" void __stdcall winrt_ff_thunk819();
    extern "C" void __stdcall winrt_ff_thunk820();
    extern "C" void __stdcall winrt_ff_thunk821();
    extern "C" void __stdcall winrt_ff_thunk822();
    extern "C" void __stdcall winrt_ff_thunk823();
    extern "C" void __stdcall winrt_ff_thunk824();
    extern "C" void __stdcall winrt_ff_thunk825();
    extern "C" void __stdcall winrt_ff_thunk826();
    extern "C" void __stdcall winrt_ff_thunk827();
    extern "C" void __stdcall winrt_ff_thunk828();
    extern "C" void __stdcall winrt_ff_thunk829();
    extern "C" void __stdcall winrt_ff_thunk830();
    extern "C" void __stdcall winrt_ff_thunk831();
    extern "C" void __stdcall winrt_ff_thunk832();
    extern "C" void __stdcall winrt_ff_thunk833();
    extern "C" void __stdcall winrt_ff_thunk834();
    extern "C" void __stdcall winrt_ff_thunk835();
    extern "C" void __stdcall winrt_ff_thunk836();
    extern "C" void __stdcall winrt_ff_thunk837();
    extern "C" void __stdcall winrt_ff_thunk838();
    extern "C" void __stdcall winrt_ff_thunk839();
    extern "C" void __stdcall winrt_ff_thunk840();
    extern "C" void __stdcall winrt_ff_thunk841();
    extern "C" void __stdcall winrt_ff_thunk842();
    extern "C" void __stdcall winrt_ff_thunk843();
    extern "C" void __stdcall winrt_ff_thunk844();
    extern "C" void __stdcall winrt_ff_thunk845();
    extern "C" void __stdcall winrt_ff_thunk846();
    extern "C" void __stdcall winrt_ff_thunk847();
    extern "C" void __stdcall winrt_ff_thunk848();
    extern "C" void __stdcall winrt_ff_thunk849();
    extern "C" void __stdcall winrt_ff_thunk850();
    extern "C" void __stdcall winrt_ff_thunk851();
    extern "C" void __stdcall winrt_ff_thunk852();
    extern "C" void __stdcall winrt_ff_thunk853();
    extern "C" void __stdcall winrt_ff_thunk854();
    extern "C" void __stdcall winrt_ff_thunk855();
    extern "C" void __stdcall winrt_ff_thunk856();
    extern "C" void __stdcall winrt_ff_thunk857();
    extern "C" void __stdcall winrt_ff_thunk858();
    extern "C" void __stdcall winrt_ff_thunk859();
    extern "C" void __stdcall winrt_ff_thunk860();
    extern "C" void __stdcall winrt_ff_thunk861();
    extern "C" void __stdcall winrt_ff_thunk862();
    extern "C" void __stdcall winrt_ff_thunk863();
    extern "C" void __stdcall winrt_ff_thunk864();
    extern "C" void __stdcall winrt_ff_thunk865();
    extern "C" void __stdcall winrt_ff_thunk866();
    extern "C" void __stdcall winrt_ff_thunk867();
    extern "C" void __stdcall winrt_ff_thunk868();
    extern "C" void __stdcall winrt_ff_thunk869();
    extern "C" void __stdcall winrt_ff_thunk870();
    extern "C" void __stdcall winrt_ff_thunk871();
    extern "C" void __stdcall winrt_ff_thunk872();
    extern "C" void __stdcall winrt_ff_thunk873();
    extern "C" void __stdcall winrt_ff_thunk874();
    extern "C" void __stdcall winrt_ff_thunk875();
    extern "C" void __stdcall winrt_ff_thunk876();
    extern "C" void __stdcall winrt_ff_thunk877();
    extern "C" void __stdcall winrt_ff_thunk878();
    extern "C" void __stdcall winrt_ff_thunk879();
    extern "C" void __stdcall winrt_ff_thunk880();
    extern "C" void __stdcall winrt_ff_thunk881();
    extern "C" void __stdcall winrt_ff_thunk882();
    extern "C" void __stdcall winrt_ff_thunk883();
    extern "C" void __stdcall winrt_ff_thunk884();
    extern "C" void __stdcall winrt_ff_thunk885();
    extern "C" void __stdcall winrt_ff_thunk886();
    extern "C" void __stdcall winrt_ff_thunk887();
    extern "C" void __stdcall winrt_ff_thunk888();
    extern "C" void __stdcall winrt_ff_thunk889();
    extern "C" void __stdcall winrt_ff_thunk890();
    extern "C" void __stdcall winrt_ff_thunk891();
    extern "C" void __stdcall winrt_ff_thunk892();
    extern "C" void __stdcall winrt_ff_thunk893();
    extern "C" void __stdcall winrt_ff_thunk894();
    extern "C" void __stdcall winrt_ff_thunk895();
    extern "C" void __stdcall winrt_ff_thunk896();
    extern "C" void __stdcall winrt_ff_thunk897();
    extern "C" void __stdcall winrt_ff_thunk898();
    extern "C" void __stdcall winrt_ff_thunk899();
    extern "C" void __stdcall winrt_ff_thunk900();
    extern "C" void __stdcall winrt_ff_thunk901();
    extern "C" void __stdcall winrt_ff_thunk902();
    extern "C" void __stdcall winrt_ff_thunk903();
    extern "C" void __stdcall winrt_ff_thunk904();
    extern "C" void __stdcall winrt_ff_thunk905();
    extern "C" void __stdcall winrt_ff_thunk906();
    extern "C" void __stdcall winrt_ff_thunk907();
    extern "C" void __stdcall winrt_ff_thunk908();
    extern "C" void __stdcall winrt_ff_thunk909();
    extern "C" void __stdcall winrt_ff_thunk910();
    extern "C" void __stdcall winrt_ff_thunk911();
    extern "C" void __stdcall winrt_ff_thunk912();
    extern "C" void __stdcall winrt_ff_thunk913();
    extern "C" void __stdcall winrt_ff_thunk914();
    extern "C" void __stdcall winrt_ff_thunk915();
    extern "C" void __stdcall winrt_ff_thunk916();
    extern "C" void __stdcall winrt_ff_thunk917();
    extern "C" void __stdcall winrt_ff_thunk918();
    extern "C" void __stdcall winrt_ff_thunk919();
    extern "C" void __stdcall winrt_ff_thunk920();
    extern "C" void __stdcall winrt_ff_thunk921();
    extern "C" void __stdcall winrt_ff_thunk922();
    extern "C" void __stdcall winrt_ff_thunk923();
    extern "C" void __stdcall winrt_ff_thunk924();
    extern "C" void __stdcall winrt_ff_thunk925();
    extern "C" void __stdcall winrt_ff_thunk926();
    extern "C" void __stdcall winrt_ff_thunk927();
    extern "C" void __stdcall winrt_ff_thunk928();
    extern "C" void __stdcall winrt_ff_thunk929();
    extern "C" void __stdcall winrt_ff_thunk930();
    extern "C" void __stdcall winrt_ff_thunk931();
    extern "C" void __stdcall winrt_ff_thunk932();
    extern "C" void __stdcall winrt_ff_thunk933();
    extern "C" void __stdcall winrt_ff_thunk934();
    extern "C" void __stdcall winrt_ff_thunk935();
    extern "C" void __stdcall winrt_ff_thunk936();
    extern "C" void __stdcall winrt_ff_thunk937();
    extern "C" void __stdcall winrt_ff_thunk938();
    extern "C" void __stdcall winrt_ff_thunk939();
    extern "C" void __stdcall winrt_ff_thunk940();
    extern "C" void __stdcall winrt_ff_thunk941();
    extern "C" void __stdcall winrt_ff_thunk942();
    extern "C" void __stdcall winrt_ff_thunk943();
    extern "C" void __stdcall winrt_ff_thunk944();
    extern "C" void __stdcall winrt_ff_thunk945();
    extern "C" void __stdcall winrt_ff_thunk946();
    extern "C" void __stdcall winrt_ff_thunk947();
    extern "C" void __stdcall winrt_ff_thunk948();
    extern "C" void __stdcall winrt_ff_thunk949();
    extern "C" void __stdcall winrt_ff_thunk950();
    extern "C" void __stdcall winrt_ff_thunk951();
    extern "C" void __stdcall winrt_ff_thunk952();
    extern "C" void __stdcall winrt_ff_thunk953();
    extern "C" void __stdcall winrt_ff_thunk954();
    extern "C" void __stdcall winrt_ff_thunk955();
    extern "C" void __stdcall winrt_ff_thunk956();
    extern "C" void __stdcall winrt_ff_thunk957();
    extern "C" void __stdcall winrt_ff_thunk958();
    extern "C" void __stdcall winrt_ff_thunk959();
    extern "C" void __stdcall winrt_ff_thunk960();
    extern "C" void __stdcall winrt_ff_thunk961();
    extern "C" void __stdcall winrt_ff_thunk962();
    extern "C" void __stdcall winrt_ff_thunk963();
    extern "C" void __stdcall winrt_ff_thunk964();
    extern "C" void __stdcall winrt_ff_thunk965();
    extern "C" void __stdcall winrt_ff_thunk966();
    extern "C" void __stdcall winrt_ff_thunk967();
    extern "C" void __stdcall winrt_ff_thunk968();
    extern "C" void __stdcall winrt_ff_thunk969();
    extern "C" void __stdcall winrt_ff_thunk970();
    extern "C" void __stdcall winrt_ff_thunk971();
    extern "C" void __stdcall winrt_ff_thunk972();
    extern "C" void __stdcall winrt_ff_thunk973();
    extern "C" void __stdcall winrt_ff_thunk974();
    extern "C" void __stdcall winrt_ff_thunk975();
    extern "C" void __stdcall winrt_ff_thunk976();
    extern "C" void __stdcall winrt_ff_thunk977();
    extern "C" void __stdcall winrt_ff_thunk978();
    extern "C" void __stdcall winrt_ff_thunk979();
    extern "C" void __stdcall winrt_ff_thunk980();
    extern "C" void __stdcall winrt_ff_thunk981();
    extern "C" void __stdcall winrt_ff_thunk982();
    extern "C" void __stdcall winrt_ff_thunk983();
    extern "C" void __stdcall winrt_ff_thunk984();
    extern "C" void __stdcall winrt_ff_thunk985();
    extern "C" void __stdcall winrt_ff_thunk986();
    extern "C" void __stdcall winrt_ff_thunk987();
    extern "C" void __stdcall winrt_ff_thunk988();
    extern "C" void __stdcall winrt_ff_thunk989();
    extern "C" void __stdcall winrt_ff_thunk990();
    extern "C" void __stdcall winrt_ff_thunk991();
    extern "C" void __stdcall winrt_ff_thunk992();
    extern "C" void __stdcall winrt_ff_thunk993();
    extern "C" void __stdcall winrt_ff_thunk994();
    extern "C" void __stdcall winrt_ff_thunk995();
    extern "C" void __stdcall winrt_ff_thunk996();
    extern "C" void __stdcall winrt_ff_thunk997();
    extern "C" void __stdcall winrt_ff_thunk998();
    extern "C" void __stdcall winrt_ff_thunk999();
    extern "C" void __stdcall winrt_ff_thunk1000();
    extern "C" void __stdcall winrt_ff_thunk1001();
    extern "C" void __stdcall winrt_ff_thunk1002();
    extern "C" void __stdcall winrt_ff_thunk1003();
    extern "C" void __stdcall winrt_ff_thunk1004();
    extern "C" void __stdcall winrt_ff_thunk1005();
    extern "C" void __stdcall winrt_ff_thunk1006();
    extern "C" void __stdcall winrt_ff_thunk1007();
    extern "C" void __stdcall winrt_ff_thunk1008();
    extern "C" void __stdcall winrt_ff_thunk1009();
    extern "C" void __stdcall winrt_ff_thunk1010();
    extern "C" void __stdcall winrt_ff_thunk1011();
    extern "C" void __stdcall winrt_ff_thunk1012();
    extern "C" void __stdcall winrt_ff_thunk1013();
    extern "C" void __stdcall winrt_ff_thunk1014();
    extern "C" void __stdcall winrt_ff_thunk1015();
    extern "C" void __stdcall winrt_ff_thunk1016();
    extern "C" void __stdcall winrt_ff_thunk1017();
    extern "C" void __stdcall winrt_ff_thunk1018();
    extern "C" void __stdcall winrt_ff_thunk1019();
    extern "C" void __stdcall winrt_ff_thunk1020();
    extern "C" void __stdcall winrt_ff_thunk1021();
    extern "C" void __stdcall winrt_ff_thunk1022();
    extern "C" void __stdcall winrt_ff_thunk1023();

    struct fast_abi_forwarder
    {
        struct guid
        {
            uint32_t Data1;
            uint16_t Data2;
            uint16_t Data3;
            uint8_t  Data4[8];
            inline bool operator!=(guid const& right) const noexcept
            {
                return memcmp(this, &right, sizeof(guid));
            }
        };

        struct __declspec(novtable) inspectable
        {
            virtual int32_t __stdcall QueryInterface(guid const& id, void** object) noexcept = 0;
            virtual uint32_t __stdcall AddRef() noexcept = 0;
            virtual uint32_t __stdcall Release() noexcept = 0;
            virtual int32_t __stdcall GetIids(uint32_t* count, guid** ids) noexcept = 0;
            virtual int32_t __stdcall GetRuntimeClassName(void** name) noexcept = 0;
            virtual int32_t __stdcall GetTrustLevel(uint32_t* level) noexcept = 0;
        };

        void* const* m_vfptr;
        inspectable* m_owner;
        std::size_t m_offset;
        guid m_iid;
        std::atomic<uint32_t> m_references{ 1 };

        fast_abi_forwarder(void* owner, guid const& iid, std::size_t offset) noexcept :
            m_vfptr(s_vtable), m_owner(static_cast<inspectable*>(owner)), m_iid(iid), m_offset(offset)
        {
            m_owner->AddRef();
        }

        ~fast_abi_forwarder() noexcept
        {
            m_owner->Release();
        }

        static int32_t __stdcall QueryInterface(fast_abi_forwarder* self, guid const& iid, void** object) noexcept
        {
            if (iid != self->m_iid)
            {
                return self->m_owner->QueryInterface(iid, object);
            }
            AddRef(self);
            *object = self;
            return 0;
        }

        // Note: COM interfaces use stdcall, not thiscall, ('this' gets no special treatment), permitting static implementations
        static uint32_t __stdcall AddRef(fast_abi_forwarder* self) noexcept
        {
            return 1 + self->m_references.fetch_add(1, std::memory_order_relaxed);
        }

        static uint32_t __stdcall Release(fast_abi_forwarder* self) noexcept
        {
            uint32_t const remaining = self->m_references.fetch_sub(1, std::memory_order_release) - 1;
            if (remaining == 0)
            {
                std::atomic_thread_fence(std::memory_order_acquire);
                delete self;
            }
            return remaining;
        }

        static uint32_t __stdcall GetIids(fast_abi_forwarder* self, uint32_t* count, guid** iids) noexcept
        {
            return self->m_owner->GetIids(count, iids);
        }

        static uint32_t __stdcall GetRuntimeClassName(fast_abi_forwarder* self, void** name) noexcept
        {
            return self->m_owner->GetRuntimeClassName(name);
        }

        static uint32_t __stdcall GetTrustLevel(fast_abi_forwarder* self, uint32_t* level) noexcept
        {
            return self->m_owner->GetTrustLevel(level);
        }

        static inline void* const s_vtable[] =
        {
            QueryInterface,
            AddRef,
            Release,
            GetIids,
            GetRuntimeClassName,
            GetTrustLevel,

#if WINRT_FAST_ABI_SIZE > 6
            winrt_ff_thunk6,
#endif

#if WINRT_FAST_ABI_SIZE > 7
            winrt_ff_thunk7,
#endif

#if WINRT_FAST_ABI_SIZE > 8
            winrt_ff_thunk8,
#endif

#if WINRT_FAST_ABI_SIZE > 9
            winrt_ff_thunk9,
#endif

#if WINRT_FAST_ABI_SIZE > 10
            winrt_ff_thunk10,
#endif

#if WINRT_FAST_ABI_SIZE > 11
            winrt_ff_thunk11,
#endif

#if WINRT_FAST_ABI_SIZE > 12
            winrt_ff_thunk12,
#endif

#if WINRT_FAST_ABI_SIZE > 13
            winrt_ff_thunk13,
#endif

#if WINRT_FAST_ABI_SIZE > 14
            winrt_ff_thunk14,
#endif

#if WINRT_FAST_ABI_SIZE > 15
            winrt_ff_thunk15,
#endif

#if WINRT_FAST_ABI_SIZE > 16
            winrt_ff_thunk16,
#endif

#if WINRT_FAST_ABI_SIZE > 17
            winrt_ff_thunk17,
#endif

#if WINRT_FAST_ABI_SIZE > 18
            winrt_ff_thunk18,
#endif

#if WINRT_FAST_ABI_SIZE > 19
            winrt_ff_thunk19,
#endif

#if WINRT_FAST_ABI_SIZE > 20
            winrt_ff_thunk20,
#endif

#if WINRT_FAST_ABI_SIZE > 21
            winrt_ff_thunk21,
#endif

#if WINRT_FAST_ABI_SIZE > 22
            winrt_ff_thunk22,
#endif

#if WINRT_FAST_ABI_SIZE > 23
            winrt_ff_thunk23,
#endif

#if WINRT_FAST_ABI_SIZE > 24
            winrt_ff_thunk24,
#endif

#if WINRT_FAST_ABI_SIZE > 25
            winrt_ff_thunk25,
#endif

#if WINRT_FAST_ABI_SIZE > 26
            winrt_ff_thunk26,
#endif

#if WINRT_FAST_ABI_SIZE > 27
            winrt_ff_thunk27,
#endif

#if WINRT_FAST_ABI_SIZE > 28
            winrt_ff_thunk28,
#endif

#if WINRT_FAST_ABI_SIZE > 29
            winrt_ff_thunk29,
#endif

#if WINRT_FAST_ABI_SIZE > 30
            winrt_ff_thunk30,
#endif

#if WINRT_FAST_ABI_SIZE > 31
            winrt_ff_thunk31,
#endif

#if WINRT_FAST_ABI_SIZE > 32
            winrt_ff_thunk32,
#endif

#if WINRT_FAST_ABI_SIZE > 33
            winrt_ff_thunk33,
#endif

#if WINRT_FAST_ABI_SIZE > 34
            winrt_ff_thunk34,
#endif

#if WINRT_FAST_ABI_SIZE > 35
            winrt_ff_thunk35,
#endif

#if WINRT_FAST_ABI_SIZE > 36
            winrt_ff_thunk36,
#endif

#if WINRT_FAST_ABI_SIZE > 37
            winrt_ff_thunk37,
#endif

#if WINRT_FAST_ABI_SIZE > 38
            winrt_ff_thunk38,
#endif

#if WINRT_FAST_ABI_SIZE > 39
            winrt_ff_thunk39,
#endif

#if WINRT_FAST_ABI_SIZE > 40
            winrt_ff_thunk40,
#endif

#if WINRT_FAST_ABI_SIZE > 41
            winrt_ff_thunk41,
#endif

#if WINRT_FAST_ABI_SIZE > 42
            winrt_ff_thunk42,
#endif

#if WINRT_FAST_ABI_SIZE > 43
            winrt_ff_thunk43,
#endif

#if WINRT_FAST_ABI_SIZE > 44
            winrt_ff_thunk44,
#endif

#if WINRT_FAST_ABI_SIZE > 45
            winrt_ff_thunk45,
#endif

#if WINRT_FAST_ABI_SIZE > 46
            winrt_ff_thunk46,
#endif

#if WINRT_FAST_ABI_SIZE > 47
            winrt_ff_thunk47,
#endif

#if WINRT_FAST_ABI_SIZE > 48
            winrt_ff_thunk48,
#endif

#if WINRT_FAST_ABI_SIZE > 49
            winrt_ff_thunk49,
#endif

#if WINRT_FAST_ABI_SIZE > 50
            winrt_ff_thunk50,
#endif

#if WINRT_FAST_ABI_SIZE > 51
            winrt_ff_thunk51,
#endif

#if WINRT_FAST_ABI_SIZE > 52
            winrt_ff_thunk52,
#endif

#if WINRT_FAST_ABI_SIZE > 53
            winrt_ff_thunk53,
#endif

#if WINRT_FAST_ABI_SIZE > 54
            winrt_ff_thunk54,
#endif

#if WINRT_FAST_ABI_SIZE > 55
            winrt_ff_thunk55,
#endif

#if WINRT_FAST_ABI_SIZE > 56
            winrt_ff_thunk56,
#endif

#if WINRT_FAST_ABI_SIZE > 57
            winrt_ff_thunk57,
#endif

#if WINRT_FAST_ABI_SIZE > 58
            winrt_ff_thunk58,
#endif

#if WINRT_FAST_ABI_SIZE > 59
            winrt_ff_thunk59,
#endif

#if WINRT_FAST_ABI_SIZE > 60
            winrt_ff_thunk60,
#endif

#if WINRT_FAST_ABI_SIZE > 61
            winrt_ff_thunk61,
#endif

#if WINRT_FAST_ABI_SIZE > 62
            winrt_ff_thunk62,
#endif

#if WINRT_FAST_ABI_SIZE > 63
            winrt_ff_thunk63,
#endif

#if WINRT_FAST_ABI_SIZE > 64
            winrt_ff_thunk64,
#endif

#if WINRT_FAST_ABI_SIZE > 65
            winrt_ff_thunk65,
#endif

#if WINRT_FAST_ABI_SIZE > 66
            winrt_ff_thunk66,
#endif

#if WINRT_FAST_ABI_SIZE > 67
            winrt_ff_thunk67,
#endif

#if WINRT_FAST_ABI_SIZE > 68
            winrt_ff_thunk68,
#endif

#if WINRT_FAST_ABI_SIZE > 69
            winrt_ff_thunk69,
#endif

#if WINRT_FAST_ABI_SIZE > 70
            winrt_ff_thunk70,
#endif

#if WINRT_FAST_ABI_SIZE > 71
            winrt_ff_thunk71,
#endif

#if WINRT_FAST_ABI_SIZE > 72
            winrt_ff_thunk72,
#endif

#if WINRT_FAST_ABI_SIZE > 73
            winrt_ff_thunk73,
#endif

#if WINRT_FAST_ABI_SIZE > 74
            winrt_ff_thunk74,
#endif

#if WINRT_FAST_ABI_SIZE > 75
            winrt_ff_thunk75,
#endif

#if WINRT_FAST_ABI_SIZE > 76
            winrt_ff_thunk76,
#endif

#if WINRT_FAST_ABI_SIZE > 77
            winrt_ff_thunk77,
#endif

#if WINRT_FAST_ABI_SIZE > 78
            winrt_ff_thunk78,
#endif

#if WINRT_FAST_ABI_SIZE > 79
            winrt_ff_thunk79,
#endif

#if WINRT_FAST_ABI_SIZE > 80
            winrt_ff_thunk80,
#endif

#if WINRT_FAST_ABI_SIZE > 81
            winrt_ff_thunk81,
#endif

#if WINRT_FAST_ABI_SIZE > 82
            winrt_ff_thunk82,
#endif

#if WINRT_FAST_ABI_SIZE > 83
            winrt_ff_thunk83,
#endif

#if WINRT_FAST_ABI_SIZE > 84
            winrt_ff_thunk84,
#endif

#if WINRT_FAST_ABI_SIZE > 85
            winrt_ff_thunk85,
#endif

#if WINRT_FAST_ABI_SIZE > 86
            winrt_ff_thunk86,
#endif

#if WINRT_FAST_ABI_SIZE > 87
            winrt_ff_thunk87,
#endif

#if WINRT_FAST_ABI_SIZE > 88
            winrt_ff_thunk88,
#endif

#if WINRT_FAST_ABI_SIZE > 89
            winrt_ff_thunk89,
#endif

#if WINRT_FAST_ABI_SIZE > 90
            winrt_ff_thunk90,
#endif

#if WINRT_FAST_ABI_SIZE > 91
            winrt_ff_thunk91,
#endif

#if WINRT_FAST_ABI_SIZE > 92
            winrt_ff_thunk92,
#endif

#if WINRT_FAST_ABI_SIZE > 93
            winrt_ff_thunk93,
#endif

#if WINRT_FAST_ABI_SIZE > 94
            winrt_ff_thunk94,
#endif

#if WINRT_FAST_ABI_SIZE > 95
            winrt_ff_thunk95,
#endif

#if WINRT_FAST_ABI_SIZE > 96
            winrt_ff_thunk96,
#endif

#if WINRT_FAST_ABI_SIZE > 97
            winrt_ff_thunk97,
#endif

#if WINRT_FAST_ABI_SIZE > 98
            winrt_ff_thunk98,
#endif

#if WINRT_FAST_ABI_SIZE > 99
            winrt_ff_thunk99,
#endif

#if WINRT_FAST_ABI_SIZE > 100
            winrt_ff_thunk100,
#endif

#if WINRT_FAST_ABI_SIZE > 101
            winrt_ff_thunk101,
#endif

#if WINRT_FAST_ABI_SIZE > 102
            winrt_ff_thunk102,
#endif

#if WINRT_FAST_ABI_SIZE > 103
            winrt_ff_thunk103,
#endif

#if WINRT_FAST_ABI_SIZE > 104
            winrt_ff_thunk104,
#endif

#if WINRT_FAST_ABI_SIZE > 105
            winrt_ff_thunk105,
#endif

#if WINRT_FAST_ABI_SIZE > 106
            winrt_ff_thunk106,
#endif

#if WINRT_FAST_ABI_SIZE > 107
            winrt_ff_thunk107,
#endif

#if WINRT_FAST_ABI_SIZE > 108
            winrt_ff_thunk108,
#endif

#if WINRT_FAST_ABI_SIZE > 109
            winrt_ff_thunk109,
#endif

#if WINRT_FAST_ABI_SIZE > 110
            winrt_ff_thunk110,
#endif

#if WINRT_FAST_ABI_SIZE > 111
            winrt_ff_thunk111,
#endif

#if WINRT_FAST_ABI_SIZE > 112
            winrt_ff_thunk112,
#endif

#if WINRT_FAST_ABI_SIZE > 113
            winrt_ff_thunk113,
#endif

#if WINRT_FAST_ABI_SIZE > 114
            winrt_ff_thunk114,
#endif

#if WINRT_FAST_ABI_SIZE > 115
            winrt_ff_thunk115,
#endif

#if WINRT_FAST_ABI_SIZE > 116
            winrt_ff_thunk116,
#endif

#if WINRT_FAST_ABI_SIZE > 117
            winrt_ff_thunk117,
#endif

#if WINRT_FAST_ABI_SIZE > 118
            winrt_ff_thunk118,
#endif

#if WINRT_FAST_ABI_SIZE > 119
            winrt_ff_thunk119,
#endif

#if WINRT_FAST_ABI_SIZE > 120
            winrt_ff_thunk120,
#endif

#if WINRT_FAST_ABI_SIZE > 121
            winrt_ff_thunk121,
#endif

#if WINRT_FAST_ABI_SIZE > 122
            winrt_ff_thunk122,
#endif

#if WINRT_FAST_ABI_SIZE > 123
            winrt_ff_thunk123,
#endif

#if WINRT_FAST_ABI_SIZE > 124
            winrt_ff_thunk124,
#endif

#if WINRT_FAST_ABI_SIZE > 125
            winrt_ff_thunk125,
#endif

#if WINRT_FAST_ABI_SIZE > 126
            winrt_ff_thunk126,
#endif

#if WINRT_FAST_ABI_SIZE > 127
            winrt_ff_thunk127,
#endif

#if WINRT_FAST_ABI_SIZE > 128
            winrt_ff_thunk128,
#endif

#if WINRT_FAST_ABI_SIZE > 129
            winrt_ff_thunk129,
#endif

#if WINRT_FAST_ABI_SIZE > 130
            winrt_ff_thunk130,
#endif

#if WINRT_FAST_ABI_SIZE > 131
            winrt_ff_thunk131,
#endif

#if WINRT_FAST_ABI_SIZE > 132
            winrt_ff_thunk132,
#endif

#if WINRT_FAST_ABI_SIZE > 133
            winrt_ff_thunk133,
#endif

#if WINRT_FAST_ABI_SIZE > 134
            winrt_ff_thunk134,
#endif

#if WINRT_FAST_ABI_SIZE > 135
            winrt_ff_thunk135,
#endif

#if WINRT_FAST_ABI_SIZE > 136
            winrt_ff_thunk136,
#endif

#if WINRT_FAST_ABI_SIZE > 137
            winrt_ff_thunk137,
#endif

#if WINRT_FAST_ABI_SIZE > 138
            winrt_ff_thunk138,
#endif

#if WINRT_FAST_ABI_SIZE > 139
            winrt_ff_thunk139,
#endif

#if WINRT_FAST_ABI_SIZE > 140
            winrt_ff_thunk140,
#endif

#if WINRT_FAST_ABI_SIZE > 141
            winrt_ff_thunk141,
#endif

#if WINRT_FAST_ABI_SIZE > 142
            winrt_ff_thunk142,
#endif

#if WINRT_FAST_ABI_SIZE > 143
            winrt_ff_thunk143,
#endif

#if WINRT_FAST_ABI_SIZE > 144
            winrt_ff_thunk144,
#endif

#if WINRT_FAST_ABI_SIZE > 145
            winrt_ff_thunk145,
#endif

#if WINRT_FAST_ABI_SIZE > 146
            winrt_ff_thunk146,
#endif

#if WINRT_FAST_ABI_SIZE > 147
            winrt_ff_thunk147,
#endif

#if WINRT_FAST_ABI_SIZE > 148
            winrt_ff_thunk148,
#endif

#if WINRT_FAST_ABI_SIZE > 149
            winrt_ff_thunk149,
#endif

#if WINRT_FAST_ABI_SIZE > 150
            winrt_ff_thunk150,
#endif

#if WINRT_FAST_ABI_SIZE > 151
            winrt_ff_thunk151,
#endif

#if WINRT_FAST_ABI_SIZE > 152
            winrt_ff_thunk152,
#endif

#if WINRT_FAST_ABI_SIZE > 153
            winrt_ff_thunk153,
#endif

#if WINRT_FAST_ABI_SIZE > 154
            winrt_ff_thunk154,
#endif

#if WINRT_FAST_ABI_SIZE > 155
            winrt_ff_thunk155,
#endif

#if WINRT_FAST_ABI_SIZE > 156
            winrt_ff_thunk156,
#endif

#if WINRT_FAST_ABI_SIZE > 157
            winrt_ff_thunk157,
#endif

#if WINRT_FAST_ABI_SIZE > 158
            winrt_ff_thunk158,
#endif

#if WINRT_FAST_ABI_SIZE > 159
            winrt_ff_thunk159,
#endif

#if WINRT_FAST_ABI_SIZE > 160
            winrt_ff_thunk160,
#endif

#if WINRT_FAST_ABI_SIZE > 161
            winrt_ff_thunk161,
#endif

#if WINRT_FAST_ABI_SIZE > 162
            winrt_ff_thunk162,
#endif

#if WINRT_FAST_ABI_SIZE > 163
            winrt_ff_thunk163,
#endif

#if WINRT_FAST_ABI_SIZE > 164
            winrt_ff_thunk164,
#endif

#if WINRT_FAST_ABI_SIZE > 165
            winrt_ff_thunk165,
#endif

#if WINRT_FAST_ABI_SIZE > 166
            winrt_ff_thunk166,
#endif

#if WINRT_FAST_ABI_SIZE > 167
            winrt_ff_thunk167,
#endif

#if WINRT_FAST_ABI_SIZE > 168
            winrt_ff_thunk168,
#endif

#if WINRT_FAST_ABI_SIZE > 169
            winrt_ff_thunk169,
#endif

#if WINRT_FAST_ABI_SIZE > 170
            winrt_ff_thunk170,
#endif

#if WINRT_FAST_ABI_SIZE > 171
            winrt_ff_thunk171,
#endif

#if WINRT_FAST_ABI_SIZE > 172
            winrt_ff_thunk172,
#endif

#if WINRT_FAST_ABI_SIZE > 173
            winrt_ff_thunk173,
#endif

#if WINRT_FAST_ABI_SIZE > 174
            winrt_ff_thunk174,
#endif

#if WINRT_FAST_ABI_SIZE > 175
            winrt_ff_thunk175,
#endif

#if WINRT_FAST_ABI_SIZE > 176
            winrt_ff_thunk176,
#endif

#if WINRT_FAST_ABI_SIZE > 177
            winrt_ff_thunk177,
#endif

#if WINRT_FAST_ABI_SIZE > 178
            winrt_ff_thunk178,
#endif

#if WINRT_FAST_ABI_SIZE > 179
            winrt_ff_thunk179,
#endif

#if WINRT_FAST_ABI_SIZE > 180
            winrt_ff_thunk180,
#endif

#if WINRT_FAST_ABI_SIZE > 181
            winrt_ff_thunk181,
#endif

#if WINRT_FAST_ABI_SIZE > 182
            winrt_ff_thunk182,
#endif

#if WINRT_FAST_ABI_SIZE > 183
            winrt_ff_thunk183,
#endif

#if WINRT_FAST_ABI_SIZE > 184
            winrt_ff_thunk184,
#endif

#if WINRT_FAST_ABI_SIZE > 185
            winrt_ff_thunk185,
#endif

#if WINRT_FAST_ABI_SIZE > 186
            winrt_ff_thunk186,
#endif

#if WINRT_FAST_ABI_SIZE > 187
            winrt_ff_thunk187,
#endif

#if WINRT_FAST_ABI_SIZE > 188
            winrt_ff_thunk188,
#endif

#if WINRT_FAST_ABI_SIZE > 189
            winrt_ff_thunk189,
#endif

#if WINRT_FAST_ABI_SIZE > 190
            winrt_ff_thunk190,
#endif

#if WINRT_FAST_ABI_SIZE > 191
            winrt_ff_thunk191,
#endif

#if WINRT_FAST_ABI_SIZE > 192
            winrt_ff_thunk192,
#endif

#if WINRT_FAST_ABI_SIZE > 193
            winrt_ff_thunk193,
#endif

#if WINRT_FAST_ABI_SIZE > 194
            winrt_ff_thunk194,
#endif

#if WINRT_FAST_ABI_SIZE > 195
            winrt_ff_thunk195,
#endif

#if WINRT_FAST_ABI_SIZE > 196
            winrt_ff_thunk196,
#endif

#if WINRT_FAST_ABI_SIZE > 197
            winrt_ff_thunk197,
#endif

#if WINRT_FAST_ABI_SIZE > 198
            winrt_ff_thunk198,
#endif

#if WINRT_FAST_ABI_SIZE > 199
            winrt_ff_thunk199,
#endif

#if WINRT_FAST_ABI_SIZE > 200
            winrt_ff_thunk200,
#endif

#if WINRT_FAST_ABI_SIZE > 201
            winrt_ff_thunk201,
#endif

#if WINRT_FAST_ABI_SIZE > 202
            winrt_ff_thunk202,
#endif

#if WINRT_FAST_ABI_SIZE > 203
            winrt_ff_thunk203,
#endif

#if WINRT_FAST_ABI_SIZE > 204
            winrt_ff_thunk204,
#endif

#if WINRT_FAST_ABI_SIZE > 205
            winrt_ff_thunk205,
#endif

#if WINRT_FAST_ABI_SIZE > 206
            winrt_ff_thunk206,
#endif

#if WINRT_FAST_ABI_SIZE > 207
            winrt_ff_thunk207,
#endif

#if WINRT_FAST_ABI_SIZE > 208
            winrt_ff_thunk208,
#endif

#if WINRT_FAST_ABI_SIZE > 209
            winrt_ff_thunk209,
#endif

#if WINRT_FAST_ABI_SIZE > 210
            winrt_ff_thunk210,
#endif

#if WINRT_FAST_ABI_SIZE > 211
            winrt_ff_thunk211,
#endif

#if WINRT_FAST_ABI_SIZE > 212
            winrt_ff_thunk212,
#endif

#if WINRT_FAST_ABI_SIZE > 213
            winrt_ff_thunk213,
#endif

#if WINRT_FAST_ABI_SIZE > 214
            winrt_ff_thunk214,
#endif

#if WINRT_FAST_ABI_SIZE > 215
            winrt_ff_thunk215,
#endif

#if WINRT_FAST_ABI_SIZE > 216
            winrt_ff_thunk216,
#endif

#if WINRT_FAST_ABI_SIZE > 217
            winrt_ff_thunk217,
#endif

#if WINRT_FAST_ABI_SIZE > 218
            winrt_ff_thunk218,
#endif

#if WINRT_FAST_ABI_SIZE > 219
            winrt_ff_thunk219,
#endif

#if WINRT_FAST_ABI_SIZE > 220
            winrt_ff_thunk220,
#endif

#if WINRT_FAST_ABI_SIZE > 221
            winrt_ff_thunk221,
#endif

#if WINRT_FAST_ABI_SIZE > 222
            winrt_ff_thunk222,
#endif

#if WINRT_FAST_ABI_SIZE > 223
            winrt_ff_thunk223,
#endif

#if WINRT_FAST_ABI_SIZE > 224
            winrt_ff_thunk224,
#endif

#if WINRT_FAST_ABI_SIZE > 225
            winrt_ff_thunk225,
#endif

#if WINRT_FAST_ABI_SIZE > 226
            winrt_ff_thunk226,
#endif

#if WINRT_FAST_ABI_SIZE > 227
            winrt_ff_thunk227,
#endif

#if WINRT_FAST_ABI_SIZE > 228
            winrt_ff_thunk228,
#endif

#if WINRT_FAST_ABI_SIZE > 229
            winrt_ff_thunk229,
#endif

#if WINRT_FAST_ABI_SIZE > 230
            winrt_ff_thunk230,
#endif

#if WINRT_FAST_ABI_SIZE > 231
            winrt_ff_thunk231,
#endif

#if WINRT_FAST_ABI_SIZE > 232
            winrt_ff_thunk232,
#endif

#if WINRT_FAST_ABI_SIZE > 233
            winrt_ff_thunk233,
#endif

#if WINRT_FAST_ABI_SIZE > 234
            winrt_ff_thunk234,
#endif

#if WINRT_FAST_ABI_SIZE > 235
            winrt_ff_thunk235,
#endif

#if WINRT_FAST_ABI_SIZE > 236
            winrt_ff_thunk236,
#endif

#if WINRT_FAST_ABI_SIZE > 237
            winrt_ff_thunk237,
#endif

#if WINRT_FAST_ABI_SIZE > 238
            winrt_ff_thunk238,
#endif

#if WINRT_FAST_ABI_SIZE > 239
            winrt_ff_thunk239,
#endif

#if WINRT_FAST_ABI_SIZE > 240
            winrt_ff_thunk240,
#endif

#if WINRT_FAST_ABI_SIZE > 241
            winrt_ff_thunk241,
#endif

#if WINRT_FAST_ABI_SIZE > 242
            winrt_ff_thunk242,
#endif

#if WINRT_FAST_ABI_SIZE > 243
            winrt_ff_thunk243,
#endif

#if WINRT_FAST_ABI_SIZE > 244
            winrt_ff_thunk244,
#endif

#if WINRT_FAST_ABI_SIZE > 245
            winrt_ff_thunk245,
#endif

#if WINRT_FAST_ABI_SIZE > 246
            winrt_ff_thunk246,
#endif

#if WINRT_FAST_ABI_SIZE > 247
            winrt_ff_thunk247,
#endif

#if WINRT_FAST_ABI_SIZE > 248
            winrt_ff_thunk248,
#endif

#if WINRT_FAST_ABI_SIZE > 249
            winrt_ff_thunk249,
#endif

#if WINRT_FAST_ABI_SIZE > 250
            winrt_ff_thunk250,
#endif

#if WINRT_FAST_ABI_SIZE > 251
            winrt_ff_thunk251,
#endif

#if WINRT_FAST_ABI_SIZE > 252
            winrt_ff_thunk252,
#endif

#if WINRT_FAST_ABI_SIZE > 253
            winrt_ff_thunk253,
#endif

#if WINRT_FAST_ABI_SIZE > 254
            winrt_ff_thunk254,
#endif

#if WINRT_FAST_ABI_SIZE > 255
            winrt_ff_thunk255,
#endif

#if WINRT_FAST_ABI_SIZE > 256
            winrt_ff_thunk256,
#endif

#if WINRT_FAST_ABI_SIZE > 257
            winrt_ff_thunk257,
#endif

#if WINRT_FAST_ABI_SIZE > 258
            winrt_ff_thunk258,
#endif

#if WINRT_FAST_ABI_SIZE > 259
            winrt_ff_thunk259,
#endif

#if WINRT_FAST_ABI_SIZE > 260
            winrt_ff_thunk260,
#endif

#if WINRT_FAST_ABI_SIZE > 261
            winrt_ff_thunk261,
#endif

#if WINRT_FAST_ABI_SIZE > 262
            winrt_ff_thunk262,
#endif

#if WINRT_FAST_ABI_SIZE > 263
            winrt_ff_thunk263,
#endif

#if WINRT_FAST_ABI_SIZE > 264
            winrt_ff_thunk264,
#endif

#if WINRT_FAST_ABI_SIZE > 265
            winrt_ff_thunk265,
#endif

#if WINRT_FAST_ABI_SIZE > 266
            winrt_ff_thunk266,
#endif

#if WINRT_FAST_ABI_SIZE > 267
            winrt_ff_thunk267,
#endif

#if WINRT_FAST_ABI_SIZE > 268
            winrt_ff_thunk268,
#endif

#if WINRT_FAST_ABI_SIZE > 269
            winrt_ff_thunk269,
#endif

#if WINRT_FAST_ABI_SIZE > 270
            winrt_ff_thunk270,
#endif

#if WINRT_FAST_ABI_SIZE > 271
            winrt_ff_thunk271,
#endif

#if WINRT_FAST_ABI_SIZE > 272
            winrt_ff_thunk272,
#endif

#if WINRT_FAST_ABI_SIZE > 273
            winrt_ff_thunk273,
#endif

#if WINRT_FAST_ABI_SIZE > 274
            winrt_ff_thunk274,
#endif

#if WINRT_FAST_ABI_SIZE > 275
            winrt_ff_thunk275,
#endif

#if WINRT_FAST_ABI_SIZE > 276
            winrt_ff_thunk276,
#endif

#if WINRT_FAST_ABI_SIZE > 277
            winrt_ff_thunk277,
#endif

#if WINRT_FAST_ABI_SIZE > 278
            winrt_ff_thunk278,
#endif

#if WINRT_FAST_ABI_SIZE > 279
            winrt_ff_thunk279,
#endif

#if WINRT_FAST_ABI_SIZE > 280
            winrt_ff_thunk280,
#endif

#if WINRT_FAST_ABI_SIZE > 281
            winrt_ff_thunk281,
#endif

#if WINRT_FAST_ABI_SIZE > 282
            winrt_ff_thunk282,
#endif

#if WINRT_FAST_ABI_SIZE > 283
            winrt_ff_thunk283,
#endif

#if WINRT_FAST_ABI_SIZE > 284
            winrt_ff_thunk284,
#endif

#if WINRT_FAST_ABI_SIZE > 285
            winrt_ff_thunk285,
#endif

#if WINRT_FAST_ABI_SIZE > 286
            winrt_ff_thunk286,
#endif

#if WINRT_FAST_ABI_SIZE > 287
            winrt_ff_thunk287,
#endif

#if WINRT_FAST_ABI_SIZE > 288
            winrt_ff_thunk288,
#endif

#if WINRT_FAST_ABI_SIZE > 289
            winrt_ff_thunk289,
#endif

#if WINRT_FAST_ABI_SIZE > 290
            winrt_ff_thunk290,
#endif

#if WINRT_FAST_ABI_SIZE > 291
            winrt_ff_thunk291,
#endif

#if WINRT_FAST_ABI_SIZE > 292
            winrt_ff_thunk292,
#endif

#if WINRT_FAST_ABI_SIZE > 293
            winrt_ff_thunk293,
#endif

#if WINRT_FAST_ABI_SIZE > 294
            winrt_ff_thunk294,
#endif

#if WINRT_FAST_ABI_SIZE > 295
            winrt_ff_thunk295,
#endif

#if WINRT_FAST_ABI_SIZE > 296
            winrt_ff_thunk296,
#endif

#if WINRT_FAST_ABI_SIZE > 297
            winrt_ff_thunk297,
#endif

#if WINRT_FAST_ABI_SIZE > 298
            winrt_ff_thunk298,
#endif

#if WINRT_FAST_ABI_SIZE > 299
            winrt_ff_thunk299,
#endif

#if WINRT_FAST_ABI_SIZE > 300
            winrt_ff_thunk300,
#endif

#if WINRT_FAST_ABI_SIZE > 301
            winrt_ff_thunk301,
#endif

#if WINRT_FAST_ABI_SIZE > 302
            winrt_ff_thunk302,
#endif

#if WINRT_FAST_ABI_SIZE > 303
            winrt_ff_thunk303,
#endif

#if WINRT_FAST_ABI_SIZE > 304
            winrt_ff_thunk304,
#endif

#if WINRT_FAST_ABI_SIZE > 305
            winrt_ff_thunk305,
#endif

#if WINRT_FAST_ABI_SIZE > 306
            winrt_ff_thunk306,
#endif

#if WINRT_FAST_ABI_SIZE > 307
            winrt_ff_thunk307,
#endif

#if WINRT_FAST_ABI_SIZE > 308
            winrt_ff_thunk308,
#endif

#if WINRT_FAST_ABI_SIZE > 309
            winrt_ff_thunk309,
#endif

#if WINRT_FAST_ABI_SIZE > 310
            winrt_ff_thunk310,
#endif

#if WINRT_FAST_ABI_SIZE > 311
            winrt_ff_thunk311,
#endif

#if WINRT_FAST_ABI_SIZE > 312
            winrt_ff_thunk312,
#endif

#if WINRT_FAST_ABI_SIZE > 313
            winrt_ff_thunk313,
#endif

#if WINRT_FAST_ABI_SIZE > 314
            winrt_ff_thunk314,
#endif

#if WINRT_FAST_ABI_SIZE > 315
            winrt_ff_thunk315,
#endif

#if WINRT_FAST_ABI_SIZE > 316
            winrt_ff_thunk316,
#endif

#if WINRT_FAST_ABI_SIZE > 317
            winrt_ff_thunk317,
#endif

#if WINRT_FAST_ABI_SIZE > 318
            winrt_ff_thunk318,
#endif

#if WINRT_FAST_ABI_SIZE > 319
            winrt_ff_thunk319,
#endif

#if WINRT_FAST_ABI_SIZE > 320
            winrt_ff_thunk320,
#endif

#if WINRT_FAST_ABI_SIZE > 321
            winrt_ff_thunk321,
#endif

#if WINRT_FAST_ABI_SIZE > 322
            winrt_ff_thunk322,
#endif

#if WINRT_FAST_ABI_SIZE > 323
            winrt_ff_thunk323,
#endif

#if WINRT_FAST_ABI_SIZE > 324
            winrt_ff_thunk324,
#endif

#if WINRT_FAST_ABI_SIZE > 325
            winrt_ff_thunk325,
#endif

#if WINRT_FAST_ABI_SIZE > 326
            winrt_ff_thunk326,
#endif

#if WINRT_FAST_ABI_SIZE > 327
            winrt_ff_thunk327,
#endif

#if WINRT_FAST_ABI_SIZE > 328
            winrt_ff_thunk328,
#endif

#if WINRT_FAST_ABI_SIZE > 329
            winrt_ff_thunk329,
#endif

#if WINRT_FAST_ABI_SIZE > 330
            winrt_ff_thunk330,
#endif

#if WINRT_FAST_ABI_SIZE > 331
            winrt_ff_thunk331,
#endif

#if WINRT_FAST_ABI_SIZE > 332
            winrt_ff_thunk332,
#endif

#if WINRT_FAST_ABI_SIZE > 333
            winrt_ff_thunk333,
#endif

#if WINRT_FAST_ABI_SIZE > 334
            winrt_ff_thunk334,
#endif

#if WINRT_FAST_ABI_SIZE > 335
            winrt_ff_thunk335,
#endif

#if WINRT_FAST_ABI_SIZE > 336
            winrt_ff_thunk336,
#endif

#if WINRT_FAST_ABI_SIZE > 337
            winrt_ff_thunk337,
#endif

#if WINRT_FAST_ABI_SIZE > 338
            winrt_ff_thunk338,
#endif

#if WINRT_FAST_ABI_SIZE > 339
            winrt_ff_thunk339,
#endif

#if WINRT_FAST_ABI_SIZE > 340
            winrt_ff_thunk340,
#endif

#if WINRT_FAST_ABI_SIZE > 341
            winrt_ff_thunk341,
#endif

#if WINRT_FAST_ABI_SIZE > 342
            winrt_ff_thunk342,
#endif

#if WINRT_FAST_ABI_SIZE > 343
            winrt_ff_thunk343,
#endif

#if WINRT_FAST_ABI_SIZE > 344
            winrt_ff_thunk344,
#endif

#if WINRT_FAST_ABI_SIZE > 345
            winrt_ff_thunk345,
#endif

#if WINRT_FAST_ABI_SIZE > 346
            winrt_ff_thunk346,
#endif

#if WINRT_FAST_ABI_SIZE > 347
            winrt_ff_thunk347,
#endif

#if WINRT_FAST_ABI_SIZE > 348
            winrt_ff_thunk348,
#endif

#if WINRT_FAST_ABI_SIZE > 349
            winrt_ff_thunk349,
#endif

#if WINRT_FAST_ABI_SIZE > 350
            winrt_ff_thunk350,
#endif

#if WINRT_FAST_ABI_SIZE > 351
            winrt_ff_thunk351,
#endif

#if WINRT_FAST_ABI_SIZE > 352
            winrt_ff_thunk352,
#endif

#if WINRT_FAST_ABI_SIZE > 353
            winrt_ff_thunk353,
#endif

#if WINRT_FAST_ABI_SIZE > 354
            winrt_ff_thunk354,
#endif

#if WINRT_FAST_ABI_SIZE > 355
            winrt_ff_thunk355,
#endif

#if WINRT_FAST_ABI_SIZE > 356
            winrt_ff_thunk356,
#endif

#if WINRT_FAST_ABI_SIZE > 357
            winrt_ff_thunk357,
#endif

#if WINRT_FAST_ABI_SIZE > 358
            winrt_ff_thunk358,
#endif

#if WINRT_FAST_ABI_SIZE > 359
            winrt_ff_thunk359,
#endif

#if WINRT_FAST_ABI_SIZE > 360
            winrt_ff_thunk360,
#endif

#if WINRT_FAST_ABI_SIZE > 361
            winrt_ff_thunk361,
#endif

#if WINRT_FAST_ABI_SIZE > 362
            winrt_ff_thunk362,
#endif

#if WINRT_FAST_ABI_SIZE > 363
            winrt_ff_thunk363,
#endif

#if WINRT_FAST_ABI_SIZE > 364
            winrt_ff_thunk364,
#endif

#if WINRT_FAST_ABI_SIZE > 365
            winrt_ff_thunk365,
#endif

#if WINRT_FAST_ABI_SIZE > 366
            winrt_ff_thunk366,
#endif

#if WINRT_FAST_ABI_SIZE > 367
            winrt_ff_thunk367,
#endif

#if WINRT_FAST_ABI_SIZE > 368
            winrt_ff_thunk368,
#endif

#if WINRT_FAST_ABI_SIZE > 369
            winrt_ff_thunk369,
#endif

#if WINRT_FAST_ABI_SIZE > 370
            winrt_ff_thunk370,
#endif

#if WINRT_FAST_ABI_SIZE > 371
            winrt_ff_thunk371,
#endif

#if WINRT_FAST_ABI_SIZE > 372
            winrt_ff_thunk372,
#endif

#if WINRT_FAST_ABI_SIZE > 373
            winrt_ff_thunk373,
#endif

#if WINRT_FAST_ABI_SIZE > 374
            winrt_ff_thunk374,
#endif

#if WINRT_FAST_ABI_SIZE > 375
            winrt_ff_thunk375,
#endif

#if WINRT_FAST_ABI_SIZE > 376
            winrt_ff_thunk376,
#endif

#if WINRT_FAST_ABI_SIZE > 377
            winrt_ff_thunk377,
#endif

#if WINRT_FAST_ABI_SIZE > 378
            winrt_ff_thunk378,
#endif

#if WINRT_FAST_ABI_SIZE > 379
            winrt_ff_thunk379,
#endif

#if WINRT_FAST_ABI_SIZE > 380
            winrt_ff_thunk380,
#endif

#if WINRT_FAST_ABI_SIZE > 381
            winrt_ff_thunk381,
#endif

#if WINRT_FAST_ABI_SIZE > 382
            winrt_ff_thunk382,
#endif

#if WINRT_FAST_ABI_SIZE > 383
            winrt_ff_thunk383,
#endif

#if WINRT_FAST_ABI_SIZE > 384
            winrt_ff_thunk384,
#endif

#if WINRT_FAST_ABI_SIZE > 385
            winrt_ff_thunk385,
#endif

#if WINRT_FAST_ABI_SIZE > 386
            winrt_ff_thunk386,
#endif

#if WINRT_FAST_ABI_SIZE > 387
            winrt_ff_thunk387,
#endif

#if WINRT_FAST_ABI_SIZE > 388
            winrt_ff_thunk388,
#endif

#if WINRT_FAST_ABI_SIZE > 389
            winrt_ff_thunk389,
#endif

#if WINRT_FAST_ABI_SIZE > 390
            winrt_ff_thunk390,
#endif

#if WINRT_FAST_ABI_SIZE > 391
            winrt_ff_thunk391,
#endif

#if WINRT_FAST_ABI_SIZE > 392
            winrt_ff_thunk392,
#endif

#if WINRT_FAST_ABI_SIZE > 393
            winrt_ff_thunk393,
#endif

#if WINRT_FAST_ABI_SIZE > 394
            winrt_ff_thunk394,
#endif

#if WINRT_FAST_ABI_SIZE > 395
            winrt_ff_thunk395,
#endif

#if WINRT_FAST_ABI_SIZE > 396
            winrt_ff_thunk396,
#endif

#if WINRT_FAST_ABI_SIZE > 397
            winrt_ff_thunk397,
#endif

#if WINRT_FAST_ABI_SIZE > 398
            winrt_ff_thunk398,
#endif

#if WINRT_FAST_ABI_SIZE > 399
            winrt_ff_thunk399,
#endif

#if WINRT_FAST_ABI_SIZE > 400
            winrt_ff_thunk400,
#endif

#if WINRT_FAST_ABI_SIZE > 401
            winrt_ff_thunk401,
#endif

#if WINRT_FAST_ABI_SIZE > 402
            winrt_ff_thunk402,
#endif

#if WINRT_FAST_ABI_SIZE > 403
            winrt_ff_thunk403,
#endif

#if WINRT_FAST_ABI_SIZE > 404
            winrt_ff_thunk404,
#endif

#if WINRT_FAST_ABI_SIZE > 405
            winrt_ff_thunk405,
#endif

#if WINRT_FAST_ABI_SIZE > 406
            winrt_ff_thunk406,
#endif

#if WINRT_FAST_ABI_SIZE > 407
            winrt_ff_thunk407,
#endif

#if WINRT_FAST_ABI_SIZE > 408
            winrt_ff_thunk408,
#endif

#if WINRT_FAST_ABI_SIZE > 409
            winrt_ff_thunk409,
#endif

#if WINRT_FAST_ABI_SIZE > 410
            winrt_ff_thunk410,
#endif

#if WINRT_FAST_ABI_SIZE > 411
            winrt_ff_thunk411,
#endif

#if WINRT_FAST_ABI_SIZE > 412
            winrt_ff_thunk412,
#endif

#if WINRT_FAST_ABI_SIZE > 413
            winrt_ff_thunk413,
#endif

#if WINRT_FAST_ABI_SIZE > 414
            winrt_ff_thunk414,
#endif

#if WINRT_FAST_ABI_SIZE > 415
            winrt_ff_thunk415,
#endif

#if WINRT_FAST_ABI_SIZE > 416
            winrt_ff_thunk416,
#endif

#if WINRT_FAST_ABI_SIZE > 417
            winrt_ff_thunk417,
#endif

#if WINRT_FAST_ABI_SIZE > 418
            winrt_ff_thunk418,
#endif

#if WINRT_FAST_ABI_SIZE > 419
            winrt_ff_thunk419,
#endif

#if WINRT_FAST_ABI_SIZE > 420
            winrt_ff_thunk420,
#endif

#if WINRT_FAST_ABI_SIZE > 421
            winrt_ff_thunk421,
#endif

#if WINRT_FAST_ABI_SIZE > 422
            winrt_ff_thunk422,
#endif

#if WINRT_FAST_ABI_SIZE > 423
            winrt_ff_thunk423,
#endif

#if WINRT_FAST_ABI_SIZE > 424
            winrt_ff_thunk424,
#endif

#if WINRT_FAST_ABI_SIZE > 425
            winrt_ff_thunk425,
#endif

#if WINRT_FAST_ABI_SIZE > 426
            winrt_ff_thunk426,
#endif

#if WINRT_FAST_ABI_SIZE > 427
            winrt_ff_thunk427,
#endif

#if WINRT_FAST_ABI_SIZE > 428
            winrt_ff_thunk428,
#endif

#if WINRT_FAST_ABI_SIZE > 429
            winrt_ff_thunk429,
#endif

#if WINRT_FAST_ABI_SIZE > 430
            winrt_ff_thunk430,
#endif

#if WINRT_FAST_ABI_SIZE > 431
            winrt_ff_thunk431,
#endif

#if WINRT_FAST_ABI_SIZE > 432
            winrt_ff_thunk432,
#endif

#if WINRT_FAST_ABI_SIZE > 433
            winrt_ff_thunk433,
#endif

#if WINRT_FAST_ABI_SIZE > 434
            winrt_ff_thunk434,
#endif

#if WINRT_FAST_ABI_SIZE > 435
            winrt_ff_thunk435,
#endif

#if WINRT_FAST_ABI_SIZE > 436
            winrt_ff_thunk436,
#endif

#if WINRT_FAST_ABI_SIZE > 437
            winrt_ff_thunk437,
#endif

#if WINRT_FAST_ABI_SIZE > 438
            winrt_ff_thunk438,
#endif

#if WINRT_FAST_ABI_SIZE > 439
            winrt_ff_thunk439,
#endif

#if WINRT_FAST_ABI_SIZE > 440
            winrt_ff_thunk440,
#endif

#if WINRT_FAST_ABI_SIZE > 441
            winrt_ff_thunk441,
#endif

#if WINRT_FAST_ABI_SIZE > 442
            winrt_ff_thunk442,
#endif

#if WINRT_FAST_ABI_SIZE > 443
            winrt_ff_thunk443,
#endif

#if WINRT_FAST_ABI_SIZE > 444
            winrt_ff_thunk444,
#endif

#if WINRT_FAST_ABI_SIZE > 445
            winrt_ff_thunk445,
#endif

#if WINRT_FAST_ABI_SIZE > 446
            winrt_ff_thunk446,
#endif

#if WINRT_FAST_ABI_SIZE > 447
            winrt_ff_thunk447,
#endif

#if WINRT_FAST_ABI_SIZE > 448
            winrt_ff_thunk448,
#endif

#if WINRT_FAST_ABI_SIZE > 449
            winrt_ff_thunk449,
#endif

#if WINRT_FAST_ABI_SIZE > 450
            winrt_ff_thunk450,
#endif

#if WINRT_FAST_ABI_SIZE > 451
            winrt_ff_thunk451,
#endif

#if WINRT_FAST_ABI_SIZE > 452
            winrt_ff_thunk452,
#endif

#if WINRT_FAST_ABI_SIZE > 453
            winrt_ff_thunk453,
#endif

#if WINRT_FAST_ABI_SIZE > 454
            winrt_ff_thunk454,
#endif

#if WINRT_FAST_ABI_SIZE > 455
            winrt_ff_thunk455,
#endif

#if WINRT_FAST_ABI_SIZE > 456
            winrt_ff_thunk456,
#endif

#if WINRT_FAST_ABI_SIZE > 457
            winrt_ff_thunk457,
#endif

#if WINRT_FAST_ABI_SIZE > 458
            winrt_ff_thunk458,
#endif

#if WINRT_FAST_ABI_SIZE > 459
            winrt_ff_thunk459,
#endif

#if WINRT_FAST_ABI_SIZE > 460
            winrt_ff_thunk460,
#endif

#if WINRT_FAST_ABI_SIZE > 461
            winrt_ff_thunk461,
#endif

#if WINRT_FAST_ABI_SIZE > 462
            winrt_ff_thunk462,
#endif

#if WINRT_FAST_ABI_SIZE > 463
            winrt_ff_thunk463,
#endif

#if WINRT_FAST_ABI_SIZE > 464
            winrt_ff_thunk464,
#endif

#if WINRT_FAST_ABI_SIZE > 465
            winrt_ff_thunk465,
#endif

#if WINRT_FAST_ABI_SIZE > 466
            winrt_ff_thunk466,
#endif

#if WINRT_FAST_ABI_SIZE > 467
            winrt_ff_thunk467,
#endif

#if WINRT_FAST_ABI_SIZE > 468
            winrt_ff_thunk468,
#endif

#if WINRT_FAST_ABI_SIZE > 469
            winrt_ff_thunk469,
#endif

#if WINRT_FAST_ABI_SIZE > 470
            winrt_ff_thunk470,
#endif

#if WINRT_FAST_ABI_SIZE > 471
            winrt_ff_thunk471,
#endif

#if WINRT_FAST_ABI_SIZE > 472
            winrt_ff_thunk472,
#endif

#if WINRT_FAST_ABI_SIZE > 473
            winrt_ff_thunk473,
#endif

#if WINRT_FAST_ABI_SIZE > 474
            winrt_ff_thunk474,
#endif

#if WINRT_FAST_ABI_SIZE > 475
            winrt_ff_thunk475,
#endif

#if WINRT_FAST_ABI_SIZE > 476
            winrt_ff_thunk476,
#endif

#if WINRT_FAST_ABI_SIZE > 477
            winrt_ff_thunk477,
#endif

#if WINRT_FAST_ABI_SIZE > 478
            winrt_ff_thunk478,
#endif

#if WINRT_FAST_ABI_SIZE > 479
            winrt_ff_thunk479,
#endif

#if WINRT_FAST_ABI_SIZE > 480
            winrt_ff_thunk480,
#endif

#if WINRT_FAST_ABI_SIZE > 481
            winrt_ff_thunk481,
#endif

#if WINRT_FAST_ABI_SIZE > 482
            winrt_ff_thunk482,
#endif

#if WINRT_FAST_ABI_SIZE > 483
            winrt_ff_thunk483,
#endif

#if WINRT_FAST_ABI_SIZE > 484
            winrt_ff_thunk484,
#endif

#if WINRT_FAST_ABI_SIZE > 485
            winrt_ff_thunk485,
#endif

#if WINRT_FAST_ABI_SIZE > 486
            winrt_ff_thunk486,
#endif

#if WINRT_FAST_ABI_SIZE > 487
            winrt_ff_thunk487,
#endif

#if WINRT_FAST_ABI_SIZE > 488
            winrt_ff_thunk488,
#endif

#if WINRT_FAST_ABI_SIZE > 489
            winrt_ff_thunk489,
#endif

#if WINRT_FAST_ABI_SIZE > 490
            winrt_ff_thunk490,
#endif

#if WINRT_FAST_ABI_SIZE > 491
            winrt_ff_thunk491,
#endif

#if WINRT_FAST_ABI_SIZE > 492
            winrt_ff_thunk492,
#endif

#if WINRT_FAST_ABI_SIZE > 493
            winrt_ff_thunk493,
#endif

#if WINRT_FAST_ABI_SIZE > 494
            winrt_ff_thunk494,
#endif

#if WINRT_FAST_ABI_SIZE > 495
            winrt_ff_thunk495,
#endif

#if WINRT_FAST_ABI_SIZE > 496
            winrt_ff_thunk496,
#endif

#if WINRT_FAST_ABI_SIZE > 497
            winrt_ff_thunk497,
#endif

#if WINRT_FAST_ABI_SIZE > 498
            winrt_ff_thunk498,
#endif

#if WINRT_FAST_ABI_SIZE > 499
            winrt_ff_thunk499,
#endif

#if WINRT_FAST_ABI_SIZE > 500
            winrt_ff_thunk500,
#endif

#if WINRT_FAST_ABI_SIZE > 501
            winrt_ff_thunk501,
#endif

#if WINRT_FAST_ABI_SIZE > 502
            winrt_ff_thunk502,
#endif

#if WINRT_FAST_ABI_SIZE > 503
            winrt_ff_thunk503,
#endif

#if WINRT_FAST_ABI_SIZE > 504
            winrt_ff_thunk504,
#endif

#if WINRT_FAST_ABI_SIZE > 505
            winrt_ff_thunk505,
#endif

#if WINRT_FAST_ABI_SIZE > 506
            winrt_ff_thunk506,
#endif

#if WINRT_FAST_ABI_SIZE > 507
            winrt_ff_thunk507,
#endif

#if WINRT_FAST_ABI_SIZE > 508
            winrt_ff_thunk508,
#endif

#if WINRT_FAST_ABI_SIZE > 509
            winrt_ff_thunk509,
#endif

#if WINRT_FAST_ABI_SIZE > 510
            winrt_ff_thunk510,
#endif

#if WINRT_FAST_ABI_SIZE > 511
            winrt_ff_thunk511,
#endif

#if WINRT_FAST_ABI_SIZE > 512
            winrt_ff_thunk512,
#endif

#if WINRT_FAST_ABI_SIZE > 513
            winrt_ff_thunk513,
#endif

#if WINRT_FAST_ABI_SIZE > 514
            winrt_ff_thunk514,
#endif

#if WINRT_FAST_ABI_SIZE > 515
            winrt_ff_thunk515,
#endif

#if WINRT_FAST_ABI_SIZE > 516
            winrt_ff_thunk516,
#endif

#if WINRT_FAST_ABI_SIZE > 517
            winrt_ff_thunk517,
#endif

#if WINRT_FAST_ABI_SIZE > 518
            winrt_ff_thunk518,
#endif

#if WINRT_FAST_ABI_SIZE > 519
            winrt_ff_thunk519,
#endif

#if WINRT_FAST_ABI_SIZE > 520
            winrt_ff_thunk520,
#endif

#if WINRT_FAST_ABI_SIZE > 521
            winrt_ff_thunk521,
#endif

#if WINRT_FAST_ABI_SIZE > 522
            winrt_ff_thunk522,
#endif

#if WINRT_FAST_ABI_SIZE > 523
            winrt_ff_thunk523,
#endif

#if WINRT_FAST_ABI_SIZE > 524
            winrt_ff_thunk524,
#endif

#if WINRT_FAST_ABI_SIZE > 525
            winrt_ff_thunk525,
#endif

#if WINRT_FAST_ABI_SIZE > 526
            winrt_ff_thunk526,
#endif

#if WINRT_FAST_ABI_SIZE > 527
            winrt_ff_thunk527,
#endif

#if WINRT_FAST_ABI_SIZE > 528
            winrt_ff_thunk528,
#endif

#if WINRT_FAST_ABI_SIZE > 529
            winrt_ff_thunk529,
#endif

#if WINRT_FAST_ABI_SIZE > 530
            winrt_ff_thunk530,
#endif

#if WINRT_FAST_ABI_SIZE > 531
            winrt_ff_thunk531,
#endif

#if WINRT_FAST_ABI_SIZE > 532
            winrt_ff_thunk532,
#endif

#if WINRT_FAST_ABI_SIZE > 533
            winrt_ff_thunk533,
#endif

#if WINRT_FAST_ABI_SIZE > 534
            winrt_ff_thunk534,
#endif

#if WINRT_FAST_ABI_SIZE > 535
            winrt_ff_thunk535,
#endif

#if WINRT_FAST_ABI_SIZE > 536
            winrt_ff_thunk536,
#endif

#if WINRT_FAST_ABI_SIZE > 537
            winrt_ff_thunk537,
#endif

#if WINRT_FAST_ABI_SIZE > 538
            winrt_ff_thunk538,
#endif

#if WINRT_FAST_ABI_SIZE > 539
            winrt_ff_thunk539,
#endif

#if WINRT_FAST_ABI_SIZE > 540
            winrt_ff_thunk540,
#endif

#if WINRT_FAST_ABI_SIZE > 541
            winrt_ff_thunk541,
#endif

#if WINRT_FAST_ABI_SIZE > 542
            winrt_ff_thunk542,
#endif

#if WINRT_FAST_ABI_SIZE > 543
            winrt_ff_thunk543,
#endif

#if WINRT_FAST_ABI_SIZE > 544
            winrt_ff_thunk544,
#endif

#if WINRT_FAST_ABI_SIZE > 545
            winrt_ff_thunk545,
#endif

#if WINRT_FAST_ABI_SIZE > 546
            winrt_ff_thunk546,
#endif

#if WINRT_FAST_ABI_SIZE > 547
            winrt_ff_thunk547,
#endif

#if WINRT_FAST_ABI_SIZE > 548
            winrt_ff_thunk548,
#endif

#if WINRT_FAST_ABI_SIZE > 549
            winrt_ff_thunk549,
#endif

#if WINRT_FAST_ABI_SIZE > 550
            winrt_ff_thunk550,
#endif

#if WINRT_FAST_ABI_SIZE > 551
            winrt_ff_thunk551,
#endif

#if WINRT_FAST_ABI_SIZE > 552
            winrt_ff_thunk552,
#endif

#if WINRT_FAST_ABI_SIZE > 553
            winrt_ff_thunk553,
#endif

#if WINRT_FAST_ABI_SIZE > 554
            winrt_ff_thunk554,
#endif

#if WINRT_FAST_ABI_SIZE > 555
            winrt_ff_thunk555,
#endif

#if WINRT_FAST_ABI_SIZE > 556
            winrt_ff_thunk556,
#endif

#if WINRT_FAST_ABI_SIZE > 557
            winrt_ff_thunk557,
#endif

#if WINRT_FAST_ABI_SIZE > 558
            winrt_ff_thunk558,
#endif

#if WINRT_FAST_ABI_SIZE > 559
            winrt_ff_thunk559,
#endif

#if WINRT_FAST_ABI_SIZE > 560
            winrt_ff_thunk560,
#endif

#if WINRT_FAST_ABI_SIZE > 561
            winrt_ff_thunk561,
#endif

#if WINRT_FAST_ABI_SIZE > 562
            winrt_ff_thunk562,
#endif

#if WINRT_FAST_ABI_SIZE > 563
            winrt_ff_thunk563,
#endif

#if WINRT_FAST_ABI_SIZE > 564
            winrt_ff_thunk564,
#endif

#if WINRT_FAST_ABI_SIZE > 565
            winrt_ff_thunk565,
#endif

#if WINRT_FAST_ABI_SIZE > 566
            winrt_ff_thunk566,
#endif

#if WINRT_FAST_ABI_SIZE > 567
            winrt_ff_thunk567,
#endif

#if WINRT_FAST_ABI_SIZE > 568
            winrt_ff_thunk568,
#endif

#if WINRT_FAST_ABI_SIZE > 569
            winrt_ff_thunk569,
#endif

#if WINRT_FAST_ABI_SIZE > 570
            winrt_ff_thunk570,
#endif

#if WINRT_FAST_ABI_SIZE > 571
            winrt_ff_thunk571,
#endif

#if WINRT_FAST_ABI_SIZE > 572
            winrt_ff_thunk572,
#endif

#if WINRT_FAST_ABI_SIZE > 573
            winrt_ff_thunk573,
#endif

#if WINRT_FAST_ABI_SIZE > 574
            winrt_ff_thunk574,
#endif

#if WINRT_FAST_ABI_SIZE > 575
            winrt_ff_thunk575,
#endif

#if WINRT_FAST_ABI_SIZE > 576
            winrt_ff_thunk576,
#endif

#if WINRT_FAST_ABI_SIZE > 577
            winrt_ff_thunk577,
#endif

#if WINRT_FAST_ABI_SIZE > 578
            winrt_ff_thunk578,
#endif

#if WINRT_FAST_ABI_SIZE > 579
            winrt_ff_thunk579,
#endif

#if WINRT_FAST_ABI_SIZE > 580
            winrt_ff_thunk580,
#endif

#if WINRT_FAST_ABI_SIZE > 581
            winrt_ff_thunk581,
#endif

#if WINRT_FAST_ABI_SIZE > 582
            winrt_ff_thunk582,
#endif

#if WINRT_FAST_ABI_SIZE > 583
            winrt_ff_thunk583,
#endif

#if WINRT_FAST_ABI_SIZE > 584
            winrt_ff_thunk584,
#endif

#if WINRT_FAST_ABI_SIZE > 585
            winrt_ff_thunk585,
#endif

#if WINRT_FAST_ABI_SIZE > 586
            winrt_ff_thunk586,
#endif

#if WINRT_FAST_ABI_SIZE > 587
            winrt_ff_thunk587,
#endif

#if WINRT_FAST_ABI_SIZE > 588
            winrt_ff_thunk588,
#endif

#if WINRT_FAST_ABI_SIZE > 589
            winrt_ff_thunk589,
#endif

#if WINRT_FAST_ABI_SIZE > 590
            winrt_ff_thunk590,
#endif

#if WINRT_FAST_ABI_SIZE > 591
            winrt_ff_thunk591,
#endif

#if WINRT_FAST_ABI_SIZE > 592
            winrt_ff_thunk592,
#endif

#if WINRT_FAST_ABI_SIZE > 593
            winrt_ff_thunk593,
#endif

#if WINRT_FAST_ABI_SIZE > 594
            winrt_ff_thunk594,
#endif

#if WINRT_FAST_ABI_SIZE > 595
            winrt_ff_thunk595,
#endif

#if WINRT_FAST_ABI_SIZE > 596
            winrt_ff_thunk596,
#endif

#if WINRT_FAST_ABI_SIZE > 597
            winrt_ff_thunk597,
#endif

#if WINRT_FAST_ABI_SIZE > 598
            winrt_ff_thunk598,
#endif

#if WINRT_FAST_ABI_SIZE > 599
            winrt_ff_thunk599,
#endif

#if WINRT_FAST_ABI_SIZE > 600
            winrt_ff_thunk600,
#endif

#if WINRT_FAST_ABI_SIZE > 601
            winrt_ff_thunk601,
#endif

#if WINRT_FAST_ABI_SIZE > 602
            winrt_ff_thunk602,
#endif

#if WINRT_FAST_ABI_SIZE > 603
            winrt_ff_thunk603,
#endif

#if WINRT_FAST_ABI_SIZE > 604
            winrt_ff_thunk604,
#endif

#if WINRT_FAST_ABI_SIZE > 605
            winrt_ff_thunk605,
#endif

#if WINRT_FAST_ABI_SIZE > 606
            winrt_ff_thunk606,
#endif

#if WINRT_FAST_ABI_SIZE > 607
            winrt_ff_thunk607,
#endif

#if WINRT_FAST_ABI_SIZE > 608
            winrt_ff_thunk608,
#endif

#if WINRT_FAST_ABI_SIZE > 609
            winrt_ff_thunk609,
#endif

#if WINRT_FAST_ABI_SIZE > 610
            winrt_ff_thunk610,
#endif

#if WINRT_FAST_ABI_SIZE > 611
            winrt_ff_thunk611,
#endif

#if WINRT_FAST_ABI_SIZE > 612
            winrt_ff_thunk612,
#endif

#if WINRT_FAST_ABI_SIZE > 613
            winrt_ff_thunk613,
#endif

#if WINRT_FAST_ABI_SIZE > 614
            winrt_ff_thunk614,
#endif

#if WINRT_FAST_ABI_SIZE > 615
            winrt_ff_thunk615,
#endif

#if WINRT_FAST_ABI_SIZE > 616
            winrt_ff_thunk616,
#endif

#if WINRT_FAST_ABI_SIZE > 617
            winrt_ff_thunk617,
#endif

#if WINRT_FAST_ABI_SIZE > 618
            winrt_ff_thunk618,
#endif

#if WINRT_FAST_ABI_SIZE > 619
            winrt_ff_thunk619,
#endif

#if WINRT_FAST_ABI_SIZE > 620
            winrt_ff_thunk620,
#endif

#if WINRT_FAST_ABI_SIZE > 621
            winrt_ff_thunk621,
#endif

#if WINRT_FAST_ABI_SIZE > 622
            winrt_ff_thunk622,
#endif

#if WINRT_FAST_ABI_SIZE > 623
            winrt_ff_thunk623,
#endif

#if WINRT_FAST_ABI_SIZE > 624
            winrt_ff_thunk624,
#endif

#if WINRT_FAST_ABI_SIZE > 625
            winrt_ff_thunk625,
#endif

#if WINRT_FAST_ABI_SIZE > 626
            winrt_ff_thunk626,
#endif

#if WINRT_FAST_ABI_SIZE > 627
            winrt_ff_thunk627,
#endif

#if WINRT_FAST_ABI_SIZE > 628
            winrt_ff_thunk628,
#endif

#if WINRT_FAST_ABI_SIZE > 629
            winrt_ff_thunk629,
#endif

#if WINRT_FAST_ABI_SIZE > 630
            winrt_ff_thunk630,
#endif

#if WINRT_FAST_ABI_SIZE > 631
            winrt_ff_thunk631,
#endif

#if WINRT_FAST_ABI_SIZE > 632
            winrt_ff_thunk632,
#endif

#if WINRT_FAST_ABI_SIZE > 633
            winrt_ff_thunk633,
#endif

#if WINRT_FAST_ABI_SIZE > 634
            winrt_ff_thunk634,
#endif

#if WINRT_FAST_ABI_SIZE > 635
            winrt_ff_thunk635,
#endif

#if WINRT_FAST_ABI_SIZE > 636
            winrt_ff_thunk636,
#endif

#if WINRT_FAST_ABI_SIZE > 637
            winrt_ff_thunk637,
#endif

#if WINRT_FAST_ABI_SIZE > 638
            winrt_ff_thunk638,
#endif

#if WINRT_FAST_ABI_SIZE > 639
            winrt_ff_thunk639,
#endif

#if WINRT_FAST_ABI_SIZE > 640
            winrt_ff_thunk640,
#endif

#if WINRT_FAST_ABI_SIZE > 641
            winrt_ff_thunk641,
#endif

#if WINRT_FAST_ABI_SIZE > 642
            winrt_ff_thunk642,
#endif

#if WINRT_FAST_ABI_SIZE > 643
            winrt_ff_thunk643,
#endif

#if WINRT_FAST_ABI_SIZE > 644
            winrt_ff_thunk644,
#endif

#if WINRT_FAST_ABI_SIZE > 645
            winrt_ff_thunk645,
#endif

#if WINRT_FAST_ABI_SIZE > 646
            winrt_ff_thunk646,
#endif

#if WINRT_FAST_ABI_SIZE > 647
            winrt_ff_thunk647,
#endif

#if WINRT_FAST_ABI_SIZE > 648
            winrt_ff_thunk648,
#endif

#if WINRT_FAST_ABI_SIZE > 649
            winrt_ff_thunk649,
#endif

#if WINRT_FAST_ABI_SIZE > 650
            winrt_ff_thunk650,
#endif

#if WINRT_FAST_ABI_SIZE > 651
            winrt_ff_thunk651,
#endif

#if WINRT_FAST_ABI_SIZE > 652
            winrt_ff_thunk652,
#endif

#if WINRT_FAST_ABI_SIZE > 653
            winrt_ff_thunk653,
#endif

#if WINRT_FAST_ABI_SIZE > 654
            winrt_ff_thunk654,
#endif

#if WINRT_FAST_ABI_SIZE > 655
            winrt_ff_thunk655,
#endif

#if WINRT_FAST_ABI_SIZE > 656
            winrt_ff_thunk656,
#endif

#if WINRT_FAST_ABI_SIZE > 657
            winrt_ff_thunk657,
#endif

#if WINRT_FAST_ABI_SIZE > 658
            winrt_ff_thunk658,
#endif

#if WINRT_FAST_ABI_SIZE > 659
            winrt_ff_thunk659,
#endif

#if WINRT_FAST_ABI_SIZE > 660
            winrt_ff_thunk660,
#endif

#if WINRT_FAST_ABI_SIZE > 661
            winrt_ff_thunk661,
#endif

#if WINRT_FAST_ABI_SIZE > 662
            winrt_ff_thunk662,
#endif

#if WINRT_FAST_ABI_SIZE > 663
            winrt_ff_thunk663,
#endif

#if WINRT_FAST_ABI_SIZE > 664
            winrt_ff_thunk664,
#endif

#if WINRT_FAST_ABI_SIZE > 665
            winrt_ff_thunk665,
#endif

#if WINRT_FAST_ABI_SIZE > 666
            winrt_ff_thunk666,
#endif

#if WINRT_FAST_ABI_SIZE > 667
            winrt_ff_thunk667,
#endif

#if WINRT_FAST_ABI_SIZE > 668
            winrt_ff_thunk668,
#endif

#if WINRT_FAST_ABI_SIZE > 669
            winrt_ff_thunk669,
#endif

#if WINRT_FAST_ABI_SIZE > 670
            winrt_ff_thunk670,
#endif

#if WINRT_FAST_ABI_SIZE > 671
            winrt_ff_thunk671,
#endif

#if WINRT_FAST_ABI_SIZE > 672
            winrt_ff_thunk672,
#endif

#if WINRT_FAST_ABI_SIZE > 673
            winrt_ff_thunk673,
#endif

#if WINRT_FAST_ABI_SIZE > 674
            winrt_ff_thunk674,
#endif

#if WINRT_FAST_ABI_SIZE > 675
            winrt_ff_thunk675,
#endif

#if WINRT_FAST_ABI_SIZE > 676
            winrt_ff_thunk676,
#endif

#if WINRT_FAST_ABI_SIZE > 677
            winrt_ff_thunk677,
#endif

#if WINRT_FAST_ABI_SIZE > 678
            winrt_ff_thunk678,
#endif

#if WINRT_FAST_ABI_SIZE > 679
            winrt_ff_thunk679,
#endif

#if WINRT_FAST_ABI_SIZE > 680
            winrt_ff_thunk680,
#endif

#if WINRT_FAST_ABI_SIZE > 681
            winrt_ff_thunk681,
#endif

#if WINRT_FAST_ABI_SIZE > 682
            winrt_ff_thunk682,
#endif

#if WINRT_FAST_ABI_SIZE > 683
            winrt_ff_thunk683,
#endif

#if WINRT_FAST_ABI_SIZE > 684
            winrt_ff_thunk684,
#endif

#if WINRT_FAST_ABI_SIZE > 685
            winrt_ff_thunk685,
#endif

#if WINRT_FAST_ABI_SIZE > 686
            winrt_ff_thunk686,
#endif

#if WINRT_FAST_ABI_SIZE > 687
            winrt_ff_thunk687,
#endif

#if WINRT_FAST_ABI_SIZE > 688
            winrt_ff_thunk688,
#endif

#if WINRT_FAST_ABI_SIZE > 689
            winrt_ff_thunk689,
#endif

#if WINRT_FAST_ABI_SIZE > 690
            winrt_ff_thunk690,
#endif

#if WINRT_FAST_ABI_SIZE > 691
            winrt_ff_thunk691,
#endif

#if WINRT_FAST_ABI_SIZE > 692
            winrt_ff_thunk692,
#endif

#if WINRT_FAST_ABI_SIZE > 693
            winrt_ff_thunk693,
#endif

#if WINRT_FAST_ABI_SIZE > 694
            winrt_ff_thunk694,
#endif

#if WINRT_FAST_ABI_SIZE > 695
            winrt_ff_thunk695,
#endif

#if WINRT_FAST_ABI_SIZE > 696
            winrt_ff_thunk696,
#endif

#if WINRT_FAST_ABI_SIZE > 697
            winrt_ff_thunk697,
#endif

#if WINRT_FAST_ABI_SIZE > 698
            winrt_ff_thunk698,
#endif

#if WINRT_FAST_ABI_SIZE > 699
            winrt_ff_thunk699,
#endif

#if WINRT_FAST_ABI_SIZE > 700
            winrt_ff_thunk700,
#endif

#if WINRT_FAST_ABI_SIZE > 701
            winrt_ff_thunk701,
#endif

#if WINRT_FAST_ABI_SIZE > 702
            winrt_ff_thunk702,
#endif

#if WINRT_FAST_ABI_SIZE > 703
            winrt_ff_thunk703,
#endif

#if WINRT_FAST_ABI_SIZE > 704
            winrt_ff_thunk704,
#endif

#if WINRT_FAST_ABI_SIZE > 705
            winrt_ff_thunk705,
#endif

#if WINRT_FAST_ABI_SIZE > 706
            winrt_ff_thunk706,
#endif

#if WINRT_FAST_ABI_SIZE > 707
            winrt_ff_thunk707,
#endif

#if WINRT_FAST_ABI_SIZE > 708
            winrt_ff_thunk708,
#endif

#if WINRT_FAST_ABI_SIZE > 709
            winrt_ff_thunk709,
#endif

#if WINRT_FAST_ABI_SIZE > 710
            winrt_ff_thunk710,
#endif

#if WINRT_FAST_ABI_SIZE > 711
            winrt_ff_thunk711,
#endif

#if WINRT_FAST_ABI_SIZE > 712
            winrt_ff_thunk712,
#endif

#if WINRT_FAST_ABI_SIZE > 713
            winrt_ff_thunk713,
#endif

#if WINRT_FAST_ABI_SIZE > 714
            winrt_ff_thunk714,
#endif

#if WINRT_FAST_ABI_SIZE > 715
            winrt_ff_thunk715,
#endif

#if WINRT_FAST_ABI_SIZE > 716
            winrt_ff_thunk716,
#endif

#if WINRT_FAST_ABI_SIZE > 717
            winrt_ff_thunk717,
#endif

#if WINRT_FAST_ABI_SIZE > 718
            winrt_ff_thunk718,
#endif

#if WINRT_FAST_ABI_SIZE > 719
            winrt_ff_thunk719,
#endif

#if WINRT_FAST_ABI_SIZE > 720
            winrt_ff_thunk720,
#endif

#if WINRT_FAST_ABI_SIZE > 721
            winrt_ff_thunk721,
#endif

#if WINRT_FAST_ABI_SIZE > 722
            winrt_ff_thunk722,
#endif

#if WINRT_FAST_ABI_SIZE > 723
            winrt_ff_thunk723,
#endif

#if WINRT_FAST_ABI_SIZE > 724
            winrt_ff_thunk724,
#endif

#if WINRT_FAST_ABI_SIZE > 725
            winrt_ff_thunk725,
#endif

#if WINRT_FAST_ABI_SIZE > 726
            winrt_ff_thunk726,
#endif

#if WINRT_FAST_ABI_SIZE > 727
            winrt_ff_thunk727,
#endif

#if WINRT_FAST_ABI_SIZE > 728
            winrt_ff_thunk728,
#endif

#if WINRT_FAST_ABI_SIZE > 729
            winrt_ff_thunk729,
#endif

#if WINRT_FAST_ABI_SIZE > 730
            winrt_ff_thunk730,
#endif

#if WINRT_FAST_ABI_SIZE > 731
            winrt_ff_thunk731,
#endif

#if WINRT_FAST_ABI_SIZE > 732
            winrt_ff_thunk732,
#endif

#if WINRT_FAST_ABI_SIZE > 733
            winrt_ff_thunk733,
#endif

#if WINRT_FAST_ABI_SIZE > 734
            winrt_ff_thunk734,
#endif

#if WINRT_FAST_ABI_SIZE > 735
            winrt_ff_thunk735,
#endif

#if WINRT_FAST_ABI_SIZE > 736
            winrt_ff_thunk736,
#endif

#if WINRT_FAST_ABI_SIZE > 737
            winrt_ff_thunk737,
#endif

#if WINRT_FAST_ABI_SIZE > 738
            winrt_ff_thunk738,
#endif

#if WINRT_FAST_ABI_SIZE > 739
            winrt_ff_thunk739,
#endif

#if WINRT_FAST_ABI_SIZE > 740
            winrt_ff_thunk740,
#endif

#if WINRT_FAST_ABI_SIZE > 741
            winrt_ff_thunk741,
#endif

#if WINRT_FAST_ABI_SIZE > 742
            winrt_ff_thunk742,
#endif

#if WINRT_FAST_ABI_SIZE > 743
            winrt_ff_thunk743,
#endif

#if WINRT_FAST_ABI_SIZE > 744
            winrt_ff_thunk744,
#endif

#if WINRT_FAST_ABI_SIZE > 745
            winrt_ff_thunk745,
#endif

#if WINRT_FAST_ABI_SIZE > 746
            winrt_ff_thunk746,
#endif

#if WINRT_FAST_ABI_SIZE > 747
            winrt_ff_thunk747,
#endif

#if WINRT_FAST_ABI_SIZE > 748
            winrt_ff_thunk748,
#endif

#if WINRT_FAST_ABI_SIZE > 749
            winrt_ff_thunk749,
#endif

#if WINRT_FAST_ABI_SIZE > 750
            winrt_ff_thunk750,
#endif

#if WINRT_FAST_ABI_SIZE > 751
            winrt_ff_thunk751,
#endif

#if WINRT_FAST_ABI_SIZE > 752
            winrt_ff_thunk752,
#endif

#if WINRT_FAST_ABI_SIZE > 753
            winrt_ff_thunk753,
#endif

#if WINRT_FAST_ABI_SIZE > 754
            winrt_ff_thunk754,
#endif

#if WINRT_FAST_ABI_SIZE > 755
            winrt_ff_thunk755,
#endif

#if WINRT_FAST_ABI_SIZE > 756
            winrt_ff_thunk756,
#endif

#if WINRT_FAST_ABI_SIZE > 757
            winrt_ff_thunk757,
#endif

#if WINRT_FAST_ABI_SIZE > 758
            winrt_ff_thunk758,
#endif

#if WINRT_FAST_ABI_SIZE > 759
            winrt_ff_thunk759,
#endif

#if WINRT_FAST_ABI_SIZE > 760
            winrt_ff_thunk760,
#endif

#if WINRT_FAST_ABI_SIZE > 761
            winrt_ff_thunk761,
#endif

#if WINRT_FAST_ABI_SIZE > 762
            winrt_ff_thunk762,
#endif

#if WINRT_FAST_ABI_SIZE > 763
            winrt_ff_thunk763,
#endif

#if WINRT_FAST_ABI_SIZE > 764
            winrt_ff_thunk764,
#endif

#if WINRT_FAST_ABI_SIZE > 765
            winrt_ff_thunk765,
#endif

#if WINRT_FAST_ABI_SIZE > 766
            winrt_ff_thunk766,
#endif

#if WINRT_FAST_ABI_SIZE > 767
            winrt_ff_thunk767,
#endif

#if WINRT_FAST_ABI_SIZE > 768
            winrt_ff_thunk768,
#endif

#if WINRT_FAST_ABI_SIZE > 769
            winrt_ff_thunk769,
#endif

#if WINRT_FAST_ABI_SIZE > 770
            winrt_ff_thunk770,
#endif

#if WINRT_FAST_ABI_SIZE > 771
            winrt_ff_thunk771,
#endif

#if WINRT_FAST_ABI_SIZE > 772
            winrt_ff_thunk772,
#endif

#if WINRT_FAST_ABI_SIZE > 773
            winrt_ff_thunk773,
#endif

#if WINRT_FAST_ABI_SIZE > 774
            winrt_ff_thunk774,
#endif

#if WINRT_FAST_ABI_SIZE > 775
            winrt_ff_thunk775,
#endif

#if WINRT_FAST_ABI_SIZE > 776
            winrt_ff_thunk776,
#endif

#if WINRT_FAST_ABI_SIZE > 777
            winrt_ff_thunk777,
#endif

#if WINRT_FAST_ABI_SIZE > 778
            winrt_ff_thunk778,
#endif

#if WINRT_FAST_ABI_SIZE > 779
            winrt_ff_thunk779,
#endif

#if WINRT_FAST_ABI_SIZE > 780
            winrt_ff_thunk780,
#endif

#if WINRT_FAST_ABI_SIZE > 781
            winrt_ff_thunk781,
#endif

#if WINRT_FAST_ABI_SIZE > 782
            winrt_ff_thunk782,
#endif

#if WINRT_FAST_ABI_SIZE > 783
            winrt_ff_thunk783,
#endif

#if WINRT_FAST_ABI_SIZE > 784
            winrt_ff_thunk784,
#endif

#if WINRT_FAST_ABI_SIZE > 785
            winrt_ff_thunk785,
#endif

#if WINRT_FAST_ABI_SIZE > 786
            winrt_ff_thunk786,
#endif

#if WINRT_FAST_ABI_SIZE > 787
            winrt_ff_thunk787,
#endif

#if WINRT_FAST_ABI_SIZE > 788
            winrt_ff_thunk788,
#endif

#if WINRT_FAST_ABI_SIZE > 789
            winrt_ff_thunk789,
#endif

#if WINRT_FAST_ABI_SIZE > 790
            winrt_ff_thunk790,
#endif

#if WINRT_FAST_ABI_SIZE > 791
            winrt_ff_thunk791,
#endif

#if WINRT_FAST_ABI_SIZE > 792
            winrt_ff_thunk792,
#endif

#if WINRT_FAST_ABI_SIZE > 793
            winrt_ff_thunk793,
#endif

#if WINRT_FAST_ABI_SIZE > 794
            winrt_ff_thunk794,
#endif

#if WINRT_FAST_ABI_SIZE > 795
            winrt_ff_thunk795,
#endif

#if WINRT_FAST_ABI_SIZE > 796
            winrt_ff_thunk796,
#endif

#if WINRT_FAST_ABI_SIZE > 797
            winrt_ff_thunk797,
#endif

#if WINRT_FAST_ABI_SIZE > 798
            winrt_ff_thunk798,
#endif

#if WINRT_FAST_ABI_SIZE > 799
            winrt_ff_thunk799,
#endif

#if WINRT_FAST_ABI_SIZE > 800
            winrt_ff_thunk800,
#endif

#if WINRT_FAST_ABI_SIZE > 801
            winrt_ff_thunk801,
#endif

#if WINRT_FAST_ABI_SIZE > 802
            winrt_ff_thunk802,
#endif

#if WINRT_FAST_ABI_SIZE > 803
            winrt_ff_thunk803,
#endif

#if WINRT_FAST_ABI_SIZE > 804
            winrt_ff_thunk804,
#endif

#if WINRT_FAST_ABI_SIZE > 805
            winrt_ff_thunk805,
#endif

#if WINRT_FAST_ABI_SIZE > 806
            winrt_ff_thunk806,
#endif

#if WINRT_FAST_ABI_SIZE > 807
            winrt_ff_thunk807,
#endif

#if WINRT_FAST_ABI_SIZE > 808
            winrt_ff_thunk808,
#endif

#if WINRT_FAST_ABI_SIZE > 809
            winrt_ff_thunk809,
#endif

#if WINRT_FAST_ABI_SIZE > 810
            winrt_ff_thunk810,
#endif

#if WINRT_FAST_ABI_SIZE > 811
            winrt_ff_thunk811,
#endif

#if WINRT_FAST_ABI_SIZE > 812
            winrt_ff_thunk812,
#endif

#if WINRT_FAST_ABI_SIZE > 813
            winrt_ff_thunk813,
#endif

#if WINRT_FAST_ABI_SIZE > 814
            winrt_ff_thunk814,
#endif

#if WINRT_FAST_ABI_SIZE > 815
            winrt_ff_thunk815,
#endif

#if WINRT_FAST_ABI_SIZE > 816
            winrt_ff_thunk816,
#endif

#if WINRT_FAST_ABI_SIZE > 817
            winrt_ff_thunk817,
#endif

#if WINRT_FAST_ABI_SIZE > 818
            winrt_ff_thunk818,
#endif

#if WINRT_FAST_ABI_SIZE > 819
            winrt_ff_thunk819,
#endif

#if WINRT_FAST_ABI_SIZE > 820
            winrt_ff_thunk820,
#endif

#if WINRT_FAST_ABI_SIZE > 821
            winrt_ff_thunk821,
#endif

#if WINRT_FAST_ABI_SIZE > 822
            winrt_ff_thunk822,
#endif

#if WINRT_FAST_ABI_SIZE > 823
            winrt_ff_thunk823,
#endif

#if WINRT_FAST_ABI_SIZE > 824
            winrt_ff_thunk824,
#endif

#if WINRT_FAST_ABI_SIZE > 825
            winrt_ff_thunk825,
#endif

#if WINRT_FAST_ABI_SIZE > 826
            winrt_ff_thunk826,
#endif

#if WINRT_FAST_ABI_SIZE > 827
            winrt_ff_thunk827,
#endif

#if WINRT_FAST_ABI_SIZE > 828
            winrt_ff_thunk828,
#endif

#if WINRT_FAST_ABI_SIZE > 829
            winrt_ff_thunk829,
#endif

#if WINRT_FAST_ABI_SIZE > 830
            winrt_ff_thunk830,
#endif

#if WINRT_FAST_ABI_SIZE > 831
            winrt_ff_thunk831,
#endif

#if WINRT_FAST_ABI_SIZE > 832
            winrt_ff_thunk832,
#endif

#if WINRT_FAST_ABI_SIZE > 833
            winrt_ff_thunk833,
#endif

#if WINRT_FAST_ABI_SIZE > 834
            winrt_ff_thunk834,
#endif

#if WINRT_FAST_ABI_SIZE > 835
            winrt_ff_thunk835,
#endif

#if WINRT_FAST_ABI_SIZE > 836
            winrt_ff_thunk836,
#endif

#if WINRT_FAST_ABI_SIZE > 837
            winrt_ff_thunk837,
#endif

#if WINRT_FAST_ABI_SIZE > 838
            winrt_ff_thunk838,
#endif

#if WINRT_FAST_ABI_SIZE > 839
            winrt_ff_thunk839,
#endif

#if WINRT_FAST_ABI_SIZE > 840
            winrt_ff_thunk840,
#endif

#if WINRT_FAST_ABI_SIZE > 841
            winrt_ff_thunk841,
#endif

#if WINRT_FAST_ABI_SIZE > 842
            winrt_ff_thunk842,
#endif

#if WINRT_FAST_ABI_SIZE > 843
            winrt_ff_thunk843,
#endif

#if WINRT_FAST_ABI_SIZE > 844
            winrt_ff_thunk844,
#endif

#if WINRT_FAST_ABI_SIZE > 845
            winrt_ff_thunk845,
#endif

#if WINRT_FAST_ABI_SIZE > 846
            winrt_ff_thunk846,
#endif

#if WINRT_FAST_ABI_SIZE > 847
            winrt_ff_thunk847,
#endif

#if WINRT_FAST_ABI_SIZE > 848
            winrt_ff_thunk848,
#endif

#if WINRT_FAST_ABI_SIZE > 849
            winrt_ff_thunk849,
#endif

#if WINRT_FAST_ABI_SIZE > 850
            winrt_ff_thunk850,
#endif

#if WINRT_FAST_ABI_SIZE > 851
            winrt_ff_thunk851,
#endif

#if WINRT_FAST_ABI_SIZE > 852
            winrt_ff_thunk852,
#endif

#if WINRT_FAST_ABI_SIZE > 853
            winrt_ff_thunk853,
#endif

#if WINRT_FAST_ABI_SIZE > 854
            winrt_ff_thunk854,
#endif

#if WINRT_FAST_ABI_SIZE > 855
            winrt_ff_thunk855,
#endif

#if WINRT_FAST_ABI_SIZE > 856
            winrt_ff_thunk856,
#endif

#if WINRT_FAST_ABI_SIZE > 857
            winrt_ff_thunk857,
#endif

#if WINRT_FAST_ABI_SIZE > 858
            winrt_ff_thunk858,
#endif

#if WINRT_FAST_ABI_SIZE > 859
            winrt_ff_thunk859,
#endif

#if WINRT_FAST_ABI_SIZE > 860
            winrt_ff_thunk860,
#endif

#if WINRT_FAST_ABI_SIZE > 861
            winrt_ff_thunk861,
#endif

#if WINRT_FAST_ABI_SIZE > 862
            winrt_ff_thunk862,
#endif

#if WINRT_FAST_ABI_SIZE > 863
            winrt_ff_thunk863,
#endif

#if WINRT_FAST_ABI_SIZE > 864
            winrt_ff_thunk864,
#endif

#if WINRT_FAST_ABI_SIZE > 865
            winrt_ff_thunk865,
#endif

#if WINRT_FAST_ABI_SIZE > 866
            winrt_ff_thunk866,
#endif

#if WINRT_FAST_ABI_SIZE > 867
            winrt_ff_thunk867,
#endif

#if WINRT_FAST_ABI_SIZE > 868
            winrt_ff_thunk868,
#endif

#if WINRT_FAST_ABI_SIZE > 869
            winrt_ff_thunk869,
#endif

#if WINRT_FAST_ABI_SIZE > 870
            winrt_ff_thunk870,
#endif

#if WINRT_FAST_ABI_SIZE > 871
            winrt_ff_thunk871,
#endif

#if WINRT_FAST_ABI_SIZE > 872
            winrt_ff_thunk872,
#endif

#if WINRT_FAST_ABI_SIZE > 873
            winrt_ff_thunk873,
#endif

#if WINRT_FAST_ABI_SIZE > 874
            winrt_ff_thunk874,
#endif

#if WINRT_FAST_ABI_SIZE > 875
            winrt_ff_thunk875,
#endif

#if WINRT_FAST_ABI_SIZE > 876
            winrt_ff_thunk876,
#endif

#if WINRT_FAST_ABI_SIZE > 877
            winrt_ff_thunk877,
#endif

#if WINRT_FAST_ABI_SIZE > 878
            winrt_ff_thunk878,
#endif

#if WINRT_FAST_ABI_SIZE > 879
            winrt_ff_thunk879,
#endif

#if WINRT_FAST_ABI_SIZE > 880
            winrt_ff_thunk880,
#endif

#if WINRT_FAST_ABI_SIZE > 881
            winrt_ff_thunk881,
#endif

#if WINRT_FAST_ABI_SIZE > 882
            winrt_ff_thunk882,
#endif

#if WINRT_FAST_ABI_SIZE > 883
            winrt_ff_thunk883,
#endif

#if WINRT_FAST_ABI_SIZE > 884
            winrt_ff_thunk884,
#endif

#if WINRT_FAST_ABI_SIZE > 885
            winrt_ff_thunk885,
#endif

#if WINRT_FAST_ABI_SIZE > 886
            winrt_ff_thunk886,
#endif

#if WINRT_FAST_ABI_SIZE > 887
            winrt_ff_thunk887,
#endif

#if WINRT_FAST_ABI_SIZE > 888
            winrt_ff_thunk888,
#endif

#if WINRT_FAST_ABI_SIZE > 889
            winrt_ff_thunk889,
#endif

#if WINRT_FAST_ABI_SIZE > 890
            winrt_ff_thunk890,
#endif

#if WINRT_FAST_ABI_SIZE > 891
            winrt_ff_thunk891,
#endif

#if WINRT_FAST_ABI_SIZE > 892
            winrt_ff_thunk892,
#endif

#if WINRT_FAST_ABI_SIZE > 893
            winrt_ff_thunk893,
#endif

#if WINRT_FAST_ABI_SIZE > 894
            winrt_ff_thunk894,
#endif

#if WINRT_FAST_ABI_SIZE > 895
            winrt_ff_thunk895,
#endif

#if WINRT_FAST_ABI_SIZE > 896
            winrt_ff_thunk896,
#endif

#if WINRT_FAST_ABI_SIZE > 897
            winrt_ff_thunk897,
#endif

#if WINRT_FAST_ABI_SIZE > 898
            winrt_ff_thunk898,
#endif

#if WINRT_FAST_ABI_SIZE > 899
            winrt_ff_thunk899,
#endif

#if WINRT_FAST_ABI_SIZE > 900
            winrt_ff_thunk900,
#endif

#if WINRT_FAST_ABI_SIZE > 901
            winrt_ff_thunk901,
#endif

#if WINRT_FAST_ABI_SIZE > 902
            winrt_ff_thunk902,
#endif

#if WINRT_FAST_ABI_SIZE > 903
            winrt_ff_thunk903,
#endif

#if WINRT_FAST_ABI_SIZE > 904
            winrt_ff_thunk904,
#endif

#if WINRT_FAST_ABI_SIZE > 905
            winrt_ff_thunk905,
#endif

#if WINRT_FAST_ABI_SIZE > 906
            winrt_ff_thunk906,
#endif

#if WINRT_FAST_ABI_SIZE > 907
            winrt_ff_thunk907,
#endif

#if WINRT_FAST_ABI_SIZE > 908
            winrt_ff_thunk908,
#endif

#if WINRT_FAST_ABI_SIZE > 909
            winrt_ff_thunk909,
#endif

#if WINRT_FAST_ABI_SIZE > 910
            winrt_ff_thunk910,
#endif

#if WINRT_FAST_ABI_SIZE > 911
            winrt_ff_thunk911,
#endif

#if WINRT_FAST_ABI_SIZE > 912
            winrt_ff_thunk912,
#endif

#if WINRT_FAST_ABI_SIZE > 913
            winrt_ff_thunk913,
#endif

#if WINRT_FAST_ABI_SIZE > 914
            winrt_ff_thunk914,
#endif

#if WINRT_FAST_ABI_SIZE > 915
            winrt_ff_thunk915,
#endif

#if WINRT_FAST_ABI_SIZE > 916
            winrt_ff_thunk916,
#endif

#if WINRT_FAST_ABI_SIZE > 917
            winrt_ff_thunk917,
#endif

#if WINRT_FAST_ABI_SIZE > 918
            winrt_ff_thunk918,
#endif

#if WINRT_FAST_ABI_SIZE > 919
            winrt_ff_thunk919,
#endif

#if WINRT_FAST_ABI_SIZE > 920
            winrt_ff_thunk920,
#endif

#if WINRT_FAST_ABI_SIZE > 921
            winrt_ff_thunk921,
#endif

#if WINRT_FAST_ABI_SIZE > 922
            winrt_ff_thunk922,
#endif

#if WINRT_FAST_ABI_SIZE > 923
            winrt_ff_thunk923,
#endif

#if WINRT_FAST_ABI_SIZE > 924
            winrt_ff_thunk924,
#endif

#if WINRT_FAST_ABI_SIZE > 925
            winrt_ff_thunk925,
#endif

#if WINRT_FAST_ABI_SIZE > 926
            winrt_ff_thunk926,
#endif

#if WINRT_FAST_ABI_SIZE > 927
            winrt_ff_thunk927,
#endif

#if WINRT_FAST_ABI_SIZE > 928
            winrt_ff_thunk928,
#endif

#if WINRT_FAST_ABI_SIZE > 929
            winrt_ff_thunk929,
#endif

#if WINRT_FAST_ABI_SIZE > 930
            winrt_ff_thunk930,
#endif

#if WINRT_FAST_ABI_SIZE > 931
            winrt_ff_thunk931,
#endif

#if WINRT_FAST_ABI_SIZE > 932
            winrt_ff_thunk932,
#endif

#if WINRT_FAST_ABI_SIZE > 933
            winrt_ff_thunk933,
#endif

#if WINRT_FAST_ABI_SIZE > 934
            winrt_ff_thunk934,
#endif

#if WINRT_FAST_ABI_SIZE > 935
            winrt_ff_thunk935,
#endif

#if WINRT_FAST_ABI_SIZE > 936
            winrt_ff_thunk936,
#endif

#if WINRT_FAST_ABI_SIZE > 937
            winrt_ff_thunk937,
#endif

#if WINRT_FAST_ABI_SIZE > 938
            winrt_ff_thunk938,
#endif

#if WINRT_FAST_ABI_SIZE > 939
            winrt_ff_thunk939,
#endif

#if WINRT_FAST_ABI_SIZE > 940
            winrt_ff_thunk940,
#endif

#if WINRT_FAST_ABI_SIZE > 941
            winrt_ff_thunk941,
#endif

#if WINRT_FAST_ABI_SIZE > 942
            winrt_ff_thunk942,
#endif

#if WINRT_FAST_ABI_SIZE > 943
            winrt_ff_thunk943,
#endif

#if WINRT_FAST_ABI_SIZE > 944
            winrt_ff_thunk944,
#endif

#if WINRT_FAST_ABI_SIZE > 945
            winrt_ff_thunk945,
#endif

#if WINRT_FAST_ABI_SIZE > 946
            winrt_ff_thunk946,
#endif

#if WINRT_FAST_ABI_SIZE > 947
            winrt_ff_thunk947,
#endif

#if WINRT_FAST_ABI_SIZE > 948
            winrt_ff_thunk948,
#endif

#if WINRT_FAST_ABI_SIZE > 949
            winrt_ff_thunk949,
#endif

#if WINRT_FAST_ABI_SIZE > 950
            winrt_ff_thunk950,
#endif

#if WINRT_FAST_ABI_SIZE > 951
            winrt_ff_thunk951,
#endif

#if WINRT_FAST_ABI_SIZE > 952
            winrt_ff_thunk952,
#endif

#if WINRT_FAST_ABI_SIZE > 953
            winrt_ff_thunk953,
#endif

#if WINRT_FAST_ABI_SIZE > 954
            winrt_ff_thunk954,
#endif

#if WINRT_FAST_ABI_SIZE > 955
            winrt_ff_thunk955,
#endif

#if WINRT_FAST_ABI_SIZE > 956
            winrt_ff_thunk956,
#endif

#if WINRT_FAST_ABI_SIZE > 957
            winrt_ff_thunk957,
#endif

#if WINRT_FAST_ABI_SIZE > 958
            winrt_ff_thunk958,
#endif

#if WINRT_FAST_ABI_SIZE > 959
            winrt_ff_thunk959,
#endif

#if WINRT_FAST_ABI_SIZE > 960
            winrt_ff_thunk960,
#endif

#if WINRT_FAST_ABI_SIZE > 961
            winrt_ff_thunk961,
#endif

#if WINRT_FAST_ABI_SIZE > 962
            winrt_ff_thunk962,
#endif

#if WINRT_FAST_ABI_SIZE > 963
            winrt_ff_thunk963,
#endif

#if WINRT_FAST_ABI_SIZE > 964
            winrt_ff_thunk964,
#endif

#if WINRT_FAST_ABI_SIZE > 965
            winrt_ff_thunk965,
#endif

#if WINRT_FAST_ABI_SIZE > 966
            winrt_ff_thunk966,
#endif

#if WINRT_FAST_ABI_SIZE > 967
            winrt_ff_thunk967,
#endif

#if WINRT_FAST_ABI_SIZE > 968
            winrt_ff_thunk968,
#endif

#if WINRT_FAST_ABI_SIZE > 969
            winrt_ff_thunk969,
#endif

#if WINRT_FAST_ABI_SIZE > 970
            winrt_ff_thunk970,
#endif

#if WINRT_FAST_ABI_SIZE > 971
            winrt_ff_thunk971,
#endif

#if WINRT_FAST_ABI_SIZE > 972
            winrt_ff_thunk972,
#endif

#if WINRT_FAST_ABI_SIZE > 973
            winrt_ff_thunk973,
#endif

#if WINRT_FAST_ABI_SIZE > 974
            winrt_ff_thunk974,
#endif

#if WINRT_FAST_ABI_SIZE > 975
            winrt_ff_thunk975,
#endif

#if WINRT_FAST_ABI_SIZE > 976
            winrt_ff_thunk976,
#endif

#if WINRT_FAST_ABI_SIZE > 977
            winrt_ff_thunk977,
#endif

#if WINRT_FAST_ABI_SIZE > 978
            winrt_ff_thunk978,
#endif

#if WINRT_FAST_ABI_SIZE > 979
            winrt_ff_thunk979,
#endif

#if WINRT_FAST_ABI_SIZE > 980
            winrt_ff_thunk980,
#endif

#if WINRT_FAST_ABI_SIZE > 981
            winrt_ff_thunk981,
#endif

#if WINRT_FAST_ABI_SIZE > 982
            winrt_ff_thunk982,
#endif

#if WINRT_FAST_ABI_SIZE > 983
            winrt_ff_thunk983,
#endif

#if WINRT_FAST_ABI_SIZE > 984
            winrt_ff_thunk984,
#endif

#if WINRT_FAST_ABI_SIZE > 985
            winrt_ff_thunk985,
#endif

#if WINRT_FAST_ABI_SIZE > 986
            winrt_ff_thunk986,
#endif

#if WINRT_FAST_ABI_SIZE > 987
            winrt_ff_thunk987,
#endif

#if WINRT_FAST_ABI_SIZE > 988
            winrt_ff_thunk988,
#endif

#if WINRT_FAST_ABI_SIZE > 989
            winrt_ff_thunk989,
#endif

#if WINRT_FAST_ABI_SIZE > 990
            winrt_ff_thunk990,
#endif

#if WINRT_FAST_ABI_SIZE > 991
            winrt_ff_thunk991,
#endif

#if WINRT_FAST_ABI_SIZE > 992
            winrt_ff_thunk992,
#endif

#if WINRT_FAST_ABI_SIZE > 993
            winrt_ff_thunk993,
#endif

#if WINRT_FAST_ABI_SIZE > 994
            winrt_ff_thunk994,
#endif

#if WINRT_FAST_ABI_SIZE > 995
            winrt_ff_thunk995,
#endif

#if WINRT_FAST_ABI_SIZE > 996
            winrt_ff_thunk996,
#endif

#if WINRT_FAST_ABI_SIZE > 997
            winrt_ff_thunk997,
#endif

#if WINRT_FAST_ABI_SIZE > 998
            winrt_ff_thunk998,
#endif

#if WINRT_FAST_ABI_SIZE > 999
            winrt_ff_thunk999,
#endif

#if WINRT_FAST_ABI_SIZE > 1000
            winrt_ff_thunk1000,
#endif

#if WINRT_FAST_ABI_SIZE > 1001
            winrt_ff_thunk1001,
#endif

#if WINRT_FAST_ABI_SIZE > 1002
            winrt_ff_thunk1002,
#endif

#if WINRT_FAST_ABI_SIZE > 1003
            winrt_ff_thunk1003,
#endif

#if WINRT_FAST_ABI_SIZE > 1004
            winrt_ff_thunk1004,
#endif

#if WINRT_FAST_ABI_SIZE > 1005
            winrt_ff_thunk1005,
#endif

#if WINRT_FAST_ABI_SIZE > 1006
            winrt_ff_thunk1006,
#endif

#if WINRT_FAST_ABI_SIZE > 1007
            winrt_ff_thunk1007,
#endif

#if WINRT_FAST_ABI_SIZE > 1008
            winrt_ff_thunk1008,
#endif

#if WINRT_FAST_ABI_SIZE > 1009
            winrt_ff_thunk1009,
#endif

#if WINRT_FAST_ABI_SIZE > 1010
            winrt_ff_thunk1010,
#endif

#if WINRT_FAST_ABI_SIZE > 1011
            winrt_ff_thunk1011,
#endif

#if WINRT_FAST_ABI_SIZE > 1012
            winrt_ff_thunk1012,
#endif

#if WINRT_FAST_ABI_SIZE > 1013
            winrt_ff_thunk1013,
#endif

#if WINRT_FAST_ABI_SIZE > 1014
            winrt_ff_thunk1014,
#endif

#if WINRT_FAST_ABI_SIZE > 1015
            winrt_ff_thunk1015,
#endif

#if WINRT_FAST_ABI_SIZE > 1016
            winrt_ff_thunk1016,
#endif

#if WINRT_FAST_ABI_SIZE > 1017
            winrt_ff_thunk1017,
#endif

#if WINRT_FAST_ABI_SIZE > 1018
            winrt_ff_thunk1018,
#endif

#if WINRT_FAST_ABI_SIZE > 1019
            winrt_ff_thunk1019,
#endif

#if WINRT_FAST_ABI_SIZE > 1020
            winrt_ff_thunk1020,
#endif

#if WINRT_FAST_ABI_SIZE > 1021
            winrt_ff_thunk1021,
#endif

#if WINRT_FAST_ABI_SIZE > 1022
            winrt_ff_thunk1022,
#endif

#if WINRT_FAST_ABI_SIZE > 1023
            winrt_ff_thunk1023,
#endif
        };
    };

    // Enforce assumptions made by thunk asm code
    static_assert(offsetof(fast_abi_forwarder, m_vfptr) == 0);
    static_assert(offsetof(fast_abi_forwarder, m_owner) == sizeof(fast_abi_forwarder::m_vfptr));
    static_assert(offsetof(fast_abi_forwarder, m_offset) == sizeof(fast_abi_forwarder::m_vfptr) + sizeof(fast_abi_forwarder::m_owner));
}

namespace winrt
{
    template<typename TGuid>
    auto make_fast_abi_forwarder(void* owner, TGuid const& guid, size_t offset)
    {
        using ff_guid = impl::fast_abi_forwarder::guid;
        static_assert(sizeof(ff_guid) == sizeof(TGuid));
        return new impl::fast_abi_forwarder(owner, *reinterpret_cast<ff_guid const*>(&guid), offset);
    }
}

#undef WINRT_IMPL_STRING
#undef WINRT_IMPL_STRING_1
#endif
