return {
	include = function()
		defines { 'WEBRTC_NS_FIXED=1', 'WEBRTC_WIN=1', 'WEBRTC_AUDIO_PROCESSING_ONLY_BUILD=1' }
	
		includedirs "../vendor/webrtc-audio-processing/"
	end,
	
	run = function()
		language 'C++'
		kind 'StaticLib'
		
		defines { 'NDEBUG=1', 'NOMINMAX', 'WIN32_LEAN_AND_MEAN' }
		
		buildoptions { '/MP' }
		
		--\s+([^\s]+) ?\\?$
		
		files_project '../vendor/webrtc-audio-processing/webrtc/'  {
			'common_types.cc',
			
			'base/criticalsection.cc',
			'base/criticalsection.h',
			'base/checks.cc',
			'base/checks.h',
			'base/event.cc',
			'base/event.h',
			'base/platform_thread.cc',
			'base/platform_thread.h',
			'base/platform_file.cc',
			'base/platform_file.h',
			'base/stringutils.cc',
			'base/stringutils.h',
			'base/thread_checker.h',
			'base/thread_checker_impl.cc',
			'base/thread_checker_impl.h',
		     
			'common_audio/resampler/include/push_resampler.h',
			'common_audio/resampler/include/resampler.h',
			'common_audio/resampler/push_sinc_resampler.h',
			'common_audio/resampler/sinc_resampler.h',
			'common_audio/resampler/sinusoidal_linear_chirp_source.h',
			'common_audio/resampler/push_resampler.cc',
			'common_audio/resampler/push_sinc_resampler.cc',
			'common_audio/resampler/resampler.cc',
			'common_audio/resampler/sinc_resampler.cc',
			'common_audio/resampler/sinusoidal_linear_chirp_source.cc',
			'common_audio/signal_processing/include/real_fft.h',
			'common_audio/signal_processing/include/signal_processing_library.h',
			'common_audio/signal_processing/include/spl_inl.h',
			'common_audio/signal_processing/include/spl_inl_armv7.h',
			'common_audio/signal_processing/include/spl_inl_mips.h',
			'common_audio/signal_processing/auto_corr_to_refl_coef.c',
			'common_audio/signal_processing/auto_correlation.c',
			'common_audio/signal_processing/complex_fft.c',
			'common_audio/signal_processing/complex_fft_tables.h',
			'common_audio/signal_processing/copy_set_operations.c',
			'common_audio/signal_processing/cross_correlation.c',
			'common_audio/signal_processing/division_operations.c',
			'common_audio/signal_processing/dot_product_with_scale.c',
			'common_audio/signal_processing/downsample_fast.c',
			'common_audio/signal_processing/energy.c',
			'common_audio/signal_processing/filter_ar.c',
			'common_audio/signal_processing/filter_ma_fast_q12.c',
			'common_audio/signal_processing/get_hanning_window.c',
			'common_audio/signal_processing/get_scaling_square.c',
			'common_audio/signal_processing/ilbc_specific_functions.c',
			'common_audio/signal_processing/levinson_durbin.c',
			'common_audio/signal_processing/lpc_to_refl_coef.c',
			'common_audio/signal_processing/min_max_operations.c',
			'common_audio/signal_processing/randomization_functions.c',
			'common_audio/signal_processing/real_fft.c',
			'common_audio/signal_processing/refl_coef_to_lpc.c',
			'common_audio/signal_processing/resample.c',
			'common_audio/signal_processing/resample_48khz.c',
			'common_audio/signal_processing/resample_by_2.c',
			'common_audio/signal_processing/resample_by_2_internal.c',
			'common_audio/signal_processing/resample_by_2_internal.h',
			'common_audio/signal_processing/resample_fractional.c',
			'common_audio/signal_processing/spl_init.c',
			'common_audio/signal_processing/spl_sqrt.c',
			'common_audio/signal_processing/splitting_filter.c',
			'common_audio/signal_processing/sqrt_of_one_minus_x_squared.c',
			'common_audio/signal_processing/vector_scaling_operations.c',
			'common_audio/vad/include/vad.h',
			'common_audio/vad/include/webrtc_vad.h',
			'common_audio/vad/vad.cc',
			'common_audio/vad/vad_core.c',
			'common_audio/vad/vad_core.h',
			'common_audio/vad/vad_filterbank.c',
			'common_audio/vad/vad_filterbank.h',
			'common_audio/vad/vad_gmm.c',
			'common_audio/vad/vad_gmm.h',
			'common_audio/vad/vad_sp.c',
			'common_audio/vad/vad_sp.h',
			'common_audio/vad/webrtc_vad.c',
			'common_audio/audio_converter.cc',
			'common_audio/audio_converter.h',
			'common_audio/audio_ring_buffer.cc',
			'common_audio/audio_ring_buffer.h',
			'common_audio/audio_util.cc',
			'common_audio/blocker.cc',
			'common_audio/blocker.h',
			'common_audio/channel_buffer.cc',
			'common_audio/channel_buffer.h',
			'common_audio/fft4g.c',
			'common_audio/fft4g.h',
			'common_audio/fir_filter.cc',
			'common_audio/fir_filter.h',
			'common_audio/lapped_transform.cc',
			'common_audio/lapped_transform.h',
			'common_audio/real_fourier.cc',
			'common_audio/real_fourier.h',
			'common_audio/real_fourier_ooura.cc',
			'common_audio/real_fourier_ooura.h',
			'common_audio/real_fourier_openmax.h',
			'common_audio/ring_buffer.h',
			'common_audio/ring_buffer.c',
			'common_audio/sparse_fir_filter.cc',
			'common_audio/sparse_fir_filter.h',
			'common_audio/wav_file.h',
			'common_audio/wav_file.cc',
			'common_audio/wav_header.h',
			'common_audio/wav_header.cc',
			'common_audio/window_generator.h',
			'common_audio/window_generator.cc',
							 
			'common_audio/signal_processing/complex_bit_reverse.c',
			'common_audio/signal_processing/filter_ar_fast_q12.c',
			'common_audio/signal_processing/spl_sqrt_floor.c',
							 
			'common_audio/resampler/sinc_resampler_sse.cc',
			'common_audio/fir_filter_sse.cc',
			'common_audio/fir_filter_sse.h',
			
'modules/audio_coding/codecs/isac/main/include/isac.h',
'modules/audio_coding/codecs/isac/main/source/arith_routines.c',
'modules/audio_coding/codecs/isac/main/source/arith_routines.h',
'modules/audio_coding/codecs/isac/main/source/arith_routines_hist.c',
'modules/audio_coding/codecs/isac/main/source/arith_routines_logist.c',
'modules/audio_coding/codecs/isac/main/source/codec.h',
'modules/audio_coding/codecs/isac/main/source/encode_lpc_swb.c',
'modules/audio_coding/codecs/isac/main/source/encode_lpc_swb.h',
'modules/audio_coding/codecs/isac/main/source/entropy_coding.c',
'modules/audio_coding/codecs/isac/main/source/entropy_coding.h',
'modules/audio_coding/codecs/isac/main/source/filter_functions.c',
'modules/audio_coding/codecs/isac/main/source/filterbanks.c',
'modules/audio_coding/codecs/isac/main/source/filterbank_tables.c',
'modules/audio_coding/codecs/isac/main/source/filterbank_tables.h',
'modules/audio_coding/codecs/isac/main/source/intialize.c',
'modules/audio_coding/codecs/isac/main/source/lpc_analysis.c',
'modules/audio_coding/codecs/isac/main/source/lpc_analysis.h',
'modules/audio_coding/codecs/isac/main/source/lpc_gain_swb_tables.c',
'modules/audio_coding/codecs/isac/main/source/lpc_gain_swb_tables.h',
'modules/audio_coding/codecs/isac/main/source/lpc_shape_swb12_tables.c',
'modules/audio_coding/codecs/isac/main/source/lpc_shape_swb12_tables.h',
'modules/audio_coding/codecs/isac/main/source/lpc_shape_swb16_tables.c',
'modules/audio_coding/codecs/isac/main/source/lpc_shape_swb16_tables.h',
'modules/audio_coding/codecs/isac/main/source/lpc_tables.c',
'modules/audio_coding/codecs/isac/main/source/lpc_tables.h',
'modules/audio_coding/codecs/isac/main/source/os_specific_inline.h',
'modules/audio_coding/codecs/isac/main/source/pitch_estimator.c',
'modules/audio_coding/codecs/isac/main/source/pitch_estimator.h',
'modules/audio_coding/codecs/isac/main/source/pitch_filter.c',
'modules/audio_coding/codecs/isac/main/source/pitch_gain_tables.c',
'modules/audio_coding/codecs/isac/main/source/pitch_gain_tables.h',
'modules/audio_coding/codecs/isac/main/source/pitch_lag_tables.c',
'modules/audio_coding/codecs/isac/main/source/pitch_lag_tables.h',
'modules/audio_coding/codecs/isac/main/source/settings.h',
'modules/audio_coding/codecs/isac/main/source/spectrum_ar_model_tables.c',
'modules/audio_coding/codecs/isac/main/source/spectrum_ar_model_tables.h',
'modules/audio_coding/codecs/isac/main/source/structs.h',
'modules/audio_coding/codecs/isac/bandwidth_info.h',

'modules/audio_processing/include/audio_processing.h',
'modules/audio_processing/aec/include/echo_cancellation.h',
'modules/audio_processing/aec/aec_common.h',
'modules/audio_processing/aec/aec_core.c',
'modules/audio_processing/aec/aec_core.h',
'modules/audio_processing/aec/aec_core_internal.h',
'modules/audio_processing/aec/aec_rdft.c',
'modules/audio_processing/aec/aec_rdft.h',
'modules/audio_processing/aec/aec_resampler.c',
'modules/audio_processing/aec/aec_resampler.h',
'modules/audio_processing/aec/echo_cancellation.c',
'modules/audio_processing/aec/echo_cancellation_internal.h',
'modules/audio_processing/aecm/include/echo_control_mobile.h',
'modules/audio_processing/aecm/echo_control_mobile.c',
'modules/audio_processing/aecm/aecm_core.c',
'modules/audio_processing/aecm/aecm_core.h',
'modules/audio_processing/aecm/aecm_core_c.c',
'modules/audio_processing/aecm/aecm_defines.h',
'modules/audio_processing/agc/legacy/analog_agc.c',
'modules/audio_processing/agc/legacy/analog_agc.h',
'modules/audio_processing/agc/legacy/gain_control.h',
'modules/audio_processing/agc/legacy/digital_agc.c',
'modules/audio_processing/agc/legacy/digital_agc.h',
'modules/audio_processing/agc/agc.cc',
'modules/audio_processing/agc/agc.h',
'modules/audio_processing/agc/agc_manager_direct.cc',
'modules/audio_processing/agc/agc_manager_direct.h',
'modules/audio_processing/agc/gain_map_internal.h',
'modules/audio_processing/agc/histogram.cc',
'modules/audio_processing/agc/histogram.h',
'modules/audio_processing/agc/utility.cc',
'modules/audio_processing/agc/utility.h',
'modules/audio_processing/beamformer/array_util.h',
'modules/audio_processing/beamformer/beamformer.h',
'modules/audio_processing/beamformer/complex_matrix.h',
'modules/audio_processing/beamformer/covariance_matrix_generator.h',
'modules/audio_processing/beamformer/matrix.h',
'modules/audio_processing/beamformer/matrix_test_helpers.h',
'modules/audio_processing/beamformer/nonlinear_beamformer.h',
'modules/audio_processing/beamformer/array_util.cc',
'modules/audio_processing/beamformer/covariance_matrix_generator.cc',
'modules/audio_processing/beamformer/nonlinear_beamformer.cc',
'modules/audio_processing/intelligibility/intelligibility_enhancer.h',
'modules/audio_processing/intelligibility/intelligibility_utils.h',
'modules/audio_processing/intelligibility/intelligibility_enhancer.cc',
'modules/audio_processing/intelligibility/intelligibility_utils.cc',
'modules/audio_processing/logging/aec_logging.h',
'modules/audio_processing/logging/aec_logging_file_handling.h',
'modules/audio_processing/logging/aec_logging_file_handling.cc',
'modules/audio_processing/transient/common.h',
'modules/audio_processing/transient/daubechies_8_wavelet_coeffs.h',
'modules/audio_processing/transient/dyadic_decimator.h',
'modules/audio_processing/transient/file_utils.h',
'modules/audio_processing/transient/moving_moments.h',
'modules/audio_processing/transient/transient_detector.h',
'modules/audio_processing/transient/transient_suppressor.h',
'modules/audio_processing/transient/wpd_node.h',
'modules/audio_processing/transient/wpd_tree.h',
'modules/audio_processing/transient/click_annotate.cc',
'modules/audio_processing/transient/file_utils.cc',
'modules/audio_processing/transient/moving_moments.cc',
'modules/audio_processing/transient/transient_detector.cc',
'modules/audio_processing/transient/transient_suppressor.cc',
'modules/audio_processing/transient/wpd_node.cc',
'modules/audio_processing/transient/wpd_tree.cc',
'modules/audio_processing/utility/delay_estimator.c',
'modules/audio_processing/utility/delay_estimator.h',
'modules/audio_processing/utility/delay_estimator_internal.h',
'modules/audio_processing/utility/delay_estimator_wrapper.c',
'modules/audio_processing/utility/delay_estimator_wrapper.h',
'modules/audio_processing/vad/common.h',
'modules/audio_processing/vad/gmm.h',
'modules/audio_processing/vad/noise_gmm_tables.h',
'modules/audio_processing/vad/pitch_based_vad.h',
'modules/audio_processing/vad/pitch_internal.h',
'modules/audio_processing/vad/pole_zero_filter.h',
'modules/audio_processing/vad/standalone_vad.h',
'modules/audio_processing/vad/vad_audio_proc.h',
'modules/audio_processing/vad/vad_audio_proc_internal.h',
'modules/audio_processing/vad/vad_circular_buffer.h',
'modules/audio_processing/vad/voice_activity_detector.h',
'modules/audio_processing/vad/voice_gmm_tables.h',
'modules/audio_processing/vad/gmm.cc',
'modules/audio_processing/vad/pitch_based_vad.cc',
'modules/audio_processing/vad/pitch_internal.cc',
'modules/audio_processing/vad/pole_zero_filter.cc',
'modules/audio_processing/vad/standalone_vad.cc',
'modules/audio_processing/vad/vad_audio_proc.cc',
'modules/audio_processing/vad/vad_circular_buffer.cc',
'modules/audio_processing/vad/voice_activity_detector.cc',
'modules/audio_processing/audio_buffer.cc',
'modules/audio_processing/audio_buffer.h',
'modules/audio_processing/audio_processing_impl.cc',
'modules/audio_processing/audio_processing_impl.h',
'modules/audio_processing/common.h',
'modules/audio_processing/echo_cancellation_impl.cc',
'modules/audio_processing/echo_cancellation_impl.h',
'modules/audio_processing/echo_control_mobile_impl.cc',
'modules/audio_processing/echo_control_mobile_impl.h',
'modules/audio_processing/gain_control_impl.cc',
'modules/audio_processing/gain_control_impl.h',
'modules/audio_processing/high_pass_filter_impl.cc',
'modules/audio_processing/high_pass_filter_impl.h',
'modules/audio_processing/level_estimator_impl.cc',
'modules/audio_processing/level_estimator_impl.h',
'modules/audio_processing/noise_suppression_impl.cc',
'modules/audio_processing/noise_suppression_impl.h',
'modules/audio_processing/rms_level.cc',
'modules/audio_processing/rms_level.h',
'modules/audio_processing/splitting_filter.cc',
'modules/audio_processing/splitting_filter.h',
'modules/audio_processing/processing_component.cc',
'modules/audio_processing/processing_component.h',
'modules/audio_processing/three_band_filter_bank.cc',
'modules/audio_processing/three_band_filter_bank.h',
'modules/audio_processing/typing_detection.cc',
'modules/audio_processing/typing_detection.h',
'modules/audio_processing/voice_detection_impl.cc',
'modules/audio_processing/voice_detection_impl.h',
					
'modules/audio_processing/ns/include/noise_suppression_x.h',
'modules/audio_processing/ns/noise_suppression_x.c',
'modules/audio_processing/ns/nsx_defines.h',
'modules/audio_processing/ns/nsx_core.c',
'modules/audio_processing/ns/nsx_core.h',
'modules/audio_processing/ns/nsx_core_c.c',
					
'modules/audio_processing/aec/aec_core_sse2.c',
'modules/audio_processing/aec/aec_rdft_sse2.c',

'system_wrappers/include/aligned_malloc.h',
'system_wrappers/include/cpu_features_wrapper.h',
'system_wrappers/include/critical_section_wrapper.h',
'system_wrappers/include/file_wrapper.h',
'system_wrappers/include/logging.h',
'system_wrappers/include/metrics.h',
'system_wrappers/include/rw_lock_wrapper.h',
'system_wrappers/include/sleep.h',
'system_wrappers/include/thread_wrapper.h',
'system_wrappers/include/trace.h',
'system_wrappers/source/aligned_malloc.cc',
'system_wrappers/source/cpu_features.cc',
'system_wrappers/source/event.cc',
'system_wrappers/source/event_timer_posix.h',
'system_wrappers/source/event_timer_win.h',
'system_wrappers/source/file_impl.cc',
'system_wrappers/source/file_impl.h',
'system_wrappers/source/critical_section.cc',
'system_wrappers/source/critical_section_posix.h',
'system_wrappers/source/critical_section_win.h',
'system_wrappers/source/logging.cc',
'system_wrappers/source/metrics_default.cc',
'system_wrappers/source/rw_lock.cc',
'system_wrappers/source/rw_lock_generic.h',
'system_wrappers/source/rw_lock_posix.h',
'system_wrappers/source/rw_lock_win.h',
'system_wrappers/source/sleep.cc',
'system_wrappers/source/thread.cc',
'system_wrappers/source/thread_posix.h',
'system_wrappers/source/thread_win.h',
'system_wrappers/source/trace_impl.cc',
'system_wrappers/source/trace_impl.h',
'system_wrappers/source/trace_posix.h',
'system_wrappers/source/trace_win.h',
'system_wrappers/source/critical_section_win.cc',
'system_wrappers/source/event_timer_win.cc',
'system_wrappers/source/rw_lock_win.cc',
'system_wrappers/source/thread_win.cc',
'system_wrappers/source/trace_win.cc',
		}
	end
}