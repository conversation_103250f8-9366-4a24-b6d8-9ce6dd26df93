# Copyright (C) 2022 <PERSON>
# Distributed under the Boost Software License, Version 1.0
# https://www.boost.org/LICENSE_1_0.txt

using doxygen ;
import doxygen ;
import path ;
import os ;

path-constant here : . ;

.doxygen = [ doxygen.name ] ;
.doxygen ?= doxygen ;

local sources = [ path.glob-tree $(here)/../include/boost/locale : *.hpp : ]
                [ path.glob $(here) : *.txt ]
                [ path.glob $(here)/style : *.css *.png ]
                templates/footer.html
                ;

make html : $(here)/Doxyfile : @make-html : <dependency>$(sources) ;

if [ os.name ] = NT
{
    RMDIR = rmdir /s /q ;
}
else
{
    RMDIR = rm -rf ;
}

actions make-html {
  cd $(here)
  $(RMDIR) html
  "$(.doxygen)"
}

alias reference : html ;

alias boostdoc ;
explicit boostdoc ;
alias boostrelease : html ;
explicit boostrelease ;
