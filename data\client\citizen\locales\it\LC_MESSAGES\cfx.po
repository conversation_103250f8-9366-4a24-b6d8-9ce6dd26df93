# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <PERSON> <<EMAIL>>, 2020.
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-12-30 22:37+0100\n"
"PO-Revision-Date: 2020-10-16 08:55+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Italian <http://translations.cfx.re/projects/citizenfx/client/"
"it/>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 4.1.1\n"

#: client/launcher/MiniDump.cpp:1264
#, c-format
msgid ""
"\n"
"\n"
"Exception details: %s"
msgstr ""
"\n"
"\n"
"Dettagli eccezione: %s"

#: client/launcher/MiniDump.cpp:1269
#, c-format
msgid ""
"\n"
"\n"
"Legacy crash hash: %s"
msgstr ""
"\n"
"\n"
"Hash legacy del crash: %s"

#: client/launcher/MiniDump.cpp:1274
#, c-format
msgid ""
"\n"
"Stack trace:\n"
"%s"
msgstr ""

#: components/net/src/NetLibrary.cpp:637
msgid ""
"# Couldn't connect\n"
"Failed to get info from server (tried 3 times).\n"
"\n"
"---\n"
"\n"
"If you are the server owner, are you sure you are allowing UDP packets to "
"and from the server?"
msgstr ""

#: components/net/src/NetLibrary.cpp:674
#, c-format
msgid ""
"# Timed out\n"
"Client -> server connection timed out. Please try again later.\n"
"\n"
"---\n"
"\n"
"%s\n"
"[Reconnect](cfx.re://reconnect)"
msgstr ""

#: client/launcher/MiniDump.cpp:1260
#, c-format
msgid ""
"%s caused %s to stop working. A crash report is being uploaded to the %s "
"developers."
msgstr ""
"%s ha causato l'arresto di %s. È in corso l'invio di un rapporto sugli "
"arresti anomali allo sviluppatore %s."

#: client/launcher/Main.cpp:652
#, c-format
msgid ""
"%s could not create a file in the folder it is placed in. Please move your "
"installation out of Program Files or another protected folder."
msgstr ""
"%s non ha potuto creare un file nella cartella in cui è posizionato. "
"Spostare l'installazione fuori dalla cartella Program Files o qualsiasi "
"cartella protetta."

#: client/launcher/ViabilityChecks.cpp:100
#, fuzzy, c-format
#| msgid ""
#| "This product requires Security Update for Windows 7 for x64-based systems "
#| "(*********) to be installed to run. Please install it, and try again."
msgid ""
"%s requires the Windows Media Feature Pack for Windows N editions to be "
"installed to run. Please install it, and try again."
msgstr ""
"Questo prodotto richiede l'installazione dell'aggiornamento della protezione "
"per Windows 7 per sistemi a 64 bit (*********) per funzionare. Si prega di "
"installarlo e riprovare."

#: client/launcher/MiniDump.cpp:1417
#, fuzzy, c-format
#| msgid ""
#| "Crash signature: %s\n"
#| "Report ID: %s\n"
#| "You can press Ctrl-C to copy this message and paste it elsewhere."
msgid ""
"%sReport ID: %s\n"
"You can press Ctrl-C to copy this message and paste it elsewhere."
msgstr ""
"Firma di arresto anomalo: %s\n"
"Report ID: %s\n"
"Puoi premere Ctrl-C per copiare questo messaggio e incollarlo altrove."

#: client/launcher/MiniDump.cpp:1356
#, fuzzy, c-format
#| msgid ""
#| "Crash signature: %s\n"
#| "Report ID: ... [uploading]\n"
#| "You can press Ctrl-C to copy this message and paste it elsewhere."
msgid ""
"%sReport ID: ... [uploading]\n"
"You can press Ctrl-C to copy this message and paste it elsewhere."
msgstr ""
"Firma di arresto anomalo: %s\n"
"Report ID: ... [upload in corso]\n"
"Puoi premere Ctrl-C per copiare questo messaggio e incollarlo altrove."

#: client/launcher/MiniDump.cpp:1421
#, fuzzy, c-format
#| msgid ""
#| "Crash signature: %s\n"
#| "%s\n"
#| "You can press Ctrl-C to copy this message and paste it elsewhere."
msgid "%sYou can press Ctrl-C to copy this message and paste it elsewhere."
msgstr ""
"Firma di arresto anomalo: %s\n"
"%s\n"
"Puoi premere Ctrl-C per copiare questo messaggio e incollarlo altrove."

#: components/net/src/NetLibrary.cpp:99
#, c-format
msgid "**Timeout info**: game=%s, recv=%s, send=%s\n"
msgstr ""

#: components/citizen-devtools/src/ResourceTimeWarnings.cpp:426
msgid ""
"A call into the Windows API took too long recently and led to a game stutter."
msgstr ""

#: client/launcher/MiniDump.cpp:1235
#, c-format
msgid "An error at %s"
msgstr "Errore %s"

#: client/launcher/Installer.cpp:127
#, c-format
msgid "Are you sure you want to remove %s from the installation root at %s?"
msgstr "Sei sicuro di voler rimuovere %s dalla root di installazione %s?"

#: client/launcher/Bootstrap.cpp:34
#, c-format
msgid "Bootstrapping %s..."
msgstr "Avvio di %s..."

#: client/launcher/Updater.cpp:624
#, c-format
msgid "Checking %s"
msgstr "Controllando %s"

#: client/launcher/MiniDump.cpp:1349
#, c-format
msgid "Crash signature: %s\n"
msgstr ""

#: client/launcher/GameCache.cpp:794
#, fuzzy, c-format
#| msgid ""
#| "DLC files are missing (or corrupted) in your game installation. Please "
#| "update or verify the game using Steam or the Social Club launcher and try "
#| "again. See http://rsg.ms/verify step 4 for more info."
msgid ""
"DLC files are missing (or corrupted) in your game installation. Please "
"update or verify the game using Steam, Epic Games Launcher or Rockstar Games "
"Launcher and try again. See http://rsg.ms/verify step 4 for more info.\n"
"Currently, the game installation in '%s' is being used.\n"
"Relevant files: \n"
"%s"
msgstr ""
"I file DLC mancano (o sono danneggiati) nell'installazione del gioco. "
"Aggiorna o verifica il gioco utilizzando Steam o Social Club e riprova. "
"Vedere il passaggio 4 http://rsg.ms/verify per ulteriori informazioni."

#: client/launcher/ViabilityChecks.cpp:57
#, c-format
msgid "DXGI 1.2 support is required to run this product %s"
msgstr "Il supporto DXGI 1.2 è necessario per eseguire questo prodotto %s"

#: client/launcher/Download.cpp:273
#, c-format
msgid "Downloaded %.2f/%.2f MB (%.0f%%, %.1f MB/s)"
msgstr "Scaricato %.2f/%.2f MB (%.0f%%, %.1f MB/s)"

#: client/launcher/MiniDump.cpp:1231
#, c-format
msgid "Error %s"
msgstr "Errore %s"

#: client/launcher/InstallerExtraction.cpp:534
#, c-format
msgid "Extracting %s"
msgstr "Estraendo %s"

#: client/launcher/InstallerExtraction.cpp:270
#, c-format
msgid "Extracting %s (scanning)"
msgstr "Estraendo %s (analisi)"

#: client/launcher/MiniDump.cpp:1245
msgid "FiveM crashed... but we're on it!"
msgstr "FiveM ha smesso di funzionare ... ma ce ne stiamo occupando!"

#: client/launcher/Main.cpp:633
msgid ""
"FiveM does not support running under elevated privileges. Please change your "
"Windows settings to not run FiveM as administrator.\n"
"The game will exit now."
msgstr ""
"FiveM non supporta l'esecuzione con privilegi elevati. Modifica le "
"impostazioni di Windows per non eseguire FiveM come amministratore.\n"
"Il gioco ora si chiuderà."

#: client/launcher/MiniDump.cpp:1308
msgid "Game crashed: "
msgstr "Il gioco ha smesso di funzionare: "

#: components/citizen-devtools/src/ResourceTimeWarnings.cpp:428
msgid ""
"Please close any software you have running in the background (including "
"Windows apps such as File Explorer or Task Manager)."
msgstr ""

#: client/launcher/ViabilityChecks.cpp:50
msgid "Please install Windows 7 SP1 or greater, and try again."
msgstr "Installa Windows 7 SP1 o superiore e riprova."

#: client/launcher/ViabilityChecks.cpp:54
msgid "Please install the Platform Update for Windows 7, and try again."
msgstr "Installa l'aggiornamento della piattaforma per Windows 7 e riprova."

#: client/launcher/MiniDump.cpp:328 client/launcher/MiniDump.cpp:1447
msgid "Save information"
msgstr ""

#: client/launcher/MiniDump.cpp:1343
msgid ""
"Save information\n"
"Stores a file with crash information that you should copy and upload when "
"asking for help."
msgstr ""
"Salva le informazioni\n"
"Memorizza un file con le informazioni sull'arresto anomalo che devi copiare "
"ed inviare quando richiedi aiuto."

#: client/launcher/GameSelect.cpp:194
msgid "Select the folder containing Grand Theft Auto V"
msgstr "Seleziona la cartella che contiene Grand Theft Auto V"

#: components/ros-patches-five/src/AccountID.cpp:265
msgid "Signing in with Epic"
msgstr ""

#: components/ros-patches-five/src/AccountID.cpp:229
msgid "Signing in with Steam"
msgstr ""

#: components/citizen-devtools/src/ResourceTimeWarnings.cpp:424
msgid "Slow system performance detected"
msgstr ""

#: client/launcher/Download.cpp:511
msgid "Starting IPFS discovery..."
msgstr "Avvio del rilevamento IPFS..."

#: client/launcher/ViabilityChecks.cpp:46
msgid "The game will exit now."
msgstr "Il gioco ora si chiuderà."

#: client/launcher/GameCache.cpp:783
#, fuzzy, c-format
#| msgid ""
#| "The local %s game cache is outdated, and needs to be updated. This will "
#| "copy %.2f MB of data from the local disk, and download %.2f MB of data "
#| "from the internet.\n"
#| "Do you wish to continue?"
msgid ""
"The local %s game data is outdated, and needs to be updated. This will copy "
"%.2f MB of data from the local disk, and download %.2f MB of data from the "
"internet.\n"
"Do you wish to continue?"
msgstr ""
"La cache di gioco locale %s non è aggiornata e deve esserlo. L'aggiornamento "
"copierà %.2f MB di dati dall'unità locale e scaricherà %.2f MB di dati da "
"Internet.\n"
"Vuoi continuare?"

#: client/launcher/GameSelect.cpp:329
#, c-format
msgid "The selected path does not contain a %s file."
msgstr "Il percorso selezionato non contiene il file %s."

#: client/launcher/ViabilityChecks.cpp:78
msgid ""
"This product requires Security Update for Windows 7 for x64-based systems "
"(*********) to be installed to run. Please install it, and try again."
msgstr ""
"Questo prodotto richiede l'installazione dell'aggiornamento della protezione "
"per Windows 7 per sistemi a 64 bit (*********) per funzionare. Si prega di "
"installarlo e riprovare."

#: client/launcher/Main.cpp:809
msgid "Transitioning to another build..."
msgstr "Migrando ad un'altra versione..."

#: client/launcher/MiniDump.cpp:1300
msgid "Unhandled exception: "
msgstr "Eccezione non gestita: "

#: client/launcher/Installer.cpp:125
#, c-format
msgid "Uninstall %s"
msgstr "Disinstalla %s"

#: client/launcher/Installer.cpp:126
#, c-format
msgid "Uninstall %s?"
msgstr "Disinstallare %s?"

#: client/launcher/Updater.cpp:502
#, c-format
msgid "Updating %s..."
msgstr "Aggiornando %s..."

#: client/launcher/GameCache.cpp:1050
#, fuzzy
#| msgid "Updating game cache..."
msgid "Updating game storage..."
msgstr "Aggiornamento della cache del gioco..."

#: client/launcher/Updater.cpp:468
msgid "Verifying content..."
msgstr "Verifica del contenuto..."

#: client/launcher/GameCache.cpp:883
msgid "Verifying game content..."
msgstr "Controllo del contenuto del gioco in corso..."

#: client/launcher/Main.cpp:810
msgid "We're getting there."
msgstr "Ci siamo quasi."

#: client/launcher/Main.cpp:613
#, c-format
msgid ""
"You are currently using an outdated version of Windows. This may lead to "
"issues using the %s client. Please update to Windows 10 version 1703 "
"(\"Creators Update\") or higher in case you are experiencing any issues. The "
"game will continue to start now."
msgstr ""
"Stai attualmente utilizzando una versione obsoleta di Windows. Ciò può "
"causare problemi utilizzando il client %s. Si prega di aggiornare a Windows "
"10 versione 1703 (\\\"Creators Update\\\") o superiore in caso di problemi. "
"Il gioco ora si avvierà."

#~ msgid ""
#~ "\n"
#~ "\n"
#~ "This is a fatal error because game unloading failed. Please report this "
#~ "issue and how to cause it (what server you played on, any resources/"
#~ "scripts, etc.) so this can be solved."
#~ msgstr ""
#~ "\n"
#~ "\n"
#~ "Questo è un errore fatale perché il gioco non è stato liberato "
#~ "correttamente dalle risorse. Si prega di segnalare questo problema e come "
#~ "causarlo (su quale server si è giocato, eventuali risorse / script ecc.) "
#~ "In modo che possa essere risolto."

#, c-format
#~ msgid ""
#~ "A game error (at %016llx) caused %s to stop working. A crash report has "
#~ "been uploaded to the %s developers.\n"
#~ "\n"
#~ "%s"
#~ msgstr ""
#~ "Un errore di gioco (a %016llx) ha causato l'arresto di %s. Un rapporto di "
#~ "errore è stato inviato allo sviluppatore %s.\n"
#~ "\n"
#~ "%s"

#, c-format
#~ msgid "RAGE error: %s"
#~ msgstr "Errore RAGE: %s"
