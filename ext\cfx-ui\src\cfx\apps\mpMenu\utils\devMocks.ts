/**
 * Development mocks for FiveM NUI functions
 * Chỉ được sử dụng trong môi trường development để mock các API của FiveM
 */

export interface MockNativeResponse {
  type: string;
  [key: string]: any;
}

/**
 * Mock data cho development environment
 */
export const MOCK_DATA = {
  // Mock convars với giá trị mặc định
  convars: [
    { key: 'ui_streamerMode', value: 'false' },
    { key: 'ui_quickAccessLocalhostPort', value: '30120' },
    { key: 'ui_updateChannel', value: 'production' },
    { key: 'ui_customBackdrop', value: '' },
    { key: 'ui_preferLightColorScheme', value: 'false' },
    { key: 'ui_preferBlurredBackdrop', value: 'true' },
    { key: 'ui_premium', value: 'false' },
    { key: 'ui_customBrandingEmoji', value: '' },
    { key: 'ui_devMode', value: 'true' },
    { key: 'ui_language', value: 'en-us' }
  ],

  // Mock server data
  mockServer: {
    hostname: 'Development Mock Server',
    players: 42,
    maxPlayers: 64,
    ping: 25,
    tags: ['roleplay', 'development'],
    description: 'This is a mock server for development purposes'
  },

  // Mock user data
  mockUser: {
    nickname: 'DevUser',
    computerName: 'DEV-MACHINE'
  }
};

/**
 * Xử lý các native calls trong development environment
 */
export function handleMockNativeCall(native: string, arg: string): void {
  console.log('[DEV MOCK] Native call:', native, arg);

  switch (native) {
    case 'getMinModeInfo':
      postMockMessage({
        type: 'minModeInfo',
        data: { enabled: false }
      }, 100);
      break;

    case 'checkNickname':
      postMockMessage({
        type: 'nicknameChecked',
        data: { valid: true }
      }, 100);
      break;

    case 'getConvars':
      postMockMessage({
        type: 'convarsSet',
        vars: MOCK_DATA.convars
      }, 100);
      break;

    case 'setConvar':
    case 'setArchivedConvar':
      try {
        const payload = JSON.parse(arg);
        // Cập nhật mock data
        const existingConvar = MOCK_DATA.convars.find(c => c.key === payload.name);
        if (existingConvar) {
          existingConvar.value = payload.value;
        } else {
          MOCK_DATA.convars.push({ key: payload.name, value: payload.value });
        }
        
        postMockMessage({
          type: 'convarSet',
          name: payload.name,
          value: payload.value
        }, 50);
      } catch (e) {
        console.warn('[DEV MOCK] Failed to parse convar payload:', arg);
      }
      break;

    case 'queryServer':
      postMockMessage({
        type: 'serverQueried',
        queryCorrelation: arg,
        data: MOCK_DATA.mockServer
      }, 500);
      break;

    case 'openFileDialog':
      // Mock file dialog response
      postMockMessage({
        type: 'fileDialogResult',
        dialogKey: arg,
        result: 'C:\\mock\\file\\path.txt'
      }, 1000);
      break;

    case 'backfillEnable':
    case 'loadWarning':
    case 'executeCommand':
    case 'exit':
    case 'openUrl':
      // Các native này không cần response, chỉ log
      console.log('[DEV MOCK] Action native called:', native, arg);
      break;

    default:
      console.log('[DEV MOCK] Unhandled native call:', native, arg);
  }
}

/**
 * Gửi mock message với delay
 */
function postMockMessage(message: MockNativeResponse, delay: number = 0): void {
  setTimeout(() => {
    window.postMessage(message, '*');
    console.log('[DEV MOCK] Posted message:', message);
  }, delay);
}

/**
 * Khởi tạo mock environment cho development
 */
export function initializeMockEnvironment(): void {
  if (!__CFXUI_DEV__) {
    return;
  }

  console.log('[DEV MOCK] Initializing mock environment for FiveM UI development');

  // Mock một số dữ liệu ban đầu
  setTimeout(() => {
    postMockMessage({
      type: 'setComputerName',
      data: MOCK_DATA.mockUser.computerName
    }, 200);
  });

  // Thêm mock data vào window object để có thể truy cập từ console
  (window as any).__FIVEM_DEV_MOCKS__ = {
    data: MOCK_DATA,
    postMessage: postMockMessage,
    handleNative: handleMockNativeCall
  };

  console.log('[DEV MOCK] Mock environment initialized. Access via window.__FIVEM_DEV_MOCKS__');
}
