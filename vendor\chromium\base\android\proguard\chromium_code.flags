# Copyright 2016 The Chromium Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

# Contains flags that can be safely shared with <PERSON><PERSON><PERSON>, and thus would be
# appropriate for third-party apps to include.

# Keep all annotation related attributes that can affect runtime
-keepattributes RuntimeVisible*Annotations
-keepattributes AnnotationDefault

# Keep the annotations, because if we don't, the ProGuard rules that use them
# will not be respected. These classes then show up in our final dex, which we
# do not want - see crbug.com/628226.
-keep @interface org.chromium.base.annotations.AccessedByNative
-keep @interface org.chromium.base.annotations.CalledByNative
-keep @interface org.chromium.base.annotations.CalledByNativeUnchecked
-keep @interface org.chromium.base.annotations.DoNotInline
-keep @interface org.chromium.base.annotations.RemovableInRelease
-keep @interface org.chromium.base.annotations.UsedByReflection

# Keeps for class level annotations.
-keep @org.chromium.base.annotations.UsedByReflection class ** {}

# Keeps for method level annotations.
-keepclasseswithmembers class ** {
  @org.chromium.base.annotations.AccessedByNative <fields>;
}
-keepclasseswithmembers,includedescriptorclasses class ** {
  @org.chromium.base.annotations.CalledByNative <methods>;
}
-keepclasseswithmembers,includedescriptorclasses class ** {
  @org.chromium.base.annotations.CalledByNativeUnchecked <methods>;
}
-keepclasseswithmembers class ** {
  @org.chromium.base.annotations.UsedByReflection <methods>;
}
-keepclasseswithmembers class ** {
  @org.chromium.base.annotations.UsedByReflection <fields>;
}
# Even unused methods kept due to explicit jni registration:
# https://crbug.com/688465.
-keepclasseswithmembers,includedescriptorclasses class !org.chromium.base.library_loader.**,** {
  native <methods>;
}
-keepclasseswithmembernames,includedescriptorclasses class org.chromium.base.library_loader.** {
  native <methods>;
}

-assumenosideeffects class ** {
  # Remove @RemovableInRelease methods so long as return values are unused.
  @org.chromium.base.annotations.RemovableInRelease <methods>;
  # Remove object @RemovableInRelease methods even when return value is used.
  # Note: * in return type does not match primitives.
  @org.chromium.base.annotations.RemovableInRelease * *(...) return null;
  # Remove boolean @RemovableInRelease methods even when return value is used.
  @org.chromium.base.annotations.RemovableInRelease boolean *(...) return false;
}

# Never inline classes or methods with this annotation, but allow shrinking and
# obfuscation.
-keep,allowobfuscation,allowoptimization @org.chromium.base.annotations.DoNotInline class ** {
  <methods>;
}
-keepclassmembers,allowobfuscation,allowoptimization class ** {
  @org.chromium.base.annotations.DoNotInline <methods>;
}

# Keep all CREATOR fields within Parcelable that are kept.
-keepclassmembers class org.chromium.** implements android.os.Parcelable {
  public static *** CREATOR;
}

# Don't obfuscate Parcelables as they might be marshalled outside Chrome.
# If we annotated all Parcelables that get put into Bundles other than
# for saveInstanceState (e.g. PendingIntents), then we could actually keep the
# names of just those ones. For now, we'll just keep them all.
-keepnames class org.chromium.** implements android.os.Parcelable {}

# Keep all enum values and valueOf methods. See
# http://proguard.sourceforge.net/index.html#manual/examples.html
# for the reason for this. Also, see http://crbug.com/248037.
-keepclassmembers enum org.chromium.** {
    public static **[] values();
}
