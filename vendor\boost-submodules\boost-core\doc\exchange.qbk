[/
Copyright 2018 <PERSON>
(<EMAIL>)

Distributed under the Boost Software License, Version 1.0.
(http://www.boost.org/LICENSE_1_0.txt)
]

[section:exchange exchange]

[simplesect Authors]

* <PERSON>

[endsimplesect]

[section Overview]

The header <boost/core/exchange.hpp> provides the function template
`boost::exchange` which is an implementation of the `std::exchange`
function introduced in C++14. `boost::exchange(o, v)` replaces the
value of `o` with `v` and returns the old value of `o`.

[endsect]

[section Examples]

The following example shows `boost::exchange` used to simplify the
implementation of a move constructor.

```
Node(Node&& other)
  : head_(boost::exchange(other.head_, nullptr))
  , tail_(boost::exchange(other.tail_, nullptr)) { }
```

[endsect]

[section Reference]

```
namespace boost {
  template<class T, class U = T>
  constexpr T exchange(T& t, U&& u);
}
```
[section Functions]

[*`template<class T, class U = T> constexpr T exchange(T& t, U&& u);`]

Equivalent to:

```
T v = std::move(t);
t = std::forward<U>(u);
return v;
```

[endsect]

[endsect]

[endsect]
