<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FiveM UI Standalone Test</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: white;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .status {
            background: #2a2a2a;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        button {
            background: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
        }
        
        button:hover {
            background: #005a9e;
        }
        
        .iframe-container {
            border: 2px solid #444;
            border-radius: 8px;
            overflow: hidden;
            height: 80vh;
        }
        
        iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        
        .logs {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 FiveM UI Standalone Test</h1>
            <p>Test FiveM UI mà không cần game client</p>
        </div>
        
        <div class="status">
            <strong>Status:</strong> <span id="status">Initializing...</span>
        </div>
        
        <div class="controls">
            <button onclick="loadUI()">Load UI</button>
            <button onclick="reloadUI()">Reload</button>
            <button onclick="clearLogs()">Clear Logs</button>
            <button onclick="testNatives()">Test Natives</button>
        </div>
        
        <div class="iframe-container">
            <iframe id="uiFrame" src="about:blank"></iframe>
        </div>
        
        <div class="logs" id="logs">
            <div>Logs sẽ hiển thị ở đây...</div>
        </div>
    </div>

    <script>
        let uiLoaded = false;
        
        function log(message) {
            const logs = document.getElementById('logs');
            const timestamp = new Date().toLocaleTimeString();
            logs.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            logs.scrollTop = logs.scrollHeight;
        }
        
        function updateStatus(status) {
            document.getElementById('status').textContent = status;
            log(`Status: ${status}`);
        }
        
        function loadUI() {
            updateStatus('Loading UI...');
            const iframe = document.getElementById('uiFrame');
            
            // Inject bypass script trước khi load UI
            iframe.onload = function() {
                try {
                    const iframeWindow = iframe.contentWindow;
                    
                    // Inject native bypass
                    const script = iframe.contentDocument.createElement('script');
                    script.textContent = `
                        // Global bypass cho tất cả native functions
                        window.invokeNative = function(native, arg) {
                            console.log('[BYPASS] Native call:', native, arg);
                            parent.postMessage({
                                type: 'nativeCall',
                                native: native,
                                arg: arg
                            }, '*');
                            return Promise.resolve();
                        };
                        
                        // Mock nuiWindow object
                        window.nuiTargetGame = 'gta5';
                        window.nuiTargetGameBuild = 2944;
                        window.nuiTargetGamePureLevel = 0;
                        window.nuiSystemLanguages = ['en-us', 'vi-vn'];
                        
                        window.nuiSetAudioCategory = function(category) {
                            console.log('[BYPASS] Audio category:', category);
                        };
                        
                        // Override console để forward logs
                        const originalLog = console.log;
                        console.log = function(...args) {
                            originalLog.apply(console, args);
                            parent.postMessage({
                                type: 'consoleLog',
                                message: args.join(' ')
                            }, '*');
                        };
                        
                        // Override error handling
                        window.addEventListener('error', function(event) {
                            parent.postMessage({
                                type: 'error',
                                message: event.message,
                                filename: event.filename,
                                lineno: event.lineno
                            }, '*');
                        });
                        
                        console.log('[BYPASS] Native bypass injected successfully');
                    `;
                    iframe.contentDocument.head.appendChild(script);
                    
                    updateStatus('UI Loaded with bypass');
                    uiLoaded = true;
                } catch (e) {
                    log(`Error injecting bypass: ${e.message}`);
                    updateStatus('Error loading UI');
                }
            };
            
            iframe.src = 'http://localhost:4200/';
        }
        
        function reloadUI() {
            if (uiLoaded) {
                updateStatus('Reloading UI...');
                document.getElementById('uiFrame').src = 'http://localhost:4200/';
            } else {
                loadUI();
            }
        }
        
        function clearLogs() {
            document.getElementById('logs').innerHTML = '<div>Logs cleared...</div>';
        }
        
        function testNatives() {
            if (!uiLoaded) {
                log('UI chưa được load. Hãy load UI trước.');
                return;
            }
            
            const iframe = document.getElementById('uiFrame');
            try {
                const iframeWindow = iframe.contentWindow;
                
                // Test các native calls
                log('Testing native calls...');
                iframeWindow.invokeNative('getConvars', '');
                iframeWindow.invokeNative('checkNickname', 'TestUser');
                iframeWindow.invokeNative('getMinModeInfo', '');
                
                log('Native calls sent successfully');
            } catch (e) {
                log(`Error testing natives: ${e.message}`);
            }
        }
        
        // Listen for messages from iframe
        window.addEventListener('message', function(event) {
            if (event.data.type === 'nativeCall') {
                log(`Native Call: ${event.data.native}(${event.data.arg})`);
            } else if (event.data.type === 'consoleLog') {
                log(`Console: ${event.data.message}`);
            } else if (event.data.type === 'error') {
                log(`Error: ${event.data.message} at ${event.data.filename}:${event.data.lineno}`);
            }
        });
        
        // Auto-load UI when page loads
        window.onload = function() {
            updateStatus('Ready to load UI');
            log('Standalone test environment initialized');
            log('Click "Load UI" để bắt đầu test');
        };
    </script>
</body>
</html>
