// Copyright 2017 The Chromium Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     base/android/jni_generator/jni_registration_generator.py
// Please do not change its content.

#ifndef HEADER_GUARD
#define HEADER_GUARD

#include <jni.h>

#include "base/android/jni_generator/jni_generator_helper.h"
#include "base/android/jni_int_wrapper.h"
#include "base/stl_util.h"  // For base::size().


// Step 1: Forward declarations (classes).

extern const char kClassPath_org_chromium_TestJni[];
extern std::atomic<jclass> g_org_chromium_TestJni_clazz;
#ifndef org_chromium_TestJni_clazz_defined
#define org_chromium_TestJni_clazz_defined
inline jclass org_chromium_TestJni_clazz(JNIEnv* env) {
  return base::android::LazyGetClass(env, kClassPath_org_chromium_TestJni,
      &g_org_chromium_TestJni_clazz);
}
#endif


// Step 2: Forward declarations (methods).

JNI_GENERATOR_EXPORT jint Java_org_chromium_TestJni_nativeInit(
    JNIEnv* env,
    jobject jcaller);
JNI_GENERATOR_EXPORT void Java_org_chromium_TestJni_nativeDestroy(
    JNIEnv* env,
    jobject jcaller,
    jint nativeChromeBrowserProvider);
JNI_GENERATOR_EXPORT jlong Java_org_chromium_TestJni_nativeAddBookmark(
    JNIEnv* env,
    jobject jcaller,
    jint nativeChromeBrowserProvider,
    jstring url,
    jstring title,
    jboolean isFolder,
    jlong parentId);
JNI_GENERATOR_EXPORT jstring Java_org_chromium_TestJni_nativeGetDomainAndRegistry(
    JNIEnv* env,
    jclass jcaller,
    jstring url);
JNI_GENERATOR_EXPORT void Java_org_chromium_TestJni_nativeCreateHistoricalTabFromState(
    JNIEnv* env,
    jclass jcaller,
    jbyteArray state,
    jint tab_index);
JNI_GENERATOR_EXPORT jbyteArray Java_org_chromium_TestJni_nativeGetStateAsByteArray(
    JNIEnv* env,
    jobject jcaller,
    jobject view);
JNI_GENERATOR_EXPORT jobjectArray Java_org_chromium_TestJni_nativeGetAutofillProfileGUIDs(
    JNIEnv* env,
    jclass jcaller);
JNI_GENERATOR_EXPORT void Java_org_chromium_TestJni_nativeSetRecognitionResults(
    JNIEnv* env,
    jobject jcaller,
    jint sessionId,
    jobjectArray results);
JNI_GENERATOR_EXPORT jlong Java_org_chromium_TestJni_nativeAddBookmarkFromAPI(
    JNIEnv* env,
    jobject jcaller,
    jint nativeChromeBrowserProvider,
    jstring url,
    jobject created,
    jobject isBookmark,
    jobject date,
    jbyteArray favicon,
    jstring title,
    jobject visits);
JNI_GENERATOR_EXPORT jint Java_org_chromium_TestJni_nativeFindAll(
    JNIEnv* env,
    jobject jcaller,
    jstring find);
JNI_GENERATOR_EXPORT jobject Java_org_chromium_TestJni_nativeGetInnerClass(
    JNIEnv* env,
    jclass jcaller);
JNI_GENERATOR_EXPORT jobject Java_org_chromium_TestJni_nativeQueryBitmap(
    JNIEnv* env,
    jobject jcaller,
    jint nativeChromeBrowserProvider,
    jobjectArray projection,
    jstring selection,
    jobjectArray selectionArgs,
    jstring sortOrder);
JNI_GENERATOR_EXPORT void Java_org_chromium_TestJni_nativeGotOrientation(
    JNIEnv* env,
    jobject jcaller,
    jint nativeDataFetcherImplAndroid,
    jdouble alpha,
    jdouble beta,
    jdouble gamma);
JNI_GENERATOR_EXPORT jthrowable Java_org_chromium_TestJni_nativeMessWithJavaException(
    JNIEnv* env,
    jclass jcaller,
    jthrowable e);


// Step 3: Method declarations.

static const JNINativeMethod kMethods_org_chromium_TestJni[] = {
    { "nativeInit", "()I", reinterpret_cast<void*>(Java_org_chromium_TestJni_nativeInit) },
    { "nativeDestroy", "(I)V", reinterpret_cast<void*>(Java_org_chromium_TestJni_nativeDestroy) },
    { "nativeAddBookmark", "(ILjava/lang/String;Ljava/lang/String;ZJ)J",
        reinterpret_cast<void*>(Java_org_chromium_TestJni_nativeAddBookmark) },
    { "nativeGetDomainAndRegistry", "(Ljava/lang/String;)Ljava/lang/String;",
        reinterpret_cast<void*>(Java_org_chromium_TestJni_nativeGetDomainAndRegistry) },
    { "nativeCreateHistoricalTabFromState", "([BI)V",
        reinterpret_cast<void*>(Java_org_chromium_TestJni_nativeCreateHistoricalTabFromState) },
    { "nativeGetStateAsByteArray", "(Landroid/view/View;)[B",
        reinterpret_cast<void*>(Java_org_chromium_TestJni_nativeGetStateAsByteArray) },
    { "nativeGetAutofillProfileGUIDs", "()[Ljava/lang/String;",
        reinterpret_cast<void*>(Java_org_chromium_TestJni_nativeGetAutofillProfileGUIDs) },
    { "nativeSetRecognitionResults", "(I[Ljava/lang/String;)V",
        reinterpret_cast<void*>(Java_org_chromium_TestJni_nativeSetRecognitionResults) },
    { "nativeAddBookmarkFromAPI",
        "(ILjava/lang/String;Ljava/lang/Long;Ljava/lang/Boolean;Ljava/lang/Long;[BLjava/lang/String;Ljava/lang/Integer;)J",
        reinterpret_cast<void*>(Java_org_chromium_TestJni_nativeAddBookmarkFromAPI) },
    { "nativeFindAll", "(Ljava/lang/String;)I",
        reinterpret_cast<void*>(Java_org_chromium_TestJni_nativeFindAll) },
    { "nativeGetInnerClass",
        "()Lorg/chromium/example/jni_generator/SampleForTests$OnFrameAvailableListener;",
        reinterpret_cast<void*>(Java_org_chromium_TestJni_nativeGetInnerClass) },
    { "nativeQueryBitmap",
        "(I[Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;Ljava/lang/String;)Landroid/graphics/Bitmap;",
        reinterpret_cast<void*>(Java_org_chromium_TestJni_nativeQueryBitmap) },
    { "nativeGotOrientation", "(IDDD)V",
        reinterpret_cast<void*>(Java_org_chromium_TestJni_nativeGotOrientation) },
    { "nativeMessWithJavaException", "(Ljava/lang/Throwable;)Ljava/lang/Throwable;",
        reinterpret_cast<void*>(Java_org_chromium_TestJni_nativeMessWithJavaException) },
};


JNI_REGISTRATION_EXPORT bool RegisterNative_org_chromium_TestJni(JNIEnv* env) {
  const int kMethods_org_chromium_TestJniSize =
      base::size(kMethods_org_chromium_TestJni);
  if (env->RegisterNatives(
      org_chromium_TestJni_clazz(env),
      kMethods_org_chromium_TestJni,
      kMethods_org_chromium_TestJniSize) < 0) {
    jni_generator::HandleRegistrationError(env,
        org_chromium_TestJni_clazz(env),
        __FILE__);
    return false;
  }

  return true;
}


// Step 4: Main dex and non-main dex registration functions.

namespace test {

bool RegisterMainDexNatives(JNIEnv* env) {
  if (!RegisterNative_org_chromium_TestJni(env))
    return false;

  return true;
}

bool RegisterNonMainDexNatives(JNIEnv* env) {

  return true;
}

}  // namespace test

#endif  // HEADER_GUARD
