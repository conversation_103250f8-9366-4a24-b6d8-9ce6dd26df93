// Copyright 2014 The Chromium Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     base/android/jni_generator/jni_generator.py
// For
//     org/chromium/TestJni

#ifndef org_chromium_TestJni_JNI
#define org_chromium_TestJni_JNI

#include <jni.h>

#include "base/android/jni_generator/jni_generator_helper.h"


// Step 1: Forward declarations.

JNI_REGISTRATION_EXPORT extern const char kClassPath_org_chromium_TestJni[];
const char kClassPath_org_chromium_TestJni[] = "org/chromium/TestJni";
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_REGISTRATION_EXPORT std::atomic<jclass> g_org_chromium_TestJni_clazz(nullptr);
#ifndef org_chromium_TestJni_clazz_defined
#define org_chromium_TestJni_clazz_defined
inline jclass org_chromium_TestJni_clazz(JNIEnv* env) {
  return base::android::LazyGetClass(env, kClassPath_org_chromium_TestJni,
      &g_org_chromium_TestJni_clazz);
}
#endif


// Step 2: Constants (optional).


// Step 3: Method stubs.
static jint JNI_TestJni_Init(JNIEnv* env, const base::android::JavaParamRef<jobject>& jcaller);

JNI_GENERATOR_EXPORT jint Java_org_chromium_TestJni_nativeInit(
    JNIEnv* env,
    jobject jcaller) {
  return JNI_TestJni_Init(env, base::android::JavaParamRef<jobject>(env, jcaller));
}

JNI_GENERATOR_EXPORT void Java_org_chromium_TestJni_nativeDestroy(
    JNIEnv* env,
    jobject jcaller,
    jint nativeChromeBrowserProvider) {
  ChromeBrowserProvider* native =
      reinterpret_cast<ChromeBrowserProvider*>(nativeChromeBrowserProvider);
  CHECK_NATIVE_PTR(env, jcaller, native, "Destroy");
  return native->Destroy(env, base::android::JavaParamRef<jobject>(env, jcaller));
}

JNI_GENERATOR_EXPORT jlong Java_org_chromium_TestJni_nativeAddBookmark(
    JNIEnv* env,
    jobject jcaller,
    jint nativeChromeBrowserProvider,
    jstring url,
    jstring title,
    jboolean isFolder,
    jlong parentId) {
  ChromeBrowserProvider* native =
      reinterpret_cast<ChromeBrowserProvider*>(nativeChromeBrowserProvider);
  CHECK_NATIVE_PTR(env, jcaller, native, "AddBookmark", 0);
  return native->AddBookmark(env, base::android::JavaParamRef<jobject>(env, jcaller),
      base::android::JavaParamRef<jstring>(env, url), base::android::JavaParamRef<jstring>(env,
      title), isFolder, parentId);
}

static base::android::ScopedJavaLocalRef<jstring> JNI_TestJni_GetDomainAndRegistry(JNIEnv* env,
    const base::android::JavaParamRef<jstring>& url);

JNI_GENERATOR_EXPORT jstring Java_org_chromium_TestJni_nativeGetDomainAndRegistry(
    JNIEnv* env,
    jclass jcaller,
    jstring url) {
  return JNI_TestJni_GetDomainAndRegistry(env, base::android::JavaParamRef<jstring>(env,
      url)).Release();
}

static void JNI_TestJni_CreateHistoricalTabFromState(JNIEnv* env, const
    base::android::JavaParamRef<jbyteArray>& state,
    jint tab_index);

JNI_GENERATOR_EXPORT void Java_org_chromium_TestJni_nativeCreateHistoricalTabFromState(
    JNIEnv* env,
    jclass jcaller,
    jbyteArray state,
    jint tab_index) {
  return JNI_TestJni_CreateHistoricalTabFromState(env, base::android::JavaParamRef<jbyteArray>(env,
      state), tab_index);
}

static base::android::ScopedJavaLocalRef<jbyteArray> JNI_TestJni_GetStateAsByteArray(JNIEnv* env,
    const base::android::JavaParamRef<jobject>& jcaller,
    const base::android::JavaParamRef<jobject>& view);

JNI_GENERATOR_EXPORT jbyteArray Java_org_chromium_TestJni_nativeGetStateAsByteArray(
    JNIEnv* env,
    jobject jcaller,
    jobject view) {
  return JNI_TestJni_GetStateAsByteArray(env, base::android::JavaParamRef<jobject>(env, jcaller),
      base::android::JavaParamRef<jobject>(env, view)).Release();
}

static base::android::ScopedJavaLocalRef<jobjectArray> JNI_TestJni_GetAutofillProfileGUIDs(JNIEnv*
    env);

JNI_GENERATOR_EXPORT jobjectArray Java_org_chromium_TestJni_nativeGetAutofillProfileGUIDs(
    JNIEnv* env,
    jclass jcaller) {
  return JNI_TestJni_GetAutofillProfileGUIDs(env).Release();
}

static void JNI_TestJni_SetRecognitionResults(JNIEnv* env, const
    base::android::JavaParamRef<jobject>& jcaller,
    jint sessionId,
    const base::android::JavaParamRef<jobjectArray>& results);

JNI_GENERATOR_EXPORT void Java_org_chromium_TestJni_nativeSetRecognitionResults(
    JNIEnv* env,
    jobject jcaller,
    jint sessionId,
    jobjectArray results) {
  return JNI_TestJni_SetRecognitionResults(env, base::android::JavaParamRef<jobject>(env, jcaller),
      sessionId, base::android::JavaParamRef<jobjectArray>(env, results));
}

JNI_GENERATOR_EXPORT jlong Java_org_chromium_TestJni_nativeAddBookmarkFromAPI(
    JNIEnv* env,
    jobject jcaller,
    jint nativeChromeBrowserProvider,
    jstring url,
    jobject created,
    jobject isBookmark,
    jobject date,
    jbyteArray favicon,
    jstring title,
    jobject visits) {
  ChromeBrowserProvider* native =
      reinterpret_cast<ChromeBrowserProvider*>(nativeChromeBrowserProvider);
  CHECK_NATIVE_PTR(env, jcaller, native, "AddBookmarkFromAPI", 0);
  return native->AddBookmarkFromAPI(env, base::android::JavaParamRef<jobject>(env, jcaller),
      base::android::JavaParamRef<jstring>(env, url), base::android::JavaParamRef<jobject>(env,
      created), base::android::JavaParamRef<jobject>(env, isBookmark),
      base::android::JavaParamRef<jobject>(env, date), base::android::JavaParamRef<jbyteArray>(env,
      favicon), base::android::JavaParamRef<jstring>(env, title),
      base::android::JavaParamRef<jobject>(env, visits));
}

static jint JNI_TestJni_FindAll(JNIEnv* env, const base::android::JavaParamRef<jobject>& jcaller,
    const base::android::JavaParamRef<jstring>& find);

JNI_GENERATOR_EXPORT jint Java_org_chromium_TestJni_nativeFindAll(
    JNIEnv* env,
    jobject jcaller,
    jstring find) {
  return JNI_TestJni_FindAll(env, base::android::JavaParamRef<jobject>(env, jcaller),
      base::android::JavaParamRef<jstring>(env, find));
}

static base::android::ScopedJavaLocalRef<jobject> JNI_TestJni_GetInnerClass(JNIEnv* env);

JNI_GENERATOR_EXPORT jobject Java_org_chromium_TestJni_nativeGetInnerClass(
    JNIEnv* env,
    jclass jcaller) {
  return JNI_TestJni_GetInnerClass(env).Release();
}

JNI_GENERATOR_EXPORT jobject Java_org_chromium_TestJni_nativeQueryBitmap(
    JNIEnv* env,
    jobject jcaller,
    jint nativeChromeBrowserProvider,
    jobjectArray projection,
    jstring selection,
    jobjectArray selectionArgs,
    jstring sortOrder) {
  ChromeBrowserProvider* native =
      reinterpret_cast<ChromeBrowserProvider*>(nativeChromeBrowserProvider);
  CHECK_NATIVE_PTR(env, jcaller, native, "QueryBitmap", NULL);
  return native->QueryBitmap(env, base::android::JavaParamRef<jobject>(env, jcaller),
      base::android::JavaParamRef<jobjectArray>(env, projection),
      base::android::JavaParamRef<jstring>(env, selection),
      base::android::JavaParamRef<jobjectArray>(env, selectionArgs),
      base::android::JavaParamRef<jstring>(env, sortOrder)).Release();
}

JNI_GENERATOR_EXPORT void Java_org_chromium_TestJni_nativeGotOrientation(
    JNIEnv* env,
    jobject jcaller,
    jint nativeDataFetcherImplAndroid,
    jdouble alpha,
    jdouble beta,
    jdouble gamma) {
  DataFetcherImplAndroid* native =
      reinterpret_cast<DataFetcherImplAndroid*>(nativeDataFetcherImplAndroid);
  CHECK_NATIVE_PTR(env, jcaller, native, "GotOrientation");
  return native->GotOrientation(env, base::android::JavaParamRef<jobject>(env, jcaller), alpha,
      beta, gamma);
}

static base::android::ScopedJavaLocalRef<jthrowable> JNI_TestJni_MessWithJavaException(JNIEnv* env,
    const base::android::JavaParamRef<jthrowable>& e);

JNI_GENERATOR_EXPORT jthrowable Java_org_chromium_TestJni_nativeMessWithJavaException(
    JNIEnv* env,
    jclass jcaller,
    jthrowable e) {
  return JNI_TestJni_MessWithJavaException(env, base::android::JavaParamRef<jthrowable>(env,
      e)).Release();
}


// Step 4: Generated test functions (optional).


#endif  // org_chromium_TestJni_JNI
