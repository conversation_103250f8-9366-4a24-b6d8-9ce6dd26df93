// Copyright 2014 The Chromium Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     base/android/jni_generator/jni_generator.py
// For
//     java/io/InputStream

#ifndef java_io_InputStream_JNI
#define java_io_InputStream_JNI

#include <jni.h>

#include "base/android/jni_generator/jni_generator_helper.h"


// Step 1: Forward declarations.

JNI_REGISTRATION_EXPORT extern const char kClassPath_java_io_InputStream[];
const char kClassPath_java_io_InputStream[] = "java/io/InputStream";
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_REGISTRATION_EXPORT std::atomic<jclass> g_java_io_InputStream_clazz(nullptr);
#ifndef java_io_InputStream_clazz_defined
#define java_io_InputStream_clazz_defined
inline jclass java_io_InputStream_clazz(JNIEnv* env) {
  return base::android::LazyGetClass(env, kClassPath_java_io_InputStream,
      &g_java_io_InputStream_clazz);
}
#endif


// Step 2: Constants (optional).


// Step 3: Method stubs.
namespace JNI_InputStream {


static std::atomic<jmethodID> g_java_io_InputStream_available(nullptr);
static jint Java_InputStream_available(JNIEnv* env, const base::android::JavaRef<jobject>& obj)
    __attribute__ ((unused));
static jint Java_InputStream_available(JNIEnv* env, const base::android::JavaRef<jobject>& obj) {
  jclass clazz = java_io_InputStream_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_io_InputStream_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "available",
          "()I",
          &g_java_io_InputStream_available);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_java_io_InputStream_close(nullptr);
static void Java_InputStream_close(JNIEnv* env, const base::android::JavaRef<jobject>& obj)
    __attribute__ ((unused));
static void Java_InputStream_close(JNIEnv* env, const base::android::JavaRef<jobject>& obj) {
  jclass clazz = java_io_InputStream_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_io_InputStream_clazz(env));

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "close",
          "()V",
          &g_java_io_InputStream_close);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id);
}

static std::atomic<jmethodID> g_java_io_InputStream_mark(nullptr);
static void Java_InputStream_mark(JNIEnv* env, const base::android::JavaRef<jobject>& obj,
    JniIntWrapper p0) __attribute__ ((unused));
static void Java_InputStream_mark(JNIEnv* env, const base::android::JavaRef<jobject>& obj,
    JniIntWrapper p0) {
  jclass clazz = java_io_InputStream_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_io_InputStream_clazz(env));

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "mark",
          "(I)V",
          &g_java_io_InputStream_mark);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id, as_jint(p0));
}

static std::atomic<jmethodID> g_java_io_InputStream_markSupported(nullptr);
static jboolean Java_InputStream_markSupported(JNIEnv* env, const base::android::JavaRef<jobject>&
    obj) __attribute__ ((unused));
static jboolean Java_InputStream_markSupported(JNIEnv* env, const base::android::JavaRef<jobject>&
    obj) {
  jclass clazz = java_io_InputStream_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_io_InputStream_clazz(env), false);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "markSupported",
          "()Z",
          &g_java_io_InputStream_markSupported);

  jboolean ret =
      env->CallBooleanMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_java_io_InputStream_readI(nullptr);
static jint Java_InputStream_readI(JNIEnv* env, const base::android::JavaRef<jobject>& obj)
    __attribute__ ((unused));
static jint Java_InputStream_readI(JNIEnv* env, const base::android::JavaRef<jobject>& obj) {
  jclass clazz = java_io_InputStream_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_io_InputStream_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "read",
          "()I",
          &g_java_io_InputStream_readI);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_java_io_InputStream_readI_AB(nullptr);
static jint Java_InputStream_readI_AB(JNIEnv* env, const base::android::JavaRef<jobject>& obj, const
    base::android::JavaRef<jbyteArray>& p0) __attribute__ ((unused));
static jint Java_InputStream_readI_AB(JNIEnv* env, const base::android::JavaRef<jobject>& obj, const
    base::android::JavaRef<jbyteArray>& p0) {
  jclass clazz = java_io_InputStream_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_io_InputStream_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "read",
          "([B)I",
          &g_java_io_InputStream_readI_AB);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id, p0.obj());
  return ret;
}

static std::atomic<jmethodID> g_java_io_InputStream_readI_AB_I_I(nullptr);
static jint Java_InputStream_readI_AB_I_I(JNIEnv* env, const base::android::JavaRef<jobject>& obj,
    const base::android::JavaRef<jbyteArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2) __attribute__ ((unused));
static jint Java_InputStream_readI_AB_I_I(JNIEnv* env, const base::android::JavaRef<jobject>& obj,
    const base::android::JavaRef<jbyteArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2) {
  jclass clazz = java_io_InputStream_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_io_InputStream_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "read",
          "([BII)I",
          &g_java_io_InputStream_readI_AB_I_I);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id, p0.obj(), as_jint(p1), as_jint(p2));
  return ret;
}

static std::atomic<jmethodID> g_java_io_InputStream_reset(nullptr);
static void Java_InputStream_reset(JNIEnv* env, const base::android::JavaRef<jobject>& obj)
    __attribute__ ((unused));
static void Java_InputStream_reset(JNIEnv* env, const base::android::JavaRef<jobject>& obj) {
  jclass clazz = java_io_InputStream_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_io_InputStream_clazz(env));

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "reset",
          "()V",
          &g_java_io_InputStream_reset);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id);
}

static std::atomic<jmethodID> g_java_io_InputStream_skip(nullptr);
static jlong Java_InputStream_skip(JNIEnv* env, const base::android::JavaRef<jobject>& obj, jlong
    p0) __attribute__ ((unused));
static jlong Java_InputStream_skip(JNIEnv* env, const base::android::JavaRef<jobject>& obj, jlong
    p0) {
  jclass clazz = java_io_InputStream_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_io_InputStream_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "skip",
          "(J)J",
          &g_java_io_InputStream_skip);

  jlong ret =
      env->CallLongMethod(obj.obj(),
          call_context.base.method_id, p0);
  return ret;
}

static std::atomic<jmethodID> g_java_io_InputStream_Constructor(nullptr);
static base::android::ScopedJavaLocalRef<jobject> Java_InputStream_Constructor(JNIEnv* env)
    __attribute__ ((unused));
static base::android::ScopedJavaLocalRef<jobject> Java_InputStream_Constructor(JNIEnv* env) {
  jclass clazz = java_io_InputStream_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_io_InputStream_clazz(env), NULL);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "<init>",
          "()V",
          &g_java_io_InputStream_Constructor);

  jobject ret =
      env->NewObject(clazz,
          call_context.base.method_id);
  return base::android::ScopedJavaLocalRef<jobject>(env, ret);
}

}  // namespace JNI_InputStream

// Step 4: Generated test functions (optional).


#endif  // java_io_InputStream_JNI
