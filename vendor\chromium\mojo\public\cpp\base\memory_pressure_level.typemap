# Copyright 2018 The Chromium Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

mojom = "//mojo/public/mojom/base/memory_pressure_level.mojom"
public_headers = [ "//base/memory/memory_pressure_listener.h" ]
traits_headers =
    [ "//mojo/public/cpp/base/memory_pressure_level_mojom_traits.h" ]
sources = [
  "//mojo/public/cpp/base/memory_pressure_level_mojom_traits.cc",
  "//mojo/public/cpp/base/memory_pressure_level_mojom_traits.h",
]
public_deps = [
  "//base",
]
type_mappings = [ "mojo_base.mojom.MemoryPressureLevel=::base::MemoryPressureListener::MemoryPressureLevel" ]
