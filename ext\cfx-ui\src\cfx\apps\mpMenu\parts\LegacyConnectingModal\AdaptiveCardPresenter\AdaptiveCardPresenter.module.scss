:global(.ac-ctrl-overlay) {
  position: absolute;
  left: 0;
  top: 0;
  z-index: 10000;
}

.root {
  :global(.ac-container) {
    * {
      line-height: inherit;
    }

    p {
      padding: inherit;
      line-height: inherit;
    }

    :global(.ac-selectable) {
      &:hover {
        background-color: ui.color-token('button-hover-background');
      }

      &:active {
        background-color: ui.color-token('button-active-background');
      }
    }

    :global(.expandable) {
      &::after {
        font-family: monospace;
        content: '>';
        display: block;

        font-weight: bold;

        margin-left: ui.offset('small');
      }

      &:global(.expanded) {
        &::after {
          transform: rotate(90deg);
        }
      }
    }

    :global(.ac-textInput),
    :global(.ac-timeInput),
    :global(.ac-dateInput),
    :global(.ac-numberInput) {
      height: ui.control-height('normal');
      padding: ui.offset('normal');

      @include ui.font-weight('normal');
      @include ui.font-family('primary');
      @include ui.font-size('normal');
      color: ui.color-token('input-text');

      @include ui.border-radius('small');
      border: none;

      box-shadow: 0 0 0 2px ui.color-token('input-border') inset;

      @include ui.animated();

      background-color: ui.color-token('input-background');

      &:disabled {
        box-shadow: 0 0 0 2px ui.color-token('input-disabled-border') inset;
        cursor: not-allowed;
        opacity: .5;
      }

      &:not(:disabled) {
        &:hover {
          box-shadow: 0 0 0 2px ui.color-token('input-hover-border') inset;
        }

        &:focus-visible,
        &:focus-visible:hover {
          box-shadow: 0 0 0 2px ui.color-token('input-focus-border') inset;
        }
      }

      &::-webkit-input-placeholder {
        color: ui.color-token('input-placeholder-text');
      }
    }

    :global(.ac-textInput.ac-multiline) {
      min-height: calc(#{ui.control-height('normal')} * 2);
    }

    :global(.ac-choiceSetInput-compact) {
      height: ui.control-height('normal');
      padding: 0 ui.offset('small');

      @include ui.font-weight('normal');
      @include ui.font-family('primary');
      @include ui.font-size('normal');
      color: ui.color-token('select-text');

      @include ui.border-radius('small');
      border: none;

      box-shadow: 0 0 0 2px ui.color-token('select-border') inset;
      background-color: ui.color-token('select-background');

      @include ui.animated();

      &:not(:disabled) {
        &:hover {
          box-shadow: 0 0 0 2px ui.color-token('select-hover-border') inset;
        }

        &:focus-visible {
          box-shadow: 0 0 0 2px ui.color-token('select-focus-border') inset;
        }
      }

      option {
        @include ui.font-size('normal');

        background-color: ui.color-token('select-option-background');
      }
    }

    :global(.ac-pushButton),
    :global(.ac-pushButton-disabled),
    :global(.ac-inlineActionButton) {
      gap: ui.offset('small');

      @include ui.def('height', ui.control-height('normal'));

      height: var(--height);
      padding: 0 ui.offset('normal');

      border: none;
      outline: none;
      user-select: none;

      @include ui.font-family('primary');
      @include ui.font-size('normal');
      font-weight: ui.font-weight('normal');

      line-height: 2;
      letter-spacing: 1px;
      text-decoration: none;

      cursor: pointer;

      border: solid 2px;
      @include ui.border-radius('small');

      color: ui.color-token('button-text');
      border-color: ui.color-token('button-border');
      background-color: ui.color-token('button-background');

      @include ui.animated('background-color');

      
      &:global(.ac-pushButton-disabled) {
        cursor: not-allowed;
        opacity: .5;
      }

      &:not(:global(.ac-pushButton-disabled)) {
        &:hover {
          border-color: ui.color-token('button-hover-border');
          background-color: ui.color-token('button-hover-background');
        }

        &:active {
          border-color: ui.color-token('button-active-border');
          background-color: ui.color-token('button-active-background');
        }

        &:focus-visible:not(:active):not(:hover) {
          box-shadow: 0 0 0 2px ui.color-token('button-focus-outline');
        }

        &:global(.style-positive) {
          &,
          &:hover,
          &:active {
            border-color: ui.color('success');
          }
        }
        &:global(.style-destructive) {
          &,
          &:hover,
          &:active {
            border-color: ui.color('error');
          }
        }
      }
    }

    :global(.ac-media-playButton) {
      color: white;
      background-color: black;

      width: ui.control-height('large');
      height: ui.control-height('large');

      border-radius: ui.control-height('large');

      cursor: pointer;

      transition: transform .2s ease;

      &:hover {
        transform: scale(1.1);
      }
    }
  }
}
