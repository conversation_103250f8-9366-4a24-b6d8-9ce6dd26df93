/**
 * \file
 * Define Win32 API subset defaults.
 * Other subsetters can fork this file, or
 * define symbols ahead of it, or after it (with undef).
 *
 * Note that #if of an undefined symbols is defined as if 0,
 * so that an implicit default here.
 *
 * Copyright 2018 Microsoft
 * Licensed under the MIT license. See LICENSE file in the project root for full license information.
 */

#ifndef HAVE_API_SUPPORT_WIN32_GET_COMPUTER_NAME
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT) || G_HAVE_API_SUPPORT(HAVE_UWP_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_GET_COMPUTER_NAME 1
#else
#define HAVE_API_SUPPORT_WIN32_GET_COMPUTER_NAME 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_GET_DRIVE_TYPE
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT) || G_HAVE_API_SUPPORT(HAVE_UWP_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_GET_DRIVE_TYPE 1
#else
#define HAVE_API_SUPPORT_WIN32_GET_DRIVE_TYPE 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_REPLACE_FILE
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT) || G_HAVE_API_SUPPORT(HAVE_UWP_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_REPLACE_FILE 1
#else
#define HAVE_API_SUPPORT_WIN32_REPLACE_FILE 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_COPY_FILE
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT) || G_HAVE_API_SUPPORT(HAVE_UWP_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_COPY_FILE 1
#else
#define HAVE_API_SUPPORT_WIN32_COPY_FILE 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_COPY_FILE2 // not on Windows7
#if G_HAVE_API_SUPPORT(HAVE_UWP_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_COPY_FILE2 1
#else
#define HAVE_API_SUPPORT_WIN32_COPY_FILE2 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_LOCK_FILE
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT) || G_HAVE_API_SUPPORT(HAVE_UWP_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_LOCK_FILE 1
#else
#define HAVE_API_SUPPORT_WIN32_LOCK_FILE 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_UNLOCK_FILE
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT) || G_HAVE_API_SUPPORT(HAVE_UWP_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_UNLOCK_FILE 1
#else
#define HAVE_API_SUPPORT_WIN32_UNLOCK_FILE 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_MOVE_FILE
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_MOVE_FILE 1
#else
#define HAVE_API_SUPPORT_WIN32_MOVE_FILE 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_MOVE_FILE_EX
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT) || G_HAVE_API_SUPPORT(HAVE_UWP_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_MOVE_FILE_EX 1
#else
#define HAVE_API_SUPPORT_WIN32_MOVE_FILE_EX 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_GET_STD_HANDLE
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_GET_STD_HANDLE 1
#else
#define HAVE_API_SUPPORT_WIN32_GET_STD_HANDLE 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_GET_FILE_SIZE_EX
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT) || G_HAVE_API_SUPPORT(HAVE_UWP_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_GET_FILE_SIZE_EX 1
#else
#define HAVE_API_SUPPORT_WIN32_GET_FILE_SIZE_EX 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_GET_LOGICAL_DRIVE_STRINGS
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_GET_LOGICAL_DRIVE_STRINGS 1
#else
#define HAVE_API_SUPPORT_WIN32_GET_LOGICAL_DRIVE_STRINGS 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_SH_GET_FOLDER_PATH
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_SH_GET_FOLDER_PATH 1
#else
#define HAVE_API_SUPPORT_WIN32_SH_GET_FOLDER_PATH 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_SEND_MESSAGE_TIMEOUT
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_SEND_MESSAGE_TIMEOUT 1
#else
#define HAVE_API_SUPPORT_WIN32_SEND_MESSAGE_TIMEOUT 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_WAIT_FOR_INPUT_IDLE
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_WAIT_FOR_INPUT_IDLE 1
#else
#define HAVE_API_SUPPORT_WIN32_WAIT_FOR_INPUT_IDLE 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_SIGNAL_OBJECT_AND_WAIT
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_SIGNAL_OBJECT_AND_WAIT 1
#else
#define HAVE_API_SUPPORT_WIN32_SIGNAL_OBJECT_AND_WAIT 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_MSG_WAIT_FOR_MULTIPLE_OBJECTS
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_MSG_WAIT_FOR_MULTIPLE_OBJECTS 1
#else
#define HAVE_API_SUPPORT_WIN32_MSG_WAIT_FOR_MULTIPLE_OBJECTS 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_SET_ERROR_MODE
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_SET_ERROR_MODE 1
#else
#define HAVE_API_SUPPORT_WIN32_SET_ERROR_MODE 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_LOAD_LIBRARY
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_LOAD_LIBRARY 1
#else
#define HAVE_API_SUPPORT_WIN32_LOAD_LIBRARY 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_LOAD_PACKAGED_LIBRARY
#if G_HAVE_API_SUPPORT(HAVE_UWP_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_LOAD_PACKAGED_LIBRARY 1
#else
#define HAVE_API_SUPPORT_WIN32_LOAD_PACKAGED_LIBRARY 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_GET_MODULE_HANDLE
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_GET_MODULE_HANDLE 1
#else
#define HAVE_API_SUPPORT_WIN32_GET_MODULE_HANDLE 0
#endif
#endif
