////
Copyright 2022 Peter <PERSON>v
Distributed under the Boost Software License, Version 1.0.
https://www.boost.org/LICENSE_1_0.txt
////

[#recent]
= Recent Changes
:idprefix: recent_

== Boost 1.84.0

* {cpp}03 is no longer supported.

== Boost 1.82.0

* Added an overload of `hash_value` for `std::nullptr_t`.
* Added `is_tuple_like` and an overload of `hash_value` for
  tuple-like types.
* Changed string hashing to use
  https://github.com/pdimov/mulxp_hash[`mulxp1_hash`],
  improving both quality and speed. This changes the hash values
  for string-like types (ranges of `char`, `signed char`,
  `unsigned char`, `std::byte`, `char8_t`).

== Boost 1.81.0

Major update.

* The specializations of `boost::hash` have been removed; it now
  always calls `hash_value`.
* Support for `BOOST_HASH_NO_EXTENSIONS` has been removed. The
  extensions are always enabled.
* All standard containers are now supported. This includes
  `std::forward_list` and the unordered associative containers.
* User-defined containers (types that have `begin()` and `end()`
  member functions that return iterators) are now supported out
  of the box.
* Described structs and classes (those annotated with
  `BOOST_DESCRIBE_STRUCT` or `BOOST_DESCRIBE_CLASS`) are now
  supported out of the box.
* `hash_combine` has been improved. This changes the hash values
  of composite (container and tuple) types and of scalar types
  bigger than `size_t`.
* The performance (and quality, as a result of the above change)
  of string hashing has been improved. `boost::hash` for strings
  now passes SMHasher in 64 bit mode.
* The documentation has been substantially revised to reflect
  the changes.

== Boost 1.78.0

* Fixed `hash_combine` so that its behavior no longer depends
  on whether `size_t` is the exact same type as `boost::uint64_t`
  (which wasn't the case on macOS). This changes the hash values
  of composite (container and tuple) types on macOS.
