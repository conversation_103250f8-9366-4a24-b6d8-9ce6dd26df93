@import "variables";

$zGameView: 1;
$zToolbar: 2;

$weToolbarHeight: $q*10;
$weToolbarOffset: $q*15;

$wePanelOffset: $q*2;

$wePanelGap: $q*4;
$wePanelWidth: 15vw;
$wePanelHeight: calc(((100vh - 2*#{$weToolbarOffset}) / 2) - #{$wePanelGap*0.5});

@mixin overlayPanel {
  backdrop-filter: blur(20px);

  background-color: rgba($bgColor, .95);
  box-shadow: 0 0 10px 0 rgba($bgColor, .5);
  border: solid 2px rgba($fgColor, .5);

  z-index: 3000;
}

@mixin backdrop-blur {
  backdrop-filter: blur(20px);
  background-color: rgba($bgColor, .25);
}
