[library Boost.Lexical_Cast
    [quickbook 1.5]
    [version 1.0]
    [copyright 2000-2005 <PERSON><PERSON><PERSON>]
    [copyright 2006-2010 <PERSON>]
    [copyright 2011-2024 <PERSON>]
    [category String and text processing]
    [category Miscellaneous]
    [license
        Distributed under the Boost Software License, Version 1.0.
        (See accompanying file LICENSE_1_0.txt or copy at
        [@http://www.boost.org/LICENSE_1_0.txt])
    ]
]

[def __numericcast__  [@boost:libs/numeric/conversion/doc/html/boost_numericconversion/improved_numeric_cast__.html `boost::numeric_cast`]]
[def __proposallong__ [@http://www.open-std.org/jtc1/sc22/wg21/docs/papers/2006/n1973.html Lexical Conversion Library Proposal for TR2, N1973 by <PERSON><PERSON><PERSON> and <PERSON><PERSON>]]
[def __proposalshort__ [@http://www.open-std.org/jtc1/sc22/wg21/docs/papers/2006/n1973.html Lexical Conversion Library Proposal for TR2, N1973]]

[section Motivation]
Sometimes a value must be converted to a literal text form, such as an [c++] `int` represented as a `std::string`, or vice-versa, when a `std::string` is interpreted as an `int`. Such examples are common when converting between data types internal to a program and representation external to a program, such as windows and configuration files.

The standard C and C++ libraries offer a number of facilities for performing such conversions. However, they vary with their ease of use, extensibility, and safety.

For instance, there are a number of limitations with the family of standard C functions typified by `atoi`:

* Conversion is supported in one direction only: from text to internal data type. Converting the other way using the C library requires either the inconvenience and compromised safety of the `sprintf` function, or the loss of portability associated with non-standard functions such as `itoa`.
* The range of types supported is only a subset of the built-in numeric types, namely `int`, `long`, and `double`.
* The range of types cannot be extended in a uniform manner. For instance, conversion from string representation to complex or rational.

The standard C functions typified by `strtol` have the same basic limitations, but offer finer control over the conversion process. However, for the common case such control is often either not required or not used. The `scanf` family of functions offer even greater control, but also lack safety and ease of use.

The standard C++ library offers `stringstream` for the kind of in-core formatting being discussed. It offers a great deal of control over the formatting and conversion of I/O to and from arbitrary types through text. However, for simple conversions direct use of `stringstream` can be either clumsy (with the introduction of extra local variables and the loss of infix-expression convenience) or obscure (where `stringstream` objects are created as temporary objects in an expression). Facets provide a comprehensive concept and facility for controlling textual representation, but their perceived complexity and high entry level requires an extreme degree of involvement for simple conversions, and excludes all but a few programmers.

The `lexical_cast` function template offers a convenient and consistent form for supporting common conversions to and from arbitrary types when they are represented as text. The simplification it offers is in expression-level convenience for such conversions. For more involved conversions, such as where precision or formatting need tighter control than is offered by the default behavior of `lexical_cast`, the conventional `std::stringstream` approach is recommended. Where the conversions are numeric to numeric, __numericcast__ may offer more reasonable behavior than `lexical_cast`.

For a good discussion of the options and issues involved in string-based formatting, including comparison of `stringstream`, `lexical_cast`, and others, see Herb Sutter's article, [@http://www.gotw.ca/publications/mill19.htm The String Formatters of Manor Farm]. Also, take a look at the [link boost_lexical_cast.performance Performance] section.
[endsect]

[section Examples]

[import ../example/args_to_numbers.cpp]

[section Strings to numbers conversion] [lexical_cast_args_example] [endsect]

[import ../example/small_examples.cpp]
[section Numbers to strings conversion] [lexical_cast_log_errno] [endsect]
[section Converting to string without dynamic memory allocation] [lexical_cast_fixed_buffer] [endsect]
[section Converting part of the string] [lexical_cast_substring_conversion] [endsect]

[import ../example/generic_stringize.cpp]
[section Generic programming (Boost.Fusion)] [lexical_cast_stringize] [endsect]

[import ../example/variant_to_long_double.cpp]
[section Generic programming (Boost.Variant)] [lexical_cast_variant_to_long_double] [endsect]

[endsect]

[section Synopsis]
Library features defined in [@boost:boost/lexical_cast.hpp boost/lexical_cast.hpp]:
``
    namespace boost
    {
        class bad_lexical_cast;
        
        template<typename Target, typename Source>
          Target lexical_cast(const Source& arg);

        template <typename Target>
          Target lexical_cast(const AnyCharacterType* chars, std::size_t count);

        namespace conversion
        {
            template<typename Target, typename Source>
                bool try_lexical_convert(const Source& arg, Target& result);

            template <typename AnyCharacterType, typename Target>
                bool try_lexical_convert(const AnyCharacterType* chars, std::size_t count, Target& result);

        } // namespace conversion
    } // namespace boost
``

[section lexical_cast]
``
    template<typename Target, typename Source>
      Target lexical_cast(const Source& arg);
``
Returns the result of streaming arg into a standard library string-based stream and then out as a Target object. Where Target is either `std::string` or `std::wstring`, stream extraction takes the whole content of the string, including spaces, rather than relying on the default `operator>>` behavior. If the conversion is unsuccessful, a `bad_lexical_cast` exception is thrown.

``
    template <typename Target>
      Target lexical_cast(const AnyCharacterType* chars, std::size_t count);
``
Takes an array of `count` characters as input parameter and streams them out as a Target object. If the conversion is unsuccessful, a `bad_lexical_cast` exception is thrown. This call may be useful for processing nonzero terminated array of characters or processing just some part of character array.

The requirements on the argument and result types for both functions are:

* Source is OutputStreamable, meaning that an `operator<<` is defined that takes a `std::ostream` or `std::wostream` object on the left hand side and an instance of the argument type on the right.
* Target is InputStreamable, meaning that an `operator>>` is defined that takes a `std::istream` or `std::wistream` object on the left hand side and an instance of the result type on the right.
* Target is CopyConstructible [20.1.3].
* Target is DefaultConstructible, meaning that it is possible to default-initialize an object of that type [8.5, 20.1.4].

The character type of the underlying stream is assumed to be `char` unless either the `Source` or the `Target` requires wide-character streaming, in which case the underlying stream uses `wchar_t`. Following types also can use `char16_t` or `char32_t` for wide-character streaming:

* Single character: `char16_t`, `char32_t`
* Arrays of characters: `char16_t *`, `char32_t *`, `const char16_t *`, `const char32_t *`
* Strings: `std::basic_string`, `boost::containers::basic_string`
* `boost::iterator_range<WideCharPtr>`, where `WideCharPtr` is a pointer to wide-character or pointer to const wide-character
* `boost::array<CharT, N>` and `std::array<CharT, N>`, `boost::array<const CharT, N>` and `std::array<const CharT, N>` 

[important Many compilers and runtime libraries fail to make conversions using new Unicode characters. Make sure that the following code compiles and outputs nonzero values, before using new types:
``
    std::cout
        << boost::lexical_cast<std::u32string>(1.0).size()
        << "  "
        << boost::lexical_cast<std::u16string>(1.0).size();
``
]

Where a higher degree of control is required over conversions, `std::stringstream` and `std::wstringstream` offer a more appropriate path. Where non-stream-based conversions are required, `lexical_cast` is the wrong tool for the job and is not special-cased for such scenarios.
[endsect]

[section bad_lexical_cast]
``
    class bad_lexical_cast : public std::bad_cast
    {
    public:
        ... // same member function interface as std::exception
    };
``
Exception used to indicate runtime lexical_cast failure.
[endsect]

[section try_lexical_convert]
`boost::lexical_cast` remains the main interface for lexical conversions. It must be used by default in most cases. However
some developers wish to make their own conversion functions, reusing all the optimizations of the `boost::lexical_cast`.
That's where the `boost::conversion::try_lexical_convert` function steps in.

`try_lexical_convert` returns `true` if conversion succeeded, otherwise returns `false`. If conversion
failed and `false` was returned, state of `result` output variable is undefined.

Actually, `boost::lexical_cast` is implemented using `try_lexical_convert`:
``
    template <typename Target, typename Source>
    inline Target lexical_cast(const Source &arg)
    {
        Target result;

        if (!conversion::try_lexical_convert(arg, result))
            throw bad_lexical_cast();

        return result;
    }
``

`try_lexical_convert` relaxes the CopyConstructible and DefaultConstructible requirements for `Target` type.
Following requirements for `Target` and `Source` remain:

* Source must be OutputStreamable, meaning that an `operator<<` is defined that takes a `std::ostream` or `std::wostream` object on the left hand side and an instance of the argument type on the right.
* Target must be InputStreamable, meaning that an `operator>>` is defined that takes a `std::istream` or `std::wistream` object on the left hand side and an instance of the result type on the right.

[endsect]


[endsect]

[section Frequently Asked Questions]

* [*Question:]    Why does `lexical_cast<int8_t>("127")` throw `bad_lexical_cast`?
  * [*Answer:]      The type `int8_t` is a `typedef` to `char` or `signed char`. Lexical conversion to these types is simply reading a byte from source but since the source has more than one byte, the exception is thrown.
Please use other integer types such as `int` or `short int`. If bounds checking is important, you can also
call __numericcast__:
`numeric_cast<int8_t>(lexical_cast<int>("127"));`

[pre
]

* [*Question:]    Why does `lexical_cast<unsigned char>("127")` throw `bad_lexical_cast`?
  * [*Answer:]      Lexical conversion to any char type is simply reading a byte from source. But since the source has more than one byte, the exception is thrown.
Please use other integer types such as `int` or `short int`. If bounds checking is important, you can also
call __numericcast__:
`numeric_cast<unsigned char>(lexical_cast<int>("127"));`

[pre
]

* [*Question:]    What does `lexical_cast<std::string>` of an `int8_t` or `uint8_t` not do what I expect?
  * [*Answer:]      As above, note that int8_t and uint8_t are actually chars and are formatted as such. To avoid
this, cast to an integer type first: `lexical_cast<std::string>(static_cast<int>(n));`

[pre
]

* [*Question:]    The implementation always resets the `ios_base::skipws` flag of an underlying stream object.
It breaks my `operator>>` that works only in presence of this flag. Can you remove code that resets the flag?
  * [*Answer:]      May be in a future version. There is no requirement in
__proposallong__ to reset the flag but
remember that __proposalshort__ is not yet accepted by the committee. By the way, it's a great opportunity to
make your `operator>>` more general.
Read a good C++ book, study `std::sentry` and [@boost:libs/io/doc/ios_state.html `ios_state_saver`].

[pre
]

* [*Question:]    Why `std::cout << boost::lexical_cast<unsigned int>("-1");` does not throw, but outputs 4294967295?
  * [*Answer:]      `boost::lexical_cast` has the behavior of `std::stringstream`, which uses `num_get` functions of
`std::locale` to convert numbers. If we look at the Programming languages — C++, we'll see, that `num_get` uses
the rules of `scanf` for conversions. And in the C99 standard for unsigned input value minus sign is optional, so
if a negative number is read, no errors will arise and the result will be the two's complement.

[pre
]

* [*Question:]    Why `boost::lexical_cast<int>(L'A');` outputs 65 and `boost::lexical_cast<wchar_t>(L"65");` does not throw?
  * [*Answer:]      If you are using an old version of Visual Studio or compile code with /Zc:wchar_t- flag,
`boost::lexical_cast` sees single `wchar_t` character as `unsigned short`. It is not a `boost::lexical_cast` mistake, but a
limitation of compiler options that you use.

[pre
]

* [*Question:]    Why `boost::lexical_cast<double>("-1.#IND");` throws `boost::bad_lexical_cast`?
  * [*Answer:]      `"-1.#IND"` is a compiler extension, that violates standard. You shall input `"-nan"`, `"nan"`, `"inf"`
, `"-inf"` (case insensitive) strings to get NaN and Inf values. `boost::lexical_cast<string>` outputs `"-nan"`, `"nan"`,
`"inf"`, `"-inf"` strings, when has NaN or Inf input values.

[pre
]

* [*Question:]    What is the fastest way to convert a non zero terminated string or a substring using `boost::lexical_cast`?
  * [*Answer:]      Use `boost::iterator_range` for conversion or `lexical_cast` overload with two parameters. For example, if you whant to convert to `int` two characters from a string `str`, you shall write `lexical_cast<int>(make_iterator_range(str.data(), str.data() + 2));` or `lexical_cast<int>(str.data(), 2);`.

[endsect]

[section Changes]

* [*boost 1.85.0 :]

    * Significant rewrite of the internal logic to separate optimized and C++ Standard Library IO-based streams:
      * C++ Standard Library based streams now constructed in less cases leading to [*better performance];
      * less template instantiations and simpler code;
      * always use `std::char_traits` (do not use custom traits from input/output types as it leads to linktime or runtime errors);
      * support for `volatile` input types was dropped, following the C++ Standard Library trend.
    * Optimized conversions from std::basic_string_view and boost::basic_string_view
    * Dropped dependency on Boost.NumericConversion and Boost.MPL. Fixed some cases
      of converting floting point types to arithmetics.
    * The library now works fine with `-fno-sanitize-recover=integer`.

* [*boost 1.84.0 :]

    * Dropped support of C++98 and C++03.

* [*boost 1.56.0 :]

    * Added `boost::conversion::try_lexical_convert` functions.

* [*boost 1.54.0 :]

    * Fix some issues with `boost::int128_type` and `boost::uint128_type` conversions. Notify user at compile time 
      if the `std::numeric_limits` are not specialized for 128bit types and `boost::lexical_cast` can not make conversions.

* [*boost 1.54.0 :]

    * Added code to convert `boost::int128_type` and `boost::uint128_type` types (requires GCC 4.7 or higher).
    * Conversions to pointers will now fail to compile, instead of throwing at runtime.
    * Restored ability to get pointers to `lexical_cast` function (was broken in 1.53.0).

* [*boost 1.53.0 :]

    * Much better input and output streams detection for user defined types.

* [*boost 1.52.0 :]

    * Restored compilation on MSVC-2003 (was broken in 1.51.0).
    * Added `lexical_cast(const CharType* chars, std::size_t count)` function overload.
    
* [*boost 1.51.0 :]

    * Better performance, less memory usage for `boost::array<character_type, N>` and `std::array<character_type, N>` conversions.

* [*boost 1.50.0 :]

    * `boost::bad_lexical_cast` exception is now globaly visible and can be catched even if code is compiled with -fvisibility=hidden.
    * Now it is possible to compile library with disabled exceptions.
    * Better performance, less memory usage and bugfixes for `boost::iterator_range<character_type*>` conversions.

* [*boost 1.49.0 :]

    * Restored work with typedefed wchar_t (compilation flag /Zc:wchar_t- for Visual Studio).
    * Better performance and less memory usage for `boost::container::basic_string` conversions.

* [*boost 1.48.0 :]

    * Added code to work with Inf and NaN on any platform.
    * Better performance and less memory usage for conversions to float type (and to double type, if `sizeof(double) < sizeof(long double)`).

* [*boost 1.47.0 :]

    * Optimizations for "C" and other locales without number grouping.
    * Better performance and less memory usage for unsigned char and signed char conversions.
    * Better performance and less memory usage for conversions to arithmetic types.
    * Better performance and less memory usage for conversions from arithmetic type to arithmetic type.
    * Directly construct Target from Source on some conversions (like conversions from string to string, from char array to string, from char to char and others).

* [*boost 1.34.0 :]

    * Better performance for many combinations of Source and Target types. For more details refer to Alexander Nasonovs article [@http://accu.org/index.php/journals/1375 Fine Tuning for lexical_cast, Overload #74, August 2006] [@http://www.accu.org/var/uploads/journals/overload74.pdf (PDF)].

* [*boost 1.33.0 :]

    * Call-by-const reference for the parameters. This requires partial specialization of class templates, so it doesn't work for MSVC 6, and it uses the original pass by value there.
    * The MSVC 6 support is deprecated, and will be removed in a future Boost version.

* [*Earlier :]

    * The previous version of lexical_cast used the default stream precision for reading and writing floating-point numbers. For numerics that have a corresponding specialization of `std::numeric_limits`, the current version now chooses a precision to match.
    * The previous version of lexical_cast did not support conversion to or from any wide-character-based types. For compilers with full language and library support for wide characters, `lexical_cast` now supports conversions from `wchar_t`, `wchar_t *`, and `std::wstring` and to `wchar_t` and `std::wstring`.
    * The previous version of `lexical_cast` assumed that the conventional stream extractor operators were sufficient for reading values. However, string I/O is asymmetric, with the result that spaces play the role of I/O separators rather than string content. The current version fixes this error for `std::string` and, where supported, `std::wstring`: `lexical_cast<std::string>("Hello, World")` succeeds instead of failing with a `bad_lexical_cast` exception.
    * The previous version of `lexical_cast` allowed unsafe and meaningless conversions to pointers. The current version now throws a `bad_lexical_cast` for conversions to pointers: `lexical_cast<char *>("Goodbye, World")` now throws an exception instead of causing undefined behavior.

[endsect]

[section Performance]

In most cases `boost::lexical_cast` is faster than `scanf`, `printf`, `std::stringstream`. For more detailed info you can look at the tables below.

[section Tests description]
All the tests measure execution speed in milliseconds for 10000 iterations of the following code blocks:
[table:legend Tests source code
[[Test name] [Code]]
[[lexical_cast]
  [``
            _out = boost::lexical_cast<OUTTYPE>(_in);
  ``]
  ]
[[std::stringstream with construction]
  [``
            std::stringstream ss;
            ss << _in;
            if (ss.fail()) throw std::logic_error(descr);
            ss >> _out;
            if (ss.fail()) throw std::logic_error(descr);
  ``]
  ]
[[std::stringstream without construction]
  [``
            ss << _in; // ss is an instance of std::stringstream
            if (ss.fail()) throw std::logic_error(descr);
            ss >> _out;
            if (ss.fail()) throw std::logic_error(descr);
            /* reseting std::stringstream to use it again */
            ss.str(std::string());
            ss.clear();
  ``]
  ]
[[scanf/printf]
  [``
            typename OUTTYPE::value_type buffer[500];
            sprintf( (char*)buffer, conv, _in);
            _out = buffer;
  ``]
  ]
]
Fastest results are highlitened with "!!! *x* !!!".
Do not use this results to compare compilers, because tests were taken on different hardware.

[endsect]

[/ BEGIN of section, generated by performance measuring program ]



[section GNU C++ version 9.4.0]
[table:id Performance Table ( GNU C++ version 9.4.0)
[[From->To] [lexical_cast] [std::stringstream with construction] [std::stringstream without construction][scanf/printf]]
  [[ string->char ][ !!! *<1* !!! ][ 59 ][ 7 ][ 17 ]]
  [[ string->signed char ][ !!! *<1* !!! ][ 142 ][ 13 ][ 21 ]]
  [[ string->unsigned char ][ !!! *<1* !!! ][ 63 ][ 5 ][ 8 ]]
  [[ string->int ][ !!! *4* !!! ][ 63 ][ 13 ][ 12 ]]
  [[ string->short ][ !!! *4* !!! ][ 65 ][ 12 ][ 12 ]]
  [[ string->long int ][ !!! *4* !!! ][ 62 ][ 12 ][ 12 ]]
  [[ string->long long ][ !!! *3* !!! ][ 61 ][ 13 ][ 12 ]]
  [[ string->unsigned int ][ !!! *4* !!! ][ 58 ][ 10 ][ 12 ]]
  [[ string->unsigned short ][ !!! *4* !!! ][ 59 ][ 10 ][ 12 ]]
  [[ string->unsigned long int ][ !!! *4* !!! ][ 62 ][ 11 ][ 12 ]]
  [[ string->unsigned long long ][ !!! *4* !!! ][ 64 ][ 11 ][ 12 ]]
  [[ string->float ][ 59 ][ 92 ][ 35 ][ !!! *23* !!! ]]
  [[ string->double ][ 55 ][ 89 ][ 31 ][ !!! *23* !!! ]]
  [[ string->long double ][ 52 ][ 83 ][ 32 ][ !!! *26* !!! ]]
  [[ string->array<char, 50> ][ !!! *<1* !!! ][ 59 ][ 10 ][ 8 ]]
  [[ string->string ][ !!! *1* !!! ][ 68 ][ 13 ][ --- ]]
  [[ string->container::string ][ !!! *<1* !!! ][ 65 ][ 11 ][ --- ]]
  [[ string->char ][ !!! *1* !!! ][ 58 ][ 10 ][ 7 ]]
  [[ string->signed char ][ !!! *1* !!! ][ 57 ][ 10 ][ 10 ]]
  [[ string->unsigned char ][ !!! *1* !!! ][ 57 ][ 10 ][ 10 ]]
  [[ int->string ][ !!! *4* !!! ][ 64 ][ 13 ][ 10 ]]
  [[ short->string ][ !!! *4* !!! ][ 69 ][ 13 ][ 10 ]]
  [[ long int->string ][ !!! *4* !!! ][ 68 ][ 13 ][ 10 ]]
  [[ long long->string ][ !!! *4* !!! ][ 66 ][ 13 ][ 10 ]]
  [[ unsigned int->string ][ !!! *4* !!! ][ 62 ][ 14 ][ 10 ]]
  [[ unsigned short->string ][ !!! *4* !!! ][ 66 ][ 13 ][ 10 ]]
  [[ unsigned long int->string ][ !!! *4* !!! ][ 70 ][ 13 ][ 10 ]]
  [[ unsigned long long->string ][ !!! *4* !!! ][ 64 ][ 14 ][ 10 ]]
  [[ float->string ][ 26 ][ 108 ][ 50 ][ !!! *23* !!! ]]
  [[ double->string ][ 33 ][ 114 ][ 51 ][ !!! *26* !!! ]]
  [[ long double->string ][ 88 ][ 194 ][ 54 ][ !!! *26* !!! ]]
  [[ char*->char ][ !!! *<1* !!! ][ 53 ][ 6 ][ 5 ]]
  [[ char*->signed char ][ !!! *<1* !!! ][ 52 ][ 6 ][ 7 ]]
  [[ char*->unsigned char ][ !!! *<1* !!! ][ 55 ][ 6 ][ 7 ]]
  [[ char*->int ][ !!! *4* !!! ][ 67 ][ 12 ][ 10 ]]
  [[ char*->short ][ !!! *4* !!! ][ 68 ][ 13 ][ 10 ]]
  [[ char*->long int ][ !!! *3* !!! ][ 68 ][ 13 ][ 10 ]]
  [[ char*->long long ][ !!! *3* !!! ][ 67 ][ 14 ][ 10 ]]
  [[ char*->unsigned int ][ !!! *3* !!! ][ 67 ][ 11 ][ 10 ]]
  [[ char*->unsigned short ][ !!! *3* !!! ][ 61 ][ 11 ][ 11 ]]
  [[ char*->unsigned long int ][ !!! *3* !!! ][ 61 ][ 13 ][ 10 ]]
  [[ char*->unsigned long long ][ !!! *3* !!! ][ 67 ][ 13 ][ 10 ]]
  [[ char*->float ][ 56 ][ 93 ][ 35 ][ !!! *21* !!! ]]
  [[ char*->double ][ 59 ][ 88 ][ 33 ][ !!! *21* !!! ]]
  [[ char*->long double ][ 54 ][ 88 ][ 33 ][ !!! *24* !!! ]]
  [[ char*->array<char, 50> ][ !!! *<1* !!! ][ 63 ][ 11 ][ 8 ]]
  [[ char*->string ][ !!! *1* !!! ][ 65 ][ 12 ][ --- ]]
  [[ char*->container::string ][ !!! *<1* !!! ][ 71 ][ 13 ][ --- ]]
  [[ unsigned char*->char ][ !!! *<1* !!! ][ 57 ][ 6 ][ 5 ]]
  [[ unsigned char*->signed char ][ !!! *<1* !!! ][ 57 ][ 6 ][ 7 ]]
  [[ unsigned char*->unsigned char ][ !!! *<1* !!! ][ 63 ][ 5 ][ 6 ]]
  [[ unsigned char*->int ][ !!! *4* !!! ][ 63 ][ 12 ][ 10 ]]
  [[ unsigned char*->short ][ !!! *4* !!! ][ 65 ][ 13 ][ 10 ]]
  [[ unsigned char*->long int ][ !!! *3* !!! ][ 67 ][ 12 ][ 10 ]]
  [[ unsigned char*->long long ][ !!! *3* !!! ][ 66 ][ 12 ][ 10 ]]
  [[ unsigned char*->unsigned int ][ !!! *3* !!! ][ 64 ][ 11 ][ 10 ]]
  [[ unsigned char*->unsigned short ][ !!! *3* !!! ][ 65 ][ 12 ][ 10 ]]
  [[ unsigned char*->unsigned long int ][ !!! *3* !!! ][ 60 ][ 13 ][ 10 ]]
  [[ unsigned char*->unsigned long long ][ !!! *3* !!! ][ 62 ][ 12 ][ 10 ]]
  [[ unsigned char*->float ][ 57 ][ 94 ][ 34 ][ !!! *21* !!! ]]
  [[ unsigned char*->double ][ 60 ][ 88 ][ 35 ][ !!! *21* !!! ]]
  [[ unsigned char*->long double ][ 54 ][ 89 ][ 32 ][ !!! *24* !!! ]]
  [[ unsigned char*->array<char, 50> ][ !!! *<1* !!! ][ 68 ][ 12 ][ 8 ]]
  [[ unsigned char*->string ][ !!! *1* !!! ][ 61 ][ 13 ][ --- ]]
  [[ unsigned char*->container::string ][ !!! *<1* !!! ][ 66 ][ 12 ][ --- ]]
  [[ signed char*->char ][ !!! *<1* !!! ][ 58 ][ 6 ][ 5 ]]
  [[ signed char*->signed char ][ !!! *<1* !!! ][ 59 ][ 6 ][ 7 ]]
  [[ signed char*->unsigned char ][ !!! *<1* !!! ][ 57 ][ 6 ][ 7 ]]
  [[ signed char*->int ][ !!! *4* !!! ][ 68 ][ 12 ][ 10 ]]
  [[ signed char*->short ][ !!! *4* !!! ][ 68 ][ 12 ][ 10 ]]
  [[ signed char*->long int ][ !!! *3* !!! ][ 69 ][ 12 ][ 10 ]]
  [[ signed char*->long long ][ !!! *3* !!! ][ 61 ][ 12 ][ 11 ]]
  [[ signed char*->unsigned int ][ !!! *3* !!! ][ 59 ][ 11 ][ 10 ]]
  [[ signed char*->unsigned short ][ !!! *3* !!! ][ 58 ][ 11 ][ 10 ]]
  [[ signed char*->unsigned long int ][ !!! *3* !!! ][ 59 ][ 12 ][ 10 ]]
  [[ signed char*->unsigned long long ][ !!! *3* !!! ][ 68 ][ 12 ][ 10 ]]
  [[ signed char*->float ][ 55 ][ 93 ][ 39 ][ !!! *22* !!! ]]
  [[ signed char*->double ][ 56 ][ 95 ][ 34 ][ !!! *23* !!! ]]
  [[ signed char*->long double ][ 58 ][ 89 ][ 32 ][ !!! *23* !!! ]]
  [[ signed char*->array<char, 50> ][ !!! *<1* !!! ][ 70 ][ 11 ][ 8 ]]
  [[ signed char*->string ][ !!! *1* !!! ][ 64 ][ 15 ][ --- ]]
  [[ signed char*->container::string ][ !!! *1* !!! ][ 67 ][ 13 ][ --- ]]
  [[ iterator_range<char*>->char ][ !!! *<1* !!! ][ 62 ][ 6 ][ 7 ]]
  [[ iterator_range<char*>->signed char ][ !!! *<1* !!! ][ 56 ][ 6 ][ 8 ]]
  [[ iterator_range<char*>->unsigned char ][ !!! *<1* !!! ][ 57 ][ 5 ][ 11 ]]
  [[ iterator_range<char*>->int ][ !!! *4* !!! ][ 75 ][ 16 ][ 12 ]]
  [[ iterator_range<char*>->short ][ !!! *3* !!! ][ 69 ][ 16 ][ 12 ]]
  [[ iterator_range<char*>->long int ][ !!! *3* !!! ][ 66 ][ 21 ][ 12 ]]
  [[ iterator_range<char*>->long long ][ !!! *3* !!! ][ 70 ][ 15 ][ 12 ]]
  [[ iterator_range<char*>->unsigned int ][ !!! *3* !!! ][ 67 ][ 16 ][ 12 ]]
  [[ iterator_range<char*>->unsigned short ][ !!! *3* !!! ][ 64 ][ 14 ][ 12 ]]
  [[ iterator_range<char*>->unsigned long int ][ !!! *3* !!! ][ 66 ][ 15 ][ 12 ]]
  [[ iterator_range<char*>->unsigned long long ][ !!! *3* !!! ][ 72 ][ 17 ][ 12 ]]
  [[ iterator_range<char*>->float ][ 55 ][ 100 ][ 42 ][ !!! *23* !!! ]]
  [[ iterator_range<char*>->double ][ 54 ][ 100 ][ 40 ][ !!! *23* !!! ]]
  [[ iterator_range<char*>->long double ][ 56 ][ 100 ][ 39 ][ !!! *25* !!! ]]
  [[ iterator_range<char*>->array<char, 50> ][ !!! *<1* !!! ][ 69 ][ 20 ][ 10 ]]
  [[ iterator_range<char*>->string ][ !!! *1* !!! ][ 72 ][ 21 ][ --- ]]
  [[ iterator_range<char*>->container::string ][ !!! *<1* !!! ][ 82 ][ 21 ][ --- ]]
  [[ std::string_view->char ][ !!! *<1* !!! ][ 65 ][ 5 ][ 8 ]]
  [[ std::string_view->signed char ][ !!! *<1* !!! ][ 61 ][ 6 ][ 10 ]]
  [[ std::string_view->unsigned char ][ !!! *<1* !!! ][ 56 ][ 5 ][ 9 ]]
  [[ std::string_view->int ][ !!! *4* !!! ][ 59 ][ 12 ][ 14 ]]
  [[ std::string_view->short ][ !!! *4* !!! ][ 63 ][ 12 ][ 14 ]]
  [[ std::string_view->long int ][ !!! *4* !!! ][ 61 ][ 11 ][ 13 ]]
  [[ std::string_view->long long ][ !!! *4* !!! ][ 58 ][ 13 ][ 14 ]]
  [[ std::string_view->unsigned int ][ !!! *3* !!! ][ 65 ][ 10 ][ 13 ]]
  [[ std::string_view->unsigned short ][ !!! *3* !!! ][ 64 ][ 10 ][ 14 ]]
  [[ std::string_view->unsigned long int ][ !!! *3* !!! ][ 64 ][ 12 ][ 13 ]]
  [[ std::string_view->unsigned long long ][ !!! *3* !!! ][ 65 ][ 11 ][ 14 ]]
  [[ std::string_view->float ][ 55 ][ 89 ][ 35 ][ !!! *24* !!! ]]
  [[ std::string_view->double ][ 53 ][ 87 ][ 36 ][ !!! *25* !!! ]]
  [[ std::string_view->long double ][ 56 ][ 93 ][ 34 ][ !!! *28* !!! ]]
  [[ std::string_view->array<char, 50> ][ !!! *1* !!! ][ 64 ][ 10 ][ 11 ]]
  [[ std::string_view->string ][ !!! *1* !!! ][ 62 ][ 11 ][ --- ]]
  [[ std::string_view->container::string ][ !!! *1* !!! ][ 65 ][ 11 ][ --- ]]
  [[ array<char, 50>->char ][ !!! *<1* !!! ][ 54 ][ 6 ][ 5 ]]
  [[ array<char, 50>->signed char ][ !!! *<1* !!! ][ 54 ][ 5 ][ 7 ]]
  [[ array<char, 50>->unsigned char ][ !!! *<1* !!! ][ 53 ][ 6 ][ 6 ]]
  [[ array<char, 50>->int ][ !!! *2* !!! ][ 59 ][ 12 ][ 10 ]]
  [[ array<char, 50>->short ][ !!! *3* !!! ][ 58 ][ 12 ][ 10 ]]
  [[ array<char, 50>->long int ][ !!! *3* !!! ][ 57 ][ 12 ][ 10 ]]
  [[ array<char, 50>->long long ][ !!! *3* !!! ][ 60 ][ 12 ][ 10 ]]
  [[ array<char, 50>->unsigned int ][ !!! *3* !!! ][ 60 ][ 11 ][ 10 ]]
  [[ array<char, 50>->unsigned short ][ !!! *3* !!! ][ 58 ][ 11 ][ 10 ]]
  [[ array<char, 50>->unsigned long int ][ !!! *3* !!! ][ 61 ][ 11 ][ 10 ]]
  [[ array<char, 50>->unsigned long long ][ !!! *3* !!! ][ 59 ][ 12 ][ 10 ]]
  [[ array<char, 50>->float ][ 52 ][ 89 ][ 35 ][ !!! *21* !!! ]]
  [[ array<char, 50>->double ][ 50 ][ 91 ][ 33 ][ !!! *21* !!! ]]
  [[ array<char, 50>->long double ][ 52 ][ 83 ][ 33 ][ !!! *23* !!! ]]
  [[ array<char, 50>->array<char, 50> ][ !!! *<1* !!! ][ 59 ][ 11 ][ 8 ]]
  [[ array<char, 50>->string ][ !!! *1* !!! ][ 66 ][ 13 ][ --- ]]
  [[ array<char, 50>->container::string ][ !!! *1* !!! ][ 61 ][ 12 ][ --- ]]
  [[ int->int ][ !!! *<1* !!! ][ 61 ][ 14 ][ --- ]]
  [[ float->double ][ !!! *<1* !!! ][ 136 ][ 72 ][ --- ]]
  [[ char->signed char ][ !!! *<1* !!! ][ 54 ][ 5 ][ --- ]]
]
[endsect]

[section Clang version 15.0.7 ]
[table:id Performance Table ( Clang version 15.0.7 )
[[From->To] [lexical_cast] [std::stringstream with construction] [std::stringstream without construction][scanf/printf]]
  [[ string->char ][ !!! *<1* !!! ][ 68 ][ 5 ][ 6 ]]
  [[ string->signed char ][ !!! *<1* !!! ][ 59 ][ 5 ][ 7 ]]
  [[ string->unsigned char ][ !!! *<1* !!! ][ 59 ][ 5 ][ 7 ]]
  [[ string->int ][ !!! *3* !!! ][ 69 ][ 11 ][ 10 ]]
  [[ string->short ][ !!! *3* !!! ][ 64 ][ 11 ][ 10 ]]
  [[ string->long int ][ !!! *3* !!! ][ 65 ][ 11 ][ 10 ]]
  [[ string->long long ][ !!! *3* !!! ][ 68 ][ 13 ][ 10 ]]
  [[ string->unsigned int ][ !!! *3* !!! ][ 65 ][ 10 ][ 10 ]]
  [[ string->unsigned short ][ !!! *3* !!! ][ 64 ][ 10 ][ 10 ]]
  [[ string->unsigned long int ][ !!! *3* !!! ][ 62 ][ 11 ][ 10 ]]
  [[ string->unsigned long long ][ !!! *3* !!! ][ 64 ][ 11 ][ 10 ]]
  [[ string->float ][ 55 ][ 90 ][ 33 ][ !!! *22* !!! ]]
  [[ string->double ][ 56 ][ 89 ][ 32 ][ !!! *22* !!! ]]
  [[ string->long double ][ 55 ][ 92 ][ 32 ][ !!! *23* !!! ]]
  [[ string->array<char, 50> ][ !!! *<1* !!! ][ 66 ][ 10 ][ 8 ]]
  [[ string->string ][ !!! *1* !!! ][ 66 ][ 12 ][ --- ]]
  [[ string->container::string ][ !!! *<1* !!! ][ 66 ][ 15 ][ --- ]]
  [[ string->char ][ !!! *<1* !!! ][ 61 ][ 9 ][ 1 ]]
  [[ string->signed char ][ !!! *<1* !!! ][ 62 ][ 9 ][ 10 ]]
  [[ string->unsigned char ][ !!! *<1* !!! ][ 59 ][ 9 ][ 10 ]]
  [[ int->string ][ !!! *4* !!! ][ 63 ][ 13 ][ 9 ]]
  [[ short->string ][ !!! *4* !!! ][ 69 ][ 13 ][ 10 ]]
  [[ long int->string ][ !!! *4* !!! ][ 67 ][ 13 ][ 10 ]]
  [[ long long->string ][ !!! *4* !!! ][ 65 ][ 13 ][ 10 ]]
  [[ unsigned int->string ][ !!! *4* !!! ][ 66 ][ 13 ][ 10 ]]
  [[ unsigned short->string ][ !!! *4* !!! ][ 67 ][ 13 ][ 10 ]]
  [[ unsigned long int->string ][ !!! *4* !!! ][ 66 ][ 12 ][ 10 ]]
  [[ unsigned long long->string ][ !!! *4* !!! ][ 67 ][ 13 ][ 10 ]]
  [[ float->string ][ 25 ][ 108 ][ 47 ][ !!! *22* !!! ]]
  [[ double->string ][ !!! *32* !!! ][ 130 ][ 116 ][ 56 ]]
  [[ long double->string ][ 103 ][ 266 ][ 123 ][ !!! *64* !!! ]]
  [[ char*->char ][ !!! *<1* !!! ][ 129 ][ 13 ][ 14 ]]
  [[ char*->signed char ][ !!! *<1* !!! ][ 131 ][ 13 ][ 17 ]]
  [[ char*->unsigned char ][ !!! *<1* !!! ][ 133 ][ 13 ][ 17 ]]
  [[ char*->int ][ !!! *7* !!! ][ 154 ][ 32 ][ 26 ]]
  [[ char*->short ][ !!! *7* !!! ][ 161 ][ 29 ][ 27 ]]
  [[ char*->long int ][ !!! *7* !!! ][ 159 ][ 30 ][ 27 ]]
  [[ char*->long long ][ !!! *7* !!! ][ 158 ][ 28 ][ 26 ]]
  [[ char*->unsigned int ][ !!! *7* !!! ][ 146 ][ 26 ][ 26 ]]
  [[ char*->unsigned short ][ !!! *7* !!! ][ 150 ][ 26 ][ 26 ]]
  [[ char*->unsigned long int ][ !!! *7* !!! ][ 159 ][ 28 ][ 26 ]]
  [[ char*->unsigned long long ][ !!! *7* !!! ][ 158 ][ 30 ][ 27 ]]
  [[ char*->float ][ 123 ][ 207 ][ 78 ][ !!! *54* !!! ]]
  [[ char*->double ][ 135 ][ 218 ][ 77 ][ !!! *54* !!! ]]
  [[ char*->long double ][ 130 ][ 216 ][ 82 ][ !!! *59* !!! ]]
  [[ char*->array<char, 50> ][ !!! *<1* !!! ][ 153 ][ 25 ][ 21 ]]
  [[ char*->string ][ !!! *2* !!! ][ 159 ][ 31 ][ --- ]]
  [[ char*->container::string ][ !!! *3* !!! ][ 151 ][ 15 ][ --- ]]
  [[ unsigned char*->char ][ !!! *<1* !!! ][ 56 ][ 5 ][ 6 ]]
  [[ unsigned char*->signed char ][ !!! *<1* !!! ][ 58 ][ 5 ][ 7 ]]
  [[ unsigned char*->unsigned char ][ !!! *<1* !!! ][ 59 ][ 5 ][ 7 ]]
  [[ unsigned char*->int ][ !!! *3* !!! ][ 58 ][ 12 ][ 11 ]]
  [[ unsigned char*->short ][ !!! *3* !!! ][ 67 ][ 12 ][ 11 ]]
  [[ unsigned char*->long int ][ !!! *3* !!! ][ 67 ][ 12 ][ 11 ]]
  [[ unsigned char*->long long ][ !!! *3* !!! ][ 70 ][ 11 ][ 11 ]]
  [[ unsigned char*->unsigned int ][ !!! *3* !!! ][ 60 ][ 10 ][ 10 ]]
  [[ unsigned char*->unsigned short ][ !!! *3* !!! ][ 65 ][ 10 ][ 11 ]]
  [[ unsigned char*->unsigned long int ][ !!! *3* !!! ][ 61 ][ 11 ][ 10 ]]
  [[ unsigned char*->unsigned long long ][ !!! *3* !!! ][ 58 ][ 11 ][ 10 ]]
  [[ unsigned char*->float ][ 51 ][ 88 ][ 32 ][ !!! *22* !!! ]]
  [[ unsigned char*->double ][ 54 ][ 88 ][ 32 ][ !!! *22* !!! ]]
  [[ unsigned char*->long double ][ 51 ][ 88 ][ 35 ][ !!! *24* !!! ]]
  [[ unsigned char*->array<char, 50> ][ !!! *<1* !!! ][ 127 ][ 26 ][ 21 ]]
  [[ unsigned char*->string ][ !!! *2* !!! ][ 90 ][ 11 ][ --- ]]
  [[ unsigned char*->container::string ][ !!! *1* !!! ][ 62 ][ 15 ][ --- ]]
  [[ signed char*->char ][ !!! *<1* !!! ][ 52 ][ 5 ][ 5 ]]
  [[ signed char*->signed char ][ !!! *<1* !!! ][ 53 ][ 5 ][ 7 ]]
  [[ signed char*->unsigned char ][ !!! *<1* !!! ][ 52 ][ 5 ][ 7 ]]
  [[ signed char*->int ][ !!! *3* !!! ][ 60 ][ 12 ][ 11 ]]
  [[ signed char*->short ][ !!! *3* !!! ][ 63 ][ 12 ][ 11 ]]
  [[ signed char*->long int ][ !!! *3* !!! ][ 63 ][ 11 ][ 11 ]]
  [[ signed char*->long long ][ !!! *3* !!! ][ 64 ][ 11 ][ 11 ]]
  [[ signed char*->unsigned int ][ !!! *3* !!! ][ 60 ][ 10 ][ 10 ]]
  [[ signed char*->unsigned short ][ !!! *3* !!! ][ 59 ][ 10 ][ 10 ]]
  [[ signed char*->unsigned long int ][ !!! *2* !!! ][ 62 ][ 11 ][ 10 ]]
  [[ signed char*->unsigned long long ][ !!! *3* !!! ][ 66 ][ 11 ][ 10 ]]
  [[ signed char*->float ][ 51 ][ 86 ][ 32 ][ !!! *22* !!! ]]
  [[ signed char*->double ][ 55 ][ 87 ][ 32 ][ !!! *22* !!! ]]
  [[ signed char*->long double ][ 55 ][ 86 ][ 32 ][ !!! *24* !!! ]]
  [[ signed char*->array<char, 50> ][ !!! *<1* !!! ][ 64 ][ 10 ][ 8 ]]
  [[ signed char*->string ][ !!! *1* !!! ][ 62 ][ 12 ][ --- ]]
  [[ signed char*->container::string ][ !!! *1* !!! ][ 66 ][ 15 ][ --- ]]
  [[ iterator_range<char*>->char ][ !!! *<1* !!! ][ 53 ][ 5 ][ 5 ]]
  [[ iterator_range<char*>->signed char ][ !!! *<1* !!! ][ 55 ][ 5 ][ 7 ]]
  [[ iterator_range<char*>->unsigned char ][ !!! *<1* !!! ][ 57 ][ 5 ][ 7 ]]
  [[ iterator_range<char*>->int ][ !!! *3* !!! ][ 67 ][ 15 ][ 11 ]]
  [[ iterator_range<char*>->short ][ !!! *3* !!! ][ 96 ][ 37 ][ 27 ]]
  [[ iterator_range<char*>->long int ][ !!! *7* !!! ][ 166 ][ 37 ][ 27 ]]
  [[ iterator_range<char*>->long long ][ !!! *7* !!! ][ 150 ][ 37 ][ 26 ]]
  [[ iterator_range<char*>->unsigned int ][ !!! *7* !!! ][ 158 ][ 34 ][ 26 ]]
  [[ iterator_range<char*>->unsigned short ][ !!! *7* !!! ][ 170 ][ 36 ][ 26 ]]
  [[ iterator_range<char*>->unsigned long int ][ !!! *7* !!! ][ 154 ][ 35 ][ 26 ]]
  [[ iterator_range<char*>->unsigned long long ][ !!! *7* !!! ][ 158 ][ 36 ][ 26 ]]
  [[ iterator_range<char*>->float ][ 122 ][ 233 ][ 100 ][ !!! *54* !!! ]]
  [[ iterator_range<char*>->double ][ 134 ][ 245 ][ 97 ][ !!! *54* !!! ]]
  [[ iterator_range<char*>->long double ][ 127 ][ 238 ][ 98 ][ !!! *59* !!! ]]
  [[ iterator_range<char*>->array<char, 50> ][ !!! *<1* !!! ][ 159 ][ 42 ][ 21 ]]
  [[ iterator_range<char*>->string ][ !!! *2* !!! ][ 165 ][ 50 ][ --- ]]
  [[ iterator_range<char*>->container::string ][ !!! *1* !!! ][ 186 ][ 58 ][ --- ]]
  [[ std::string_view->char ][ !!! *<1* !!! ][ 130 ][ 12 ][ 14 ]]
  [[ std::string_view->signed char ][ !!! *<1* !!! ][ 128 ][ 12 ][ 18 ]]
  [[ std::string_view->unsigned char ][ !!! *<1* !!! ][ 134 ][ 12 ][ 17 ]]
  [[ std::string_view->int ][ !!! *7* !!! ][ 153 ][ 31 ][ 27 ]]
  [[ std::string_view->short ][ !!! *7* !!! ][ 148 ][ 29 ][ 26 ]]
  [[ std::string_view->long int ][ !!! *7* !!! ][ 150 ][ 28 ][ 26 ]]
  [[ std::string_view->long long ][ !!! *7* !!! ][ 116 ][ 11 ][ 10 ]]
  [[ std::string_view->unsigned int ][ !!! *2* !!! ][ 57 ][ 10 ][ 10 ]]
  [[ std::string_view->unsigned short ][ !!! *3* !!! ][ 57 ][ 10 ][ 10 ]]
  [[ std::string_view->unsigned long int ][ !!! *2* !!! ][ 66 ][ 11 ][ 10 ]]
  [[ std::string_view->unsigned long long ][ !!! *2* !!! ][ 61 ][ 11 ][ 10 ]]
  [[ std::string_view->float ][ 52 ][ 90 ][ 31 ][ !!! *22* !!! ]]
  [[ std::string_view->double ][ 56 ][ 92 ][ 31 ][ !!! *22* !!! ]]
  [[ std::string_view->long double ][ 56 ][ 95 ][ 32 ][ !!! *24* !!! ]]
  [[ std::string_view->array<char, 50> ][ !!! *<1* !!! ][ 65 ][ 10 ][ 8 ]]
  [[ std::string_view->string ][ !!! *1* !!! ][ 60 ][ 12 ][ --- ]]
  [[ std::string_view->container::string ][ !!! *2* !!! ][ 67 ][ 14 ][ --- ]]
  [[ array<char, 50>->char ][ !!! *<1* !!! ][ 53 ][ 5 ][ 6 ]]
  [[ array<char, 50>->signed char ][ !!! *<1* !!! ][ 54 ][ 5 ][ 7 ]]
  [[ array<char, 50>->unsigned char ][ !!! *<1* !!! ][ 53 ][ 5 ][ 7 ]]
  [[ array<char, 50>->int ][ !!! *3* !!! ][ 63 ][ 12 ][ 11 ]]
  [[ array<char, 50>->short ][ !!! *3* !!! ][ 62 ][ 12 ][ 11 ]]
  [[ array<char, 50>->long int ][ !!! *3* !!! ][ 63 ][ 11 ][ 11 ]]
  [[ array<char, 50>->long long ][ !!! *3* !!! ][ 60 ][ 11 ][ 11 ]]
  [[ array<char, 50>->unsigned int ][ !!! *3* !!! ][ 57 ][ 10 ][ 11 ]]
  [[ array<char, 50>->unsigned short ][ !!! *3* !!! ][ 65 ][ 11 ][ 11 ]]
  [[ array<char, 50>->unsigned long int ][ !!! *3* !!! ][ 69 ][ 11 ][ 11 ]]
  [[ array<char, 50>->unsigned long long ][ !!! *3* !!! ][ 68 ][ 11 ][ 10 ]]
  [[ array<char, 50>->float ][ 54 ][ 82 ][ 32 ][ !!! *22* !!! ]]
  [[ array<char, 50>->double ][ 57 ][ 93 ][ 32 ][ !!! *22* !!! ]]
  [[ array<char, 50>->long double ][ 53 ][ 85 ][ 32 ][ !!! *23* !!! ]]
  [[ array<char, 50>->array<char, 50> ][ !!! *<1* !!! ][ 60 ][ 10 ][ 8 ]]
  [[ array<char, 50>->string ][ !!! *1* !!! ][ 61 ][ 11 ][ --- ]]
  [[ array<char, 50>->container::string ][ !!! *1* !!! ][ 62 ][ 15 ][ --- ]]
  [[ int->int ][ !!! *<1* !!! ][ 65 ][ 15 ][ --- ]]
  [[ float->double ][ !!! *<1* !!! ][ 138 ][ 68 ][ --- ]]
  [[ char->signed char ][ !!! *<1* !!! ][ 53 ][ 5 ][ --- ]]
]
[endsect]

[section GNU C++ version 10.5.0]
[table:id Performance Table ( GNU C++ version 10.5.0)
[[From->To] [lexical_cast] [std::stringstream with construction] [std::stringstream without construction][scanf/printf]]
  [[ string->char ][ !!! *<1* !!! ][ 64 ][ 5 ][ 5 ]]
  [[ string->signed char ][ !!! *<1* !!! ][ 51 ][ 5 ][ 7 ]]
  [[ string->unsigned char ][ !!! *<1* !!! ][ 53 ][ 5 ][ 6 ]]
  [[ string->int ][ !!! *3* !!! ][ 56 ][ 11 ][ 10 ]]
  [[ string->short ][ !!! *3* !!! ][ 56 ][ 12 ][ 10 ]]
  [[ string->long int ][ !!! *3* !!! ][ 58 ][ 13 ][ 10 ]]
  [[ string->long long ][ !!! *3* !!! ][ 56 ][ 13 ][ 10 ]]
  [[ string->unsigned int ][ !!! *3* !!! ][ 60 ][ 10 ][ 10 ]]
  [[ string->unsigned short ][ !!! *3* !!! ][ 58 ][ 10 ][ 10 ]]
  [[ string->unsigned long int ][ !!! *3* !!! ][ 59 ][ 12 ][ 10 ]]
  [[ string->unsigned long long ][ !!! *3* !!! ][ 53 ][ 11 ][ 10 ]]
  [[ string->float ][ 49 ][ 81 ][ 34 ][ !!! *21* !!! ]]
  [[ string->double ][ 45 ][ 75 ][ 28 ][ !!! *21* !!! ]]
  [[ string->long double ][ 46 ][ 76 ][ 30 ][ !!! *25* !!! ]]
  [[ string->array<char, 50> ][ !!! *<1* !!! ][ 55 ][ 10 ][ 10 ]]
  [[ string->string ][ !!! *1* !!! ][ 54 ][ 11 ][ --- ]]
  [[ string->container::string ][ !!! *<1* !!! ][ 57 ][ 11 ][ --- ]]
  [[ string->char ][ !!! *<1* !!! ][ 51 ][ 9 ][ 7 ]]
  [[ string->signed char ][ !!! *<1* !!! ][ 51 ][ 10 ][ 10 ]]
  [[ string->unsigned char ][ !!! *<1* !!! ][ 53 ][ 10 ][ 10 ]]
  [[ int->string ][ !!! *3* !!! ][ 55 ][ 12 ][ 10 ]]
  [[ short->string ][ !!! *3* !!! ][ 55 ][ 13 ][ 10 ]]
  [[ long int->string ][ !!! *3* !!! ][ 55 ][ 12 ][ 10 ]]
  [[ long long->string ][ !!! *3* !!! ][ 56 ][ 12 ][ 10 ]]
  [[ unsigned int->string ][ !!! *3* !!! ][ 54 ][ 12 ][ 10 ]]
  [[ unsigned short->string ][ !!! *3* !!! ][ 54 ][ 12 ][ 10 ]]
  [[ unsigned long int->string ][ !!! *3* !!! ][ 55 ][ 12 ][ 10 ]]
  [[ unsigned long long->string ][ !!! *3* !!! ][ 55 ][ 12 ][ 10 ]]
  [[ float->string ][ 26 ][ 96 ][ 46 ][ !!! *25* !!! ]]
  [[ double->string ][ 33 ][ 94 ][ 46 ][ !!! *23* !!! ]]
  [[ long double->string ][ 44 ][ 96 ][ 52 ][ !!! *26* !!! ]]
  [[ char*->char ][ !!! *<1* !!! ][ 48 ][ 5 ][ 5 ]]
  [[ char*->signed char ][ !!! *<1* !!! ][ 47 ][ 5 ][ 7 ]]
  [[ char*->unsigned char ][ !!! *<1* !!! ][ 47 ][ 5 ][ 6 ]]
  [[ char*->int ][ !!! *3* !!! ][ 57 ][ 12 ][ 10 ]]
  [[ char*->short ][ !!! *3* !!! ][ 55 ][ 12 ][ 10 ]]
  [[ char*->long int ][ !!! *3* !!! ][ 59 ][ 11 ][ 10 ]]
  [[ char*->long long ][ !!! *4* !!! ][ 61 ][ 11 ][ 10 ]]
  [[ char*->unsigned int ][ !!! *3* !!! ][ 53 ][ 10 ][ 10 ]]
  [[ char*->unsigned short ][ !!! *3* !!! ][ 53 ][ 10 ][ 10 ]]
  [[ char*->unsigned long int ][ !!! *3* !!! ][ 54 ][ 11 ][ 10 ]]
  [[ char*->unsigned long long ][ !!! *3* !!! ][ 55 ][ 11 ][ 10 ]]
  [[ char*->float ][ 46 ][ 78 ][ 39 ][ !!! *21* !!! ]]
  [[ char*->double ][ 43 ][ 73 ][ 28 ][ !!! *21* !!! ]]
  [[ char*->long double ][ 46 ][ 74 ][ 30 ][ !!! *23* !!! ]]
  [[ char*->array<char, 50> ][ !!! *<1* !!! ][ 52 ][ 10 ][ 8 ]]
  [[ char*->string ][ !!! *1* !!! ][ 58 ][ 11 ][ --- ]]
  [[ char*->container::string ][ !!! *<1* !!! ][ 64 ][ 11 ][ --- ]]
  [[ unsigned char*->char ][ !!! *<1* !!! ][ 47 ][ 5 ][ 5 ]]
  [[ unsigned char*->signed char ][ !!! *<1* !!! ][ 47 ][ 5 ][ 7 ]]
  [[ unsigned char*->unsigned char ][ !!! *<1* !!! ][ 47 ][ 5 ][ 7 ]]
  [[ unsigned char*->int ][ !!! *3* !!! ][ 55 ][ 12 ][ 12 ]]
  [[ unsigned char*->short ][ !!! *4* !!! ][ 59 ][ 11 ][ 10 ]]
  [[ unsigned char*->long int ][ !!! *3* !!! ][ 56 ][ 12 ][ 10 ]]
  [[ unsigned char*->long long ][ !!! *3* !!! ][ 55 ][ 12 ][ 10 ]]
  [[ unsigned char*->unsigned int ][ !!! *3* !!! ][ 53 ][ 10 ][ 10 ]]
  [[ unsigned char*->unsigned short ][ !!! *3* !!! ][ 54 ][ 10 ][ 10 ]]
  [[ unsigned char*->unsigned long int ][ !!! *3* !!! ][ 54 ][ 11 ][ 10 ]]
  [[ unsigned char*->unsigned long long ][ !!! *3* !!! ][ 55 ][ 11 ][ 10 ]]
  [[ unsigned char*->float ][ 100 ][ 143 ][ 33 ][ !!! *21* !!! ]]
  [[ unsigned char*->double ][ 44 ][ 73 ][ 28 ][ !!! *22* !!! ]]
  [[ unsigned char*->long double ][ 46 ][ 75 ][ 30 ][ !!! *23* !!! ]]
  [[ unsigned char*->array<char, 50> ][ !!! *<1* !!! ][ 53 ][ 11 ][ 8 ]]
  [[ unsigned char*->string ][ !!! *1* !!! ][ 58 ][ 11 ][ --- ]]
  [[ unsigned char*->container::string ][ !!! *<1* !!! ][ 59 ][ 11 ][ --- ]]
  [[ signed char*->char ][ !!! *<1* !!! ][ 47 ][ 5 ][ 5 ]]
  [[ signed char*->signed char ][ !!! *<1* !!! ][ 47 ][ 5 ][ 7 ]]
  [[ signed char*->unsigned char ][ !!! *<1* !!! ][ 51 ][ 5 ][ 6 ]]
  [[ signed char*->int ][ !!! *3* !!! ][ 55 ][ 12 ][ 10 ]]
  [[ signed char*->short ][ !!! *4* !!! ][ 56 ][ 12 ][ 10 ]]
  [[ signed char*->long int ][ !!! *4* !!! ][ 56 ][ 11 ][ 10 ]]
  [[ signed char*->long long ][ !!! *3* !!! ][ 55 ][ 11 ][ 10 ]]
  [[ signed char*->unsigned int ][ !!! *3* !!! ][ 60 ][ 10 ][ 10 ]]
  [[ signed char*->unsigned short ][ !!! *3* !!! ][ 53 ][ 10 ][ 10 ]]
  [[ signed char*->unsigned long int ][ !!! *3* !!! ][ 54 ][ 11 ][ 10 ]]
  [[ signed char*->unsigned long long ][ !!! *6* !!! ][ 58 ][ 11 ][ 10 ]]
  [[ signed char*->float ][ 47 ][ 76 ][ 32 ][ !!! *21* !!! ]]
  [[ signed char*->double ][ 44 ][ 73 ][ 29 ][ !!! *21* !!! ]]
  [[ signed char*->long double ][ 45 ][ 74 ][ 31 ][ !!! *24* !!! ]]
  [[ signed char*->array<char, 50> ][ !!! *<1* !!! ][ 52 ][ 11 ][ 8 ]]
  [[ signed char*->string ][ !!! *1* !!! ][ 58 ][ 11 ][ --- ]]
  [[ signed char*->container::string ][ !!! *<1* !!! ][ 59 ][ 11 ][ --- ]]
  [[ iterator_range<char*>->char ][ !!! *<1* !!! ][ 47 ][ 5 ][ 5 ]]
  [[ iterator_range<char*>->signed char ][ !!! *<1* !!! ][ 47 ][ 5 ][ 7 ]]
  [[ iterator_range<char*>->unsigned char ][ !!! *<1* !!! ][ 53 ][ 5 ][ 6 ]]
  [[ iterator_range<char*>->int ][ !!! *3* !!! ][ 58 ][ 15 ][ 10 ]]
  [[ iterator_range<char*>->short ][ !!! *3* !!! ][ 59 ][ 16 ][ 10 ]]
  [[ iterator_range<char*>->long int ][ !!! *3* !!! ][ 58 ][ 15 ][ 10 ]]
  [[ iterator_range<char*>->long long ][ !!! *3* !!! ][ 59 ][ 15 ][ 10 ]]
  [[ iterator_range<char*>->unsigned int ][ !!! *3* !!! ][ 56 ][ 14 ][ 10 ]]
  [[ iterator_range<char*>->unsigned short ][ !!! *3* !!! ][ 61 ][ 14 ][ 10 ]]
  [[ iterator_range<char*>->unsigned long int ][ !!! *3* !!! ][ 58 ][ 14 ][ 10 ]]
  [[ iterator_range<char*>->unsigned long long ][ !!! *3* !!! ][ 71 ][ 36 ][ 25 ]]
  [[ iterator_range<char*>->float ][ 116 ][ 93 ][ 39 ][ !!! *21* !!! ]]
  [[ iterator_range<char*>->double ][ 43 ][ 81 ][ 34 ][ !!! *21* !!! ]]
  [[ iterator_range<char*>->long double ][ 44 ][ 87 ][ 37 ][ !!! *24* !!! ]]
  [[ iterator_range<char*>->array<char, 50> ][ !!! *<1* !!! ][ 58 ][ 17 ][ 8 ]]
  [[ iterator_range<char*>->string ][ !!! *1* !!! ][ 63 ][ 20 ][ --- ]]
  [[ iterator_range<char*>->container::string ][ !!! *<1* !!! ][ 65 ][ 20 ][ --- ]]
  [[ std::string_view->char ][ !!! *<1* !!! ][ 46 ][ 5 ][ 5 ]]
  [[ std::string_view->signed char ][ !!! *<1* !!! ][ 47 ][ 5 ][ 7 ]]
  [[ std::string_view->unsigned char ][ !!! *<1* !!! ][ 47 ][ 5 ][ 7 ]]
  [[ std::string_view->int ][ !!! *4* !!! ][ 56 ][ 11 ][ 10 ]]
  [[ std::string_view->short ][ !!! *3* !!! ][ 55 ][ 12 ][ 10 ]]
  [[ std::string_view->long int ][ !!! *4* !!! ][ 54 ][ 11 ][ 10 ]]
  [[ std::string_view->long long ][ !!! *4* !!! ][ 54 ][ 11 ][ 10 ]]
  [[ std::string_view->unsigned int ][ !!! *3* !!! ][ 53 ][ 10 ][ 10 ]]
  [[ std::string_view->unsigned short ][ !!! *3* !!! ][ 54 ][ 10 ][ 10 ]]
  [[ std::string_view->unsigned long int ][ !!! *3* !!! ][ 54 ][ 11 ][ 10 ]]
  [[ std::string_view->unsigned long long ][ !!! *3* !!! ][ 55 ][ 11 ][ 10 ]]
  [[ std::string_view->float ][ 47 ][ 76 ][ 32 ][ !!! *22* !!! ]]
  [[ std::string_view->double ][ 43 ][ 74 ][ 28 ][ !!! *21* !!! ]]
  [[ std::string_view->long double ][ 46 ][ 75 ][ 31 ][ !!! *24* !!! ]]
  [[ std::string_view->array<char, 50> ][ !!! *1* !!! ][ 51 ][ 10 ][ 8 ]]
  [[ std::string_view->string ][ !!! *2* !!! ][ 53 ][ 10 ][ --- ]]
  [[ std::string_view->container::string ][ !!! *1* !!! ][ 56 ][ 11 ][ --- ]]
  [[ array<char, 50>->char ][ !!! *<1* !!! ][ 47 ][ 5 ][ 5 ]]
  [[ array<char, 50>->signed char ][ !!! *<1* !!! ][ 47 ][ 5 ][ 6 ]]
  [[ array<char, 50>->unsigned char ][ !!! *<1* !!! ][ 49 ][ 5 ][ 6 ]]
  [[ array<char, 50>->int ][ !!! *3* !!! ][ 54 ][ 12 ][ 10 ]]
  [[ array<char, 50>->short ][ !!! *3* !!! ][ 61 ][ 11 ][ 10 ]]
  [[ array<char, 50>->long int ][ !!! *3* !!! ][ 55 ][ 11 ][ 10 ]]
  [[ array<char, 50>->long long ][ !!! *3* !!! ][ 54 ][ 11 ][ 10 ]]
  [[ array<char, 50>->unsigned int ][ !!! *3* !!! ][ 53 ][ 10 ][ 10 ]]
  [[ array<char, 50>->unsigned short ][ !!! *3* !!! ][ 52 ][ 10 ][ 10 ]]
  [[ array<char, 50>->unsigned long int ][ !!! *3* !!! ][ 62 ][ 11 ][ 10 ]]
  [[ array<char, 50>->unsigned long long ][ !!! *3* !!! ][ 54 ][ 11 ][ 10 ]]
  [[ array<char, 50>->float ][ 45 ][ 75 ][ 35 ][ !!! *24* !!! ]]
  [[ array<char, 50>->double ][ 44 ][ 75 ][ 28 ][ !!! *21* !!! ]]
  [[ array<char, 50>->long double ][ 45 ][ 76 ][ 29 ][ !!! *24* !!! ]]
  [[ array<char, 50>->array<char, 50> ][ !!! *<1* !!! ][ 53 ][ 10 ][ 8 ]]
  [[ array<char, 50>->string ][ !!! *1* !!! ][ 54 ][ 11 ][ --- ]]
  [[ array<char, 50>->container::string ][ !!! *1* !!! ][ 56 ][ 12 ][ --- ]]
  [[ int->int ][ !!! *<1* !!! ][ 58 ][ 13 ][ --- ]]
  [[ float->double ][ !!! *<1* !!! ][ 116 ][ 63 ][ --- ]]
  [[ char->signed char ][ !!! *<1* !!! ][ 47 ][ 5 ][ --- ]]
]
[endsect]




[/ END of section, generated by performance measuring program ]
[endsect]

