$selectorHeight: ui.q(8);
$selectorOffset: ui.offset();

// Revolutionary TopServers with Holographic Glass Effects
.root {
  display: flex;
  flex-direction: column;

  gap: $selectorOffset;

  width: 100%;
  height: 100%;

  // Sophisticated entrance animation with 3D transform
  animation: holographicEntrance 1.4s cubic-bezier(0.16, 1, 0.3, 1);

  .selector {
    display: flex;
    align-items: stretch;

    gap: ui.offset();

    width: 100%;
    height: $selectorHeight;

    // Clean glass container without colored gradients
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%);

    backdrop-filter: blur(20px) saturate(1.4) brightness(1.1);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: ui.border-radius('xlarge');
    padding: ui.offset('medium');

    // Advanced multi-layer shadow system
    box-shadow:
      0 12px 48px rgba(0, 0, 0, 0.15),
      0 6px 24px rgba(102, 126, 234, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.3),
      inset 0 -1px 0 rgba(0, 0, 0, 0.1),
      0 0 0 1px rgba(255, 255, 255, 0.08);

    // Holographic shimmer effect
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: conic-gradient(from 0deg,
          transparent 0deg,
          rgba(255, 255, 255, 0.1) 45deg,
          transparent 90deg,
          rgba(102, 126, 234, 0.1) 135deg,
          transparent 180deg,
          rgba(240, 147, 251, 0.1) 225deg,
          transparent 270deg,
          rgba(255, 255, 255, 0.1) 315deg,
          transparent 360deg);
      animation: holographicSpin 8s linear infinite;
      z-index: 0;
    }

    .item {
      flex-shrink: 0;
      position: relative;
      display: flex;
      align-items: center;

      padding: ui.offset('large') ui.offset('xlarge');
      cursor: pointer;

      // Clean button design without colored borders
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.18) 0%, rgba(255, 255, 255, 0.08) 100%);

      backdrop-filter: blur(16px) saturate(1.3) brightness(1.05);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: ui.border-radius('large');

      // Advanced transitions with spring physics
      transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);

      // Clean shadow system without colored shadows
      box-shadow:
        inset 0 1px 0 rgba(255, 255, 255, 0.4),
        inset 0 -1px 0 rgba(0, 0, 0, 0.1),
        0 6px 24px rgba(0, 0, 0, 0.12),
        0 0 0 1px rgba(255, 255, 255, 0.1);

      overflow: hidden;
      z-index: 1;

      // Holographic sweep effect
      &::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -150%;
        width: 100%;
        height: 200%;
        background: linear-gradient(45deg,
            transparent 30%,
            rgba(255, 255, 255, 0.3) 50%,
            transparent 70%);
        transform: rotate(45deg);
        transition: left 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        z-index: 0;
      }

      // Prismatic light effect
      &::after {
        content: '';
        position: absolute;
        inset: 0;
        background: linear-gradient(45deg,
            rgba(255, 0, 150, 0.1) 0%,
            rgba(0, 255, 255, 0.1) 25%,
            rgba(255, 255, 0, 0.1) 50%,
            rgba(255, 0, 255, 0.1) 75%,
            rgba(0, 255, 0, 0.1) 100%);
        opacity: 0;
        transition: opacity 0.4s ease;
        z-index: 0;
      }


      &:first-child {
        flex-grow: 1;
        flex-shrink: 1;

        padding: ui.offset();

        .title {
          display: flex;
          width: auto;

          margin-left: ui.offset();

          opacity: .5;
        }

        &.active {
          .title {
            opacity: 1;
          }
        }
      }

      &:hover,
      &.active {
        .icon {
          opacity: 1;
        }
      }

      &:hover {
        // Clean hover effect without colored borders
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.12) 100%);

        border: 1px solid rgba(255, 255, 255, 0.3);

        box-shadow:
          inset 0 1px 0 rgba(255, 255, 255, 0.5),
          inset 0 -1px 0 rgba(0, 0, 0, 0.1),
          0 12px 48px rgba(0, 0, 0, 0.2);

        transform: translateY(-4px) scale(1.02);

        &::before {
          left: 150%;
        }

        &::after {
          opacity: 0.6;
        }

        .icon {
          transform: scale(1.2) rotate(10deg);
          filter: drop-shadow(0 0 8px rgba(102, 126, 234, 0.6));
        }
      }

      &.active {
        // Clean active state without colored borders
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.15) 100%);

        border: 1px solid rgba(255, 255, 255, 0.4);

        box-shadow:
          inset 0 1px 0 rgba(255, 255, 255, 0.6),
          inset 0 -1px 0 rgba(0, 0, 0, 0.2),
          0 16px 64px rgba(0, 0, 0, 0.2);

        transform-origin: center;
        animation: holographicPulse 2s ease-in-out infinite;

        &::after {
          opacity: 0.8;
          animation: prismaticShift 3s ease-in-out infinite;
        }

        .progress {
          opacity: 1;
          background: linear-gradient(90deg,
              rgba(102, 126, 234, 0.8),
              rgba(240, 147, 251, 0.8),
              rgba(168, 85, 247, 0.8));
        }

        .icon {
          transform: scale(1.15) rotate(5deg);
          filter: drop-shadow(0 0 12px rgba(102, 126, 234, 0.8));
          animation: iconGlow 2s ease-in-out infinite;
        }
      }

      .progress {
        position: absolute;
        inset: 0;

        opacity: 0;

        z-index: 0;

        &::after {
          display: block;
          content: '';

          height: 100%;
          width: 100%;

          background-image: linear-gradient(90deg, transparent, ui.color-token('carousel-selector-item-progress-background'));

          transform: translateX(calc(-100% + ui.use('progress', 0%)));
        }
      }

      .icon {
        height: ui.q(4);

        opacity: 1;

        z-index: 1;
      }

      @include ui.animated('all', '.title');

      .title {
        display: none;
        width: 0px;
        flex-grow: 1;
        overflow: hidden;
      }
    }
  }

  .cardHolder {
    position: relative;

    height: calc(100% - ($selectorHeight + $selectorOffset));
    width: 100%;

    // Enhanced glass morphism container
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.06) 0%,
        rgba(255, 255, 255, 0.02) 100%);
    backdrop-filter: blur(16px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: ui.border-radius('large');

    // Enhanced shadow for depth
    box-shadow:
      0 16px 64px rgba(0, 0, 0, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);

    overflow: hidden;
  }

  .card {
    position: absolute;

    width: 100%;
    height: 100%;

    @include ui.border-radius();
    overflow: hidden;

    cursor: pointer;

    opacity: 0;
    pointer-events: none;

    @keyframes disappr {
      from {
        opacity: 1;
      }

      to {
        opacity: 0;
      }
    }

    animation: disappr .5s cubic-bezier(0.69, 0, 0.83, 0.83);

    &.active {
      opacity: 1;
      pointer-events: all;

      z-index: 1;

      @keyframes appr {
        from {
          opacity: 0;
          transform: scale(0.95);
        }

        to {
          opacity: 1;
          transform: scale(1);
        }
      }

      animation: appr .3s cubic-bezier(0.69, 0, 0.83, 0.83);

      .icon {
        @keyframes iconAppr {
          from {
            opacity: 0;
          }

          to {
            opacity: 1;
          }
        }

        animation: iconAppr 1s cubic-bezier(0.17, 0.17, 0.23, 1);
      }
    }

    .background {
      position: absolute;
      inset: 0;

      $shader: ui.color-token('backdrop-300');
      background-image: ui.use('checkered-pattern'), linear-gradient($shader, $shader), ui.use('banner');
      background-size: 2px, cover, cover;
      background-position: top left, center center, center center;

      @include ui.border-radius();
    }

    .hoverDecoration {
      position: absolute;
      inset: 0;

      opacity: 0;

      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

      // Enhanced gradient with multiple layers
      background:
        radial-gradient(circle at top right, rgba(102, 126, 234, 0.15) 0%, transparent 50%),
        radial-gradient(circle at bottom left, rgba(240, 147, 251, 0.12) 0%, transparent 50%),
        linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, transparent 100%);

      // Animated shimmer effect
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg,
            transparent,
            rgba(255, 255, 255, 0.1),
            transparent);
        transition: left 0.8s ease;
      }
    }

    &:hover .hoverDecoration {
      opacity: 1;

      &::before {
        left: 100%;
      }
    }

    .content {
      position: absolute;
      inset: 0;

      padding: ui.offset('xlarge') calc(ui.offset('xlarge') + ui.offset('normal'));

      @include ui.border-radius();

      .description {
        width: 33%;
        height: 100%;

        -webkit-mask: linear-gradient(180deg, red, red calc(100% - ui.q(4)), transparent);
        mask: linear-gradient(180deg, red, red calc(100% - ui.q(4)), transparent);

        overflow: hidden;
      }
    }
  }
}

// Enhanced keyframe animations
@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(20px);
  }

  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes activeScale {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }

  100% {
    transform: scale(1);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }

  100% {
    transform: translateX(100%);
  }
}

// Revolutionary holographic animations
@keyframes holographicSpin {
  0% {
    transform: rotate(0deg) scale(1);
    opacity: 0.4;
  }

  25% {
    transform: rotate(90deg) scale(1.05);
    opacity: 0.6;
  }

  50% {
    transform: rotate(180deg) scale(1.1);
    opacity: 0.8;
  }

  75% {
    transform: rotate(270deg) scale(1.05);
    opacity: 0.6;
  }

  100% {
    transform: rotate(360deg) scale(1);
    opacity: 0.4;
  }
}

@keyframes holographicEntrance {
  0% {
    opacity: 0;
    transform: translateY(60px) scale(0.7) rotateX(30deg);
    filter: blur(30px) hue-rotate(0deg) saturate(0.5);
  }

  30% {
    opacity: 0.6;
    transform: translateY(20px) scale(0.9) rotateX(10deg);
    filter: blur(10px) hue-rotate(120deg) saturate(1.2);
  }

  70% {
    opacity: 0.9;
    transform: translateY(-5px) scale(1.02) rotateX(-2deg);
    filter: blur(2px) hue-rotate(240deg) saturate(1.4);
  }

  100% {
    opacity: 1;
    transform: translateY(0) scale(1) rotateX(0deg);
    filter: blur(0px) hue-rotate(360deg) saturate(1);
  }
}

@keyframes holographicPulse {

  0%,
  100% {
    transform: scale(1);
    filter: brightness(1) saturate(1) contrast(1);
  }

  25% {
    transform: scale(1.01);
    filter: brightness(1.05) saturate(1.1) contrast(1.05);
  }

  50% {
    transform: scale(1.02);
    filter: brightness(1.1) saturate(1.2) contrast(1.1);
  }

  75% {
    transform: scale(1.01);
    filter: brightness(1.05) saturate(1.1) contrast(1.05);
  }
}

@keyframes prismaticShift {
  0% {
    filter: hue-rotate(0deg) saturate(1) brightness(1);
  }

  20% {
    filter: hue-rotate(72deg) saturate(1.2) brightness(1.05);
  }

  40% {
    filter: hue-rotate(144deg) saturate(1.4) brightness(1.1);
  }

  60% {
    filter: hue-rotate(216deg) saturate(1.3) brightness(1.08);
  }

  80% {
    filter: hue-rotate(288deg) saturate(1.1) brightness(1.03);
  }

  100% {
    filter: hue-rotate(360deg) saturate(1) brightness(1);
  }
}

@keyframes iconGlow {

  0%,
  100% {
    filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.3)) brightness(1);
  }

  50% {
    filter: drop-shadow(0 0 12px rgba(255, 255, 255, 0.5)) brightness(1.1);
  }
}