.. Distributed under the Boost
.. Software License, Version 1.0. (See accompanying
.. file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

+++++++++++++++++++
 Indirect Iterator
+++++++++++++++++++

:Author: <PERSON>, <PERSON>, <PERSON>
:Contact: <EMAIL>, <EMAIL>, <EMAIL>
:organization: `Boost Consulting`_, Indiana University `Open Systems
               Lab`_, University of Hanover `Institute for Transport
               Railway Operation and Construction`_
:copyright: Copyright <PERSON>, <PERSON>, and <PERSON> 2003. 

.. _`Boost Consulting`: http://www.boost-consulting.com
.. _`Open Systems Lab`: http://www.osl.iu.edu
.. _`Institute for Transport Railway Operation and Construction`: http://www.ive.uni-hannover.de

:abstract:

  .. include:: indirect_iterator_abstract.rst

.. contents:: Table of Contents

``indirect_iterator`` synopsis
..............................

.. include:: indirect_iterator_ref.rst
.. include:: indirect_iterator_eg.rst

.. _iterator-category: iterator_facade.html#iterator-category
.. |iterator-category| replace:: *iterator-category*
