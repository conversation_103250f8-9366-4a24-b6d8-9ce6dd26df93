# Copyright 2014 The Chromium Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//third_party/protobuf/proto_library.gni")

static_library("test_support") {
  testonly = true
  sources = [
    "mojo_test_base.cc",
    "mojo_test_base.h",
    "test_utils.h",
    "test_utils_win.cc",
  ]

  if (!is_ios) {
    sources += [
      "multiprocess_test_helper.cc",
      "multiprocess_test_helper.h",
    ]
  }

  if (is_fuchsia || is_posix) {
    sources += [ "test_utils.cc" ]
  }

  public_deps = [
    "//base",
    "//base/test:test_support",
    "//mojo/core/embedder",
    "//mojo/public/cpp/system",
    "//testing/gtest",
  ]
}

source_set("run_all_unittests") {
  testonly = true
  sources = [ "run_all_unittests.cc" ]

  deps = [
    ":test_support",
    ":test_support_impl",
    "//base",
    "//base/test:test_support",
    "//mojo/core/embedder",
    "//mojo/public/c/test_support",
    "//testing/gtest",
  ]

  if (is_linux && !is_component_build) {
    public_configs = [ "//build/config/gcc:rpath_for_built_shared_libraries" ]
  }
}

source_set("run_all_perftests") {
  testonly = true
  deps = [
    ":test_support_impl",
    "//base",
    "//base/test:test_support",
    "//mojo/core/embedder",
    "//mojo/core/test:test_support",
    "//mojo/public/c/test_support",
  ]

  sources = [ "run_all_perftests.cc" ]

  if (is_linux && !is_component_build) {
    public_configs = [ "//build/config/gcc:rpath_for_built_shared_libraries" ]
  }
}

static_library("test_support_impl") {
  testonly = true
  deps = [
    "//base",
    "//base/test:test_support",
    "//mojo/public/c/test_support",
    "//mojo/public/cpp/system",
  ]

  sources = [
    "test_support_impl.cc",
    "test_support_impl.h",
  ]
}

if (is_mac) {
  proto_library("channel_mac_proto") {
    proto_in_dir = "//"
    sources = [ "data/channel_mac/channel_mac.proto" ]
    link_deps = [ "//testing/libfuzzer/fuzzers/mach:proto" ]
  }
}
