import { injectable } from 'inversify';
import { makeAutoObservable, action } from 'mobx';

import { ServicesContainer, defineService, useService } from 'cfx/base/servicesContainer';
import { getVietnameseErrorMessage, parseServerError } from './errorMessages';

export const ICustomAuthService = defineService<CustomAuthService>('customAuthService');

export function registerCustomAuthService(container: ServicesContainer) {
  container.registerImpl(ICustomAuthService, CustomAuthService);
}

export function useCustomAuthService() {
  return useService(ICustomAuthService);
}

export enum AuthStep {
  REGISTER = 'register',
  VERIFY = 'verify',
  LOGIN = 'login',
  AUTHENTICATED = 'authenticated',
}

export interface AuthUser {
  id?: string | number;
  username: string;
  email: string;
  isVerified?: boolean;
  createdAt?: string;
  // Additional optional properties for flexibility
  verified?: boolean; // Alternative to isVerified
  verifiedAt?: string;
}

export interface ApiResponse<T = any> {
  success?: boolean;
  message?: string;
  data?: T;
  // Optional properties for flexible response structures
  token?: string;
  tokenType?: string;
  expiresIn?: string;
  user?: AuthUser;
  loginInfo?: any;
  username?: string;
  email?: string;
  id?: string | number;
  isVerified?: boolean;
  createdAt?: string;
  // Error response properties
  code?: string;
  details?: any;
  errors?: any;
}

export interface RateLimitInfo {
  attempts: number;
  lastAttempt: number;
  isLocked: boolean;
  lockUntil?: number;
}

const LS_KEY = 'customAuthData';
const RATE_LIMIT_KEY = 'authRateLimit';
const BASE_URL = 'https://jason.io.vn';

// Type guards for safe property access
function isValidUser(obj: any): obj is AuthUser {
  return obj &&

    typeof obj === 'object' &&
    typeof obj.username === 'string' &&
    typeof obj.email === 'string';
}

function hasLoginData(obj: any): obj is { token?: string; user?: AuthUser } {
  return obj && typeof obj === 'object';
}

// Rate limiting constants
const REGISTER_RATE_LIMIT = { maxAttempts: 5, windowMs: 15 * 60 * 1000 }; // 5 attempts per 15 minutes
const LOGIN_RATE_LIMIT = { maxAttempts: 10, windowMs: 15 * 60 * 1000 }; // 10 attempts per 15 minutes
const ACCOUNT_LOCKOUT = { maxAttempts: 5, lockDurationMs: 30 * 60 * 1000 }; // Lock for 30 minutes after 5 failed attempts

@injectable()
export class CustomAuthService {
  private _currentStep: AuthStep = AuthStep.REGISTER;
  private _user: AuthUser | null = null;
  private _isLoading = false;
  private _error: string | null = null;
  private _pendingEmail: string | null = null;
  private _isAuthenticated = false;

  public get currentStep(): AuthStep {
    return this._currentStep;
  }

  public get user(): AuthUser | null {
    return this._user;
  }

  public get isLoading(): boolean {
    return this._isLoading;
  }

  public get error(): string | null {
    return this._error;
  }

  public get pendingEmail(): string | null {
    return this._pendingEmail;
  }

  public get isAuthenticated(): boolean {
    return this._isAuthenticated;
  }

  constructor() {
    makeAutoObservable(this);
    this.loadStoredAuth();
  }

  private loadStoredAuth() {
    try {
      const stored = localStorage.getItem(LS_KEY);

      if (stored) {
        const data = JSON.parse(stored);

        if (data.user && data.isAuthenticated && data.timestamp) {
          // Check if stored auth is not too old (7 days)
          const maxAge = 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds
          const age = Date.now() - data.timestamp;

          if (age < maxAge) {
            // Handle nested user object structure from API response
            let userData = data.user;

            if (userData.user) {
              // If there's a nested user object, use that
              userData = userData.user;
            }

            // Ensure we have required fields
            if (userData.username && userData.email) {
              // Use dedicated method to set authenticated state
              this.setAuthenticatedState(userData);
            } else {
              localStorage.removeItem(LS_KEY);
            }
          } else {
            // Clear expired auth
            localStorage.removeItem(LS_KEY);
          }
        }
      }
    } catch (e) {
      // Clear corrupted data
      try {
        localStorage.removeItem(LS_KEY);
      } catch (clearError) {
        // Ignore clear errors
      }
    }
  }

  private saveAuth() {
    try {
      const data = {
        user: this._user,
        isAuthenticated: this._isAuthenticated,
        timestamp: Date.now(),
      };
      localStorage.setItem(LS_KEY, JSON.stringify(data));
    } catch (e) {
      console.warn('Failed to save auth data:', e);
    }
  }

  private getRateLimitInfo(type: 'register' | 'login'): RateLimitInfo {
    try {
      const stored = localStorage.getItem(`${RATE_LIMIT_KEY}_${type}`);
      if (stored) {
        return JSON.parse(stored);
      }
    } catch (e) {
      console.warn('Failed to load rate limit info:', e);
    }
    return { attempts: 0, lastAttempt: 0, isLocked: false };
  }

  private updateRateLimitInfo(type: 'register' | 'login', info: RateLimitInfo) {
    try {
      localStorage.setItem(`${RATE_LIMIT_KEY}_${type}`, JSON.stringify(info));
    } catch (e) {
      console.warn('Failed to save rate limit info:', e);
    }
  }

  private checkRateLimit(type: 'register' | 'login'): boolean {
    const info = this.getRateLimitInfo(type);
    const now = Date.now();
    const config = type === 'register' ? REGISTER_RATE_LIMIT : LOGIN_RATE_LIMIT;

    // Check if account is locked
    if (info.isLocked && info.lockUntil && now < info.lockUntil) {
      const remainingMs = info.lockUntil - now;
      const remainingMin = Math.ceil(remainingMs / (60 * 1000));
      const originalError = `Account temporarily locked. Try again in ${remainingMin} minutes.`;
      const vietnameseError = getVietnameseErrorMessage(originalError);
      throw new Error(vietnameseError);
    }

    // Reset if window has passed
    if (now - info.lastAttempt > config.windowMs) {
      info.attempts = 0;
      info.isLocked = false;
      info.lockUntil = undefined;
    }

    // Check rate limit
    if (info.attempts >= config.maxAttempts) {
      if (type === 'login' && info.attempts >= ACCOUNT_LOCKOUT.maxAttempts) {
        info.isLocked = true;
        info.lockUntil = now + ACCOUNT_LOCKOUT.lockDurationMs;
        this.updateRateLimitInfo(type, info);
        const lockMin = Math.ceil(ACCOUNT_LOCKOUT.lockDurationMs / (60 * 1000));
        const originalError = `Too many failed attempts. Account locked for ${lockMin} minutes.`;
        const vietnameseError = getVietnameseErrorMessage(originalError);
        throw new Error(vietnameseError);
      }
      const originalError = 'Rate limit exceeded. Try again later.';
      const vietnameseError = getVietnameseErrorMessage(originalError);
      throw new Error(vietnameseError);
    }

    return true;
  }

  private incrementRateLimit(type: 'register' | 'login', failed = false) {
    const info = this.getRateLimitInfo(type);
    const now = Date.now();

    if (failed) {
      info.attempts++;
      info.lastAttempt = now;
      this.updateRateLimitInfo(type, info);
    } else {
      // Reset on success
      info.attempts = 0;
      info.isLocked = false;
      info.lockUntil = undefined;
      this.updateRateLimitInfo(type, info);
    }
  }

  private validatePassword(password: string): string | null {
    if (password.length < 8) {
      return 'Password must be at least 8 characters long';
    }
    if (!/[a-z]/.test(password)) {
      return 'Password must contain at least one lowercase letter';
    }
    if (!/[A-Z]/.test(password)) {
      return 'Password must contain at least one uppercase letter';
    }
    if (!/\d/.test(password)) {
      return 'Password must contain at least one number';
    }
    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      return 'Password must contain at least one special character';
    }
    return null;
  }

  private async apiCall<T = any>(endpoint: string, data: any): Promise<ApiResponse<T>> {
    const url = `${BASE_URL}${endpoint}`;

    console.log(`🔍 API Call Debug:`, {
      endpoint,
      url,
      requestData: data
    });

    try {
      const response = await fetch(url, {
        method: 'POST',
        mode: 'cors', // Explicitly set CORS mode
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      console.log(`📥 Response Debug:`, {
        status: response.status,
        statusText: response.statusText,
        ok: response.ok,
        headers: Object.fromEntries(response.headers.entries())
      });

      if (!response.ok) {
        // Đọc response body để có thông tin lỗi chi tiết
        let errorData: any = null;
        let errorMessage = `HTTP ${response.status}: ${response.statusText}`;

        try {
          const errorBody = await response.text();

          // Thử parse JSON error response
          try {
            errorData = JSON.parse(errorBody);

            // Parse error theo structure mới
            const parsedError = parseServerError(errorData);

            // Tạo Vietnamese error message
            const vietnameseMessage = getVietnameseErrorMessage(parsedError.message, parsedError.code);

            // Tạo error object với thông tin đầy đủ
            const enhancedError = new Error(vietnameseMessage) as any;
            enhancedError.code = parsedError.code;
            enhancedError.details = parsedError.details;
            enhancedError.originalMessage = parsedError.message;
            enhancedError.httpStatus = response.status;

            throw enhancedError;

          } catch (parseError) {
            // Nếu không parse được JSON, dùng text response
            if (errorBody) {
              errorMessage = errorBody;
            }
          }
        } catch (readError) {
          // Fallback nếu không đọc được response
        }

        // Fallback error
        const vietnameseMessage = getVietnameseErrorMessage(errorMessage);
        throw new Error(vietnameseMessage);
      }

      const responseData = await response.json();

      console.log(`✅ Success Response Debug:`, {
        endpoint,
        responseData: JSON.stringify(responseData, null, 2)
      });

      return responseData;

    } catch (error) {
      console.error('❌ API Call Error:', error);

      // Phân loại lỗi để đưa ra thông báo phù hợp
      if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
        console.log('🚨 Network/CORS Error detected');
        console.log('💡 Server có duplicate CORS headers - cần fix ở server backend');

        throw new Error('Lỗi kết nối: Server có vấn đề với CORS headers (duplicate headers). Vui lòng liên hệ admin để fix server.');
      }

      if (error instanceof TypeError && error.message.includes('fetch')) {
        throw new Error('Không thể kết nối đến server. Vui lòng kiểm tra kết nối mạng.');
      }

      // Re-throw error gốc nếu không phải network error
      throw error;
    }
  }

  public readonly checkHealth = async (): Promise<boolean> => {
    console.log('🏥 Đang kiểm tra health check...');
    console.log(`🔗 Health check URL: ${BASE_URL}/health`);

    try {
      const response = await fetch(`${BASE_URL}/health`, {
        method: 'GET',
        mode: 'cors', // Explicitly set CORS mode
        headers: {
          'Content-Type': 'application/json',
        },
      });

      console.log(`📥 Health check response status: ${response.status}`);
      console.log(`📥 Health check response ok: ${response.ok}`);

      if (response.ok) {
        console.log('✅ Health check thành công');
        return true;
      } else {
        console.log('❌ Health check thất bại với status:', response.status);
        return false;
      }
    } catch (e) {
      console.error('❌ Health check error:', e);

      // Check if it's a CORS error
      if (e instanceof TypeError && e.message.includes('Failed to fetch')) {
        console.log('🚨 CORS Error detected - Server backend có vấn đề với CORS configuration');
        console.log('💡 Server cần sửa Access-Control-Allow-Origin header (chỉ được có 1 giá trị)');
        console.log('💡 Hiện tại server đang trả về cả "*" và specific origin');
      }

      console.log('💡 Gợi ý: Kiểm tra server có đang chạy tại', BASE_URL);

      // For development, we can still allow the app to work
      // even if health check fails due to CORS
      console.log('⚠️ Tạm thời bỏ qua health check để app có thể hoạt động');
      return true; // Return true to allow app to continue working
    }
  };

  public readonly register = async (username: string, email: string, password: string): Promise<void> => {
    this._isLoading = true;
    this._error = null;

    try {
      this.checkRateLimit('register');
    } catch (rateLimitError) {
      this._isLoading = false;
      // Set error để hiển thị trong UI
      this._error = rateLimitError instanceof Error ? rateLimitError.message : 'Rate limit exceeded';
      throw rateLimitError;
    }

    const passwordError = this.validatePassword(password);
    if (passwordError) {
      this._isLoading = false;
      const vietnameseError = getVietnameseErrorMessage(passwordError, 'INVALID_PASSWORD');
      this._error = vietnameseError;
      throw new Error(vietnameseError);
    }

    try {
      const response = await this.apiCall('/api/auth/register', {
        username,
        email,
        password,
      });

      if (response.success) {
        this._pendingEmail = email;
        this._currentStep = AuthStep.VERIFY;
        this.incrementRateLimit('register', false);
      } else {
        this.incrementRateLimit('register', true);

        // Parse error response để lấy error code
        const parsedError = parseServerError(response);
        const vietnameseError = getVietnameseErrorMessage(parsedError.message, parsedError.code);
        this._error = vietnameseError;
        throw new Error(vietnameseError);
      }
    } catch (error: any) {
      this.incrementRateLimit('register', true);

      // Sử dụng error message đã được transform từ apiCall
      if (!this._error) {
        // Nếu error đã có Vietnamese message từ apiCall, sử dụng luôn
        if (error.message && error.code) {
          this._error = error.message;
        } else {
          // Fallback transformation
          const originalMessage = error instanceof Error ? error.message : 'Registration failed';
          this._error = getVietnameseErrorMessage(originalMessage);
        }
      }

      // Re-throw with Vietnamese message
      throw new Error(this._error || 'Đăng ký thất bại');
    } finally {
      this._isLoading = false;
    }
  };

  public readonly verify = async (email: string, code: string): Promise<void> => {
    this._isLoading = true;
    this._error = null;

    try {
      const response = await this.apiCall('/api/auth/verify', {
        email,
        code,
      });

      if (response.success) {
        this._currentStep = AuthStep.LOGIN;
        this._pendingEmail = null;
      } else {
        // Parse error response để lấy error code
        const parsedError = parseServerError(response);
        const vietnameseError = getVietnameseErrorMessage(parsedError.message, parsedError.code);
        this._error = vietnameseError;
        throw new Error(vietnameseError);
      }
    } catch (error: any) {
      // Sử dụng error message đã được transform từ apiCall
      if (!this._error) {
        // Nếu error đã có Vietnamese message từ apiCall, sử dụng luôn
        if (error.message && error.code) {
          console.log('🔍 Using enhanced error from apiCall:', error);
          this._error = error.message;
        } else {
          // Fallback transformation
          const originalMessage = error instanceof Error ? error.message : 'Verification failed';
          this._error = getVietnameseErrorMessage(originalMessage, 'INVALID_CODE');
        }
      }

      // Re-throw with Vietnamese message
      throw new Error(this._error || 'Xác thực thất bại');
    } finally {
      this._isLoading = false;
    }
  };

  public readonly login = async (email: string, password: string): Promise<void> => {
    // Use action to set loading state
    action(() => {
      this._isLoading = true;
      this._error = null;
    })();

    try {
      this.checkRateLimit('login');
    } catch (rateLimitError) {
      // Use action to set error state
      action(() => {
        this._isLoading = false;
        this._error = rateLimitError instanceof Error ? rateLimitError.message : 'Rate limit exceeded';
      })();
      throw rateLimitError;
    }

    try {
      const response = await this.apiCall('/api/auth/login', {
        email,
        password,
      });

      console.log(`🔍 Login Response Analysis:`, {
        hasResponse: !!response,
        responseKeys: response ? Object.keys(response) : [],
        success: response?.success,
        hasData: !!response?.data,
        dataKeys: response?.data ? Object.keys(response.data) : [],
        fullResponse: JSON.stringify(response, null, 2)
      });

      // Check for different possible success response structures
      if (response.success) {
        console.log(`✅ Server returned success=true, analyzing data structure...`);

        let loginData = response.data;
        let token, tokenType, expiresIn, user, loginInfo;

        // Case 1: Standard structure with nested data
        if (hasLoginData(response.data)) {
          const dataObj = response.data as any;
          token = dataObj.token;
          tokenType = dataObj.tokenType;
          expiresIn = dataObj.expiresIn;
          user = dataObj.user;
          loginInfo = dataObj.loginInfo;
          console.log(`📋 Case 1 - Standard nested structure:`, { token: !!token, user: !!user });
        }

        // Case 2: Flat structure (data fields directly in response)
        if (!token && !user && response.token) {
          token = response.token;
          tokenType = response.tokenType;
          expiresIn = response.expiresIn;
          user = response.user;
          loginInfo = response.loginInfo;
          console.log(`📋 Case 2 - Flat structure:`, { token: !!token, user: !!user });
        }

        // Case 3: User data directly in response.data (legacy)
        if (!user && isValidUser(response.data)) {
          user = response.data;
          console.log(`📋 Case 3 - User data directly in response.data:`, { user: !!user });
        }

        console.log(`🔍 Final extracted data:`, {
          hasToken: !!token,
          hasUser: !!user,
          userStructure: user ? Object.keys(user) : [],
          tokenType,
          expiresIn
        });

        // Validate required fields using type guard
        if (!user || !isValidUser(user)) {
          console.log(`❌ No valid user data found in any structure`);
          throw new Error('Invalid login response: missing or invalid user data');
        }

        // Store token info if available
        if (typeof window !== 'undefined') {
          if (token) {
            localStorage.setItem('auth_token', token);
            localStorage.setItem('auth_token_type', tokenType || 'Bearer');
            localStorage.setItem('auth_expires_in', expiresIn || '24h');
            console.log(`💾 Token stored successfully`);
          } else {
            console.log(`⚠️ No token provided, skipping token storage`);
          }

          if (loginInfo) {
            localStorage.setItem('auth_login_info', JSON.stringify(loginInfo));
          }
        }

        console.log(`🔄 Setting authenticated state with user:`, user);

        // Update authentication state with proper MobX action
        this.setAuthenticatedState(user);

        this.saveAuth();
        this.incrementRateLimit('login', false);

        console.log(`✅ Login process completed successfully`);

      } else if (response.success === false) {
        console.log(`❌ Login Response Failed - success=false:`, {
          success: response?.success,
          hasData: !!response?.data,
          message: response?.message,
          fullResponse: JSON.stringify(response, null, 2)
        });

        this.incrementRateLimit('login', true);

        // Parse error response để lấy error code
        const parsedError = parseServerError(response);
        const vietnameseError = getVietnameseErrorMessage(parsedError.message, parsedError.code);

        // Use action to set error state
        action(() => {
          this._error = vietnameseError;
        })();

        throw new Error(vietnameseError);

      } else {
        // Case when response doesn't have success field - might be successful but different format
        console.log(`⚠️ Response without success field - checking for user data:`, {
          hasResponse: !!response,
          responseKeys: response ? Object.keys(response) : [],
          hasUser: !!(response.user || response.username),
          fullResponse: JSON.stringify(response, null, 2)
        });

        // Try to extract user data from response directly
        let user = response.user || (isValidUser(response) ? response : null);

        if (user && isValidUser(user)) {
          console.log(`✅ Found user data in response without success field:`, user);

          // Update authentication state
          this.setAuthenticatedState(user);
          this.saveAuth();
          this.incrementRateLimit('login', false);

          console.log(`✅ Login completed with alternative response format`);
        } else {
          console.log(`❌ No valid user data found in response`);
          this.incrementRateLimit('login', true);

          // Use action to set error state
          const errorMessage = 'Email hoặc mật khẩu không đúng. Vui lòng kiểm tra lại thông tin đăng nhập.';
          action(() => {
            this._error = errorMessage;
          })();

          throw new Error(errorMessage);
        }
      }
    } catch (error: any) {
      this.incrementRateLimit('login', true);

      // Use action to set error state
      action(() => {
        // Sử dụng error message đã được transform từ apiCall
        if (!this._error) {
          // Nếu error đã có Vietnamese message từ apiCall, sử dụng luôn
          if (error.message && error.code) {
            this._error = error.message;
          } else {
            // Fallback transformation
            const originalMessage = error instanceof Error ? error.message : 'Login failed';
            this._error = getVietnameseErrorMessage(originalMessage, 'INVALID_CREDENTIALS');
          }
        }
      })();

      // Re-throw with Vietnamese message
      throw new Error(this._error || 'Đăng nhập thất bại');
    } finally {
      // Use action to set loading state
      action(() => {
        this._isLoading = false;
      })();
    }
  };

  public readonly logout = (): void => {
    // Use action to update observables
    action(() => {
      this._user = null;
      this._isAuthenticated = false;
      this._currentStep = AuthStep.LOGIN;
      this._error = null;
      this._pendingEmail = null;
    })();

    try {
      localStorage.removeItem(LS_KEY);
      localStorage.removeItem('auth_token');
      localStorage.removeItem('auth_token_type');
      localStorage.removeItem('auth_expires_in');
      localStorage.removeItem('auth_login_info');
    } catch (e) {
      // Ignore localStorage errors
    }
  };

  public readonly setStep = (step: AuthStep): void => {
    action(() => {
      this._currentStep = step;
    })();
  };

  public readonly clearError = (): void => {
    action(() => {
      this._error = null;
    })();
  };

  // Dedicated method to set authenticated state with proper MobX action
  private readonly setAuthenticatedState = action((user: AuthUser): void => {
    console.log('🔄 Setting authenticated state:', { user });

    this._user = user;
    this._isAuthenticated = true;
    this._currentStep = AuthStep.AUTHENTICATED;
    this._error = null;
    this._pendingEmail = null;

    console.log('✅ Authentication state updated:', {
      isAuthenticated: this._isAuthenticated,
      currentStep: this._currentStep,
      hasUser: !!this._user
    });

    // Force observable notification and trigger navigation
    setTimeout(() => {
      console.log('🔔 Triggering delayed observable update and navigation');
      this._isAuthenticated = this._isAuthenticated;

      // Trigger a custom event for navigation
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('authStateChanged', {
          detail: { isAuthenticated: this._isAuthenticated, user: this._user }
        }));
      }
    }, 100); // Slightly longer delay to ensure all state is settled
  });

  // Debug method to force reload auth from localStorage
  public readonly debugReloadAuth = (): void => {
    console.log('=== DEBUG: Force reloading auth ===');
    this.loadStoredAuth();
    console.log('=== DEBUG: After reload ===', {
      isAuthenticated: this._isAuthenticated,
      user: this._user,
      currentStep: this._currentStep
    });
  };

  // Method to manually trigger observable update
  public readonly forceUpdate = (): void => {
    console.log('Force triggering observable update');
    // Touch observables to trigger update
    this._isAuthenticated = this._isAuthenticated;
    this._user = this._user;
  };




}
