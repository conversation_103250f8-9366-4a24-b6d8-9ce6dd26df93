import { observer } from 'mobx-react-lite';
import React from 'react';
import { Outlet } from 'react-router-dom';

import { AuthModal } from 'cfx/apps/mpMenu/parts/AuthModal/AuthModal';
import { CustomAuth } from 'cfx/apps/mpMenu/parts/CustomAuth/CustomAuth';
import { LegacyConnectingModal } from 'cfx/apps/mpMenu/parts/LegacyConnectingModal/LegacyConnectingModal';
import { LegacyUiMessageModal } from 'cfx/apps/mpMenu/parts/LegacyUiMessageModal/LegacyUiMessageModal';
import { LegalAccepter } from 'cfx/apps/mpMenu/parts/LegalAccepter/LegalAccepter';
import { NavBar } from 'cfx/apps/mpMenu/parts/NavBar/NavBar';
import { ServerBoostModal } from 'cfx/apps/mpMenu/parts/ServerBoostModal/ServerBoostModal';
import { SettingsFlyout } from 'cfx/apps/mpMenu/parts/SettingsFlyout/SettingsFlyout';
import { ThemeManager } from 'cfx/apps/mpMenu/parts/ThemeManager/ThemeManager';
import { TopNavigation } from 'cfx/apps/mpMenu/parts/TopNavigation/TopNavigation';
import { useCustomAuthService } from 'cfx/apps/mpMenu/services/customAuth/customAuth.service';
import { useLegalService } from 'cfx/apps/mpMenu/services/legal/legal.service';

import { NavigationTracker } from './PageViewTracker';
import { AcitivityItemMediaViewerProvider } from '../AcitivityItemMediaViewer/AcitivityItemMediaViewer.context';

import s from './MpMenuApp.module.scss';

function MpMenuUI() {
  const customAuthService = useCustomAuthService();

  return (
    <>
      <NavigationTracker />

      <AuthModal />
      <SettingsFlyout />

      <LegacyConnectingModal />
      <LegacyUiMessageModal />

      <ServerBoostModal />

      {/* Hide Top Navigation completely - clean interface */}
      {/* {customAuthService.isAuthenticated && <TopNavigation />} */}

      <div className={s.root}>
        {/* Bottom NavBar hidden for cleaner interface */}
        {/* <NavBar /> */}

        <div className={s.outlet}>
          <Outlet />
        </div>
      </div>
    </>
  );
}

export const MpMenuApp = observer(function MpMenuApp() {
  const legalService = useLegalService();
  const customAuthService = useCustomAuthService();

  // Enhanced debug logging to understand current flow
  React.useEffect(() => {
    console.log('🏠 MpMenuApp Navigation Debug:', {
      hasUserAccepted: legalService.hasUserAccepted,
      shouldShowAuth: legalService.shouldShowAuth,
      isAuthenticated: customAuthService.isAuthenticated,
      currentStep: customAuthService.currentStep,
      user: customAuthService.user,
      userEmail: customAuthService.user?.email
    });

    // Make services available globally for debugging
    if (typeof window !== 'undefined') {
      (window as any).cfxDebug = {
        legalService,
        customAuthService,
        triggerCustomAuth: () => legalService.forceShowAuth(),
        clearAuth: () => {
          localStorage.removeItem('customAuthData');
          localStorage.removeItem('legalAcceptanceData');
          location.reload();
        },
        checkLocalStorage: () => {
          console.log('=== LocalStorage Debug ===');
          console.log('customAuthData:', localStorage.getItem('customAuthData'));
          console.log('legalAcceptanceData:', localStorage.getItem('legalAcceptanceData'));
        },
        forceReloadAuth: () => customAuthService.debugReloadAuth(),
        forceUpdate: () => customAuthService.forceUpdate(),
        getCurrentState: () => ({
          isAuthenticated: customAuthService.isAuthenticated,
          user: customAuthService.user,
          currentStep: customAuthService.currentStep
        })
      };
    }
  }, [legalService.hasUserAccepted, legalService.shouldShowAuth, customAuthService.isAuthenticated, customAuthService.currentStep, customAuthService.user, legalService, customAuthService]);

  // Enhanced navigation logic with better debugging
  const getMainUI = () => {
    console.log('🔍 MpMenuApp - Determining UI to show:', {
      hasUserAccepted: legalService.hasUserAccepted,
      isAuthenticated: customAuthService.isAuthenticated,
      currentStep: customAuthService.currentStep
    });

    // SKIP TERMS OF SERVICE - Always auto-accept and show main UI
    if (!legalService.hasUserAccepted) {
      console.log('📋 Auto-accepting legal terms - Skipping TOS screen');
      legalService.accept(); // Auto-accept terms
    }

    // Always show the main UI with top navigation
    // Authentication will be handled by the login modal in TopNavigation
    console.log('🏠 Showing MpMenuUI - Main interface with top navigation');
    return <MpMenuUI />;
  };

  // Force re-render when authentication state changes
  React.useEffect(() => {
    console.log('🔄 MpMenuApp - Authentication state changed, forcing re-render');
  }, [customAuthService.isAuthenticated, customAuthService.currentStep]);

  // Listen for custom auth state change events
  React.useEffect(() => {
    const handleAuthStateChange = (event: CustomEvent) => {
      console.log('🔔 MpMenuApp - Received authStateChanged event:', event.detail);
      // Force a re-render by updating a dummy state
      setForceUpdate(prev => prev + 1);
    };

    window.addEventListener('authStateChanged', handleAuthStateChange as EventListener);

    return () => {
      window.removeEventListener('authStateChanged', handleAuthStateChange as EventListener);
    };
  }, []);

  // State to force re-renders
  const [forceUpdate, setForceUpdate] = React.useState(0);

  return (
    <AcitivityItemMediaViewerProvider>
      <ThemeManager />

      {getMainUI()}
    </AcitivityItemMediaViewerProvider>
  );
});
