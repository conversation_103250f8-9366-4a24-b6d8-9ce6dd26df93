[/
 /  Copyright (c) 2002, 2003, 2005 <PERSON>
 /  Copyright (c) 2014 <PERSON>
 /
 /  Distributed under the Boost Software License, Version 1.0. (See
 /  accompanying file LICENSE_1_0.txt or copy at
 /  http://www.boost.org/LICENSE_1_0.txt)
 /]

[section:checked_delete checked_delete]

[simplesect Authors]

* <PERSON><PERSON>
* <PERSON>
* <PERSON>
* <PERSON>
* <PERSON>

[endsimplesect]

[section Overview]

The header `<boost/checked_delete.hpp>` defines two function
templates, `checked_delete` and `checked_array_delete`, and two
class templates, `checked_deleter` and `checked_array_deleter`.

The C++ Standard allows, in 5.3.5/5, pointers to incomplete
class types to be deleted with a delete-expression. When the
class has a non-trivial destructor, or a class-specific
operator delete, the behavior is undefined. Some compilers
issue a warning when an incomplete type is deleted, but 
unfortunately, not all do, and programmers sometimes ignore or
disable warnings.

A particularly troublesome case is when a smart pointer's
destructor, such as `boost::scoped_ptr<T>::~scoped_ptr`, is
instantiated with an incomplete type. This can often lead to
silent, hard to track failures.

The supplied function and class templates can be used to
prevent these problems, as they require a complete type, and
cause a compilation error otherwise.

[endsect]

[section Synopsis]

``
namespace boost
{
    template<class T> void checked_delete(T * p);
    template<class T> void checked_array_delete(T * p);
    template<class T> struct checked_deleter;
    template<class T> struct checked_array_deleter;
}
``

[endsect]

[section checked_delete]

[section template<class T> void checked_delete(T * p);]

* *Requires:* `T` must be a complete type. The expression
  `delete p` must be well-formed.
* *Effects:* `delete p;`

[endsect]

[endsect]

[section checked_array_delete]

[section template<class T> void checked_array_delete(T * p);]

* *Requires:* `T` must be a complete type. The expression
  `delete [] p` must be well-formed.
* *Effects:* `delete [] p;`

[endsect]

[endsect]

[section checked_deleter]

``
template<class T> struct checked_deleter
{
    typedef void result_type;
    typedef T * argument_type;
    void operator()(T * p) const;
};
``

[section void checked_deleter<T>::operator()(T * p) const;]

* *Requires:* `T` must be a complete type. The expression
  `delete p` must be well-formed.
* *Effects:* `delete p;`
  
[endsect]

[endsect]

[section checked_array_deleter]

``
template<class T> struct checked_array_deleter
{
    typedef void result_type;
    typedef T * argument_type;
    void operator()(T * p) const;
};
``

[section void checked_array_deleter<T>::operator()(T * p) const;]

* *Requires:* `T` must be a complete type. The expression
  `delete [] p` must be well-formed.
* *Effects:* `delete [] p;`

[endsect]

[endsect]

[section Acknowledgements]

The function templates `checked_delete` and
`checked_array_delete` were originally part of
`<boost/utility.hpp>`, and the documentation
acknowledged Beman Dawes, Dave Abrahams,
Vladimir Prus, Rainer Deyke, John Maddock, 
and others as contributors.

[endsect]

[endsect]
