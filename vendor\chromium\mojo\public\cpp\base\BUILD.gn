# Copyright 2018 The Chromium Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//mojo/public/tools/bindings/mojom.gni")

component("base") {
  output_name = "mojo_base_lib"

  sources = [
    "big_buffer.cc",
    "big_buffer.h",
    "shared_memory_utils.cc",
    "shared_memory_utils.h",
  ]

  defines = [ "IS_MOJO_BASE_IMPL" ]

  public_deps = [
    "//base",
    "//mojo/public/cpp/bindings",
    "//mojo/public/cpp/system",
  ]
}

# Normally typemap traits sources should be build directly into mojom targets
# via the typemap file. This target is for typemapped mojo_base types whose
# traits are shared between chromium and blink variants.
component("shared_typemap_traits") {
  output_name = "mojo_base_shared_typemap_traits"

  sources = [
    "big_buffer_mojom_traits.cc",
    "big_buffer_mojom_traits.h",
    "file_info_mojom_traits.cc",
    "file_info_mojom_traits.h",
    "file_mojom_traits.cc",
    "file_mojom_traits.h",
    "file_path_mojom_traits.cc",
    "file_path_mojom_traits.h",
    "generic_pending_receiver_mojom_traits.cc",
    "generic_pending_receiver_mojom_traits.h",
    "read_only_buffer_mojom_traits.cc",
    "read_only_buffer_mojom_traits.h",
    "shared_memory_mojom_traits.cc",
    "shared_memory_mojom_traits.h",
    "time_mojom_traits.cc",
    "time_mojom_traits.h",
    "token_mojom_traits.cc",
    "token_mojom_traits.h",
    "unguessable_token_mojom_traits.cc",
    "unguessable_token_mojom_traits.h",
    "values_mojom_traits.cc",
    "values_mojom_traits.h",
  ]

  defines = [ "IS_MOJO_BASE_SHARED_TRAITS_IMPL" ]

  public_deps = [
    ":base",
    "//base:i18n",
    "//mojo/public/mojom/base:base_shared",
  ]
}

source_set("tests") {
  testonly = true

  sources = [
    "big_buffer_unittest.cc",
    "big_string_unittest.cc",
    "file_path_unittest.cc",
    "file_unittest.cc",
    "memory_allocator_dump_cross_process_uid_unittest.cc",
    "process_id_unittest.cc",
    "read_only_buffer_unittest.cc",
    "ref_counted_memory_unittest.cc",
    "shared_memory_unittest.cc",
    "string16_unittest.cc",
    "text_direction_unittest.cc",
    "thread_priority_unittest.cc",
    "time_unittest.cc",
    "token_unittest.cc",
    "unguessable_token_unittest.cc",
    "values_unittest.cc",
  ]

  public_deps = [
    ":base",
    ":shared_typemap_traits",
    "//base",
    "//base/test:test_support",
    "//mojo/public/cpp/test_support:test_utils",
    "//mojo/public/mojom/base",
    "//testing/gtest",
  ]
}
