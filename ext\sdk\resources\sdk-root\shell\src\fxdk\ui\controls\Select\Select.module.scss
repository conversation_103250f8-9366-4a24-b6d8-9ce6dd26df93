@import "variables";

.root {
  display: block;

  backdrop-filter: blur($blurSize);
  background-color: rgba($bgColor, .8);

  height: $q*9;
  padding: 0 $q*4;

  color: $fgColor;

  @include fontPrimary;
  font-size: $fs1;

  border: none;

  background-color: rgba($fgColor, .1);
  box-shadow: 0 2px 0 0 rgba($fgColor, .25) inset, 0 -2px 0 0 rgba($fgColor, .25) inset;

  &.small {
    height: $q*7;
    font-size: $fs08;
  }

  option {
    font-size: $fs1;

    background-color: $bgColor;
  }
}
