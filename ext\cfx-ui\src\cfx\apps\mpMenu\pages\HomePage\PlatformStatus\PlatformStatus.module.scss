// Modern Platform Status with Glass Morphism
.service-notice {
  flex-grow: 1;
  display: flex;
  align-items: center;
  justify-content: center;

  height: ui.control-height('large');

  // Smooth entrance animation
  animation: slideInFromTop 0.6s cubic-bezier(0.16, 1, 0.3, 1);

  >* {
    visibility: hidden;
  }

  >*:first-child {
    visibility: visible;

    width: 100%;
    min-height: 100%;

    padding: ui.offset('large');

    font-weight: ui.font-weight('bold');
    font-size: 0.95rem;

    // Modern glass morphism design
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.08) 0%,
        rgba(255, 255, 255, 0.04) 100%);
    backdrop-filter: blur(12px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: ui.border-radius('medium');

    // Enhanced shadow and glow
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.12),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);

    // Smooth transitions
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    // Status-specific styling
    &:global(.warning) {
      background: linear-gradient(135deg,
          rgba(255, 193, 7, 0.15) 0%,
          rgba(255, 193, 7, 0.08) 100%);
      border-color: rgba(255, 193, 7, 0.3);
      box-shadow:
        0 8px 32px rgba(255, 193, 7, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
      color: #ffc107;
    }

    &:global(.error) {
      background: linear-gradient(135deg,
          rgba(220, 53, 69, 0.15) 0%,
          rgba(220, 53, 69, 0.08) 100%);
      border-color: rgba(220, 53, 69, 0.3);
      box-shadow:
        0 8px 32px rgba(220, 53, 69, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
      color: #dc3545;
    }

    // Success state (default)
    &:not(:global(.warning)):not(:global(.error)) {
      background: linear-gradient(135deg,
          rgba(40, 167, 69, 0.15) 0%,
          rgba(40, 167, 69, 0.08) 100%);
      border-color: rgba(40, 167, 69, 0.3);
      box-shadow:
        0 8px 32px rgba(40, 167, 69, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
      color: #28a745;
    }

    // Hover effect
    &:hover {
      transform: translateY(-2px);
      box-shadow:
        0 12px 48px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.25);
    }
  }
}

// Keyframe animations
@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }

  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}