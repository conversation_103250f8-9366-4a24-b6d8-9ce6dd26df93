@import "variables";

.root {
  display: flex;
  align-items: center;
  justify-content: center;

  width: $toolbarHeight*1.5 !important;

  // background-color: rgba($fgColor, .1);

  @include interactiveTransition;

  svg {
    font-size: $fs4 !important;
    color: rgba($fgColor, .75);
  }

  &:hover svg {
    color: rgba($fgColor, 1);
  }

  &.up {
    background-color: $scColor !important;

    svg {
      color: $bgColor !important;
    }

    &:hover {
      background-color: $erColor !important;
    }
  }

  &.down {
    &:hover {
      background-color: $scColor !important;
    }
  }

  &.error {
    background-color: $erColor !important;
  }
}
