.root {
  background-color: ui.color-token('server-review-background');

  @include ui.border-radius('normal');

  .show-on-hover {
    opacity: 0;
  }
  &:hover {
    .show-on-hover {
      opacity: 1;
    }
  }
  @include ui.animated('opacity', '.show-on-hover');

  .content {
    &, * {
      user-select: text;
    }

    :global(.emoji) {
      height: 1.5ch;
      width: auto;
    }

    img {
      max-width: 100%;
    }

    li {
      padding: ui.offset('xsmall') 0;
    }
  }
}
