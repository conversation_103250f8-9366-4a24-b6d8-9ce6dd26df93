// Copyright 2016 The Chromium Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

module IPC.mojom;

import "mojo/public/mojom/base/big_buffer.mojom";
import "mojo/public/interfaces/bindings/native_struct.mojom";

// A placeholder interface type since we don't yet support generic associated
// message pipe handles.
interface GenericInterface {};

// Typemapped such that arbitrarily large IPC::Message objects can be sent and
// received with minimal copying.
struct Message {
  mojo_base.mojom.BigBuffer buffer;
  array<mojo.native.SerializedHandle>? handles;
};

interface Channel {
  // Informs the remote end of this client's PID. Must be called exactly once,
  // before any calls to Receive() below.
  SetPeerPid(int32 pid);

  // Transmits a classical Chrome IPC message.
  Receive(Message message);

  // Requests a Channel-associated interface.
  GetAssociatedInterface(
      string name,
      pending_associated_receiver<GenericInterface> receiver);
};

// A strictly nominal interface used to identify Channel bootstrap requests.
interface ChannelBootstrap {};

