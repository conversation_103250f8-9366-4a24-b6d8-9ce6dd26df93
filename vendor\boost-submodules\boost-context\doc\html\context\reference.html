<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=US-ASCII">
<title>Reference</title>
<link rel="stylesheet" href="../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../index.html" title="Chapter&#160;1.&#160;Context">
<link rel="up" href="../index.html" title="Chapter&#160;1.&#160;Context">
<link rel="prev" href="rationale/x86_and_floating_point_env.html" title="x86 and floating-point env">
<link rel="next" href="acknowledgements.html" title="Acknowledgments">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../boost.png"></td>
<td align="center"><a href="../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="rationale/x86_and_floating_point_env.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../index.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="acknowledgements.html"><img src="../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="context.reference"></a><a class="link" href="reference.html" title="Reference">Reference</a>
</h2></div></div></div>
<h4>
<a name="context.reference.h0"></a>
      <span><a name="context.reference.arm"></a></span><a class="link" href="reference.html#context.reference.arm">ARM</a>
    </h4>
<div class="itemizedlist"><ul class="itemizedlist" type="disc">
<li class="listitem">
          AAPCS ABI: Procedure Call Standard for the ARM Architecture
        </li>
<li class="listitem">
          AAPCS/LINUX: ARM GNU/Linux Application Binary Interface Supplement
        </li>
</ul></div>
<h4>
<a name="context.reference.h1"></a>
      <span><a name="context.reference.mips"></a></span><a class="link" href="reference.html#context.reference.mips">MIPS</a>
    </h4>
<div class="itemizedlist"><ul class="itemizedlist" type="disc"><li class="listitem">
          O32 ABI: SYSTEM V APPLICATION BINARY INTERFACE, MIPS RISC Processor Supplement
        </li></ul></div>
<h4>
<a name="context.reference.h2"></a>
      <span><a name="context.reference.powerpc32"></a></span><a class="link" href="reference.html#context.reference.powerpc32">PowerPC32</a>
    </h4>
<div class="itemizedlist"><ul class="itemizedlist" type="disc"><li class="listitem">
          SYSV ABI: SYSTEM V APPLICATION BINARY INTERFACE PowerPC Processor Supplement
        </li></ul></div>
<h4>
<a name="context.reference.h3"></a>
      <span><a name="context.reference.powerpc64"></a></span><a class="link" href="reference.html#context.reference.powerpc64">PowerPC64</a>
    </h4>
<div class="itemizedlist"><ul class="itemizedlist" type="disc"><li class="listitem">
          SYSV ABI: PowerPC User Instruction Set Architecture, Book I
        </li></ul></div>
<h4>
<a name="context.reference.h4"></a>
      <span><a name="context.reference.x86_32"></a></span><a class="link" href="reference.html#context.reference.x86_32">X86-32</a>
    </h4>
<div class="itemizedlist"><ul class="itemizedlist" type="disc">
<li class="listitem">
          SYSV ABI: SYSTEM V APPLICATION BINARY INTERFACE, Intel386TM Architecture
          Processor Supplement
        </li>
<li class="listitem">
          MS PE: <a href="http://msdn.microsoft.com/en-us/library/k2b2ssfy.aspx" target="_top">Calling
          Conventions</a>
        </li>
</ul></div>
<h4>
<a name="context.reference.h5"></a>
      <span><a name="context.reference.x86_64"></a></span><a class="link" href="reference.html#context.reference.x86_64">X86-64</a>
    </h4>
<div class="itemizedlist"><ul class="itemizedlist" type="disc">
<li class="listitem">
          SYSV ABI: System V Application Binary Interface, AMD64 Architecture Processor
          Supplement
        </li>
<li class="listitem">
          MS PE: <a href="http://msdn.microsoft.com/en-us/library/7kcdt6fy%28VS.80%29.aspx" target="_top">x64
          Software Conventions</a>
        </li>
</ul></div>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright &#169; 2014 Oliver Kowalke<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="rationale/x86_and_floating_point_env.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../index.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="acknowledgements.html"><img src="../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
