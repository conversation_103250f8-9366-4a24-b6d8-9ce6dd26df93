#pragma once
#include <SDK.h>
#include <HostSharedData.h>
#include <ReverseGameData.h>

#pragma region icon
// 'component' icon by https://paomedia.github.io/small-n-flat/ licensed under CC-BY 3.0
unsigned char componentIcon[6866] = {
	0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D, 0x49, 0x48, 0x44, 0x52,
	0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01, 0x00, 0x08, 0x06, 0x00, 0x00, 0x00, 0x5C, 0x72, 0xA8,
	0x66, 0x00, 0x00, 0x00, 0x04, 0x73, 0x42, 0x49, 0x54, 0x08, 0x08, 0x08, 0x08, 0x7C, 0x08, 0x64,
	0x88, 0x00, 0x00, 0x00, 0x09, 0x70, 0x48, 0x59, 0x73, 0x00, 0x00, 0x0B, 0x13, 0x00, 0x00, 0x0B,
	0x13, 0x01, 0x00, 0x9A, 0x9C, 0x18, 0x00, 0x00, 0x1A, 0x74, 0x49, 0x44, 0x41, 0x54, 0x78, 0x9C,
	0xED, 0xDD, 0x79, 0x90, 0x1C, 0x77, 0x75, 0x07, 0xF0, 0xEF, 0xFB, 0xF5, 0xCC, 0xEC, 0x39, 0x33,
	0xBB, 0x6B, 0xCB, 0xB6, 0x0E, 0xDF, 0x06, 0x63, 0x49, 0xDC, 0x46, 0x06, 0x5B, 0xB6, 0x31, 0xC1,
	0x89, 0x0B, 0x42, 0xA5, 0x52, 0x24, 0x14, 0x7F, 0x70, 0x04, 0x5F, 0x80, 0x49, 0x42, 0xB8, 0x21,
	0x0E, 0x10, 0x8E, 0xA4, 0x28, 0x28, 0x28, 0x2A, 0xC1, 0x50, 0x14, 0x01, 0x2A, 0x04, 0x48, 0x2C,
	0x43, 0x55, 0x52, 0x24, 0x4E, 0x9C, 0x4A, 0xA2, 0xD8, 0x33, 0x2B, 0x09, 0x5B, 0xF2, 0x21, 0xC9,
	0xC1, 0xD8, 0xD8, 0xC2, 0x87, 0x24, 0x4B, 0x5E, 0xED, 0xCE, 0xB1, 0x3B, 0x3B, 0x3B, 0xD3, 0xFD,
	0x7B, 0xF9, 0x63, 0x56, 0xA6, 0x2D, 0xED, 0xAE, 0x66, 0x66, 0xBB, 0xFB, 0xF7, 0xEB, 0xEE, 0xF7,
	0xA9, 0x72, 0x59, 0x96, 0x77, 0x67, 0x9E, 0xB4, 0xFB, 0x7B, 0xEF, 0xBB, 0x7D, 0xFC, 0x9A, 0x20,
	0x62, 0x8D, 0xB7, 0xC1, 0x99, 0x59, 0x3B, 0xBE, 0x3E, 0xC3, 0xED, 0xB3, 0x35, 0xA9, 0xF5, 0x44,
	0xB4, 0x81, 0x99, 0xCF, 0x02, 0x70, 0x3A, 0x18, 0xA7, 0xB1, 0xA2, 0xD3, 0x88, 0xB9, 0x40, 0xC4,
	0x23, 0x5A, 0xAB, 0x51, 0xA5, 0xF4, 0xB0, 0x86, 0xCA, 0x00, 0xC8, 0x28, 0x20, 0xA3, 0x35, 0xB4,
	0x82, 0x6E, 0x02, 0xAA, 0xA9, 0x15, 0x16, 0x14, 0x74, 0x53, 0xB3, 0x6A, 0x2A, 0xD6, 0x0B, 0xAC,
	0x68, 0x96, 0x98, 0x8F, 0x80, 0xD4, 0x11, 0x66, 0x7E, 0x16, 0xA0, 0x23, 0x44, 0xF4, 0x2C, 0xB4,
	0x77, 0xA4, 0x9D, 0xCB, 0x1E, 0x3C, 0xED, 0xB2, 0xE9, 0x83, 0x44, 0x60, 0xD3, 0x7F, 0x07, 0xA2,
	0x7F, 0x64, 0xBA, 0x00, 0xD1, 0x9D, 0x83, 0xBB, 0xD7, 0x0E, 0x8F, 0x34, 0xE7, 0x36, 0x12, 0x68,
	0x33, 0x83, 0x37, 0xB3, 0xA6, 0x4B, 0x00, 0x5C, 0x08, 0xE0, 0x7C, 0xA5, 0x90, 0x33, 0x53, 0x95,
	0x9E, 0x03, 0xE3, 0x51, 0x40, 0xFD, 0x12, 0xC0, 0x2F, 0x59, 0xF1, 0x23, 0x0C, 0xFA, 0x65, 0x91,
	0xF2, 0x8F, 0xA8, 0xCB, 0x9F, 0x99, 0x37, 0x53, 0x93, 0xE8, 0x85, 0x34, 0x00, 0x0B, 0x31, 0x43,
	0xCD, 0xEE, 0x1C, 0xDB, 0xE4, 0x69, 0xBD, 0x15, 0x8C, 0xD7, 0x00, 0xB8, 0x94, 0x99, 0x36, 0x29,
	0x05, 0x65, 0xBA, 0xB6, 0x6E, 0x68, 0xC0, 0x55, 0x1A, 0x0F, 0x82, 0xB0, 0x93, 0xC1, 0xBB, 0x00,
	0xDA, 0x55, 0xDC, 0x5A, 0x3D, 0x20, 0x69, 0xC1, 0x3E, 0xD2, 0x00, 0x2C, 0xC0, 0x0C, 0x9A, 0x2D,
	0x8F, 0x6F, 0xD4, 0xD0, 0xD7, 0x6A, 0xC2, 0x6F, 0x29, 0xE8, 0x2B, 0x00, 0x35, 0x6E, 0xBA, 0xAE,
	0x20, 0x69, 0x8D, 0xA3, 0x44, 0xBC, 0x13, 0x8C, 0xFF, 0x64, 0xA6, 0x7F, 0x1F, 0xBF, 0xBA, 0x7A,
	0xC0, 0x74, 0x4D, 0x42, 0x1A, 0x80, 0x31, 0x7A, 0xC7, 0x86, 0xA1, 0x3A, 0x57, 0xDF, 0x00, 0x8D,
	0xDF, 0x63, 0xA6, 0x37, 0x93, 0xA2, 0x75, 0xA6, 0x6B, 0x8A, 0x92, 0xD6, 0x78, 0x84, 0x88, 0xEF,
	0x64, 0xA6, 0x3B, 0xC7, 0x26, 0xAA, 0x25, 0xDA, 0x8C, 0x96, 0xE9, 0x9A, 0xD2, 0x48, 0x1A, 0x40,
	0x84, 0x78, 0x3B, 0x06, 0xEB, 0x4E, 0xF1, 0x3A, 0x06, 0xDE, 0x0E, 0xE8, 0xB7, 0x40, 0xA9, 0x61,
	0xD3, 0x35, 0xD9, 0x41, 0xCF, 0x6A, 0x56, 0xFF, 0x4C, 0xC4, 0xFF, 0x50, 0x3C, 0x54, 0xFB, 0x6F,
	0x7A, 0x1B, 0x3C, 0xD3, 0x15, 0xA5, 0x85, 0x34, 0x80, 0x90, 0x31, 0x83, 0x6A, 0xA5, 0xFC, 0x16,
	0x28, 0x5C, 0x0F, 0x8D, 0xB7, 0x43, 0xA9, 0x82, 0xE9, 0x9A, 0x6C, 0xC6, 0x9A, 0x0F, 0x41, 0xE1,
	0xC7, 0x8E, 0x52, 0x3F, 0xC8, 0x5F, 0x5E, 0xD9, 0x67, 0xBA, 0x9E, 0xA4, 0x93, 0x06, 0x10, 0x92,
	0x23, 0xDB, 0xD7, 0x8C, 0x0E, 0x64, 0x17, 0xDE, 0x09, 0x8D, 0x5B, 0x48, 0xD1, 0x66, 0xD3, 0xF5,
	0xC4, 0x92, 0xD6, 0x0F, 0x12, 0xA9, 0x6F, 0xE4, 0xDD, 0xEA, 0x8F, 0xE8, 0x1A, 0x34, 0x4D, 0x97,
	0x93, 0x44, 0xD2, 0x00, 0x02, 0x36, 0x5D, 0x1A, 0x3F, 0x47, 0x91, 0xF7, 0x21, 0xD2, 0x7C, 0xBD,
	0x4C, 0xFB, 0x60, 0x68, 0x8D, 0x23, 0x44, 0x7C, 0x9B, 0x72, 0xF5, 0xB7, 0x0A, 0xD7, 0xCC, 0x4E,
	0x99, 0xAE, 0x27, 0x49, 0xA4, 0x01, 0x04, 0xA4, 0x5A, 0xCE, 0x5F, 0xCC, 0x4C, 0x9F, 0x64, 0xA2,
	0x77, 0x28, 0x20, 0x63, 0xBA, 0x9E, 0x24, 0x62, 0xCD, 0xF3, 0x04, 0xFA, 0x7B, 0x26, 0x7C, 0x65,
	0xEC, 0xCA, 0xEA, 0x13, 0xA6, 0xEB, 0x49, 0x02, 0x69, 0x00, 0xAB, 0x34, 0x3D, 0x39, 0x76, 0xAE,
	0xD2, 0xFA, 0xB3, 0xCC, 0xF4, 0xEE, 0xB8, 0x9C, 0xA7, 0x8F, 0x3B, 0xAD, 0xD1, 0x06, 0xE1, 0x5B,
	0x0E, 0x7B, 0x5F, 0x2C, 0x5C, 0x35, 0xFB, 0x9C, 0xE9, 0x7A, 0xE2, 0x4C, 0x1A, 0x40, 0x9F, 0xA6,
	0x77, 0x8F, 0x17, 0xD5, 0xBC, 0x77, 0x2B, 0x33, 0x7D, 0xD0, 0xDC, 0x95, 0x78, 0x29, 0xA7, 0x75,
	0x9D, 0x15, 0x7D, 0x69, 0x6E, 0x70, 0xE4, 0xEB, 0xEB, 0x2F, 0x3D, 0xDC, 0x30, 0x5D, 0x4E, 0x1C,
	0x49, 0x03, 0xE8, 0x11, 0x33, 0xA8, 0xB6, 0xA3, 0xF8, 0x47, 0xDA, 0xC3, 0x97, 0x94, 0xC2, 0x19,
	0xA6, 0xEB, 0x11, 0x9D, 0x33, 0x07, 0xA4, 0xF8, 0x13, 0x85, 0x2B, 0xEA, 0x3F, 0x92, 0xAB, 0x0D,
	0x7B, 0x23, 0x0D, 0xA0, 0x07, 0xB5, 0xC9, 0xD1, 0x97, 0x68, 0xAD, 0xBE, 0x4D, 0x44, 0x57, 0x99,
	0xAE, 0x45, 0x9C, 0x4C, 0x03, 0xFF, 0x9A, 0x53, 0xCE, 0xFB, 0x46, 0x2E, 0x9F, 0x3E, 0x68, 0xBA,
	0x96, 0xB8, 0x90, 0x9F, 0x59, 0xBB, 0xC0, 0x0C, 0x55, 0x2D, 0x15, 0x3F, 0xE2, 0x79, 0xCE, 0x43,
	0xB2, 0xF8, 0xED, 0xA5, 0x80, 0xDF, 0x75, 0xBD, 0xF6, 0xC3, 0xD5, 0x52, 0xF1, 0x7A, 0x66, 0x19,
	0x6E, 0xDD, 0x90, 0xBF, 0xA4, 0x53, 0x98, 0xDA, 0x35, 0xB1, 0x21, 0xE3, 0xBA, 0x3F, 0x24, 0xD0,
	0xD5, 0xA6, 0x6B, 0x11, 0x3D, 0x60, 0xDC, 0xA5, 0x94, 0xFB, 0xAE, 0xFC, 0x15, 0x73, 0x47, 0x4D,
	0x97, 0x62, 0x33, 0x69, 0x00, 0x2B, 0xA8, 0x94, 0x0A, 0xD7, 0x32, 0xD1, 0x8F, 0x15, 0x70, 0xBA,
	0xE9, 0x5A, 0x44, 0x1F, 0x58, 0x3F, 0xC3, 0x44, 0xBF, 0x3F, 0xB6, 0xB5, 0xB6, 0xDB, 0x74, 0x29,
	0xB6, 0x92, 0x1F, 0x01, 0x96, 0xC0, 0x0C, 0xAA, 0x96, 0x8B, 0x1F, 0x63, 0xA6, 0xBB, 0x64, 0xF1,
	0xC7, 0x18, 0xA9, 0x0D, 0x0C, 0x2A, 0x57, 0x4B, 0x63, 0xEF, 0x32, 0x5D, 0x8A, 0xAD, 0x24, 0x01,
	0x9C, 0x80, 0x77, 0x23, 0x5B, 0x6D, 0x16, 0xBE, 0x45, 0xA0, 0x1B, 0x4C, 0xD7, 0x22, 0x02, 0xC4,
	0xF8, 0xDB, 0x82, 0x5B, 0xFD, 0x30, 0x5D, 0x03, 0xD7, 0x74, 0x29, 0x36, 0x91, 0x06, 0xE0, 0x73,
	0x70, 0xF7, 0xDA, 0xE1, 0xE1, 0xF9, 0xC6, 0x36, 0x45, 0x78, 0xB3, 0xE9, 0x5A, 0x44, 0x08, 0x18,
	0xFF, 0x54, 0x70, 0xAB, 0xEF, 0x94, 0x26, 0xF0, 0x1B, 0xD2, 0x00, 0x16, 0x1D, 0xD9, 0xBE, 0x66,
	0x74, 0x20, 0xB3, 0xF0, 0x6F, 0x72, 0x94, 0x3F, 0xF1, 0xB6, 0x15, 0x06, 0xAB, 0xEF, 0xA0, 0x4B,
	0xD1, 0x36, 0x5D, 0x88, 0x0D, 0xE4, 0x18, 0x00, 0x3A, 0x9B, 0x73, 0x0C, 0x66, 0x9A, 0xFF, 0x22,
	0x8B, 0x3F, 0x15, 0xDE, 0x56, 0x9B, 0xCF, 0xFF, 0x23, 0xEF, 0x46, 0xD6, 0x74, 0x21, 0x36, 0x48,
	0x7D, 0x03, 0xE0, 0x6D, 0x70, 0x6A, 0xBA, 0x76, 0x3B, 0x48, 0xBD, 0xC1, 0x74, 0x2D, 0x22, 0x22,
	0xA4, 0xDE, 0x5A, 0x9B, 0x2F, 0xFE, 0x40, 0xAE, 0x15, 0x90, 0x06, 0x80, 0xEA, 0xBA, 0xC2, 0x57,
	0x09, 0xF4, 0x16, 0xD3, 0x75, 0x88, 0x88, 0x11, 0xDE, 0x5E, 0x29, 0x15, 0x3E, 0x6E, 0xBA, 0x0C,
	0xD3, 0x52, 0xDD, 0x01, 0xAB, 0x93, 0xC5, 0xF7, 0x80, 0xF1, 0x3D, 0xD3, 0x75, 0x08, 0x33, 0xB4,
	0x06, 0x13, 0xF8, 0x4D, 0x63, 0x57, 0xD5, 0xFE, 0xC3, 0x74, 0x2D, 0xA6, 0xA4, 0xB6, 0x01, 0xD4,
	0x27, 0xC7, 0x37, 0x7B, 0x9E, 0x77, 0x2F, 0x29, 0x1A, 0x32, 0x5D, 0x8B, 0x30, 0x49, 0x57, 0xE0,
	0x62, 0x4B, 0xF1, 0xF5, 0xF5, 0xC7, 0x4C, 0x57, 0x62, 0x42, 0x2A, 0x7F, 0x04, 0xE0, 0xFD, 0xC8,
	0x79, 0xDA, 0xFB, 0xB1, 0x2C, 0x7E, 0x01, 0xA8, 0x31, 0x38, 0xEA, 0x87, 0xCC, 0xE9, 0x5C, 0x0B,
	0xA9, 0xFC, 0x43, 0xD7, 0x66, 0x8A, 0xB7, 0x12, 0xD1, 0x4B, 0x4D, 0xD7, 0x21, 0x2C, 0x41, 0xD8,
	0x52, 0x2D, 0x17, 0x6E, 0x32, 0x5D, 0x86, 0x09, 0xA9, 0xFB, 0x11, 0xA0, 0x52, 0x2E, 0x5C, 0xC8,
	0x9A, 0x7E, 0xA1, 0x94, 0x9C, 0x06, 0x12, 0x7E, 0x7A, 0x46, 0x91, 0x7E, 0x49, 0xDA, 0x6E, 0x1E,
	0x4A, 0x61, 0x02, 0xE0, 0x2F, 0xCB, 0xE2, 0x17, 0x27, 0x53, 0xE3, 0x1E, 0x3B, 0x5F, 0x36, 0x5D,
	0x45, 0xD4, 0x52, 0x95, 0x00, 0x2A, 0x77, 0x17, 0x7E, 0x9B, 0x1C, 0xBA, 0xCB, 0x74, 0x1D, 0xC2,
	0x62, 0xAE, 0xBE, 0xBC, 0xF8, 0xFA, 0xFA, 0x4E, 0xD3, 0x65, 0x44, 0x25, 0x35, 0x09, 0xA0, 0x5A,
	0x1A, 0x7B, 0x37, 0x14, 0x7E, 0x66, 0xBA, 0x0E, 0x61, 0x37, 0xCE, 0xD0, 0xFF, 0x54, 0xCB, 0xF9,
	0x77, 0x98, 0xAE, 0x23, 0x2A, 0x89, 0x4F, 0x00, 0x73, 0x3B, 0x26, 0xD6, 0xB7, 0x3C, 0xEF, 0xDB,
	0x72, 0x83, 0x8F, 0xE8, 0x05, 0x83, 0x7F, 0x96, 0xF1, 0xDC, 0xF7, 0x8E, 0x5E, 0xDD, 0x38, 0x6C,
	0xBA, 0x96, 0x30, 0x25, 0x3A, 0x01, 0x54, 0x4B, 0xC5, 0xEB, 0x5D, 0xAF, 0xFD, 0xB0, 0x2C, 0x7E,
	0xD1, 0x2B, 0x02, 0xBD, 0xC5, 0x73, 0x9C, 0x87, 0xAB, 0xA5, 0xB1, 0x77, 0x9B, 0xAE, 0x25, 0x4C,
	0x89, 0x4C, 0x00, 0x53, 0xBB, 0x26, 0x36, 0x64, 0x5D, 0xEF, 0x3B, 0x00, 0xAE, 0x33, 0x5D, 0x8B,
	0x88, 0x3F, 0xAD, 0x71, 0x67, 0x2E, 0xE3, 0xDC, 0x9C, 0xC4, 0xCD, 0x46, 0x13, 0x97, 0x00, 0x66,
	0xCA, 0xC5, 0x9B, 0xB2, 0xAD, 0xF6, 0xC3, 0x90, 0xC5, 0x2F, 0x02, 0xA2, 0x14, 0xDE, 0xE4, 0x7A,
	0xED, 0x87, 0x6B, 0xA5, 0x62, 0xE2, 0x36, 0x89, 0x49, 0x4C, 0x02, 0x98, 0x2E, 0x8D, 0x9F, 0xE3,
	0x90, 0xFE, 0x3B, 0x00, 0xD7, 0x9A, 0xAE, 0x45, 0x24, 0x18, 0xE3, 0x2E, 0x57, 0xAB, 0x9B, 0x4E,
	0xBB, 0x7A, 0xE6, 0x69, 0xD3, 0xA5, 0x04, 0x21, 0xF6, 0x09, 0x80, 0x19, 0x54, 0x29, 0x17, 0xDE,
	0xE7, 0xB0, 0xBB, 0x1F, 0xB2, 0xF8, 0x45, 0xD8, 0x08, 0xBF, 0x93, 0x21, 0x77, 0x7F, 0xA5, 0x54,
	0xBC, 0xD9, 0x74, 0x29, 0x41, 0x88, 0x75, 0x02, 0x98, 0xD9, 0x59, 0x3C, 0x4F, 0xB9, 0xFA, 0xBB,
	0x72, 0x2F, 0xBF, 0x30, 0x43, 0xFF, 0x97, 0x47, 0xCE, 0x8D, 0x13, 0x57, 0x54, 0x9E, 0x34, 0x5D,
	0x49, 0xBF, 0x62, 0x99, 0x00, 0x98, 0x41, 0x95, 0x52, 0xE1, 0x03, 0xCA, 0xD3, 0xFB, 0x64, 0xF1,
	0x0B, 0x73, 0xD4, 0x1B, 0x1D, 0xF6, 0xF6, 0x57, 0xCA, 0x85, 0xF7, 0xC7, 0x75, 0x73, 0x91, 0xD8,
	0x15, 0x5D, 0x29, 0x15, 0x2F, 0x20, 0xD2, 0xDF, 0x05, 0xD4, 0xEB, 0x0D, 0x97, 0x22, 0xC4, 0x6F,
	0x68, 0xBD, 0x5D, 0xB3, 0xBA, 0x61, 0xFC, 0xEA, 0xEA, 0x01, 0xD3, 0xA5, 0xF4, 0x22, 0x36, 0x09,
	0x80, 0x19, 0x54, 0x9D, 0xCC, 0xFF, 0x29, 0xB1, 0xDE, 0x27, 0x8B, 0x5F, 0x58, 0x47, 0xA9, 0x6B,
	0x94, 0xA3, 0xF7, 0x55, 0x4A, 0x85, 0x3F, 0x8E, 0x53, 0x1A, 0x88, 0x45, 0xA1, 0x95, 0x1D, 0x85,
	0x8B, 0xE0, 0xE2, 0x7B, 0xA4, 0xE8, 0xCA, 0x9E, 0x3F, 0xD9, 0x05, 0x90, 0x09, 0xBE, 0x26, 0x91,
	0x02, 0x8C, 0xBE, 0x56, 0x08, 0x83, 0xEF, 0x06, 0x70, 0xC3, 0xD8, 0xD6, 0xDA, 0xE3, 0x41, 0x97,
	0x14, 0x34, 0xAB, 0x13, 0x00, 0x33, 0x54, 0xB5, 0x5C, 0xFC, 0x30, 0x5C, 0xEC, 0xED, 0x6B, 0xF1,
	0x03, 0xE0, 0xA6, 0xD5, 0x7F, 0x44, 0x61, 0x31, 0xF2, 0xFA, 0xFC, 0x3C, 0xD0, 0xD5, 0xA4, 0x79,
	0x6F, 0xB5, 0x54, 0xFC, 0x33, 0xDB, 0x37, 0x1A, 0xB1, 0x36, 0x01, 0x54, 0xEF, 0xC9, 0xBF, 0x58,
	0x93, 0xFA, 0xBE, 0x22, 0x5C, 0xBE, 0x9A, 0xD7, 0xE1, 0xBA, 0x02, 0x46, 0x18, 0xA4, 0xE4, 0xB1,
	0xF1, 0xA2, 0x37, 0xD4, 0x06, 0x78, 0x95, 0x37, 0x8E, 0x6B, 0x60, 0x52, 0x69, 0x7D, 0x7D, 0xF1,
	0xAA, 0xFA, 0xA3, 0xC1, 0x54, 0x15, 0x2C, 0xEB, 0xBA, 0x13, 0x6F, 0x83, 0x53, 0x2D, 0x17, 0x3F,
	0xA6, 0x95, 0x7A, 0x68, 0xB5, 0x8B, 0xFF, 0xF9, 0xD7, 0x6C, 0x59, 0xDB, 0xE7, 0x84, 0xAD, 0x74,
	0x30, 0x2F, 0xA3, 0x80, 0x2B, 0x18, 0xF4, 0x60, 0xB5, 0x54, 0xFC, 0x88, 0x8D, 0x69, 0xC0, 0xAA,
	0x95, 0x51, 0xDB, 0x39, 0x7A, 0x89, 0xE7, 0x39, 0xDF, 0x57, 0xC0, 0x65, 0x41, 0xBD, 0x26, 0xD7,
	0x15, 0x18, 0x04, 0x95, 0xEF, 0x33, 0xCF, 0x89, 0x54, 0xA2, 0x26, 0x00, 0x67, 0xF5, 0x09, 0xC0,
	0x4F, 0x6B, 0xEC, 0x74, 0x1C, 0xEF, 0xFA, 0xC2, 0x15, 0xB3, 0x8F, 0x04, 0xF7, 0xAA, 0xAB, 0x63,
	0x45, 0x47, 0xE2, 0x6D, 0x70, 0x2A, 0xA5, 0xC2, 0x27, 0x3D, 0xCF, 0x79, 0x20, 0xC8, 0xC5, 0x7F,
	0x9C, 0x9E, 0x95, 0xA3, 0x80, 0xA2, 0x47, 0x0B, 0xC1, 0xCF, 0x46, 0xA5, 0xF0, 0x3A, 0x8F, 0x9D,
	0x07, 0x6A, 0xE5, 0xE2, 0xC7, 0x79, 0x1B, 0x9C, 0xC0, 0xDF, 0xA0, 0x0F, 0xC6, 0x13, 0x40, 0xBD,
	0x34, 0xBE, 0x49, 0xB3, 0xFE, 0x3E, 0x14, 0x5E, 0x13, 0xC6, 0xEB, 0x73, 0x5D, 0xA1, 0xFD, 0xD4,
	0x30, 0x72, 0x9B, 0x66, 0xC3, 0x78, 0x79, 0x91, 0x50, 0x74, 0x84, 0x80, 0x09, 0x0E, 0x34, 0x01,
	0xBC, 0x00, 0xE3, 0x5E, 0x62, 0xEF, 0x3D, 0x85, 0xAB, 0x66, 0xFF, 0x2F, 0xA4, 0x77, 0xE8, 0x8A,
	0xB1, 0x04, 0xC0, 0xDB, 0x91, 0xA9, 0x94, 0x0B, 0xB7, 0xBA, 0xAC, 0xEF, 0x0F, 0x6B, 0xF1, 0x1F,
	0xE7, 0x4E, 0x4B, 0x02, 0x10, 0x3D, 0x6A, 0x84, 0xBC, 0x34, 0x08, 0x5B, 0x3C, 0xE5, 0xDC, 0x5F,
	0x29, 0x17, 0xFE, 0xDC, 0x64, 0x1A, 0x30, 0xD2, 0x00, 0xEA, 0x3B, 0xC6, 0x5E, 0x5A, 0xCB, 0xE6,
	0x7F, 0x4E, 0xA0, 0x2F, 0x2A, 0x85, 0x5C, 0xD8, 0xEF, 0xC7, 0x4D, 0x47, 0x4E, 0x07, 0x8A, 0xAE,
	0x91, 0x0B, 0xD0, 0x42, 0xF8, 0xEF, 0xA3, 0x80, 0x01, 0x02, 0xFD, 0x55, 0xED, 0xAC, 0xE2, 0xAE,
	0xFA, 0xE4, 0xF8, 0xE6, 0xF0, 0xDF, 0x71, 0xC9, 0x1A, 0xA2, 0xC3, 0xBB, 0x91, 0xAD, 0x94, 0x0B,
	0x9F, 0x71, 0x5D, 0xDE, 0x03, 0xA8, 0x57, 0x45, 0xF7, 0xC6, 0x80, 0x7B, 0x34, 0xF4, 0x3E, 0x23,
	0x92, 0xA2, 0x1E, 0xF1, 0x4F, 0xC6, 0x0A, 0x97, 0xBA, 0x9E, 0xDE, 0x53, 0x29, 0x15, 0x3E, 0xCD,
	0xDB, 0xA3, 0xBD, 0x6C, 0x2D, 0xB2, 0x06, 0x30, 0x53, 0x2E, 0xBE, 0xA2, 0xD6, 0xC8, 0xDF, 0x4B,
	0xA0, 0xCF, 0x99, 0xD8, 0x96, 0xDB, 0x7D, 0x7A, 0x30, 0xEA, 0xB7, 0x14, 0x31, 0x45, 0x95, 0xE8,
	0xD3, 0xA2, 0x52, 0xC8, 0x11, 0xD1, 0xE7, 0x6B, 0x99, 0xFC, 0xBD, 0xD3, 0xA5, 0xB1, 0x97, 0x47,
	0xF6, 0xBE, 0x61, 0xBF, 0x01, 0xEF, 0x46, 0xB6, 0x5A, 0x2E, 0x7E, 0x1E, 0x1A, 0xF7, 0x42, 0xA9,
	0x57, 0x84, 0xFD, 0x7E, 0xCB, 0xD1, 0x73, 0x0E, 0xBC, 0x63, 0xF2, 0x38, 0x00, 0x71, 0x0A, 0x0D,
	0x02, 0x22, 0x88, 0xFF, 0xCB, 0x22, 0xF5, 0x4A, 0x62, 0xBE, 0xAF, 0x5A, 0x2A, 0xFE, 0x25, 0xEF,
	0x0E, 0x7F, 0x50, 0x86, 0xDA, 0x00, 0x66, 0xEE, 0x2E, 0xBE, 0xAA, 0xD6, 0xCC, 0xEF, 0x06, 0xF0,
	0x69, 0x1B, 0x1E, 0xC6, 0xD1, 0x7E, 0x4A, 0x52, 0x80, 0x58, 0x99, 0x9A, 0x36, 0x7F, 0xAC, 0x48,
	0x29, 0x64, 0x41, 0xF8, 0x6C, 0xAD, 0x99, 0xBF, 0x6F, 0x66, 0xB2, 0xF8, 0xCA, 0x50, 0xDF, 0x2B,
	0x8C, 0x17, 0xE5, 0xFD, 0xC8, 0x55, 0x4B, 0xC5, 0x2F, 0xC2, 0xC1, 0xCF, 0x01, 0xF5, 0xB2, 0x30,
	0xDE, 0xA3, 0x1F, 0xDE, 0x54, 0x0E, 0xBA, 0x6E, 0xC5, 0xE9, 0x57, 0x61, 0x21, 0x6A, 0x02, 0x98,
	0x33, 0x5D, 0x85, 0x9F, 0x7A, 0x39, 0x18, 0xF7, 0x56, 0xCB, 0xC5, 0x2F, 0xF0, 0xFE, 0x70, 0x0E,
	0x96, 0x07, 0xDE, 0x00, 0x2A, 0xE5, 0xC2, 0xA5, 0xD5, 0x4A, 0xE1, 0x7E, 0x10, 0x6E, 0x55, 0xB6,
	0xDD, 0x87, 0xC7, 0x40, 0xEB, 0x17, 0xA3, 0xA6, 0xAB, 0x10, 0xB6, 0x3A, 0xAA, 0x3A, 0x77, 0x00,
	0x5A, 0x64, 0x71, 0x0D, 0xFD, 0x45, 0x75, 0xBA, 0xB0, 0xA7, 0x52, 0x2A, 0xBE, 0x3A, 0x84, 0xD7,
	0x0F, 0x86, 0xBE, 0x13, 0x03, 0xD5, 0x72, 0xF1, 0x4B, 0x0C, 0xDA, 0x45, 0xA0, 0x4D, 0x41, 0xBD,
	0x6E, 0xD0, 0xBC, 0x4A, 0x06, 0xED, 0x83, 0x03, 0xA6, 0xCB, 0x10, 0x96, 0xA1, 0x3A, 0x81, 0x1A,
	0xC6, 0xAF, 0x8B, 0x5B, 0x16, 0x29, 0xDA, 0xCC, 0x84, 0x5D, 0xD5, 0x72, 0xF1, 0xAF, 0xF5, 0x9D,
	0x08, 0xEC, 0x1B, 0x38, 0x90, 0x06, 0x50, 0xBD, 0x27, 0x7F, 0x59, 0x35, 0x5F, 0x7C, 0x00, 0xC0,
	0x27, 0x14, 0xEC, 0xB8, 0xC4, 0x71, 0x25, 0xED, 0xC7, 0x86, 0xC1, 0x6D, 0x7B, 0xBF, 0xD8, 0x22,
	0x62, 0xDE, 0xE2, 0x95, 0x7F, 0x96, 0x5B, 0x4C, 0x03, 0x9F, 0xAA, 0x16, 0x8A, 0xF7, 0x57, 0x76,
	0x14, 0xB6, 0x04, 0xF4, 0x9A, 0xFD, 0xE3, 0xED, 0x18, 0xAC, 0x94, 0x0B, 0x5F, 0xD1, 0x4A, 0x4D,
	0x2A, 0xC2, 0x25, 0x41, 0x14, 0x14, 0x05, 0x6E, 0x29, 0xB4, 0x1E, 0x19, 0x31, 0x5D, 0x86, 0xB0,
	0x04, 0x3D, 0xAB, 0x00, 0xD7, 0xFE, 0x06, 0x70, 0x9C, 0x02, 0x36, 0xB2, 0xA6, 0x1D, 0x95, 0x52,
	0xE1, 0xCB, 0xBC, 0x1D, 0xAB, 0x3A, 0xB2, 0xDD, 0x77, 0x03, 0xA8, 0x4E, 0xE6, 0x2F, 0xAF, 0x38,
	0xC5, 0x07, 0x09, 0xF4, 0xD1, 0x38, 0x4C, 0xFD, 0x13, 0xB9, 0x87, 0x07, 0xD0, 0x7E, 0x52, 0xCE,
	0x0A, 0xA4, 0x1D, 0xCD, 0x28, 0x50, 0xD4, 0x17, 0xFE, 0x04, 0x40, 0x01, 0x0E, 0x11, 0x7D, 0xAC,
	0xE2, 0x14, 0x1F, 0xAC, 0x96, 0xF3, 0xAF, 0x5B, 0xC5, 0xEB, 0xF4, 0x46, 0xEF, 0xD8, 0x30, 0x54,
	0x29, 0x15, 0xBE, 0xA6, 0x3D, 0x55, 0x52, 0x0A, 0x17, 0xF7, 0xFB, 0xC6, 0x36, 0x68, 0x3D, 0x3A,
	0x0C, 0x6F, 0xDA, 0xF8, 0xD9, 0x49, 0x61, 0x4A, 0x03, 0xA0, 0xA3, 0xF1, 0x5B, 0xFC, 0x7E, 0x4A,
	0xE1, 0x62, 0xAD, 0x55, 0xB9, 0x52, 0x2A, 0x7C, 0x55, 0xEF, 0xD8, 0x30, 0xD4, 0xF3, 0xE7, 0xF7,
	0xF2, 0xC1, 0xB5, 0xF2, 0xD8, 0x95, 0x55, 0x5D, 0x7F, 0x88, 0x88, 0x3E, 0xA4, 0x94, 0x1D, 0xB7,
	0x12, 0xAF, 0x0A, 0x13, 0x16, 0xF6, 0x8E, 0x82, 0xE7, 0xE3, 0xFF, 0x47, 0x11, 0x3D, 0x6A, 0x11,
	0xD4, 0x41, 0xC7, 0xBA, 0xA3, 0xFE, 0xFD, 0x50, 0x0A, 0x8A, 0x88, 0x3E, 0x5C, 0xD5, 0xF5, 0x87,
	0x6A, 0xA5, 0xF1, 0xAD, 0x3D, 0x7D, 0x6E, 0x37, 0x1F, 0x74, 0x70, 0xF7, 0xDA, 0xE1, 0x6A, 0xA9,
	0xF8, 0x37, 0x9E, 0xE6, 0xBB, 0x15, 0xF0, 0xA2, 0xFE, 0xCA, 0xB4, 0x13, 0xB7, 0x14, 0x9A, 0x7B,
	0x0A, 0xD0, 0x72, 0xB3, 0x50, 0x7A, 0xB8, 0x80, 0x7A, 0x5A, 0x01, 0x09, 0xDB, 0x23, 0x46, 0x01,
	0x2F, 0xF2, 0x58, 0xDF, 0x5D, 0x29, 0x17, 0xBE, 0x7E, 0x70, 0xF7, 0xDA, 0xE1, 0x2E, 0x3F, 0x67,
	0x65, 0xB5, 0x7B, 0xC6, 0xAE, 0x1E, 0x6D, 0x34, 0xF6, 0x82, 0xF0, 0x27, 0x4A, 0x99, 0xDF, 0x3F,
	0x20, 0x0C, 0xBA, 0xE1, 0xA0, 0xB9, 0xBB, 0x20, 0x77, 0x0C, 0xA6, 0x81, 0x4B, 0x9D, 0xC5, 0xDF,
	0x36, 0x5D, 0x48, 0x38, 0x94, 0x82, 0x22, 0xD0, 0x07, 0x47, 0x1B, 0x8D, 0xBD, 0xB5, 0xC9, 0xB1,
	0xAB, 0x4E, 0xF9, 0xF1, 0xCB, 0xFD, 0x8F, 0x23, 0xDB, 0xD7, 0x8C, 0x56, 0xCB, 0xC5, 0xDB, 0x3C,
	0xF0, 0x76, 0x28, 0x5C, 0x18, 0x6C, 0x99, 0xF6, 0xE1, 0x86, 0x83, 0xF9, 0xFB, 0xA4, 0x09, 0x24,
	0x19, 0xB9, 0x80, 0x7A, 0x8A, 0x42, 0xD9, 0xED, 0xC7, 0x3A, 0x0A, 0x17, 0x7A, 0x1E, 0xFF, 0x6F,
	0xB5, 0x5C, 0xFC, 0xC6, 0xE1, 0xBB, 0xCE, 0x5C, 0xF6, 0x94, 0xD7, 0x92, 0xDF, 0xED, 0x33, 0x93,
	0xC5, 0x37, 0x0C, 0x66, 0x9B, 0x7B, 0x01, 0xDC, 0x92, 0xD4, 0xA9, 0xBF, 0x14, 0x9E, 0x77, 0x30,
	0x7F, 0x5F, 0x11, 0x7A, 0x2E, 0x76, 0x27, 0x35, 0xC4, 0xA9, 0x2C, 0x10, 0xF0, 0xA4, 0x03, 0xA4,
	0x68, 0x83, 0xD8, 0xC5, 0xB5, 0xFB, 0x81, 0xE1, 0x91, 0xC6, 0xBE, 0x99, 0x72, 0xF1, 0x9A, 0xA5,
	0x3E, 0xE6, 0x05, 0x7F, 0x1B, 0xCF, 0x95, 0x4F, 0xCB, 0xE7, 0xE0, 0x7E, 0x45, 0x6B, 0xDC, 0x9C,
	0x94, 0x85, 0xCF, 0x75, 0x85, 0xC6, 0xCE, 0xF1, 0xDE, 0x3E, 0x29, 0xCB, 0x18, 0xD8, 0x5C, 0x47,
	0x66, 0x4D, 0x42, 0x73, 0x62, 0xCA, 0xD0, 0x1C, 0x81, 0x0E, 0xF5, 0xFE, 0x33, 0x3F, 0x5F, 0xE8,
	0x85, 0xB7, 0x25, 0x58, 0xC4, 0xB4, 0x06, 0x2B, 0xE0, 0xDB, 0x2D, 0x95, 0xF9, 0xF8, 0x9A, 0xAD,
	0xC7, 0xEA, 0xC7, 0x7F, 0xFF, 0xF9, 0x04, 0x50, 0x29, 0x15, 0xAE, 0xCD, 0xA1, 0xB5, 0x1F, 0xC0,
	0x7B, 0x93, 0xB2, 0xF8, 0xFB, 0xD6, 0x26, 0x2C, 0x3C, 0x58, 0x40, 0xFB, 0xF1, 0xAE, 0x8E, 0xA3,
	0x08, 0x8B, 0xD1, 0x34, 0x81, 0x9E, 0x49, 0xDE, 0x01, 0xBF, 0x5E, 0x29, 0x05, 0x82, 0xC2, 0xFB,
	0x72, 0xBA, 0xB5, 0xAF, 0x52, 0x2A, 0xBE, 0xF1, 0xF8, 0xEF, 0xD3, 0xD4, 0xAE, 0x89, 0x42, 0xA6,
	0xED, 0x7E, 0x95, 0x88, 0x6E, 0x34, 0x59, 0x60, 0x58, 0xFA, 0x4A, 0x00, 0x3E, 0x99, 0x33, 0x5A,
	0x18, 0xD8, 0x3C, 0x0B, 0x64, 0x12, 0x70, 0xBE, 0x28, 0x4D, 0x34, 0xA0, 0x0E, 0x29, 0x60, 0xB6,
	0xFF, 0x59, 0x96, 0xA4, 0x04, 0x70, 0x12, 0xD6, 0xDF, 0x69, 0x67, 0xB3, 0x1F, 0xCD, 0xA8, 0x2A,
	0x6E, 0xA7, 0x11, 0xBA, 0xCE, 0x74, 0x3D, 0x61, 0xA1, 0x55, 0x66, 0x19, 0xF7, 0x68, 0x0E, 0xDE,
	0x8E, 0x31, 0x0C, 0x6C, 0x9C, 0x83, 0x73, 0x7A, 0x2B, 0x98, 0xA2, 0x44, 0xA8, 0xA8, 0x41, 0xC0,
	0xE1, 0xD5, 0x1F, 0xE9, 0x67, 0x22, 0x24, 0xE2, 0x42, 0x81, 0xA5, 0x90, 0xBA, 0x29, 0x3B, 0xEB,
	0x9D, 0xAD, 0x9A, 0x93, 0x63, 0xD7, 0xB5, 0x7F, 0x35, 0x58, 0x82, 0xA7, 0xEB, 0xA7, 0xFE, 0xAC,
	0xF8, 0xE1, 0x00, 0x8E, 0xE7, 0x71, 0x53, 0xA1, 0xF9, 0x40, 0x1E, 0x0B, 0xFB, 0x47, 0xC1, 0x31,
	0xBA, 0x66, 0x3C, 0x75, 0x74, 0xE7, 0xA6, 0x1E, 0x7A, 0x5A, 0x81, 0x82, 0x38, 0x7C, 0xE3, 0x24,
	0x74, 0xF1, 0x6B, 0x5D, 0x53, 0xCF, 0x71, 0x59, 0xFD, 0xDA, 0xB9, 0x4E, 0x01, 0x40, 0xEB, 0x89,
	0x91, 0x2B, 0xE7, 0xCA, 0x13, 0x75, 0x9E, 0x57, 0x7B, 0x4C, 0xD7, 0x16, 0xB8, 0x5C, 0x40, 0x5F,
	0x44, 0x06, 0xDC, 0x43, 0x03, 0x68, 0x4E, 0x8E, 0xCB, 0x06, 0xA3, 0x36, 0x9A, 0x25, 0xD0, 0x01,
	0x07, 0x34, 0x13, 0xD0, 0x3D, 0xFD, 0x0E, 0x2C, 0x78, 0x6A, 0x46, 0x08, 0x5C, 0xEC, 0xD1, 0x07,
	0x54, 0x03, 0xC7, 0x32, 0x5B, 0x01, 0xFF, 0x86, 0x1D, 0x0B, 0xCE, 0xBA, 0x46, 0x69, 0x7C, 0x5D,
	0xEE, 0x82, 0xB9, 0x52, 0xF6, 0xFC, 0xC6, 0x2B, 0xE0, 0xA8, 0xBC, 0xB1, 0x22, 0x03, 0x44, 0x0E,
	0x83, 0x94, 0x06, 0xEB, 0x60, 0xCE, 0xEF, 0xEB, 0x05, 0xC2, 0xC2, 0x83, 0x79, 0xB8, 0x13, 0x6D,
	0xE4, 0x2E, 0x6E, 0x40, 0xE5, 0xDD, 0x40, 0x5E, 0x57, 0xF4, 0x69, 0x01, 0xA0, 0xA3, 0x0A, 0x34,
	0x17, 0xF0, 0x6A, 0x4D, 0xDA, 0x31, 0x1F, 0xAD, 0x6B, 0xEA, 0x18, 0xED, 0xC5, 0xB1, 0xCC, 0x56,
	0xFF, 0x4A, 0x38, 0x69, 0x55, 0x24, 0x31, 0x0D, 0x50, 0x50, 0x29, 0xC0, 0xC7, 0x9B, 0xCE, 0x62,
	0x7E, 0x57, 0x11, 0x0B, 0xFB, 0xE4, 0x5E, 0x02, 0x13, 0xA8, 0xDD, 0xB9, 0x8D, 0x57, 0xFD, 0xDA,
	0x09, 0x7E, 0xF1, 0x03, 0x80, 0x4A, 0xD0, 0xF8, 0x3F, 0x61, 0xEA, 0xFB, 0x2D, 0xBD, 0x65, 0x57,
	0xC2, 0xD2, 0x80, 0x33, 0xEC, 0x41, 0x37, 0x43, 0xB8, 0xB8, 0x87, 0x3B, 0xB7, 0x15, 0x7B, 0x47,
	0x72, 0xC8, 0x9C, 0xBD, 0x80, 0xCC, 0x79, 0x0D, 0xA8, 0x81, 0x84, 0x4D, 0x0E, 0xDB, 0xB8, 0x00,
	0x1D, 0x53, 0xA0, 0x0A, 0x00, 0x0E, 0x71, 0x91, 0x0E, 0x25, 0xE0, 0xEB, 0xB8, 0xCC, 0xD4, 0xF7,
	0x5B, 0x71, 0x74, 0x25, 0x25, 0x0D, 0xD0, 0x58, 0xB8, 0x17, 0xF4, 0xB0, 0x26, 0xB4, 0x9F, 0x1C,
	0x44, 0xB3, 0x34, 0x8E, 0x85, 0x7D, 0xA3, 0xB2, 0xF1, 0x68, 0x18, 0x5A, 0x04, 0x75, 0x58, 0x41,
	0x3D, 0xEE, 0x80, 0x66, 0x28, 0xDC, 0xC5, 0x0F, 0x80, 0x47, 0x62, 0xDE, 0x00, 0x56, 0x98, 0xFA,
	0x7E, 0xA7, 0xDE, 0xB4, 0x33, 0x01, 0x69, 0x20, 0x7B, 0x66, 0x0B, 0xAD, 0x27, 0xC2, 0xDF, 0x01,
	0x88, 0x35, 0xC1, 0x3D, 0x3C, 0x00, 0xF7, 0xD9, 0x01, 0xA8, 0xF1, 0x36, 0x72, 0xE7, 0x35, 0xE5,
	0xD4, 0xE1, 0x2A, 0xD1, 0x1C, 0x01, 0xD3, 0x8B, 0xFB, 0xF5, 0x45, 0xB5, 0x26, 0x09, 0xE0, 0xB8,
	0x26, 0x80, 0x2E, 0xA6, 0xBE, 0x5F, 0xD7, 0xBB, 0xF6, 0xB6, 0x9E, 0x18, 0xB9, 0xB2, 0x75, 0x70,
	0xF0, 0xD0, 0xF0, 0x96, 0xDA, 0xA3, 0x34, 0xA4, 0x03, 0xDF, 0x9D, 0x34, 0x54, 0x79, 0x0D, 0x35,
	0xE0, 0x41, 0x2F, 0x44, 0x34, 0x99, 0x19, 0xD0, 0xD3, 0x59, 0x34, 0xA7, 0xB3, 0xA0, 0x61, 0x0F,
	0x99, 0xB5, 0x0B, 0xC8, 0xAE, 0x6B, 0x81, 0x86, 0x52, 0x7E, 0x39, 0x5A, 0xB7, 0xDC, 0xCE, 0xD3,
	0x79, 0xA8, 0x46, 0x80, 0x89, 0xFE, 0x39, 0xC8, 0x06, 0x1F, 0x9B, 0xBB, 0x0A, 0x2E, 0xF6, 0xE8,
	0x27, 0xD5, 0x7A, 0xD5, 0x76, 0xBA, 0xDE, 0x13, 0x80, 0x0E, 0x7D, 0xE6, 0x82, 0x9E, 0x5B, 0x5D,
	0x1C, 0xD3, 0x40, 0x7B, 0xFF, 0x30, 0x5A, 0x87, 0x7A, 0xDE, 0x30, 0x25, 0x38, 0x04, 0x38, 0xC5,
	0x36, 0x32, 0x6B, 0x5B, 0x70, 0xCE, 0x5A, 0x00, 0x65, 0x63, 0x3A, 0x61, 0xC2, 0xE2, 0xA1, 0x73,
	0x2A, 0xAF, 0x46, 0xE1, 0x1C, 0xD4, 0xEB, 0x01, 0x9F, 0xC9, 0xE0, 0x71, 0x6D, 0xB4, 0x86, 0x9E,
	0xF8, 0xA6, 0x7E, 0xAF, 0x9F, 0xDA, 0x57, 0x03, 0x00, 0x00, 0x0C, 0x78, 0x87, 0x86, 0xB7, 0xD4,
	0x0E, 0xC7, 0x25, 0x0D, 0xE8, 0x39, 0xC2, 0xFC, 0x8E, 0x09, 0x2B, 0x2E, 0xEC, 0x22, 0xC5, 0x50,
	0x13, 0x6D, 0x64, 0xD6, 0xB4, 0xE1, 0x9C, 0xDE, 0x02, 0x0D, 0xC5, 0xE8, 0x9B, 0x2D, 0x48, 0x2E,
	0x81, 0x66, 0xD1, 0x59, 0xF8, 0x73, 0x11, 0x46, 0xFC, 0x95, 0x28, 0x40, 0x5F, 0xE4, 0xC5, 0x27,
	0x01, 0xB8, 0xD8, 0xA3, 0x9F, 0xC4, 0xFA, 0x4C, 0xDB, 0x39, 0xAB, 0x9F, 0x4F, 0xEF, 0xFF, 0xC1,
	0x1D, 0x31, 0x3B, 0x36, 0xA0, 0x46, 0x18, 0x99, 0x42, 0x1B, 0x6E, 0xD5, 0xFC, 0xC5, 0xDD, 0xAC,
	0x09, 0xDE, 0x54, 0x0E, 0xDE, 0x54, 0x0E, 0xA0, 0x11, 0xA8, 0x61, 0x0F, 0xCE, 0x62, 0x33, 0x70,
	0xC6, 0x5C, 0x40, 0xD9, 0xB0, 0x12, 0x42, 0xC0, 0x00, 0xCD, 0x53, 0xE7, 0xE9, 0x3B, 0x73, 0x04,
	0x5A, 0xB0, 0x64, 0xD1, 0xFB, 0xF0, 0x98, 0x8E, 0xC7, 0xE2, 0xEF, 0xF1, 0x67, 0xFD, 0xE5, 0xF4,
	0x9F, 0x00, 0xFC, 0x62, 0x92, 0x06, 0xF4, 0x31, 0x07, 0xF3, 0x7B, 0xC6, 0x4C, 0x97, 0xB1, 0x22,
	0x72, 0x00, 0x95, 0x6F, 0x43, 0x8D, 0x79, 0x70, 0x8A, 0x6D, 0xD0, 0x58, 0x3B, 0xBE, 0xA7, 0x16,
	0xBD, 0xC5, 0x05, 0x3F, 0x0F, 0x50, 0x43, 0x81, 0x17, 0x00, 0xB2, 0x3C, 0xEC, 0xE8, 0x0B, 0x74,
	0x70, 0x57, 0x8F, 0x86, 0x65, 0x95, 0x53, 0xDF, 0x2F, 0x98, 0x47, 0x77, 0xC5, 0x24, 0x0D, 0xA8,
	0xD3, 0xBC, 0x4E, 0x0A, 0xA8, 0x99, 0x4F, 0x01, 0xCB, 0x61, 0x0F, 0xF0, 0x2A, 0x59, 0x78, 0x95,
	0x2C, 0xDA, 0x8B, 0x5B, 0xBE, 0xD3, 0xA0, 0x86, 0x2A, 0xBA, 0x70, 0xF2, 0x1E, 0xD4, 0xB0, 0x0B,
	0x1A, 0xF5, 0x40, 0xC3, 0x1A, 0x64, 0x4B, 0x52, 0x60, 0x74, 0xF6, 0xD5, 0x5F, 0x00, 0xA8, 0xD9,
	0x39, 0x70, 0x47, 0xF3, 0x74, 0xD2, 0xCD, 0x38, 0xD6, 0x5F, 0x5A, 0x53, 0x64, 0xBB, 0x17, 0x7F,
	0x40, 0x53, 0xDF, 0x2F, 0x98, 0x04, 0xE0, 0x67, 0x79, 0x1A, 0xE0, 0xBA, 0x83, 0xF9, 0x5D, 0x45,
	0x70, 0xC8, 0xE7, 0x91, 0x43, 0x47, 0x8B, 0x8D, 0x61, 0xC4, 0x83, 0x1A, 0x71, 0x41, 0x03, 0x0C,
	0xCA, 0xE9, 0xCE, 0x3F, 0x03, 0xBA, 0xF3, 0xDF, 0x59, 0x1D, 0xCC, 0xAA, 0xF3, 0x00, 0x78, 0x04,
	0xB8, 0x0C, 0x72, 0x09, 0x70, 0xD1, 0x39, 0x52, 0xDF, 0x5A, 0x3C, 0x4A, 0xDF, 0xB6, 0x2F, 0xCA,
	0xF7, 0xCC, 0x01, 0xF4, 0x05, 0x9E, 0xBD, 0x4F, 0xB8, 0x08, 0x70, 0xEA, 0xFB, 0x05, 0xFF, 0xF0,
	0x4E, 0xCB, 0xD3, 0x00, 0xE5, 0x3D, 0x64, 0xCF, 0x5A, 0x40, 0xEB, 0x70, 0xCC, 0x1F, 0x0A, 0xC2,
	0x00, 0xCF, 0x2B, 0x78, 0xF3, 0x0A, 0xDE, 0xD4, 0x32, 0x89, 0x86, 0xD0, 0x69, 0x02, 0x4E, 0xE7,
	0x9E, 0x08, 0x10, 0x3A, 0xC7, 0x17, 0x14, 0x83, 0x14, 0x83, 0x14, 0x30, 0x78, 0xC6, 0x1C, 0x88,
	0x09, 0xCC, 0x9D, 0x15, 0x4C, 0x0C, 0xB0, 0x47, 0x20, 0x46, 0x67, 0xE1, 0x6B, 0xC4, 0x7F, 0x71,
	0x77, 0x81, 0xD7, 0x68, 0x3B, 0x17, 0x7F, 0x08, 0x53, 0xDF, 0x2F, 0xB4, 0xA7, 0xF7, 0xDA, 0x7C,
	0xDD, 0x40, 0x76, 0x63, 0x03, 0xDE, 0xB1, 0x2C, 0xBC, 0x96, 0x8D, 0x5F, 0xF1, 0x00, 0x71, 0x67,
	0xDB, 0xF3, 0xC5, 0x5F, 0x2E, 0x89, 0x86, 0x1A, 0x9D, 0x7F, 0xFB, 0xA2, 0x42, 0xCC, 0xB3, 0x51,
	0xEF, 0x86, 0x18, 0x5C, 0xB4, 0xB0, 0xCB, 0xF5, 0x71, 0x5E, 0xBF, 0x57, 0xE1, 0x1E, 0xEF, 0xEC,
	0xA4, 0x81, 0x57, 0x5B, 0xB7, 0xDF, 0x80, 0xC3, 0xC8, 0xBD, 0xAC, 0x9E, 0xC2, 0xEF, 0x74, 0x71,
	0x12, 0x05, 0xE8, 0xB5, 0x6C, 0xD7, 0xF7, 0xC2, 0xF1, 0xFB, 0xF5, 0x7F, 0xE5, 0xBC, 0x3A, 0xE8,
	0xC8, 0x7F, 0xA2, 0x48, 0x4E, 0x78, 0xD8, 0x78, 0x4F, 0x81, 0x9A, 0xF0, 0x30, 0x78, 0xC1, 0x9C,
	0xE9, 0x32, 0x84, 0x49, 0x04, 0xF0, 0x3A, 0xCB, 0x8E, 0xFA, 0x77, 0x79, 0x0D, 0x7F, 0x50, 0xA2,
	0x3B, 0xE3, 0x69, 0x61, 0x1A, 0x70, 0x2E, 0x6C, 0x22, 0xBB, 0x66, 0xC1, 0x74, 0x19, 0xC2, 0x10,
	0x5E, 0xA3, 0xC1, 0xA3, 0x96, 0x2C, 0xFE, 0x08, 0xA7, 0xBE, 0x5F, 0xE4, 0x97, 0x3C, 0xD8, 0x96,
	0x06, 0x72, 0x2F, 0x9F, 0x45, 0x26, 0x2F, 0x37, 0xEC, 0xA4, 0x0D, 0x17, 0x18, 0x3C, 0x61, 0xC9,
	0xE2, 0x8F, 0x78, 0xEA, 0xFB, 0x99, 0xB9, 0xE6, 0xC9, 0xA6, 0x34, 0xA0, 0x80, 0xDC, 0x96, 0x59,
	0x64, 0x86, 0x65, 0x67, 0x9F, 0xB4, 0xE0, 0x02, 0x83, 0xD7, 0x5A, 0x70, 0x45, 0x92, 0xA1, 0xA9,
	0xEF, 0x67, 0xF4, 0xA2, 0x47, 0x5B, 0xD2, 0x00, 0x39, 0x8C, 0xC1, 0xCB, 0xAA, 0x70, 0x86, 0xE5,
	0x41, 0x20, 0x49, 0xC7, 0xF9, 0xC5, 0xC5, 0x6F, 0xFA, 0xA0, 0x9F, 0xC1, 0xA9, 0xEF, 0x67, 0xFE,
	0xAA, 0x67, 0x4B, 0xD2, 0x00, 0x67, 0x81, 0x81, 0xD7, 0xD5, 0xE1, 0x8C, 0x4A, 0x13, 0x48, 0x2A,
	0x2E, 0x30, 0x78, 0xBD, 0xE1, 0xC5, 0x6F, 0xC1, 0xD4, 0xF7, 0x33, 0xDF, 0x00, 0x16, 0xD9, 0x90,
	0x06, 0xC8, 0x61, 0x0C, 0xBE, 0xB6, 0x8E, 0xEC, 0xB8, 0x1C, 0x13, 0x48, 0x1A, 0x9E, 0xD0, 0x9D,
	0x23, 0xFE, 0x26, 0x59, 0x32, 0xF5, 0xFD, 0xAC, 0x69, 0x00, 0x00, 0xEC, 0x48, 0x03, 0x8A, 0x91,
	0x7B, 0x4D, 0x1D, 0x03, 0x1B, 0x1A, 0x46, 0xDE, 0x5E, 0x04, 0x8C, 0x3A, 0x91, 0x9F, 0xCF, 0x30,
	0x78, 0xC0, 0xCF, 0xB2, 0xA9, 0xEF, 0x67, 0x57, 0x03, 0x58, 0x64, 0x43, 0x1A, 0xC8, 0x6C, 0x9C,
	0xC7, 0xD0, 0xA6, 0x3A, 0xC8, 0xB1, 0xE0, 0x60, 0x91, 0xE8, 0x0B, 0x67, 0x01, 0x3E, 0xD7, 0xF0,
	0x55, 0x7E, 0x16, 0x4E, 0x7D, 0x3F, 0x2B, 0x1B, 0x00, 0x00, 0x2B, 0xD2, 0x80, 0x5A, 0xDF, 0xC2,
	0xE0, 0x6B, 0xAB, 0x70, 0xE4, 0x0C, 0x41, 0xEC, 0xF0, 0x08, 0x83, 0xCF, 0xD5, 0xE0, 0x41, 0x43,
	0x8B, 0xDF, 0xE2, 0xA9, 0xEF, 0x67, 0x6F, 0x03, 0x58, 0x64, 0x3A, 0x0D, 0xA8, 0x11, 0x8D, 0xC1,
	0xCB, 0xAB, 0x18, 0x38, 0xBB, 0x01, 0x22, 0x4B, 0xCE, 0x1B, 0x8B, 0xE5, 0x29, 0x40, 0x9F, 0xA5,
	0xC1, 0x67, 0x6B, 0x73, 0x0F, 0xF7, 0xB0, 0x7C, 0xEA, 0xFB, 0x59, 0xDF, 0x00, 0x00, 0x98, 0x4F,
	0x03, 0x0A, 0xC8, 0x5C, 0x32, 0x8F, 0xC1, 0x4B, 0x25, 0x0D, 0xD8, 0x8C, 0x47, 0x18, 0x7C, 0xBE,
	0x07, 0x8C, 0xC9, 0xD4, 0xEF, 0x56, 0x3C, 0x1A, 0xC0, 0x22, 0xE3, 0x69, 0x60, 0xDC, 0xC3, 0xE0,
	0x15, 0x55, 0xE4, 0xCE, 0x9B, 0x03, 0x29, 0x39, 0x36, 0x60, 0x0D, 0x67, 0xF1, 0x40, 0xDF, 0xD9,
	0xDA, 0xDC, 0xE3, 0xBC, 0x63, 0x34, 0xF5, 0xFD, 0x42, 0xBB, 0x1D, 0x38, 0x34, 0xA6, 0xF7, 0x1B,
	0x20, 0x20, 0xFB, 0xE2, 0x26, 0x32, 0xE7, 0x2D, 0xA0, 0xFD, 0xF0, 0x08, 0xDA, 0x53, 0xB9, 0xD0,
	0x1F, 0x52, 0x21, 0x96, 0xA1, 0x00, 0x9E, 0x60, 0xF0, 0x69, 0x06, 0xCF, 0xED, 0x87, 0x7C, 0xBF,
	0x7E, 0xD8, 0xE2, 0x58, 0x33, 0x00, 0xF3, 0x69, 0x80, 0x72, 0x8C, 0xDC, 0x2B, 0x67, 0x31, 0xFC,
	0xDA, 0x2A, 0x32, 0x63, 0x72, 0xDD, 0x40, 0xA4, 0x08, 0xE0, 0x71, 0x86, 0xBE, 0x40, 0x83, 0x4F,
	0x37, 0xB8, 0xF8, 0x63, 0x3A, 0xF5, 0xFD, 0x82, 0xDF, 0x12, 0xCC, 0x00, 0x1B, 0x76, 0x1F, 0xD2,
	0x95, 0x0C, 0xDA, 0x8F, 0x0D, 0xC1, 0x9D, 0xC9, 0xC2, 0xFC, 0x75, 0xA6, 0xDD, 0xCB, 0x9F, 0x53,
	0x31, 0x5D, 0x42, 0xF7, 0x08, 0x40, 0x91, 0xA1, 0x27, 0x0C, 0xEF, 0xDD, 0xB7, 0x8A, 0x7D, 0xF8,
	0x6D, 0x93, 0x88, 0x06, 0x00, 0xC0, 0x9A, 0xBD, 0x08, 0xB9, 0xAE, 0xD0, 0x7E, 0x6C, 0x08, 0xED,
	0xE9, 0x01, 0x40, 0xDB, 0xDF, 0x08, 0x62, 0xD1, 0x00, 0x1C, 0x80, 0xC7, 0x35, 0x78, 0x0C, 0xE6,
	0x1F, 0xDB, 0x1D, 0xD2, 0xDE, 0x7C, 0xA6, 0x24, 0xA7, 0x01, 0x2C, 0xB2, 0x21, 0x0D, 0x00, 0x00,
	0x5A, 0x04, 0xF7, 0xF1, 0x21, 0xB4, 0x8F, 0x0C, 0x40, 0xB7, 0xEC, 0xFD, 0x49, 0xCB, 0xEA, 0x06,
	0x90, 0xEB, 0xDC, 0xB2, 0xCB, 0x45, 0x0B, 0x76, 0xEC, 0x49, 0xD0, 0xD4, 0xF7, 0x4B, 0x5C, 0x03,
	0x00, 0x60, 0x4D, 0x1A, 0x38, 0xCE, 0x3B, 0x92, 0x81, 0xF7, 0xD4, 0x20, 0xDA, 0x15, 0xFB, 0x0E,
	0x18, 0x5A, 0xD7, 0x00, 0x32, 0x0C, 0xCE, 0x03, 0x28, 0xB2, 0xB9, 0x8B, 0x78, 0x4E, 0x94, 0xB0,
	0xA9, 0xEF, 0x17, 0xBF, 0xB3, 0x00, 0xDD, 0x30, 0x7D, 0xA6, 0xE0, 0x04, 0xCE, 0x99, 0x2E, 0x9C,
	0x33, 0x67, 0x91, 0x75, 0x01, 0xFD, 0xCC, 0x00, 0xDC, 0xC3, 0x39, 0x78, 0xB3, 0x39, 0xB0, 0x25,
	0xDF, 0xDF, 0xC6, 0x11, 0xC0, 0xA3, 0x8B, 0x93, 0x7E, 0xC4, 0x82, 0x69, 0x7F, 0x5C, 0xCC, 0x8F,
	0xF0, 0x77, 0x23, 0x99, 0x09, 0xC0, 0xCF, 0xB2, 0x34, 0xF0, 0x3C, 0x17, 0x70, 0x0F, 0x0D, 0xC0,
	0x3B, 0x92, 0x85, 0x57, 0xCB, 0x82, 0x3D, 0x33, 0xDF, 0x62, 0xC6, 0x12, 0x40, 0x16, 0xE0, 0x51,
	0x0D, 0x8C, 0x02, 0x3C, 0x8C, 0xCE, 0x7E, 0xE4, 0x36, 0x49, 0xF0, 0xD4, 0xF7, 0x4B, 0x66, 0x02,
	0xF0, 0xB3, 0x2C, 0x0D, 0x3C, 0x2F, 0x03, 0x64, 0xCE, 0x59, 0x40, 0xE6, 0x9C, 0x85, 0xCE, 0xF6,
	0xDD, 0xD3, 0x59, 0xB8, 0xCF, 0x66, 0xA0, 0x67, 0x72, 0x70, 0x9B, 0x99, 0xCE, 0x7E, 0xFC, 0x49,
	0xE2, 0xA0, 0x13, 0xE9, 0x47, 0x18, 0x3C, 0x02, 0xC0, 0xD6, 0xC7, 0x9D, 0xA5, 0x60, 0xEA, 0xFB,
	0x25, 0x3F, 0x01, 0xF8, 0xD9, 0x9A, 0x06, 0x4E, 0xA4, 0x09, 0x3C, 0x95, 0x81, 0x77, 0x2C, 0x03,
	0xAF, 0x96, 0x81, 0x6E, 0x38, 0xD0, 0xAE, 0x13, 0xCA, 0x03, 0x3A, 0x42, 0x49, 0x00, 0x04, 0x20,
	0x07, 0x60, 0x88, 0xA1, 0x87, 0x18, 0x18, 0x82, 0x5D, 0x3B, 0xEF, 0x2E, 0x27, 0x25, 0x53, 0xDF,
	0x2F, 0xF9, 0x09, 0xC0, 0xCF, 0xD6, 0x34, 0x70, 0x22, 0xC5, 0xA0, 0x33, 0xDA, 0xC8, 0x9C, 0xD1,
	0x7E, 0xFE, 0x0B, 0xC4, 0x2D, 0x82, 0x9E, 0x76, 0xC0, 0xD5, 0x0C, 0xF4, 0x6C, 0x06, 0x7A, 0x5E,
	0x81, 0x5B, 0x0E, 0xB4, 0xA7, 0xCC, 0x3D, 0xB9, 0x87, 0x00, 0x64, 0xB9, 0xB3, 0xD8, 0x07, 0x00,
	0x7D, 0xFC, 0xD7, 0x83, 0x1C, 0xAF, 0x4B, 0xCC, 0x52, 0x36, 0xF5, 0xFD, 0xD2, 0x95, 0x00, 0xFC,
	0xE2, 0x92, 0x06, 0x4E, 0x81, 0x3D, 0x02, 0xD7, 0x14, 0xD0, 0x70, 0xE0, 0x35, 0x14, 0xB0, 0xE0,
	0x80, 0x17, 0x08, 0xDC, 0x56, 0x60, 0x17, 0x80, 0xA7, 0xA0, 0x3D, 0x5A, 0x7C, 0xC4, 0x97, 0x3A,
	0xE9, 0xC0, 0xE3, 0x0B, 0x12, 0x00, 0xA1, 0xB3, 0x70, 0x17, 0xFF, 0xCD, 0x84, 0xCE, 0x79, 0x77,
	0x07, 0xA0, 0x0C, 0xC0, 0x19, 0xEE, 0x8C, 0x8C, 0x0C, 0xC0, 0x0E, 0x3A, 0x53, 0xDD, 0x96, 0x03,
	0x76, 0xFD, 0x4A, 0xE1, 0xD4, 0xF7, 0x4B, 0x6F, 0x03, 0x58, 0x94, 0xBD, 0x68, 0xAE, 0x9C, 0x3B,
	0xB7, 0xF1, 0x32, 0x38, 0xAA, 0x60, 0xBA, 0x96, 0x48, 0x30, 0x3A, 0x3F, 0x62, 0x30, 0x83, 0x34,
	0x75, 0x16, 0x38, 0x21, 0xFE, 0x0B, 0xB9, 0x57, 0x5A, 0xD7, 0xD4, 0xB4, 0xDA, 0x8B, 0xA9, 0xF0,
	0x1E, 0xBB, 0x15, 0x07, 0x69, 0x4B, 0x3C, 0x27, 0x69, 0xFF, 0x6A, 0x64, 0xEB, 0xEC, 0xE4, 0xC4,
	0x9C, 0xD7, 0x54, 0xBB, 0x4D, 0xD7, 0x12, 0x09, 0x02, 0xE0, 0x30, 0x28, 0x83, 0xCE, 0x04, 0x3F,
	0x3E, 0xF1, 0xD3, 0xE4, 0xF8, 0x35, 0xFC, 0x29, 0x5F, 0xFC, 0x80, 0x34, 0x00, 0x00, 0x00, 0x35,
	0x9D, 0xB5, 0xCD, 0x7B, 0xC6, 0x2F, 0x6D, 0x3D, 0x31, 0x58, 0x86, 0xA7, 0x6B, 0xA6, 0xEB, 0x11,
	0x21, 0xD1, 0xBA, 0xA6, 0xA6, 0x10, 0xAB, 0xFB, 0xF5, 0xC3, 0x26, 0x0D, 0xC0, 0x27, 0x75, 0x69,
	0x20, 0x4D, 0x64, 0xEA, 0x2F, 0x49, 0x1A, 0xC0, 0x09, 0x24, 0x0D, 0x24, 0x8C, 0x4C, 0xFD, 0x15,
	0x49, 0x03, 0x58, 0x86, 0xA4, 0x81, 0x04, 0x90, 0xA9, 0x7F, 0x4A, 0xD2, 0x00, 0x56, 0x20, 0x69,
	0x20, 0xA6, 0x64, 0xEA, 0x77, 0x4D, 0x1A, 0x40, 0x17, 0x24, 0x0D, 0xC4, 0x88, 0x4C, 0xFD, 0x9E,
	0x48, 0x03, 0xE8, 0x92, 0xA4, 0x01, 0xCB, 0xC9, 0xD4, 0xEF, 0x8B, 0x34, 0x80, 0x1E, 0x49, 0x1A,
	0xB0, 0x90, 0x4C, 0xFD, 0xBE, 0x49, 0x03, 0xE8, 0x83, 0xA4, 0x01, 0x4B, 0xC8, 0xD4, 0x5F, 0x35,
	0x69, 0x00, 0xAB, 0x20, 0x69, 0xC0, 0x20, 0x99, 0xFA, 0x81, 0x90, 0x06, 0xB0, 0x4A, 0x92, 0x06,
	0x22, 0x26, 0x53, 0x3F, 0x50, 0xD2, 0x00, 0x02, 0x22, 0x69, 0x20, 0x02, 0x32, 0xF5, 0x03, 0x27,
	0x0D, 0x20, 0x40, 0x92, 0x06, 0x42, 0x22, 0x53, 0x3F, 0x34, 0xD2, 0x00, 0x42, 0x20, 0x69, 0x20,
	0x40, 0x32, 0xF5, 0x43, 0x25, 0x0D, 0x20, 0x24, 0x92, 0x06, 0x56, 0x49, 0xA6, 0x7E, 0x24, 0xA4,
	0x01, 0x84, 0x4C, 0xD2, 0x40, 0x1F, 0x64, 0xEA, 0x47, 0x46, 0x1A, 0x40, 0x04, 0x24, 0x0D, 0x74,
	0x49, 0xA6, 0x7E, 0xE4, 0xA4, 0x01, 0x44, 0x48, 0xD2, 0xC0, 0x0A, 0x64, 0xEA, 0x1B, 0x21, 0x0D,
	0x20, 0x62, 0x92, 0x06, 0x4E, 0x20, 0x53, 0xDF, 0x28, 0x69, 0x00, 0x86, 0x48, 0x1A, 0x80, 0x4C,
	0x7D, 0x0B, 0x48, 0x03, 0x30, 0x28, 0xB5, 0x69, 0x40, 0xA6, 0xBE, 0x35, 0xA4, 0x01, 0x58, 0x20,
	0x55, 0x69, 0x40, 0xA6, 0xBE, 0x55, 0xA4, 0x01, 0x58, 0x22, 0xF1, 0x69, 0x40, 0xA6, 0xBE, 0x95,
	0xA4, 0x01, 0x58, 0x26, 0x91, 0x69, 0x40, 0xA6, 0xBE, 0xB5, 0xA4, 0x01, 0x58, 0x28, 0x31, 0x69,
	0x40, 0xA6, 0xBE, 0xF5, 0xA4, 0x01, 0x58, 0x2C, 0xD6, 0x69, 0x40, 0xA6, 0x7E, 0x2C, 0x48, 0x03,
	0xB0, 0x5C, 0xEC, 0xD2, 0x80, 0x4C, 0xFD, 0x58, 0x91, 0x06, 0x10, 0x13, 0xB1, 0x48, 0x03, 0x32,
	0xF5, 0x63, 0x47, 0x1A, 0x40, 0x8C, 0x58, 0x9B, 0x06, 0x64, 0xEA, 0xC7, 0x96, 0x34, 0x80, 0x18,
	0xB2, 0x2A, 0x0D, 0xC8, 0xD4, 0x8F, 0x35, 0x69, 0x00, 0x31, 0x65, 0x3C, 0x0D, 0xC8, 0xD4, 0x4F,
	0x04, 0x69, 0x00, 0x31, 0x67, 0x24, 0x0D, 0xC8, 0xD4, 0x4F, 0x0C, 0x69, 0x00, 0x09, 0x10, 0x59,
	0x1A, 0x90, 0xA9, 0x9F, 0x38, 0xD2, 0x00, 0x12, 0x24, 0xD4, 0x34, 0x20, 0x53, 0x3F, 0x91, 0xA4,
	0x01, 0x24, 0x4C, 0xE0, 0x69, 0x40, 0xA6, 0x7E, 0xA2, 0x49, 0x03, 0x48, 0xA8, 0x40, 0xD2, 0x80,
	0x4C, 0xFD, 0xC4, 0x93, 0x06, 0x90, 0x60, 0x7D, 0xA7, 0x01, 0x99, 0xFA, 0xA9, 0x21, 0x0D, 0x20,
	0x05, 0x7A, 0x4A, 0x03, 0x32, 0xF5, 0x53, 0x45, 0x1A, 0x40, 0x4A, 0x9C, 0x32, 0x0D, 0xC8, 0xD4,
	0x4F, 0x25, 0x69, 0x00, 0x29, 0xB3, 0x64, 0x1A, 0x90, 0xA9, 0x9F, 0x5A, 0x19, 0xD3, 0x05, 0x88,
	0xE8, 0x2D, 0xA6, 0x81, 0xB5, 0xD9, 0xF3, 0xE7, 0xCA, 0x83, 0xE3, 0x4D, 0x60, 0xCA, 0xD9, 0x2A,
	0x93, 0x20, 0x9D, 0xA4, 0x01, 0xA4, 0x58, 0xFB, 0xC0, 0xC8, 0xD6, 0x41, 0xAF, 0x6D, 0xBA, 0x0C,
	0x61, 0x90, 0x34, 0x7E, 0x21, 0x52, 0x4C, 0x1A, 0x80, 0x10, 0x29, 0x26, 0x0D, 0x40, 0x88, 0x14,
	0x93, 0x06, 0x20, 0x44, 0x8A, 0x49, 0x03, 0x10, 0x22, 0xC5, 0xA4, 0x01, 0x08, 0x91, 0x62, 0xD2,
	0x00, 0x84, 0x48, 0x31, 0x69, 0x00, 0x42, 0xA4, 0x98, 0x34, 0x00, 0x21, 0x52, 0x4C, 0x1A, 0x80,
	0x10, 0x29, 0x26, 0x0D, 0x40, 0x88, 0x14, 0x93, 0x06, 0x20, 0x44, 0x8A, 0x49, 0x03, 0x10, 0x22,
	0xC5, 0x94, 0xD6, 0xFA, 0xA8, 0xE9, 0x22, 0x84, 0x10, 0x06, 0x68, 0x3C, 0xA7, 0xC8, 0xC9, 0x6D,
	0x82, 0xE6, 0xDB, 0x4D, 0xD7, 0x22, 0x84, 0x88, 0x8E, 0x06, 0x6E, 0x67, 0x6E, 0x6D, 0xA4, 0xE3,
	0xBF, 0x71, 0xE8, 0xD3, 0xE7, 0xBF, 0x55, 0x33, 0x7F, 0x53, 0x29, 0x75, 0x86, 0xC9, 0xC2, 0x44,
	0xB4, 0xF2, 0xE7, 0x54, 0x4C, 0x97, 0x20, 0xA2, 0xA4, 0xF1, 0x1C, 0x2B, 0xBC, 0xBF, 0x70, 0xE3,
	0xF4, 0x4F, 0x01, 0xDF, 0x31, 0x80, 0x75, 0x5F, 0x38, 0xF0, 0x53, 0x49, 0x03, 0x42, 0x24, 0xD7,
	0xF1, 0xA9, 0x7F, 0x7C, 0xF1, 0x03, 0x00, 0x2D, 0xF5, 0x81, 0x92, 0x06, 0xD2, 0x43, 0x12, 0x40,
	0x0A, 0x9C, 0x30, 0xF5, 0xFD, 0x96, 0x3C, 0x0B, 0x20, 0x69, 0x40, 0x88, 0x64, 0x58, 0x6A, 0xEA,
	0xFB, 0x2D, 0x99, 0x00, 0xFC, 0x24, 0x0D, 0x24, 0x9B, 0x24, 0x80, 0x84, 0x5A, 0x61, 0xEA, 0xFB,
	0x9D, 0xF2, 0x3A, 0x00, 0x49, 0x03, 0x42, 0xC4, 0xCB, 0xA9, 0xA6, 0xBE, 0xDF, 0x29, 0x13, 0x80,
	0x9F, 0xA4, 0x81, 0xE4, 0x91, 0x04, 0x90, 0x20, 0x5D, 0x4E, 0x7D, 0xBF, 0x9E, 0xAE, 0x04, 0x94,
	0x34, 0x20, 0x84, 0x9D, 0x7A, 0x99, 0xFA, 0x7E, 0x3D, 0x25, 0x00, 0x3F, 0x49, 0x03, 0xC9, 0x20,
	0x09, 0x20, 0xE6, 0xFA, 0x98, 0xFA, 0x7E, 0x7D, 0xDF, 0x0B, 0x20, 0x69, 0x40, 0x08, 0xB3, 0xFA,
	0x9D, 0xFA, 0x7E, 0x7D, 0x27, 0x00, 0x3F, 0x49, 0x03, 0xF1, 0x25, 0x09, 0x20, 0x86, 0x56, 0x39,
	0xF5, 0xFD, 0x02, 0xB9, 0x1B, 0x50, 0xD2, 0x80, 0x10, 0xD1, 0x08, 0x62, 0xEA, 0xFB, 0x05, 0x92,
	0x00, 0xFC, 0x24, 0x0D, 0xC4, 0x8B, 0x24, 0x80, 0x98, 0x08, 0x70, 0xEA, 0xFB, 0x05, 0xBE, 0x1F,
	0x80, 0xA4, 0x01, 0x21, 0x82, 0x15, 0xF4, 0xD4, 0xF7, 0x0B, 0x3C, 0x01, 0xF8, 0x49, 0x1A, 0xB0,
	0x9F, 0x24, 0x00, 0x8B, 0x85, 0x34, 0xF5, 0xFD, 0x42, 0xDD, 0x11, 0x48, 0xD2, 0x80, 0x10, 0xFD,
	0x09, 0x73, 0xEA, 0xFB, 0x85, 0x9A, 0x00, 0xFC, 0x24, 0x0D, 0xD8, 0x49, 0x12, 0x80, 0x65, 0x22,
	0x98, 0xFA, 0x7E, 0x91, 0xED, 0x09, 0x28, 0x69, 0x40, 0x88, 0x95, 0x45, 0x35, 0xF5, 0xFD, 0x22,
	0x4B, 0x00, 0x7E, 0x92, 0x06, 0xEC, 0x21, 0x09, 0xC0, 0x02, 0x11, 0x4F, 0x7D, 0x3F, 0x23, 0xBB,
	0x02, 0x4B, 0x1A, 0x10, 0xA2, 0xC3, 0xC4, 0xD4, 0xF7, 0x33, 0x92, 0x00, 0xFC, 0x24, 0x0D, 0x98,
	0x25, 0x09, 0xC0, 0x10, 0x8D, 0xE7, 0x18, 0xB8, 0xA5, 0x70, 0xF3, 0xF4, 0x4F, 0x4C, 0x96, 0x61,
	0xFC, 0xB9, 0x00, 0xC7, 0xD3, 0x00, 0x03, 0xDB, 0x4C, 0xD7, 0x22, 0x44, 0x14, 0x34, 0x63, 0x1B,
	0x3B, 0xAD, 0x4D, 0xA6, 0x17, 0x3F, 0x60, 0x41, 0x02, 0xF0, 0x93, 0x34, 0x10, 0x3D, 0x49, 0x00,
	0x11, 0xB2, 0x64, 0xEA, 0xFB, 0x19, 0x4F, 0x00, 0x7E, 0x92, 0x06, 0x44, 0x52, 0xD9, 0x34, 0xF5,
	0xFD, 0xAC, 0x4A, 0x00, 0x7E, 0x92, 0x06, 0xA2, 0x21, 0x09, 0x20, 0x64, 0x16, 0x4E, 0x7D, 0x3F,
	0xAB, 0x12, 0x80, 0x9F, 0xA4, 0x01, 0x11, 0x77, 0xB6, 0x4E, 0x7D, 0x3F, 0x6B, 0x13, 0x80, 0x9F,
	0xA4, 0x81, 0xF0, 0x48, 0x02, 0x08, 0x81, 0xE5, 0x53, 0xDF, 0xCF, 0xDA, 0x04, 0xE0, 0x27, 0x69,
	0x40, 0xC4, 0x45, 0x1C, 0xA6, 0xBE, 0x5F, 0x2C, 0x12, 0x80, 0x9F, 0xA4, 0x81, 0x60, 0x49, 0x02,
	0x08, 0x48, 0x8C, 0xA6, 0xBE, 0x5F, 0x2C, 0x12, 0x80, 0x9F, 0xA4, 0x01, 0x61, 0x9B, 0xB8, 0x4D,
	0x7D, 0xBF, 0xD8, 0x25, 0x00, 0x3F, 0x49, 0x03, 0xAB, 0x27, 0x09, 0x60, 0x15, 0x62, 0x3A, 0xF5,
	0xFD, 0x62, 0x97, 0x00, 0xFC, 0x24, 0x0D, 0x08, 0x53, 0xE2, 0x3C, 0xF5, 0xFD, 0x62, 0x9D, 0x00,
	0xFC, 0x24, 0x0D, 0xF4, 0x47, 0x12, 0x40, 0x8F, 0x12, 0x30, 0xF5, 0xFD, 0x62, 0x9D, 0x00, 0xFC,
	0x24, 0x0D, 0x88, 0xB0, 0x25, 0x65, 0xEA, 0xFB, 0x25, 0x26, 0x01, 0xF8, 0x49, 0x1A, 0xE8, 0x9E,
	0x24, 0x80, 0x2E, 0x24, 0x6C, 0xEA, 0xFB, 0x25, 0x26, 0x01, 0xF8, 0x49, 0x1A, 0x10, 0x41, 0x49,
	0xE2, 0xD4, 0xF7, 0x4B, 0x64, 0x02, 0xF0, 0x93, 0x34, 0xB0, 0x32, 0x49, 0x00, 0xCB, 0x48, 0xF0,
	0xD4, 0xF7, 0x4B, 0x64, 0x02, 0xF0, 0x93, 0x34, 0x20, 0x7A, 0x95, 0xF4, 0xA9, 0xEF, 0x97, 0xF8,
	0x04, 0xE0, 0x27, 0x69, 0xE0, 0x64, 0x92, 0x00, 0x7C, 0x52, 0x32, 0xF5, 0xFD, 0x12, 0x9F, 0x00,
	0xFC, 0x24, 0x0D, 0x88, 0xE5, 0xA4, 0x69, 0xEA, 0xFB, 0xA5, 0x2A, 0x01, 0xF8, 0x49, 0x1A, 0xE8,
	0x48, 0x7D, 0x02, 0x48, 0xE1, 0xD4, 0xF7, 0x4B, 0x55, 0x02, 0xF0, 0x93, 0x34, 0x20, 0xD2, 0x3A,
	0xF5, 0xFD, 0x52, 0x9B, 0x00, 0xFC, 0x0E, 0x7D, 0xF6, 0xFC, 0x3F, 0xD0, 0xCC, 0xDF, 0x54, 0x50,
	0x6B, 0x4C, 0xD7, 0x12, 0xB5, 0x54, 0x26, 0x80, 0x94, 0x4F, 0x7D, 0xBF, 0xD4, 0x26, 0x00, 0xBF,
	0x75, 0x9F, 0x3B, 0xF0, 0x13, 0xA2, 0xDC, 0x46, 0x49, 0x03, 0xC9, 0x27, 0x53, 0xFF, 0x85, 0x24,
	0x01, 0x9C, 0x20, 0x6D, 0x69, 0x20, 0x35, 0x09, 0x40, 0xA6, 0xFE, 0x92, 0x24, 0x01, 0x9C, 0x40,
	0xD2, 0x40, 0xF2, 0xC8, 0xD4, 0x5F, 0x9E, 0x24, 0x80, 0x15, 0xA4, 0x21, 0x0D, 0x24, 0x3A, 0x01,
	0xC8, 0xD4, 0x3F, 0x25, 0x49, 0x00, 0x2B, 0x90, 0x34, 0x10, 0x5F, 0x32, 0xF5, 0xBB, 0x23, 0x09,
	0xA0, 0x4B, 0x49, 0x4D, 0x03, 0x89, 0x4B, 0x00, 0x32, 0xF5, 0x7B, 0x22, 0x09, 0xA0, 0x4B, 0x92,
	0x06, 0xEC, 0x27, 0x53, 0xBF, 0x77, 0x92, 0x00, 0xFA, 0x90, 0xA4, 0x34, 0x90, 0x88, 0x04, 0x20,
	0x53, 0xBF, 0x6F, 0x92, 0x00, 0xFA, 0x20, 0x69, 0xC0, 0x1E, 0x32, 0xF5, 0x57, 0x47, 0x12, 0xC0,
	0x2A, 0xC5, 0x3D, 0x0D, 0xC4, 0x36, 0x01, 0xC8, 0xD4, 0x0F, 0x84, 0x24, 0x80, 0x55, 0x92, 0x34,
	0x10, 0x3D, 0x99, 0xFA, 0xC1, 0x91, 0x04, 0x10, 0xA0, 0x38, 0xA6, 0x81, 0x58, 0x25, 0x00, 0x99,
	0xFA, 0x81, 0x93, 0x04, 0x10, 0x20, 0x49, 0x03, 0xE1, 0x91, 0xA9, 0x1F, 0x0E, 0x49, 0x00, 0x21,
	0x89, 0x4B, 0x1A, 0xB0, 0x3E, 0x01, 0xC8, 0xD4, 0x0F, 0x95, 0x24, 0x80, 0x90, 0x48, 0x1A, 0x58,
	0x3D, 0x99, 0xFA, 0xE1, 0x93, 0x04, 0x10, 0x01, 0x9B, 0xD3, 0x80, 0x95, 0x09, 0x40, 0xA6, 0x7E,
	0x64, 0x24, 0x01, 0x44, 0x40, 0xD2, 0x40, 0xF7, 0x64, 0xEA, 0x47, 0x4B, 0x12, 0x40, 0xC4, 0x6C,
	0x4B, 0x03, 0xD6, 0x24, 0x00, 0x99, 0xFA, 0x46, 0x48, 0x02, 0x88, 0x98, 0xA4, 0x81, 0x93, 0xC9,
	0xD4, 0x37, 0x47, 0x12, 0x80, 0x41, 0x36, 0xA4, 0x01, 0xC3, 0x09, 0x60, 0x8A, 0x35, 0xDE, 0x2F,
	0x0B, 0xDF, 0x1C, 0x49, 0x00, 0x06, 0xAD, 0xFB, 0xDC, 0x81, 0x9F, 0xA8, 0x76, 0x66, 0x13, 0x03,
	0x77, 0x98, 0xAE, 0xC5, 0x80, 0x3B, 0x98, 0x5A, 0x1B, 0x65, 0xF1, 0x9B, 0x25, 0x09, 0xC0, 0x12,
	0x87, 0x3F, 0x73, 0xFE, 0x1F, 0x7A, 0xE0, 0xDB, 0xA2, 0x4E, 0x03, 0x06, 0x12, 0xC0, 0x14, 0x80,
	0x5B, 0xF2, 0x37, 0x4E, 0xA7, 0xB1, 0xE9, 0x59, 0x47, 0x12, 0x80, 0x25, 0xD6, 0x7E, 0xFE, 0xC0,
	0x1D, 0x29, 0x48, 0x03, 0x77, 0x30, 0xB5, 0x36, 0xCA, 0xE2, 0xB7, 0x87, 0x24, 0x00, 0x0B, 0x45,
	0x99, 0x06, 0x22, 0x4A, 0x00, 0x32, 0xF5, 0x2D, 0x25, 0x09, 0xC0, 0x42, 0x09, 0x4B, 0x03, 0x32,
	0xF5, 0x2D, 0x26, 0x09, 0xC0, 0x72, 0x61, 0xA7, 0x81, 0x10, 0x13, 0x80, 0x4C, 0xFD, 0x18, 0x90,
	0x04, 0x60, 0xB9, 0x98, 0xA6, 0x01, 0x99, 0xFA, 0x31, 0x21, 0x09, 0x20, 0x46, 0xC2, 0x48, 0x03,
	0x01, 0x27, 0x00, 0x99, 0xFA, 0x31, 0x23, 0x09, 0x20, 0x46, 0x2C, 0x4F, 0x03, 0x32, 0xF5, 0x63,
	0x48, 0x12, 0x40, 0x4C, 0x05, 0x95, 0x06, 0x02, 0x48, 0x00, 0x32, 0xF5, 0x63, 0x4C, 0x12, 0x40,
	0x4C, 0x59, 0x92, 0x06, 0x64, 0xEA, 0xC7, 0x9C, 0x24, 0x80, 0x04, 0x58, 0x4D, 0x1A, 0xE8, 0x33,
	0x01, 0xC8, 0xD4, 0x4F, 0x08, 0x49, 0x00, 0x09, 0x10, 0x71, 0x1A, 0x90, 0xA9, 0x9F, 0x20, 0x92,
	0x00, 0x12, 0xA6, 0xD7, 0x34, 0xD0, 0x43, 0x02, 0x90, 0xA9, 0x9F, 0x40, 0x92, 0x00, 0x12, 0x26,
	0xA4, 0x34, 0x20, 0x53, 0x3F, 0xA1, 0x24, 0x01, 0x24, 0x58, 0x37, 0x69, 0xE0, 0x14, 0x09, 0x40,
	0xA6, 0x7E, 0xC2, 0x49, 0x02, 0x48, 0xB0, 0x55, 0xA6, 0x01, 0x99, 0xFA, 0x29, 0x20, 0x09, 0x20,
	0x25, 0x96, 0x4B, 0x03, 0x4B, 0x24, 0x00, 0x99, 0xFA, 0x29, 0x22, 0x09, 0x20, 0x25, 0xBA, 0x4C,
	0x03, 0x32, 0xF5, 0x53, 0x46, 0x12, 0x40, 0x0A, 0xF9, 0xD3, 0xC0, 0x62, 0x02, 0x90, 0xA9, 0x9F,
	0x52, 0xD2, 0x00, 0x52, 0xEA, 0xD0, 0xA7, 0x2E, 0x5A, 0xC3, 0x59, 0x7D, 0x5B, 0xE1, 0x9C, 0x0A,
	0x98, 0x5A, 0x1F, 0x28, 0xDC, 0x30, 0xFB, 0x9C, 0xE9, 0x9A, 0x44, 0xF4, 0xFE, 0x1F, 0x31, 0xE8,
	0xB1, 0x92, 0x08, 0x75, 0xC6, 0x74, 0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4E, 0x44, 0xAE, 0x42,
	0x60, 0x82
};

static CefRefPtr<CefImage> g_icon;
#pragma endregion

// When using the Views framework this object provides the delegate
// implementation for the CefWindow that hosts the Views-based browser.
SDKWindowDelegate::SDKWindowDelegate(CefRefPtr<CefBrowserView> browser_view, const std::wstring& placementKey)
	: browser_view_(browser_view), placementRegistryKey(placementKey)
{
}

SDKWindowDelegate::SDKWindowDelegate(CefRefPtr<CefBrowserView> browser_view, const std::wstring& placementKey, const std::string& forceWindowTitle)
	: browser_view_(browser_view), placementRegistryKey(placementKey), forcedWindowTitle(forceWindowTitle)
{
}

void SDKWindowDelegate::OnWindowCreated(CefRefPtr<CefWindow> window)
{
	if (!g_icon)
	{
		g_icon = CefImage::CreateImage();
		g_icon->AddPNG(1.0f, componentIcon, sizeof(componentIcon));
	}

	// Add the browser view and show the window.
	window->AddChildView(browser_view_);
	window->SetWindowAppIcon(g_icon);
	window->SetWindowIcon(g_icon);

	if (!forcedWindowTitle.empty())
	{
		window->SetTitle(forcedWindowTitle);
	}

	window->Show();
	LoadPlacement(window);

	// Give keyboard focus to the browser view.
	browser_view_->RequestFocus();
}

void SDKWindowDelegate::OnWindowDestroyed(CefRefPtr<CefWindow> window)
{
	browser_view_ = nullptr;
}

bool SDKWindowDelegate::CanClose(CefRefPtr<CefWindow> window)
{
	SavePlacement(window);

	// Allow the window to close if the browser says it's OK.
	CefRefPtr<CefBrowser> browser = browser_view_->GetBrowser();
	if (browser)
		return browser->GetHost()->TryCloseBrowser();
	return true;
}

CefSize SDKWindowDelegate::GetMinimumSize(CefRefPtr<CefView> view)
{
	return CefSize(1360, 768);
}

void SDKWindowDelegate::LoadPlacement(CefRefPtr<CefWindow> window)
{
	DWORD dataSize = 0;
	if (RegGetValueW(HKEY_CURRENT_USER, L"SOFTWARE\\CitizenFX\\FxDK", placementRegistryKey.c_str(), RRF_RT_REG_BINARY, NULL, NULL, &dataSize) == ERROR_SUCCESS)
	{
		std::vector<uint8_t> data(dataSize);
		if (RegGetValueW(HKEY_CURRENT_USER, L"SOFTWARE\\CitizenFX\\FxDK", placementRegistryKey.c_str(), RRF_RT_REG_BINARY, NULL, data.data(), &dataSize) == ERROR_SUCCESS)
		{
			SetWindowPlacement(window->GetWindowHandle(), (const WINDOWPLACEMENT*)data.data());
		}
	}
}

void SDKWindowDelegate::SavePlacement(CefRefPtr<CefWindow> window)
{
	WINDOWPLACEMENT wndpl = { 0 };
	wndpl.length = sizeof(wndpl);

	if (GetWindowPlacement(window->GetWindowHandle(), &wndpl))
	{
		RegSetKeyValueW(HKEY_CURRENT_USER, L"SOFTWARE\\CitizenFX\\FxDK", placementRegistryKey.c_str(), REG_BINARY, &wndpl, sizeof(wndpl));
	}
}

SDKSubViewDelegate::SDKSubViewDelegate()
{
}

CefRefPtr<CefBrowserViewDelegate> SDKSubViewDelegate::GetDelegateForPopupBrowserView(
	CefRefPtr<CefBrowserView> browser_view,
	const CefBrowserSettings& settings,
	CefRefPtr<CefClient> client,
	bool is_devtools)
{
	return new SDKSubViewDelegate();
}

bool SDKSubViewDelegate::OnPopupBrowserViewCreated(
	CefRefPtr<CefBrowserView> browser_view,
	CefRefPtr<CefBrowserView> popup_browser_view,
	bool is_devtools)
{
	std::string forceTitle;

	if (is_devtools)
	{
		forceTitle = "FxDK: DevTools";
	}

	CefRefPtr<CefWindow> wnd = CefWindow::CreateTopLevelWindow(new SDKWindowDelegate(popup_browser_view, L"Last Window Placement Subview", forceTitle));

	return true;
}
