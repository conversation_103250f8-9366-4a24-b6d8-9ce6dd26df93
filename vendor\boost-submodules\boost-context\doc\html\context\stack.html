<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=US-ASCII">
<title>Stack allocation</title>
<link rel="stylesheet" href="../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../index.html" title="Chapter&#160;1.&#160;Context">
<link rel="up" href="../index.html" title="Chapter&#160;1.&#160;Context">
<link rel="prev" href="cc/class__continuation_.html" title="Class continuation">
<link rel="next" href="stack/protected_fixedsize.html" title="Class protected_fixedsize">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../boost.png"></td>
<td align="center"><a href="../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="cc/class__continuation_.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../index.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="stack/protected_fixedsize.html"><img src="../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="context.stack"></a><a name="stack"></a><a class="link" href="stack.html" title="Stack allocation">Stack allocation</a>
</h2></div></div></div>
<div class="toc"><dl>
<dt><span class="section"><a href="stack/protected_fixedsize.html">Class <span class="emphasis"><em>protected_fixedsize</em></span></a></span></dt>
<dt><span class="section"><a href="stack/pooled_fixedsize.html">Class <span class="emphasis"><em>pooled_fixedsize_stack</em></span></a></span></dt>
<dt><span class="section"><a href="stack/fixedsize.html">Class <span class="emphasis"><em>fixedsize_stack</em></span></a></span></dt>
<dt><span class="section"><a href="stack/segmented.html">Class
      <span class="emphasis"><em>segmented_stack</em></span></a></span></dt>
<dt><span class="section"><a href="stack/stack_traits.html">Class <span class="emphasis"><em>stack_traits</em></span></a></span></dt>
<dt><span class="section"><a href="stack/stack_context.html">Class <span class="emphasis"><em>stack_context</em></span></a></span></dt>
<dt><span class="section"><a href="stack/valgrind.html">Support for valgrind</a></span></dt>
<dt><span class="section"><a href="stack/sanitizers.html">Support for sanitizers</a></span></dt>
</dl></div>
<p>
      The memory used by the stack is allocated/deallocated via a <span class="emphasis"><em>StackAllocator</em></span>
      which is required to model a <span class="emphasis"><em>stack-allocator concept</em></span>.
    </p>
<h4>
<a name="context.stack.h0"></a>
      <span><a name="context.stack._emphasis_stack_allocator_concept__emphasis_"></a></span><a class="link" href="stack.html#context.stack._emphasis_stack_allocator_concept__emphasis_"><span class="emphasis"><em>stack-allocator
      concept</em></span></a>
    </h4>
<p>
      A <span class="emphasis"><em>StackAllocator</em></span> must satisfy the <span class="emphasis"><em>stack-allocator
      concept</em></span> requirements shown in the following table, in which <code class="computeroutput"><span class="identifier">a</span></code> is an object of a <span class="emphasis"><em>StackAllocator</em></span>
      type, <code class="computeroutput"><span class="identifier">sctx</span></code> is a <code class="computeroutput"><span class="identifier">stack_context</span></code>, and <code class="computeroutput"><span class="identifier">size</span></code>
      is a <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span></code>:
    </p>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              <p>
                expression
              </p>
            </th>
<th>
              <p>
                return type
              </p>
            </th>
<th>
              <p>
                notes
              </p>
            </th>
</tr></thead>
<tbody>
<tr>
<td>
              <p>
                <code class="computeroutput"><span class="identifier">a</span><span class="special">(</span><span class="identifier">size</span><span class="special">)</span></code>
              </p>
            </td>
<td>
            </td>
<td>
              <p>
                creates a stack allocator
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                <code class="computeroutput"><span class="identifier">a</span><span class="special">.</span><span class="identifier">allocate</span><span class="special">()</span></code>
              </p>
            </td>
<td>
              <p>
                <code class="computeroutput"><span class="identifier">stack_context</span></code>
              </p>
            </td>
<td>
              <p>
                creates a stack
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                <code class="computeroutput"><span class="identifier">a</span><span class="special">.</span><span class="identifier">deallocate</span><span class="special">(</span>
                <span class="identifier">sctx</span><span class="special">)</span></code>
              </p>
            </td>
<td>
              <p>
                <code class="computeroutput"><span class="keyword">void</span></code>
              </p>
            </td>
<td>
              <p>
                deallocates the stack created by <code class="computeroutput"><span class="identifier">a</span><span class="special">.</span><span class="identifier">allocate</span><span class="special">()</span></code>
              </p>
            </td>
</tr>
</tbody>
</table></div>
<div class="important"><table border="0" summary="Important">
<tr>
<td rowspan="2" align="center" valign="top" width="25"><img alt="[Important]" src="../../../../../doc/src/images/important.png"></td>
<th align="left">Important</th>
</tr>
<tr><td align="left" valign="top"><p>
        The implementation of <code class="computeroutput"><span class="identifier">allocate</span><span class="special">()</span></code> might include logic to protect against
        exceeding the context's available stack size rather than leaving it as undefined
        behaviour.
      </p></td></tr>
</table></div>
<div class="important"><table border="0" summary="Important">
<tr>
<td rowspan="2" align="center" valign="top" width="25"><img alt="[Important]" src="../../../../../doc/src/images/important.png"></td>
<th align="left">Important</th>
</tr>
<tr><td align="left" valign="top"><p>
        Calling <code class="computeroutput"><span class="identifier">deallocate</span><span class="special">()</span></code>
        with a <code class="computeroutput"><span class="identifier">stack_context</span></code> not
        set by <code class="computeroutput"><span class="identifier">allocate</span><span class="special">()</span></code>
        results in undefined behaviour.
      </p></td></tr>
</table></div>
<div class="note"><table border="0" summary="Note">
<tr>
<td rowspan="2" align="center" valign="top" width="25"><img alt="[Note]" src="../../../../../doc/src/images/note.png"></td>
<th align="left">Note</th>
</tr>
<tr><td align="left" valign="top"><p>
        Depending on the architecture <code class="computeroutput"><span class="identifier">allocate</span><span class="special">()</span></code> stores an address from the top of the stack
        (growing downwards) or the bottom of the stack (growing upwards).
      </p></td></tr>
</table></div>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright &#169; 2014 Oliver Kowalke<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="cc/class__continuation_.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../index.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="stack/protected_fixedsize.html"><img src="../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
