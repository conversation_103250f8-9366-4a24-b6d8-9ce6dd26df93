<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=US-ASCII">
<title>Statistical Distribution Explorer</title>
<link rel="stylesheet" href="../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="index.html" title="Statistical Distribution Explorer">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../boost.png"></td>
<td align="center"><a href="../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav"></div>
<div class="article">
<div class="titlepage">
<div>
<div><h2 class="title">
<a name="statistical_distribution_explorer"></a>Statistical Distribution Explorer</h2></div>
<div><div class="authorgroup">
<div class="author"><h3 class="author">
<span class="firstname">Paul A.</span> <span class="surname">Bristow</span>
</h3></div>
<div class="author"><h3 class="author">
<span class="firstname">John</span> <span class="surname">Maddock</span>
</h3></div>
</div></div>
<div><p class="copyright">Copyright &#169; 2008 Paul A. Bristow, John Maddock</p></div>
<div><div class="legalnotice">
<a name="id759711"></a><p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></div>
</div>
<hr>
</div>
<p>
    A Windows utility to show the properties of statistical distributions using parameters
    provided interactively by the user.
  </p>
<p>
    The distributions provided are:
  </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc">
<li class="listitem">
        bernoulli
      </li>
<li class="listitem">
        beta_distribution
      </li>
<li class="listitem">
        binomial_distribution
      </li>
<li class="listitem">
        cauchy
      </li>
<li class="listitem">
        chi_squared
      </li>
<li class="listitem">
        exponential
      </li>
<li class="listitem">
        extreme_value
      </li>
<li class="listitem">
        fisher_f
      </li>
<li class="listitem">
        gamma_distribution
      </li>
<li class="listitem">
        lognormal_distribution
      </li>
<li class="listitem">
        negative_binomial_distribution
      </li>
<li class="listitem">
        normal_distribution
      </li>
<li class="listitem">
        pareto
      </li>
<li class="listitem">
        poisson
      </li>
<li class="listitem">
        rayleigh
      </li>
<li class="listitem">
        students_t
      </li>
<li class="listitem">
        triangular
      </li>
<li class="listitem">
        uniform
      </li>
<li class="listitem">
        weibull
      </li>
</ul></div>
<p>
    Properties of distributions computed are:
  </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc">
<li class="listitem">
        mean
      </li>
<li class="listitem">
        mode
      </li>
<li class="listitem">
        median
      </li>
<li class="listitem">
        variance
      </li>
<li class="listitem">
        standard deviation
      </li>
<li class="listitem">
        coefficient of variation,
      </li>
<li class="listitem">
        skewness
      </li>
<li class="listitem">
        kurtosis
      </li>
<li class="listitem">
        excess
      </li>
<li class="listitem">
        range supported
      </li>
</ul></div>
<p>
    Calculated, from values provided, are:
  </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc">
<li class="listitem">
        probability density (or mass) function (PDF)
      </li>
<li class="listitem">
        cumulative distribution function (CDF), and complement
      </li>
<li class="listitem">
        Quantiles (percentiles) are calculated for typical risk (alpha) probabilities
        (0.001, 0.01, 0.5, 0.1, 0.333) and for additional probabilities provided
        by the user.
      </li>
</ul></div>
<p>
    Results can be saved to text files using Save or SaveAs. All the values on the
    four tabs are output to the file chosen, and are tab separated to assist input
    to other programs, for example, spreadsheets or text editors.
  </p>
<p>
    Note: Excel (for example), only shows 10 decimal digits, by default: to display
    the maximum possible precision (about 15 decimal digits), it is necessary to
    format all cells to display this precision. Although unusually accurate, not
    all values computed by Distexplorer will be as accurate as this. Values shown
    as NaN cannot be calculated from the value(s) given, most commonly because the
    value input is outside the range for the distribution.
  </p>
<p>
    For more information, including downloads, see
  </p>
<p>
    <a href="http://distexplorer.sourceforge.net/" target="_top">Distexplorer at Sourceforge</a>
  </p>
<p>
    This Microsoft Windows 32 package distribution.exe was generated from a C# program
    and uses a boost_math.dll generated using the Boost.Math C++ source code from
    the Boost.Math Toolkit, compiled in CLI mode, containing the underlying statistical
    distribution classes and functions.
  </p>
<p>
    All source code is freely available for view and use under the <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">Boost
    Open Source License</a>.
  </p>
<p>
    <a href="https://svn.boost.org/svn/boost/sandbox%5Cmath_toolkit%5Clibs%5Cmath%5Cdot_net_example" target="_top">Math
    Toolkit C++ source code</a> to produce boost_math.dll is in the most recent
    <a href="http://www.boost.org" target="_top">Boost</a> release, initially 1.35.0.
  </p>
<p>
    It is distributed as a single Windows Installer package Setupdistex.msi. Unzip
    the distexplorer.zip to a temporary location of your choice and run setup.exe.
  </p>
<p>
    (Note that .NET framework 2.0 and VCredist are requirements for this program.
    Most recent and updated Windows environments will already have these, but they
    are quickly, easily and safely installed from the Microsoft site if required.)
  </p>
<p>
    (The package cannot be run on other platforms at present but it should be possible
    to build an equivalent utility on any C/C++ platform if anyone would like to
    undertake this task.)
  </p>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"><p><small>Last revised: July 01, 2010 at 21:45:23 GMT</small></p></td>
<td align="right"><div class="copyright-footer"></div></td>
</tr></table>
<hr>
<div class="spirit-nav"></div>
</body>
</html>
