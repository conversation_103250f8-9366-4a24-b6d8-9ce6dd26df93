# Copyright 2017 The Chromium Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

mojom = "//mojo/public/mojom/base/read_only_buffer.mojom"
public_headers = [ "//base/containers/span.h" ]
traits_headers = [ "//mojo/public/cpp/base/read_only_buffer_mojom_traits.h" ]
public_deps = [
  "//base",
  "//mojo/public/cpp/base:shared_typemap_traits",
]

type_mappings = [ "mojo_base.mojom.ReadOnlyBuffer=::base::span<const ::uint8_t>[copyable_pass_by_value,force_serialize]" ]
