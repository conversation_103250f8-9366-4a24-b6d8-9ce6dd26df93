# Copyright (c) 2013 The Chromium Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

static_library("xdg_mime") {
  visibility = [ "//base/*" ]
  sources = [
    "xdgmime.c",
    "xdgmime.h",
    "xdgmimealias.c",
    "xdgmimealias.h",
    "xdgmimecache.c",
    "xdgmimecache.h",
    "xdgmimeglob.c",
    "xdgmimeglob.h",
    "xdgmimeicon.c",
    "xdgmimeicon.h",
    "xdgmimeint.c",
    "xdgmimeint.h",
    "xdgmimemagic.c",
    "xdgmimemagic.h",
    "xdgmimeparent.c",
    "xdgmimeparent.h",
  ]

  configs -= [ "//build/config/compiler:chromium_code" ]
  configs += [ "//build/config/compiler:no_chromium_code" ]
}
