[/
Copyright 2023 <PERSON>
(<EMAIL>)

Distributed under the Boost Software License, Version 1.0.
(http://www.boost.org/LICENSE_1_0.txt)
]

[section:size size]

[simplesect Authors]

* <PERSON>

[endsimplesect]

[section Overview]

The header <boost/core/size.hpp> provides function templates `size` to obtain
the number of elements in a range.

[endsect]

[section Reference]

```
namespace boost {

template<class C>
constexpr auto
size(const C& c) noexcept(noexcept(c.size())) -> decltype(c.size());

template<class T, std::size_t N>
constexpr std::size_t
size(T(&)[N]) noexcept;

} /* boost */
```

[section Functions]

[variablelist
[[`template<class C> constexpr auto size(const C& c)
noexcept(noexcept(c.size())) -> decltype(c.size());`]
[Returns `c.size()`.]]
[[`template<class T, std::size_t N> constexpr std::size_t size(T(&)[N])
noexcept;`]
[Returns `N`.]]]

[endsect]

[endsect]

[endsect]
