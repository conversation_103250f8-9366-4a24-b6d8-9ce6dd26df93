@use "sass:math";

@import "variables";

.root {
  display: flex;
  align-items: center;
  justify-content: center;

  padding: math.div($q, 1.5) $q*2;

  font-size: $fs08;
  font-weight: 100;
  letter-spacing: .5px;

  color: $fgColor;
  background-color: mix($bgColor, $fgColor, 75%);

  border-radius: 2px;

  text-shadow: 0 1px 1px $bgColor;
  box-shadow: 0 -1px rgba($bgColor, .5) inset, 0 1px rgba($fgColor, .25) inset, 0 0 5px rgba($bgColor, .25);

  user-select: none;
}
