// (C) Copyright <PERSON><PERSON> 2017.
// Distributed under the Boost Software License, Version 1.0. (See
// accompanying file LICENSE_1_0.txt or copy at
// http://www.boost.org/LICENSE_1_0.txt)

#ifndef BOOST_FUNCTION_OUTPUT_ITERATOR_HPP
#define BOOST_FUNCTION_OUTPUT_ITERATOR_HPP

// This is a deprecated header left for backward compatibility.
// Use boost/iterator/function_output_iterator.hpp instead.

#include <boost/config/header_deprecated.hpp>

BOOST_HEADER_DEPRECATED("<boost/iterator/function_output_iterator.hpp>")

#include <boost/iterator/function_output_iterator.hpp>

#endif // BOOST_FUNCTION_OUTPUT_ITERATOR_HPP
