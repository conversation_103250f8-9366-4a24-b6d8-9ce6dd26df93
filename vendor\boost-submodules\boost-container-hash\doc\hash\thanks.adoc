////
Copyright 2005-2008 <PERSON>
Copyright 2022 <PERSON>
Copyright 2022 <PERSON>
Distributed under the Boost Software License, Version 1.0.
https://www.boost.org/LICENSE_1_0.txt
////

[#thanks]
= Acknowledgements
:idprefix: thanks_

This library is based on the design by <PERSON>. During the initial development <PERSON><PERSON><PERSON><PERSON> made many useful suggestions and contributed fixes.

The formal review was managed by <PERSON><PERSON>, and the library reviewed by: <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON> and <PERSON>. Since then, further constructive criticism has been made by <PERSON>, <PERSON> and 沈慧峰.

The implementation of the hash function for pointers is based on suggestions made by <PERSON> and <PERSON>. <PERSON> also suggested an important improvement to `boost::hash_combine` that was taken up.

Some useful improvements to the floating point hash algorithm were suggested by <PERSON>.

The original implementation came from <PERSON>'s hash table library, although this is a complete rewrite.

The documentation was converted from Quickbook to AsciiDoc by <PERSON>.
