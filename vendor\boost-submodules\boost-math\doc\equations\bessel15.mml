<math  xmlns="http://www.w3.org/1998/Math/MathML" display="block" >
   <mtable columnalign="left" class="align">
      <mtr>
         <mtd columnalign="right" class="align-odd">
            <msub>
               <mrow >
                  <mi >Y</mi>
               </mrow>
               <mrow >
                  <mi >&#x03BC;</mi>
               </mrow>
            </msub >
         </mtd>
         <mtd class="align-even">
            <mo class="MathClass-rel">=</mo>
            <mo class="MathClass-bin">&#x2212;</mo>
            <munderover accentunder="false" accent="false">
               <mrow  >
                  <mo mathsize="big" >&#x2211;</mo>
               </mrow>
               <mrow >
                  <mi >k</mi>
                  <mo class="MathClass-rel">=</mo>
                  <mn>0</mn>
               </mrow>
               <mrow >
                  <mi >&#x221E;</mi>
               </mrow>
            </munderover >
            <msub>
               <mrow >
                  <mi >c</mi>
               </mrow>
               <mrow >
                  <mi >k</mi>
               </mrow>
            </msub >
            <msub>
               <mrow >
                  <mi >g</mi>
               </mrow>
               <mrow >
                  <mi >k</mi>
               </mrow>
            </msub >
            <mspace width="2em"/>
         </mtd>
         <mtd columnalign="right" class="align-label"></mtd>
         <mtd class="align-label">
            <mspace width="2em"/>
         </mtd>
      </mtr>
      <mtr>
         <mtd columnalign="right" class="align-odd">
            <msub>
               <mrow >
                  <mi >Y</mi>
               </mrow>
               <mrow >
                  <mi >&#x03BC;</mi>
                  <mo class="MathClass-bin">+</mo>
                  <mn>1</mn>
               </mrow>
            </msub >
         </mtd>
         <mtd class="align-even">
            <mo class="MathClass-rel">=</mo>
            <mo class="MathClass-bin">&#x2212;</mo>
            <mfrac>
               <mrow >
                  <mn>2</mn>
               </mrow>
               <mrow >
                  <mi >x</mi>
               </mrow>
            </mfrac>
            <munderover accentunder="false" accent="false">
               <mrow  >
                  <mo mathsize="big" >&#x2211;</mo>
               </mrow>
               <mrow >
                  <mi >k</mi>
                  <mo class="MathClass-rel">=</mo>
                  <mn>0</mn>
               </mrow>
               <mrow >
                  <mi >&#x221E;</mi>
               </mrow>
            </munderover >
            <msub>
               <mrow >
                  <mi >c</mi>
               </mrow>
               <mrow >
                  <mi >k</mi>
               </mrow>
            </msub >
            <msub>
               <mrow >
                  <mi >h</mi>
               </mrow>
               <mrow >
                  <mi >k</mi>
               </mrow>
            </msub >
            <mspace width="2em"/>
         </mtd>
         <mtd columnalign="right" class="align-label"></mtd>
         <mtd class="align-label">
            <mspace width="2em"/>
         </mtd>
      </mtr>
   </mtable>
</math>