// Copyright 2014 The Chromium Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     base/android/jni_generator/jni_generator.py
// For
//     foo/bar

#ifndef foo_bar_JNI
#define foo_bar_JNI

#include <jni.h>

#include "base/android/jni_generator/jni_generator_helper.h"


// Step 1: Forward declarations.

JNI_REGISTRATION_EXPORT extern const char kClassPath_foo_bar[];
const char kClassPath_foo_bar[] = "foo/bar";
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_REGISTRATION_EXPORT std::atomic<jclass> g_foo_bar_clazz(nullptr);
#ifndef foo_bar_clazz_defined
#define foo_bar_clazz_defined
inline jclass foo_bar_clazz(JNIEnv* env) {
  return base::android::LazyGetClass(env, kClassPath_foo_bar, &g_foo_bar_clazz);
}
#endif


// Step 2: Constants (optional).


// Step 3: Method stubs.
static void JNI_bar_SyncSetupEnded(JNIEnv* env, const base::android::JavaParamRef<jobject>& jcaller,
    jint nativeAndroidSyncSetupFlowHandler);

JNI_GENERATOR_EXPORT void Java_foo_bar_nativeSyncSetupEnded(
    JNIEnv* env,
    jobject jcaller,
    jint nativeAndroidSyncSetupFlowHandler) {
  return JNI_bar_SyncSetupEnded(env, base::android::JavaParamRef<jobject>(env, jcaller),
      nativeAndroidSyncSetupFlowHandler);
}


// Step 4: Generated test functions (optional).


#endif  // foo_bar_JNI
