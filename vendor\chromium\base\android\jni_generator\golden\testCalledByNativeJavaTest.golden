// Copyright 2014 The Chromium Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     base/android/jni_generator/jni_generator.py
// For
//     org/chromium/TestJni

#ifndef org_chromium_TestJni_JNI
#define org_chromium_TestJni_JNI

#include <jni.h>

#include "base/android/jni_generator/jni_generator_helper.h"


// Step 1: Forward declarations.

JNI_REGISTRATION_EXPORT extern const char kClassPath_org_chromium_TestJni[];
const char kClassPath_org_chromium_TestJni[] = "org/chromium/TestJni";

JNI_REGISTRATION_EXPORT extern const char kClassPath_org_chromium_TestJni_00024MyInnerClass[];
const char kClassPath_org_chromium_TestJni_00024MyInnerClass[] =
    "org/chromium/TestJni$MyInnerClass";
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_REGISTRATION_EXPORT std::atomic<jclass> g_org_chromium_TestJni_clazz(nullptr);
#ifndef org_chromium_TestJni_clazz_defined
#define org_chromium_TestJni_clazz_defined
inline jclass org_chromium_TestJni_clazz(JNIEnv* env) {
  return base::android::LazyGetClass(env, kClassPath_org_chromium_TestJni,
      &g_org_chromium_TestJni_clazz);
}
#endif
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_REGISTRATION_EXPORT std::atomic<jclass> g_org_chromium_TestJni_00024MyInnerClass_clazz(nullptr);
#ifndef org_chromium_TestJni_00024MyInnerClass_clazz_defined
#define org_chromium_TestJni_00024MyInnerClass_clazz_defined
inline jclass org_chromium_TestJni_00024MyInnerClass_clazz(JNIEnv* env) {
  return base::android::LazyGetClass(env, kClassPath_org_chromium_TestJni_00024MyInnerClass,
      &g_org_chromium_TestJni_00024MyInnerClass_clazz);
}
#endif


// Step 2: Constants (optional).


// Step 3: Method stubs.

static std::atomic<jmethodID> g_org_chromium_TestJni_Constructor(nullptr);
static base::android::ScopedJavaLocalRef<jobject> Java_TestJni_Constructor(JNIEnv* env) {
  jclass clazz = org_chromium_TestJni_clazz(env);
  CHECK_CLAZZ(env, clazz,
      org_chromium_TestJni_clazz(env), NULL);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "<init>",
          "()V",
          &g_org_chromium_TestJni_Constructor);

  jobject ret =
      env->NewObject(clazz,
          call_context.base.method_id);
  return base::android::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_org_chromium_TestJni_testFoo(nullptr);
static jint Java_TestJni_testFoo(JNIEnv* env, const base::android::JavaRef<jobject>& obj) {
  jclass clazz = org_chromium_TestJni_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_chromium_TestJni_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "testFoo",
          "()I",
          &g_org_chromium_TestJni_testFoo);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_org_chromium_TestJni_testOtherFoo(nullptr);
static void Java_TestJni_testOtherFoo(JNIEnv* env, const base::android::JavaRef<jobject>& obj) {
  jclass clazz = org_chromium_TestJni_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_chromium_TestJni_clazz(env));

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "testOtherFoo",
          "()V",
          &g_org_chromium_TestJni_testOtherFoo);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id);
}

static std::atomic<jmethodID>
    g_org_chromium_TestJni_testLongNameActionServiceModelProducerDelegateProxyObserverMediatorFactoryConsumerImplForTesting(nullptr);
static void
    Java_TestJni_testLongNameActionServiceModelProducerDelegateProxyObserverMediatorFactoryConsumerImplForTesting(JNIEnv*
    env, const base::android::JavaRef<jobject>& obj) {
  jclass clazz = org_chromium_TestJni_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_chromium_TestJni_clazz(env));

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
"testLongNameActionServiceModelProducerDelegateProxyObserverMediatorFactoryConsumerImplForTesting",
          "()V",
&g_org_chromium_TestJni_testLongNameActionServiceModelProducerDelegateProxyObserverMediatorFactoryConsumerImplForTesting);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id);
}

static std::atomic<jmethodID> g_org_chromium_TestJni_00024MyInnerClass_testInnerFoo(nullptr);
static void Java_MyInnerClass_testInnerFoo(JNIEnv* env, const base::android::JavaRef<jobject>& obj)
    {
  jclass clazz = org_chromium_TestJni_00024MyInnerClass_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_chromium_TestJni_00024MyInnerClass_clazz(env));

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "testInnerFoo",
          "()V",
          &g_org_chromium_TestJni_00024MyInnerClass_testInnerFoo);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id);
}

// Step 4: Generated test functions (optional).
#define JAVA_TESTS(test_fixture, java_test_object)\
  TEST_F(test_fixture, TestFoo) { \
    JNIEnv* env = base::android::AttachCurrentThread(); \
    Java_TestJni_testFoo(\
        env, java_test_object); \
  }\
  TEST_F(test_fixture, TestOtherFoo) { \
    JNIEnv* env = base::android::AttachCurrentThread(); \
    Java_TestJni_testOtherFoo(\
        env, java_test_object); \
  }\
  TEST_F(test_fixture, TestLongNameActionServiceModelProducerDelegateProxyObserverMediatorFactoryConsumerImplForTesting) { \
    JNIEnv* env = base::android::AttachCurrentThread(); \
    Java_TestJni_testLongNameActionServiceModelProducerDelegateProxyObserverMediatorFactoryConsumerImplForTesting(\
        env, java_test_object); \
  }\
  TEST_F(test_fixture, TestInnerFoo) { \
    JNIEnv* env = base::android::AttachCurrentThread(); \
    Java_MyInnerClass_testInnerFoo(\
        env, java_test_object); \
  }

#endif  // org_chromium_TestJni_JNI
