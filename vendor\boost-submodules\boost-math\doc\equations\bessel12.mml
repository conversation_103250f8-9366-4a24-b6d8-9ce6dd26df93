<math  xmlns="http://www.w3.org/1998/Math/MathML" display="block" ><mrow >    <!--mstyle class="mbox"--><mtext class="textrm" mathvariant="normal" >&#x00A0;CF2</mtext><!--/mstyle--> <mo class="MathClass-punc">:</mo>   <mi >p</mi> <mo class="MathClass-bin">+</mo> <mi >i</mi><mi >q</mi> <mo class="MathClass-rel">=</mo> <mfrac><mrow ><msubsup><mrow ><mi >J</mi></mrow><mrow ><mi >&#x03BD;</mi></mrow><mrow ><mi >&#x2032;</mi></mrow></msubsup > <mo class="MathClass-bin">+</mo> <mi >i</mi><msubsup><mrow ><mi >Y</mi> </mrow><mrow ><mi >&#x03BD;</mi></mrow><mrow ><mi >&#x2032;</mi></mrow></msubsup ></mrow> <mrow ><msub><mrow ><mi >J</mi></mrow><mrow ><mi >&#x03BD;</mi></mrow></msub > <mo class="MathClass-bin">+</mo> <mi >i</mi><msub><mrow ><mi >Y</mi> </mrow><mrow ><mi >&#x03BD;</mi></mrow></msub ></mrow></mfrac>  <mo class="MathClass-rel">=</mo> <mfenced separators="" open="("  close=")" ><mrow><mi >i</mi> <mo class="MathClass-bin">&#x2212;</mo> <mfrac><mrow ><mn>1</mn></mrow> <mrow ><mn>2</mn><mi >x</mi></mrow></mfrac></mrow></mfenced> <mo class="MathClass-bin">+</mo> <mfrac><mrow ><mi >i</mi></mrow> <mrow ><mi >x</mi></mrow></mfrac> <mfenced separators="" open="["  close="]" ><mrow> <mfrac><mrow ><msup><mrow > <mfenced separators="" open="("  close=")" ><mrow><mfrac><mrow ><mn>1</mn></mrow><mrow ><mn>2</mn></mrow></mfrac></mrow></mfenced> </mrow><mrow ><mn>2</mn></mrow></msup > <mo class="MathClass-bin">&#x2212;</mo> <msup><mrow ><mi >&#x03BD;</mi></mrow><mrow ><mn>2</mn></mrow></msup ></mrow><mrow ><mn>2</mn><mrow ><mo class="MathClass-open">(</mo><mrow><mi >x</mi> <mo class="MathClass-bin">+</mo> <mi >i</mi></mrow><mo class="MathClass-close">)</mo></mrow> <mo class="MathClass-bin">+</mo> </mrow></mfrac>    <mfrac><mrow ><msup><mrow > <mfenced separators="" open="("  close=")" ><mrow><mfrac><mrow ><mn>3</mn></mrow><mrow ><mn>2</mn></mrow></mfrac></mrow></mfenced> </mrow><mrow ><mn>2</mn></mrow></msup > <mo class="MathClass-bin">&#x2212;</mo> <msup><mrow ><mi >&#x03BD;</mi></mrow><mrow ><mn>2</mn></mrow></msup ></mrow> <mrow ><mn>2</mn><mrow ><mo class="MathClass-open">(</mo><mrow><mi >x</mi> <mo class="MathClass-bin">+</mo> <mn>2</mn><mi >i</mi></mrow><mo class="MathClass-close">)</mo></mrow> <mo class="MathClass-bin">+</mo> </mrow></mfrac> <mo class="MathClass-bin">&#x22C5;</mo><mo class="MathClass-bin">&#x22C5;</mo><mo class="MathClass-bin">&#x22C5;</mo></mrow></mfenced></mrow></math>