<?xml version="1.0" encoding="utf-8"?>
<svg:svg xmlns="http://www.w3.org/1998/Math/MathML" xmlns:svg="http://www.w3.org/2000/svg" height="19.781250pt" width="196.177110pt" xmlns:svgmath="http://www.grigoriev.ru/svgmath" viewBox="0 -13.693359 196.177110 19.781250"><svg:metadata><svgmath:metrics top="19.78125" bottom="0.0" baseline="6.087890625" axis="10.072265625"/></svg:metadata><svg:text font-size="12.000000" text-anchor="middle" y="0.000000" x="3.665039" font-family="Times New Roman" fill="black">if</svg:text><svg:g transform="translate(10.548832, 0.000000)"/><svg:g transform="translate(22.548832, 0.000000)"><svg:text font-size="12.000000" text-anchor="middle" y="0.000000" x="3.000000" font-family="Times New Roman" font-style="italic" fill="black">a</svg:text></svg:g><svg:g transform="translate(31.882168, -3.984375)"><svg:text font-size="12.000000" text-anchor="middle" y="3.984375" x="3.383789" font-family="Times New Roman" fill="black">=</svg:text></svg:g><svg:g transform="translate(41.983082, 0.000000)"><svg:text font-size="12.000000" text-anchor="middle" y="0.000000" x="3.000000" font-family="Times New Roman" fill="black">1</svg:text></svg:g><svg:g transform="translate(47.983082, 0.000000)"/><svg:g transform="translate(59.983082, 0.000000)"/><svg:g transform="translate(73.983086, 0.000000)"><svg:text font-size="12.000000" text-anchor="middle" y="0.000000" x="11.997070" font-family="Times New Roman" fill="black">then:</svg:text></svg:g><svg:g transform="translate(99.977231, 0.000000)"/><svg:g transform="translate(111.977231, 0.000000)"/><svg:g transform="translate(125.977235, 0.000000)"><svg:text font-size="12.000000" text-anchor="middle" y="0.000000" x="10.022461" font-family="Times New Roman" fill="black">beta</svg:text></svg:g><svg:g transform="translate(147.992864, 0.000000)"><svg:g transform="translate(0.000000, -3.984375)"><svg:text font-size="12.012889" transform="scale(0.998927, 1)" text-anchor="middle" y="3.981593" x="2.000193" font-family="Times New Roman" fill="black">(</svg:text></svg:g><svg:g transform="translate(3.996094, 0.000000)"><svg:text font-size="12.000000" text-anchor="middle" y="0.000000" x="3.000000" font-family="Times New Roman" font-style="italic" fill="black">a</svg:text><svg:g transform="translate(6.000000, -3.984375)"><svg:text font-size="12.000000" text-anchor="middle" y="3.984375" x="1.500000" font-family="Times New Roman" fill="black">,</svg:text></svg:g><svg:g transform="translate(12.999996, 0.000000)"><svg:text font-size="12.000000" text-anchor="middle" y="0.000000" x="3.000000" font-family="Times New Roman" font-style="italic" fill="black">b</svg:text></svg:g></svg:g><svg:g transform="translate(22.996090, -3.984375)"><svg:text font-size="12.012889" transform="scale(0.998927, 1)" text-anchor="middle" y="3.981593" x="2.000193" font-family="Times New Roman" fill="black">)</svg:text></svg:g></svg:g><svg:g transform="translate(178.318383, -3.984375)"><svg:text font-size="12.000000" text-anchor="middle" y="3.984375" x="3.383789" font-family="Times New Roman" fill="black">=</svg:text></svg:g><svg:g transform="translate(189.005235, -3.984375)"><svg:g transform="translate(0.585938, -1.599609)"><svg:text font-size="12.000000" text-anchor="middle" y="0.000000" x="3.000000" font-family="Times New Roman" fill="black">1</svg:text></svg:g><svg:g transform="translate(0.585938, 9.931641)"><svg:text font-size="12.000000" text-anchor="middle" y="0.000000" x="3.000000" font-family="Times New Roman" font-style="italic" fill="black">b</svg:text></svg:g><svg:line y2="0.000000" stroke-width="0.585938" x2="7.171875" stroke="black" stroke-linecap="butt" stroke-dasharray="none" y1="0.000000" x1="0.000000" fill="none"/></svg:g></svg:svg>