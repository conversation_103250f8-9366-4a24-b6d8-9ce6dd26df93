/*
 * StandaloneMode.cpp - Xử lý chế độ standalone cho GangHaiCity
 * Cho phép executable chạy độc lập mà không cần dependencies
 */

#include "StdInc.h"
#include <windows.h>
#include <string>

// Kiểm tra xem có đang chạy ở standalone mode không
bool IsStandaloneMode()
{
    // Kiểm tra xem có file CoreRT.dll trong cùng thư mục không
    wchar_t exePath[MAX_PATH];
    GetModuleFileNameW(NULL, exePath, MAX_PATH);
    
    std::wstring exeDir = std::wstring(exePath);
    size_t lastSlash = exeDir.find_last_of(L"\\");
    if (lastSlash != std::wstring::npos)
    {
        exeDir = exeDir.substr(0, lastSlash + 1);
        std::wstring coreRTPath = exeDir + L"CoreRT.dll";
        
        // Nếu không có CoreRT.dll thì là standalone mode
        return GetFileAttributesW(coreRTPath.c_str()) == INVALID_FILE_ATTRIBUTES;
    }
    return true;
}

// Window procedure cho giao diện tùy chỉnh
LRESULT CALLBACK CustomWindowProc(HWND hwnd, UINT msg, WPARAM wParam, LPARAM lParam)
{
    switch (msg)
    {
    case WM_CREATE:
    {
        // Tạo các control cho giao diện
        CreateWindowW(L"BUTTON", L"Khởi động Game", 
                     WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                     50, 50, 200, 40, hwnd, (HMENU)1001, 
                     GetModuleHandle(NULL), NULL);
                     
        CreateWindowW(L"BUTTON", L"Cài đặt", 
                     WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                     50, 100, 200, 40, hwnd, (HMENU)1002, 
                     GetModuleHandle(NULL), NULL);
                     
        CreateWindowW(L"BUTTON", L"Thoát", 
                     WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                     50, 150, 200, 40, hwnd, (HMENU)1003, 
                     GetModuleHandle(NULL), NULL);
                     
        CreateWindowW(L"STATIC", L"GangHaiCity - Phiên bản tùy chỉnh", 
                     WS_VISIBLE | WS_CHILD | SS_LEFT,
                     50, 220, 400, 30, hwnd, NULL, 
                     GetModuleHandle(NULL), NULL);
        return 0;
    }
    
    case WM_COMMAND:
    {
        switch (LOWORD(wParam))
        {
        case 1001: // Khởi động Game
            MessageBoxW(hwnd, L"Chức năng khởi động game sẽ được thêm vào đây.\nBạn có thể tùy chỉnh để mở game hoặc launcher khác.", 
                       L"GangHaiCity", MB_OK | MB_ICONINFORMATION);
            break;
            
        case 1002: // Cài đặt
            MessageBoxW(hwnd, L"Cửa sổ cài đặt sẽ được thêm vào đây.\nBạn có thể tùy chỉnh các tùy chọn game.", 
                       L"Cài đặt", MB_OK | MB_ICONINFORMATION);
            break;
            
        case 1003: // Thoát
            PostQuitMessage(0);
            break;
        }
        return 0;
    }
    
    case WM_PAINT:
    {
        PAINTSTRUCT ps;
        HDC hdc = BeginPaint(hwnd, &ps);
        
        // Vẽ background gradient
        RECT rect;
        GetClientRect(hwnd, &rect);
        
        HBRUSH brush = CreateSolidBrush(RGB(25, 25, 35));
        FillRect(hdc, &rect, brush);
        DeleteObject(brush);
        
        // Vẽ tiêu đề
        SetTextColor(hdc, RGB(255, 255, 255));
        SetBkMode(hdc, TRANSPARENT);
        
        HFONT font = CreateFontW(24, 0, 0, 0, FW_BOLD, FALSE, FALSE, FALSE,
                                DEFAULT_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS,
                                DEFAULT_QUALITY, DEFAULT_PITCH | FF_SWISS, L"Arial");
        HFONT oldFont = (HFONT)SelectObject(hdc, font);
        
        TextOutW(hdc, 50, 10, L"GangHaiCity Launcher", 20);
        
        SelectObject(hdc, oldFont);
        DeleteObject(font);
        
        EndPaint(hwnd, &ps);
        return 0;
    }
    
    case WM_DESTROY:
        PostQuitMessage(0);
        return 0;
        
    default:
        return DefWindowProcW(hwnd, msg, wParam, lParam);
    }
}

// Hàm hiển thị giao diện tùy chỉnh standalone
int ShowStandaloneInterface()
{
    // Đăng ký window class
    WNDCLASSW wc = {};
    wc.lpfnWndProc = CustomWindowProc;
    wc.hInstance = GetModuleHandle(NULL);
    wc.lpszClassName = L"GangHaiCityStandalone";
    wc.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
    wc.hCursor = LoadCursor(NULL, IDC_ARROW);
    wc.hIcon = LoadIcon(NULL, IDI_APPLICATION);
    
    if (!RegisterClassW(&wc))
    {
        MessageBoxW(NULL, L"Không thể đăng ký window class!", L"Lỗi", MB_OK | MB_ICONERROR);
        return 1;
    }
    
    // Tạo cửa sổ chính
    HWND hwnd = CreateWindowW(
        L"GangHaiCityStandalone",
        L"GangHaiCity - Standalone Mode",
        WS_OVERLAPPEDWINDOW,
        CW_USEDEFAULT, CW_USEDEFAULT,
        500, 350,
        NULL, NULL, GetModuleHandle(NULL), NULL
    );
    
    if (!hwnd)
    {
        MessageBoxW(NULL, L"Không thể tạo cửa sổ!", L"Lỗi", MB_OK | MB_ICONERROR);
        return 1;
    }
    
    // Hiển thị cửa sổ
    ShowWindow(hwnd, SW_SHOW);
    UpdateWindow(hwnd);
    
    // Message loop
    MSG msg;
    while (GetMessage(&msg, NULL, 0, 0))
    {
        TranslateMessage(&msg);
        DispatchMessage(&msg);
    }
    
    return 0;
}

// Hàm khởi tạo standalone mode
int InitStandaloneMode()
{
    // Hiển thị thông báo chào mừng
    int result = MessageBoxW(NULL, 
        L"GangHaiCity đang chạy ở chế độ Standalone.\n\n"
        L"Chế độ này cho phép bạn chạy ứng dụng mà không cần các file dependencies khác.\n\n"
        L"Bạn có muốn tiếp tục không?",
        L"GangHaiCity - Standalone Mode",
        MB_YESNO | MB_ICONQUESTION);
    
    if (result == IDNO)
    {
        return 0;
    }
    
    // Khởi tạo và hiển thị giao diện
    return ShowStandaloneInterface();
}
