<math xmlns="http://www.w3.org/1998/Math/MathML" display="block">
  <mrow>
    <mtext>if</mtext>
    <mspace width="1em"/>
    <mi>a</mi>
    <mo>=</mo>
    <mn>1</mn>
    <mspace width="1em"/>
    <mspace width="1em"/>
    <mtext>then:</mtext>
    <mspace width="1em"/>
    <mspace width="1em"/>
    <mtext>beta</mtext>
    <mfenced>
      <mrow>
        <mi>a</mi>
        <mo>,</mo>
        <mi>b</mi>
      </mrow>
    </mfenced>
    <mo>=</mo>
    <mfrac>
      <mn>1</mn>
      <mi>b</mi>
    </mfrac>
  </mrow>
</math>

