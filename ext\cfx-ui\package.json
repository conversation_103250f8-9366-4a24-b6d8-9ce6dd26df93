{"name": "cfxuirnw", "version": "0.0.1", "private": true, "scripts": {"lint": "eslint --quiet \"src/**/*{ts,tsx}\"", "lint:fix": "eslint --fix --quiet \"src/**/*{ts,tsx}\"", "watch": "webpack --config config/webpack.config.js --watch --mode development --env app=mpMenu", "build": "webpack --config config/webpack.config.js --mode production --env app=mpMenu", "serve": "webpack serve --config config/webpack.config.js --mode development --env app=mpMenu", "test": "jest"}, "dependencies": {"@cfx-dev/ui-components": "1.0.2", "@sentry/react": "^7.14.0", "@sentry/tracing": "^7.14.0", "adaptivecards": "^2.10.0", "blurhash": "^1.1.5", "color": "^4.2.1", "date-fns": "^2.28.0", "dexie": "^3.2.2", "domhandler": "^5.0.3", "emoji-regex": "^10.0.0", "emoji.json": "^13.1.0", "howler": "^2.2.3", "html-entities": "^2.3.2", "html-react-parser": "^3.0.1", "idb": "^7.1.0", "inversify": "^6.0.1", "js-md5": "^0.7.3", "markdown-it": "^13.0.1", "mobx": "^6.6.1", "mobx-react-lite": "^3.4.0", "mobx-utils": "^6.0.5", "node-forge": "^1.2.1", "pdfjs-dist": "3.2.146", "protobufjs": "^6.11.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-focus-lock": "^2.9.1", "react-icons": "^4.4.0", "react-router-dom": "^6.4.3", "reflect-metadata": "^0.1.13", "swiper": "^8.2.6", "typeid-js": "^0.3.0"}, "devDependencies": {"@cfx-dev/eslint-config-ui": "^0.0.11", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.5", "@sentry/types": "^7.14.0", "@sentry/webpack-plugin": "^1.19.0", "@swc/core": "^1.3.6", "@swc/jest": "^0.2.23", "@types/color": "^3.0.3", "@types/howler": "^2.2.7", "@types/jest": "^29.1.2", "@types/markdown-it": "^12.2.3", "@types/node-forge": "^1.0.0", "@types/offscreencanvas": "^2019.7.0", "@types/react": "^18.0.15", "@types/react-dom": "^18.0.6", "@types/react-window": "^1.8.5", "cldr-core": "^40.0.0", "cldr-localenames-modern": "^40.0.0", "cldrjs": "^0.5.5", "copy-webpack-plugin": "^13.0.0", "css-loader": "^5.1.3", "eslint": "^8.57.0", "file-loader": "^6.2.0", "fork-ts-checker-webpack-plugin": "^7.2.13", "html-webpack-plugin": "^5.5.0", "image-minimizer-webpack-plugin": "^3.3.0", "imagemin-gifsicle": "^7.0.0", "img-optimize-loader": "^1.0.7", "jest": "^29.1.2", "mini-css-extract-plugin": "^2.6.1", "react-refresh": "^0.11.0", "react-refresh-typescript": "^2.0.9", "sass": "^1.45.1", "sass-loader": "^12.4.0", "sharp": "^0.30.7", "source-map-loader": "^3.0.1", "style-loader": "^3.3.1", "ts-loader": "^9.4.4", "typescript": "^5.2.2", "typescript-plugin-css-modules": "^3.4.0", "webpack": "^5.75.0", "webpack-cli": "^5.0.0", "webpack-dev-server": "^4.11.1"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}