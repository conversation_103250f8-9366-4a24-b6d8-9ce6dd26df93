---
ns: CFX
apiset: client
game: gta5
---
## SET_ENTITY_MATRIX

```c
void SET_ENTITY_MATRIX(Entity entity, float forwardX, float forwardY, float forwardZ, float rightX, float rightY, float rightZ, float upX, float upY, float upZ, float atX, float atY, float atZ);
```

Sets an entity's matrix. Arguments are in the same order as with GET_ENTITY_MATRIX.

## Parameters
* **entity**: A valid entity handle.
* **forwardX**:
* **forwardY**:
* **forwardZ**:
* **rightX**:
* **rightY**:
* **rightZ**:
* **upX**:
* **upY**:
* **upZ**:
* **atX**:
* **atY**:
* **atZ**:
