// Copyright 2018 The Chromium Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

package org.chromium.base.natives;

// This file is autogenerated by
//     base/android/jni_generator/jni_registration_generator.py
// Please do not change its content.

public class GEN_JNI {
  public static final boolean TESTING_ENABLED = true;
  public static final boolean REQUIRE_MOCK = false;

  public static native void org_chromium_example_jni_1generator_SampleForAnnotationProcessor_foo();

  public static native Object org_chromium_example_jni_1generator_SampleForAnnotationProcessor_bar(Object sample);

  public static native String org_chromium_example_jni_1generator_SampleForAnnotationProcessor_revString(String stringToReverse);

  public static native String[] org_chromium_example_jni_1generator_SampleForAnnotationProcessor_sendToNative(String[] strs);

  public static native Object[] org_chromium_example_jni_1generator_SampleForAnnotationProcessor_sendSamplesToNative(Object[] strs);

  public static native boolean org_chromium_example_jni_1generator_SampleForAnnotationProcessor_hasPhalange();

  public static native int[] org_chromium_example_jni_1generator_SampleForAnnotationProcessor_testAllPrimitives(int zint, int[] ints, long zlong, long[] longs, short zshort, short[] shorts, char zchar, char[] chars, byte zbyte, byte[] bytes, double zdouble, double[] doubles, float zfloat, float[] floats, boolean zbool, boolean[] bools);

  public static native void org_chromium_example_jni_1generator_SampleForAnnotationProcessor_testSpecialTypes(Class clazz, Class[] classes, Throwable throwable, Throwable[] throwables, String string, String[] strings, Object tStruct, Object[] structs, Object obj, Object[] objects);

  public static native Throwable org_chromium_example_jni_1generator_SampleForAnnotationProcessor_returnThrowable();

  public static native Throwable[] org_chromium_example_jni_1generator_SampleForAnnotationProcessor_returnThrowables();

  public static native Class org_chromium_example_jni_1generator_SampleForAnnotationProcessor_returnClass();

  public static native Class[] org_chromium_example_jni_1generator_SampleForAnnotationProcessor_returnClasses();

  public static native String org_chromium_example_jni_1generator_SampleForAnnotationProcessor_returnString();

  public static native String[] org_chromium_example_jni_1generator_SampleForAnnotationProcessor_returnStrings();

  public static native Object org_chromium_example_jni_1generator_SampleForAnnotationProcessor_returnStruct();

  public static native Object[] org_chromium_example_jni_1generator_SampleForAnnotationProcessor_returnStructs();

  public static native Object org_chromium_example_jni_1generator_SampleForAnnotationProcessor_returnObject();

  public static native Object[] org_chromium_example_jni_1generator_SampleForAnnotationProcessor_returnObjects();

}
