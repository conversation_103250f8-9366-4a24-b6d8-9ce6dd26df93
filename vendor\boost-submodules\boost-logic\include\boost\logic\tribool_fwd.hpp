// Three-state boolean logic library

// Copyright Douglas <PERSON> 2002-2004. Use, modification and
// distribution is subject to the Boost Software License, Version
// 1.0. (See accompanying file LICENSE_1_0.txt or copy at
// http://www.boost.org/LICENSE_1_0.txt)


// For more information, see http://www.boost.org
#ifndef BOOST_LOGIC_TRIBOOL_FWD_HPP
#define BOOST_LOGIC_TRIBOOL_FWD_HPP

namespace boost { namespace logic { class tribool; } }

#endif // BOOST_LOGIC_TRIBOOL_FWD_HPP
