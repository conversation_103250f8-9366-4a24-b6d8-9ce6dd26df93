// Copyright 2014 The Chromium Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     base/android/jni_generator/jni_generator.py
// For
//     org/chromium/foo/Foo

#ifndef org_chromium_foo_Foo_JNI
#define org_chromium_foo_Foo_JNI

#include <jni.h>

#include "base/android/jni_generator/jni_generator_helper.h"


// Step 1: Forward declarations.

JNI_REGISTRATION_EXPORT extern const char kClassPath_org_chromium_foo_Foo[];
const char kClassPath_org_chromium_foo_Foo[] = "org/chromium/foo/Foo";
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_REGISTRATION_EXPORT std::atomic<jclass> g_org_chromium_foo_Foo_clazz(nullptr);
#ifndef org_chromium_foo_Foo_clazz_defined
#define org_chromium_foo_Foo_clazz_defined
inline jclass org_chromium_foo_Foo_clazz(JNIEnv* env) {
  return base::android::LazyGetClass(env, kClassPath_org_chromium_foo_Foo,
      &g_org_chromium_foo_Foo_clazz);
}
#endif


// Step 2: Constants (optional).


// Step 3: Method stubs.
JNI_GENERATOR_EXPORT void Java_org_chromium_foo_Foo_nativeInstanceMethod(
    JNIEnv* env,
    jobject jcaller,
    jlong nativeInstance) {
  TRACE_EVENT0("jni", "Instance::InstanceMethod");
  Instance* native = reinterpret_cast<Instance*>(nativeInstance);
  CHECK_NATIVE_PTR(env, jcaller, native, "InstanceMethod");
  return native->InstanceMethod(env, base::android::JavaParamRef<jobject>(env, jcaller));
}

static void JNI_Foo_StaticMethod(JNIEnv* env);

JNI_GENERATOR_EXPORT void Java_org_chromium_foo_Foo_nativeStaticMethod(
    JNIEnv* env,
    jclass jcaller) {
  TRACE_EVENT0("jni", "JNI_Foo_StaticMethod");
  return JNI_Foo_StaticMethod(env);
}

static void JNI_Foo_Foo(JNIEnv* env);

JNI_GENERATOR_EXPORT void Java_org_chromium_base_natives_GEN_1JNI_org_1chromium_1foo_1Foo_1foo(
    JNIEnv* env,
    jclass jcaller) {
  TRACE_EVENT0("jni", "JNI_Foo_Foo");
  return JNI_Foo_Foo(env);
}

static base::android::ScopedJavaLocalRef<jstring> JNI_Foo_Bar(JNIEnv* env, const
    base::android::JavaParamRef<jstring>& s,
    jint y,
    jchar x,
    jshort z);

JNI_GENERATOR_EXPORT jstring Java_org_chromium_base_natives_GEN_1JNI_org_1chromium_1foo_1Foo_1bar(
    JNIEnv* env,
    jclass jcaller,
    jstring s,
    jint y,
    jchar x,
    jshort z) {
  TRACE_EVENT0("jni", "JNI_Foo_Bar");
  return JNI_Foo_Bar(env, base::android::JavaParamRef<jstring>(env, s), y, x, z).Release();
}

static base::android::ScopedJavaLocalRef<jobjectArray> JNI_Foo_Foobar(JNIEnv* env, const
    base::android::JavaParamRef<jobjectArray>& a);

JNI_GENERATOR_EXPORT jobjectArray
    Java_org_chromium_base_natives_GEN_1JNI_org_1chromium_1foo_1Foo_1foobar(
    JNIEnv* env,
    jclass jcaller,
    jobjectArray a) {
  TRACE_EVENT0("jni", "JNI_Foo_Foobar");
  return JNI_Foo_Foobar(env, base::android::JavaParamRef<jobjectArray>(env, a)).Release();
}

JNI_GENERATOR_EXPORT void Java_org_chromium_base_natives_GEN_1JNI_org_1chromium_1foo_1Foo_1baz(
    JNIEnv* env,
    jclass jcaller,
    jlong nativePtr,
    jobject caller) {
  TRACE_EVENT0("jni", "Ptr::Baz");
  Ptr* native = reinterpret_cast<Ptr*>(nativePtr);
  CHECK_NATIVE_PTR(env, jcaller, native, "Baz");
  return native->Baz(env, base::android::JavaParamRef<jobject>(env, caller));
}

JNI_GENERATOR_EXPORT void Java_org_chromium_base_natives_GEN_1JNI_org_1chromium_1foo_1Foo_1fooBar(
    JNIEnv* env,
    jclass jcaller,
    jlong nativePtr) {
  TRACE_EVENT0("jni", "Ptr::FooBar");
  Ptr* native = reinterpret_cast<Ptr*>(nativePtr);
  CHECK_NATIVE_PTR(env, jcaller, native, "FooBar");
  return native->FooBar(env);
}


// Step 4: Generated test functions (optional).


#endif  // org_chromium_foo_Foo_JNI
