AUTOMAKE_OPTIONS = foreign no-dependencies

ACLOCAL_AMFLAGS = -I m4

# This is the point release for libevent.  It shouldn't include any
# a/b/c/d/e notations.
RELEASE = 1.4

# This is the version info for the libevent binary API.  It has three
# numbers:
#   Current  -- the number of the binary API that we're implementing
#   Revision -- which iteration of the implementation of the binary
#               API are we supplying?
#   Age      -- How many previous binary API versions do we also
#               support?
#
# If we release a new version that does not change the binary API,
# increment Revision.
#
# If we release a new version that changes the binary API, but does
# not break programs compiled against the old binary API, increment
# Current and Age.  Set Revision to 0, since this is the first
# implementation of the new API.
#
# Otherwise, we're changing the binary API and breaking bakward
# compatibility with old binaries.  Increment Current.  Set Age to 0,
# since we're backward compatible with no previous APIs.  Set Revision
# to 0 too.
VERSION_INFO = 4:1:2

###
# History:
#   We started using Libtool around version 1.0d.  For all versions from
#   1.0d through 1.3e, we set RELEASE to the version name, and
#   VERSION_INFO to something haphazard.  The didn't matter, since
#   setting RELEASE meant that no version of Libevent was treated as
#   binary-compatible with any other version.
#
#   As of 1.4.0-beta, we set RELEASE to "1.4", so that releases in the
#   1.4.x series could be potentially binary-compatible with one another,
#   but not with any other series.  (They aren't.)  We didn't necessarily
#   set VERSION_INFO correctly, or update it as often as we should have.
#   The VERSION_INFO values were:
#    1.4.0-beta .. 1.4.4-stable     : 2:0:0   [See note 1]
#    1.4.5-stable                   : 3:0:1   (compatible ABI change)
#    1.4.6-stable                   : 3:1:1   (no ABI change)
#    1.4.7-stable                   : 3:1:1   [see note 1]
#    1.4.8-stable                   : 3:2:1   (no ABI change)
#    1.4.9-stable                   : 3:2:1   [see note 1]
#    1.4.10-stable                  : 3:3:1   (no ABI change)
#    1.4.11-stable .. 1.4.13-stable : 3:3:1   [see note 1]
#    1.4.14a-stable:                : 3:3:2   [see note 2]
#    1.4.14b-stable:                : 4:0:2   (compatible ABI change)
#    1.4.15-stable:                 : 4:1:2   (no ABI change)
#
# [1]: Using the same VERSION_INFO value was wrong; we should have been
#      updating the Revision field.
# [2]: We set the VERSION_INFO completely wrong on 1.4.14b-stable

bin_SCRIPTS = event_rpcgen.py

EXTRA_DIST = autogen.sh event.h event-internal.h log.h evsignal.h evdns.3 \
	evrpc.h evrpc-internal.h min_heap.h \
	event.3 \
	Doxyfile \
	kqueue.c epoll_sub.c epoll.c select.c poll.c signal.c \
	evport.c devpoll.c event_rpcgen.py \
	sample/Makefile.am sample/Makefile.in sample/event-test.c \
	sample/signal-test.c sample/time-test.c \
	test/Makefile.am test/Makefile.in test/bench.c test/regress.c \
	test/test-eof.c test/test-weof.c test/test-time.c \
	test/test-init.c test/test.sh \
	compat/sys/queue.h compat/sys/_libevent_time.h \
	WIN32-Code/config.h \
	WIN32-Code/event-config.h \
	WIN32-Code/win32.c \
	WIN32-Code/tree.h \
	WIN32-Prj/event_test/event_test.dsp \
	WIN32-Prj/event_test/test.txt WIN32-Prj/libevent.dsp \
	WIN32-Prj/libevent.dsw WIN32-Prj/signal_test/signal_test.dsp \
	WIN32-Prj/time_test/time_test.dsp WIN32-Prj/regress/regress.vcproj \
	WIN32-Prj/libevent.sln WIN32-Prj/libevent.vcproj \
	Makefile.nmake test/Makefile.nmake \
	LICENSE

lib_LTLIBRARIES = libevent.la libevent_core.la libevent_extra.la

if BUILD_WIN32

SUBDIRS = . sample
SYS_LIBS = -lws2_32
SYS_SRC = WIN32-Code/win32.c
SYS_INCLUDES = -IWIN32-Code

else

SUBDIRS = . sample test
SYS_LIBS =
SYS_SRC =
SYS_INCLUDES =

endif

BUILT_SOURCES = event-config.h

event-config.h: config.h
	echo '/* event-config.h' > $@
	echo ' * Generated by autoconf; post-processed by libevent.' >> $@
	echo ' * Do not edit this file.' >> $@
	echo ' * Do not rely on macros in this file existing in later versions.'>> $@
	echo ' */' >> $@
	echo '#ifndef _EVENT_CONFIG_H_' >> $@
	echo '#define _EVENT_CONFIG_H_' >> $@

	sed -e 's/#define /#define _EVENT_/' \
	    -e 's/#undef /#undef _EVENT_/' \
	    -e 's/#ifndef /#ifndef _EVENT_/' < config.h >> $@
	echo "#endif" >> $@

CORE_SRC = event.c buffer.c evbuffer.c log.c evutil.c $(SYS_SRC)
EXTRA_SRC = event_tagging.c http.c evhttp.h http-internal.h evdns.c \
	evdns.h evrpc.c evrpc.h evrpc-internal.h \
	strlcpy.c strlcpy-internal.h strlcpy-internal.h

libevent_la_SOURCES = $(CORE_SRC) $(EXTRA_SRC)
libevent_la_LIBADD = @LTLIBOBJS@ $(SYS_LIBS)
libevent_la_LDFLAGS = -release $(RELEASE) -version-info $(VERSION_INFO)

libevent_core_la_SOURCES = $(CORE_SRC)
libevent_core_la_LIBADD = @LTLIBOBJS@ $(SYS_LIBS)
libevent_core_la_LDFLAGS = -release $(RELEASE) -version-info $(VERSION_INFO)

libevent_extra_la_SOURCES = $(EXTRA_SRC)
libevent_extra_la_LIBADD = @LTLIBOBJS@ $(SYS_LIBS)
libevent_extra_la_LDFLAGS = -release $(RELEASE) -version-info $(VERSION_INFO)

include_HEADERS = event.h evhttp.h evdns.h evrpc.h evutil.h

nodist_include_HEADERS = event-config.h

INCLUDES = -I$(srcdir)/compat $(SYS_INCLUDES)

man_MANS = event.3 evdns.3

verify: libevent.la
	cd test && make verify

doxygen: FORCE
	doxygen $(srcdir)/Doxyfile
FORCE:

DISTCLEANFILES = *~ event-config.h
