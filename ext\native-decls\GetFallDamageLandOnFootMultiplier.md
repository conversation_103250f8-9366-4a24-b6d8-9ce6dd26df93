---
ns: CFX
apiset: client
game: gta5
---
## GET_FALL_DAMAGE_LAND_ON_FOOT_MULTIPLIER

```c
float GET_FALL_DAMAGE_LAND_ON_FOOT_MULTIPLIER();
```

A getter for [SET_FALL_DAMAGE_LAND_ON_FOOT_MULTIPLIER](#_0x164A08C9).

## Return value
Returns the fall damage multiplier applied when a ped lands **on foot** from a fall below the kill fall height threshold (i.e., when the fall does not cause instant death).
The default value is `3.0`.