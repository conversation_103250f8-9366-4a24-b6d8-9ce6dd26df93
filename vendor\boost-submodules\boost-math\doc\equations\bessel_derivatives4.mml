<?xml version="1.0"?>
<math xmlns="http://www.w3.org/1998/Math/MathML">
 <mrow>
    <mtable columnalign="right center left">
    <mtr>
     <mtd>
      <mrow>
       <mi>J</mi>
       <msub>
        <mi>'</mi>
        <mi>v</mi>
       </msub>
       <mrow>
        <mo stretchy="false">(</mo>
        <mrow>
         <mi>x</mi>
        </mrow>
        <mo stretchy="false">)</mo>
       </mrow>
      </mrow>
     </mtd>
     <mtd>
      <mrow>
       <mtext>=</mtext>
      </mrow>
     </mtd>
     <mtd>
      <mrow>
       <msub>
        <mi>N</mi>
        <mi>v</mi>
       </msub>
       <mi>cos</mi>
       <msub>
        <mo stretchy="false">ϕ</mo>
        <mi>v</mi>
       </msub>
      </mrow>
     </mtd>
    </mtr>
    <mtr>
     <mtd>
      <mrow>
       <mi>Y</mi>
       <msub>
        <mi>'</mi>
        <mi>v</mi>
       </msub>
       <mrow>
        <mo stretchy="false">(</mo>
        <mrow>
         <mi>x</mi>
        </mrow>
        <mo stretchy="false">)</mo>
       </mrow>
      </mrow>
     </mtd>
     <mtd>
      <mrow>
       <mtext>=</mtext>
      </mrow>
     </mtd>
     <mtd>
      <mrow>
       <msub>
        <mi>N</mi>
        <mi>v</mi>
       </msub>
       <mi>sin</mi>
       <msub>
        <mo stretchy="false">ϕ</mo>
        <mi>v</mi>
       </msub>
      </mrow>
     </mtd>
    </mtr>
    <mtr>
     <mtd>
      <mrow>
       <msubsup>
        <mi>N</mi>
        <mi>v</mi>
        <mn>2</mn>
       </msubsup>
      </mrow>
     </mtd>
     <mtd>
      <mrow>
       <mrow>
        <mrow/>
        <mo stretchy="false">≈</mo>
        <mrow/>
       </mrow>
      </mrow>
     </mtd>
     <mtd>
      <mrow>
       <mrow>
        <mfrac>
         <mn>2</mn>
         <mrow>
          <mo stretchy="false">π</mo>
          <mi>x</mi>
         </mrow>
        </mfrac>
       </mrow>
       <mfenced open="{" close="}">
        <mrow>
         <mrow>
          <mn>1</mn>
          <mo stretchy="false">−</mo>
          <mrow>
           <mfrac>
            <mn>1</mn>
            <mn>2</mn>
           </mfrac>
          </mrow>
         </mrow>
         <mrow>
          <mfrac>
           <mrow>
            <mrow>
             <mo stretchy="false">μ</mo>
             <mo stretchy="false">−</mo>
             <mn>3</mn>
            </mrow>
           </mrow>
           <mrow>
            <msup>
             <mrow>
              <mo stretchy="false">(</mo>
              <mrow>
               <mn>2x</mn>
              </mrow>
              <mo stretchy="false">)</mo>
             </mrow>
             <mn>2</mn>
            </msup>
           </mrow>
          </mfrac>
          <mo stretchy="false">−</mo>
          <mrow>
           <mfrac>
            <mn>1</mn>
            <mn>8</mn>
           </mfrac>
          </mrow>
         </mrow>
         <mfrac>
          <mrow>
           <mrow>
            <mo stretchy="false">(</mo>
            <mrow>
             <mrow>
              <mo stretchy="false">μ</mo>
              <mo stretchy="false">−</mo>
              <mn>1</mn>
             </mrow>
            </mrow>
            <mo stretchy="false">)</mo>
           </mrow>
           <mrow>
            <mo stretchy="false">(</mo>
            <mrow>
             <mrow>
              <mo stretchy="false">μ</mo>
              <mo stretchy="false">−</mo>
              <mn>45</mn>
             </mrow>
            </mrow>
            <mo stretchy="false">)</mo>
           </mrow>
          </mrow>
          <mrow>
           <mrow>
            <msup>
             <mrow>
              <mo stretchy="false">(</mo>
              <mrow>
               <mn>2x</mn>
              </mrow>
              <mo stretchy="false">)</mo>
             </mrow>
             <mn>4</mn>
            </msup>
           </mrow>
          </mrow>
         </mfrac>
        </mrow>
       </mfenced>
      </mrow>
     </mtd>
    </mtr>
    <mtr>
     <mtd>
      <mrow>
       <mrow>
        <msub>
         <mo stretchy="false">ϕ</mo>
         <mi>v</mi>
        </msub>
       </mrow>
      </mrow>
     </mtd>
     <mtd>
      <mrow>
       <mrow>
        <mrow/>
        <mo stretchy="false">≈</mo>
        <mrow/>
       </mrow>
      </mrow>
     </mtd>
     <mtd>
      <mrow>
       <mrow>
        <mrow>
         <mi>x</mi>
         <mo stretchy="false">−</mo>
         <mrow>
          <mo stretchy="false">(</mo>
          <mrow>
           <mfrac>
            <mn>1</mn>
            <mn>2</mn>
           </mfrac>
           <mrow>
            <mi>v</mi>
            <mo stretchy="false">−</mo>
            <mfrac>
             <mn>1</mn>
             <mn>4</mn>
            </mfrac>
           </mrow>
          </mrow>
          <mo stretchy="false">)</mo>
         </mrow>
        </mrow>
        <mrow>
         <mrow>
          <mrow>
           <mo stretchy="false">π</mo>
           <mo stretchy="false">+</mo>
           <mfrac>
            <mrow>
             <mrow>
              <mo stretchy="false">μ</mo>
              <mo stretchy="false">+</mo>
              <mn>3</mn>
             </mrow>
            </mrow>
            <mrow>
             <mn>2</mn>
             <mrow>
              <mo stretchy="false">(</mo>
              <mrow>
               <mn>4x</mn>
              </mrow>
              <mo stretchy="false">)</mo>
             </mrow>
            </mrow>
           </mfrac>
          </mrow>
          <mo stretchy="false">+</mo>
          <mfrac>
           <mrow>
            <mrow>
             <msup>
              <mo stretchy="false">μ</mo>
              <mn>2</mn>
             </msup>
             <mo stretchy="false">+</mo>
             <mn>46</mn>
            </mrow>
            <mrow>
             <mo stretchy="false">μ</mo>
             <mo stretchy="false">−</mo>
             <mn>63</mn>
            </mrow>
           </mrow>
           <mrow>
            <mn>6</mn>
            <msup>
             <mrow>
              <mo stretchy="false">(</mo>
              <mrow>
               <mn>4x</mn>
              </mrow>
              <mo stretchy="false">)</mo>
             </mrow>
             <mn>3</mn>
            </msup>
           </mrow>
          </mfrac>
         </mrow>
         <mo stretchy="false">+</mo>
         <mfrac>
          <mrow>
           <mrow>
            <msup>
             <mo stretchy="false">μ</mo>
             <mn>3</mn>
            </msup>
            <mo stretchy="false">+</mo>
            <mn>185</mn>
           </mrow>
           <mrow>
            <msup>
             <mo stretchy="false">μ</mo>
             <mn>2</mn>
            </msup>
            <mo stretchy="false">−</mo>
            <mn>2053</mn>
           </mrow>
           <mrow>
            <mo stretchy="false">μ</mo>
            <mo stretchy="false">+</mo>
            <mn>1899</mn>
           </mrow>
          </mrow>
          <mrow>
           <mn>5</mn>
           <msup>
            <mrow>
             <mo stretchy="false">(</mo>
             <mrow>
              <mn>4x</mn>
             </mrow>
             <mo stretchy="false">)</mo>
            </mrow>
            <mn>5</mn>
           </msup>
          </mrow>
         </mfrac>
        </mrow>
       </mrow>
      </mrow>
     </mtd>
    </mtr>
    <mtr>
     <mtd>
      <mrow>
       <mrow>
        <mo stretchy="false">μ</mo>
       </mrow>
      </mrow>
     </mtd>
     <mtd>
      <mrow>
       <mrow>
        <mrow/>
        <mo stretchy="false">=</mo>
        <mrow/>
       </mrow>
      </mrow>
     </mtd>
     <mtd>
      <mrow>
       <mrow>
        <msup>
         <mn>4v</mn>
         <mn>2</mn>
        </msup>
       </mrow>
      </mrow>
     </mtd>
    </mtr>
   </mtable>
  </mrow>
</math>
