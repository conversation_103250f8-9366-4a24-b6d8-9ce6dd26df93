[/
  Copyright 2010, 2011 <PERSON><PERSON>
  Copyright 2013 Ion <PERSON>ga
  Copyright 2014-2019 <PERSON>
  Copyright 2017 <PERSON><PERSON><PERSON>

  Distributed under the Boost Software License, Version 1.0.

  See accompanying file LICENSE_1_0.txt
  or copy at http://boost.org/LICENSE_1_0.txt
]

[section:lightweight_test lightweight_test]

[simplesect Authors]

* <PERSON>v
* Be<PERSON>

[endsimplesect]

[section Header <boost/core/lightweight_test.hpp>]

The header `<boost/core/lightweight_test.hpp>` is a
lightweight test framework. It's useful for writing
Boost regression tests for components that are dependencies
of Boost.Test.

When using `lightweight_test.hpp`, *do not forget* to
`return boost::report_errors()` from `main`.

[section Synopsis]

``
#define BOOST_TEST(expression) /*unspecified*/
#define BOOST_TEST_NOT(expression) /*unspecified*/
#define BOOST_ERROR(message) /*unspecified*/
#define BOOST_TEST_EQ(expr1, expr2) /*unspecified*/
#define BOOST_TEST_NE(expr1, expr2) /*unspecified*/
#define BOOST_TEST_LT(expr1, expr2) /*unspecified*/
#define BOOST_TEST_LE(expr1, expr2) /*unspecified*/
#define BOOST_TEST_GT(expr1, expr2) /*unspecified*/
#define BOOST_TEST_GE(expr1, expr2) /*unspecified*/
#define BOOST_TEST_CSTR_EQ(expr1, expr2) /*unspecified*/
#define BOOST_TEST_CSTR_NE(expr1, expr2) /*unspecified*/
#define BOOST_TEST_WITH(expr1, expr2, pred) /*unspecified*/
#define BOOST_TEST_ALL_EQ(begin1, end1, begin2, end2) /* unspecified */
#define BOOST_TEST_ALL_WITH(begin1, end1, begin2, end2, predicate) /* unspecified */
#define BOOST_TEST_THROWS(expr, excep) /*unspecified*/
#define BOOST_TEST_NO_THROW(expr) /*unspecified*/

namespace boost
{

int report_errors();

namespace core
{

void lwt_init();

} // namespace core
} // namespace boost
``

[endsect]

[section BOOST_TEST]

``
BOOST_TEST(expression)
``

If expression is false increases the error count and outputs a
message containing `expression`.

[endsect]

[section BOOST_TEST_NOT]

``
BOOST_TEST_NOT(expression)
``

If expression is true increases the error count and outputs a
message containing `!(expression)`.

[endsect]

[section BOOST_ERROR]

``
BOOST_ERROR(message)
``

Increases error count and outputs a message containing
`message`.

[endsect]

[section BOOST_TEST_EQ]

``
BOOST_TEST_EQ(expr1, expr2)
``

If `expr1 == expr2` is not true increases the error count and outputs a
message containing both expressions.

[endsect]

[section BOOST_TEST_NE]

``
BOOST_TEST_NE(expr1, expr2)
``

If `expr1 != expr2` is not true increases the error count and outputs a
message containing both expressions.

[endsect]

[section BOOST_TEST_LT]

``
BOOST_TEST_LT(expr1, expr2)
``

If `expr1 < expr2` is not true increases the error count and outputs a
message containing both expressions.

[endsect]

[section BOOST_TEST_LE]

``
BOOST_TEST_LE(expr1, expr2)
``

If `expr1 <= expr2` is not true increases the error count and outputs a
message containing both expressions.

[endsect]

[section BOOST_TEST_GT]

``
BOOST_TEST_GT(expr1, expr2)
``

If `expr1 > expr2` is not true increases the error count and outputs a
message containing both expressions.

[endsect]

[section BOOST_TEST_GE]

``
BOOST_TEST_GE(expr1, expr2)
``

If `expr1 >= expr2` is not true increases the error count and outputs a
message containing both expressions.

[endsect]

[section BOOST_TEST_CSTR_EQ]

``
BOOST_TEST_CSTR_EQ(expr1, expr2)
``

Specialization of `BOOST_TEST_EQ` which interprets `expr1` and `expr2` as pointers to null-terminated byte strings (C strings). If `std::strcmp(expr1, expr2) != 0`, increase the error count and output a message containing both expressions.

[endsect]

[section BOOST_TEST_CSTR_NE]

``
BOOST_TEST_CSTR_NE(expr1, expr2)
``

Specialization of `BOOST_TEST_NE` which interprets `expr1` and `expr2` as pointers to null-terminated byte strings (C strings). If `std::strcmp(expr1, expr2) == 0`, increase the error count and output a message containing both expressions.

[endsect]

[section BOOST_TEST_WITH]

``
BOOST_TEST_WITH(expr1, expr2, pred)
``

If `pred(expr1, expr2)` is not true increases the error count and outputs a
message containing both expressions.

[endsect]

[section BOOST_TEST_ALL_EQ]

``
BOOST_TEST_ALL_EQ(begin1, end1, begin2, end2)
``

Compares the content of two sequences. If they have different sizes, or if any pairwise element differs, increases the error count and outputs a message containing at most 8 differing elements.

[endsect]

[section BOOST_TEST_ALL_WITH]

``
BOOST_TEST_ALL_WITH(begin1, end1, begin2, end2, predicate)
``

Compares the content of two sequences. If they have different sizes, or if any pairwise element do not fulfill the binary predicate, increases the error count and outputs a message containing at most 8 differing elements.

[endsect]

[section BOOST_TEST_THROWS]

``
BOOST_TEST_THROWS(expr, excep)
``

If `BOOST_NO_EXCEPTIONS` is *not* defined and if `expr` does not
throw an exception of type `excep`, increases the error count
and outputs a message containing the expression.

If `BOOST_NO_EXCEPTIONS` is defined, this macro expands to
nothing and `expr` is not evaluated.

[endsect]

[section BOOST_TEST_NO_THROW]

``
BOOST_TEST_NO_THROW(expr)
``

If `BOOST_NO_EXCEPTIONS` is *not* defined and if `expr` throws an exception,
increases the error count and outputs a message containing the expression
and (if possible) the exception message.

If `BOOST_NO_EXCEPTIONS` is defined, `expr` is evaluated.

[endsect]

[section report_errors]

``
int boost::report_errors()
``

Return the error count from `main`.

[endsect]

[section lwt_init]

``
void boost::core::lwt_init()
``

Performs one-time initialization. Disables the interactive message
boxes displayed by the Microsoft Windows debug runtime library on
`abort`, failing `assert`, and other abnormal program terminations
(to facilitate unattended testing), and ensures that in case
`boost::report_errors` is not called (a common mistake), the program
ends with a nonzero exit code.

`lwt_init` is automatically called by the test macros. There is
no need to call it explicitly, except in cases where a test fails due
to e.g. an assertion failure before the first test macro is invoked.

[endsect]

[section Example]

``
#include <boost/core/lightweight_test.hpp>

int sqr( int x )
{
    return x * x;
}

int main()
{
    BOOST_TEST( sqr(2) == 4 );
    BOOST_TEST_EQ( sqr(-3), 9 );

    return boost::report_errors();
}
``

[endsect]

[endsect]

[section Header <boost/core/lightweight_test_trait.hpp>]

The header `<boost/core/lightweight_test_trait.hpp>` defines
a couple of extra macros for testing compile-time traits that
return a boolean value.

[section Synopsis]

``
#define BOOST_TEST_TRAIT_TRUE((Trait)) /*unspecified*/
#define BOOST_TEST_TRAIT_FALSE((Trait)) /*unspecified*/
#define BOOST_TEST_TRAIT_SAME(Type1, Type2) /*unspecified*/
``

[endsect]

[section BOOST_TEST_TRAIT_TRUE]

``
BOOST_TEST_TRAIT_TRUE((Trait))
``

If `Trait::value != true` increases the error count and outputs a
message containing `Trait`. Note the double set of parentheses; these
enable `Trait` to contain a comma, which is common for templates.

[endsect]

[section BOOST_TEST_TRAIT_FALSE]

``
BOOST_TEST_TRAIT_FALSE((Trait))
``

If `Trait::value != false` increases the error count and outputs a
message containing `Trait`. Note the double set of parentheses.

[endsect]

[section BOOST_TEST_TRAIT_SAME]

``
BOOST_TEST_TRAIT_SAME(Type1, Type2)
``

If the two types are not the same, increases the error count and outputs a
message containing them. This macro requires that the compiler supports
variadic macros and `__VA_ARGS__`. (Note that unlike `BOOST_TEST_TRAIT_TRUE`
and `BOOST_TEST_TRAIT_FALSE`, this macro only requires a single set of
parentheses.)

[endsect]

[section Example]

``
#include <boost/core/lightweight_test_trait.hpp>
#include <boost/type_traits/is_same.hpp>

template<class T, class U> struct X
{
    typedef T type;
};

using boost::is_same;

int main()
{
    BOOST_TEST_TRAIT_TRUE(( is_same<X<int, long>::type, int> ));

    BOOST_TEST_TRAIT_SAME( X<int, long>::type, int );

    return boost::report_errors();
}
``

[endsect]

[endsect]

[endsect]
