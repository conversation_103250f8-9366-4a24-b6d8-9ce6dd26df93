////
Copyright 2005-2008 <PERSON>
Copyright 2022 <PERSON>
Copyright 2022 <PERSON>
Distributed under the Boost Software License, Version 1.0.
https://www.boost.org/LICENSE_1_0.txt
////

[#tutorial]
= Tutorial
:idprefix: tutorial_

When using a Boost container such as
link:../../../unordered/index.html[Boost.Unordered], you don't need to do
anything to use `boost::hash` as it's the default. To find out how to use
a user-defined type, read the <<user,section on extending boost::hash
for user types>>.

If you wish to use `boost::hash` with the standard unordered associative
containers, pass it as a template parameter:

[source]
----
std::unordered_multiset<int, boost::hash<int> >
        set_of_ints;

std::unordered_set<std::pair<int, int>, boost::hash<std::pair<int, int> > >
        set_of_pairs;

std::unordered_map<int, std::string, boost::hash<int> > map_int_to_string;
----

To use `boost::hash` directly, create an instance and call it as a function:

[source]
----
#include <boost/container_hash/hash.hpp>

int main()
{
    boost::hash<std::string> string_hash;
    std::size_t h = string_hash("Hash me");
}
----

or alternatively:

[source]
----
#include <boost/container_hash/hash.hpp>

int main()
{
    std::size_t h = boost::hash<std::string>()("Hash me");
}
----

For an example of generic use, here is a function to generate a vector
containing the hashes of the elements of a container:

[source]
----
template <class Container>
std::vector<std::size_t> get_hashes(Container const& x)
{
    std::vector<std::size_t> hashes;
    std::transform(x.begin(), x.end(), std::back_inserter(hashes),
        boost::hash<typename Container::value_type>());

    return hashes;
}
----
