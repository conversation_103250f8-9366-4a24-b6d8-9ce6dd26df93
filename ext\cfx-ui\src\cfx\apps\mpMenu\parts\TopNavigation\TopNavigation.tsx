import React from 'react';
import { observer } from 'mobx-react-lite';
import { useNavigate } from 'react-router-dom';
import { createPortal } from 'react-dom';
import { useCustomAuthService } from 'cfx/apps/mpMenu/services/customAuth/customAuth.service';

import s from './TopNavigation.module.scss';
// Import logo image using require with proper path
const logoImageUrl = require('../../../../../assets/images/GANGHAI.png');

interface NavigationItem {
  label: string;
  path: string;
  external?: boolean;
}

const navigationItems: NavigationItem[] = [
  { label: 'Home', path: '/' },
  { label: 'About', path: '/about' },
  { label: 'Services', path: '/services' },
  { label: 'Contact', path: '/contact' }
];

export const TopNavigation = observer(function TopNavigation() {
  const navigate = useNavigate();
  const customAuthService = useCustomAuthService();
  const [currentPath, setCurrentPath] = React.useState(window.location.pathname);

  // Track current path for active states
  React.useEffect(() => {
    const handleLocationChange = () => {
      setCurrentPath(window.location.pathname);
    };

    window.addEventListener('popstate', handleLocationChange);
    return () => window.removeEventListener('popstate', handleLocationChange);
  }, []);

  const handleNavigation = (path: string, external?: boolean) => {
    if (external) {
      window.open(path, '_blank');
    } else {
      navigate(path);
      setCurrentPath(path);
    }
  };

  const handleLogout = () => {
    customAuthService.logout();
    // Navigate to home page after logout
    navigate('/');
    setCurrentPath('/');
  };

  // Create navigation content
  const navigationContent = (
    <nav className={s.topNavigation}>
      <div className={s.container}>
        {/* Logo Section */}
        <div className={s.logo} onClick={() => handleNavigation('/')}>
          <img
            src={logoImageUrl}
            alt="Logo"
            className={s.logoImage}
          />
        </div>

        {/* Navigation Links */}
        <div className={s.navigationLinks}>
          {navigationItems.map((item) => (
            <button
              key={item.path}
              className={`${s.navLink} ${currentPath === item.path ? s.active : ''}`}
              onClick={() => handleNavigation(item.path, item.external)}
            >
              {item.label}
            </button>
          ))}
        </div>

        {/* User Section - Show user info and logout when authenticated */}
        <div className={s.userSection}>
          {customAuthService.isAuthenticated && customAuthService.user ? (
            <div className={s.userInfo}>
              <span className={s.userName}>
                Xin chào, {customAuthService.user.username}
              </span>
              <button
                className={s.logoutButton}
                onClick={handleLogout}
                title="Đăng xuất"
              >
                Đăng Xuất
              </button>
            </div>
          ) : null}
        </div>
      </div>
    </nav>
  );

  // Use Portal to render outside of #cfxui-root to ensure it appears above fullscreen background
  return createPortal(navigationContent, document.body);
});
