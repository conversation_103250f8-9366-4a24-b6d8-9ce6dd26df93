[/
 / Copyright (c) 2022 <PERSON><PERSON>
 /
 / Distributed under the Boost Software License, Version 1.0. (See accompanying
 / file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
 /]

[section:fclose_deleter fclose_deleter]

[simplesect Authors]

* <PERSON><PERSON>

[endsimplesect]

[section Header <boost/core/fclose_deleter.hpp>]

The header `<boost/core/fclose_deleter.hpp>` defines the `boost::fclose_deleter` function object,
which can be used as a deleter with smart pointers such as `unique_ptr` or `shared_ptr` pointing to `std::FILE`.
structures returned by `std::fopen` calls. The deleter calls `std::fclose` on the passed pointer, causing
the file stream to be flushed and closed.

[section Example]
``
std::unique_ptr< std::FILE, boost::fclose_deleter > make_file(const char* filename, const char* open_mode)
{
    return { std::fopen(filename, open_mode) };
}
``
[endsect]

[endsect]

[endsect]
