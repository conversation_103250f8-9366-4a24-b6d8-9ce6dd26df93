# Tribool library

# Copyright (C) 2002-2003 <PERSON>

# Use, modification and distribution is subject to the Boost Software License, 
# Version 1.0. (See accompanying file LICENSE_1_0.txt or copy at 
# http://www.boost.org/LICENSE_1_0.txt) 

# For more information, see http://www.boost.org/

import path ;
import os ;
import regex ;
import testing ;

local self = logic ;

rule test-expected-failures
{
    local all_rules = ;
    local file ;
    local tests_path = [ path.make $(BOOST_ROOT)/libs/$(self)/test/compile-fail ] ;
    for file in [ path.glob-tree $(tests_path) : *.cpp ]
    {
        local rel_file = [ path.relative-to $(tests_path) $(file) ] ;
        local test_name = [ regex.replace [ regex.replace $(rel_file) "/" "-" ] ".cpp" "" ] ;
        local decl_test_name = cf-$(test_name) ;
        # ECHO $(rel_file) ;
        all_rules += [ compile-fail $(file) : : $(decl_test_name) ] ;
    }

    # ECHO All rules: $(all_rules) ;
    return $(all_rules) ;
}

rule test-header-isolation
{
    local all_rules = ;
    local file ;
    local headers_path = [ path.make $(BOOST_ROOT)/libs/$(self)/include ] ;
    for file in [ path.glob-tree $(headers_path) : *.hpp ]
    {
        local rel_file = [ path.relative-to $(headers_path) $(file) ] ;
        # Note: The test name starts with '~' in order to group these tests in the test report table, preferably at the end.
        #       All '/' are replaced with '-' because apparently test scripts have a problem with test names containing slashes.
        local test_name = [ regex.replace $(rel_file) "/" "-" ] ;
        local decl_test_name = ~hdr-decl-$(test_name) ;
        # ECHO $(rel_file) ;
        all_rules += [ compile compile/decl_header.cpp : <define>"BOOST_TEST_HEADER=$(rel_file)" <dependency>$(file) : $(decl_test_name) ] ;
    }

    # ECHO All rules: $(all_rules) ;
    return $(all_rules) ;
}

  test-suite logic  : 
    [ test-expected-failures ]
    [ test-header-isolation ]
    [ run tribool_test.cpp ]
    [ run tribool_rename_test.cpp ]
    [ run tribool_io_test.cpp ]
   ;
