[/
Copyright 2019 <PERSON>
(<EMAIL>)

Distributed under the Boost Software License, Version 1.0.
(http://www.boost.org/LICENSE_1_0.txt)
]

[section:nvp nvp]

[section Overview]

The header <boost/core/nvp.hpp> provides the class template `boost::nvp` that
pairs a name (`const char*`) with the address of a value (`T*`). It is the new
implementation of the NVP type previously provided by the Boost Serialization
library. This type now lives in the Core library so that other Boost libraries
can support named value serialization without taking a dependency on the
Serialization library.

[endsect]

[section Examples]

The following snippet shows use in a member serialize function:

```
template<class A>
void serialize(A& archive, unsigned)
{
    archive & boost::make_nvp("x", x_) & boost::make_nvp("y", y_);
}
```

[endsect]

[section Reference]

```
namespace boost {

template<class T>
class nvp {
public:
    nvp(const char* name, T& value) noexcept;

    const char* name() const noexcept;

    T& value() const noexcept;

    const T& const_value() const noexcept;
};

template<class T>
const nvp<T> make_nvp(const char* name, T& value) noexcept;

} /* boost */

#define BOOST_NVP(object) ``['see below]``
```

[section Constructors]

[variablelist
[[`nvp(const char* name, T& value) noexcept;`]
[Initializes the stored name pointer with `name` and the value pointer with
`addressof(value)`.]]]

[endsect]

[section Members]

[variablelist
[[`const char* name() const noexcept;`]
[Returns a pointer to the name.]]
[[`T& value() const noexcept;`]
[Returns a reference to the value.]]
[[`const T& const_value() const noexcept;`]
[Returns a reference to the value.]]]

[endsect]

[section Functions]

[variablelist
[[`template<class T> const nvp<T> make_nvp(const char* name, T& value)
noexcept;`]
[Returns `nvp<T>(name, value)`.]]]

[endsect]

[section Macros]

[variablelist
[[`#define BOOST_NVP(object) see below`]
[Expands to `boost::make_nvp(BOOST_STRINGIZE(object), object)`.]]]

[endsect]

[endsect]

[section History]

Robert Ramey originally implemented NVP in the Serialization library. Glen
Fernandes implemented this new (but compatible) version in the Core library.

[endsect]

[endsect]
