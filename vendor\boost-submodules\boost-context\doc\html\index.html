<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=US-ASCII">
<title>Chapter&#160;1.&#160;Context</title>
<link rel="stylesheet" href="../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="index.html" title="Chapter&#160;1.&#160;Context">
<link rel="next" href="context/overview.html" title="Overview">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../boost.png"></td>
<td align="center"><a href="../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav"><a accesskey="n" href="context/overview.html"><img src="../../../../doc/src/images/next.png" alt="Next"></a></div>
<div class="chapter">
<div class="titlepage"><div>
<div><h2 class="title">
<a name="context"></a>Chapter&#160;1.&#160;Context</h2></div>
<div><div class="author"><h3 class="author">
<span class="firstname">Oliver</span> <span class="surname">Kowalke</span>
</h3></div></div>
<div><p class="copyright">Copyright &#169; 2014 Oliver Kowalke</p></div>
<div><div class="legalnotice">
<a name="context.legal"></a><p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></div>
</div></div>
<div class="toc">
<p><b>Table of Contents</b></p>
<dl>
<dt><span class="section"><a href="context/overview.html">Overview</a></span></dt>
<dt><span class="section"><a href="context/requirements.html">Requirements</a></span></dt>
<dt><span class="section"><a href="context/ff.html">Context switching with fibers</a></span></dt>
<dd><dl>
<dt><span class="section"><a href="context/ff/implementations__fcontext_t__ucontext_t_and_winfiber.html">Implementations:
      fcontext_t, ucontext_t and WinFiber</a></span></dt>
<dt><span class="section"><a href="context/ff/class__fiber_.html">Class <code class="computeroutput"><span class="identifier">fiber</span></code></a></span></dt>
</dl></dd>
<dt><span class="section"><a href="context/cc.html">Context switching with call/cc</a></span></dt>
<dd><dl>
<dt><span class="section"><a href="context/cc/implementations__fcontext_t__ucontext_t_and_winfiber.html">Implementations:
      fcontext_t, ucontext_t and WinFiber</a></span></dt>
<dt><span class="section"><a href="context/cc/class__continuation_.html">Class <code class="computeroutput"><span class="identifier">continuation</span></code></a></span></dt>
</dl></dd>
<dt><span class="section"><a href="context/stack.html">Stack allocation</a></span></dt>
<dd><dl>
<dt><span class="section"><a href="context/stack/protected_fixedsize.html">Class <span class="emphasis"><em>protected_fixedsize</em></span></a></span></dt>
<dt><span class="section"><a href="context/stack/pooled_fixedsize.html">Class <span class="emphasis"><em>pooled_fixedsize_stack</em></span></a></span></dt>
<dt><span class="section"><a href="context/stack/fixedsize.html">Class <span class="emphasis"><em>fixedsize_stack</em></span></a></span></dt>
<dt><span class="section"><a href="context/stack/segmented.html">Class
      <span class="emphasis"><em>segmented_stack</em></span></a></span></dt>
<dt><span class="section"><a href="context/stack/stack_traits.html">Class <span class="emphasis"><em>stack_traits</em></span></a></span></dt>
<dt><span class="section"><a href="context/stack/stack_context.html">Class <span class="emphasis"><em>stack_context</em></span></a></span></dt>
<dt><span class="section"><a href="context/stack/valgrind.html">Support for valgrind</a></span></dt>
<dt><span class="section"><a href="context/stack/sanitizers.html">Support for sanitizers</a></span></dt>
</dl></dd>
<dt><span class="section"><a href="context/struct__preallocated_.html">Struct <code class="computeroutput"><span class="identifier">preallocated</span></code></a></span></dt>
<dt><span class="section"><a href="context/performance.html">Performance</a></span></dt>
<dt><span class="section"><a href="context/architectures.html">Architectures</a></span></dt>
<dd><dl><dt><span class="section"><a href="context/architectures/crosscompiling.html">Cross compiling</a></span></dt></dl></dd>
<dt><span class="section"><a href="context/rationale.html">Rationale</a></span></dt>
<dd><dl>
<dt><span class="section"><a href="context/rationale/other_apis_.html">Other APIs </a></span></dt>
<dt><span class="section"><a href="context/rationale/x86_and_floating_point_env.html">x86 and
      floating-point env</a></span></dt>
</dl></dd>
<dt><span class="section"><a href="context/reference.html">Reference</a></span></dt>
<dt><span class="section"><a href="context/acknowledgements.html">Acknowledgments</a></span></dt>
</dl>
</div>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"><p><small>Last revised: October 02, 2019 at 06:15:46 GMT</small></p></td>
<td align="right"><div class="copyright-footer"></div></td>
</tr></table>
<hr>
<div class="spirit-nav"><a accesskey="n" href="context/overview.html"><img src="../../../../doc/src/images/next.png" alt="Next"></a></div>
</body>
</html>
