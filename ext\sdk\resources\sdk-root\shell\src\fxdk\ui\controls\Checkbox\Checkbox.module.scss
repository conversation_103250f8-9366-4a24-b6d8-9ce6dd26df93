@import "variables";

.root {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;

  line-height: 1.2;
  user-select: none;

  cursor: pointer;

  @include fontPrimary;
  color: rgba($fgColor, .75);

  input {
    width: 0;
    height: 0;
  }

  .indicator {
    width: $q*3;
    height: $q*3;

    margin: $q*0.5;
    margin-top: $q; // Compensating for line height

    border: solid $q*0.5 transparent;
    box-shadow: 0 0 0 $q*0.5 rgba($fgColor, .75), 0 0 0 0 rgba($fgColor, .75) inset;

    transition: box-shadow .2s ease, border .2s ease;
  }

  input:checked + .indicator {
    box-shadow: 0 0 0 $q*0.5 rgba($fgColor, .75), 0 0 0 $q*4 rgba($fgColor, .75) inset;
  }

  &.disabled {
    cursor: not-allowed;
    opacity: .5;
  }
  &:not(.disabled) {
    &:hover {
      .indicator {
        box-shadow: 0 0 0 $q*0.5 $acColor, 0 0 0 0 rgba($fgColor, .75) inset;
        transition: none;
      }

      input:checked + .indicator {
        box-shadow: 0 0 0 $q*0.5 $acColor, 0 0 0 $q*4 rgba($fgColor, .75) inset;
        transition: none;
      }
    }
  }

  &.with-label {
    .indicator {
      margin-right: $q*2;
    }
  }
}
