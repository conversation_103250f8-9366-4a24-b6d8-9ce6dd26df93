{"name": "sdk-root", "version": "0.0.1", "private": true, "scripts": {"start": "node scripts/watch-all.js", "build": "yarn build:client && yarn build:server", "watch:client": "webpack --config config/webpack.config.js --watch", "build:client": "webpack --config config/webpack.config.js --mode=production", "watch:server": "webpack --config config/webpack.backend.config.js --watch", "build:server": "webpack --config config/webpack.backend.config.js --mode=production"}, "dependencies": {"@fontsource/montserrat": "^4.5.0", "@fontsource/rubik": "^4.5.0", "@fontsource/source-code-pro": "^4.5.0", "@octokit/rest": "^18.3.0", "@sentry/node": "^6.2.0", "@sentry/react": "^6.2.0", "@sentry/tracing": "^6.2.0", "@types/follow-redirects": "^1.13.0", "ansi-to-html": "^0.6.14", "byline": "^5.0.0", "classnames": "^2.2.6", "cors": "^2.8.5", "debug": "^4.3.1", "deepmerge": "^4.2.2", "express": "^4.17.1", "fast-glob": "^3.2.5", "fast-xml-parser": "^3.20.3", "filesize": "^6.1.0", "flatbush": "^3.3.0", "follow-redirects": "^1.13.3", "fuzzysort": "^1.1.4", "immer": "^8.0.0", "inversify": "^5.0.1", "inversify-inject-decorators": "^3.1.0", "isomorphic-git": "^1.10.2", "jsonc": "^2.0.0", "minimatch": "^3.0.4", "mkdirp": "^1.0.4", "mobx": "^6.2.0", "mobx-react-lite": "^3.2.0", "node-fetch": "^2.6.1", "overlayscrollbars": "^1.13.1", "overlayscrollbars-react": "^0.2.3", "react": "^17.0.2", "react-dnd": "^11.1.3", "react-dnd-html5-backend": "^11.1.3", "react-dom": "^17.0.2", "react-icons": "^4.3.1", "react-markdown": "^5.0.3", "react-virtualized-auto-sizer": "^1.0.5", "react-window": "^1.8.6", "reconnectingwebsocket": "^1.0.0", "reflect-metadata": "^0.1.13", "rimraf": "^3.0.2", "tar-fs": "^2.1.1", "threads": "^1.6.4", "uuid": "^8.3.2", "ws": "^8.2.3", "yauzl": "^2.10.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@sentry/webpack-plugin": "^1.14.1", "@types/classnames": "^2.2.10", "@types/cors": "^2.8.7", "@types/debug": "^4.1.5", "@types/express-ws": "^3.0.0", "@types/flatbush": "^3.3.0", "@types/jest": "^24.0.0", "@types/minimatch": "^3.0.3", "@types/mkdirp": "^1.0.1", "@types/node": "^12.0.0", "@types/node-fetch": "^2.5.7", "@types/overlayscrollbars": "^1.12.1", "@types/react": "^16.9.0", "@types/react-dom": "^16.9.0", "@types/react-virtualized-auto-sizer": "^1.0.0", "@types/react-window": "^1.8.3", "@types/reconnectingwebsocket": "^1.0.7", "@types/tar-fs": "^2.0.0", "@types/uuid": "^8.3.0", "@types/ws": "^8.2.0", "copy-webpack-plugin": "^9.0.1", "css-loader": "^6.3.0", "fork-ts-checker-webpack-plugin": "^6.1.0", "html-webpack-plugin": "^5.3.2", "mini-css-extract-plugin": "^2.3.0", "sass": "^1.42.1", "sass-loader": "^12.1.0", "style-loader": "^3.3.0", "terser-webpack-plugin": "^5.2.4", "ts-loader": "^9.2.6", "typescript": "4.4.2", "typescript-plugin-css-modules": "^2.4.0", "url-loader": "^4.1.1", "webpack": "^5.54.0", "webpack-cli": "^4.8.0"}}