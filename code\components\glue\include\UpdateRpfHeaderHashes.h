#pragma once

/*
 * This file is autogenerated.
 * Do not edit it manually!
 */

#include "Sha256ByteUtils.h"

namespace cfx { namespace puremode {

Sha256Result updateSafeHashesInit[] = {
    // 1604/update.rpf
    ShaUnpack("13663080655178f7b6da04357a74ee1c6b5768f143d64d7a3ae8d40965ca185c"), // update.rpf
    ShaUnpack("f1427322ec5b6744e09be3fa4258181beb7bd09a104d227e34cc235cdaccd062"), // update.rpf/dlc_patch/mpapartment/x64/data/effects/ptfx.rpf
    ShaUnpack("3ab57f2ae8ff36239a637d4f84417c3cd0cf198914deeddc5adfe9b120ea65da"), // update.rpf/dlc_patch/mpapartment/x64/data/lang/chinesesimpdlc.rpf
    ShaUnpack("e18d760537b0614466dedc24d5a98807cffa09ce0eca155db87211b8e912da8d"), // update.rpf/dlc_patch/mpbeach/x64/data/lang/chinesesimpdlc.rpf
    ShaUnpack("b2918269ece4978b4a46dc6f7c77881253f8aa1f883b9eec52b65884859e123b"), // update.rpf/dlc_patch/mpbiker/x64/data/lang/chinesesimpdlc.rpf
    ShaUnpack("7f0bca215882df9cdaaaf4d08b3a3c7911491f2cd9e0ab3846f1e113bcc3c396"), // update.rpf/dlc_patch/mpbusiness/x64/data/lang/chinesesimpdlc.rpf
    ShaUnpack("bd5cc4b55f8b9e4a5d47f84a4d9acad96fc95b39f470b641b55decc8e31c9791"), // update.rpf/dlc_patch/mpbusiness2/x64/data/lang/chinesesimpdlc.rpf
    ShaUnpack("712fac4527b59a0eec8891f5a1e12c0c0806e29a4f566213a8414d98d8cb0850"), // update.rpf/dlc_patch/mpchristmas2/x64/data/lang/chinesesimpdlc.rpf
    ShaUnpack("2a38d707d6fa24ad88ff4a0db59aae34a2ff9f3306efce4467fa5c0c5a271eae"), // update.rpf/dlc_patch/mpchristmas2017/x64/levels/gta5/props/x17dlc_props_facility.rpf
    ShaUnpack("1a3f442bdd31c5059b2fa8548dc14ebf871d6115fd72dd0c0fb0e89a318c54df"), // update.rpf/dlc_patch/mpexecutive/x64/data/lang/chinesesimpdlc.rpf
    ShaUnpack("f6ddf8ec38ad3099c4711ad9409cca69a04cd8f006b63836df5bdcb523172e7f"), // update.rpf/dlc_patch/mpgunrunning/x64/data/lang/chinesesimpdlc.rpf
    ShaUnpack("4a2065e25cb0639f6fa14cdf85e0862299330e1d4275742a0e5567b8b989cd77"), // update.rpf/dlc_patch/mphalloween/x64/data/lang/chinesesimpdlc.rpf
    ShaUnpack("04cc7039483def6afc19458c1eb7987ca5b8963d90b342453d832e58820d933a"), // update.rpf/dlc_patch/mpheist/x64/anim/networkdefs.rpf
    ShaUnpack("06204859d5c6c06c81cc58542efcaba81ca3e4c490a2e84564b65ec3caca7e5f"), // update.rpf/dlc_patch/mpheist/x64/data/effects/ptfx.rpf
    ShaUnpack("ef40907d60abab83daa87edd3435c5178c27b9ae7fb97b0e3c4f531cff7d92df"), // update.rpf/dlc_patch/mpheist/x64/data/effects/ptfx_hi.rpf
    ShaUnpack("d555425070016094aa0a46a886b5bba0edec86022f6df6aa41046f76ec72687d"), // update.rpf/dlc_patch/mpheist/x64/data/lang/chinesesimpdlc.rpf
    ShaUnpack("530685367a905e6dc736132b3e01201e6a0f9dcfc7ae4c5eb1f0df2b3444dcbd"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_citye/downtown_01/downtown.rpf
    ShaUnpack("3a55f75613ff2b6fd62cbb961fdbbee6c5c5250e27465b0a2faa9c1b0335bebf"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_citye/downtown_01/downtown_01_metadata.rpf
    ShaUnpack("6428b15e6852940f7f3793bf22b95b88221a2be3ab154616abde1dd11ac31fbe"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_citye/downtown_01/dt1_02.rpf
    ShaUnpack("79ebf95189e80bfbb5a07be9ec286aece99904ec39e7922d42b4681019b05ba7"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_citye/downtown_01/dt1_03.rpf
    ShaUnpack("cb710b8e8d479accadb3e1e2ab84a8f581779a3e0017af267593c1db75648d1d"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_citye/downtown_01/dt1_11.rpf
    ShaUnpack("d60e4cac99852eeb8e8121623e103c2d5fd675af32af7089bdf324fd9080faef"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_citye/downtown_01/dt1_19.rpf
    ShaUnpack("5d246ae700cb6770efea329b2a5c160faa7e74ac7c78c7d4596d8c2337803701"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_citye/downtown_01/dt1_occl.rpf
    ShaUnpack("74f1cc94d9614d49da44f347b7a93f9210e6cb4ce064b11939c268a40c6b7117"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_citye/hollywood_01/hollywood.rpf
    ShaUnpack("07f6a31bc5ead15c9bf49f355da5bb7d47edb92b68c10dfbf931646999956409"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_citye/hollywood_01/hollywood_metadata.rpf
    ShaUnpack("3794499f916ef4d63be2f622ed9c540263438e86a4cb6de65f4580c2917dc135"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_citye/hollywood_01/hw1_13.rpf
    ShaUnpack("11a7a9d06a34d3db76369e055edd6e377c52e523fe0deb98896af51409282efd"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_citye/port_01/po1_01.rpf
    ShaUnpack("c5f65f8f3ac2231c62534bfa7f73fd90975283a9e508b773dce3195bb4e39763"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_citye/port_01/po1_03.rpf
    ShaUnpack("47833cb70a1ee06a7fa3885082cea8d1fa69374a35da3692c5187e5ccc9d0338"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_citye/port_01/po1_06.rpf
    ShaUnpack("63a8e7b88c0e1c04d116ef0707f4b9ca80565fcb271589b2a10509662771267e"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_citye/port_01/po1_10.rpf
    ShaUnpack("396c5faad1cc1ca50ad5a5c2ba67f709343a18527ae1d2acdf91076133e78a2c"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_citye/port_01/port_metadata.rpf
    ShaUnpack("2b395d820ef44b5e6b0e45650bf84431cea2b0e8ddee19791bc58acb48cf7405"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_cityw/santamon_01/santamon.rpf
    ShaUnpack("705ca9f4fc7e3d34b464af5e5badc09b9811190dfef33a9f3fc1c29ed0992d84"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_cityw/santamon_01/santamon_metadata.rpf
    ShaUnpack("d5352fd3fc48f2d19c711e9b70116674918fbcfd602b0f581b335da094703d2f"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_cityw/santamon_01/sm_13.rpf
    ShaUnpack("06f879349019d8c7e8e15857b1be2f026d07281c979c31ba735515b6b164ceb0"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_cityw/santamon_01/sm_15.rpf
    ShaUnpack("798918f51102797e9570b3fa321bf39d75fc1d8848a250d58cbfb014b748fd1a"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_cityw/santamon_01/sm_occl.rpf
    ShaUnpack("322e42d19c0cb9ce291e6caaae3d836caae7d424cf4df51baaa716bceab490be"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_hills/country_03/country_03.rpf
    ShaUnpack("4ccd64d367b05b1635cc0705c42199ac9a24db98d300ca4450ed1cf47c050f5e"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_hills/country_03/country_03_metadata.rpf
    ShaUnpack("5a4399fc938482d3659df064975c8cd86a6dc01b3b0d623f4edc5c05d5714481"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_hills/country_03/cs3_04.rpf
    ShaUnpack("da91321ffa73d1f1e96440014762b3283ee8e36a5d535bab3d3f9990d256698f"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_hills/country_03/cs3_07.rpf
    ShaUnpack("428edb6677dc31ae1efad59068a04be116059f8660c6eeb8a7fec142b880de43"), // update.rpf/dlc_patch/mphipster/x64/data/lang/chinesesimpdlc.rpf
    ShaUnpack("b2e9227e8b27928036e1ac97e0d1a4396c1637e61a2226b917c6bade9b1128a9"), // update.rpf/dlc_patch/mpimportexport/x64/data/effects/ptfx.rpf
    ShaUnpack("74d96802ae69f2a8b906bfcc1090c8d637bd8176a36fb943c5a5bd754836c03e"), // update.rpf/dlc_patch/mpimportexport/x64/data/lang/chinesesimpdlc.rpf
    ShaUnpack("353b86bbda1c46b32a120cc470d0aebba1e863a679618d788eb73db1a5efe3ce"), // update.rpf/dlc_patch/mpindependence/x64/data/lang/chinesesimpdlc.rpf
    ShaUnpack("e7135e64492e6ecd40b9fc083ff5df5fee3133f5a5e70cf0918cacdcb261e372"), // update.rpf/dlc_patch/mpjanuary2016/x64/data/lang/chinesesimpdlc.rpf
    ShaUnpack("b1ebc5a88cb853db3eccfdbf604c7115a40cd9c183b82a23529b8d60caaf7abb"), // update.rpf/dlc_patch/mplowrider/x64/data/lang/chinesesimpdlc.rpf
    ShaUnpack("17a4b3ddb87fb99129b410abc9af926d9f2beed6cffa1f900da4df944f3a6d03"), // update.rpf/dlc_patch/mplowrider/x64/levels/gta5/_hills/country_04/country_04_metadata.rpf
    ShaUnpack("2107195f68a3c37b013455738cd7a446264fe140f03fdf3676a7db91b22b0502"), // update.rpf/dlc_patch/mplowrider/x64/levels/gta5/_hills/country_04/cs4_06.rpf
    ShaUnpack("18b74c38d0677be08484a45a3e2c1e7ac20c557fe8200aed3d6f5b1dbeffd33b"), // update.rpf/dlc_patch/mplowrider/x64/levels/gta5/_hills/country_04/cs4_09.rpf
    ShaUnpack("4f87e150ad4c24ad229161075aecc6bf4880a488f7b479e69fb20723169a48e9"), // update.rpf/dlc_patch/mplowrider2/x64/data/lang/chinesesimpdlc.rpf
    ShaUnpack("c967847402efe9d351ffd773c572db54d1d613ad115d7e264889e1df60fb29b3"), // update.rpf/dlc_patch/mplts/x64/data/lang/chinesesimpdlc.rpf
    ShaUnpack("cda39f4df60597148af163a37d8a0d054da4ac8df691101ae788bf0c3aa59775"), // update.rpf/dlc_patch/mpluxe/x64/data/lang/chinesesimpdlc.rpf
    ShaUnpack("13bc2ec9e4ede9f78e441e2a1c296340d5a2b60b9695a9a55d18009fc88a241b"), // update.rpf/dlc_patch/mpluxe2/x64/data/lang/chinesesimpdlc.rpf
    ShaUnpack("c240232935cb85f20286def136a97569c25caa9e6d0ef5db93be5bcb0e6b7c93"), // update.rpf/dlc_patch/mppilot/x64/data/lang/chinesesimpdlc.rpf
    ShaUnpack("c71f7d29bcbb41d12dda0edbc7b0cf9da94bf479b60a2bb07668648f3abf24b5"), // update.rpf/dlc_patch/mpsmuggler/x64/data/effects/ptfx.rpf
    ShaUnpack("26b44f13d87c85b061feef2e003327ee2b0cf638fa301c9395ba95c510913a71"), // update.rpf/dlc_patch/mpsmuggler/x64/data/lang/chinesesimpdlc.rpf
    ShaUnpack("b93041d04876d47623beaac0c62addbf243775c8ef7a8e286eadf2a6d703ab10"), // update.rpf/dlc_patch/mpspecialraces/x64/data/lang/chinesesimpdlc.rpf
    ShaUnpack("93a67e6bd54a1a2de2d1de7d95da54c8f28703ff236c038c148a9ec138a75808"), // update.rpf/dlc_patch/mpstunt/x64/data/lang/chinesesimpdlc.rpf
    ShaUnpack("e8a3656e2fa0a5f2094bebc09e588b6db572a28f307ee2328c0957d474bd7300"), // update.rpf/dlc_patch/mpvalentines/x64/data/lang/chinesesimpdlc.rpf
    ShaUnpack("57ea9c9ff065b6cf589cc4b5a51dd416812076251742aa1acb75ee8a78b06360"), // update.rpf/dlc_patch/mpvalentines2/x64/data/lang/chinesesimpdlc.rpf
    ShaUnpack("fc8044e4ed7f595781e9aedbda32f5a2655a1cdc572829ff615a3f52f6fe31fe"), // update.rpf/dlc_patch/mpxmas_604490/x64/data/lang/chinesesimpdlc.rpf
    ShaUnpack("c48e08b5b8a8a3d402506cd6c7a376907006c787f2021c0a699cae960f0f2cfe"), // update.rpf/dlc_patch/spupgrade/x64/data/lang/chinesesimpdlc.rpf
    ShaUnpack("06924199622297555b3a62ef7b43b6d9e8230497782417f769f6b295f5b74b2c"), // update.rpf/x64/audio/occlusion.rpf
    ShaUnpack("33005f42d78997c1e57c503413973f91480aa6b6d10d81ecb7df273eabaa17a2"), // update.rpf/x64/data/cdimages/carrec.rpf
    ShaUnpack("f73bd68eff333e31119f5e171c54dbe4a0bb88685d321d42dedfa926a8aecd72"), // update.rpf/x64/data/cdimages/moviesubs.rpf
    ShaUnpack("7282420adcb3ede2b21fffce9ed220a3650f578173d1fe578e16a5ff602e073f"), // update.rpf/x64/data/cdimages/scaleform_frontend.rpf
    ShaUnpack("7dcc8e91be7e90f633b622f84f7d9501f6ef4f83bb7efc9766316673ee51f4d6"), // update.rpf/x64/data/cdimages/scaleform_platform_pc.rpf
    ShaUnpack("ef18747c55f2591f99eca519cef5950935c2be396db65087b263183405b3f301"), // update.rpf/x64/data/cdimages/scaleform_platform_ps4.rpf
    ShaUnpack("f7261ffcb55d466845bfafb7d5f463fe2647c8de4fdaf4d55dd719ad3ec37bcb"), // update.rpf/x64/data/cdimages/scaleform_platform_xboxone.rpf
    ShaUnpack("1dfe69c08d53bed877439ad98b43776eb46f83eb42591468a5484906ad8c1035"), // update.rpf/x64/data/lang/american_rel.rpf
    ShaUnpack("51d763dd37e7b2d96a7232114c2eae36d708417b079cd4184fef00f608f4f842"), // update.rpf/x64/data/lang/chinese_rel.rpf
    ShaUnpack("ee8dbb386ee44a16c7f94c0aba530e069c57c32d4d9c6aa4bb886f6c74901ce6"), // update.rpf/x64/data/lang/chinesesimp_rel.rpf
    ShaUnpack("0360d764038a4bf44836576be577842c81a13ae9d38b135f5f82ae502fdb9b60"), // update.rpf/x64/data/lang/french_rel.rpf
    ShaUnpack("566c582a9fad5a15450c04cd516fd76c9c57948f222d9d77470959a26b56bd75"), // update.rpf/x64/data/lang/german_rel.rpf
    ShaUnpack("4f4347c6d2f2b568b4f95fd922c19fbb18d339a337dd8d45926a6f4f24805629"), // update.rpf/x64/data/lang/italian_rel.rpf
    ShaUnpack("f906002bde4d475954be1f21eadbe11d9e2d41358261e049d53471fb9be107c5"), // update.rpf/x64/data/lang/japanese_rel.rpf
    ShaUnpack("d819702f86e0bc0c26ffa6ec36bc02eea11315bee69525afeaa8bc49c3408833"), // update.rpf/x64/data/lang/korean_rel.rpf
    ShaUnpack("2e3ab974b5f66cf7fce425b42519e5e6b5a7b4112e0731fa5dea190efd4817cf"), // update.rpf/x64/data/lang/mexican_rel.rpf
    ShaUnpack("fd800b687f487b8b7cce0e8e4e5c7d41bbbc22d8f33072bd63c98fced7fededb"), // update.rpf/x64/data/lang/polish_rel.rpf
    ShaUnpack("ee837596cfe4cef1fcc1f478507e12fddfa75def7f495a7334694d08696efabf"), // update.rpf/x64/data/lang/portuguese_rel.rpf
    ShaUnpack("90ad4594e50399f19f056d69c166282075dd0478d7d4513aef0819226607ee93"), // update.rpf/x64/data/lang/russian_rel.rpf
    ShaUnpack("7be156ebc1ccb8e54d1628331c1bb5db1d940f7d3604eefddb1a0ca3f9c63969"), // update.rpf/x64/data/lang/spanish_rel.rpf
    ShaUnpack("d019cf16f353d0dffb3c27d710ee85f1636deb7a67155604e6484e50e64520bb"), // update.rpf/x64/levels/gta5/lodlights.rpf
    ShaUnpack("6a83485369c3f082d4590add711ff2e2a4bdda33540fd58a6cca6f02d30231af"), // update.rpf/x64/levels/gta5/paths.rpf
    ShaUnpack("d79f8b3df04ab5d6d6d9f1a8c17091d6283b077594a195bc88fb578c2aeaeb23"), // update.rpf/x64/levels/gta5/waypointrec.rpf
    ShaUnpack("78e1a0b76fd929c1312a1e88b869cd710105c1baacf9f4033b84a3d937057ecc"), // update.rpf/x64/levels/gta5/_citye/downtown_01/replay_placement.rpf
    ShaUnpack("e62b770aab6d30eac50b7b283a9a685d471dcfaa5a570ba16175b27953eb140d"), // update.rpf/x64/levels/gta5/interiors/dlc_replayeditor_casting.rpf
    ShaUnpack("04e1c41fb68e0340d1ec317b34efb1b1ebb5b673711e47e062243016ab78e954"), // update.rpf/x64/levels/gta5/script/script_rel.rpf
    ShaUnpack("4254d3c2b35439a56475bbc631b79f093acfc0ce1313897fa2d4ccdd79c78073"), // update.rpf/x64/models/cdimages/ped_mugshot_txds.rpf
    ShaUnpack("2797d3a37db9e7225b533a41e1828e906ce03a13e824cce7a35dc34546f1d308"), // update.rpf/x64/patch/anim/expressions.rpf
    ShaUnpack("39e6e1737dff1ed77a213500585120527ff27a6af8ececaf71827e424d314f98"), // update.rpf/x64/patch/anim/networkdefs.rpf
    ShaUnpack("e335a6994dfbdae631757ad4fb3fff4f9c405547878bde561f85c2c3352b9976"), // update.rpf/x64/patch/data/cdimages/scaleform_generic.rpf
    ShaUnpack("8570a342db239d05b119d7102a7afac3689d66c37b965171d5629942fc73272b"), // update.rpf/x64/patch/data/cdimages/scaleform_minigames.rpf
    ShaUnpack("b195ca682580a9428e84d33d0c2bedd370e8fbd0b70ce6d7ba2247bda2e55bcb"), // update.rpf/x64/patch/data/cdimages/scaleform_minimap.rpf
    ShaUnpack("547f1d801f7a4b0d2287a97b133bbb7d486ceef51b1d467f6252188b4d7c7803"), // update.rpf/x64/patch/data/cdimages/scaleform_web.rpf
    ShaUnpack("647cf258757ecb5af782b3e603da60106980cceb4c4e564604cd8ce092bc5327"), // update.rpf/x64/patch/data/effects/ptfx.rpf
    ShaUnpack("586a220a5b1d7a2377fc214ad5dcb0f64f67aca47e9f2ff05b1fcee61ce94c9a"), // update.rpf/x64/patch/data/effects/ptfx_hi.rpf
    ShaUnpack("7451e44221ecaf9930df45fe301cd08f7ffe1de2cce7e0dfd4d1927512c0afa4"), // update.rpf/x64/patch/data/effects/ptfx_lo.rpf
    ShaUnpack("8ae2a92d84aab48beee49dbcfe3fbfe6b13522d132c5c83e65cdd8d597b8dc8b"), // update.rpf/x64/patch/data/lang/american_rel.rpf
    ShaUnpack("bb5767e43b4cab08e366bef2d80db18065ddd1c4c93aa206ec2da14befcafbe9"), // update.rpf/x64/patch/data/lang/chinese_rel.rpf
    ShaUnpack("c91cfa15acc6d8cb5305a5421d94cd63459c9a7ee552fdc98ec6e6248ac6d64f"), // update.rpf/x64/patch/data/lang/chinesesimp_rel.rpf
    ShaUnpack("08946af4001d8cf61c5eb3b8e713bb6618f1de752d764ef1d50f1a86e6fb2337"), // update.rpf/x64/patch/data/lang/french_rel.rpf
    ShaUnpack("40c2e0e419bfa03548d809cb5a61fbea1736b72c89a092be8863da4582bc3142"), // update.rpf/x64/patch/data/lang/german_rel.rpf
    ShaUnpack("1e17e8e1a15a53aeac51f9a67ff7811d0c2e46f50258e90ceda8b016c5ab235f"), // update.rpf/x64/patch/data/lang/italian_rel.rpf
    ShaUnpack("29ea7aa897b77e30765e60464d4852742cc8d8c99a1c8d0481d205ad99ae3468"), // update.rpf/x64/patch/data/lang/japanese_rel.rpf
    ShaUnpack("901bdf967ed35329d1660790066f9f17bfc1f3e85c15ffb8a1d13e22786ad608"), // update.rpf/x64/patch/data/lang/korean_rel.rpf
    ShaUnpack("38c20c114e6612eb4cba14e2c57d26134f3034a0149a0ab081e2027533ae65bb"), // update.rpf/x64/patch/data/lang/mexican_rel.rpf
    ShaUnpack("5ec777bf00ffefb1846925315afc4d229c03a8756618b9c6a04bebd966fb0261"), // update.rpf/x64/patch/data/lang/polish_rel.rpf
    ShaUnpack("e9f8896e4a0ecd2b3911dd11e1f6036d3f30b4218da76e55078749f0b275e8b3"), // update.rpf/x64/patch/data/lang/portuguese_rel.rpf
    ShaUnpack("e64e09dc1a0f9dc091c3068c49d013cbcd4de9ec51161fcad86870c16149ea3b"), // update.rpf/x64/patch/data/lang/russian_rel.rpf
    ShaUnpack("8aa508c28d3fa53b2bce8304a44cb251f15ef293d8932e33d633c3b93a0844c3"), // update.rpf/x64/patch/data/lang/spanish_rel.rpf
    ShaUnpack("be03d7e8a1a24c086969f903fd423959b3dccf5a6b02db0c27cb3d9b5a9faf0e"), // update.rpf/x64/textures/script_txds.rpf
    // 2060/update.rpf
    ShaUnpack("073760ceb1d54db42f2e4310d018df80eb691fff9aa42c676260fd7bafbb878b"), // update.rpf
    ShaUnpack("51c4931dd0cb60099872a685836058ecbfeffb198b0f08c403caf05308c70d6c"), // update.rpf/dlc_patch/mpchristmas2018/x64/data/effects/ptfx.rpf
    ShaUnpack("06de574a52304fc97e1264cc1a5fe872f4bf786d182b682b084d13f4d662d765"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_hills/cityhills_03/ch3_04.rpf
    ShaUnpack("4448fe789ed7ce19e010a9578308f71afe246c93bfc6de973545bb493897ecc6"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_hills/cityhills_03/ch3_08.rpf
    ShaUnpack("efba5104dbee5d48c51cffb28941fb10f82743ddc934ad024107d0ff7e0466c8"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_hills/cityhills_03/ch3_12.rpf
    ShaUnpack("49c424494a09835ceed1f8c1bb75a0762bccd00ace71c9a125ba0f5adcadefae"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_hills/cityhills_03/ch3_lod.rpf
    ShaUnpack("be2b5bd104b7a0d38ab20ddbed72ae7d18234d11773927af3ef570790ca8da87"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_hills/cityhills_03/ch3_occl.rpf
    ShaUnpack("7761ed2acec6dc82f2d5c98ba99c58484a313061f0c3447235a17caca19ff113"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_hills/cityhills_03/ch3_rd1a.rpf
    ShaUnpack("2a6c58b088c5e1e2f2ef0c82c1a821e441b6516094cf3e18c68cc691d8ee8c1a"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_hills/cityhills_03/cityhills_03.rpf
    ShaUnpack("526e872546f0ca7a9af5364be84feefcaceecc461a25ed5cd139123e232eba0d"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_hills/cityhills_03/cityhills_03_metadata.rpf
    ShaUnpack("f58ab1c7f40960dd98ec0e6a453b5f75e6fdb2126137f10ea223b686b94a2337"), // update.rpf/dlc_patch/mpheist3/x64/data/effects/ptfx.rpf
    ShaUnpack("3d8da5bd1ff14adc7d00a01f2ca033ae28a2ea90bad13e5c743401f19280ded7"), // update.rpf/dlc_patch/mpheist3/x64/levels/gta5/props/prop_ch_casino_backarea.rpf
    ShaUnpack("e0b2fa1ed6c041de360193002b6275f3a1e44448ba612fcfe43bc74d50c71366"), // update.rpf/x64/data/cdimages/scaleform_frontend.rpf
    ShaUnpack("5c6847301dd6cf009045c1cd7512e045821491ef1ff0d31fa77b84d7be122cec"), // update.rpf/x64/data/cdimages/scaleform_platform_pc.rpf
    ShaUnpack("c3837cfb04d2b7ffd5432c8ae6e18b7efb82f3e0c8b25a53b5225dabcd0abe94"), // update.rpf/x64/data/cdimages/scaleform_platform_ps4.rpf
    ShaUnpack("01dc58eb2e3dc4442cbf8d2988be9ce2be17f511f7f92149e8ea63d4bba079ff"), // update.rpf/x64/data/cdimages/scaleform_platform_xboxone.rpf
    ShaUnpack("34e38ae66eaa1e5d87c8c9e854ff96257853a0c8ce576407c676ccb6f62690b2"), // update.rpf/x64/data/lang/american_rel.rpf
    ShaUnpack("c66e80a8503fcab56ae2e930f1d065e86a683f7ac860ab691b8cf5b24915ac38"), // update.rpf/x64/data/lang/chinese_rel.rpf
    ShaUnpack("1f892f28b6264e131d4ec85a2a1973ed770b4e5569e20a7bf7996143f2e176c2"), // update.rpf/x64/data/lang/chinesesimp_rel.rpf
    ShaUnpack("a31568a14e3c65b4e9b81175756a5e56947c59769fd59b1c0729e1455a93339d"), // update.rpf/x64/data/lang/french_rel.rpf
    ShaUnpack("dd05a091cd8e30e26ae14c828b21598c2c182c7e67352189b25f40b6edaaa399"), // update.rpf/x64/data/lang/german_rel.rpf
    ShaUnpack("b5324faf318f59bccf06d5d3a3d583c6b6a6680dd5c3618a238707f52a4dd343"), // update.rpf/x64/data/lang/italian_rel.rpf
    ShaUnpack("ea79adc22bd10ad445ad2275978a0bd6a3c0e404f5fdc440ea7b664604a15fd3"), // update.rpf/x64/data/lang/japanese_rel.rpf
    ShaUnpack("265505c381545814029f782a2f39a258bb2476be191e72bc2a5b2c32296d080f"), // update.rpf/x64/data/lang/korean_rel.rpf
    ShaUnpack("daabfbe1092885a6dd7f527012802f05b285d33ec7c01cac76b27cece8f6c401"), // update.rpf/x64/data/lang/mexican_rel.rpf
    ShaUnpack("f68a779ff160732f8cf80b524871d7b882f3342127a11231cfbe6c61e07a2d6a"), // update.rpf/x64/data/lang/polish_rel.rpf
    ShaUnpack("686c437c424838c410c9f48b388f3609504013a4c4da428ff8e06f4994439d22"), // update.rpf/x64/data/lang/portuguese_rel.rpf
    ShaUnpack("2bd0a467971bb1a42064cd42930dbe40d93af9eb56339eada1fc31a5aeef1969"), // update.rpf/x64/data/lang/russian_rel.rpf
    ShaUnpack("6c70c6b00b7cb30b34737b9937939d49922cd68555173be73fb965f233af8c59"), // update.rpf/x64/data/lang/spanish_rel.rpf
    ShaUnpack("4c89d205798e9d78d8ebb20812d73561f84230374dafdd892aed4818c4959608"), // update.rpf/x64/levels/gta5/waypointrec.rpf
    ShaUnpack("b83ac3a39646a18d7374cd02ff5af5f0ae99208dc99100c2c041adc7892e7f54"), // update.rpf/x64/levels/gta5/script/script_rel.rpf
    ShaUnpack("e44ca924e762ab2dd34aebcaf747d6e4e348480c939e9eea350f8e150eaf84f2"), // update.rpf/x64/patch/anim/networkdefs.rpf
    ShaUnpack("0fed4f130391d129c078a6fd255b8f2d33a3c908c693ecc6b9911cf1eb51bf6d"), // update.rpf/x64/patch/data/cdimages/scaleform_generic.rpf
    ShaUnpack("d89ad7dd9f015ec2655ab3f42cc4c33c243b47bbf7d8be3e161d732f9b863e31"), // update.rpf/x64/patch/data/cdimages/scaleform_minigames.rpf
    ShaUnpack("7250db7de97908d19ebde0ab9aa2f2acdc59a88c1439e45facc952d328db8a0e"), // update.rpf/x64/patch/data/cdimages/scaleform_minimap.rpf
    ShaUnpack("5eab35458053e311915390707301c9306464417e27ba5aad97b8d74e63638bfc"), // update.rpf/x64/patch/data/cdimages/scaleform_web.rpf
    ShaUnpack("55b3dac3f05b80b004537e53679955bf8e3ae74ce9405fd53c2aab62eef1bfab"), // update.rpf/x64/patch/data/effects/ptfx.rpf
    ShaUnpack("62b79cd6f36520d57753ec7f931597b5cb4ce00a840a330763d7dcf0d9aa8f00"), // update.rpf/x64/patch/data/effects/ptfx_hi.rpf
    ShaUnpack("48993b251ea1e95f5ef7d578764bb94920088d46ad23b7ccd14136106c9ad303"), // update.rpf/x64/patch/data/effects/ptfx_lo.rpf
    ShaUnpack("0afd1669b8836e1f1f6d01b4f7fd77c3195604772d800431ae140e63da536d39"), // update.rpf/x64/patch/data/lang/american_rel.rpf
    ShaUnpack("bb7ef2fdf36ce8a4ef96306a37c0326c733b9761c30b79f07d24d54807b9dc10"), // update.rpf/x64/patch/data/lang/chinese_rel.rpf
    ShaUnpack("a205501cb1d55d186ab435b02dd0d5306e8215d52d9c5da03d4bcca14778e158"), // update.rpf/x64/patch/data/lang/chinesesimp_rel.rpf
    ShaUnpack("281984a35381c0e5bab0e17b6a912d2982eca6173b8ae0cb3d87d66041fbe514"), // update.rpf/x64/patch/data/lang/french_rel.rpf
    ShaUnpack("23c15886d38fdf882692dccc71acf7584009d8d5d4aaffa35496865ced6897e4"), // update.rpf/x64/patch/data/lang/german_rel.rpf
    ShaUnpack("b6aec94a48670c6fc1551e1caa2a40fb666eed8db474f1737e42853aa511e9c6"), // update.rpf/x64/patch/data/lang/italian_rel.rpf
    ShaUnpack("c925645e34f63baa50c05794e3ad62d4230ea45b9804009c250eef336f0f8670"), // update.rpf/x64/patch/data/lang/japanese_rel.rpf
    ShaUnpack("2c8ffe7f1418b126bfaf8e9dc624ea95494ca563d7b9f42ecbb81f330a38d3f7"), // update.rpf/x64/patch/data/lang/korean_rel.rpf
    ShaUnpack("846446e1ccd7f8cf8cfddaa80e19baa35e2db805a3c8fd3e6ac9697a0215fadd"), // update.rpf/x64/patch/data/lang/mexican_rel.rpf
    ShaUnpack("308234ae859daf28d7da02ca081db7d28133d5a027d14d8408ac0c9f12399a10"), // update.rpf/x64/patch/data/lang/polish_rel.rpf
    ShaUnpack("3efa17d97d4d61088913bee7624e22c88f8093104d3f9e3a712766c766517934"), // update.rpf/x64/patch/data/lang/portuguese_rel.rpf
    ShaUnpack("ffa17ddef097151e8f9361588ef5f8d0872e5a10f382653d6bdfd7417fea5d27"), // update.rpf/x64/patch/data/lang/russian_rel.rpf
    ShaUnpack("3aa3bf5863cd41cbaa3f797fe289c83ea64a3aef6dececd894529455423586ef"), // update.rpf/x64/patch/data/lang/spanish_rel.rpf
    ShaUnpack("8ed8475b382143379c5161cd769d4f8136b1ee870b3537aaf88c48ed4ba7fea7"), // update.rpf/x64/textures/script_txds.rpf
    // 2189/update.rpf
    ShaUnpack("d45c1d4d4bc92ac0aa589d6a3f49a9bdbaf0aeb9c993e738d49f128d4dca288b"), // update.rpf
    ShaUnpack("fc9cb9830383666ccda2dd63019d2ec94816e23b8b164114598c087be22783cc"), // update.rpf/x64/data/cdimages/scaleform_frontend.rpf
    ShaUnpack("880d5197887dd47110f4ccbf15a719f48866789c8eda78a4c24af3d559daf1fc"), // update.rpf/x64/data/cdimages/scaleform_generic_2.rpf
    ShaUnpack("5698cd4e93172f87e21c99f3f58b381c50f815343d984961c35f23ac57aed802"), // update.rpf/x64/data/lang/american_rel.rpf
    ShaUnpack("5b05c9d81f8bb8154ca2ce92e3e6d8afe4f8ce7f9304ce6e4c61a79c5cfa71e6"), // update.rpf/x64/data/lang/chinese_rel.rpf
    ShaUnpack("397840c835d984b564cc983ab867584c28706d96096a2679495d1c2effbbddad"), // update.rpf/x64/data/lang/chinesesimp_rel.rpf
    ShaUnpack("7132757d4b432111fad2894c40a3781cf97a4f6ed9e8278dbed1e66c72d97686"), // update.rpf/x64/data/lang/french_rel.rpf
    ShaUnpack("3590324c9f9d5d35fe07e9ae1d5cc65d10b9fa593d557721dd632bcc50df5991"), // update.rpf/x64/data/lang/german_rel.rpf
    ShaUnpack("fc77a5cb01acb66b45e5f97ad6fca0739f5af96a481ea9f09d15b3ec739bd109"), // update.rpf/x64/data/lang/italian_rel.rpf
    ShaUnpack("df0f9e3fd3d76489e9327bf76597291779187fe583439c0313d19ece55955fad"), // update.rpf/x64/data/lang/japanese_rel.rpf
    ShaUnpack("ea1a8b9ab5257702521b4dc92331bb5838f5782555be584f631eb8c777240a7d"), // update.rpf/x64/data/lang/korean_rel.rpf
    ShaUnpack("ae5d005161ac7b2e61b0e9a3eac132a7fe046851db9dac1c8bf193d16a0e3dd9"), // update.rpf/x64/data/lang/mexican_rel.rpf
    ShaUnpack("8b992bdd7b2faa03252228bf6dc9df0df43fe29c163c6339ad1255f1dbe415eb"), // update.rpf/x64/data/lang/polish_rel.rpf
    ShaUnpack("8da4b92114bbde2aa8c294a841737d862f0ebc0c8574700952d407285ff75d9d"), // update.rpf/x64/data/lang/portuguese_rel.rpf
    ShaUnpack("6c225ce5136d93c69169405bc781f4bb76f6a01563e7ed75cffd844279d77483"), // update.rpf/x64/data/lang/russian_rel.rpf
    ShaUnpack("2d336ddca423c54defa366dc6ef5aba72ac557f131aba6bf35dfe55084ca47b8"), // update.rpf/x64/data/lang/spanish_rel.rpf
    ShaUnpack("066a8c16fcd636a86c0de1fb99465cb698f8053a1f7f93ab55de971698b33320"), // update.rpf/x64/levels/gta5/paths.rpf
    ShaUnpack("079a95fe691096e3dc8b20a0a4906c498d3311b35e98bdbe083aae5ef9e2c98e"), // update.rpf/x64/levels/gta5/waypointrec.rpf
    ShaUnpack("36082b781ea9b360f9fc2bd301ce703a954af15ab096c1e134399f9aa4d0a4fa"), // update.rpf/x64/levels/gta5/script/script_rel.rpf
    ShaUnpack("117c8526316b1c94f7f809b954d7d67a37ebe5cf6d17ca570f257e35cf60b59a"), // update.rpf/x64/patch/anim/networkdefs.rpf
    ShaUnpack("cd36de91452f6b840832163d5f35bad9f289a23f596d065fe41da41c2d08169b"), // update.rpf/x64/patch/data/cdimages/scaleform_generic.rpf
    ShaUnpack("5897e884ed2bf9a8e1b87e134757cd2ca2295eb54b381d25c2ace19721fa4c07"), // update.rpf/x64/patch/data/cdimages/scaleform_minimap.rpf
    ShaUnpack("59e2aa8884d4d98a71f81fe6fe84ed7973fbbb6b8b41cdbbe64e99f0b08a9867"), // update.rpf/x64/patch/data/cdimages/scaleform_web.rpf
    ShaUnpack("297eb23684334bc3f6a071445d7f2d2747f4e466904ee10fe2afe29444014b91"), // update.rpf/x64/patch/data/effects/ptfx.rpf
    ShaUnpack("934a2cf76a1608f0528c834a45db260a92d7c1742dce79a0b0fb72cc082cf972"), // update.rpf/x64/patch/data/effects/ptfx_hi.rpf
    ShaUnpack("345c8bce702489501e8b73f667ec71e25c29add9fce7a4ffd46ead8a65affc44"), // update.rpf/x64/patch/data/effects/ptfx_lo.rpf
    ShaUnpack("cb75c1801f0139a9eed1956930b49b954a23e85a8491668d3b27382f46c44a3b"), // update.rpf/x64/textures/script_txds.rpf
    ShaUnpack("be494114d6ca4728da70ec775cac906f60198436b7bb8807748dbc0c09bec8ca"), // update.rpf/x64/textures/waterfog_txds.rpf
    ShaUnpack("cb1c84301b6dce69d35994cf550b0eb89abae1b901b1671c0553b371f68523d7"), // update.rpf/x64/textures/waterfog_txds_dx.rpf
    // 2372/update.rpf
    ShaUnpack("1f6b25becf2571a2fb4dfb1c53b24d4c2c6f49865f317afa0dfd66ee8d6f03ac"), // update.rpf
    ShaUnpack("7ea033c55aa784da23729ba524f4aacb1beeaa5331fb1fd37b305cfa6fedcd76"), // update.rpf/x64/data/cdimages/scaleform_frontend.rpf
    ShaUnpack("f389da70591b2664dc98f4e413aa11043035b644b0869b934453cfb1fa596865"), // update.rpf/x64/data/cdimages/scaleform_generic_2.rpf
    ShaUnpack("c52e1167682f3e85873576a6944d5a1345f75b8b46482f9382490d2cc3e08d5f"), // update.rpf/x64/data/lang/american_rel.rpf
    ShaUnpack("24a2b9b35e3a2432bad484836834340f3a391b3749accbc1f7037189d4a54273"), // update.rpf/x64/data/lang/chinese_rel.rpf
    ShaUnpack("87dcd4c83428cea3f3897fb855d305d307e8f0173457ec54ae9138743ab349cb"), // update.rpf/x64/data/lang/chinesesimp_rel.rpf
    ShaUnpack("d8da6d75a796566e433fb0fe90450dcc382c6ff3f62863db97e5a623d3022fe8"), // update.rpf/x64/data/lang/french_rel.rpf
    ShaUnpack("baf5bdf799ece38e4c50f675ef4cc9ab091c0481d87b6bf2e433cdc9247b7276"), // update.rpf/x64/data/lang/german_rel.rpf
    ShaUnpack("20e39cf3e350dfac5ee61d0d3bcf7cdddb2737ca509aa08e0df359d7f2b1e94c"), // update.rpf/x64/data/lang/italian_rel.rpf
    ShaUnpack("1526608932e875aca1fb725b8fb9917061b94e556aef58863cb95a9c88df0ebd"), // update.rpf/x64/data/lang/japanese_rel.rpf
    ShaUnpack("90a3c2a2f019566abf67978a512ff08477a456b76bbedd43e39e3be2d2db0d2b"), // update.rpf/x64/data/lang/korean_rel.rpf
    ShaUnpack("4dfd22bc4d4f504ef5b3fc32da9bf9b2f95f1c06be3cea97ed5f4cec1cc2e522"), // update.rpf/x64/data/lang/mexican_rel.rpf
    ShaUnpack("b87fff2c8c4de4a21a75fc897d2e2ac36115a0feaf0c4c399e861ef3aa8d2efb"), // update.rpf/x64/data/lang/polish_rel.rpf
    ShaUnpack("cc81b6f37c52e47cbdf92183cb36388e8a2d635ff1edfc64598858f395dbec50"), // update.rpf/x64/data/lang/portuguese_rel.rpf
    ShaUnpack("af8a3f093a2466ec3ab221dd2dbdb3fef69842439396c3432091f9e32ec49a42"), // update.rpf/x64/data/lang/russian_rel.rpf
    ShaUnpack("b066eb336606ff4db89b3514468fd530225acd09664a5aa1119a89c54a5ff7e6"), // update.rpf/x64/data/lang/spanish_rel.rpf
    ShaUnpack("5cfa887b0d9af4402afeae1e0a51e8e5006f5e81536c5acb1c039d691157c898"), // update.rpf/x64/levels/gta5/waypointrec.rpf
    ShaUnpack("98608a25a8b9c508ebe104c30bd6128f585d0e0c57f39f984bca2e8dd8772f40"), // update.rpf/x64/levels/gta5/script/script_rel.rpf
    ShaUnpack("2c5743331d13a39aaf4bc2b9fb5abf2ddc83335cada2807ba2b19740d52cec91"), // update.rpf/x64/patch/anim/networkdefs.rpf
    ShaUnpack("8bcb2f6026d44894532ed21e1a5928ab1e24391209ad1d8ccd9a587771f77167"), // update.rpf/x64/patch/data/cdimages/scaleform_generic.rpf
    ShaUnpack("2b7ab86d27993b204953d6db32e22c77f1d066de376c8444bd6761deea286a4c"), // update.rpf/x64/patch/data/cdimages/scaleform_minimap.rpf
    ShaUnpack("7587d65eab15d725918d7a66eda29e4688db6ea1015ecba95db2dd3280842ec4"), // update.rpf/x64/patch/data/cdimages/scaleform_web.rpf
    ShaUnpack("6ee3321753d4fedd1cc5d491d40e86d20e96cb14f5e57d6db74eb19577e88046"), // update.rpf/x64/patch/data/effects/ptfx.rpf
    ShaUnpack("007e065a09d55285c2c67a087787f20a3f6e3af08b745ca363ae760d2f9cbd99"), // update.rpf/x64/patch/data/effects/ptfx_hi.rpf
    ShaUnpack("fe12a70395f25d9c1922c8069f16d06ce0eaca0af6f481dce7ef23e1ba15fb73"), // update.rpf/x64/patch/data/effects/ptfx_lo.rpf
    ShaUnpack("6e04df53a925135438de71a720d071eb05ac08286a80294fc75b5789ce14c738"), // update.rpf/x64/patch/data/lang/american_rel.rpf
    ShaUnpack("202e76efd5c5e8432d12189664d56cca25f3b16f144986e53e0743105203d6d5"), // update.rpf/x64/patch/data/lang/chinese_rel.rpf
    ShaUnpack("30dad745666878973f988b7a129e134a710c5e925e3d1ae99e221e2b52ebc1f4"), // update.rpf/x64/patch/data/lang/chinesesimp_rel.rpf
    ShaUnpack("4e14d8d1802e9c08dbd15ff9850ed23a328d5ee7ed242e845e3fbdd4d50a063b"), // update.rpf/x64/patch/data/lang/french_rel.rpf
    ShaUnpack("c8bfbdbb78a97abab4e85df3ac05dfddc4b2d4d02b39ecad9af1d01a3fd8b185"), // update.rpf/x64/patch/data/lang/german_rel.rpf
    ShaUnpack("394efe6aa6acfc755354d588473437443796c41f6aa06aa394c7b2621180733b"), // update.rpf/x64/patch/data/lang/italian_rel.rpf
    ShaUnpack("bef0c92c272905f72f41bd6a0f55cb281e7adc3e81543716eee02f472d167287"), // update.rpf/x64/patch/data/lang/japanese_rel.rpf
    ShaUnpack("fcf1544356d19f68e523319d5ca87f9a0da726f65f5d2d386c2f148214b135ab"), // update.rpf/x64/patch/data/lang/korean_rel.rpf
    ShaUnpack("20961826f993c2a69725832ac35c5aca34dc928aeba302d8e396af2c16954393"), // update.rpf/x64/patch/data/lang/mexican_rel.rpf
    ShaUnpack("f586217ddcd9e14c050562248f57a1cec3f537ecff6bf597dd5ce985b9f24880"), // update.rpf/x64/patch/data/lang/polish_rel.rpf
    ShaUnpack("0c4a82a090fcf1d6e6e54483e413d3de300c46857c9c5b05213ff3eb29222e11"), // update.rpf/x64/patch/data/lang/portuguese_rel.rpf
    ShaUnpack("0a83466ecfdb3aeafd0becdcf608bae12b86dd67b33fd1421da107e94a65496f"), // update.rpf/x64/patch/data/lang/russian_rel.rpf
    ShaUnpack("e2818e97bd35f8a5ebb670a04e89906707e8ec94f0ae35a9f479cfbf3166414e"), // update.rpf/x64/patch/data/lang/spanish_rel.rpf
    ShaUnpack("80a03af693a5807196ef25da394a4a94d233587c7e8c445f9b49dcada5988d1f"), // update.rpf/x64/textures/script_txds.rpf
    // 2545/update.rpf
    ShaUnpack("8bfc56a225fbebdd372a0db4fe45cbc319d5724ca2e8749e20ab77b2c82c78a8"), // update.rpf
    ShaUnpack("03a446b30ef62cef13bfcdb54d2140e37126d0b1b214d7e92e0d74a87c161ae7"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_citye/hollywood_01/hollywood.rpf
    ShaUnpack("3f9cc93c2d3f18aeb83c481f6f7326be187886a4854ae2687536a45b6320cfbe"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_citye/hollywood_01/hollywood_metadata.rpf
    ShaUnpack("300a8488ce3e8a8eb617bf94ccc94ed25edf1bb59536db0b75b76917ac1733ef"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_citye/hollywood_01/hw1_02.rpf
    ShaUnpack("b870cb59b72d5d535da19f1b898148f6343816552942816667078d7abb3cf045"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_citye/hollywood_01/hw1_03.rpf
    ShaUnpack("34ad8c5ad23d490047066f9fe7d2078c038eadf41d3d79db6754dab2b902613b"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_citye/hollywood_01/hw1_06.rpf
    ShaUnpack("10c219d6246e2af9eddb82a6e06c4aa7c878eb0560382564b7d627d483484fc8"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_citye/hollywood_01/hw1_07.rpf
    ShaUnpack("19d2557f86a6684d7df373bb34639564bb9e9112de7fa4bc26de56df46a6dd34"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_citye/hollywood_01/hw1_08.rpf
    ShaUnpack("3f87286157aac5aa929ef46b54352d1ae9a91a6f028e254b9bddeb84996e1e1e"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_citye/hollywood_01/hw1_13.rpf
    ShaUnpack("20496373f67300e1acffd501254fe3de9d5bb1455a805dc53cdd26e1ca84a0ba"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_citye/hollywood_01/hw1_14.rpf
    ShaUnpack("9f73634b9bd0a4ebdbd30fc7efd243938f82e19c15e39ad8d048125ca1fec37f"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_citye/hollywood_01/hw1_24.rpf
    ShaUnpack("dfae7e359523728782ff798b4ace63c5ddb2d8ec4529c2137f4858888e0e9cce"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_citye/hollywood_01/hw1_blimp.rpf
    ShaUnpack("91ed5dd4a0dd96ce48ed2b37beeef3f8c1a248504af0c6c1d7cccdfb73b5796b"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_citye/hollywood_01/hw1_occl.rpf
    ShaUnpack("cf18863f9cadc9007466c7f7fa23215ab701dc4c1c4f2fe35a39d27cd8dd5be9"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_citye/hollywood_01/hw1_rd.rpf
    ShaUnpack("2f835ff89330b67ac450dfee34d3c28f67815416650567d4e7357a4455e01f5c"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_citye/scentral_01/sc1_10.rpf
    ShaUnpack("11a8706e699c7371f647a8f751fe81be9b793a4258f919f7ef0955bca1e9cf80"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_citye/scentral_01/scentral_metadata.rpf
    ShaUnpack("3cd37e10ad5963e957c16f948be6ac0217b3e334840f5c76dc200970984979e9"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_cityw/beverly_01/beverly.rpf
    ShaUnpack("7ef36f5671b96f83175ab305227df829dc9c14babf9b5e8738f07602e14ffd36"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_cityw/beverly_01/beverly_metadata.rpf
    ShaUnpack("deae2f23b0735d87e4f4a1ff2619a03b40ecc0fca24facb9c0cfb54997bc2240"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_cityw/beverly_01/bh1_03.rpf
    ShaUnpack("4e856a218bcc36aa0ede19f8fc573e9e88f4801ad48dd1adaef70e575be65835"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_cityw/beverly_01/bh1_04.rpf
    ShaUnpack("45e2d4e6da8fb25fd31a47f0b54cb31e2cba46bededb8ae61352a99a60348c63"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_cityw/beverly_01/bh1_05.rpf
    ShaUnpack("444216423137dd8b3c019305e21030c5dce4e67afb2c00eb74b98cb0529296ae"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_cityw/beverly_01/bh1_08.rpf
    ShaUnpack("d56a426b0f95e4e8aeb2cc8d9d23372c083937afb7db9234d4701bdb3190fc99"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_cityw/beverly_01/bh1_09.rpf
    ShaUnpack("3fd2abf067ebb1e9d2751af33088358ee4a9e5ea108ec973797c5380631b645d"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_cityw/beverly_01/bh1_11.rpf
    ShaUnpack("65b7ce9b9accf7aa1f6bf384e11b287a7a60eb6a9516cd94bc67502befad91db"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_cityw/beverly_01/bh1_20.rpf
    ShaUnpack("a0ff1349dcaf017ec077af746ee948a48012150e1513a48676742169d678a14a"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_cityw/beverly_01/bh1_22.rpf
    ShaUnpack("5b65913789317b88c52619dcc23e2bb72c8acf401e36e6d9c0ad49196a286254"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_cityw/beverly_01/bh1_40.rpf
    ShaUnpack("3830bc3ee09a2a1e45a1e026629cfd2705ac2a0339bb57794e00cdaf446cb1d9"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_cityw/beverly_01/bh1_43.rpf
    ShaUnpack("3eb3c7a4a9f7155efce9876a65312bfefeaad941ec76c5f84fc56e2fbe16cf00"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_cityw/beverly_01/bh1_lod.rpf
    ShaUnpack("eed4ca463b3b23e44a3aff23e35add0285520266c9071a1e176ccfaeb2d41528"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_cityw/beverly_01/bh1_occl.rpf
    ShaUnpack("5a50efcd1c2c7a790e37626b8666df3eac4f7390783446498e7b4dfd0a497d2a"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_cityw/beverly_01/bh1_rd.rpf
    ShaUnpack("172b581630cef86cfe101e4a95c1dc80d80aaccb4e10615b17d34f153a743783"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_cityw/koreatown_01/koreatown.rpf
    ShaUnpack("80d50dc07f6b7641062d02764883a2c10c41223d35b7cd78cba5a0facf9ad65e"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_cityw/koreatown_01/koreatown_metadata.rpf
    ShaUnpack("43f99b66213230617ceb97224f32a98dade2bcc8edaf50c3e4f13471f12d9332"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_cityw/koreatown_01/kt1_04.rpf
    ShaUnpack("5249ee8185c6be7b697a5a5fdf4ddc8cb3c831b5a7f64f7514fbec0f143f57b8"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_cityw/koreatown_01/kt1_05.rpf
    ShaUnpack("b27e8caa62af729a425cb1316c361c05c410dd4815423a7cbb55ed5aa073b75e"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_cityw/koreatown_01/kt1_08.rpf
    ShaUnpack("8552c61617f8ac94f6f2cff5941a97e78d31bc98b1b75f0e8ed6c499a663e309"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_cityw/koreatown_01/kt1_11.rpf
    ShaUnpack("b4a60d1d1fdc7db00071bc35b1e100e2c1657136ffbec1c8dba16d14823dced5"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_cityw/koreatown_01/kt1_occl.rpf
    ShaUnpack("e266f13abad02fc27d6e961001d3fa076edfd3ce4f7283c028a112ed111d8723"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_cityw/koreatown_01/kt1_rd.rpf
    ShaUnpack("f9d863fedb4b0db0e0cf33a663d3e62562cd0fcdd6efc5b6b79fc11ceb21b5e6"), // update.rpf/dlc_patch/mplowrider/x64/levels/gta5/_citye/scentral_01/sc1_00b.rpf
    ShaUnpack("b80c87989c384ad3c2f8dae771bf6f5d61af2e4b755acef716b0723e990438e6"), // update.rpf/dlc_patch/mplowrider/x64/levels/gta5/_citye/scentral_01/sc1_02.rpf
    ShaUnpack("78b80ceac718b94976fe312ba7b112dd06f0ac34f7993876d09448aa55b7763a"), // update.rpf/dlc_patch/mplowrider/x64/levels/gta5/_citye/scentral_01/sc1_10.rpf
    ShaUnpack("1afb7c77c186141a4f99164a37c2508b93b66500b58354d8c0850dc9b01d807a"), // update.rpf/dlc_patch/mplowrider/x64/levels/gta5/_citye/scentral_01/sc1_11.rpf
    ShaUnpack("e4b5db50a600f48231075f6200a414f5883f3a34654bba17037ce8074059be1a"), // update.rpf/dlc_patch/mplowrider/x64/levels/gta5/_citye/scentral_01/sc1_12.rpf
    ShaUnpack("ecb9862de1df1450f973ace6478402802146c190567e329757485b2f917f4eff"), // update.rpf/dlc_patch/mplowrider/x64/levels/gta5/_citye/scentral_01/sc1_23.rpf
    ShaUnpack("87d7a074b2430cd1ce261a49a4162e6bbf44dad1359206cb8539fe4eb731bd7b"), // update.rpf/dlc_patch/mplowrider/x64/levels/gta5/_citye/scentral_01/sc1_occl.rpf
    ShaUnpack("b53ec272c5139784a987d0465e69a193aef16e80b692d5b227a2f76e5e494874"), // update.rpf/dlc_patch/mplowrider/x64/levels/gta5/_citye/scentral_01/sc1_rd.rpf
    ShaUnpack("844db0c739ff9129691922aa2ac901533d1ee2699201d17ea5e921816962bc81"), // update.rpf/dlc_patch/mplowrider/x64/levels/gta5/_citye/scentral_01/scentral.rpf
    ShaUnpack("b8d829bfca5b553db900bc31b685fcb3e4d0f9ea9a34d320ae530e5cf69ad51b"), // update.rpf/dlc_patch/mplowrider/x64/levels/gta5/_citye/scentral_01/scentral_metadata.rpf
    ShaUnpack("c6b4da87f75c46d90ec391d16d65af76d51e5f020300547b1d68215dde80a522"), // update.rpf/x64/data/cdimages/scaleform_frontend.rpf
    ShaUnpack("d21b7d6b7a6915081cf7f0bc9e102ec15444e0341a3ba2cb6e854f3e5d58f1d9"), // update.rpf/x64/data/cdimages/scaleform_generic.rpf
    ShaUnpack("32365df55528e1c4a96fd64575876fafc14b667698435ca0a4fa5c0eed4c868d"), // update.rpf/x64/data/cdimages/scaleform_generic_2.rpf
    ShaUnpack("6a2b1437e708deaaff77bf2f1c71a029430b96aa8a983f4dc6bb4fde571696fd"), // update.rpf/x64/data/cdimages/scaleform_platform_pc.rpf
    ShaUnpack("3651ea94aa3904536e4ca8f0232be48fb1a634cce174c00e271909129a0f585f"), // update.rpf/x64/data/lang/american_rel.rpf
    ShaUnpack("2c30035c101cce8f2cea4375a6a544f511ed06a0d8ab704283fbd79df69c598d"), // update.rpf/x64/data/lang/chinese_rel.rpf
    ShaUnpack("96d2be30ada5404a836198069bbf1d46a88ec8084399492d19f3b19ccbbc65ad"), // update.rpf/x64/data/lang/chinesesimp_rel.rpf
    ShaUnpack("78668fddf337e5bcab9e5c5ffc0583df54a614d19d29546086852f211c187876"), // update.rpf/x64/data/lang/french_rel.rpf
    ShaUnpack("ce4d8b7f83a342d60019b4814a98eefcdd721234c608a7f8ab7f7a61d8421c58"), // update.rpf/x64/data/lang/german_rel.rpf
    ShaUnpack("ac7b1285705b589df09486b5f3170ef1224b48641be1181180671b8d07bc65e1"), // update.rpf/x64/data/lang/italian_rel.rpf
    ShaUnpack("beabf1b23cc7c5e0fb7765afd31c479ab6a8027594267eb23381811081327d39"), // update.rpf/x64/data/lang/japanese_rel.rpf
    ShaUnpack("564cb6bc1833816202481fdbefa0bb2152101d23fab5a919fd429886aa4ecfe2"), // update.rpf/x64/data/lang/korean_rel.rpf
    ShaUnpack("c198a0ec25081da4bc74c6eb42289612e8e9da4af9dd8a9753ac782b554c4e73"), // update.rpf/x64/data/lang/mexican_rel.rpf
    ShaUnpack("44266001d35db69678ab3e9cb7f90caa1e6aa1571bce60a16305908c30646a48"), // update.rpf/x64/data/lang/polish_rel.rpf
    ShaUnpack("a4acdf68c5345c868839d137a88fd9c6d40bbdaa273c5ffdfb6dbd804ad4bc65"), // update.rpf/x64/data/lang/portuguese_rel.rpf
    ShaUnpack("ca7217f9832323315e63480e136c2814bf4aa7a67efa2eee02ad115657606792"), // update.rpf/x64/data/lang/russian_rel.rpf
    ShaUnpack("6a65feadfe492db78aef388001a22cc0238e94546157e60b3a66330fab904725"), // update.rpf/x64/data/lang/spanish_rel.rpf
    ShaUnpack("13ea091ff55a2e368cff8590b0f4221a3bf3e3679a379320dcc514e58ef63595"), // update.rpf/x64/levels/gta5/script/script_rel.rpf
    ShaUnpack("aa9534894c557df3754c7ad82a7550a1378be9351f814f2f7635d267092bfba3"), // update.rpf/x64/patch/anim/expressions.rpf
    ShaUnpack("eb1ede5735348c4775d09e365651c89c92a1aae1f0563960fa922a6f6423bb6d"), // update.rpf/x64/patch/anim/networkdefs.rpf
    ShaUnpack("59c5fe8e4bd984f0581eb04679247ce2913bdb93b06498044a4fcbc7f0208a0c"), // update.rpf/x64/patch/data/cdimages/scaleform_minimap.rpf
    ShaUnpack("09e8a2c45ff03c266479eacbcb81a04099b5cfc4804addcc1b2ac22b98726a0c"), // update.rpf/x64/patch/data/cdimages/scaleform_web.rpf
    ShaUnpack("6e45acaf4f8db7a062ee8efc80dfe9e971b1ef3a7548d22080c2c3afb533e3d8"), // update.rpf/x64/patch/data/effects/ptfx.rpf
    ShaUnpack("97baa214bf63faa5daf5223798c633a8a8678bc86ed329c422eacfea16a1e94a"), // update.rpf/x64/patch/data/effects/ptfx_hi.rpf
    ShaUnpack("8b76cf76a50892b6b619f2e6e817839d5fb509cf21319bde83be641ce437032b"), // update.rpf/x64/patch/data/effects/ptfx_lo.rpf
    ShaUnpack("07b3c69468b15f2c40d934a91d64fab8ef107a9877df04f2d2f7d8f599b6a2f5"), // update.rpf/x64/patch/data/lang/chinese_rel.rpf
    ShaUnpack("547762f4d4c1ed72eaa35148f2694c1e86301319ec82065de13eee405d0a04e2"), // update.rpf/x64/patch/data/lang/chinesesimp_rel.rpf
    ShaUnpack("fbbee9ef1c810f9bd9d7de43312beeef304d4b4f4d49bd2e0080c7abbea73905"), // update.rpf/x64/patch/data/lang/french_rel.rpf
    ShaUnpack("3b727deab1e650278d0b515e8ce57ecfed1781daa6c8c0a27078b6a00fe8ff4f"), // update.rpf/x64/patch/data/lang/german_rel.rpf
    ShaUnpack("b23260b174843beba2e49db4ab131d2542a571e6d16ec0f8fc0921db4e1794aa"), // update.rpf/x64/patch/data/lang/italian_rel.rpf
    ShaUnpack("c2f3babbf03e30f93754bfad1a1c467b72b5624c8a986f4099353577f591bdea"), // update.rpf/x64/patch/data/lang/japanese_rel.rpf
    ShaUnpack("43f8498197af439c0023dca65906ee8168333fd7f3a0b56e75d20104480e3c97"), // update.rpf/x64/patch/data/lang/korean_rel.rpf
    ShaUnpack("dcf28c79936248b230205c8cc56f13686c3a50a225a41a51d3adcf4358c3f41a"), // update.rpf/x64/patch/data/lang/mexican_rel.rpf
    ShaUnpack("0c434216570a57603804e6f00119c70908b632be6028e198803f0e7d487a32af"), // update.rpf/x64/patch/data/lang/polish_rel.rpf
    ShaUnpack("c5cb4994cba94681bbb0cab88e09adb9166b3daebbea5cb274bf0e0a434a3741"), // update.rpf/x64/patch/data/lang/portuguese_rel.rpf
    ShaUnpack("59869a87ce5bf6b7cddf1281be950281720e480c5b9a380ddfd19a94110f33f8"), // update.rpf/x64/patch/data/lang/russian_rel.rpf
    ShaUnpack("b2e4aaefb7f3cf76865ccdab463b1375b9af652cdbb4feb84569f6875e846f61"), // update.rpf/x64/patch/data/lang/spanish_rel.rpf
    ShaUnpack("75f057b81ff218542372bfd80f894e9e90229e17fed17d05b475cdacfe7debe6"), // update.rpf/x64/textures/script_txds.rpf
    // 2612/update.rpf
    ShaUnpack("6eb926bba8002addf8245f0b593a962d6763c5a09ff3246fdb30e594a05eb67d"), // update.rpf
    ShaUnpack("5e24a9a056881ab1b112e3512956799795726dfc176941ee483bc3c02d53265f"), // update.rpf/x64/textures/script_txds.rpf
    // 2699/update.rpf
    ShaUnpack("1ac4d674c8da9dc0ccf630b04e77f63fcd35c7936250f23772c7f83e1f847ab7"), // update.rpf
    ShaUnpack("f0961f553afc94185d07406baa61cbb87c43726a1a38283a8b20c0bb89d55987"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_citye/hollywood_01/hollywood_metadata.rpf
    ShaUnpack("088f455d72b0d4305f60c6d9fdb17b75104163009df4a13bd4649c5378ed1999"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_citye/hollywood_01/hw1_02.rpf
    ShaUnpack("6bff08b1b5b0bf3807fe1f6b7b76c3b701cd09ff5c0f1059137e97ebc6528855"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_citye/hollywood_01/hw1_03.rpf
    ShaUnpack("4b24a6cdd6159c7d121b930db803ec5382f5eebf909e656a99f96db87550f2f4"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_citye/hollywood_01/hw1_06.rpf
    ShaUnpack("83155002608a21d293815f4fca4bf16210614462e2e3899a2db65cb4c90a644e"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_citye/hollywood_01/hw1_07.rpf
    ShaUnpack("41473fb04432e05aca88eaa1ff01a4ff900208f5ad0e1266a4a19eaffc7c28d1"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_citye/hollywood_01/hw1_08.rpf
    ShaUnpack("84dc9ca81f71b2f059028a3bb27dee4e94d1d883acbad8d3c2d35b7758bbfbb1"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_citye/hollywood_01/hw1_13.rpf
    ShaUnpack("8c870aa17200f3147396dfb298a39088c23828199e4163192af6197c52817f80"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_citye/hollywood_01/hw1_14.rpf
    ShaUnpack("8c08f5962ad813bb4b8fc917606716d3f75507585de15f50df9e848eec863cf0"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_citye/hollywood_01/hw1_24.rpf
    ShaUnpack("c4da535bd50f8f3c348499b3bb773357c5ab335c3498c4cf5ad9b60da35d0804"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_citye/hollywood_01/hw1_blimp.rpf
    ShaUnpack("50e56f9ebeed8352a85dec09bea2e30ce2269c9b1f49ffe4f6da0d0c9bd0351c"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_citye/hollywood_01/hw1_rd.rpf
    ShaUnpack("c69125ff4c75fbc77afa05d6f45f585fe35fb301ebecbe909bfb67e6cb089d3b"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_cityw/beverly_01/beverly.rpf
    ShaUnpack("9f06eec38a9945516b52dfb8c1d1f7e4df940c48820dc6d7cdc1dbff0aca1f86"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_cityw/beverly_01/beverly_metadata.rpf
    ShaUnpack("920e34d9c74e182e8211539fbc628810689760851e869f2b77adf23958721686"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_cityw/beverly_01/bh1_03.rpf
    ShaUnpack("542d0783efe12e685079eb6f8bbadf91eb220d76b346fef14961f4beda9043bc"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_cityw/beverly_01/bh1_04.rpf
    ShaUnpack("bfc60077bec44cb95a1be4b0ddb4960c2b499af5cee3a60cc3ba612f7b1c067e"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_cityw/beverly_01/bh1_05.rpf
    ShaUnpack("c98ca7dc2d5dbe420b7d98f98dccde1bcc008b21aecfc53d9ef8b40b6a75a5cd"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_cityw/beverly_01/bh1_08.rpf
    ShaUnpack("074ff12523028e27f42a689a75f7c44d7f20f34e7b6a142ae8f6a47287842de6"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_cityw/beverly_01/bh1_09.rpf
    ShaUnpack("6a5985b2907b492f4891f792fd3d312f83d348656a3b585c3540219e7bc28c5e"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_cityw/beverly_01/bh1_11.rpf
    ShaUnpack("44f98858122d38fe52008e85f30013f650db45062fddb889ec0741a8231117a5"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_cityw/beverly_01/bh1_13.rpf
    ShaUnpack("305fbdb83213296bbfabd30237327b06b725bafaa9889eb9a16541c30a13e33e"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_cityw/beverly_01/bh1_20.rpf
    ShaUnpack("62e5f60b93459158bd6802ea97a15f34eff904e75545136ca952612253cc675a"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_cityw/beverly_01/bh1_22.rpf
    ShaUnpack("d032466746060dc8e35206d3b7d5e9c16a9aaec73b5fc426ddaf877f7354f946"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_cityw/beverly_01/bh1_40.rpf
    ShaUnpack("44fb116393fa96830770f2754176de2ecb7fe0a3e2366e8823bac65188338716"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_cityw/beverly_01/bh1_43.rpf
    ShaUnpack("b9867ad86ac1fd99c8738fd0dd28010c11d02866053c1f6e6f333071126793bd"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_cityw/beverly_01/bh1_occl.rpf
    ShaUnpack("162b7128d2417291c1b98e2a8fe19a32009fae4ba5a6019dfed7a95394a810c5"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_cityw/beverly_01/bh1_rd.rpf
    ShaUnpack("510c0a101ae301a726b3673431c7c351e0d51c128c13c3863db68a14a18d8099"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_cityw/venice_01/vb_34.rpf
    ShaUnpack("bb4defbe2cdcf163d50e82ad97504968f75324512c82c129843bb8e5a1211d62"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_cityw/venice_01/venice.rpf
    ShaUnpack("955357ce7bd38c4851a528fcb83004e7156d890802b58e79edd71436314e64db"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_cityw/venice_01/venice_metadata.rpf
    ShaUnpack("cb423e307fd0cf08bcac745f59a4bbc776c85da76856132b0d75560481218831"), // update.rpf/x64/data/cdimages/scaleform_frontend.rpf
    ShaUnpack("1714efbb6809fc786def924c8cab1ee43083ab755beb8b6a2ffb58d76a3b038c"), // update.rpf/x64/data/cdimages/scaleform_generic.rpf
    ShaUnpack("a67eb45a7553530a561d90dac994297c7fead67ed95d6e790ef92be549068223"), // update.rpf/x64/data/cdimages/scaleform_generic_2.rpf
    ShaUnpack("79dfee01244333201e8a2655cd5d25ca8a881a542adc804087c946c7d5741bf0"), // update.rpf/x64/patch/data/cdimages/scaleform_minimap.rpf
    ShaUnpack("4c583aaf76055fb21520fe9560423923714b93ff7292904e37c0ae0725c80b6c"), // update.rpf/x64/patch/data/cdimages/scaleform_web.rpf
    ShaUnpack("5a019716aeb5784237d6a5d22c8ae7173d082b44006e5264878b510900cbe51b"), // update.rpf/x64/patch/data/effects/ptfx.rpf
    ShaUnpack("9a10df1ed4d11d4dbba3a7e05075fd00476ddb4abafd556f2873a1d13a93e93c"), // update.rpf/x64/patch/data/effects/ptfx_hi.rpf
    ShaUnpack("20803a4ffe3a2b4fe6f116fa709e2a4c24c26ce07c403722c29c91281b27310f"), // update.rpf/x64/patch/data/effects/ptfx_lo.rpf
    ShaUnpack("77ba4fee9fdb75155629b4fe9e82d06ca50c48c8d19586d905ff343d25bcef43"), // update.rpf/x64/textures/script_txds.rpf
    ShaUnpack("b9a4942b4a04352af3997ba7080cfa782c427ff6055a777c5fe8127fffdb5614"), // update.rpf/x64/textures/vehicle_paint_ramps.rpf
    // 2802/update.rpf
    ShaUnpack("30264c286533bab99cdcf446850d275b1de443fe1052939580baf6511fb53a9f"), // update.rpf
    ShaUnpack("0aead6952394a7cdc74a033c1d0e7a32bdf091b565980db15affa1256b3212e4"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_cityw/beverly_01/beverly_metadata.rpf
    ShaUnpack("13da1bc103e0ddf576dead714bf27a47b671192b073007740674c28c0c379262"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_cityw/beverly_01/bh1_03.rpf
    ShaUnpack("e8e4002751a9a0fce52883cec3695bda265e1ec80e298605c4b52f55121dd044"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_cityw/beverly_01/bh1_04.rpf
    ShaUnpack("2c764f8b9528de7cd921292c17087e3582e3827adc4c1946a865ded680982980"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_cityw/beverly_01/bh1_05.rpf
    ShaUnpack("f4b378608c01204d8fb023191f3a042552557bde3be34ac4cb6ad8b193d84d75"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_cityw/beverly_01/bh1_08.rpf
    ShaUnpack("3811dcab0bd382f614d8efdceaa0392545b35e04f69aded05006dcee7fb16407"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_cityw/beverly_01/bh1_09.rpf
    ShaUnpack("61c2d9f966b0c72c6446fffafa3171efc62b6930de0ff7fc3f59cef42c26808d"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_cityw/beverly_01/bh1_11.rpf
    ShaUnpack("ccd51cc6c0a938a1c6f2cbf690bea453fd3a46d0d35c91da42f54976c6a77017"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_cityw/beverly_01/bh1_13.rpf
    ShaUnpack("3c2837a0a8bbfbc286557bbefa54a85969c22d25b91ab22b5b93d054e71f326b"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_cityw/beverly_01/bh1_20.rpf
    ShaUnpack("17b03c9ab1617a11d775d5526a631a6d178a5b4613061dac9b04fcba7a61036d"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_cityw/beverly_01/bh1_22.rpf
    ShaUnpack("fb40fc4564c67468b5610360848a56556fd2fe834ad1d32e72fdafe3f5254e50"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_cityw/beverly_01/bh1_40.rpf
    ShaUnpack("1c982f6348f7123a81ee79b643a4ee27990e70d2252760b9ed6ed5294f79e7ec"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_cityw/beverly_01/bh1_43.rpf
    ShaUnpack("cfc95fb6ee0526c5761ade8549db8ae609194e470c09645379da34d3f56a591b"), // update.rpf/dlc_patch/mpheist/x64/levels/gta5/_cityw/beverly_01/bh1_rd.rpf
    ShaUnpack("08d0f876a0ef94a1a5d579d5f4b0ebc614d2beb2af64c03f7b3a2682fc5cb67e"), // update.rpf/dlc_patch/mpsum2/x64/data/effects/ptfx.rpf
    ShaUnpack("a28272ac535fd4695700325325f94ed55a361d5831e4dc5d1063d930258d1d3b"), // update.rpf/x64/data/cdimages/scaleform_frontend.rpf
    ShaUnpack("1e98ad6e387c7d190d312a4e7bcb762106137e98cee0a190030d3ebc10184376"), // update.rpf/x64/data/cdimages/scaleform_generic.rpf
    ShaUnpack("eab08b96785966252526186bc902412894622992c841dc974ecafe8272a11f6c"), // update.rpf/x64/data/cdimages/scaleform_generic_2.rpf
    ShaUnpack("8202a604ce8006230b349e4aaf9ec699c6f0e078ab8ed1932cc1125cbbcf88bb"), // update.rpf/x64/data/cdimages/scaleform_platform_pc.rpf
    ShaUnpack("96ab71f02c23d8a00c3ffa8568d584f184b9a5b7ce619c0b084e13d220101c75"), // update.rpf/x64/patch/data/cdimages/scaleform_minigames.rpf
    ShaUnpack("1e214e090d48009ea41a344e166e1c53e07b5cb794ada7c771c3cc594d491b10"), // update.rpf/x64/patch/data/cdimages/scaleform_minimap.rpf
    ShaUnpack("ea20495ffbbddccfe3525e62a3ae90978a166c635f3eaabee47b01c03e9d0578"), // update.rpf/x64/patch/data/cdimages/scaleform_web.rpf
    ShaUnpack("6573ab1090251aa2d7fb89f4f1eca2a64bb115c8ecd9adbd1e575461c9b94d74"), // update.rpf/x64/patch/data/effects/ptfx.rpf
    ShaUnpack("9ad141878c6d36fd9bcf975caad87b3e9bff6879f089c1030a777177fb855fb9"), // update.rpf/x64/patch/data/effects/ptfx_hi.rpf
    ShaUnpack("0e2cac4b2e0f28b0dfe4198c9b01312861454edf89493fc29f855d8ccd5519d2"), // update.rpf/x64/patch/data/effects/ptfx_lo.rpf
    ShaUnpack("329392ba7f17fd110651fd1ed53e6598642dd16b1cdfccf3219a2d21578cc220"), // update.rpf/x64/textures/script_txds.rpf
    // 2944/update.rpf
    ShaUnpack("c2dd991b4aebc498232e31a0dd05f8d6a150d148bf7b075f6485184b7a16eac8"), // update.rpf
    ShaUnpack("f03dd7bfd709044c2a6fc1a5c2da1ce042ea477472d2dc6723962d848b8b32ee"), // update.rpf/x64/data/cdimages/scaleform_frontend.rpf
    ShaUnpack("a77602f757dc9e3ddbb71b82c264bbdcd0804ef16d100bb067fba1acbd9e958b"), // update.rpf/x64/data/cdimages/scaleform_generic.rpf
    ShaUnpack("ebd573899617c896501c25c5ebfa5f27876edd2b7e554fb2e9e4351322609a18"), // update.rpf/x64/data/cdimages/scaleform_generic_2.rpf
    ShaUnpack("943867e1e157beb91185823aa535d4c79c91141d71ab8ba8cfb7ddbb8943a563"), // update.rpf/x64/patch/data/cdimages/scaleform_minimap.rpf
    ShaUnpack("4b3020e623e4af77a3051df774a1c8d7c8e10d11e83627c1d8c81c1cbf4953f6"), // update.rpf/x64/patch/data/cdimages/scaleform_web.rpf
    ShaUnpack("48b2a1f0c7f5b011835361b6cf05650c7a8790069c57a098c5c2a2670ff69bba"), // update.rpf/x64/patch/data/effects/ptfx.rpf
    ShaUnpack("e00bd3ec7b27e4b947634ae748c1cc75f18287f679d9684527d2e1e26571877d"), // update.rpf/x64/patch/data/effects/ptfx_hi.rpf
    ShaUnpack("3a8fbb3960919beaffa316d2c315c01ce2dbe2705a0c8a788d6fcab508937354"), // update.rpf/x64/patch/data/effects/ptfx_lo.rpf
    ShaUnpack("52bf194610824735c8f1be5b77ef2ef3da5878e303008455021924f592664d76"), // update.rpf/x64/patch/data/lang/chinese_rel.rpf
    ShaUnpack("9ddb133ee356b30a8914c8c71c8bea282d6448424d31539c9622cb9f1f794a36"), // update.rpf/x64/patch/data/lang/chinesesimp_rel.rpf
    ShaUnpack("3fdffca30fb047054bebd231f8149562e2bba90b62e76608f546e833e7e150a9"), // update.rpf/x64/patch/data/lang/french_rel.rpf
    ShaUnpack("e954577de67b79aab7d80cbe579faa1ba7244289db23615d53ff1b1de15a2273"), // update.rpf/x64/patch/data/lang/german_rel.rpf
    ShaUnpack("71aff7db637cfa700c152f958f51f57152fb786d005bd11963298a2594badeb6"), // update.rpf/x64/patch/data/lang/italian_rel.rpf
    ShaUnpack("59c69fa847fb78009f0b0358ac5f92756a71353006bcd21ae4f495a082ebc1d5"), // update.rpf/x64/patch/data/lang/japanese_rel.rpf
    ShaUnpack("9f3122cfb053214643037134f0d8226b166fae6c8c567ff9277debf68228c86d"), // update.rpf/x64/patch/data/lang/korean_rel.rpf
    ShaUnpack("27ff4dda5f1ed00cd5251556419378f844a12d20ef12f512b2b42615cb6faedc"), // update.rpf/x64/patch/data/lang/mexican_rel.rpf
    ShaUnpack("efbb81af66d5a888874bd57ea48df9156cd89e02918a671cee4b379d4b348ea0"), // update.rpf/x64/patch/data/lang/polish_rel.rpf
    ShaUnpack("0085090d0bc83ab3edab4b035fe5826a59211ccf79bf329bd09282942f5ecf6b"), // update.rpf/x64/patch/data/lang/portuguese_rel.rpf
    ShaUnpack("4b23808a79e269227614da1ea048c45532a61812f082f8ee75bb278c920695ff"), // update.rpf/x64/patch/data/lang/russian_rel.rpf
    ShaUnpack("73cd252cad84298ee6f238f6667b260eaa247b4d4756ef68658f5b5e8df4b516"), // update.rpf/x64/patch/data/lang/spanish_rel.rpf
    ShaUnpack("94f327736e43c22169ff41a20e652d7eed7cc698206462b3641a8fad7ebc7c3e"), // update.rpf/x64/textures/script_txds.rpf
    // 3095/update.rpf
    ShaUnpack("8e9bc73401681d0dfeb2a8aacb6f91e3dff08f1b34bc2513d2bb0f9f4143e2fd"), // update.rpf
    ShaUnpack("60c8e5b47a30b7f38b5a519feb8a48f5aa376ef1803d87bde5e5e843b3a1cd9f"), // update.rpf/dlc_patch/mp2023_01/x64/models/cdimages/weapons.rpf
    ShaUnpack("065f9cdc9ab0cac0de81e1cfb960570fa42c9dacb526db64764ae42fa354568a"), // update.rpf/dlc_patch/mpapartment/x64/models/cdimages/weapons.rpf
    ShaUnpack("a4436036bded7268f5c421cedff2c566745634eacb62725d9886b0e6f6337b7a"), // update.rpf/dlc_patch/mpbattle/x64/models/cdimages/weapons.rpf
    ShaUnpack("54fe29b7e060241c20259bbc9f9bc0cb1eed2eee74d21e466e77412b6a9fa60e"), // update.rpf/dlc_patch/mpbeach/x64/models/cdimages/weapons.rpf
    ShaUnpack("c16294fdbc037810fa174dda70c877a5e3c4ffe4384e44d2a7addc396173f17d"), // update.rpf/dlc_patch/mpbiker/x64/models/cdimages/weapons.rpf
    ShaUnpack("5604b2c6f275f858877cf0b09cd3a64cf18620560a918b31c3f82dd5d4e3bf0c"), // update.rpf/dlc_patch/mpbusiness/x64/models/cdimages/weapons.rpf
    ShaUnpack("43b9ac3d098cb2fcad4c987768c707e07cab8d67124c5196733a8e2c057e628b"), // update.rpf/dlc_patch/mpbusiness2/x64/models/cdimages/weapons.rpf
    ShaUnpack("b59bf09f16f9fda1d18581e399d7991d1fdb44835cd458eb4ff681a1d1bc111e"), // update.rpf/dlc_patch/mpchristmas2/x64/models/cdimages/weapons.rpf
    ShaUnpack("a6f1c6ad4db10c07c63733a1842ee30dd4537201b5e3a68882895b721e8118e4"), // update.rpf/dlc_patch/mpchristmas2017/x64/models/cdimages/weapons.rpf
    ShaUnpack("dadefe7c5c3e89a585602e9c76e658283566306b635be888314619cd3d9bcd87"), // update.rpf/dlc_patch/mpchristmas2018/x64/models/cdimages/weapons.rpf
    ShaUnpack("1abd59e0c04e9088f369a4ea20f14d26be621e48c8214e6e5501f95344740cfe"), // update.rpf/dlc_patch/mpchristmas3/x64/models/cdimages/weapons.rpf
    ShaUnpack("b097524e4065af87ee1864d475394770b3edea388368b5201b2a631dd5994600"), // update.rpf/dlc_patch/mpexecutive/x64/models/cdimages/weapons.rpf
    ShaUnpack("128e8f7b9d6d7d1d385afc8b63f9144ed9c247b72fde3a7148569adf82e4ebb6"), // update.rpf/dlc_patch/mpgunrunning/x64/models/cdimages/weapons.rpf
    ShaUnpack("62301f6afe77a76b2c9915ab64b80ec5287b42a8688b7eb172082604c8c7a933"), // update.rpf/dlc_patch/mphalloween/x64/models/cdimages/weapons.rpf
    ShaUnpack("441d53c3f85fce04eb8ad68b9e3f8158849030d96bc3039383c7651e553a2a6b"), // update.rpf/dlc_patch/mpheist/x64/models/cdimages/weapons.rpf
    ShaUnpack("692505ddee4ed150a240a4f5a473874287a66eeadb80555d6fddc5d598e2092f"), // update.rpf/dlc_patch/mpheist3/x64/levels/gta5/interiors/dlc_int_06_ch.rpf
    ShaUnpack("6c0682cd6ddad9c1d3710e33fd3608b74e956fae059a7fb1e4f9e90ee17bb9a5"), // update.rpf/dlc_patch/mpheist3/x64/models/cdimages/weapons.rpf
    ShaUnpack("274834251fb41e7800d888ea57e1465ca998dbd89eb0bde715d48be4446d2011"), // update.rpf/dlc_patch/mpheist4/x64/models/cdimages/weapons.rpf
    ShaUnpack("77e833ad5b0df4ac78f168e6aba435b0ddd3f3e6d8b38106d11bcd9a8f072ab2"), // update.rpf/dlc_patch/mphipster/x64/models/cdimages/weapons.rpf
    ShaUnpack("e3a7aa2061cde73cc25ef6f39b45d99a614072ecb6db1f8e5577dbc1b226fe87"), // update.rpf/dlc_patch/mpindependence/x64/models/cdimages/weapons.rpf
    ShaUnpack("d27b7e84d93060c4bab3efbe825e7ae43ace5bc4de4d94a466d58c67f98fb296"), // update.rpf/dlc_patch/mplowrider/x64/models/cdimages/weapons.rpf
    ShaUnpack("36a44605b89080160d2f27f52961b3e11a95664aca41406c85f5c46adae534c4"), // update.rpf/dlc_patch/mplowrider2/x64/models/cdimages/weapons.rpf
    ShaUnpack("a0a934f529b7f1ed8e22676dedbbdeddfa6976abe8fe71169dc0a99b8ba09b86"), // update.rpf/dlc_patch/mplts/x64/models/cdimages/weapons.rpf
    ShaUnpack("2fe582e7c90e3ba2de805afe83598ea7516135b172b3c0900a2fa166cfcbd310"), // update.rpf/dlc_patch/mpluxe/x64/models/cdimages/weapons.rpf
    ShaUnpack("0f30979cd82fbd567506c595fc24dd5f10e5726b56e392b9f3c432849b3c6c11"), // update.rpf/dlc_patch/mpluxe2/x64/models/cdimages/weapons.rpf
    ShaUnpack("0d447be917fc96b8a7c2f766d787eb8efc9972cb3ec046653e36b4bec9eb3d5a"), // update.rpf/dlc_patch/mpsecurity/x64/models/cdimages/weapons.rpf
    ShaUnpack("fa08e5309064c92878176e16bb8efe66dbc43242ce59224b5c2a31d4c62033b1"), // update.rpf/dlc_patch/mpsmuggler/x64/models/cdimages/weapons.rpf
    ShaUnpack("75a261c26b9b818ba9db2aa56bfaf63012e0b916e3f266d011c60fc2e03cdb93"), // update.rpf/dlc_patch/mpsum2/x64/models/cdimages/weapons.rpf
    ShaUnpack("99280b77dbd505ff9332d815d3017e05dfc820970439fe048a9d5aed682ff33e"), // update.rpf/dlc_patch/mpvalentines/x64/models/cdimages/weapons.rpf
    ShaUnpack("94882344bc84b63d6218fcfbdf698b9eebe6a0b5c2ad28502cd0ca38e8a796b2"), // update.rpf/dlc_patch/mpvinewood/x64/models/cdimages/weapons.rpf
    ShaUnpack("e0823a292fb4cd1769c2098417230cb66a4cee7591f86df01581421ba0e86a3f"), // update.rpf/x64/data/cdimages/scaleform_frontend.rpf
    ShaUnpack("103c18f36b3781e94c63f0d7680c44a825ad2687dec62697b2c11855b9f72d21"), // update.rpf/x64/data/cdimages/scaleform_generic.rpf
    ShaUnpack("ec7e412f6662063eb41b059a77800cb862dbc4e085285155b9a5a9021ec390e1"), // update.rpf/x64/data/cdimages/scaleform_generic_2.rpf
    ShaUnpack("7bfd409b676d905db60bd014b383dba67b1a9d45a3f5198c1bb1ee5b6085129c"), // update.rpf/x64/patch/data/cdimages/scaleform_minigames.rpf
    ShaUnpack("76f36a06ff64a0fcc4cc00d8d22b3b308ae5f75bdab20231a373c9089bf8bc5b"), // update.rpf/x64/patch/data/cdimages/scaleform_minimap.rpf
    ShaUnpack("7cdbfdd3859c19afe1eea7fecc8a69b5d480b589e11c8a4c0e5f0760a76b5cae"), // update.rpf/x64/patch/data/cdimages/scaleform_web.rpf
    ShaUnpack("83ffde5ceb3cf756432de81cfccdecbcc9cb0e5b58d30ae4e8edf0abcdec6717"), // update.rpf/x64/patch/data/lang/american_rel.rpf
    ShaUnpack("ea860a3d654c2428a85c9d9d1733037e1d4b723f9ec874f939563b8145ebd10b"), // update.rpf/x64/patch/data/lang/chinese_rel.rpf
    ShaUnpack("d156b347d62c55c86868181901ad228bb32f142b180600ca30283ae13127877e"), // update.rpf/x64/patch/data/lang/chinesesimp_rel.rpf
    ShaUnpack("c165df812d98ebf6e66c159623de031885cf721de20d3d3be7b317260b15ade3"), // update.rpf/x64/patch/data/lang/french_rel.rpf
    ShaUnpack("dcfa871c86c3070036dd0a2eab16b3bbffa9c6d50397c98bb7c2e745bb34ff57"), // update.rpf/x64/patch/data/lang/german_rel.rpf
    ShaUnpack("fbe81264fa6efc7b765760cb5d1996cb9b75a4d867206ff946180a2f706e6962"), // update.rpf/x64/patch/data/lang/italian_rel.rpf
    ShaUnpack("c8575d302f1c11118e627ac856a639f32f594c3bb59110042da621e5adee0738"), // update.rpf/x64/patch/data/lang/japanese_rel.rpf
    ShaUnpack("6a573fb210a0b715a72e02dc5f2a26496fea94f7d52f9539e2219a6a7ab273a8"), // update.rpf/x64/patch/data/lang/korean_rel.rpf
    ShaUnpack("0438550309047b8b77b5a175a277d0dd5289fbd4786f13bc1fb9ca1a8696420c"), // update.rpf/x64/patch/data/lang/mexican_rel.rpf
    ShaUnpack("36ff9cec46a50b09530f8be9dea52a8f9480690e5f791794934fb7e8c287d2f3"), // update.rpf/x64/patch/data/lang/polish_rel.rpf
    ShaUnpack("dcd1e73602314fe6a358952a233276cadb0b12f0693624fb0f5e0f7071b51383"), // update.rpf/x64/patch/data/lang/portuguese_rel.rpf
    ShaUnpack("e5bc68963a0d26ee3472d9a28a20652cb58e32aa739dc7800c221f22ef74ca7e"), // update.rpf/x64/patch/data/lang/russian_rel.rpf
    ShaUnpack("3babb2819d4dba3afdc1ea84741c3d97fb0921d5fc81807c78a669a76c43b698"), // update.rpf/x64/patch/data/lang/spanish_rel.rpf
    ShaUnpack("951c1f68d8de4f117051eb5e7ac6991cf6b15feb34e125a265198bef87da2046"), // update.rpf/x64/textures/script_txds.rpf
    // 3179/update.rpf
    // 2612/update2.rpf
    ShaUnpack("61212715d2581ed3344237d97c572d107e67a14d93a600fda9ba1cea8763b7dd"), // update2.rpf
    ShaUnpack("5620b599443f8d4d8dc674e8f21b162e85493ecccf0879a63a2d2e948c41e20c"), // update2.rpf/x64/data/lang/american_rel.rpf
    ShaUnpack("6b17024e9a664c36efdc192dfc2ace87b8cd5a05e164bb0b597b9b808d8ca75c"), // update2.rpf/x64/data/lang/chinese_rel.rpf
    ShaUnpack("8411f3506fa41585ad52fd90876070bb984dab0a87f2efaf51c2f07a0f0a33a1"), // update2.rpf/x64/data/lang/chinesesimp_rel.rpf
    ShaUnpack("808db1cfb2a100a0ed24e85a9f896a625835030d39a93402001d99d122e8e609"), // update2.rpf/x64/data/lang/french_rel.rpf
    ShaUnpack("1a7769945d77adb9f9af316b098a5986a4ba5b597e1512f2ae983ffb563a13e4"), // update2.rpf/x64/data/lang/german_rel.rpf
    ShaUnpack("c4318bd43a379e7096f9c712554506fe752fc6c83abaa052d490ea82bbd105db"), // update2.rpf/x64/data/lang/italian_rel.rpf
    ShaUnpack("b77a356f7b5614936b526d308087ccfa481426a6bdc62511590398741b17e2d9"), // update2.rpf/x64/data/lang/japanese_rel.rpf
    ShaUnpack("0b5209e01a36e2dce93373c002a7d2ba7239a2d942179c3bae5ca9bc0fc7cc41"), // update2.rpf/x64/data/lang/korean_rel.rpf
    ShaUnpack("535769c3cd4baeac525e5d18b2a524215a0ae1edfd04ccb0dd0738a189505bd6"), // update2.rpf/x64/data/lang/mexican_rel.rpf
    ShaUnpack("02e5246f3a8aef1a8385e3da88638598e248ea23e1a0de61fc638f5080cc9e7c"), // update2.rpf/x64/data/lang/polish_rel.rpf
    ShaUnpack("3e04422a523676a178afbb65b89f264aaf881be03f9bd3cd681e076bc1524980"), // update2.rpf/x64/data/lang/portuguese_rel.rpf
    ShaUnpack("11a4afa00c1b15de778df703e253566cffc33fbe60667165c16e9f75bc056642"), // update2.rpf/x64/data/lang/russian_rel.rpf
    ShaUnpack("d93476b32769d6e1118ca43b2cb1bc2ad9b9a223421eca1992ef5610cbf529bf"), // update2.rpf/x64/data/lang/spanish_rel.rpf
    ShaUnpack("f99f4cb173b7b0e76c4df0d3e98cc40702dd24b56bd49d4efd04c225992ce201"), // update2.rpf/x64/levels/gta5/script/script_rel.rpf
    // 2699/update2.rpf
    ShaUnpack("5d6f6534ee6efcdff58dc14911fde0516d0f1e150eb6e5489f17afe98785245f"), // update2.rpf
    ShaUnpack("9f888681fba7061ba87b81af281289fab3fff1a6029157bbcadf43aaab8eb5f8"), // update2.rpf/x64/data/lang/american_rel.rpf
    ShaUnpack("a3e35aa7d7a0bb42a6001d58b3017678d2d96693718117d2409bdb832ba6b7e1"), // update2.rpf/x64/data/lang/chinese_rel.rpf
    ShaUnpack("9948ffde08c5effdfe2123399f054d297abd54e9d3cbecd4917cc798fcd38448"), // update2.rpf/x64/data/lang/chinesesimp_rel.rpf
    ShaUnpack("c28fdbc95e65c0df0913846530e261d13f9dd71bb12dbb199bfd462aa1415054"), // update2.rpf/x64/data/lang/french_rel.rpf
    ShaUnpack("78c0308f32b4c6a290088670b813565969899dd7ea1f02c46f91ac9d5431b33d"), // update2.rpf/x64/data/lang/german_rel.rpf
    ShaUnpack("8c2e044970889300b150e35609872ae5a862ec92a5d4eaa4a6486831bf8a3350"), // update2.rpf/x64/data/lang/italian_rel.rpf
    ShaUnpack("4beeabb4c085f58ae972bab9ae321645972b81307bb2ac38c5b4a0fc45acd6ad"), // update2.rpf/x64/data/lang/japanese_rel.rpf
    ShaUnpack("9fb8a5f66229fec77eea7327f5b9a71618801e0dc7be83c4ea0d10c18fd82bf7"), // update2.rpf/x64/data/lang/korean_rel.rpf
    ShaUnpack("60e6d54da5c2f85d9cc43eded653ea7a700bc69ccb29ede69f60b2cd6158717c"), // update2.rpf/x64/data/lang/mexican_rel.rpf
    ShaUnpack("8b73cf3da8ccb8c952dc64a97e2360d86e45d5a90c403e11895e0f2609b130a8"), // update2.rpf/x64/data/lang/polish_rel.rpf
    ShaUnpack("7e7d7f9fc77203b5cfa4f90097de495fc34918817e4c6d366c0967dd15bcfc6c"), // update2.rpf/x64/data/lang/portuguese_rel.rpf
    ShaUnpack("c89b29a15f8eb80550db5c408e04713a552872759d9c345a9773377b8013ffbc"), // update2.rpf/x64/data/lang/russian_rel.rpf
    ShaUnpack("e91bf2ae6c5ba534ba7a9c43cd42ec6f3e2674cbbf7344d8d8d35b46ccc2245f"), // update2.rpf/x64/data/lang/spanish_rel.rpf
    ShaUnpack("d203a643f1b3da5d4140f7bf953395c25f7e21578c340e3833d26e32d6e9c885"), // update2.rpf/x64/levels/gta5/script/script_rel.rpf
    // 2802/update2.rpf
    ShaUnpack("a0728fdb1f6db30c16b2a22771e7530f0121f0ba84aee331fdacd129e10ba2e4"), // update2.rpf
    ShaUnpack("47dd811f424b338ae3a8bef668854cc3ca4aad1993dd869eebb40bab8869fc32"), // update2.rpf/x64/data/lang/american_rel.rpf
    ShaUnpack("a63c012079c67aad41d900513d3296d82e1aa5bb9cec424de940ae67255438a6"), // update2.rpf/x64/data/lang/chinese_rel.rpf
    ShaUnpack("9818f3246b48c22fc9e5c4252396cebd869e018dff156e0a35022e2cf8f1e9e3"), // update2.rpf/x64/data/lang/chinesesimp_rel.rpf
    ShaUnpack("6d7a54949ee0ff630f2434f444f43fd156b1e023135303960b86ab5158117a7f"), // update2.rpf/x64/data/lang/french_rel.rpf
    ShaUnpack("4cf5b3c77009af8a63896dc3e51b27ce5798a0fd17bf6b4e8fbd2dcdc36a0be1"), // update2.rpf/x64/data/lang/german_rel.rpf
    ShaUnpack("d8ea05c95c286089846eede49c59a3bba460819403fd89ed02d6501b27b32caf"), // update2.rpf/x64/data/lang/italian_rel.rpf
    ShaUnpack("a885835e5ff0edde9fbd9eacdad8d93b787e474a18b85f504e320ff7d0dbce47"), // update2.rpf/x64/data/lang/japanese_rel.rpf
    ShaUnpack("b4e6269be66b1a2b4c85e7bc7e6c139efbf55601854e13440c6053deaef2c4fd"), // update2.rpf/x64/data/lang/korean_rel.rpf
    ShaUnpack("b5559755e27c1ca7737f753d303e63656b5f193f469683440c7f9c0f989b2430"), // update2.rpf/x64/data/lang/mexican_rel.rpf
    ShaUnpack("cdcebb59b19224f31e7c6e602a38c7daab4d4186facfa9e824c442c43a4fc5a4"), // update2.rpf/x64/data/lang/polish_rel.rpf
    ShaUnpack("8bad31304e5083358ffbbb5dd81b2e2ba434a6ce298c256867f3ddfc29d0d732"), // update2.rpf/x64/data/lang/portuguese_rel.rpf
    ShaUnpack("3a305c5a31015ac72257adead6955ecc5376384b30a44eecd5d88b0525cdc9b9"), // update2.rpf/x64/data/lang/russian_rel.rpf
    ShaUnpack("497ddddffcbeb12fb3cd733d05d8ad95a8e9f608427dc1b654e4676da0caf326"), // update2.rpf/x64/data/lang/spanish_rel.rpf
    ShaUnpack("153772f98218ee648d36160ef69f039062d3a990a852bafa1d366cf8d78d0033"), // update2.rpf/x64/levels/gta5/script/script_rel.rpf
    // 2944/update2.rpf
    ShaUnpack("ab450cbb009f97a13ac8de776c839feb94504ab26b6cb70fba834a48296d4298"), // update2.rpf
    ShaUnpack("3ccc8c1cb92a38daed59f885fea5f2ba2e22172766279b54f3616dad9a9ee76c"), // update2.rpf/x64/data/lang/american_rel.rpf
    ShaUnpack("466136fd9c4d190c4e225bac92ef44eb8edc612af403dfb334ffb78228bb7f5c"), // update2.rpf/x64/data/lang/chinese_rel.rpf
    ShaUnpack("aa6b8b8e4326c84596c143627f6f3ca7cbaa2d8793bbf9c0df6b524e24f3f269"), // update2.rpf/x64/data/lang/chinesesimp_rel.rpf
    ShaUnpack("0e9f25ad9238e337c30f0b659696321f5a6cd041f7882d985fe81b73c05f45e7"), // update2.rpf/x64/data/lang/french_rel.rpf
    ShaUnpack("92a7ebee236810c65472c89505eaefcdf64982ca130c3e50a9e6bdb00fd6ff87"), // update2.rpf/x64/data/lang/german_rel.rpf
    ShaUnpack("2c29243fe2d152393b4dd6e9691b1227828d3230d030d224cb05e8bc614937b7"), // update2.rpf/x64/data/lang/italian_rel.rpf
    ShaUnpack("d1f78419a8943b30bb92cf418e73ba1940ee187e55ba4cddb905e376e5a4df53"), // update2.rpf/x64/data/lang/japanese_rel.rpf
    ShaUnpack("966e4a17b13694e97c73ae11855ac2f2570b0ace6b6ecdcfee67a10ff788afbb"), // update2.rpf/x64/data/lang/korean_rel.rpf
    ShaUnpack("d81492db054a6b162f95a1009d08246abd538217658fffb2c975aec5a278e2f0"), // update2.rpf/x64/data/lang/mexican_rel.rpf
    ShaUnpack("3491c15cfb5cbe916b1232ab07db37b4020b537896b6695377e0fe84a59ec20c"), // update2.rpf/x64/data/lang/polish_rel.rpf
    ShaUnpack("e016101302781accb93ab90dbe4ea55f87e8a17371e565c904bf0920eda74188"), // update2.rpf/x64/data/lang/portuguese_rel.rpf
    ShaUnpack("480c4c12953729e848e66d6a47af21157d11b701f1cb4cc07ce81e205901df0f"), // update2.rpf/x64/data/lang/russian_rel.rpf
    ShaUnpack("ee7104a61839956c79029552af21cf802124c24008dd0c9cea57b6ee450e81d8"), // update2.rpf/x64/data/lang/spanish_rel.rpf
    ShaUnpack("53b61d8ee3d13938914be01ec5b25d8f50198d850dd52e73929e0168d88265a0"), // update2.rpf/x64/levels/gta5/script/script_rel.rpf
    // 3095/update2.rpf
    ShaUnpack("75603205d68dc93377506e0824f0cfd924695121b509e644b95027c6a60feba9"), // update2.rpf
    ShaUnpack("d86c30f5dd31c46e75f5fff0f6c11dd74a5bf8e976c500eb632bca911f976785"), // update2.rpf/x64/data/lang/american_rel.rpf
    ShaUnpack("76b1b79d4c6fcaa7276204213f13615dd62a5022966f2bce91bc71551f3d9570"), // update2.rpf/x64/data/lang/chinese_rel.rpf
    ShaUnpack("60efa88af08e11bd1930cd126855656e1e16893e2fe5e732ffed271ad1ac29ed"), // update2.rpf/x64/data/lang/chinesesimp_rel.rpf
    ShaUnpack("2665c540b20ce804e4e6f463d4a0b24fd682ba2d23316dc4d46bbcc4aca0e7d7"), // update2.rpf/x64/data/lang/french_rel.rpf
    ShaUnpack("7ba9f3a198d09a48ab8d2b56a6471ed3aad11ec89594900e576edfd8eea09126"), // update2.rpf/x64/data/lang/german_rel.rpf
    ShaUnpack("f5be168ba2e62ba87f722ab60f72358a89040fc6a89715a8c9a63710e7071cb2"), // update2.rpf/x64/data/lang/italian_rel.rpf
    ShaUnpack("860f139aa5c601eef09aa6de022fdcd54b96634e460ffa786b4f6fb0cf39ac97"), // update2.rpf/x64/data/lang/japanese_rel.rpf
    ShaUnpack("060c4cde6093a1df0cfa949336ec052acec23aabd8a9989da4e72431d10c5913"), // update2.rpf/x64/data/lang/korean_rel.rpf
    ShaUnpack("25b88c025663926de014dfc9d559ea39f05d64b124b28fe9fd7455b225b8db71"), // update2.rpf/x64/data/lang/mexican_rel.rpf
    ShaUnpack("2ea3ee986b4c96256af8a5e119e9a4493dc56cbd87fd6dd2ed70d1a1866dcd0e"), // update2.rpf/x64/data/lang/polish_rel.rpf
    ShaUnpack("11171d06471e9af4b7655f0a69f78db458937e8611853b665d1947f8138c42b7"), // update2.rpf/x64/data/lang/portuguese_rel.rpf
    ShaUnpack("d82fd80c7b9ed1292773f289fdc555397c3ac9ae8ced50c8efd0ed670da543d3"), // update2.rpf/x64/data/lang/russian_rel.rpf
    ShaUnpack("ec15736dc2ea9634d1cdf087fa5fb72647a2316a6eddae2e6a19c3c729ea7802"), // update2.rpf/x64/data/lang/spanish_rel.rpf
    ShaUnpack("bc26dff6259eeed48c3da286dc677a98ee2a2602c06e3c9c3081c20b93772151"), // update2.rpf/x64/levels/gta5/script/script_rel.rpf
    // 3179/update2.rpf
    ShaUnpack("356abd389c8181627a7623cd0a9d249f959723abcf5a1c11c655855844156680"), // update2.rpf
    ShaUnpack("07553ba68a510260e99106f78d4aab285bebb81edfc756ab2f44f5bc7823500f"), // update2.rpf/x64/data/lang/american_rel.rpf
    ShaUnpack("b860163955ce22f4f8b6cbe790cb04a6c2e0f28b67fe0e5baa15241889639abc"), // update2.rpf/x64/data/lang/chinese_rel.rpf
    ShaUnpack("2b5f34e0de6528a5a2421169ccd7e5358ea9d63ee5f8943244f9e524fade907c"), // update2.rpf/x64/data/lang/chinesesimp_rel.rpf
    ShaUnpack("c98f4449b617842ec257a8393bce174ff63d1c43f6338ee0286515581e01f3e2"), // update2.rpf/x64/data/lang/french_rel.rpf
    ShaUnpack("97f912e56da4d2422f1adc19dc70edbf62c090cd350dfc912ebbb824b1322798"), // update2.rpf/x64/data/lang/german_rel.rpf
    ShaUnpack("2c98e584b0b686c72e0298bfa71ba4a447353a8cbff3d5c4d5253c0a8533a6ca"), // update2.rpf/x64/data/lang/italian_rel.rpf
    ShaUnpack("c51e3cb1b245b135ada8e278fe6611b0f0c301966d00bb996d202264d819734e"), // update2.rpf/x64/data/lang/japanese_rel.rpf
    ShaUnpack("f094f541509142598510cf3fea31a4fa8e3cf108f2e5527a8b5cb2e926303d4f"), // update2.rpf/x64/data/lang/korean_rel.rpf
    ShaUnpack("8f0241ddd4bb5299caa8f9dc8fb092d3e1d2e1666d10d10b1bc184830bd89c97"), // update2.rpf/x64/data/lang/mexican_rel.rpf
    ShaUnpack("52700e3b8de3f0ab1259da742896890907b6048845baf501069385f679dec2e4"), // update2.rpf/x64/data/lang/polish_rel.rpf
    ShaUnpack("2efd9200b8b34b338ccf88f90057b8fe9b1f1ef5c6e10f4b820f2a93651c304b"), // update2.rpf/x64/data/lang/portuguese_rel.rpf
    ShaUnpack("e3a9bc6e11548da7bf9461f69d0974a7941d37c55e61c5faefa78938b0cdbeef"), // update2.rpf/x64/data/lang/russian_rel.rpf
    ShaUnpack("6b7cb19bf334abb29162f51228fed749af76f9a9d259b81e3b5d609f676cd188"), // update2.rpf/x64/data/lang/spanish_rel.rpf
    ShaUnpack("1bb2c1d1af3fc523855a450668f0f87939cb62890d382be3d9436d3d931e7660"), // update2.rpf/x64/levels/gta5/script/script_rel.rpf
    // update/update.rpf
    // update/update2.rpf
    // 3258/update.rpf
    ShaUnpack("8204f8df26965073a8518ddb26f54654bb1b0da039a9ed77ad6ea68fb5b3a229"), // update.rpf
    ShaUnpack("8c68d958ffa9e320180543f6d7f3af8de0586f2e2f2de8990988b7c333eaa22d"), // update.rpf/dlc_patch/mpchristmas2018/x64/data/effects/ptfx.rpf
    ShaUnpack("be871a7070475224af51d9348f5ad97e1170b380ec580124526492415ccaefe1"), // update.rpf/x64/data/cdimages/carrec.rpf
    ShaUnpack("80d62e4a3c82fd91f2b7fe42b97a31abaf05b600f0e3fd36dfc8e917e30af67f"), // update.rpf/x64/data/cdimages/scaleform_frontend.rpf
    ShaUnpack("29eb90155249f45d86394700bfc97fba18e95bea948cf6aba2f61daa542023c9"), // update.rpf/x64/data/cdimages/scaleform_generic.rpf
    ShaUnpack("db3207a586cfa8044fd2e0fdc3fc22d31d4005079b53b565d476ce759de9ca49"), // update.rpf/x64/data/cdimages/scaleform_generic_2.rpf
    ShaUnpack("eb7473b1660366b49be59f8a0ff46970ae65c3d82d933e02e0f4c94be7664417"), // update.rpf/x64/levels/gta5/waypointrec.rpf
    ShaUnpack("485867196caa9b3fc9b8441cbd0d240a51b6e8194ea425e78539b5f85b7b0b82"), // update.rpf/x64/patch/data/cdimages/scaleform_minimap.rpf
    ShaUnpack("891abbdbb9fc447f10d718fb43c376037f26e97e8d75c0022f50f143654c28ea"), // update.rpf/x64/patch/data/cdimages/scaleform_web.rpf
    ShaUnpack("3584b0fa211bec0513097565b908946f5b346a75a12757ed3b096c6e9abaa97b"), // update.rpf/x64/patch/data/effects/ptfx.rpf
    ShaUnpack("6647b57008cd49f887b29f32490f417c25477656f1f264057da3d0194a245384"), // update.rpf/x64/patch/data/effects/ptfx_hi.rpf
    ShaUnpack("ae09e8cd072d96d85d6ffc4dec7a8a83f836cb160e12793716b54c3ca9bccb40"), // update.rpf/x64/patch/data/effects/ptfx_lo.rpf
    ShaUnpack("dde30548e4b9e51dd0c8e002c4f1f2235d168ad4deb54d4aaf127c9857f85e06"), // update.rpf/x64/patch/data/lang/american_rel.rpf
    ShaUnpack("0e4d767ec7b46764708e8487d79d2d2965518faa4f814bc60a422fc053f06075"), // update.rpf/x64/patch/data/lang/chinese_rel.rpf
    ShaUnpack("1857acff0701c54b4323062d802e4fb32c2f600308aa72261f14aabc63e17023"), // update.rpf/x64/patch/data/lang/chinesesimp_rel.rpf
    ShaUnpack("3761451856a85b680429b627446765b4b5bbaf8a5df61dcccafea4b0852fc89c"), // update.rpf/x64/patch/data/lang/french_rel.rpf
    ShaUnpack("6a7f0a1d10b0b904c686931d4e2db0fde50de37b902b0f99cf5fc7dae1741c54"), // update.rpf/x64/patch/data/lang/german_rel.rpf
    ShaUnpack("28230e8ac870ef34ac587fb656daea0b4bf42768227dfb00a7cc855049f12d58"), // update.rpf/x64/patch/data/lang/italian_rel.rpf
    ShaUnpack("f376f366d29b3f719ba5c0ddb7760661c08d1aa7f3401199e305fa5ec14dade1"), // update.rpf/x64/patch/data/lang/japanese_rel.rpf
    ShaUnpack("a6374af1376fe5862590e04594690636dbcb139fb39936475f79534997698b02"), // update.rpf/x64/patch/data/lang/korean_rel.rpf
    ShaUnpack("489e20853b81b128934c2af9f9e6cc569865e15a5928cfacd538637171f2a8dc"), // update.rpf/x64/patch/data/lang/mexican_rel.rpf
    ShaUnpack("770a466b95c2601e0f890e261d564af38f38790afca37ac9544e8a483090eeba"), // update.rpf/x64/patch/data/lang/polish_rel.rpf
    ShaUnpack("3a39e037c94c783dd65443afc4dd353dbd8d2c2632ae747b18c04f0671c474b2"), // update.rpf/x64/patch/data/lang/portuguese_rel.rpf
    ShaUnpack("a51616cfd8ee531dcd7403b81a54ce2ca7513e64072b9801cc6f98b3170f5ad3"), // update.rpf/x64/patch/data/lang/russian_rel.rpf
    ShaUnpack("2382d2755fd79ac7f0686b9f2e01cf7472b659ad3b09cd779f4ef4c1d917f5db"), // update.rpf/x64/patch/data/lang/spanish_rel.rpf
    ShaUnpack("4925201e52974f900927fb058a719f517e6352a74450664011f066aace6b1657"), // update.rpf/x64/textures/script_txds.rpf
    // 3258/update2.rpf
    ShaUnpack("cb4297eb78cdf2537115de7e405b262858b5a143a59886c557d15b9f6e9e720d"), // update2.rpf
    ShaUnpack("500aeaff1605d4ce4f35c5bfc71a180ff2ff1d1e793ba36b871ab0c1d4e0a193"), // update2.rpf/x64/data/lang/american_rel.rpf
    ShaUnpack("3d9f75f3c1b8daac5c690009d65363691215775e6910b38c70bed27cbf96276d"), // update2.rpf/x64/data/lang/chinese_rel.rpf
    ShaUnpack("beedb751e9ec3422104adf915b615edc829e97b2be984e12098fbb6b9c410bc9"), // update2.rpf/x64/data/lang/chinesesimp_rel.rpf
    ShaUnpack("1d04c26b86355dd6e6cd9806392cab65021d07e5fdc7693642af52c01d60d2d2"), // update2.rpf/x64/data/lang/french_rel.rpf
    ShaUnpack("71385102113bce00dd49729d84c6e26e76713eb21714869645cce8208624c7e3"), // update2.rpf/x64/data/lang/german_rel.rpf
    ShaUnpack("393fe4c40a4ea2fcd2d62678d883ab315da1824b6ac24e0b7b12f72dde76e415"), // update2.rpf/x64/data/lang/italian_rel.rpf
    ShaUnpack("1dec3e35060139e357e83c6008b9915c5462586e5e500fde2bcf7e8f9f2db41f"), // update2.rpf/x64/data/lang/japanese_rel.rpf
    ShaUnpack("331dd3cede0f2cdb8db4517aaf637b84ebdbd7f01cc65f5b26219c078c65f677"), // update2.rpf/x64/data/lang/korean_rel.rpf
    ShaUnpack("a1e19008ea198f5e7789f73c8a9da0af0e71b9747a5f54beda4cfa9b28e6d3c8"), // update2.rpf/x64/data/lang/mexican_rel.rpf
    ShaUnpack("669c69f651df1a33620680d8f6f1762534e9141bf195c34b71bda1533cb1f0a9"), // update2.rpf/x64/data/lang/polish_rel.rpf
    ShaUnpack("b9882e40d6624706df5acda746aa4d974faca0307613ddcb96d8bd3282cfebb0"), // update2.rpf/x64/data/lang/portuguese_rel.rpf
    ShaUnpack("c2bfefac4448c3271c15c1eb39c81c405505a966f698bda3408032ac010cb526"), // update2.rpf/x64/data/lang/russian_rel.rpf
    ShaUnpack("828962418a93ecf93044d0a9a79f27b9ef528f040432eb3f97a6f47e2d99ec19"), // update2.rpf/x64/data/lang/spanish_rel.rpf
    ShaUnpack("d64a9acc5b84d3db6da92c62429e995e19e5aa621343a2759e11e12b9d4947ca"), // update2.rpf/x64/levels/gta5/script/script_rel.rpf
    // 3323/update.rpf
    ShaUnpack("e3101eae56495a4d0eafe450c04073e3a41e15b743888561ee44d644a4a11400"), // update.rpf
    ShaUnpack("824c778c183a32e05afe9f9181d46f9f60e070cb00857a2c32fa7fd8132b6347"), // update.rpf/x64/data/cdimages/scaleform_frontend.rpf
    // 3323/update2.rpf
    ShaUnpack("7d266cc3ccbf844193f695e1111ba028d392796ba1439eef9c0873cb0b49ed6b"), // update2.rpf
    ShaUnpack("b0da021d0df99b5b89e3697d236566f59edd818461037b32e917c8925fa22468"), // update2.rpf/x64/data/lang/american_rel.rpf
    ShaUnpack("8390727556bfc5a165c5cf2dbfe9e39ffbca62d5e80e12c5e8cfa98b2b009a24"), // update2.rpf/x64/data/lang/chinese_rel.rpf
    ShaUnpack("3ffcdddb8c20c2d076928550f629844f7e7bbee67cd75b41c9a545037ae80237"), // update2.rpf/x64/data/lang/chinesesimp_rel.rpf
    ShaUnpack("1fd69d06075fa33c528e651669fc75c5d50f6eaffd784c488058beb615a57f32"), // update2.rpf/x64/data/lang/french_rel.rpf
    ShaUnpack("1da1c61739793b39d2ec4eb86bcdb356d8479d9bddeff561a0ebafc208c36ea2"), // update2.rpf/x64/data/lang/german_rel.rpf
    ShaUnpack("629bb2c20cea9ab31f7e7c270dd4e459939d2cf6a4bd24298cc0f302e4a0e85d"), // update2.rpf/x64/data/lang/italian_rel.rpf
    ShaUnpack("9a355be1c150c8bb46a251adc3c8695d3eedc24c5d466bb6389ea8a769e764d4"), // update2.rpf/x64/data/lang/japanese_rel.rpf
    ShaUnpack("fcdd3f694222b58447fda6ff31394cf146fa4cd0825012de1a699d608edd51a2"), // update2.rpf/x64/data/lang/korean_rel.rpf
    ShaUnpack("b484024762292b06fe83108c71d89944a4c928333089aa6a679ff191d4ed38f6"), // update2.rpf/x64/data/lang/mexican_rel.rpf
    ShaUnpack("7ca239eadaa75c57bfe2a38c8478ca5fb4ff1da2e8aec69393ea776adf7b7fed"), // update2.rpf/x64/data/lang/polish_rel.rpf
    ShaUnpack("cf1418b6526e5dc05b699743c9caada1e3cd9e717c6997fdb441a27b400df0e6"), // update2.rpf/x64/data/lang/portuguese_rel.rpf
    ShaUnpack("c9a7b8187442edf2e624501ecd5bcc19cf9ed31343e7388a28ecaff7a1a06c3e"), // update2.rpf/x64/data/lang/russian_rel.rpf
    ShaUnpack("e7282580378be4407de22388000e617787af93c6920779e66f444f3469ae3f64"), // update2.rpf/x64/data/lang/spanish_rel.rpf
    ShaUnpack("657fd2cd6040159bb51011b8f4f64b60c02419a93bb537ec4c24dd25a3ed0f63"), // update2.rpf/x64/levels/gta5/script/script_rel.rpf
    // 3407/update.rpf
    ShaUnpack("c18da40f5bf1f8876bffc70d19a74c2369a1b85848228d57f261099cf7be695f"), // update.rpf
    ShaUnpack("1120922a62bec5576cb57eb6b0e82c8003e5fad13d1b9252999d63740cc2abc0"), // update.rpf/dlc_patch/mpheist/x64/data/effects/ptfx.rpf
    ShaUnpack("42555a0c2f4b35a411b17e704a3c1f251cb06c0c76c3e9a6e65c0be41bb06bc5"), // update.rpf/dlc_patch/mpheist/x64/data/effects/ptfx_hi.rpf
    ShaUnpack("bf95bed8b3ab1ccf19b8d259b2171cdb92bee2f01504f1dc90770d7665e8e186"), // update.rpf/dlc_patch/mpspecialraces/x64/levels/gta5/props/mp_specraces_tubes_spirals.rpf
    ShaUnpack("cb31bde4b38800a98b1b7e0f0c45aebc57094a27bcf780cca5c197b202a8f14e"), // update.rpf/dlc_patch/mpspecialraces/x64/levels/gta5/props/mp_specraces_tubes_waves.rpf
    ShaUnpack("5048e7312d318455f4cabebb36b25179dd7392bb671e7894b12081e000940753"), // update.rpf/x64/data/cdimages/carrec.rpf
    ShaUnpack("c663662a5702531195887aa65464fcb3cfd2e9a1c7d803f732047c73e6ccff89"), // update.rpf/x64/data/cdimages/scaleform_frontend.rpf
    ShaUnpack("acbd5c4a9ac49fb5abb254e14a8f830f6014f6cad119d9d83080a9dc0b83348b"), // update.rpf/x64/data/cdimages/scaleform_generic.rpf
    ShaUnpack("677722deabb698750ef5c968cbadac2cbfede08c20da6afcf98244a63ac5a0ec"), // update.rpf/x64/data/cdimages/scaleform_generic_2.rpf
    ShaUnpack("159a0d840e5e508a7895f8aee5efa318385cf02ec6a9b6f3e8558584fc5827e9"), // update.rpf/x64/patch/data/cdimages/scaleform_minimap.rpf
    ShaUnpack("a67928d3ba57c0a4d43737b4c2dce7bb78791d3783e4ffa068d085f05c5a3319"), // update.rpf/x64/patch/data/cdimages/scaleform_web.rpf
    ShaUnpack("e9cfb36897f5a9f20774941b248c05944800e5157d0174cbcced3ba7ca9eb814"), // update.rpf/x64/patch/data/effects/ptfx.rpf
    ShaUnpack("47aaa7fde828335eddd6eac31e2be008bd5b7bfa756252471ee3642ac8a952aa"), // update.rpf/x64/patch/data/effects/ptfx_hi.rpf
    ShaUnpack("26bee59a884849f7f88dce8283b2d67f186b70dca0ee157dbdb4e04f23412a92"), // update.rpf/x64/patch/data/effects/ptfx_lo.rpf
    ShaUnpack("44ccb2e112286163207df776c6a45f6607ceb4c7aea859459506253e81e59e48"), // update.rpf/x64/patch/data/lang/american_rel.rpf
    ShaUnpack("1f297d68ad68296255a5a7b1cf707fc75783f096124d47f68a3dff919632b46e"), // update.rpf/x64/patch/data/lang/chinese_rel.rpf
    ShaUnpack("8455f6c03d388bae7d21b86e8864249406f017ebb154f48ac924ea20d896afe2"), // update.rpf/x64/patch/data/lang/chinesesimp_rel.rpf
    ShaUnpack("05f27ffe192606bc428b5b800bbe8e96968a542ff3f50b9df6664a8da2168b8f"), // update.rpf/x64/patch/data/lang/french_rel.rpf
    ShaUnpack("6cd3a6df38b6337e81a65e475259f06b7fb8585bdbbdeeccc796969f867914a3"), // update.rpf/x64/patch/data/lang/german_rel.rpf
    ShaUnpack("8b3c2748d7c7af069c33ab50444f65998f34de4934d5a9da5bd0c29f29303e8d"), // update.rpf/x64/patch/data/lang/italian_rel.rpf
    ShaUnpack("c6b2206c9c145cad79aa4e47029c8e3f33e30302887c728aae5131f83d1552dd"), // update.rpf/x64/patch/data/lang/japanese_rel.rpf
    ShaUnpack("8fd451b7ff6bf8302b14212ad3ecb6af8a71d7548e41247cfd7eb8220d5962c4"), // update.rpf/x64/patch/data/lang/korean_rel.rpf
    ShaUnpack("1f5e9a74b9c1606888851e6ea04ce06cc29f48ab58f4ac9eb5638a38b4a75920"), // update.rpf/x64/patch/data/lang/mexican_rel.rpf
    ShaUnpack("fe6fd5de93f27cf97ed0dec3f045cebf839f0ea55d600fa4864b8c440fad497f"), // update.rpf/x64/patch/data/lang/polish_rel.rpf
    ShaUnpack("73ebc5de062e4608213c2656b55eaba02b33b8c9a08ade0258c146820ab40a06"), // update.rpf/x64/patch/data/lang/portuguese_rel.rpf
    ShaUnpack("af2f473f22a569a156d575c33bda4db47a500d7cccc8819d69243abc04b8ab37"), // update.rpf/x64/patch/data/lang/russian_rel.rpf
    ShaUnpack("a4971ff09106e15479dd4510ff82d7c041ad6b7b4a4fe4f93f608a2bc6b7df4d"), // update.rpf/x64/patch/data/lang/spanish_rel.rpf
    ShaUnpack("8f7f914ec76baceda48c6514a53bb034e08ec0256e4ae91ed0b69185ad85e28c"), // update.rpf/x64/textures/script_txds.rpf
    // 3407/update2.rpf
    ShaUnpack("21980058f69a822cafb721d97e6a5ec667bbb54f43c9a9b0f4e07b3b78c5a151"), // update2.rpf
    ShaUnpack("3c5b6ca5a2737df40b13834c7871b647a3acea6a13df069bed477a72f8cdef51"), // update2.rpf/x64/data/lang/american_rel.rpf
    ShaUnpack("c4dc0af87676d00dc8ffcdb9559d7f9268ad94fd5ed81dc0ebd820414d9b7f6a"), // update2.rpf/x64/data/lang/chinese_rel.rpf
    ShaUnpack("51291accc8e748c66aa46e90245aae2e6ef8a07ab950b972d43910438a2dc99a"), // update2.rpf/x64/data/lang/chinesesimp_rel.rpf
    ShaUnpack("5170556e46f495f59a72c27e1238c102d760fce2fffdc34985635fcecb565a7a"), // update2.rpf/x64/data/lang/french_rel.rpf
    ShaUnpack("7417eb9b54243a6839e9d9cad20a8ebcdbf524d936aa4f644edd043408a2d580"), // update2.rpf/x64/data/lang/german_rel.rpf
    ShaUnpack("5f358b603f3926be9327a458a60531ad5212c74c697352caec6d8ef24fb24848"), // update2.rpf/x64/data/lang/italian_rel.rpf
    ShaUnpack("4c6d1e22485be63e51895290468a900a7ed681e39f5f5c3e6c1e06b7b4c49eb8"), // update2.rpf/x64/data/lang/japanese_rel.rpf
    ShaUnpack("7fc56fa3fc217b2d0a5db42267b46ad01dd82680952c3cc6222479b3f3dde9cb"), // update2.rpf/x64/data/lang/korean_rel.rpf
    ShaUnpack("fc4c45001b2ba429942a0e402fdb6f1f041a47a9dc69981e3a44b322462554d6"), // update2.rpf/x64/data/lang/mexican_rel.rpf
    ShaUnpack("079c6a1b0b96e1252d53cdbae8bf24e041447cc2ff85516e49e38ef245d8c6b8"), // update2.rpf/x64/data/lang/polish_rel.rpf
    ShaUnpack("36134db086755d49ae1ef3cdc9478b971dfb2e16a3ded9ea13dfdf538820f141"), // update2.rpf/x64/data/lang/portuguese_rel.rpf
    ShaUnpack("baf67a7eae63ad9e4037f3368d373bafdc945f7ef7d1b3716e589ff3f4d72661"), // update2.rpf/x64/data/lang/russian_rel.rpf
    ShaUnpack("931d083f74a4b5644bad428fce936fc7016fd198af1f4fcbd937f59a58502a8c"), // update2.rpf/x64/data/lang/spanish_rel.rpf
    ShaUnpack("a47efa481c9c26765e073bb1324f17f33cb2868d49f7e5274e4caa5e56f39018"), // update2.rpf/x64/levels/gta5/script/script_rel.rpf
    // 3570/update.rpf
    ShaUnpack("771569c28457a99f43a00fd73cadc39d7a81a903022466844be36d17b8972bb6"), // update.rpf
    ShaUnpack("6db5ab40f886fa223aea6fc2d1c40e8297caded5f239e63b48910a733e2deb82"), // update.rpf/dlc_patch/mp2024_02/x64/levels/gta5/props/prop_m24_2_barge_01.rpf
    ShaUnpack("d5ecfd7d34da84c0027d8294ca211ee08374add45c8a0c317019248154a57d1a"), // update.rpf/dlc_patch/mp2024_02/x64/levels/gta5/props/prop_m24_2_crates_01.rpf
    ShaUnpack("fc6879fc485348a80094cb062a52b869ca67300fb0bf51519d3b9437cef7da56"), // update.rpf/dlc_patch/patchday10ng/x64/levels/gta5/props/residential/v_electrical.rpf
    ShaUnpack("5331871c30d391c9c44564d3b24082c9bf472816abfe1c2d81cf81645970c94b"), // update.rpf/x64/data/cdimages/carrec.rpf
    ShaUnpack("ed94861a0687eb7a143bbc5431a6788775ce5d146737b00e2fe7c423e6d0dc21"), // update.rpf/x64/data/cdimages/scaleform_frontend.rpf
    ShaUnpack("ac22d6b0c896076b9ce45bad711ad48f4a1ed9e31fe0abc9594d545b8f1d8318"), // update.rpf/x64/data/cdimages/scaleform_generic.rpf
    ShaUnpack("920bbf19916a5fe13b285fdc726edcba8e91855ac2ad11547a71f2b17c9fbb3a"), // update.rpf/x64/data/cdimages/scaleform_generic_2.rpf
    ShaUnpack("336bab728359e63e15acc635ad3223c06f6b8d136b1dbb10fedbfb1283b7c440"), // update.rpf/x64/levels/gta5/waypointrec.rpf
    ShaUnpack("82a91c40d5c8e3878340052a8a761329977c2c036e040b2fa7beb6031e10f131"), // update.rpf/x64/patch/data/cdimages/scaleform_minigames.rpf
    ShaUnpack("50bd2fd106521dd5490508ac48f8abb85642786cd2b2781166725eb997dc072b"), // update.rpf/x64/patch/data/cdimages/scaleform_minimap.rpf
    ShaUnpack("6874bf353bcc6cdcf20eae661a1ad89aa414b9edc3a41037f01d4cacaa6d95ca"), // update.rpf/x64/patch/data/cdimages/scaleform_web.rpf
    ShaUnpack("bc76e58ca0dcdf7ad7299c0f4a987fda0e985db5793ea21a3e158fd53e3f5aef"), // update.rpf/x64/patch/data/effects/ptfx.rpf
    ShaUnpack("c87395f975cbb6b0232b73bcb581215c0e55fa45e9c78da0cdd478ff39f2642b"), // update.rpf/x64/patch/data/effects/ptfx_hi.rpf
    ShaUnpack("dee3a0bee1d1b2689a9b0503d8a865ba1d10ec26f1e4701545ddc72b7fe82d0e"), // update.rpf/x64/patch/data/effects/ptfx_lo.rpf
    ShaUnpack("dfc39c4b62589083e0e30ee1c15b0af2f80e36e682e17776e83a162522d9e61b"), // update.rpf/x64/patch/data/lang/american_rel.rpf
    ShaUnpack("905e030190cb7fb17a25c8b070baa297a8837e02d9def7599c50c04a43398358"), // update.rpf/x64/patch/data/lang/chinese_rel.rpf
    ShaUnpack("3fd2e0c2fec53999f9bb79cb83316f89ed62585266860374302a929d80fd905d"), // update.rpf/x64/patch/data/lang/chinesesimp_rel.rpf
    ShaUnpack("2d299cbf0d4f06e9ad4fdff46cc396b354cbeed365db8f16eb3e0c5d1578e342"), // update.rpf/x64/patch/data/lang/french_rel.rpf
    ShaUnpack("a4b2b767fbad7cf422aafbff44449d43b48726644847566f717ccc769d55176d"), // update.rpf/x64/patch/data/lang/german_rel.rpf
    ShaUnpack("f05cca25d1d72b22efe62d7f1097965506cfce148d00aaeae6c05bc4a5093220"), // update.rpf/x64/patch/data/lang/italian_rel.rpf
    ShaUnpack("948a99e8ccb347bcb67bbdc37d31df04bd669dc57e2036deb1d18b485da6b8d4"), // update.rpf/x64/patch/data/lang/japanese_rel.rpf
    ShaUnpack("92fb581d908e63f96420e8517fc5541759a7d68cfab644303dd8d622c83f9344"), // update.rpf/x64/patch/data/lang/korean_rel.rpf
    ShaUnpack("567f49a21117a89aad9a8be0e55397493342c4ae2fbaf26de433f2297f8bb451"), // update.rpf/x64/patch/data/lang/mexican_rel.rpf
    ShaUnpack("896fe6bc83db7c7975f62742be90f94f88654d29acc3e5538b56529b6f8ea4ab"), // update.rpf/x64/patch/data/lang/polish_rel.rpf
    ShaUnpack("2e738c1d5229c93b178b03a33eeb4e54583c81555cefe9a0fa32028b46f5d427"), // update.rpf/x64/patch/data/lang/portuguese_rel.rpf
    ShaUnpack("391905ec2e5f907c0b58c8c543218cb0a15b7532b0be50c190805efd39121d4f"), // update.rpf/x64/patch/data/lang/russian_rel.rpf
    ShaUnpack("96243c064e20d72f4d672d39df5cc988716986247c33616b725e02ec06a9e4a9"), // update.rpf/x64/patch/data/lang/spanish_rel.rpf
    ShaUnpack("30c806cd21221e836914e8557dba266272bc609e798af6fbe080b4624029b8aa"), // update.rpf/x64/textures/script_txds.rpf
    // 3570/update2.rpf
    ShaUnpack("1f488e0da8fe2420f961eed0a5c7e3235bce27a0c056884dfbfaf7405a899dd9"), // update2.rpf
    ShaUnpack("b060dae9c70b239d6ff0bceb7f992eb6f7aa57f8e7280edbe25413675c9b4d26"), // update2.rpf/x64/data/lang/american_rel.rpf
    ShaUnpack("f4d9e42e1ddc606e12496254886d17fdb3a01c22b2e9e4e0b72370e3ecf0eb4d"), // update2.rpf/x64/data/lang/chinese_rel.rpf
    ShaUnpack("bcce40d7b1a9f830e2722ae5eb94855f4630cc1fa2f7d46d9f0deb47bc7ca152"), // update2.rpf/x64/data/lang/chinesesimp_rel.rpf
    ShaUnpack("7941bc62c340eacd428bc93e3d764012b08b4b52812aab53218be1adbb9a7dd4"), // update2.rpf/x64/data/lang/french_rel.rpf
    ShaUnpack("b2d1bad57c352dc798d5c9165243de1f88711be0ce50bfae9c7c5e7f1addde08"), // update2.rpf/x64/data/lang/german_rel.rpf
    ShaUnpack("7f28f44f48212c057b83a058895dedc97fcaad53627e54857619801bc3e27ab5"), // update2.rpf/x64/data/lang/italian_rel.rpf
    ShaUnpack("58edf8d99ce8c8db12db4da74e291c8e71a687ccf953321d90ab6709d7f1edd2"), // update2.rpf/x64/data/lang/japanese_rel.rpf
    ShaUnpack("cc7430ff3904bfaeefd725680f8820e58503ceeaa60972300d9321f731d6546b"), // update2.rpf/x64/data/lang/korean_rel.rpf
    ShaUnpack("a19e9d32b019380736693513b710567e6334fe70ed797d778222a70d9d4273b3"), // update2.rpf/x64/data/lang/mexican_rel.rpf
    ShaUnpack("2b34e8b3ac3a8a42b151397d7ed59716b4da58db8afb5b39a10576c9dcb20f63"), // update2.rpf/x64/data/lang/polish_rel.rpf
    ShaUnpack("fe2727f9a100d247fbe89a530cce81700e48b3dc892dfad80f3471c17777d350"), // update2.rpf/x64/data/lang/portuguese_rel.rpf
    ShaUnpack("83618b631c87b299f2a337918e548d4909a86fcdaa952c209e3828fdd495b5ca"), // update2.rpf/x64/data/lang/russian_rel.rpf
    ShaUnpack("2ec82e579cf3c2a2928663c414c507ce83090442989a8abb3bb6bd597a519707"), // update2.rpf/x64/data/lang/spanish_rel.rpf
    ShaUnpack("2828bb490c39d56f20ef27ffc91d148536b3d183f4acb5dadfa202d77cb0f85a"), // update2.rpf/x64/levels/gta5/script/script_rel.rpf
};

}}
