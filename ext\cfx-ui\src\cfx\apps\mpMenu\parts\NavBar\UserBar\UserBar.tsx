import {
  Button,
  Icons,
  Interactive,
  Flex,
  Text,
  Title,
} from '@cfx-dev/ui-components';
import { observer } from 'mobx-react-lite';
import React from 'react';

import { useStreamerMode } from 'cfx/apps/mpMenu/services/convars/convars.service';
import { useService } from 'cfx/base/servicesContainer';
import { useEventHandler } from 'cfx/common/services/analytics/analytics.service';
import { EventActionNames, ElementPlacements } from 'cfx/common/services/analytics/types';
import { ISettingsUIService } from 'cfx/common/services/settings/settings.service';
import { useCustomAuthService, AuthUser } from 'cfx/apps/mpMenu/services/customAuth/customAuth.service';
import { useLegalService } from 'cfx/apps/mpMenu/services/legal/legal.service';

import { NavBarState } from '../NavBarState';

type ButtonTheme = React.ComponentProps<typeof Button>['theme'];

export const UserBar = observer(function UserBar() {
  const CustomAuthService = useCustomAuthService();
  const LegalService = useLegalService();
  const SettingsUIService = useService(ISettingsUIService);
  const eventHandler = useEventHandler();

  // Force re-render when auth state changes
  const [, forceUpdate] = React.useReducer(x => x + 1, 0);

  const buttonTheme: ButtonTheme = NavBarState.forceTransparentNav
    ? 'default'
    : 'default-blurred';

  const streamerMode = useStreamerMode();

  // Force re-render when auth state changes
  React.useEffect(() => {
    console.log('UserBar: Auth state changed, forcing re-render');
    forceUpdate();
  }, [CustomAuthService.isAuthenticated, CustomAuthService.user, forceUpdate]);

  // Debug logging to understand current state
  React.useEffect(() => {
    console.log('UserBar Debug (Custom Auth Only):', {
      customAuthAuthenticated: CustomAuthService.isAuthenticated,
      customAuthUser: CustomAuthService.user,
      customAuthUserUsername: CustomAuthService.user?.username,
      willShowCustomProfile: CustomAuthService.isAuthenticated && CustomAuthService.user && CustomAuthService.user.username,
      willShowLoginButton: !CustomAuthService.isAuthenticated
    });

    // Make UserBar debug available globally
    if (typeof window !== 'undefined') {
      (window as any).userBarDebug = {
        forceRerender: () => {
          console.log('Force re-render UserBar');
          forceUpdate();
        },
        checkAuthState: () => ({
          isAuthenticated: CustomAuthService.isAuthenticated,
          user: CustomAuthService.user,
          hasUsername: !!CustomAuthService.user?.username
        })
      };
    }
  }, [CustomAuthService.isAuthenticated, CustomAuthService.user, forceUpdate]);

  const handleSettingsClick = React.useCallback(
    (text: string) => {
      SettingsUIService.open('account');
      eventHandler({
        action: EventActionNames.SiteNavClick,
        properties: {
          text,
          link_url: '/',
          element_placement: ElementPlacements.Nav,
          position: 0,
        },
      });
    },
    [eventHandler, SettingsUIService],
  );

  const handleAvatarClick = React.useCallback(() => {
    handleSettingsClick('UserBar_AccountSettings');
  }, [handleSettingsClick]);

  const handleStreamerClick = React.useCallback(() => {
    handleSettingsClick('Streamer');
  }, [handleSettingsClick]);

  // CFX auth handler removed - only using custom auth

  const handleCustomAuthClick = React.useCallback(() => {
    // If not authenticated, trigger custom auth flow
    // If authenticated, open settings
    if (CustomAuthService.isAuthenticated) {
      handleSettingsClick('CustomAuth_Profile');
    } else {
      // Force show custom auth by setting shouldShowAuth to true
      LegalService.forceShowAuth();
      console.log('Triggered custom auth flow');
    }
  }, [CustomAuthService.isAuthenticated, handleSettingsClick, LegalService]);

  // Only use custom auth - completely remove CFX authentication
  console.log('UserBar render check:', {
    isAuthenticated: CustomAuthService.isAuthenticated,
    user: CustomAuthService.user,
    userKeys: CustomAuthService.user ? Object.keys(CustomAuthService.user) : null,
    username: CustomAuthService.user?.username,
    email: CustomAuthService.user?.email,
    conditionMet: CustomAuthService.isAuthenticated && CustomAuthService.user && CustomAuthService.user.username
  });

  if (CustomAuthService.isAuthenticated && CustomAuthService.user && CustomAuthService.user.username) {
    const user = CustomAuthService.user;
    console.log('UserBar: Showing profile for user:', user);

    if (streamerMode) {
      return (
        <Button size="large" theme={buttonTheme} icon={Icons.accountLoaded} onClick={handleStreamerClick} />
      );
    }

    const userInitial = user.username.charAt(0).toUpperCase();

    return (
      <Title title={getCustomAuthUserTitle(user)}>
        <Interactive onClick={handleAvatarClick}>
          <div style={{
            width: '40px',
            height: '40px',
            borderRadius: '50%',
            backgroundColor: '#f0f0f0',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '18px',
            fontWeight: 'bold',
            color: '#333'
          }}>
            {userInitial}
          </div>
        </Interactive>
      </Title>
    );
  }

  // Show login button if not authenticated with custom auth
  console.log('UserBar: Showing login button because not authenticated');
  const profileButtonTheme = NavBarState.forceTransparentNav
    ? 'default'
    : 'primary';

  return (
    <Button
      theme={profileButtonTheme}
      size="large"
      text="Đăng nhập"
      onClick={handleCustomAuthClick}
    />
  );
});

function getCustomAuthUserTitle(user: AuthUser | null): React.ReactNode {
  // Add null safety checks
  if (!user) {
    return <span>Account Settings</span>;
  }

  const username = user.username || 'Unknown User';
  const email = user.email || 'No email';
  const joinDate = user.createdAt ? new Date(user.createdAt).toLocaleDateString() : 'Unknown';

  return (
    <Flex vertical centered gap="small">
      <span>Account Settings</span>
      <Flex vertical centered gap="thin">
        <Text size="small" opacity="75">
          <strong>{username}</strong>
        </Text>
        <Text size="small" opacity="50">
          {email}
        </Text>
        <Text size="small" opacity="50">
          Member since {joinDate}
        </Text>
      </Flex>
    </Flex>
  );
}

// CFX account functions removed - only using custom auth
