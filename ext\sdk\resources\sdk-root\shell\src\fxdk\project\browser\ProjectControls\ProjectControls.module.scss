@import "variables";

.item {
  flex-shrink: 0;
  flex-grow: 0;
  width: $q*15;

  cursor: pointer;

  &:hover {
    color: $fgColor;
  }

  svg {
    font-size: $fs08;
  }
}

.item-stack {
  position: relative;

  .stack {
    position: absolute;

    top: 0;
    left: 0;

    opacity: 0;
    pointer-events: none;

    box-shadow: 0 0 0 1px rgba($fgColor, .5) inset;
    background-color: $bgColor;

    z-index: 1;
  }

  &:hover,
  &.focused {
    .stack {
      opacity: 1;
      pointer-events: all;
    }
  }
}
