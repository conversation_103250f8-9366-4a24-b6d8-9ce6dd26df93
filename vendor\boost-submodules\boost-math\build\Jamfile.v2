# copyright <PERSON> 2008
# Distributed under the Boost Software License, Version 1.0. 
# (See accompanying file LICENSE_1_0.txt or copy at 
# http://www.boost.org/LICENSE_1_0.txt.

import testing ;
import pch ;
import ../../config/checks/config : requires ;

project  
    : requirements 
      <toolset>intel-win:<cxxflags>-nologo 
      <toolset>intel-win:<linkflags>-nologo 
      #<toolset>intel-linux:<pch>off
      <toolset>intel-darwin:<pch>off
      <toolset>msvc-7.1:<pch>off
      <toolset>gcc,<target-os>windows:<pch>off
      #<toolset>gcc:<cxxflags>-fvisibility=hidden
      <toolset>intel-linux:<cxxflags>-fvisibility=hidden
      #<toolset>sun:<cxxflags>-xldscope=hidden
      [ check-target-builds ../config//has_gcc_visibility "gcc visibility" : <toolset>gcc:<cxxflags>-fvisibility=hidden : ]
      [ requires cxx11_noexcept cxx11_rvalue_references sfinae_expr cxx11_auto_declarations cxx11_lambdas cxx11_unified_initialization_syntax cxx11_hdr_tuple cxx11_hdr_initializer_list cxx11_hdr_chrono cxx11_thread_local cxx11_constexpr cxx11_nullptr cxx11_numeric_limits cxx11_decltype cxx11_hdr_array cxx11_hdr_atomic cxx11_hdr_type_traits cxx11_allocator cxx11_explicit_conversion_operators ]
    ;

cpp-pch pch : ../src/tr1/pch.hpp : <include>../src/tr1 <link>shared:<define>BOOST_MATH_TR1_DYN_LINK=1 ; 

C99_SOURCES = acosh
asinh
atanh
cbrt
copysign
erfc
erf
expm1
fmax
fmin
fpclassify
hypot
lgamma
llround
log1p
lround
nextafter
nexttoward
round
tgamma
trunc ;

TR1_SOURCES = 
assoc_laguerre
assoc_legendre
beta
comp_ellint_1
comp_ellint_2
comp_ellint_3
cyl_bessel_i
cyl_bessel_j
cyl_bessel_k
cyl_neumann
ellint_1
ellint_2
ellint_3
expint
hermite
laguerre
legendre
riemann_zeta
sph_bessel
sph_legendre
sph_neumann
;

# Configure checks.

import project ;
import configure ;
import property ;
import property-set ;
import targets ;

obj long_double_check : ../config/has_long_double_support.cpp ;
explicit long_double_check ;
        
# Library targets
lib boost_math_tr1 : ../src/tr1/$(TR1_SOURCES).cpp pch
    :         
         <link>shared:<define>BOOST_MATH_TR1_DYN_LINK=1
         <include>../src/tr1
   ;

lib boost_math_tr1f : ../src/tr1/$(TR1_SOURCES)f.cpp pch
    :         
         <link>shared:<define>BOOST_MATH_TR1_DYN_LINK=1
         <include>../src/tr1
   ;

lib boost_math_tr1l : ../src/tr1/$(TR1_SOURCES)l.cpp pch
    :         
         <link>shared:<define>BOOST_MATH_TR1_DYN_LINK=1
         <dependency>../config//has_long_double_support
         <include>../src/tr1
         [ check-target-builds ../config//has_long_double_support "long double support" : : <build>no ]
   ;

lib boost_math_c99 : ../src/tr1/$(C99_SOURCES).cpp pch
    :         
         <link>shared:<define>BOOST_MATH_TR1_DYN_LINK=1
         <include>../src/tr1
   ;

lib boost_math_c99f : ../src/tr1/$(C99_SOURCES)f.cpp pch
    :         
         <link>shared:<define>BOOST_MATH_TR1_DYN_LINK=1
         <include>../src/tr1
   ;

lib boost_math_c99l : ../src/tr1/$(C99_SOURCES)l.cpp pch
    :         
         <link>shared:<define>BOOST_MATH_TR1_DYN_LINK=1
         <dependency>../config//has_long_double_support
         <include>../src/tr1
         [ check-target-builds ../config//has_long_double_support "long double support" : : <build>no ]
   ;

boost-install boost_math_c99 boost_math_c99f boost_math_c99l boost_math_tr1 boost_math_tr1f boost_math_tr1l ;
