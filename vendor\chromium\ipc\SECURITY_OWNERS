# Changes to IPC messages require a security review to avoid introducing sandbox
# escapes. The shared memory implementation code needs review from a security
# owner. (Shared memory call sites should be reviewed as part of normal IPC
# security review.)
#
# Security team: If you are uncomfortable reviewing a particular bit of code
# yourself, don't hesitate to seek help from another security team member!
# Nobody knows everything, and the only way to learn is from experience.
<EMAIL>
<EMAIL>
<EMAIL>
<EMAIL>
<EMAIL>
<EMAIL>
<EMAIL>
<EMAIL>
<EMAIL>
<EMAIL>
<EMAIL>
<EMAIL>
<EMAIL>
<EMAIL>
<EMAIL>
<EMAIL>
<EMAIL>
<EMAIL>
