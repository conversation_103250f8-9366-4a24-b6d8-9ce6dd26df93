# Copyright 2016, 2017 <PERSON>
# Distributed under the Boost Software License, Version 1.0.
# (See accompanying file LICENSE_1_0.txt or copy at http://boost.org/LICENSE_1_0.txt)

language: cpp

sudo: false

python: "2.7"

os:
  - linux
  - osx

branches:
  only:
    - master
    - develop

env:
  matrix:
    - BOGUS_JOB=true

matrix:

  exclude:
    - env: BOGUS_JOB=true

  include:
    - os: linux
      env: TOOLSET=gcc COMPILER=g++-6 CXXSTD=c++03 TEST_SUITE=special_fun
      addons:
        apt:
          packages:
            - g++-6
            - libgmp-dev
            - libmpfr-dev
            - libfftw3-dev
          sources:
            - ubuntu-toolchain-r-test

    - os: linux
      env: TOOLSET=gcc COMPILER=g++-6 CXXSTD=c++11 TEST_SUITE=special_fun
      addons:
        apt:
          packages:
            - g++-6
            - libgmp-dev
            - libmpfr-dev
            - libfftw3-dev
          sources:
            - ubuntu-toolchain-r-test

    - os: linux
      env: TOOLSET=gcc COMPILER=g++-6 CXXSTD=c++14 TEST_SUITE=special_fun
      addons:
        apt:
          packages:
            - g++-6
            - libgmp-dev
            - libmpfr-dev
            - libfftw3-dev
          sources:
            - ubuntu-toolchain-r-test

    - os: linux
      env: TOOLSET=gcc COMPILER=g++-6 CXXSTD=c++1z TEST_SUITE=special_fun
      addons:
        apt:
          packages:
            - g++-6
            - libgmp-dev
            - libmpfr-dev
            - libfftw3-dev
          sources:
            - ubuntu-toolchain-r-test

    - os: linux
      env: TOOLSET=gcc COMPILER=g++-6 CXXSTD=c++03 TEST_SUITE=distribution_tests
      addons:
        apt:
          packages:
            - g++-6
            - libgmp-dev
            - libmpfr-dev
            - libfftw3-dev
          sources:
            - ubuntu-toolchain-r-test

    - os: linux
      env: TOOLSET=gcc COMPILER=g++-6 CXXSTD=c++11 TEST_SUITE=distribution_tests
      addons:
        apt:
          packages:
            - g++-6
            - libgmp-dev
            - libmpfr-dev
            - libfftw3-dev
          sources:
            - ubuntu-toolchain-r-test

    - os: linux
      env: TOOLSET=gcc COMPILER=g++-6 CXXSTD=c++14 TEST_SUITE=distribution_tests
      addons:
        apt:
          packages:
            - g++-6
            - libgmp-dev
            - libmpfr-dev
            - libfftw3-dev
          sources:
            - ubuntu-toolchain-r-test

    - os: linux
      env: TOOLSET=gcc COMPILER=g++-6 CXXSTD=c++1z TEST_SUITE=distribution_tests
      addons:
        apt:
          packages:
            - g++-6
            - libgmp-dev
            - libmpfr-dev
            - libfftw3-dev
          sources:
            - ubuntu-toolchain-r-test

    - os: linux
      env: TOOLSET=gcc COMPILER=g++-6 CXXSTD=c++03 TEST_SUITE=quadrature
      addons:
        apt:
          packages:
            - g++-6
            - libgmp-dev
            - libmpfr-dev
            - libfftw3-dev
          sources:
            - ubuntu-toolchain-r-test

    - os: linux
      env: TOOLSET=gcc COMPILER=g++-6 CXXSTD=c++11 TEST_SUITE=quadrature
      addons:
        apt:
          packages:
            - g++-6
            - libgmp-dev
            - libmpfr-dev
            - libfftw3-dev
          sources:
            - ubuntu-toolchain-r-test

    - os: linux
      env: TOOLSET=gcc COMPILER=g++-6 CXXSTD=c++14 TEST_SUITE=quadrature
      addons:
        apt:
          packages:
            - g++-6
            - libgmp-dev
            - libmpfr-dev
            - libfftw3-dev
          sources:
            - ubuntu-toolchain-r-test

    - os: linux
      env: TOOLSET=gcc COMPILER=g++-6 CXXSTD=gnu++14 TEST_SUITE=quadrature
      addons:
        apt:
          packages:
            - g++-6
            - libgmp-dev
            - libmpfr-dev
            - libfftw3-dev
          sources:
            - ubuntu-toolchain-r-test

    - os: linux
      env: TOOLSET=gcc COMPILER=g++-6 CXXSTD=c++1z TEST_SUITE=quadrature
      addons:
        apt:
          packages:
            - g++-6
            - libgmp-dev
            - libmpfr-dev
            - libfftw3-dev
          sources:
            - ubuntu-toolchain-r-test

    - os: linux
      env: TOOLSET=gcc COMPILER=g++-6 CXXSTD=c++03 TEST_SUITE=float128_tests
      addons:
        apt:
          packages:
            - g++-6
            - libgmp-dev
            - libmpfr-dev
            - libfftw3-dev
          sources:
            - ubuntu-toolchain-r-test

    - os: linux
      env: TOOLSET=gcc COMPILER=g++-6 CXXSTD=c++11 TEST_SUITE=float128_tests
      addons:
        apt:
          packages:
            - g++-6
            - libgmp-dev
            - libmpfr-dev
            - libfftw3-dev
          sources:
            - ubuntu-toolchain-r-test

    - os: linux
      env: TOOLSET=gcc COMPILER=g++-6 CXXSTD=c++14 TEST_SUITE=float128_tests
      addons:
        apt:
          packages:
            - g++-6
            - libgmp-dev
            - libmpfr-dev
            - libfftw3-dev
          sources:
            - ubuntu-toolchain-r-test

    - os: linux
      env: TOOLSET=gcc COMPILER=g++-6 CXXSTD=gnu++14 TEST_SUITE=float128_tests
      addons:
        apt:
          packages:
            - g++-6
            - libgmp-dev
            - libmpfr-dev
            - libfftw3-dev
          sources:
            - ubuntu-toolchain-r-test

    - os: linux
      env: TOOLSET=gcc COMPILER=g++-6 CXXSTD=c++1z TEST_SUITE=float128_tests
      addons:
        apt:
          packages:
            - g++-6
            - libgmp-dev
            - libmpfr-dev
            - libfftw3-dev
          sources:
            - ubuntu-toolchain-r-test

    - os: linux
      env: TOOLSET=gcc COMPILER=g++-6 CXXSTD=c++03 TEST_SUITE=../example//examples
      addons:
        apt:
          packages:
            - g++-6
            - libgmp-dev
            - libmpfr-dev
            - libfftw3-dev
          sources:
            - ubuntu-toolchain-r-test

    - os: linux
      env: TOOLSET=gcc COMPILER=g++-6 CXXSTD=c++03 TEST_SUITE=../tools
      addons:
        apt:
          packages:
            - g++-6
            - libgmp-dev
            - libmpfr-dev
            - libfftw3-dev
          sources:
            - ubuntu-toolchain-r-test

    - os: linux
      env: TOOLSET=gcc COMPILER=g++-6 CXXSTD=c++11 TEST_SUITE=../example//examples
      addons:
        apt:
          packages:
            - g++-6
            - libgmp-dev
            - libmpfr-dev
            - libfftw3-dev
          sources:
            - ubuntu-toolchain-r-test

    - os: linux
      env: TOOLSET=gcc COMPILER=g++-6 CXXSTD=c++11 TEST_SUITE=../tools
      addons:
        apt:
          packages:
            - g++-6
            - libgmp-dev
            - libmpfr-dev
            - libfftw3-dev
          sources:
            - ubuntu-toolchain-r-test

    - os: linux
      env: TOOLSET=gcc COMPILER=g++-6 CXXSTD=c++14 TEST_SUITE=../example//examples
      addons:
        apt:
          packages:
            - g++-6
            - libgmp-dev
            - libmpfr-dev
            - libfftw3-dev
          sources:
            - ubuntu-toolchain-r-test

    - os: linux
      env: TOOLSET=gcc COMPILER=g++-6 CXXSTD=c++14 TEST_SUITE=../tools
      addons:
        apt:
          packages:
            - g++-6
            - libgmp-dev
            - libmpfr-dev
            - libfftw3-dev
          sources:
            - ubuntu-toolchain-r-test

    - os: linux
      env: TOOLSET=gcc COMPILER=g++-6 CXXSTD=gnu++14 TEST_SUITE=../example//examples
      addons:
        apt:
          packages:
            - g++-6
            - libgmp-dev
            - libmpfr-dev
            - libfftw3-dev
          sources:
            - ubuntu-toolchain-r-test

    - os: linux
      env: TOOLSET=gcc COMPILER=g++-6 CXXSTD=gnu++14 TEST_SUITE=../tools
      addons:
        apt:
          packages:
            - g++-6
            - libgmp-dev
            - libmpfr-dev
            - libfftw3-dev
          sources:
            - ubuntu-toolchain-r-test

    - os: linux
      env: TOOLSET=gcc COMPILER=g++-6 CXXSTD=c++1z TEST_SUITE=../example//examples
      addons:
        apt:
          packages:
            - g++-6
            - libgmp-dev
            - libmpfr-dev
            - libfftw3-dev
          sources:
            - ubuntu-toolchain-r-test

    - os: linux
      env: TOOLSET=gcc COMPILER=g++-6 CXXSTD=c++1z TEST_SUITE=../tools
      addons:
        apt:
          packages:
            - g++-6
            - libgmp-dev
            - libmpfr-dev
            - libfftw3-dev
          sources:
            - ubuntu-toolchain-r-test

    - os: linux
      env: TOOLSET=gcc COMPILER=g++-5 CXXSTD=c++14 TEST_SUITE=special_fun
      addons:
        apt:
          packages:
            - g++-5
            - libgmp-dev
            - libmpfr-dev
            - libfftw3-dev
          sources:
            - ubuntu-toolchain-r-test

    - os: linux
      env: TOOLSET=gcc COMPILER=g++-5 CXXSTD=c++14 TEST_SUITE=distribution_tests
      addons:
        apt:
          packages:
            - g++-5
            - libgmp-dev
            - libmpfr-dev
            - libfftw3-dev
          sources:
            - ubuntu-toolchain-r-test

    - os: linux
      env: TOOLSET=gcc COMPILER=g++-5 CXXSTD=c++14 TEST_SUITE=misc
      addons:
        apt:
          packages:
            - g++-5
            - libgmp-dev
            - libmpfr-dev
            - libfftw3-dev
          sources:
            - ubuntu-toolchain-r-test

    - os: linux
      env: TOOLSET=gcc COMPILER=g++-5 CXXSTD=c++14 TEST_SUITE=quadrature
      addons:
        apt:
          packages:
            - g++-5
            - libgmp-dev
            - libmpfr-dev
            - libfftw3-dev
          sources:
            - ubuntu-toolchain-r-test

    - os: linux
      env: TOOLSET=gcc COMPILER=g++-5 CXXSTD=c++14 TEST_SUITE=float128_tests
      addons:
        apt:
          packages:
            - g++-5
            - libgmp-dev
            - libmpfr-dev
            - libfftw3-dev
          sources:
            - ubuntu-toolchain-r-test

    - os: linux
      env: TOOLSET=gcc COMPILER=g++-5 CXXSTD=c++14 TEST_SUITE=../example//examples
      addons:
        apt:
          packages:
            - g++-5
            - libgmp-dev
            - libmpfr-dev
            - libfftw3-dev
          sources:
            - ubuntu-toolchain-r-test

    - os: linux
      env: TOOLSET=gcc COMPILER=g++-5 CXXSTD=c++14 TEST_SUITE=../tools
      addons:
        apt:
          packages:
            - g++-5
            - libgmp-dev
            - libmpfr-dev
            - libfftw3-dev
          sources:
            - ubuntu-toolchain-r-test

    - os: linux
      dist: trusty
      compiler: g++-8
      env: TOOLSET=gcc COMPILER=g++-8 CXXSTD=c++14 TEST_SUITE=special_fun
      addons:
        apt:
          packages:
            - g++-8
          sources:
            - ubuntu-toolchain-r-test

    - os: linux
      dist: trusty
      compiler: g++-8
      env: TOOLSET=gcc COMPILER=g++-8 CXXSTD=c++14 TEST_SUITE=distribution_tests
      addons:
        apt:
          packages:
            - g++-8
          sources:
            - ubuntu-toolchain-r-test

    - os: linux
      dist: trusty
      compiler: g++-8
      env: TOOLSET=gcc COMPILER=g++-8 CXXSTD=c++14 TEST_SUITE=misc
      addons:
        apt:
          packages:
            - g++-8
          sources:
            - ubuntu-toolchain-r-test

    - os: linux
      dist: trusty
      compiler: g++-8
      env: TOOLSET=gcc COMPILER=g++-8 CXXSTD=c++14 TEST_SUITE=quadrature
      addons:
        apt:
          packages:
            - g++-8
          sources:
            - ubuntu-toolchain-r-test

    - os: linux
      dist: trusty
      compiler: g++-8
      env: TOOLSET=gcc COMPILER=g++-8 CXXSTD=c++14 TEST_SUITE=float128_tests
      addons:
        apt:
          packages:
            - g++-8
          sources:
            - ubuntu-toolchain-r-test

    - os: linux
      dist: trusty
      compiler: g++-8
      env: TOOLSET=gcc COMPILER=g++-8 CXXSTD=c++14 TEST_SUITE=../example//examples
      addons:
        apt:
          packages:
            - g++-8
          sources:
            - ubuntu-toolchain-r-test

    - os: linux
      dist: trusty
      compiler: g++-8
      env: TOOLSET=gcc COMPILER=g++-8 CXXSTD=c++14 TEST_SUITE=../tools
      addons:
        apt:
          packages:
            - g++-8
            - libgmp-dev
            - libmpfr-dev
          sources:
            - ubuntu-toolchain-r-test

    - os: linux
      dist: trusty
      compiler: g++-8
      env: TOOLSET=gcc COMPILER=g++-8 CXXSTD=gnu++03 TEST_SUITE=special_fun
      addons:
        apt:
          packages:
            - g++-8
          sources:
            - ubuntu-toolchain-r-test

    - os: linux
      dist: trusty
      compiler: g++-8
      env: TOOLSET=gcc COMPILER=g++-8 CXXSTD=gnu++03 TEST_SUITE=distribution_tests
      addons:
        apt:
          packages:
            - g++-8
          sources:
            - ubuntu-toolchain-r-test

    - os: linux
      dist: trusty
      compiler: g++-8
      env: TOOLSET=gcc COMPILER=g++-8 CXXSTD=gnu++03 TEST_SUITE=misc
      addons:
        apt:
          packages:
            - g++-8
          sources:
            - ubuntu-toolchain-r-test

    - os: linux
      dist: trusty
      compiler: g++-8
      env: TOOLSET=gcc COMPILER=g++-8 CXXSTD=gnu++03 TEST_SUITE=quadrature
      addons:
        apt:
          packages:
            - g++-8
          sources:
            - ubuntu-toolchain-r-test

    - os: linux
      dist: trusty
      compiler: g++-8
      env: TOOLSET=gcc COMPILER=g++-8 CXXSTD=gnu++03 TEST_SUITE=float128_tests
      addons:
        apt:
          packages:
            - g++-8
          sources:
            - ubuntu-toolchain-r-test

    - os: linux
      dist: trusty
      compiler: g++-8
      env: TOOLSET=gcc COMPILER=g++-8 CXXSTD=gnu++03 TEST_SUITE=../example//examples
      addons:
        apt:
          packages:
            - g++-8
          sources:
            - ubuntu-toolchain-r-test

    - os: linux
      dist: trusty
      compiler: g++-8
      env: TOOLSET=gcc COMPILER=g++-8 CXXSTD=gnu++03 TEST_SUITE=../tools
      addons:
        apt:
          packages:
            - g++-8
            - libgmp-dev
            - libmpfr-dev
          sources:
            - ubuntu-toolchain-r-test

    - os: linux
      dist: trusty
      compiler: g++-8
      env: TOOLSET=gcc COMPILER=g++-8 CXXSTD=gnu++17 TEST_SUITE=special_fun
      addons:
        apt:
          packages:
            - g++-8
          sources:
            - ubuntu-toolchain-r-test

    - os: linux
      dist: trusty
      compiler: g++-8
      env: TOOLSET=gcc COMPILER=g++-8 CXXSTD=gnu++17 TEST_SUITE=distribution_tests
      addons:
        apt:
          packages:
            - g++-8
          sources:
            - ubuntu-toolchain-r-test

    - os: linux
      dist: trusty
      compiler: g++-8
      env: TOOLSET=gcc COMPILER=g++-8 CXXSTD=gnu++17 TEST_SUITE=quadrature
      addons:
        apt:
          packages:
            - g++-8
          sources:
            - ubuntu-toolchain-r-test

    - os: linux
      dist: trusty
      compiler: g++-8
      env: TOOLSET=gcc COMPILER=g++-8 CXXSTD=gnu++17 TEST_SUITE=float128_tests
      addons:
        apt:
          packages:
            - g++-8
          sources:
            - ubuntu-toolchain-r-test

    - os: linux
      dist: trusty
      compiler: g++-8
      env: TOOLSET=gcc COMPILER=g++-8 CXXSTD=gnu++17 TEST_SUITE=../example//examples
      addons:
        apt:
          packages:
            - g++-8
          sources:
            - ubuntu-toolchain-r-test

    - os: linux
      dist: trusty
      compiler: g++-8
      env: TOOLSET=gcc COMPILER=g++-8 CXXSTD=gnu++17 TEST_SUITE=../tools
      addons:
        apt:
          packages:
            - g++-8
            - libgmp-dev
            - libmpfr-dev
          sources:
            - ubuntu-toolchain-r-test

    - os: linux
      compiler: clang++-6.0
      env: TOOLSET=clang COMPILER=clang++-6.0 CXXSTD=c++11 TEST_SUITE=special_fun
      addons:
        apt:
          packages:
            - clang-6.0
          sources:
            - ubuntu-toolchain-r-test
            - llvm-toolchain-xenial-6.0

    - os: linux
      compiler: clang++-6.0
      env: TOOLSET=clang COMPILER=clang++-6.0 CXXSTD=c++11 TEST_SUITE=distribution_tests
      addons:
        apt:
          packages:
            - clang-6.0
          sources:
            - ubuntu-toolchain-r-test
            - llvm-toolchain-xenial-6.0

    - os: linux
      compiler: clang++-6.0
      env: TOOLSET=clang COMPILER=clang++-6.0 CXXSTD=c++11 TEST_SUITE=misc
      addons:
        apt:
          packages:
            - clang-6.0
          sources:
            - ubuntu-toolchain-r-test
            - llvm-toolchain-xenial-6.0

    - os: linux
      compiler: clang++-6.0
      env: TOOLSET=clang COMPILER=clang++-6.0 CXXSTD=c++11 TEST_SUITE=quadrature
      addons:
        apt:
          packages:
            - clang-6.0
          sources:
            - ubuntu-toolchain-r-test
            - llvm-toolchain-xenial-6.0

    - os: linux
      compiler: clang++-6.0
      env: TOOLSET=clang COMPILER=clang++-6.0 CXXSTD=c++11 TEST_SUITE=../example//examples
      addons:
        apt:
          packages:
            - clang-6.0
          sources:
            - ubuntu-toolchain-r-test
            - llvm-toolchain-xenial-6.0

    - os: linux
      compiler: clang++-6.0
      env: TOOLSET=clang COMPILER=clang++-6.0 CXXSTD=c++11 TEST_SUITE=../tools
      addons:
        apt:
          packages:
            - clang-6.0
            - libgmp-dev
            - libmpfr-dev
          sources:
            - ubuntu-toolchain-r-test
            - llvm-toolchain-xenial-6.0

    - os: osx
      env: TOOLSET=clang COMPILER=clang++ CXXSTD=c++14 TEST_SUITE=special_fun
      osx_image: xcode11

    - os: osx
      env: TOOLSET=clang COMPILER=clang++ CXXSTD=c++14 TEST_SUITE=distribution_tests
      osx_image: xcode11

    - os: osx
      env: TOOLSET=clang COMPILER=clang++ CXXSTD=c++14 TEST_SUITE=misc
      osx_image: xcode11

    - os: osx
      env: TOOLSET=clang COMPILER=clang++ CXXSTD=c++14 TEST_SUITE=quadrature
      osx_image: xcode11

    - os: osx
      env: TOOLSET=clang COMPILER=clang++ CXXSTD=c++14 TEST_SUITE=float128_tests
      osx_image: xcode11

    - os: osx
      env: TOOLSET=clang COMPILER=clang++ CXXSTD=c++14 TEST_SUITE=../example//examples
      osx_image: xcode11


install:
  - cd ..
  - git clone -b $TRAVIS_BRANCH --depth 1 https://github.com/boostorg/boost.git boost-root
  - cd boost-root
  - git submodule update --init tools/build
  - git submodule update --init tools/boost_install
  - git submodule update --init libs/headers
  - git submodule update --init libs/config
  - git submodule update --init libs/format
  - git submodule update --init libs/numeric
  - git submodule update --init libs/type_traits
  - git submodule update --init libs/timer
  - git submodule update --init tools/boostdep
  - cp -r $TRAVIS_BUILD_DIR/* libs/math
  - python tools/boostdep/depinst/depinst.py math
  - ./bootstrap.sh
  - ./b2 headers

script:
  - |-
    echo "using $TOOLSET : : $COMPILER : <cxxflags>-std=$CXXSTD ;" > ~/user-config.jam
  - (cd libs/config/test && ../../../b2 config_info_travis_install toolset=$TOOLSET && ./config_info_travis)
  - (cd libs/math/test && travis_wait 60 ../../../b2 -j3 toolset=$TOOLSET $TEST_SUITE define=CI_SUPPRESS_KNOWN_ISSUES define=SLOW_COMPILER)

notifications:
  email:
    on_success: always
