[/
  Copyright 2023 <PERSON>
  Distributed under the Boost Software License, Version 1.0.
  https://boost.org/LICENSE_1_0.txt
]

[section:yield_primitives Yield Primitives]

[simplesect Authors]

* <PERSON>

[endsimplesect]

[section Header <boost/core/yield_primitives.hpp>]

The header `<boost/core/yield_primitives.hpp>` implements a
collection of primitives that allow the current thread to
yield the CPU in various ways.

Very low level, specialized functionality, generally only useful for
implementing spinlocks. Normal synchronization primitives should
almost always be preferable in application code.

[section Synopsis]

``
namespace boost
{
namespace core
{

void sp_thread_pause() noexcept;
void sp_thread_yield() noexcept;
void sp_thread_sleep() noexcept;

} // namespace core
} // namespace boost
``

[endsect]

[section sp_thread_pause]

`void sp_thread_pause() noexcept;`

Emits a PAUSE instruction (on x86) or a YIELD instruction (on ARM).

A portable equivalent of the GCC builtin function `__builtin_ia32_pause`.

[endsect]

[section sp_thread_yield]

`void sp_thread_yield() noexcept;`

Informs the scheduler that the current thread wishes to relinquish
the rest of its timeslice.

A portable equivalent of POSIX `sched_yield`.

[endsect]

[section sp_thread_sleep]

`void sp_thread_sleep() noexcept;`

Sleeps for a short period, as if by calling POSIX `nanosleep` with
a small, implementation-dependent, interval (usually one microsecond).

A more forcing yield primitive than `sp_thread_yield`, because it's
generally not ignored even if all other waiting threads are of lower
priority.

[endsect]

[endsect]

[endsect]
