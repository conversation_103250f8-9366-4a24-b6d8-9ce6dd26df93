<header>
    <div class="tsd-page-toolbar">
        <div class="container">
            <div class="table-wrap">
                <div class="table-cell" id="tsd-search" data-index="{{relativeURL "assets/js/search.json"}}" data-base="{{relativeURL "./"}}">
                    <div class="field">
                        <label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
                        <input id="tsd-search-field" type="text" />
                    </div>

                    <ul class="results">
                        <li class="state loading">Preparing search index...</li>
                        <li class="state failure">The search index is not available</li>
                    </ul>

                    <a href="{{relativeURL "index.html"}}" class="title">{{project.name}}</a>
                </div>

                <div class="table-cell" id="tsd-widgets">
                    <div id="tsd-filter">
                        <a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
                        <div class="tsd-filter-group">
                            <div class="tsd-select" id="tsd-filter-visibility">
                                <span class="tsd-select-label">All</span>
                                <ul class="tsd-select-list">
                                    <li data-value="public">Public</li>
                                    <li data-value="protected">Public/Protected</li>
                                    <li data-value="private" class="selected">All</li>
                                </ul>
                            </div>

                            <input type="checkbox" id="tsd-filter-inherited" checked />
                            <label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>

                            {{#unless settings.excludeExternals}}
                                <input type="checkbox" id="tsd-filter-externals" checked />
                                <label class="tsd-widget" for="tsd-filter-externals">Externals</label>
                            {{/unless}}
                        </div>
                    </div>

                    <a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
                </div>
            </div>
        </div>
    </div>
    <div class="tsd-page-title">
        <div class="container">
            {{#if model.parent}} {{! Don't show breadcrumbs on main project page, it is the root page. !}}
                <ul class="tsd-breadcrumb">
                    {{#with model}}{{> breadcrumb}}{{/with}}
                </ul>
            {{/if}}
            <h1>{{#compact}}
                {{model.kindString}}&nbsp;
                {{model.name}}
                {{#if model.typeParameters}}
                    &lt;
                    {{#each model.typeParameters}}
                        {{#if @index}},&nbsp;{{/if}}
                        {{name}}
                    {{/each}}
                    &gt;
                {{/if}}
            {{/compact}}</h1>
        </div>
    </div>
</header>
