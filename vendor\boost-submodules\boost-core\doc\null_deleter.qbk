[/
 / Copyright (c) 2014 <PERSON><PERSON>
 /
 / Distributed under the Boost Software License, Version 1.0. (See accompanying
 / file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
 /]

[section:null_deleter null_deleter]

[simplesect Authors]

* <PERSON><PERSON>

[endsimplesect]

[section Header <boost/core/null_deleter.hpp>]

The header `<boost/core/null_deleter.hpp>` defines the `boost::null_deleter` function object,
which can be used as a deleter with smart pointers such as `unique_ptr` or `shared_ptr`. The
deleter doesn't do anything with the pointer provided upon deallocation, which makes it useful
when the pointed object is deallocated elsewhere.

[section Example]
``
std::shared_ptr< std::ostream > make_stream()
{
    return std::shared_ptr< std::ostream >(&std::cout, boost::null_deleter());
}
``
[endsect]

[endsect]

[endsect]
