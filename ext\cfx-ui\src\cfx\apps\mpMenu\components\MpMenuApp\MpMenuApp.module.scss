// Clean FiveM Interface - No borders or effects
.root {
  display: flex;
  flex-direction: column;

  gap: ui.offset('xlarge');

  width: ui.viewport-width();
  height: ui.viewport-height();

  // Enhanced padding for top navigation
  padding-top: 90px;

  position: relative;
  overflow: hidden;

  // Force remove any potential borders
  border: none !important;
  border-image: none !important;
  box-shadow: none !important;
  outline: none !important;

  // Remove backdrop overlay that might cause border effects
  &::before {
    display: none !important;
  }

  // Remove animated layer that might cause border effects
  &::after {
    display: none !important;
  }

  .outlet {
    flex-grow: 1;
    height: 1px;
    position: relative;

    // Remove all styling that could create borders
    background: transparent !important;
    backdrop-filter: none !important;
    border: none !important;
    border-radius: 0 !important;
    box-shadow: none !important;
    outline: none !important;

    // Remove animation that might cause border effects
    animation: none !important;

    // Removed dynamic border glow effect that caused pink/purple border

    // Enhanced scrollbar styling
    ::-webkit-scrollbar {
      width: 8px;
    }

    ::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.05);
      border-radius: 4px;
    }

    ::-webkit-scrollbar-thumb {
      background: linear-gradient(135deg,
          rgba(255, 255, 255, 0.2),
          rgba(255, 255, 255, 0.1));
      border-radius: 4px;
      border: 1px solid rgba(255, 255, 255, 0.1);

      &:hover {
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.3),
            rgba(255, 255, 255, 0.2));
      }
    }
  }
}

// Advanced keyframe animations for cutting-edge interface
@keyframes conicRotation {
  0% {
    transform: rotate(0deg) scale(1);
  }

  25% {
    transform: rotate(90deg) scale(1.05);
  }

  50% {
    transform: rotate(180deg) scale(0.95);
  }

  75% {
    transform: rotate(270deg) scale(1.02);
  }

  100% {
    transform: rotate(360deg) scale(1);
  }
}

@keyframes floatingOrbs {

  0%,
  100% {
    transform: translate(0, 0) scale(1);
    opacity: 0.8;
  }

  25% {
    transform: translate(20px, -15px) scale(1.1);
    opacity: 1;
  }

  50% {
    transform: translate(-10px, 10px) scale(0.9);
    opacity: 0.6;
  }

  75% {
    transform: translate(15px, -5px) scale(1.05);
    opacity: 0.9;
  }
}

@keyframes advancedSlideIn {
  0% {
    opacity: 0;
    transform: translateY(40px) scale(0.9) rotateX(10deg);
    filter: blur(10px);
  }

  50% {
    opacity: 0.7;
    transform: translateY(10px) scale(0.98) rotateX(2deg);
    filter: blur(2px);
  }

  100% {
    opacity: 1;
    transform: translateY(0) scale(1) rotateX(0deg);
    filter: blur(0px);
  }
}



@keyframes smoothSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.98);
  }

  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}