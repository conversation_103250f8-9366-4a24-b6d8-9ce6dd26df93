# Copyright 2015 The Chromium Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//build/win/message_compiler.gni")

assert(is_win, "This only runs on Windows.")

message_compiler("chrome_events_win") {
  visibility = [
    "//base/*",
    "//chrome:main_dll",
  ]

  sources = [ "chrome_events_win.man" ]

  user_mode_logging = true

  # The only code generated from chrome_events_win.man is a header file that
  # is included by trace_event_etw_export_win.cc, so there is no need to
  # compile any generated code. The other thing which compile_generated_code
  # controls in this context is linking in the .res file generated from the
  # manifest. However this is only needed for ETW provider registration which
  # is done by UIforETW (https://github.com/google/UIforETW) and therefore the
  # manifest resource can be skipped in Chrome.
  compile_generated_code = false
}
