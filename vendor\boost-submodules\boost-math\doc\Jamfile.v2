
# Copyright John <PERSON> 2005. Use, modification, and distribution are
# subject to the Boost Software License, Version 1.0. (See accompanying
# file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

using quickbook ;
using auto-index ;
import modules ;
import regex ;

path-constant images_location : html ;
path-constant here : . ;
constant here-url : [ regex.replace $(here) "\\\\" "/" ] ;

xml math : math.qbk :
        <quickbook-define>enable_index 
        <quickbook-define>__base_path__=$(here-url)
        <format>pdf:<quickbook-define>__build_pdf
        <format>html:<quickbook-define>__build_html
;
boostbook standalone
    :
        math
    :
        # Path for links to Boost:
        <xsl:param>boost.root=../../../..
        <xsl:param>html.stylesheet=math.css
        
        # Some general style settings:
        <xsl:param>table.footnote.number.format=1
        <xsl:param>footnote.number.format=1

        # HTML options first:
        # Use graphics not text for navigation:
        <xsl:param>navig.graphics=1
        # How far down we chunk nested sections, basically all of them:
        <xsl:param>chunk.section.depth=10
        # Don't put the first section on the same page as the TOC:
        <xsl:param>chunk.first.sections=1
        # How far down sections get TOC's
        <xsl:param>toc.section.depth=10
        # Max depth in each TOC:
        <xsl:param>toc.max.depth=4
        # How far down we go with TOC's
        <xsl:param>generate.section.toc.level=10
        # Index on type:
        <xsl:param>index.on.type=1
        <xsl:param>boost.noexpand.chapter.toc=1
        
        #<xsl:param>root.filename="sf_dist_and_tools"
        #<xsl:param>graphicsize.extension=1
        #<xsl:param>use.extensions=1
        
        # PDF Options:
        # TOC Generation: this is needed for FOP-0.9 and later:
        <xsl:param>fop1.extensions=0
        <format>pdf:<xsl:param>xep.extensions=1
        # TOC generation: this is needed for FOP 0.2, but must not be set to zero for FOP-0.9!
        <format>pdf:<xsl:param>fop.extensions=0
        <format>pdf:<xsl:param>fop1.extensions=0
        # No indent on body text:
        #<format>pdf:<xsl:param>body.start.indent=15px indents ALL the text relative to the title :-(.
        <format>pdf:<xsl:param>body.start.indent=0pt
        # Margin size:
        <format>pdf:<xsl:param>page.margin.inner=0.5in
        # Margin size:
        <format>pdf:<xsl:param>page.margin.outer=0.5in
        # Paper type = A4
        <format>pdf:<xsl:param>paper.type=A4
        # Yes, we want graphics for admonishments:
        <xsl:param>admon.graphics=1
        # Set this one for PDF generation *only*:
        # default pnd graphics are awful in PDF form,
        # better use SVG's instead:
        <format>pdf:<xsl:param>admon.graphics.extension=".svg"
        <format>pdf:<xsl:param>admon.graphics.path=$(here)/../../../doc/src/images/
        <format>pdf:<xsl:param>use.role.for.mediaobject=1 
        <format>pdf:<xsl:param>preferred.mediaobject.role=print
        <format>pdf:<xsl:param>img.src.path=$(images_location)/
        <format>pdf:<xsl:param>draft.mode="no"
        <format>pdf:<xsl:param>boost.url.prefix=http\://www.boost.org/doc/libs/release/libs/math/doc/html
        <auto-index>on <format>pdf:<auto-index-internal>off
        <format>html:<auto-index-internal>on
        <auto-index-script>$(here)/index.idx
        <auto-index-prefix>$(here)/../../.. 
        #<auto-index-verbose>on
        <format>pdf:<xsl:param>index.on.type=1
    ;

install pdfinstall : standalone/<format>pdf : <location>. <install-type>PDF <name>math.pdf ;
explicit pdfinstall ; # b2 pdf pdfinstall to do this pdf file copy.

install css_install : math.css : <location>$(here)/html ; 

###############################################################################
alias boostdoc ;
explicit boostdoc ;
alias boostrelease : standalone ;
explicit boostrelease ;
