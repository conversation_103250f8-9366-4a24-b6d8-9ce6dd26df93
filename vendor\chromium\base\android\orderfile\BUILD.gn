# Copyright 2018 The Chromium Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//build/config/android/config.gni")

if (use_order_profiling && (target_cpu == "arm" || target_cpu == "arm64")) {
  static_library("orderfile_instrumentation") {
    sources = [ "orderfile_instrumentation.h" ]

    if (use_call_graph) {
      sources += [ "orderfile_call_graph_instrumentation.cc" ]
    } else {
      sources += [ "orderfile_instrumentation.cc" ]
    }

    deps = [ "//base" ]
  }

  executable("orderfile_instrumentation_perftest") {
    testonly = true
    if (use_call_graph) {
      sources = [ "orderfile_call_graph_instrumentation_perftest.cc" ]
    } else {
      sources = [ "orderfile_instrumentation_perftest.cc" ]
    }
    deps = [
      ":orderfile_instrumentation",
      "//base",
      "//testing/gtest",
      "//testing/perf",
    ]
    configs -= [ "//build/config/android:default_orderfile_instrumentation" ]
  }
}
