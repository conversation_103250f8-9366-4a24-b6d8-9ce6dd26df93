# Copyright 2014 The Chromium Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

component("system") {
  output_name = "mojo_public_system"

  sources = [ "thunks.cc" ]

  defines = [ "MOJO_SYSTEM_IMPLEMENTATION" ]

  public_deps = [ ":headers" ]

  deps = [ "//base" ]
}

source_set("headers") {
  public = [
    "buffer.h",
    "core.h",
    "data_pipe.h",
    "functions.h",
    "invitation.h",
    "macros.h",
    "message_pipe.h",
    "platform_handle.h",
    "system_export.h",
    "thunks.h",
    "trap.h",
    "types.h",
  ]
}
