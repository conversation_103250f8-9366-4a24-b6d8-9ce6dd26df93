//
// Copyright (c) 2009-2011 <PERSON><PERSON><PERSON> (Tonkikh)
//
// Distributed under the Boost Software License, Version 1.0.
// https://www.boost.org/LICENSE_1_0.txt

/*!
\page locale_information Getting information about the current locale

The \c std::locale::name function provides very limited information about a locale. Such a name is platform-
and compiler-dependent, and is useless for Boost.Locale. There is no way to change it, so for all locales
created by Boost.Locale, \c name() returns the same value as \c name() for \c std::locale::classic().

Thus an additional facet was created, giving more precise information: boost::locale::info. It has the following member functions:

-   \ref boost::locale::info::name() "std::string name()" -- the full name of the locale, for example \c en_US.UTF-8
-   \ref boost::locale::info::language() "std::string language()" -- the ISO-639 language code of the current locale, for example "en".
-   \ref boost::locale::info::country() "std::string country()" -- the ISO-3166 country code of the current locale, for example "US".
-   \ref boost::locale::info::variant() "std::string variant()" -- the variant of current locale, for example "euro".
-   \ref boost::locale::info::encoding() "std::string encoding()" -- the encoding used for \c char based strings, for example "UTF-8"
-   \ref boost::locale::info::utf8() "bool utf8()" -- a fast way to check whether the encoding is UTF-8.

For example:

\code
    cout << "The language code is " << std::use_facet<boost::locale::info>(some_locale).language() << endl;
\endcode


*/
