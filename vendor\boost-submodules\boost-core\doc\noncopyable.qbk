[/
  Copyright 1999-2003 <PERSON><PERSON>
  Copyright 2014 <PERSON>

  Distributed under the Boost Software License, Version 1.0.

  See accompanying file LICENSE_1_0.txt
  or copy at http://boost.org/LICENSE_1_0.txt
]

[section:noncopyable noncopyable]

[simplesect Authors]

* <PERSON>

[endsimplesect]

[section Header <boost/core/noncopyable.hpp>]

The header `<boost/core/noncopyable.hpp>` defines the class
`boost::noncopyable`. It is intended to be used as a private
base class. `boost::noncopyable` has private (under C++03) or
deleted (under C++11) copy constructor and a copy assignment
operator and can't be copied or assigned; a class that derives
from it inherits these properties.

`boost::noncopyable` was originally contributed by <PERSON>.

[section Synopsis]

``
namespace boost
{
    class noncopyable;
}
``

[endsect]

[section Example]

``
#include <boost/core/noncopyable.hpp>

class X: private boost::noncopyable
{
};
``

[endsect]

[section Rationale]

Class noncopyable has protected constructor and destructor members to emphasize
that it is to be used only as a base class. <PERSON> notes concern about
the effect on compiler optimization of adding (even trivial inline) destructor
declarations. He says:

["Probably this concern is misplaced, because `noncopyable` will be used mostly
for classes which own resources and thus have non-trivial destruction semantics.]

With C++2011, using an optimized and trivial constructor and similar destructor
can be enforced by declaring both and marking them `default`. This is done in
the current implementation.

[endsect]

[endsect]

[endsect]
