diff --git a/third_party/libevent/buffer.c b/third_party/libevent/buffer.c
index 64324bb..ebf35c9 100644
--- a/third_party/libevent/buffer.c
+++ b/third_party/libevent/buffer.c
@@ -356,7 +356,6 @@ int
 evbuffer_expand(struct evbuffer *buf, size_t datlen)
 {
 	size_t used = buf->misalign + buf->off;
-	size_t need;
 
 	assert(buf->totallen >= used);
 
diff --git a/third_party/libevent/evdns.c b/third_party/libevent/evdns.c
index fa23163..f1c70d0 100644
--- a/third_party/libevent/evdns.c
+++ b/third_party/libevent/evdns.c
@@ -55,7 +55,9 @@
 #endif
 
 /* #define _POSIX_C_SOURCE 200507 */
+#ifndef _GNU_SOURCE
 #define _GNU_SOURCE
+#endif
 
 #ifdef DNS_USE_CPU_CLOCK_FOR_ID
 #ifdef DNS_USE_OPENSSL_FOR_ID
@@ -134,7 +136,7 @@
 typedef ev_uint8_t u_char;
 typedef unsigned int uint;
 #endif
-#include <event.h>
+#include "event.h"
 
 #define u64 ev_uint64_t
 #define u32 ev_uint32_t
diff --git a/third_party/libevent/evdns.h b/third_party/libevent/evdns.h
index 1eb5c38..fca4ac3 100644
--- a/third_party/libevent/evdns.h
+++ b/third_party/libevent/evdns.h
@@ -165,7 +165,7 @@ extern "C" {
 #endif
 
 /* For integer types. */
-#include <evutil.h>
+#include "evutil.h"
 
 /** Error codes 0-5 are as described in RFC 1035. */
 #define DNS_ERR_NONE 0
diff --git a/third_party/libevent/event.c b/third_party/libevent/event.c
index da6cd42..36b1c51 100644
--- a/third_party/libevent/event.c
+++ b/third_party/libevent/event.c
@@ -107,11 +107,7 @@ static const struct eventop *eventops[] = {
 /* Global state */
 struct event_base *current_base = NULL;
 extern struct event_base *evsignal_base;
-static int use_monotonic;
-
-/* Handle signals - This is a deprecated interface */
-int (*event_sigcb)(void);		/* Signal callback when gotsig is set */
-volatile sig_atomic_t event_gotsig;	/* Set in signal handler */
+static int use_monotonic = 1;
 
 /* Prototypes */
 static void	event_queue_insert(struct event_base *, struct event *, int);
@@ -124,17 +120,6 @@ static int	timeout_next(struct event_base *, struct timeval **);
 static void	timeout_process(struct event_base *);
 static void	timeout_correct(struct event_base *, struct timeval *);
 
-static void
-detect_monotonic(void)
-{
-#if defined(HAVE_CLOCK_GETTIME) && defined(CLOCK_MONOTONIC)
-	struct timespec	ts;
-
-	if (clock_gettime(CLOCK_MONOTONIC, &ts) == 0)
-		use_monotonic = 1;
-#endif
-}
-
 static int
 gettime(struct event_base *base, struct timeval *tp)
 {
@@ -144,18 +129,18 @@ gettime(struct event_base *base, struct timeval *tp)
 	}
 
 #if defined(HAVE_CLOCK_GETTIME) && defined(CLOCK_MONOTONIC)
-	if (use_monotonic) {
-		struct timespec	ts;
-
-		if (clock_gettime(CLOCK_MONOTONIC, &ts) == -1)
-			return (-1);
+	struct timespec	ts;
 
+	if (use_monotonic &&
+	    clock_gettime(CLOCK_MONOTONIC, &ts) == 0) {
 		tp->tv_sec = ts.tv_sec;
 		tp->tv_usec = ts.tv_nsec / 1000;
 		return (0);
 	}
 #endif
 
+	use_monotonic = 0;
+
 	return (evutil_gettimeofday(tp, NULL));
 }
 
@@ -179,10 +164,6 @@ event_base_new(void)
 	if ((base = calloc(1, sizeof(struct event_base))) == NULL)
 		event_err(1, "%s: calloc", __func__);
 
-	event_sigcb = NULL;
-	event_gotsig = 0;
-
-	detect_monotonic();
 	gettime(base, &base->event_tv);
 	
 	min_heap_ctor(&base->timeheap);
@@ -398,12 +379,9 @@ event_process_active(struct event_base *base)
 			ncalls--;
 			ev->ev_ncalls = ncalls;
 			(*ev->ev_callback)((int)ev->ev_fd, ev->ev_res, ev->ev_arg);
-			if (event_gotsig || base->event_break) {
-			  	ev->ev_pncalls = NULL;
+			if (base->event_break)
 				return;
-			}
 		}
-		ev->ev_pncalls = NULL;
 	}
 }
 
@@ -506,18 +484,6 @@ event_base_loop(struct event_base *base, int flags)
 			break;
 		}
 
-		/* You cannot use this interface for multi-threaded apps */
-		while (event_gotsig) {
-			event_gotsig = 0;
-			if (event_sigcb) {
-				res = (*event_sigcb)();
-				if (res == -1) {
-					errno = EINTR;
-					return (-1);
-				}
-			}
-		}
-
 		timeout_correct(base, &tv);
 
 		tv_p = &tv;
@@ -808,8 +774,6 @@ int
 event_del(struct event *ev)
 {
 	struct event_base *base;
-	const struct eventop *evsel;
-	void *evbase;
 
 	event_debug(("event_del: %p, callback %p",
 		 ev, ev->ev_callback));
@@ -819,8 +783,6 @@ event_del(struct event *ev)
 		return (-1);
 
 	base = ev->ev_base;
-	evsel = base->evsel;
-	evbase = base->evbase;
 
 	assert(!(ev->ev_flags & ~EVLIST_ALL));
 
@@ -838,7 +800,7 @@ event_del(struct event *ev)
 
 	if (ev->ev_flags & EVLIST_INSERTED) {
 		event_queue_remove(base, ev, EVLIST_INSERTED);
-		return (evsel->del(evbase, ev));
+		return (base->evsel->del(base->evbase, ev));
 	}
 
 	return (0);
diff --git a/third_party/libevent/event.h b/third_party/libevent/event.h
index d1f5d9e..f0887b9 100644
--- a/third_party/libevent/event.h
+++ b/third_party/libevent/event.h
@@ -159,7 +159,7 @@
 extern "C" {
 #endif
 
-#include <event-config.h>
+#include "event-config.h"
 #ifdef _EVENT_HAVE_SYS_TYPES_H
 #include <sys/types.h>
 #endif
@@ -172,7 +172,7 @@ extern "C" {
 #include <stdarg.h>
 
 /* For int types. */
-#include <evutil.h>
+#include "evutil.h"
 
 #ifdef WIN32
 #define WIN32_LEAN_AND_MEAN
diff --git a/third_party/libevent/evhttp.h b/third_party/libevent/evhttp.h
index cba8be1..48c1d91 100644
--- a/third_party/libevent/evhttp.h
+++ b/third_party/libevent/evhttp.h
@@ -27,7 +27,7 @@
 #ifndef _EVHTTP_H_
 #define _EVHTTP_H_
 
-#include <event.h>
+#include "event.h"
 
 #ifdef __cplusplus
 extern "C" {
diff --git a/third_party/libevent/evutil.h b/third_party/libevent/evutil.h
index dcb0013..8b664b9 100644
--- a/third_party/libevent/evutil.h
+++ b/third_party/libevent/evutil.h
@@ -38,7 +38,7 @@
 extern "C" {
 #endif
 
-#include <event-config.h>
+#include "event-config.h"
 #ifdef _EVENT_HAVE_SYS_TIME_H
 #include <sys/time.h>
 #endif
