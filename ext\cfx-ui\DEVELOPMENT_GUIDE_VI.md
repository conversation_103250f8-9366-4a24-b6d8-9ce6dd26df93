# Hướng dẫn Phát triển FiveM UI (Tiếng Việt)

## Vấn đề: `nuiWindow.invokeNative is not a function`

### Tại sao lỗi này xảy ra?

Khi phát triển FiveM UI trong môi trường development (`http://localhost:4200/`), bạn sẽ gặp lỗi này vì:

1. **Trong FiveM thực tế**: UI chạy trong NUI (Native User Interface) window có sẵn các API như `invokeNative`
2. **Trong browser development**: UI chạy trong browser thông thường không có các API này
3. **Hàm `invokeNative`** được sử dụng để giao tiếp với FiveM game client

### Giải pháp đã triển khai

Chúng tôi đã tạo một hệ thống mock hoàn chỉnh để xử lý vấn đề này:

#### 1. Mock System (`devMocks.ts`)
- Mock tất cả các native calls quan trọng
- <PERSON><PERSON> cấp dữ liệu giả lập cho development
- Tự động phản hồi các API calls

#### 2. Conditional Loading
```typescript
const nuiWindow = (__CFXUI_DEV__ && !(window as any).invokeNative) 
  ? { ...window, ...createMockNuiWindow() } as any
  : window as any;
```

#### 3. Mock Data
- Convars với giá trị mặc định
- Server data giả lập
- User data mock

## Cách sử dụng

### Development Environment

1. **Khởi động development server:**
```bash
cd ext/cfx-ui
yarn serve
```

2. **Truy cập UI trong browser:**
```
http://localhost:4200/
```

3. **Kiểm tra console để xem mock calls:**
- Mở Developer Tools (F12)
- Xem tab Console để theo dõi mock native calls
- Sử dụng `window.__FIVEM_DEV_MOCKS__` để truy cập mock data

### Production Environment

Khi chạy trong FiveM thực tế:
```bash
FiveM.exe +set ui_url http://localhost:4200/
```

Hoặc sử dụng build production:
```bash
yarn build
```

## Mock Functions được hỗ trợ

### Convars
- `getConvars`: Trả về danh sách convars mặc định
- `setConvar`: Cập nhật convar và phản hồi
- `setArchivedConvar`: Xử lý archived convars

### Server Operations
- `queryServer`: Mock server query với dữ liệu giả
- `checkNickname`: Validate nickname
- `getMinModeInfo`: Thông tin min mode

### File Operations
- `openFileDialog`: Mock file dialog
- `openUrl`: Log URL opens

### System Operations
- `backfillEnable`: System initialization
- `loadWarning`: Load warnings
- `executeCommand`: Command execution

## Debug và Troubleshooting

### 1. Kiểm tra Mock Environment
```javascript
// Trong browser console
console.log(window.__FIVEM_DEV_MOCKS__);
```

### 2. Thêm Mock cho Native mới
Chỉnh sửa `devMocks.ts`:
```typescript
case 'yourNewNative':
  postMockMessage({
    type: 'yourResponse',
    data: { /* your mock data */ }
  }, 100);
  break;
```

### 3. Test với FiveM thực tế
```bash
# Chạy FiveM với development UI
FiveM.exe +set ui_url http://localhost:4200/
```

## Best Practices

### 1. Luôn kiểm tra `__CFXUI_DEV__`
```typescript
if (__CFXUI_DEV__) {
  // Development-only code
}
```

### 2. Log tất cả mock calls
```typescript
console.log('[DEV MOCK] Native call:', native, arg);
```

### 3. Cung cấp realistic mock data
```typescript
const mockServer = {
  hostname: 'Development Mock Server',
  players: 42,
  maxPlayers: 64,
  ping: 25
};
```

### 4. Test cả development và production
- Test trong browser với mock data
- Test trong FiveM với real data
- Đảm bảo UI hoạt động trong cả hai môi trường

## Cấu trúc File

```
ext/cfx-ui/src/cfx/apps/mpMenu/
├── mpMenu.ts                 # Main entry point với mock integration
├── utils/
│   └── devMocks.ts          # Mock system cho development
└── services/
    └── convars/
        └── convars.service.ts # Service sử dụng native calls
```

## Lưu ý quan trọng

1. **Mock chỉ hoạt động trong development** (`__CFXUI_DEV__ = true`)
2. **Production build sẽ sử dụng real FiveM APIs**
3. **Luôn test cả hai môi trường** trước khi deploy
4. **Mock data có thể được customize** thông qua `MOCK_DATA` object

## Troubleshooting thường gặp

### Lỗi: "Cannot read property 'invokeNative' of undefined"
- Đảm bảo mock system đã được khởi tạo
- Kiểm tra `__CFXUI_DEV__` flag
- Xem console logs để debug

### Mock không hoạt động
- Kiểm tra import của `devMocks.ts`
- Đảm bảo `initializeMockEnvironment()` được gọi
- Xem browser console để debug

### UI không hoạt động trong FiveM
- Đảm bảo production build không có mock code
- Kiểm tra `__CFXUI_DEV__` flag trong production
- Test với `yarn build` trước khi deploy
