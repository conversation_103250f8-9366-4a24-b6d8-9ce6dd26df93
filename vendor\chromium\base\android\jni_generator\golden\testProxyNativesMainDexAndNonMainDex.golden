// Copyright 2017 The Chromium Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     base/android/jni_generator/jni_registration_generator.py
// Please do not change its content.

#ifndef HEADER_GUARD
#define HEADER_GUARD

#include <jni.h>

#include "base/android/jni_generator/jni_generator_helper.h"
#include "base/android/jni_int_wrapper.h"
#include "base/stl_util.h"  // For base::size().


// Step 1: Forward declarations (classes).


// Step 2: Forward declarations (methods).

JNI_GENERATOR_EXPORT void Java_org_chromium_base_natives_GEN_1JNI_test_1foo_1Bar_1foo(
    JNIEnv* env,
    jclass jcaller);
JNI_GENERATOR_EXPORT void Java_org_chromium_base_natives_GEN_1JNI_test_1foo_1Bar_1bar(
    JNIEnv* env,
    jclass jcaller);
JNI_GENERATOR_EXPORT void Java_org_chromium_base_natives_GEN_1JNI_test_1foo_1Foo_1thisismaindex(
    JNIEnv* env,
    jclass jcaller);


// Step 3: Method declarations.


static const JNINativeMethod kMethods_org_chromium_base_natives_GEN_1JNI[] = {
    { "test_foo_Bar_foo", "()V",
        reinterpret_cast<void*>(Java_org_chromium_base_natives_GEN_1JNI_test_1foo_1Bar_1foo) },
    { "test_foo_Bar_bar", "()V",
        reinterpret_cast<void*>(Java_org_chromium_base_natives_GEN_1JNI_test_1foo_1Bar_1bar) },

};

namespace {

JNI_REGISTRATION_EXPORT bool RegisterNative_org_chromium_base_natives_GEN_1JNI(JNIEnv* env) {
  const int number_of_methods = base::size(kMethods_org_chromium_base_natives_GEN_1JNI);

  base::android::ScopedJavaLocalRef<jclass> native_clazz =
      base::android::GetClass(env, "org/chromium/base/natives/GEN_JNI");
  if (env->RegisterNatives(
      native_clazz.obj(),
      kMethods_org_chromium_base_natives_GEN_1JNI,
      number_of_methods) < 0) {

    jni_generator::HandleRegistrationError(env, native_clazz.obj(), __FILE__);
    return false;
  }

  return true;
}

}  // namespace

static const JNINativeMethod kMethods_org_chromium_base_natives_GEN_1JNIMAIN_DEX[] = {
    { "test_foo_Foo_thisismaindex", "()V",
        reinterpret_cast<void*>(Java_org_chromium_base_natives_GEN_1JNI_test_1foo_1Foo_1thisismaindex)
        },

};

namespace {

JNI_REGISTRATION_EXPORT bool RegisterNative_org_chromium_base_natives_GEN_1JNIMAIN_DEX(JNIEnv* env) {
  const int number_of_methods = base::size(kMethods_org_chromium_base_natives_GEN_1JNIMAIN_DEX);

  base::android::ScopedJavaLocalRef<jclass> native_clazz =
      base::android::GetClass(env, "org/chromium/base/natives/GEN_JNI");
  if (env->RegisterNatives(
      native_clazz.obj(),
      kMethods_org_chromium_base_natives_GEN_1JNIMAIN_DEX,
      number_of_methods) < 0) {

    jni_generator::HandleRegistrationError(env, native_clazz.obj(), __FILE__);
    return false;
  }

  return true;
}

}  // namespace


// Step 4: Main dex and non-main dex registration functions.

namespace test {

bool RegisterMainDexNatives(JNIEnv* env) {
  // Register natives in a proxy.
  if (!RegisterNative_org_chromium_base_natives_GEN_1JNIMAIN_DEX(env)) {
    return false;
  }


  return true;
}

bool RegisterNonMainDexNatives(JNIEnv* env) {
  // Register natives in a proxy.
  if (!RegisterNative_org_chromium_base_natives_GEN_1JNI(env)) {
    return false;
  }


  return true;
}

}  // namespace test

#endif  // HEADER_GUARD
