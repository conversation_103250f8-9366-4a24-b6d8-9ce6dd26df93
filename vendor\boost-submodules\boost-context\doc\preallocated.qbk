[/
          Copyright <PERSON> 2014.
 Distributed under the Boost Software License, Version 1.0.
    (See accompanying file LICENSE_1_0.txt or copy at
          http://www.boost.org/LICENSE_1_0.txt
]

[section Struct `preallocated`]

        struct preallocated {
            void        *   sp;
            std::size_t     size;
            stack_context   sctx;

            preallocated( void * sp, std:size_t size, stack_allocator sctx) noexcept;
        };

[heading Constructor]

    preallocated( void * sp, std:size_t size, stack_allocator sctx) noexcept;

[variablelist
[[Effects:] [Creates an object of preallocated.]]
]


[endsect]
