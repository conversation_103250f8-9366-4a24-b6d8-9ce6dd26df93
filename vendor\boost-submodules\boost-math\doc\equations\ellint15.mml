<math  xmlns="http://www.w3.org/1998/Math/MathML" display="block" ><mtable columnalign="left" class="align">           <mtr><mtd columnalign="right" class="align-odd"><mi >K</mi><mrow ><mo class="MathClass-open">(</mo><mrow><mi >k</mi></mrow><mo class="MathClass-close">)</mo></mrow></mtd>             <mtd class="align-even"> <mo class="MathClass-rel">=</mo> <msub><mrow ><mi >R</mi></mrow><mrow ><mi >F</mi> </mrow></msub ><mrow ><mo class="MathClass-open">(</mo><mrow><mn>0</mn><mo class="MathClass-punc">,</mo> <mn>1</mn> <mo class="MathClass-bin">&#x2212;</mo> <msup><mrow ><mi >k</mi></mrow><mrow ><mn>2</mn></mrow></msup ><mo class="MathClass-punc">,</mo> <mn>1</mn></mrow><mo class="MathClass-close">)</mo></mrow><mspace width="2em"/></mtd>                                      <mtd columnalign="right" class="align-label"></mtd>           <mtd class="align-label">           <mspace width="2em"/></mtd></mtr><mtr><mtd columnalign="right" class="align-odd"><mi >E</mi><mrow ><mo class="MathClass-open">(</mo><mrow><mi >k</mi></mrow><mo class="MathClass-close">)</mo></mrow></mtd>             <mtd class="align-even"> <mo class="MathClass-rel">=</mo> <msub><mrow ><mi >R</mi></mrow><mrow ><mi >F</mi> </mrow></msub ><mrow ><mo class="MathClass-open">(</mo><mrow><mn>0</mn><mo class="MathClass-punc">,</mo> <mn>1</mn> <mo class="MathClass-bin">&#x2212;</mo> <msup><mrow ><mi >k</mi></mrow><mrow ><mn>2</mn></mrow></msup ><mo class="MathClass-punc">,</mo> <mn>1</mn></mrow><mo class="MathClass-close">)</mo></mrow> <mo class="MathClass-bin">&#x2212;</mo><mfrac><mrow ><mn>1</mn></mrow> <mrow ><mn>3</mn></mrow></mfrac><msup><mrow ><mi >k</mi></mrow><mrow ><mn>2</mn></mrow></msup ><msub><mrow ><mi >R</mi></mrow><mrow ><mi >D</mi></mrow></msub ><mrow ><mo class="MathClass-open">(</mo><mrow><mn>0</mn><mo class="MathClass-punc">,</mo> <mn>1</mn> <mo class="MathClass-bin">&#x2212;</mo> <msup><mrow ><mi >k</mi></mrow><mrow ><mn>2</mn></mrow></msup ><mo class="MathClass-punc">,</mo> <mn>1</mn></mrow><mo class="MathClass-close">)</mo></mrow><mspace width="2em"/></mtd>                <mtd columnalign="right" class="align-label"></mtd>           <mtd class="align-label">           <mspace width="2em"/></mtd></mtr><mtr><mtd columnalign="right" class="align-odd"><mi >&#x03A0;</mi><mrow ><mo class="MathClass-open">(</mo><mrow><mi >n</mi><mo class="MathClass-punc">,</mo><mi >k</mi></mrow><mo class="MathClass-close">)</mo></mrow></mtd>           <mtd class="align-even"> <mo class="MathClass-rel">=</mo> <msub><mrow ><mi >R</mi></mrow><mrow ><mi >F</mi> </mrow></msub ><mrow ><mo class="MathClass-open">(</mo><mrow><mn>0</mn><mo class="MathClass-punc">,</mo> <mn>1</mn> <mo class="MathClass-bin">&#x2212;</mo> <msup><mrow ><mi >k</mi></mrow><mrow ><mn>2</mn></mrow></msup ><mo class="MathClass-punc">,</mo> <mn>1</mn></mrow><mo class="MathClass-close">)</mo></mrow> <mo class="MathClass-bin">+</mo> <mfrac><mrow ><mn>1</mn></mrow> <mrow ><mn>3</mn></mrow></mfrac><mi >n</mi><msub><mrow ><mi >R</mi></mrow><mrow ><mi >J</mi></mrow></msub ><mrow ><mo class="MathClass-open">(</mo><mrow><mn>0</mn><mo class="MathClass-punc">,</mo> <mn>1</mn> <mo class="MathClass-bin">&#x2212;</mo> <msup><mrow ><mi >k</mi></mrow><mrow ><mn>2</mn></mrow></msup ><mo class="MathClass-punc">,</mo> <mn>1</mn><mo class="MathClass-punc">,</mo> <mn>1</mn> <mo class="MathClass-bin">&#x2212;</mo> <mi >n</mi></mrow><mo class="MathClass-close">)</mo></mrow><mspace width="2em"/></mtd>           <mtd columnalign="right" class="align-label"></mtd>           <mtd class="align-label">   <mspace width="2em"/></mtd></mtr></mtable></math>