# Copyright 2018 The Chromium Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

component("embedder") {
  output_name = "mojo_core_embedder"

  public = [
    "configuration.h",
    "embedder.h",
    "scoped_ipc_support.h",
  ]

  sources = [
    "embedder.cc",
    "scoped_ipc_support.cc",
  ]

  defines = [ "IS_MOJO_CORE_EMBEDDER_IMPL" ]

  public_deps = [ "//base" ]

  deps = [
    "//mojo/core:embedder_internal",
    "//mojo/public/c/system",
  ]
}
