# Copyright 2016, 2017 <PERSON>
# Copyright 2017 - 2019 <PERSON> III
# Copyright 2019 - 2021 <PERSON> G<PERSON>d
#
# Distributed under the Boost Software License, Version 1.0.
# https://www.boost.org/LICENSE_1_0.txt

version: 1.0.{build}-{branch}

shallow_clone: true

branches:
  only:
    - master
    - develop
    - /bugfix\/.*/
    - /feature\/.*/
    - /fix\/.*/
    - /pr\/.*/

matrix:
  fast_finish: false
  # Adding MAYFAIL to any matrix job allows it to fail but the build stays green:
  allow_failures:
    - MAYFAIL: true

environment:
  global:
    B2_CI_VERSION: 1
    GIT_FETCH_JOBS: 4
    # see: http://www.boost.org/build/doc/html/bbv2/overview/invocation.html#bbv2.overview.invocation.properties
    # to use the default for a given environment, comment it out; recommend you build debug and release however:
    # on Windows it is important to exercise all the possibilities, especially shared vs static, however most
    # libraries that care about this exercise it in their Jamfiles...
    B2_ADDRESS_MODEL: 32,64
    B2_LINK: shared,static
    # B2_THREADING: threading=multi,single
    B2_VARIANT: debug,release

  matrix:
    - FLAVOR: Visual Studio 2008, 2010, 2012
      APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2015
      B2_TOOLSET: msvc-9.0,msvc-10.0,msvc-11.0
      B2_ADDRESS_MODEL: 32 # No 64bit support

    - FLAVOR: Visual Studio 2013
      APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2015
      B2_TOOLSET: msvc-12.0

    - FLAVOR: Visual Studio 2015
      APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2017
      B2_TOOLSET: msvc-14.0

    - FLAVOR: Visual Studio 2017 C++14/17
      APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2017
      B2_CXXSTD: 14,17
      B2_TOOLSET: msvc-14.1

    - FLAVOR: Visual Studio 2017 C++2a Strict
      APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2017
      B2_CXXFLAGS: -permissive-
      B2_CXXSTD: 2a
      B2_TOOLSET: msvc-14.1
      # The VS2017 image has some issues which we workaround, so collect coverage for that.
      COVERAGE: true

    - FLAVOR: Visual Studio 2019
      APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2019
      B2_CXXFLAGS: -permissive-
      B2_CXXSTD: 14,17,2a
      B2_TOOLSET: msvc-14.2

    - FLAVOR: Visual Studio 2022
      APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2022
      B2_CXXFLAGS: -permissive-
      B2_CXXSTD: 14,17,20
      B2_TOOLSET: msvc-14.3

    - FLAVOR: clang-cl
      APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2017
      B2_ADDRESS_MODEL: 64
      B2_CXXSTD: 11,14,17
      B2_TOOLSET: clang-win

    - FLAVOR: clang-cl using windows.h
      APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2017
      B2_ADDRESS_MODEL: 64
      B2_CXXSTD: 17
      B2_TOOLSET: clang-win
      B2_DEFINES: BOOST_USE_WINDOWS_H

    - FLAVOR: cygwin (32-bit)
      APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2017
      ADDPATH: C:\cygwin\bin;
      B2_ADDRESS_MODEL: 32
      B2_CXXSTD: 11,14,1z
      B2_TOOLSET: gcc

    - FLAVOR: cygwin (64-bit)
      APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2017
      ADDPATH: C:\cygwin64\bin;
      B2_ADDRESS_MODEL: 64
      B2_CXXSTD: 11,14,1z
      B2_TOOLSET: gcc

    # (Currently) the images up to 2017 use an older Cygwin
    # This tests that the library works with more recent versions
    - FLAVOR: cygwin (64-bit, latest)
      APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2022
      ADDPATH: C:\cygwin64\bin;
      B2_ADDRESS_MODEL: 64
      B2_CXXSTD: 11,1z
      B2_TOOLSET: gcc

    - FLAVOR: mingw64 (32-bit)
      APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2019
      ADDPATH: C:\mingw-w64\i686-8.1.0-posix-dwarf-rt_v6-rev0\mingw32\bin;
      B2_ADDRESS_MODEL: 32
      B2_CXXSTD: 03,11,14,17,2a
      B2_TOOLSET: gcc

    - FLAVOR: mingw64
      APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2019
      ADDPATH: C:\mingw-w64\x86_64-8.1.0-posix-seh-rt_v6-rev0\mingw64\bin;
      B2_ADDRESS_MODEL: 64
      B2_CXXSTD: 11,14,17,2a
      B2_TOOLSET: gcc

install:
  - git clone --depth 1 https://github.com/boostorg/boost-ci.git C:\boost-ci-cloned
  # Copy ci folder if not testing Boost.CI
  - if NOT "%APPVEYOR_PROJECT_NAME%" == "boost-ci" xcopy /s /e /q /i /y C:\boost-ci-cloned\ci .\ci
  - rmdir /s /q C:\boost-ci-cloned
  - ci\appveyor\install.bat

build: off

test_script:
  - set B2_TARGETS=libs/locale/test//show_config --verbose-test
  - ci\build.bat
  - set B2_TARGETS=libs/locale/test
  - ci\build.bat

for:
  # CodeCov coverage build
  - matrix:
      only: [COVERAGE: true]
    test_script: [ps: ci\codecov.ps1]
