vendor_component 'tinyxml2'
vendor_component 'tinyxml2-dll'
vendor_component 'breakpad'
vendor_component 'breakpad-crt'
vendor_component 'msgpack-c'
vendor_component 'protobuf_lite'
vendor_component 'zlib'
vendor_component 'zlib-crt'
vendor_component 'zstd'
vendor_component 'zstd-crt'
vendor_component 'opus'
vendor_component 'libuv'
vendor_component 'picohttpparser'
vendor_component 'udis86'
vendor_component 'cpp-url'
vendor_component 'cpp-url-nortti'
vendor_component 'xz'
vendor_component 'xz-crt'
vendor_component 'curl'
vendor_component 'curl-crt'
vendor_component 'curl-static'
vendor_component 'libnode'
vendor_component 'lua'
vendor_component 'lua-noglm'
vendor_component 'rpmalloc'
vendor_component 'leveldb'
vendor_component 'rocksdb'
vendor_component 'botan'
vendor_component 'fmtlib'
vendor_component 'fmtlib-crt'
vendor_component 'cpr'
vendor_component 'cpr-crt'
vendor_component 'imgui'
vendor_component 'tbb'
vendor_component 'tbb-crt'
vendor_component 'nng'

vendor_component 'boost_program_options'
vendor_component 'boost_filesystem'
vendor_component 'boost_random'
vendor_component 'boost_system'
vendor_component 'boost_locale'
vendor_component 'boost_locale-crt'

vendor_component 'freetype'

vendor_component 'rnnoise'

if os.target() == 'windows' then
	vendor_component 'discord-rpc'
	vendor_component 'minhook'
	vendor_component 'minhook-crt'
	vendor_component 'fx11'
	vendor_component 'im3d'
	vendor_component 'nvapi'
end

vendor_component 'hdiffpatch'
vendor_component 'hdiffpatch-crt'
vendor_component 'libfvad'
vendor_component 'webrtc-audio-processing'
vendor_component 'pplx'
vendor_component 'replxx'
vendor_component 'nghttp2'
vendor_component 'nghttp2-crt'
vendor_component 'lz4'
vendor_component 'node'
vendor_component 'openssl_crypto'
vendor_component 'openssl_ssl'
vendor_component 'openssl_crypto_crt'
vendor_component 'openssl_ssl_crt'
vendor_component 'minizip'
vendor_component 'minizip-crt'
vendor_component 'glm'
vendor_component 'utfcpp'
vendor_component 'eastl'
vendor_component 'mbedtls'
vendor_component 'mbedtls_crt'
vendor_component 'uvw'
vendor_component 'websocketpp'
vendor_component 'thread-pool-cpp'
vendor_component 'toojpeg'
vendor_component 'uws'
vendor_component 'nngpp'
vendor_component 'bgfx'
vendor_component 'labsound'
vendor_component 'mojo'
vendor_component 'prometheus-cpp'
vendor_component 'folly'
vendor_component 'tinygltf'
vendor_component 'concurrentqueue'
vendor_component 'xenium'
vendor_component 'directxtex'
vendor_component 'dspfilters'
vendor_component 'speexdsp'
vendor_component 'wil'
vendor_component 'v8-93'
vendor_component 'v8-12.4'
vendor_component 'cpu_features'
vendor_component 'citizen_enet'
vendor_component 'citizen_util'
vendor_component 'catch2'