// Copyright 2014 The Chromium Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     base/android/jni_generator/jni_generator.py
// For
//     org/chromium/foo/Foo

#ifndef org_chromium_foo_Foo_JNI
#define org_chromium_foo_Foo_JNI

#include <jni.h>

#include "base/android/jni_generator/jni_generator_helper.h"


// Step 1: Forward declarations.

JNI_REGISTRATION_EXPORT extern const char kClassPath_org_chromium_foo_Foo[];
const char kClassPath_org_chromium_foo_Foo[] = "org/chromium/foo/Foo";
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_REGISTRATION_EXPORT std::atomic<jclass> g_org_chromium_foo_Foo_clazz(nullptr);
#ifndef org_chromium_foo_Foo_clazz_defined
#define org_chromium_foo_Foo_clazz_defined
inline jclass org_chromium_foo_Foo_clazz(JNIEnv* env) {
  return base::android::LazyGetClass(env, kClassPath_org_chromium_foo_Foo,
      &g_org_chromium_foo_Foo_clazz);
}
#endif


// Step 2: Constants (optional).


// Step 3: Method stubs.
namespace org {
namespace chromium_foo {

JNI_GENERATOR_EXPORT void Java_org_chromium_foo_Foo_nativeInstanceMethod(
    JNIEnv* env,
    jobject jcaller,
    jlong nativeInstance) {
  TRACE_EVENT0("jni", "org::chromium_foo::Instance::InstanceMethod");
  Instance* native = reinterpret_cast<Instance*>(nativeInstance);
  CHECK_NATIVE_PTR(env, jcaller, native, "InstanceMethod");
  return native->InstanceMethod(env, base::android::JavaParamRef<jobject>(env, jcaller));
}

static void JNI_Foo_StaticMethod(JNIEnv* env);

JNI_GENERATOR_EXPORT void Java_org_chromium_foo_Foo_nativeStaticMethod(
    JNIEnv* env,
    jclass jcaller) {
  TRACE_EVENT0("jni", "org::chromium_foo::JNI_Foo_StaticMethod");
  return JNI_Foo_StaticMethod(env);
}


static std::atomic<jmethodID> g_org_chromium_foo_Foo_Constructor(nullptr);
static base::android::ScopedJavaLocalRef<jobject> Java_Foo_Constructor(JNIEnv* env) {
  jclass clazz = org_chromium_foo_Foo_clazz(env);
  CHECK_CLAZZ(env, clazz,
      org_chromium_foo_Foo_clazz(env), NULL);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "<init>",
          "()V",
          &g_org_chromium_foo_Foo_Constructor);

  TRACE_EVENT0("jni", "org.chromium.foo.Foo.<init>");
  jobject ret =
      env->NewObject(clazz,
          call_context.base.method_id);
  return base::android::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_org_chromium_foo_Foo_callbackFromNative(nullptr);
static void Java_Foo_callbackFromNative(JNIEnv* env, const base::android::JavaRef<jobject>& obj) {
  jclass clazz = org_chromium_foo_Foo_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_chromium_foo_Foo_clazz(env));

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "callbackFromNative",
          "()V",
          &g_org_chromium_foo_Foo_callbackFromNative);

  TRACE_EVENT0("jni", "org.chromium.foo.Foo.callbackFromNative");
     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id);
}

}  // namespace chromium_foo
}  // namespace org

// Step 4: Generated test functions (optional).


#endif  // org_chromium_foo_Foo_JNI
