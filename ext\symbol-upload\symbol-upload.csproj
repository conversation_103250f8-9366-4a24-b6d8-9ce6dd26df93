<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net6.0</TargetFramework>
    <RootNamespace>CitizenFX.Buildtools.SymbolUpload</RootNamespace>
    <RestoreAdditionalProjectSources>
      https://pkgs.dev.azure.com/dnceng/public/_packaging/dotnet-eng/nuget/v3/index.json;
      https://pkgs.dev.azure.com/dnceng/public/_packaging/dotnet-tools/nuget/v3/index.json;
    </RestoreAdditionalProjectSources>
  </PropertyGroup>
  
  <ItemGroup>
    <!-- manually specified dependency -->
    <PackageReference Include="Microsoft.SymbolStore" Version="1.0.215101" />
    <!-- msft pls open source this -->
    <PackageReference Include="Microsoft.SymbolUploader" Version="1.0.0-beta-65201-01" />
  </ItemGroup>

</Project>
