# Boost Container Library Test Jamfile

#  (C) Copyright Ion Gaztanaga 2009.
# Use, modification and distribution are subject to the
# Boost Software License, Version 1.0. (See accompanying file
# LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

# Adapted from <PERSON>'s TR1 Jamfile.v2
# Copyright <PERSON> 2005.
# Use, modification and distribution are subject to the
# Boost Software License, Version 1.0. (See accompanying file
# LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

# this rule enumerates through all the sources and invokes
# the run rule for each source, the result is a list of all
# the run rules, which we can pass on to the test_suite rule:

rule test_all
{
   local all_rules = ;

   for local fileb in [ glob *.cpp ]
   {
      all_rules += [ run $(fileb) /boost/container//boost_container
      :  # additional args
      :  # test-files
      :  # requirements
      ] ;
   }

   return $(all_rules) ;
}

test-suite container_bench : [ test_all r ] ;
