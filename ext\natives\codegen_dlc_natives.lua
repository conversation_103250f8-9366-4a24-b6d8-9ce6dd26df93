native "0x42B65DEEF2EDF2A1"
	hash "0x42B65DEEF2EDF2A1"
	arguments {
		Any "p0",
	}
	ns "SYSTEM"
	returns	"void"

native "0x3D120012440E6683"
	hash "0x3D120012440E6683"
	ns "AUDIO"
	returns	"Any"

native "0xB81CF134AEB56FFB"
	hash "0xB81CF134AEB56FFB"
	ns "AUDIO"
	returns	"void"

native "0x5E203DA2BA15D436"
	hash "0x5E203DA2BA15D436"
	arguments {
		Any "p0",
	}
	ns "AUDIO"
	returns	"Any"

native "0x40763EA7B9B783E7"
	hash "0x40763EA7B9B783E7"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",
	}
	ns "AUDIO"
	returns	"Any"

native "0x0BE4BE946463F917"
	hash "0x0BE4BE946463F917"
	arguments {
		Any "p0",
	}
	ns "AUDIO"
	returns	"Any"

native "0x651D3228960D08AF"
	hash "0x651D3228960D08AF"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "AUDIO"
	returns	"void"

native "0x5B9853296731E88D"
	hash "0x5B9853296731E88D"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",

		Any "p4",

		Any "p5",
	}
	ns "AUDIO"
	returns	"void"

native "0x5D2BFAAB8D956E0E"
	hash "0x5D2BFAAB8D956E0E"
	ns "AUDIO"
	returns	"void"

native "0x9A53DED9921DE990"
	hash "0x9A53DED9921DE990"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "AUDIO"
	returns	"void"

native "0x159B7318403A1CD8"
	hash "0x159B7318403A1CD8"
	arguments {
		Any "p0",
	}
	ns "AUDIO"
	returns	"void"

native "0x1B7ABE26CBCBF8C7"
	hash "0x1B7ABE26CBCBF8C7"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",
	}
	ns "AUDIO"
	returns	"void"

native "0xDA07819E452FFE8F"
	hash "0xDA07819E452FFE8F"
	arguments {
		Any "p0",
	}
	ns "AUDIO"
	returns	"void"

native "0x11579D940949C49E"
	hash "0x11579D940949C49E"
	arguments {
		Any "p0",
	}
	ns "AUDIO"
	returns	"void"

native "0x7EC3C679D0E7E46B"
	hash "0x7EC3C679D0E7E46B"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",
	}
	ns "AUDIO"
	returns	"void"

native "0x1F351CF1C6475734"
	hash "0x1F351CF1C6475734"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",

		Any "p4",

		Any "p5",

		Any "p6",

		Any "p7",

		Any "p8",

		Any "p9",
	}
	ns "BRAIN"
	returns	"void"

native "0x19D1B791CB3670FE"
	hash "0x19D1B791CB3670FE"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "BRAIN"
	returns	"void"

native "0x8C33220C8D78CA0D"
	hash "0x8C33220C8D78CA0D"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "BRAIN"
	returns	"void"

native "0x92C360B5F15D2302"
	hash "0x92C360B5F15D2302"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",

		Any "p4",

		Any "p5",

		Any "p6",
	}
	ns "BRAIN"
	returns	"void"

native "0x4879E4FE39074CDF"
	hash "0x4879E4FE39074CDF"
	ns "CAM"
	returns	"Any"

native "0x62374889A4D59F72"
	hash "0x62374889A4D59F72"
	ns "CAM"
	returns	"void"

native "0x48608C3464F58AB4"
	hash "0x48608C3464F58AB4"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",
	}
	ns "CAM"
	returns	"void"

native "0x0AA27680A0BD43FA"
	hash "0x0AA27680A0BD43FA"
	ns "CAM"
	returns	"void"

native "0x7B8A361C1813FBEF"
	hash "0x7B8A361C1813FBEF"
	ns "CAM"
	returns	"void"

native "0xD7360051C885628B"
	hash "0xD7360051C885628B"
	ns "CAM"
	returns	"Any"

native "0x47B595D60664CFFA"
	hash "0x47B595D60664CFFA"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "CAM"
	returns	"void"

native "0xE827B9382CFB41BA"
	hash "0xE827B9382CFB41BA"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",
	}
	ns "CAM"
	returns	"void"

native "0x6493CF69859B116A"
	hash "0x6493CF69859B116A"
	ns "CAM"
	returns	"void"

native "0x5C48B75732C8456C"
	hash "0x5C48B75732C8456C"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",

		Any "p4",

		Any "p5",
	}
	ns "ENTITY"
	returns	"void"

native "0xFD1695C5D3B05439"
	hash "0xFD1695C5D3B05439"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",

		Any "p4",

		Any "p5",
	}
	ns "ENTITY"
	returns	"void"

native "0x6CE177D014502E8A"
	hash "0x6CE177D014502E8A"
	arguments {
		Any "p0",
	}
	ns "ENTITY"
	returns	"void"

native "0xB328DCC3A3AA401B"
	hash "0xB328DCC3A3AA401B"
	arguments {
		Any "p0",
	}
	ns "ENTITY"
	returns	"Any"

native "0x46F8696933A63C9B"
	hash "0x46F8696933A63C9B"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "ENTITY"
	returns	"Vector3"

native "0xCE6294A232D03786"
	hash "0xCE6294A232D03786"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "ENTITY"
	returns	"Vector3"

native "0xD95CC5D2AB15A09F"
	hash "0xD95CC5D2AB15A09F"
	arguments {
		Any "p0",
	}
	ns "ENTITY"
	returns	"Any"

native "0x490861B88F4FD846"
	hash "0x490861B88F4FD846"
	arguments {
		Any "p0",
	}
	ns "ENTITY"
	returns	"void"

native "0x36F32DE87082343E"
	hash "0x36F32DE87082343E"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "ENTITY"
	returns	"void"

native "0xB17BC6453F6CF5AC"
	hash "0xB17BC6453F6CF5AC"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "ENTITY"
	returns	"void"

native "0x352E2B5CF420BF3B"
	hash "0x352E2B5CF420BF3B"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "ENTITY"
	returns	"void"

native "0xC34BC448DA29F5E9"
	hash "0xC34BC448DA29F5E9"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "ENTITY"
	returns	"void"

native "0xCEA7C8E1B48FF68C"
	hash "0xCEA7C8E1B48FF68C"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "ENTITY"
	returns	"void"

native "0xD40AAC51E8E4C663"
	hash "0xD40AAC51E8E4C663"
	arguments {
		Any "p0",
	}
	ns "FILES"
	returns	"Any"

native "0xD81B7F27BC773E66"
	hash "0xD81B7F27BC773E66"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",

		Any "p4",
	}
	ns "FILES"
	returns	"void"

native "0x82ACC484FFA3B05F"
	hash "0x82ACC484FFA3B05F"
	arguments {
		Any "p0",
	}
	ns "GRAPHICS"
	returns	"Any"

native "0xE35B38A27E8E7179"
	hash "0xE35B38A27E8E7179"
	arguments {
		Any "p0",
	}
	ns "GRAPHICS"
	returns	"Any"

native "0x259BA6D4E6F808F1"
	hash "0x259BA6D4E6F808F1"
	arguments {
		Any "p0",
	}
	ns "GRAPHICS"
	returns	"void"

native "0xEFD97FF47B745B8D"
	hash "0xEFD97FF47B745B8D"
	arguments {
		Any "p0",
	}
	ns "GRAPHICS"
	returns	"void"

native "0xE82728F0DE75D13A"
	hash "0xE82728F0DE75D13A"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",

		Any "p4",

		Any "p5",

		Any "p6",

		Any "p7",

		Any "p8",

		Any "p9",

		Any "p10",

		Any "p11",

		Any "p12",

		Any "p13",

		Any "p14",

		Any "p15",

		Any "p16",

		Any "p17",

		Any "p18",

		Any "p19",

		Any "p20",

		Any "p21",

		Any "p22",

		Any "p23",

		Any "p24",
	}
	ns "GRAPHICS"
	returns	"void"

native "0x799017F9E3B10112"
	hash "0x799017F9E3B10112"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",

		Any "p4",

		Any "p5",

		Any "p6",

		Any "p7",
	}
	ns "GRAPHICS"
	returns	"void"

native "0x2BC54A8188768488"
	hash "0x2BC54A8188768488"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",

		Any "p4",

		Any "p5",

		Any "p6",

		Any "p7",

		Any "p8",

		Any "p9",

		Any "p10",
	}
	ns "GRAPHICS"
	returns	"void"

native "0x29280002282F1928"
	hash "0x29280002282F1928"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",

		Any "p4",

		Any "p5",

		Any "p6",

		Any "p7",

		Any "p8",

		Any "p9",

		Any "p10",

		Any "p11",

		Any "p12",

		Any "p13",

		Any "p14",

		Any "p15",

		Any "p16",

		Any "p17",

		Any "p18",

		Any "p19",

		Any "p20",

		Any "p21",

		Any "p22",

		Any "p23",
	}
	ns "GRAPHICS"
	returns	"void"

native "0x736D7AA1B750856B"
	hash "0x736D7AA1B750856B"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",

		Any "p4",

		Any "p5",

		Any "p6",

		Any "p7",

		Any "p8",

		Any "p9",

		Any "p10",

		Any "p11",

		Any "p12",

		Any "p13",

		Any "p14",

		Any "p15",

		Any "p16",

		Any "p17",

		Any "p18",

		Any "p19",

		Any "p20",

		Any "p21",

		Any "p22",

		Any "p23",

		Any "p24",

		Any "p25",

		Any "p26",

		Any "p27",

		Any "p28",

		Any "p29",

		Any "p30",

		Any "p31",
	}
	ns "GRAPHICS"
	returns	"void"

native "0x814AF7DCAACC597B"
	hash "0x814AF7DCAACC597B"
	arguments {
		Any "p0",
	}
	ns "GRAPHICS"
	returns	"void"

native "0xBA0127DA25FD54C9"
	hash "0xBA0127DA25FD54C9"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "GRAPHICS"
	returns	"void"

native "0x393BD2275CEB7793"
	hash "0x393BD2275CEB7793"
	ns "GRAPHICS"
	returns	"Any"

native "0xD80A80346A45D761"
	hash "0xD80A80346A45D761"
	arguments {
		Any "p0",
	}
	ns "GRAPHICS"
	returns	"Any"

native "0x95EB5E34F821BABE"
	hash "0x95EB5E34F821BABE"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",
	}
	ns "GRAPHICS"
	returns	"Any"

native "0x65E7E78842E74CDB"
	hash "0x65E7E78842E74CDB"
	arguments {
		Any "p0",
	}
	ns "GRAPHICS"
	returns	"Any"

native "0x77FE3402004CD1B0"
	hash "0x77FE3402004CD1B0"
	arguments {
		Any "p0",
	}
	ns "GRAPHICS"
	returns	"void"

native "0x1086127B3A63505E"
	hash "0x1086127B3A63505E"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",
	}
	ns "GRAPHICS"
	returns	"void"

native "0x9D75795B9DC6EBBF"
	hash "0x9D75795B9DC6EBBF"
	arguments {
		Any "p0",
	}
	ns "GRAPHICS"
	returns	"void"

native "0xA78DE25577300BA1"
	hash "0xA78DE25577300BA1"
	arguments {
		Any "p0",
	}
	ns "GRAPHICS"
	returns	"void"

native "0x1636D7FC127B10D2"
	hash "0x1636D7FC127B10D2"
	arguments {
		Any "p0",
	}
	ns "GRAPHICS"
	returns	"void"

native "0x19E50EB6E33E1D28"
	hash "0x19E50EB6E33E1D28"
	arguments {
		Any "p0",
	}
	ns "GRAPHICS"
	returns	"void"

native "0x0C8FAC83902A62DF"
	hash "0x0C8FAC83902A62DF"
	arguments {
		Any "p0",
	}
	ns "GRAPHICS"
	returns	"void"

native "0xFEBFBFDFB66039DE"
	hash "0xFEBFBFDFB66039DE"
	arguments {
		Any "p0",
	}
	ns "GRAPHICS"
	returns	"void"

native "0xFF5992E1C9E65D05"
	hash "0xFF5992E1C9E65D05"
	arguments {
		Any "p0",
	}
	ns "GRAPHICS"
	returns	"void"

native "0x44621483FF966526"
	hash "0x44621483FF966526"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "GRAPHICS"
	returns	"void"

native "0xC5C8F970D4EDFF71"
	hash "0xC5C8F970D4EDFF71"
	arguments {
		Any "p0",
	}
	ns "GRAPHICS"
	returns	"void"

native "0x6A51F78772175A51"
	hash "0x6A51F78772175A51"
	arguments {
		Any "p0",
	}
	ns "GRAPHICS"
	returns	"void"

native "0x9641588DAB93B4B5"
	hash "0x9641588DAB93B4B5"
	arguments {
		Any "p0",
	}
	ns "GRAPHICS"
	returns	"void"

native "0xB569F41F3E7E83A4"
	hash "0xB569F41F3E7E83A4"
	arguments {
		Any "p0",
	}
	ns "GRAPHICS"
	returns	"void"

native "0xBA3D194057C79A7B"
	hash "0xBA3D194057C79A7B"
	arguments {
		Any "p0",
	}
	ns "GRAPHICS"
	returns	"void"

native "0x2B40A97646381508"
	hash "0x2B40A97646381508"
	arguments {
		Any "p0",
	}
	ns "GRAPHICS"
	returns	"void"

native "0x32F34FF7F617643B"
	hash "0x32F34FF7F617643B"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "GRAPHICS"
	returns	"void"

native "0xE6A9F00D4240B519"
	hash "0xE6A9F00D4240B519"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "GRAPHICS"
	returns	"void"

native "0x5DBF05DB5926D089"
	hash "0x5DBF05DB5926D089"
	arguments {
		Any "p0",
	}
	ns "GRAPHICS"
	returns	"void"

native "0xCA465D9CC0D231BA"
	hash "0xCA465D9CC0D231BA"
	arguments {
		Any "p0",
	}
	ns "GRAPHICS"
	returns	"void"

native "0xCE5D0E5E315DB238"
	hash "0xCE5D0E5E315DB238"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",

		Any "p4",
	}
	ns "HUD"
	returns	"Any"

native "0xD12882D3FF82BF11"
	hash "0xD12882D3FF82BF11"
	ns "HUD"
	returns	"void"

native "0x2C173AE2BDB9385E"
	hash "0x2C173AE2BDB9385E"
	arguments {
		Any "p0",
	}
	ns "HUD"
	returns	"Any"

native "0xC2D2AD9EAAE265B8"
	hash "0xC2D2AD9EAAE265B8"
	ns "HUD"
	returns	"Any"

native "0x98C3CF913D895111"
	hash "0x98C3CF913D895111"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "HUD"
	returns	"Any"

native "0x3F0CF9CB7E589B88"
	hash "0x3F0CF9CB7E589B88"
	ns "HUD"
	returns	"Any"

native "0x488043841BBE156F"
	hash "0x488043841BBE156F"
	ns "HUD"
	returns	"void"

native "0x801879A9B4F4B2FB"
	hash "0x801879A9B4F4B2FB"
	ns "HUD"
	returns	"Any"

native "0x7C226D5346D4D10A"
	hash "0x7C226D5346D4D10A"
	arguments {
		Any "p0",
	}
	ns "HUD"
	returns	"void"

native "0xF47E567B3630DD12"
	hash "0xF47E567B3630DD12"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "HUD"
	returns	"void"

native "0x6A1738B4323FE2D9"
	hash "0x6A1738B4323FE2D9"
	arguments {
		Any "p0",
	}
	ns "HUD"
	returns	"void"

native "0x2DE6C5E2E996F178"
	hash "0x2DE6C5E2E996F178"
	arguments {
		Any "p0",
	}
	ns "HUD"
	returns	"void"

native "0x2916A928514C9827"
	hash "0x2916A928514C9827"
	ns "HUD"
	returns	"void"

native "0x577599CCED639CA2"
	hash "0x577599CCED639CA2"
	arguments {
		Any "p0",
	}
	ns "HUD"
	returns	"void"

native "0xB552929B85FC27EC"
	hash "0xB552929B85FC27EC"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "HUD"
	returns	"void"

native "0xA8B6AFDAC320AC87"
	hash "0xA8B6AFDAC320AC87"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "HUD"
	returns	"void"

native "0x4B5B620C9B59ED34"
	hash "0x4B5B620C9B59ED34"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "HUD"
	returns	"void"

native "0x2C9F302398E13141"
	hash "0x2C9F302398E13141"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "HUD"
	returns	"void"

native "0xD1942374085C8469"
	hash "0xD1942374085C8469"
	arguments {
		Any "p0",
	}
	ns "HUD"
	returns	"void"

native "0xA17784FCA9548D15"
	hash "0xA17784FCA9548D15"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",
	}
	ns "HUD"
	returns	"void"

native "0xCD74233600C4EA6B"
	hash "0xCD74233600C4EA6B"
	arguments {
		Any "p0",
	}
	ns "HUD"
	returns	"void"

native "0x2790F4B17D098E26"
	hash "0x2790F4B17D098E26"
	arguments {
		Any "p0",
	}
	ns "HUD"
	returns	"void"

native "0xB9C362BABECDDC7A"
	hash "0xB9C362BABECDDC7A"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",

		Any "p4",
	}
	ns "HUD"
	returns	"void"

native "0x9C16459B2324B2CF"
	hash "0x9C16459B2324B2CF"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "HUD"
	returns	"void"

native "0xFCFACD0DB9D7A57D"
	hash "0xFCFACD0DB9D7A57D"
	arguments {
		Ped "ped",

		Any "p1",
	}
	ns "HUD"
	returns	"void"

native "0xB13DCB4C6FAAD238"
	hash "0xB13DCB4C6FAAD238"
	arguments {
		Ped "ped",

		BOOL "toggle",

		Any "p3",
	}
	ns "HUD"
	returns	"void"
	doc [[!
<summary>
		Called in decompiled scripts as alternative to _SET_PED_ENEMY_AI_BLIP in an else, when the additional parameter p3 is not -1
</summary>
	]]

native "0x6CDD58146A436083"
	hash "0x6CDD58146A436083"
	arguments {
		Any "p0",
	}
	ns "HUD"
	returns	"void"

native "0x15803FEC3B9A872B"
	hash "0x15803FEC3B9A872B"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",

		Any "p4",

		Any "p5",

		Any "p6",

		Any "p7",

		Any "p8",

		Any "p9",

		Any "p10",
	}
	ns "HUD"
	returns	"void"

native "0x32888337579A5970"
	hash "0x32888337579A5970"
	ns "HUD"
	returns	"void"

native "0x15CFA549788D35EF"
	hash "0x15CFA549788D35EF"
	ns "HUD"
	returns	"void"

native "0x483ACA1176CA93F1"
	hash "0x483ACA1176CA93F1"
	ns "INTERIOR"
	returns	"void"

native "0x50C375537449F369"
	hash "0x50C375537449F369"
	arguments {
		Any "p0",
	}
	ns "INTERIOR"
	returns	"void"

native "_SET_INTERIOR_PROP_COLOR"
	hash "0xC1F1920BAF281317"
	arguments {
		int "interiorID",

		charPtr "propName",

		int "color",
	}
	ns "INTERIOR"
	returns	"void"

native "0x7241CCB7D020DB69"
	hash "0x7241CCB7D020DB69"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "INTERIOR"
	returns	"void"

native "0x497420E022796B3F"
	hash "0x497420E022796B3F"
	ns "LOCALE"
	returns	"Any"

native "0x58A39BE597CE99CD"
	hash "0x58A39BE597CE99CD"
	ns "MISC"
	returns	"void"

native "0x0CF97F497FE7D048"
	hash "0x0CF97F497FE7D048"
	arguments {
		Any "p0",
	}
	ns "MISC"
	returns	"void"

native "0x213AEB2B90CBA7AC"
	hash "0x213AEB2B90CBA7AC"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",
	}
	ns "MISC"
	returns	"void"

native "0x171BAFB3C60389F4"
	hash "0x171BAFB3C60389F4"
	arguments {
		Any "p0",
	}
	ns "MISC"
	returns	"Any"

native "0x9E82F0F362881B29"
	hash "0x9E82F0F362881B29"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",

		Any "p4",
	}
	ns "MISC"
	returns	"Any"

native "0xE574A662ACAEFBB1"
	hash "0xE574A662ACAEFBB1"
	ns "MISC"
	returns	"void"

native "0x438822C279B73B93"
	hash "0x438822C279B73B93"
	arguments {
		Any "p0",
	}
	ns "MISC"
	returns	"void"

native "0xFB80AB299D2EE1BD"
	hash "0xFB80AB299D2EE1BD"
	arguments {
		Any "p0",
	}
	ns "MISC"
	returns	"void"

native "0x693478ACBD7F18E7"
	hash "0x693478ACBD7F18E7"
	ns "MISC"
	returns	"void"

native "0x9777734DAD16992F"
	hash "0x9777734DAD16992F"
	ns "NETCASH"
	returns	"Any"

native "0x30FD873ECE50E9F6"
	hash "0x30FD873ECE50E9F6"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",

		Any "p4",
	}
	ns "NETCASH"
	returns	"void"

native "0x0CB1BE0633C024A8"
	hash "0x0CB1BE0633C024A8"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",
	}
	ns "NETCASH"
	returns	"void"

native "0x08B0CA7A6AB3AC32"
	hash "0x08B0CA7A6AB3AC32"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",
	}
	ns "NETCASH"
	returns	"void"

native "0x8586789730B10CAF"
	hash "0x8586789730B10CAF"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",
	}
	ns "NETCASH"
	returns	"void"

native "0xECA658CE2A4E5A72"
	hash "0xECA658CE2A4E5A72"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "NETCASH"
	returns	"void"

native "0x84C0116D012E8FC2"
	hash "0x84C0116D012E8FC2"
	arguments {
		Any "p0",
	}
	ns "NETCASH"
	returns	"void"

native "0xC8407624CEF2354B"
	hash "0xC8407624CEF2354B"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "NETCASH"
	returns	"void"

native "0x9251B6ABF2D0A5B4"
	hash "0x9251B6ABF2D0A5B4"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "NETCASH"
	returns	"void"

native "0xEDEAD9A91EC768B3"
	hash "0xEDEAD9A91EC768B3"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",
	}
	ns "NETCASH"
	returns	"void"

native "0x3E4ADAFF1830F146"
	hash "0x3E4ADAFF1830F146"
	ns "NETCASH"
	returns	"Any"

native "0xCDA1C62BE2777802"
	hash "0xCDA1C62BE2777802"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",
	}
	ns "NETCASH"
	returns	"void"

native "0x55A1E095DB052FA5"
	hash "0x55A1E095DB052FA5"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "NETCASH"
	returns	"void"

native "0x821418C727FCACD7"
	hash "0x821418C727FCACD7"
	arguments {
		Any "p0",
	}
	ns "NETCASH"
	returns	"void"

native "0x08A1B82B91900682"
	hash "0x08A1B82B91900682"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",
	}
	ns "NETCASH"
	returns	"void"

native "0xDBC966A01C02BCA7"
	hash "0xDBC966A01C02BCA7"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",
	}
	ns "NETCASH"
	returns	"void"

native "0x5D97630A8A0EF123"
	hash "0x5D97630A8A0EF123"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",
	}
	ns "NETCASH"
	returns	"void"

native "0xFFBE02CD385356BD"
	hash "0xFFBE02CD385356BD"
	ns "NETCASH"
	returns	"Any"

native "0x12D148D26538D0F9"
	hash "0x12D148D26538D0F9"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",
	}
	ns "NETCASH"
	returns	"void"

native "0xAC272C0AE01B4BD8"
	hash "0xAC272C0AE01B4BD8"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",
	}
	ns "NETCASH"
	returns	"void"

native "0x5F456788B05FAEAC"
	hash "0x5F456788B05FAEAC"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",
	}
	ns "NETCASH"
	returns	"void"

native "0x5182A339A3474510"
	hash "0x5182A339A3474510"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",
	}
	ns "NETCASH"
	returns	"void"

native "0xC1952F3773BA18FE"
	hash "0xC1952F3773BA18FE"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",
	}
	ns "NETCASH"
	returns	"void"

native "0x5BCDE0F640C773D2"
	hash "0x5BCDE0F640C773D2"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",
	}
	ns "NETCASH"
	returns	"void"

native "0xD7CCCBA28C4ECAF0"
	hash "0xD7CCCBA28C4ECAF0"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",

		Any "p4",
	}
	ns "NETCASH"
	returns	"void"

native "0xE7DF4E0545DFB56E"
	hash "0xE7DF4E0545DFB56E"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",
	}
	ns "NETCASH"
	returns	"void"

native "0xE8B0B270B6E7C76E"
	hash "0xE8B0B270B6E7C76E"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",
	}
	ns "NETCASH"
	returns	"void"

native "0x05F04155A226FBBF"
	hash "0x05F04155A226FBBF"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",
	}
	ns "NETCASH"
	returns	"void"

native "0x0035BB914316F1E3"
	hash "0x0035BB914316F1E3"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",
	}
	ns "NETCASH"
	returns	"void"

native "0x0FE8E1FCD2B86B33"
	hash "0x0FE8E1FCD2B86B33"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",
	}
	ns "NETCASH"
	returns	"void"

native "0xB49ECA122467D05F"
	hash "0xB49ECA122467D05F"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",
	}
	ns "NETCASH"
	returns	"void"

native "0x112209CE0290C03A"
	hash "0x112209CE0290C03A"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",
	}
	ns "NETCASH"
	returns	"void"

native "0xE23ADC6FCB1F29AE"
	hash "0xE23ADC6FCB1F29AE"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",
	}
	ns "NETCASH"
	returns	"void"

native "0x0D30EB83668E63C5"
	hash "0x0D30EB83668E63C5"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",
	}
	ns "NETCASH"
	returns	"void"

native "0xED5FD7AF10F5E262"
	hash "0xED5FD7AF10F5E262"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",
	}
	ns "NETCASH"
	returns	"void"

native "0x6FD97159FE3C971A"
	hash "0x6FD97159FE3C971A"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",
	}
	ns "NETCASH"
	returns	"void"

native "0x998E18CEB44487FC"
	hash "0x998E18CEB44487FC"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",
	}
	ns "NETCASH"
	returns	"void"

native "0x33981D6804E62F49"
	hash "0x33981D6804E62F49"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",
	}
	ns "NETCASH"
	returns	"void"

native "0xB4C2EC463672474E"
	hash "0xB4C2EC463672474E"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",
	}
	ns "NETCASH"
	returns	"void"

native "0x69EF772B192614C1"
	hash "0x69EF772B192614C1"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",
	}
	ns "NETCASH"
	returns	"void"

native "0xBD0EFB25CCA8F97A"
	hash "0xBD0EFB25CCA8F97A"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",
	}
	ns "NETCASH"
	returns	"void"

native "0xFC4EE00A7B3BFB76"
	hash "0xFC4EE00A7B3BFB76"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",
	}
	ns "NETCASH"
	returns	"void"

native "0xA51B086B0B2C0F7A"
	hash "0xA51B086B0B2C0F7A"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",
	}
	ns "NETCASH"
	returns	"void"

native "0x2FAB6614CE22E196"
	hash "0x2FAB6614CE22E196"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",
	}
	ns "NETCASH"
	returns	"void"

native "0x675D19C6067CAE08"
	hash "0x675D19C6067CAE08"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",
	}
	ns "NETCASH"
	returns	"void"

native "0xFA07759E6FDDD7CF"
	hash "0xFA07759E6FDDD7CF"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",
	}
	ns "NETCASH"
	returns	"void"

native "0x4128464231E3CA0B"
	hash "0x4128464231E3CA0B"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",
	}
	ns "NETCASH"
	returns	"void"

native "0x2AFC2D19B50797F2"
	hash "0x2AFC2D19B50797F2"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",
	}
	ns "NETCASH"
	returns	"void"

native "0x8E243837643D9583"
	hash "0x8E243837643D9583"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",
	}
	ns "NETCASH"
	returns	"void"

native "0x365E877C61D6988B"
	hash "0x365E877C61D6988B"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",
	}
	ns "NETCASH"
	returns	"void"

native "0xA95F667A755725DA"
	hash "0xA95F667A755725DA"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",
	}
	ns "NETCASH"
	returns	"void"

native "0x0C82D21A77C22D49"
	hash "0x0C82D21A77C22D49"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",
	}
	ns "NETCASH"
	returns	"void"

native "0xA75CCF58A60A5FD1"
	hash "0xA75CCF58A60A5FD1"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",

		Any "p4",

		Any "p5",

		Any "p6",

		Any "p7",

		Any "p8",

		Any "p9",
	}
	ns "NETCASH"
	returns	"void"

native "0xD99DB210089617FE"
	hash "0xD99DB210089617FE"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",
	}
	ns "NETCASH"
	returns	"void"

native "0x375A706A5C2FD084"
	hash "0x375A706A5C2FD084"
	arguments {
		Any "p0",
	}
	ns "MOBILE"
	returns	"void"

native "0xE547E9114277098F"
	hash "0xE547E9114277098F"
	ns "NETSHOP"
	returns	"Any"

native "0x613F125BA3BD2EB9"
	hash "0x613F125BA3BD2EB9"
	ns "NETSHOP"
	returns	"Any"

native "0xBEC0816FF5ACBCDA"
	hash "0xBEC0816FF5ACBCDA"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "NETWORK"
	returns	"void"

native "0xE16AA70CE9BEEDC3"
	hash "0xE16AA70CE9BEEDC3"
	arguments {
		Any "p0",
	}
	ns "NETWORK"
	returns	"Any"

native "_CAN_REGISTER_MISSION_PICKUPS"
	hash "0x0A49D1CB6E34AF72"
	arguments {
		int "p0",
	}
	ns "NETWORK"
	returns	"BOOL"

native "0xF12E6CD06C73D69E"
	hash "0xF12E6CD06C73D69E"
	ns "NETWORK"
	returns	"Any"

native "0xB2092A1EAA7FD45F"
	hash "0xB2092A1EAA7FD45F"
	arguments {
		Any "p0",
	}
	ns "NETWORK"
	returns	"Any"

native "0x7EF7649B64D7FF10"
	hash "0x7EF7649B64D7FF10"
	arguments {
		Any "p0",
	}
	ns "NETWORK"
	returns	"Any"

native "0x3A8B55FDA4C8DDEF"
	hash "0x3A8B55FDA4C8DDEF"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",
	}
	ns "NETWORK"
	returns	"Any"

native "0x1950DAE9848A4739"
	hash "0x1950DAE9848A4739"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",
	}
	ns "NETWORK"
	returns	"Any"

native "0x697F508861875B42"
	hash "0x697F508861875B42"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",
	}
	ns "NETWORK"
	returns	"Any"

native "0xFAFC23AEE23868DB"
	hash "0xFAFC23AEE23868DB"
	ns "NETWORK"
	returns	"Any"

native "0x2B1C623823DB0D9D"
	hash "0x2B1C623823DB0D9D"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",

		Any "p4",

		Any "p5",

		Any "p6",
	}
	ns "NETWORK"
	returns	"Any"

native "0x144DA052257AE7D8"
	hash "0x144DA052257AE7D8"
	arguments {
		Any "p0",
	}
	ns "NETWORK"
	returns	"void"

native "0x8F5D1AD832AEB06C"
	hash "0x8F5D1AD832AEB06C"
	arguments {
		Any "p0",
	}
	ns "NETWORK"
	returns	"Any"

native "0x4AD490AE1536933B"
	hash "0x4AD490AE1536933B"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "NETWORK"
	returns	"Any"

native "0x07EAB372C8841D99"
	hash "0x07EAB372C8841D99"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",
	}
	ns "NETWORK"
	returns	"Any"

native "0x1888694923EF4591"
	hash "0x1888694923EF4591"
	ns "NETWORK"
	returns	"void"

native "0x6CE50E47F5543D0C"
	hash "0x6CE50E47F5543D0C"
	ns "NETWORK"
	returns	"void"

native "0x1632BE0AC1E62876"
	hash "0x1632BE0AC1E62876"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "NETWORK"
	returns	"void"

native "0xE6717E652B8C8D8A"
	hash "0xE6717E652B8C8D8A"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "NETWORK"
	returns	"void"

native "0x815F18AD865F057F"
	hash "0x815F18AD865F057F"
	arguments {
		Any "p0",
	}
	ns "NETWORK"
	returns	"Any"

native "0x2A5E0621DD815A9A"
	hash "0x2A5E0621DD815A9A"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",
	}
	ns "NETWORK"
	returns	"void"

native "0x83660B734994124D"
	hash "0x83660B734994124D"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",
	}
	ns "NETWORK"
	returns	"Any"

native "0xC434133D9BA52777"
	hash "0xC434133D9BA52777"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "NETWORK"
	returns	"Any"

native "0x37D5F739FD494675"
	hash "0x37D5F739FD494675"
	arguments {
		Any "p0",
	}
	ns "NETWORK"
	returns	"Any"

native "0x3855FB5EB2C5E8B2"
	hash "0x3855FB5EB2C5E8B2"
	arguments {
		Any "p0",
	}
	ns "NETWORK"
	returns	"Any"

native "0x2DA41ED6E1FCD7A5"
	hash "0x2DA41ED6E1FCD7A5"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "NETWORK"
	returns	"Any"

native "0x64D779659BC37B19"
	hash "0x64D779659BC37B19"
	arguments {
		Any "p0",
	}
	ns "NETWORK"
	returns	"Vector3"

native "0x125E6D638B8605D4"
	hash "0x125E6D638B8605D4"
	arguments {
		Any "p0",
	}
	ns "NETWORK"
	returns	"Vector3"

native "0x33DE49EDF4DDE77A"
	hash "0x33DE49EDF4DDE77A"
	arguments {
		Any "p0",
	}
	ns "NETWORK"
	returns	"Vector3"

native "0x617F49C2668E6155"
	hash "0x617F49C2668E6155"
	ns "NETWORK"
	returns	"Any"

native "0xAA5FAFCD2C5F5E47"
	hash "0xAA5FAFCD2C5F5E47"
	ns "NETWORK"
	returns	"Any"

native "0x71DC455F5CD1C2B1"
	hash "0x71DC455F5CD1C2B1"
	arguments {
		Any "p0",
	}
	ns "NETWORK"
	returns	"Any"

native "0x4C2A9FDC22377075"
	hash "0x4C2A9FDC22377075"
	ns "NETWORK"
	returns	"void"

native "0x2E4C123D1C8A710E"
	hash "0x2E4C123D1C8A710E"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",

		Any "p4",

		Any "p5",

		Any "p6",
	}
	ns "NETWORK"
	returns	"Any"

native "0x71302EC70689052A"
	hash "0x71302EC70689052A"
	arguments {
		Any "p0",
	}
	ns "NETWORK"
	returns	"Any"

native "0x7242F8B741CE1086"
	hash "0x7242F8B741CE1086"
	arguments {
		Any "p0",
	}
	ns "NETWORK"
	returns	"Any"

native "0x53C10C8BD774F2C9"
	hash "0x53C10C8BD774F2C9"
	ns "NETWORK"
	returns	"Any"

native "0x5626D9D6810730D5"
	hash "0x5626D9D6810730D5"
	ns "NETWORK"
	returns	"Any"

native "0xDFFA5BE8381C3314"
	hash "0xDFFA5BE8381C3314"
	ns "NETWORK"
	returns	"Any"

native "0x041C7F2A6C9894E6"
	hash "0x041C7F2A6C9894E6"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",
	}
	ns "NETWORK"
	returns	"Any"

native "0x5ECD378EE64450AB"
	hash "0x5ECD378EE64450AB"
	arguments {
		Any "p0",
	}
	ns "NETWORK"
	returns	"void"

native "0x59D421683D31835A"
	hash "0x59D421683D31835A"
	arguments {
		Any "p0",
	}
	ns "NETWORK"
	returns	"void"

native "0x0E4F77F7B9D74D84"
	hash "0x0E4F77F7B9D74D84"
	arguments {
		Any "p0",
	}
	ns "NETWORK"
	returns	"void"

native "0x2CE9D95E4051AECD"
	hash "0x2CE9D95E4051AECD"
	arguments {
		Any "p0",
	}
	ns "NETWORK"
	returns	"void"

native "0x0379DAF89BA09AA5"
	hash "0x0379DAF89BA09AA5"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "NETWORK"
	returns	"void"

native "0xBA7F0B77D80A4EB7"
	hash "0xBA7F0B77D80A4EB7"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "NETWORK"
	returns	"void"

native "0x4348BFDA56023A2F"
	hash "0x4348BFDA56023A2F"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "NETWORK"
	returns	"Any"

native "0x265559DA40B3F327"
	hash "0x265559DA40B3F327"
	arguments {
		Any "p0",
	}
	ns "NETWORK"
	returns	"void"

native "0x82A2B386716608F1"
	hash "0x82A2B386716608F1"
	ns "NETWORK"
	returns	"Any"

native "0x659CF2EF7F550C4F"
	hash "0x659CF2EF7F550C4F"
	ns "NETWORK"
	returns	"Any"

native "0xC3BFED92026A2AAD"
	hash "0xC3BFED92026A2AAD"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",

		Any "p4",
	}
	ns "NETWORK"
	returns	"Any"

native "0x04918A41BC9B8157"
	hash "0x04918A41BC9B8157"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",
	}
	ns "NETWORK"
	returns	"Any"

native "0xF6F4383B7C92F11A"
	hash "0xF6F4383B7C92F11A"
	arguments {
		Any "p0",
	}
	ns "NETWORK"
	returns	"void"

native "0x1F8E00FB18239600"
	hash "0x1F8E00FB18239600"
	arguments {
		Any "p0",
	}
	ns "NETWORK"
	returns	"void"

native "0x1398582B7F72B3ED"
	hash "0x1398582B7F72B3ED"
	arguments {
		Any "p0",
	}
	ns "NETWORK"
	returns	"void"

native "0x8B4FFC790CA131EF"
	hash "0x8B4FFC790CA131EF"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",
	}
	ns "NETWORK"
	returns	"Any"

native "0x472841A026D26D8B"
	hash "0x472841A026D26D8B"
	ns "NETWORK"
	returns	"Any"

native "0x42613035157E4208"
	hash "0x42613035157E4208"
	arguments {
		Any "p0",
	}
	ns "NETWORK"
	returns	"void"

native "0x17330EBF2F2124A8"
	hash "0x17330EBF2F2124A8"
	ns "NETWORK"
	returns	"void"

native "0x4BA166079D658ED4"
	hash "0x4BA166079D658ED4"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "NETWORK"
	returns	"void"

native "0x658500AE6D723A7E"
	hash "0x658500AE6D723A7E"
	arguments {
		Any "p0",
	}
	ns "NETWORK"
	returns	"void"

native "0xD7B6C73CAD419BCF"
	hash "0xD7B6C73CAD419BCF"
	arguments {
		Any "p0",
	}
	ns "NETWORK"
	returns	"void"

native "0x838DA0936A24ED4D"
	hash "0x838DA0936A24ED4D"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "NETWORK"
	returns	"void"

native "0x32EBD154CB6B8B99"
	hash "0x32EBD154CB6B8B99"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",
	}
	ns "NETWORK"
	returns	"void"

native "0xA2A707979FE754DC"
	hash "0xA2A707979FE754DC"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "NETWORK"
	returns	"void"

native "0x13F1FCB111B820B0"
	hash "0x13F1FCB111B820B0"
	arguments {
		Any "p0",
	}
	ns "NETWORK"
	returns	"void"

native "0xA7C511FA1C5BDA38"
	hash "0xA7C511FA1C5BDA38"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "NETWORK"
	returns	"void"

native "0x9ECA15ADFE141431"
	hash "0x9ECA15ADFE141431"
	ns "NETWORK"
	returns	"Any"

native "0xF287F506767CC8A9"
	hash "0xF287F506767CC8A9"
	ns "NETWORK"
	returns	"Any"

native "0xA12D3A5A3753CC23"
	hash "0xA12D3A5A3753CC23"
	ns "NETWORK"
	returns	"Any"

native "0x641F272B52E2F0F8"
	hash "0x641F272B52E2F0F8"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "OBJECT"
	returns	"void"

native "0x1E3F1B1B891A2AAA"
	hash "0x1E3F1B1B891A2AAA"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "OBJECT"
	returns	"void"

native "0xDA05194260CDCDF9"
	hash "0xDA05194260CDCDF9"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "OBJECT"
	returns	"void"

native "0x758A5C1B3B1E1990"
	hash "0x758A5C1B3B1E1990"
	arguments {
		Any "p0",
	}
	ns "OBJECT"
	returns	"void"

native "0x394CD08E31313C28"
	hash "0x394CD08E31313C28"
	ns "OBJECT"
	returns	"void"

native "0xE84EB93729C5F36A"
	hash "0xE84EB93729C5F36A"
	arguments {
		Any "p0",
	}
	ns "OBJECT"
	returns	"Any"

native "0xB3ECA65C7317F174"
	hash "0xB3ECA65C7317F174"
	ns "OBJECT"
	returns	"Any"

native "0x867458251D47CCB2"
	hash "0x867458251D47CCB2"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "OBJECT"
	returns	"void"

native "0xD76EEEF746057FD6"
	hash "0xD76EEEF746057FD6"
	arguments {
		Any "p0",
	}
	ns "OBJECT"
	returns	"Any"

native "0x826D1EE4D1CAFC78"
	hash "0x826D1EE4D1CAFC78"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "OBJECT"
	returns	"void"

native "0x8CAAB2BD3EA58BD4"
	hash "0x8CAAB2BD3EA58BD4"
	arguments {
		Any "p0",
	}
	ns "OBJECT"
	returns	"void"

native "0x62454A641B41F3C5"
	hash "0x62454A641B41F3C5"
	arguments {
		Any "p0",
	}
	ns "OBJECT"
	returns	"void"

native "0x3B2FD68DB5F8331C"
	hash "0x3B2FD68DB5F8331C"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "OBJECT"
	returns	"void"

native "0x96EE0EBA0163DF80"
	hash "0x96EE0EBA0163DF80"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "OBJECT"
	returns	"void"

native "0xDF6CA0330F2E737B"
	hash "0xDF6CA0330F2E737B"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "OBJECT"
	returns	"void"

native "0x0596843B34B95CE5"
	hash "0x0596843B34B95CE5"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "OBJECT"
	returns	"void"

native "0x3ED2B83AB2E82799"
	hash "0x3ED2B83AB2E82799"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "OBJECT"
	returns	"void"

native "0x8881C98A31117998"
	hash "0x8881C98A31117998"
	arguments {
		Any "p0",
	}
	ns "OBJECT"
	returns	"void"

native "0x39A5FB7EAF150840"
	hash "0x39A5FB7EAF150840"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "OBJECT"
	returns	"void"

native "0xA90E7227A9303FA9"
	hash "0xA90E7227A9303FA9"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "OBJECT"
	returns	"void"

native "0x858EC9FD25DE04AA"
	hash "0x858EC9FD25DE04AA"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "OBJECT"
	returns	"void"

native "0x1C1B69FAE509BA97"
	hash "0x1C1B69FAE509BA97"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "OBJECT"
	returns	"void"

native "0x46F3ADD1E2D5BAF2"
	hash "0x46F3ADD1E2D5BAF2"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "OBJECT"
	returns	"void"

native "0xF12E33034D887F66"
	hash "0xF12E33034D887F66"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",

		Any "p4",

		Any "p5",
	}
	ns "OBJECT"
	returns	"Any"

native "0xFB6C4072E9A32E92"
	hash "0xFB6C4072E9A32E92"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "PAD"
	returns	"Any"

native "0xF3162836C28F9DA5"
	hash "0xF3162836C28F9DA5"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",
	}
	ns "PATHFIND"
	returns	"Any"

native "0xA0F8A7517A273C05"
	hash "0xA0F8A7517A273C05"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",

		Any "p4",
	}
	ns "PATHFIND"
	returns	"Any"

native "0x705A844002B39DC0"
	hash "0x705A844002B39DC0"
	ns "PATHFIND"
	returns	"Any"

native "0x2DFC81C9B9608549"
	hash "0x2DFC81C9B9608549"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "PED"
	returns	"Any"

native "0x668FD40BCBA5DE48"
	hash "0x668FD40BCBA5DE48"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",

		Any "p4",
	}
	ns "PED"
	returns	"Any"

native "0x148B08C2D2ACB884"
	hash "0x148B08C2D2ACB884"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",
	}
	ns "PED"
	returns	"void"

native "0xCC6E3B6BB69501F1"
	hash "0xCC6E3B6BB69501F1"
	arguments {
		Any "p0",
	}
	ns "PED"
	returns	"Any"

native "0x8A24B067D175A7BD"
	hash "0x8A24B067D175A7BD"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",

		Any "p4",

		Any "p5",
	}
	ns "PED"
	returns	"Any"

native "0x76BBA2CEE66D47E9"
	hash "0x76BBA2CEE66D47E9"
	arguments {
		Any "p0",
	}
	ns "PED"
	returns	"Any"

native "0x1461B28A06717D68"
	hash "0x1461B28A06717D68"
	arguments {
		Any "p0",
	}
	ns "PED"
	returns	"Any"

native "0x451D05012CCEC234"
	hash "0x451D05012CCEC234"
	arguments {
		Any "p0",
	}
	ns "PED"
	returns	"Any"

native "0xB9496CE47546DB2C"
	hash "0xB9496CE47546DB2C"
	arguments {
		Any "p0",
	}
	ns "PED"
	returns	"Any"

native "0x412F1364FA066CFB"
	hash "0x412F1364FA066CFB"
	arguments {
		Any "p0",
	}
	ns "PED"
	returns	"Any"

native "0x81AA517FBBA05D39"
	hash "0x81AA517FBBA05D39"
	arguments {
		Any "p0",
	}
	ns "PED"
	returns	"Any"

native "0x3795688A307E1EB6"
	hash "0x3795688A307E1EB6"
	arguments {
		Any "p0",
	}
	ns "PED"
	returns	"Any"

native "0xBA8805A1108A2515"
	hash "0xBA8805A1108A2515"
	arguments {
		Any "p0",
	}
	ns "PED"
	returns	"Any"

native "0x87DDEB611B329A9C"
	hash "0x87DDEB611B329A9C"
	arguments {
		Any "p0",
	}
	ns "PED"
	returns	"void"

native "0x2F074C904D85129E"
	hash "0x2F074C904D85129E"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",

		Any "p4",

		Any "p5",

		Any "p6",
	}
	ns "PED"
	returns	"void"

native "0x711794453CFD692B"
	hash "0x711794453CFD692B"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "PED"
	returns	"void"

native "0x820E9892A77E97CD"
	hash "0x820E9892A77E97CD"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "PED"
	returns	"void"

native "0x4E90D746056E273D"
	hash "0x4E90D746056E273D"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "PED"
	returns	"void"

native "0xE906EC930F5FE7C8"
	hash "0xE906EC930F5FE7C8"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "PED"
	returns	"void"

native "0x3F7325574E41B44D"
	hash "0x3F7325574E41B44D"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",
	}
	ns "PED"
	returns	"void"

native "0x0B3E35AC043707D9"
	hash "0x0B3E35AC043707D9"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "PED"
	returns	"void"

native "0x0F62619393661D6E"
	hash "0x0F62619393661D6E"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",
	}
	ns "PED"
	returns	"void"

native "0x3E9679C1DFCF422C"
	hash "0x3E9679C1DFCF422C"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "PED"
	returns	"void"

native "0x5615E0C5EB2BC6E2"
	hash "0x5615E0C5EB2BC6E2"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "PED"
	returns	"void"

native "0xB282749D5E028163"
	hash "0xB282749D5E028163"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "PED"
	returns	"void"

native "0x0C112765300C7E1E"
	hash "0x0C112765300C7E1E"
	arguments {
		Any "p0",
	}
	ns "PHYSICS"
	returns	"Any"

native "0x9EBD751E5787BAF2"
	hash "0x9EBD751E5787BAF2"
	arguments {
		Any "p0",
	}
	ns "PHYSICS"
	returns	"void"

native "0x15F944730C832252"
	hash "0x15F944730C832252"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "PHYSICS"
	returns	"void"

native "0x2F41A3BAE005E5FA"
	hash "0x2F41A3BAE005E5FA"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "PLAYER"
	returns	"void"

native "0xCB645E85E97EA48B"
	hash "0xCB645E85E97EA48B"
	ns "PLAYER"
	returns	"Any"

native "0x8BC515BAE4AAF8FF"
	hash "0x8BC515BAE4AAF8FF"
	arguments {
		Any "p0",
	}
	ns "PLAYER"
	returns	"Any"

native "0x6E4361FF3E8CD7CA"
	hash "0x6E4361FF3E8CD7CA"
	arguments {
		Any "p0",
	}
	ns "PLAYER"
	returns	"Any"

native "0x7E07C78925D5FD96"
	hash "0x7E07C78925D5FD96"
	arguments {
		Any "p0",
	}
	ns "PLAYER"
	returns	"Any"

native "0x2382AB11450AE7BA"
	hash "0x2382AB11450AE7BA"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "PLAYER"
	returns	"void"

native "0xFAC75988A7D078D3"
	hash "0xFAC75988A7D078D3"
	arguments {
		Any "p0",
	}
	ns "PLAYER"
	returns	"void"

native "0x55FCC0C390620314"
	hash "0x55FCC0C390620314"
	arguments {
		Player "player1",

		Player "player2",

		BOOL "toggle",
	}
	ns "PLAYER"
	returns	"void"
	doc [[!
<summary>
		Order of player1 and player2 are not interchangable, it was called for both orders.
</summary>
	]]

native "0xD821056B9ACF8052"
	hash "0xD821056B9ACF8052"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "PLAYER"
	returns	"void"

native "0xEFD79FA81DFBA9CB"
	hash "0xEFD79FA81DFBA9CB"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "PLAYER"
	returns	"void"

native "0xC388A0F065F5BC34"
	hash "0xC388A0F065F5BC34"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "PLAYER"
	returns	"void"

native "0x6BC97F4F4BB3C04B"
	hash "0x6BC97F4F4BB3C04B"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "PLAYER"
	returns	"void"

native "0x8D768602ADEF2245"
	hash "0x8D768602ADEF2245"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "PLAYER"
	returns	"void"

native "0xA0D3E4F7AAFB7E78"
	hash "0xA0D3E4F7AAFB7E78"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "PLAYER"
	returns	"Any"

native "0xBCFDE9EDE4CF27DC"
	hash "0xBCFDE9EDE4CF27DC"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "PLAYER"
	returns	"void"

native "0x31E90B8873A4CD3B"
	hash "0x31E90B8873A4CD3B"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "PLAYER"
	returns	"void"

native "0xB214D570EAD7F81A"
	hash "0xB214D570EAD7F81A"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "PLAYER"
	returns	"void"

native "0x821FDC827D6F4090"
	hash "0x821FDC827D6F4090"
	arguments {
		Any "p0",
	}
	ns "PLAYER"
	returns	"void"

native "0x17F7471EACA78290"
	hash "0x17F7471EACA78290"
	arguments {
		Any "p0",
	}
	ns "PLAYER"
	returns	"void"

native "0xA40CC53DF8E50837"
	hash "0xA40CC53DF8E50837"
	arguments {
		BOOL "p0",

		Any "args",

		int "argCount",

		int "bit",
	}
	ns "SCRIPT"
	returns	"void"

native "0x7DB18CA8CAD5B098"
	hash "0x7DB18CA8CAD5B098"
	ns "SOCIALCLUB"
	returns	"Any"

native "0x07DBD622D9533857"
	hash "0x07DBD622D9533857"
	arguments {
		Any "p0",
	}
	ns "SOCIALCLUB"
	returns	"Any"

native "0xEB2BF817463DFA28"
	hash "0xEB2BF817463DFA28"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "SOCIALCLUB"
	returns	"Any"

native "0x44919CC079BB60BF"
	hash "0x44919CC079BB60BF"
	arguments {
		Any "p0",
	}
	ns "STATS"
	returns	"void"

native "0xD1C9B92BDD3F151D"
	hash "0xD1C9B92BDD3F151D"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",
	}
	ns "STATS"
	returns	"void"

native "0xA3C53804BDB68ED2"
	hash "0xA3C53804BDB68ED2"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "STATS"
	returns	"void"

native "0x6BCCF9948492FD85"
	hash "0x6BCCF9948492FD85"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",

		Any "p4",
	}
	ns "STATS"
	returns	"void"

native "0x6BC0ACD0673ACEBE"
	hash "0x6BC0ACD0673ACEBE"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",
	}
	ns "STATS"
	returns	"void"

native "0x792271AB35C356A4"
	hash "0x792271AB35C356A4"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "STATS"
	returns	"void"

native "0xCEA553E35C2246E1"
	hash "0xCEA553E35C2246E1"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",
	}
	ns "STATS"
	returns	"void"

native "0xD558BEC0BBA7E8D2"
	hash "0xD558BEC0BBA7E8D2"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",

		Any "p4",
	}
	ns "STATS"
	returns	"void"

native "0x47B32F5611E6E483"
	hash "0x47B32F5611E6E483"
	arguments {
		Any "p0",
	}
	ns "STATS"
	returns	"void"

native "0x516FC96EB88EEFE5"
	hash "0x516FC96EB88EEFE5"
	arguments {
		Any "p0",
	}
	ns "STATS"
	returns	"void"

native "0x7B18DA61F6BAE9D5"
	hash "0x7B18DA61F6BAE9D5"
	arguments {
		Any "p0",
	}
	ns "STATS"
	returns	"void"

native "0x53CAE13E9B426993"
	hash "0x53CAE13E9B426993"
	arguments {
		Any "p0",
	}
	ns "STATS"
	returns	"void"

native "0xE3261D791EB44ACB"
	hash "0xE3261D791EB44ACB"
	arguments {
		Any "p0",
	}
	ns "STATS"
	returns	"void"

native "0x930F504203F561C9"
	hash "0x930F504203F561C9"
	arguments {
		Any "p0",
	}
	ns "STATS"
	returns	"void"

native "0x73001E34F85137F8"
	hash "0x73001E34F85137F8"
	arguments {
		Any "p0",
	}
	ns "STATS"
	returns	"void"

native "0x14EDA9EE27BD1626"
	hash "0x14EDA9EE27BD1626"
	arguments {
		Any "p0",
	}
	ns "STATS"
	returns	"void"

native "0x7D36291161859389"
	hash "0x7D36291161859389"
	arguments {
		Any "p0",
	}
	ns "STATS"
	returns	"void"

native "0x06EAF70AE066441E"
	hash "0x06EAF70AE066441E"
	arguments {
		Any "p0",
	}
	ns "STATS"
	returns	"void"

native "0xD6781E42755531F7"
	hash "0xD6781E42755531F7"
	arguments {
		Any "p0",
	}
	ns "STATS"
	returns	"void"

native "0xF06A6F41CB445443"
	hash "0xF06A6F41CB445443"
	arguments {
		Any "p0",
	}
	ns "STATS"
	returns	"void"

native "0x678F86D8FC040BDB"
	hash "0x678F86D8FC040BDB"
	arguments {
		Any "p0",
	}
	ns "STATS"
	returns	"void"

native "0xA6F54BB2FFCA35EA"
	hash "0xA6F54BB2FFCA35EA"
	arguments {
		Any "p0",
	}
	ns "STATS"
	returns	"void"

native "0x8D8ADB562F09A245"
	hash "0x8D8ADB562F09A245"
	arguments {
		Any "p0",
	}
	ns "STATS"
	returns	"void"

native "0x282B6739644F4347"
	hash "0x282B6739644F4347"
	arguments {
		Any "p0",
	}
	ns "STATS"
	returns	"void"

native "0x5FF2C33B13A02A11"
	hash "0x5FF2C33B13A02A11"
	arguments {
		Any "p0",
	}
	ns "STATS"
	returns	"void"

native "0x88087EE1F28024AE"
	hash "0x88087EE1F28024AE"
	arguments {
		Any "p0",
	}
	ns "STATS"
	returns	"void"

native "0xFCC228E07217FCAC"
	hash "0xFCC228E07217FCAC"
	arguments {
		Any "p0",
	}
	ns "STATS"
	returns	"void"

native "0xD1A1EE3B4FA8E760"
	hash "0xD1A1EE3B4FA8E760"
	arguments {
		Any "p0",
	}
	ns "STATS"
	returns	"void"

native "0x28ECB8AC2F607DB2"
	hash "0x28ECB8AC2F607DB2"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",

		Any "p4",
	}
	ns "STATS"
	returns	"void"

native "0x0B565B0AAE56A0E8"
	hash "0x0B565B0AAE56A0E8"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",

		Any "p4",

		Any "p5",

		Any "p6",
	}
	ns "STATS"
	returns	"void"

native "0xB7257BA2550EA10A"
	hash "0xB7257BA2550EA10A"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",

		Any "p4",

		Any "p5",

		Any "p6",
	}
	ns "STATS"
	returns	"void"

native "0x2605663BD4F23B5D"
	hash "0x2605663BD4F23B5D"
	arguments {
		Any "p0",
	}
	ns "STATS"
	returns	"void"

native "0x848B66100EE33B05"
	hash "0x848B66100EE33B05"
	arguments {
		Any "p0",
	}
	ns "STATS"
	returns	"void"

native "0x501478855A6074CE"
	hash "0x501478855A6074CE"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",

		Any "p4",

		Any "p5",
	}
	ns "STATS"
	returns	"void"

native "0xBAA2F0490E146BE8"
	hash "0xBAA2F0490E146BE8"
	arguments {
		Any "p0",
	}
	ns "STATS"
	returns	"void"

native "0x6A60E43998228229"
	hash "0x6A60E43998228229"
	arguments {
		Any "p0",
	}
	ns "STATS"
	returns	"void"

native "0x3DE3AA516FB126A4"
	hash "0x3DE3AA516FB126A4"
	arguments {
		Any "p0",
	}
	ns "STATS"
	returns	"void"

native "0x6551B1F7F6CD46EA"
	hash "0x6551B1F7F6CD46EA"
	arguments {
		Any "p0",
	}
	ns "STATS"
	returns	"void"

native "0xEDBF6C9B0D2C65C8"
	hash "0xEDBF6C9B0D2C65C8"
	arguments {
		Any "p0",
	}
	ns "STATS"
	returns	"void"

native "0x0A9C7F36E5D7B683"
	hash "0x0A9C7F36E5D7B683"
	arguments {
		Any "p0",
	}
	ns "STATS"
	returns	"void"

native "0x84DFC579C2FC214C"
	hash "0x84DFC579C2FC214C"
	arguments {
		Any "p0",
	}
	ns "STATS"
	returns	"void"

native "0x2CD90358F67D0AA8"
	hash "0x2CD90358F67D0AA8"
	arguments {
		Any "p0",
	}
	ns "STATS"
	returns	"void"

native "0x164C5FF663790845"
	hash "0x164C5FF663790845"
	arguments {
		Any "p0",
	}
	ns "STATS"
	returns	"void"

native "0x419615486BBF1956"
	hash "0x419615486BBF1956"
	arguments {
		Any "p0",
	}
	ns "STATS"
	returns	"void"

native "0x1A7CE7CD3E653485"
	hash "0x1A7CE7CD3E653485"
	arguments {
		Any "p0",
	}
	ns "STATS"
	returns	"void"

native "0x8C9D11605E59D955"
	hash "0x8C9D11605E59D955"
	arguments {
		Any "p0",
	}
	ns "STATS"
	returns	"void"

native "0xBFAFDB5FAAA5C5AB"
	hash "0xBFAFDB5FAAA5C5AB"
	arguments {
		Any "p0",
	}
	ns "STATS"
	returns	"void"

native "0x0EACDF8487D5155A"
	hash "0x0EACDF8487D5155A"
	arguments {
		Any "p0",
	}
	ns "STATS"
	returns	"void"

native "0xDAF80797FC534BEC"
	hash "0xDAF80797FC534BEC"
	arguments {
		Any "p0",
	}
	ns "STATS"
	returns	"void"

native "0x60EEDC12AF66E846"
	hash "0x60EEDC12AF66E846"
	arguments {
		Any "p0",
	}
	ns "STATS"
	returns	"void"

native "0x7D8BA05688AD64C7"
	hash "0x7D8BA05688AD64C7"
	arguments {
		Any "p0",
	}
	ns "STATS"
	returns	"void"

native "0x8989CBD7B4E82534"
	hash "0x8989CBD7B4E82534"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",

		Any "p4",

		Any "p5",

		Any "p6",
	}
	ns "STATS"
	returns	"void"

native "0x03C2EEBB04B3FB72"
	hash "0x03C2EEBB04B3FB72"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",

		Any "p4",

		Any "p5",

		Any "p6",
	}
	ns "STATS"
	returns	"void"

native "0xF534D94DFA2EAD26"
	hash "0xF534D94DFA2EAD26"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",

		Any "p4",
	}
	ns "STATS"
	returns	"void"

native "0xCC25A4553DFBF9EA"
	hash "0xCC25A4553DFBF9EA"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",

		Any "p4",
	}
	ns "STATS"
	returns	"void"

native "0xBF371CD2B64212FD"
	hash "0xBF371CD2B64212FD"
	arguments {
		Any "p0",
	}
	ns "STATS"
	returns	"void"

native "0x203B381133817079"
	hash "0x203B381133817079"
	arguments {
		Any "p0",
	}
	ns "STATS"
	returns	"void"

native "0x04D90BA8207ADA2D"
	hash "0x04D90BA8207ADA2D"
	arguments {
		Any "p0",
	}
	ns "STATS"
	returns	"void"

native "0x27AA1C973CACFE63"
	hash "0x27AA1C973CACFE63"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",

		Any "p4",

		Any "p5",

		Any "p6",

		Any "p7",

		Any "p8",

		Any "p9",
	}
	ns "STATS"
	returns	"void"

native "0xC729991A9065376E"
	hash "0xC729991A9065376E"
	arguments {
		Any "p0",
	}
	ns "STATS"
	returns	"void"

native "0xBE509B0A3693DE8B"
	hash "0xBE509B0A3693DE8B"
	arguments {
		Any "p0",
	}
	ns "STATS"
	returns	"void"

native "0x928DBFB892638EF3"
	hash "0x928DBFB892638EF3"
	ns "STATS"
	returns	"void"

native "0x8A800DACCC0DA55D"
	hash "0x8A800DACCC0DA55D"
	ns "STATS"
	returns	"void"

native "0x0A50D2604E05CB94"
	hash "0x0A50D2604E05CB94"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",

		Any "p4",
	}
	ns "STATS"
	returns	"void"

native "0x7033EEFD9B28088E"
	hash "0x7033EEFD9B28088E"
	arguments {
		Any "p0",
	}
	ns "STATS"
	returns	"void"

native "0x015B03EE1C43E6EC"
	hash "0x015B03EE1C43E6EC"
	arguments {
		Any "p0",
	}
	ns "STATS"
	returns	"void"

native "0xAA525DFF66BB82F5"
	hash "0xAA525DFF66BB82F5"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",
	}
	ns "STATS"
	returns	"void"

native "0x3EBEAC6C3F81F6BD"
	hash "0x3EBEAC6C3F81F6BD"
	arguments {
		Any "p0",
	}
	ns "STATS"
	returns	"void"

native "0x96E6D5150DBF1C09"
	hash "0x96E6D5150DBF1C09"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",
	}
	ns "STATS"
	returns	"void"

native "0x6E0A5253375C4584"
	hash "0x6E0A5253375C4584"
	ns "STATS"
	returns	"Any"

native "0xBA9749CC94C1FD85"
	hash "0xBA9749CC94C1FD85"
	ns "STATS"
	returns	"Any"

native "0x32CAC93C9DE73D32"
	hash "0x32CAC93C9DE73D32"
	ns "STATS"
	returns	"Any"

native "0x84A810B375E69C0E"
	hash "0x84A810B375E69C0E"
	ns "STATS"
	returns	"Any"

native "0x9EC8858184CD253A"
	hash "0x9EC8858184CD253A"
	ns "STATS"
	returns	"Any"

native "0xE8853FBCE7D8D0D6"
	hash "0xE8853FBCE7D8D0D6"
	ns "STATS"
	returns	"Any"

native "0xA943FD1722E11EFD"
	hash "0xA943FD1722E11EFD"
	ns "STATS"
	returns	"Any"

native "0xAFF47709F1D5DCCE"
	hash "0xAFF47709F1D5DCCE"
	ns "STATS"
	returns	"Any"

native "0x55A8BECAF28A4EB7"
	hash "0x55A8BECAF28A4EB7"
	ns "STATS"
	returns	"Any"

native "0x1A8EA222F9C67DBB"
	hash "0x1A8EA222F9C67DBB"
	arguments {
		Any "p0",
	}
	ns "STATS"
	returns	"Any"

native "0xF11F01D98113536A"
	hash "0xF11F01D98113536A"
	arguments {
		Any "p0",
	}
	ns "STATS"
	returns	"Any"

native "0x0B8B7F74BF061C6D"
	hash "0x0B8B7F74BF061C6D"
	ns "STATS"
	returns	"Any"

native "0xF9F2922717B819EC"
	hash "0xF9F2922717B819EC"
	ns "STATS"
	returns	"Any"

native "0x8B9CDBD6C566C38C"
	hash "0x8B9CDBD6C566C38C"
	ns "STATS"
	returns	"Any"

native "0xBE3DB208333D9844"
	hash "0xBE3DB208333D9844"
	ns "STATS"
	returns	"Any"

native "0x33D72899E24C3365"
	hash "0x33D72899E24C3365"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "STATS"
	returns	"Any"

native "0xA761D4AC6115623D"
	hash "0xA761D4AC6115623D"
	ns "STATS"
	returns	"Any"

native "_IS_MODEL_A_PED"
	hash "0x75816577FEA6DAD5"
	arguments {
		Hash "modelHash",
	}
	ns "STREAMING"
	returns	"BOOL"

native "0x0419B167EE128F33"
	hash "0x0419B167EE128F33"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "VEHICLE"
	returns	"Any"

native "0xF3B0E0AED097A3F5"
	hash "0xF3B0E0AED097A3F5"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "VEHICLE"
	returns	"Any"

native "0xD3E51C0AB8C26EEE"
	hash "0xD3E51C0AB8C26EEE"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "VEHICLE"
	returns	"Any"

native "0xA1DD82F3CCF9A01E"
	hash "0xA1DD82F3CCF9A01E"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",

		Any "p4",

		Any "p5",
	}
	ns "VEHICLE"
	returns	"void"

native "0x2467A2D807D37CA3"
	hash "0x2467A2D807D37CA3"
	arguments {
		Any "p0",
	}
	ns "VEHICLE"
	returns	"Any"

native "0x2C1D8B3B19E517CC"
	hash "0x2C1D8B3B19E517CC"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "VEHICLE"
	returns	"Any"

native "0x4419966C9936071A"
	hash "0x4419966C9936071A"
	arguments {
		Any "p0",
	}
	ns "VEHICLE"
	returns	"void"

native "0xAA3F739ABDDCF21F"
	hash "0xAA3F739ABDDCF21F"
	ns "VEHICLE"
	returns	"void"

native "0xAF03011701811146"
	hash "0xAF03011701811146"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "VEHICLE"
	returns	"Any"

native "0x32CAEDF24A583345"
	hash "0x32CAEDF24A583345"
	arguments {
		Any "p0",
	}
	ns "VEHICLE"
	returns	"void"

native "0x4E417C547182C84D"
	hash "0x4E417C547182C84D"
	arguments {
		Any "p0",
	}
	ns "VEHICLE"
	returns	"Any"

native "_GET_ALL_VEHICLES"
	hash "0x9B8E1BF04B51F2E8"
	arguments {
		AnyPtr "vehArray",
	}
	ns "VEHICLE"
	returns	"int"

native "0xEC69ADF931AAE0C3"
	hash "0xEC69ADF931AAE0C3"
	arguments {
		Any "p0",
	}
	ns "VEHICLE"
	returns	"Any"

native "_HAS_VEHICLE_JUMPING_ABILITY"
	hash "0x9078C0C5EF8C19E9"
	arguments {
		Vehicle "vehicle",
	}
	ns "VEHICLE"
	returns	"BOOL"

native "0x99093F60746708CA"
	hash "0x99093F60746708CA"
	arguments {
		Any "p0",
	}
	ns "VEHICLE"
	returns	"Any"

native "_GET_ENTRY_POSITION_OF_DOOR"
	hash "0xC0572928C0ABFDA3"
	arguments {
		Vehicle "vehicle",

		int "doorIndex",
	}
	ns "VEHICLE"
	returns	"Vector3"

native "_GET_HAS_LOWERABLE_WHEELS"
	hash "0xDCA174A42133F08C"
	arguments {
		Vehicle "vehicle",
	}
	ns "VEHICLE"
	returns	"BOOL"

native "_HAS_VEHICLE_ROCKET_BOOST"
	hash "0x36D782F68B309BDA"
	arguments {
		Vehicle "vehicle",
	}
	ns "VEHICLE"
	returns	"BOOL"

native "0xBA91D045575699AD"
	hash "0xBA91D045575699AD"
	arguments {
		Any "p0",
	}
	ns "VEHICLE"
	returns	"Any"

native "_DOES_VEHICLE_HAVE_DOOR"
	hash "0x645F4B6E8499F632"
	arguments {
		Vehicle "vehicle",

		int "doorIndex",
	}
	ns "VEHICLE"
	returns	"BOOL"

native "0x1DA0DA9CB3F0C8BF"
	hash "0x1DA0DA9CB3F0C8BF"
	arguments {
		Any "p0",
	}
	ns "VEHICLE"
	returns	"Any"

native "_GET_NUMBER_OF_VEHICLE_DOORS"
	hash "0x92922A607497B14D"
	arguments {
		Vehicle "vehicle",
	}
	ns "VEHICLE"
	returns	"Any"

native "_CAN_VEHICLE_PARACHUTE_BE_ACTIVATED"
	hash "0xA916396DF4154EE3"
	arguments {
		Vehicle "vehicle",
	}
	ns "VEHICLE"
	returns	"BOOL"

native "_GET_VEHICLE_INTERIOR_COLOUR"
	hash "0x7D1464D472D32136"
	arguments {
		Vehicle "vehicle",

		intPtr "color",
	}
	ns "VEHICLE"
	returns	"void"

native "_GET_VEHICLE_DASHBOARD_COLOUR"
	hash "0xB7635E80A5C31BFF"
	arguments {
		Vehicle "vehicle",

		intPtr "color",
	}
	ns "VEHICLE"
	returns	"void"

native "0x50634E348C8D44EF"
	hash "0x50634E348C8D44EF"
	arguments {
		Any "p0",
	}
	ns "VEHICLE"
	returns	"Any"

native "_HAS_VEHICLE_PARACHUTE"
	hash "0xBC9CFF381338CB4F"
	arguments {
		Vehicle "vehicle",
	}
	ns "VEHICLE"
	returns	"BOOL"

native "0x6EAAEFC76ACC311F"
	hash "0x6EAAEFC76ACC311F"
	arguments {
		Any "p0",
	}
	ns "VEHICLE"
	returns	"Any"

native "0xCA4AC3EAAE46EC7B"
	hash "0xCA4AC3EAAE46EC7B"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "VEHICLE"
	returns	"Any"

native "0x8533CAFDE1F0F336"
	hash "0x8533CAFDE1F0F336"
	arguments {
		Any "p0",
	}
	ns "VEHICLE"
	returns	"Any"

native "0x60190048C0764A26"
	hash "0x60190048C0764A26"
	arguments {
		Any "p0",
	}
	ns "VEHICLE"
	returns	"Any"

native "0x5ECB40269053C0D4"
	hash "0x5ECB40269053C0D4"
	arguments {
		Any "p0",
	}
	ns "VEHICLE"
	returns	"Any"

native "0x5873C14A52D74236"
	hash "0x5873C14A52D74236"
	arguments {
		Any "p0",
	}
	ns "VEHICLE"
	returns	"Any"

native "0x8181CE2F25CB9BB7"
	hash "0x8181CE2F25CB9BB7"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "VEHICLE"
	returns	"Any"

native "0x5BA68A0840D546AC"
	hash "0x5BA68A0840D546AC"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "VEHICLE"
	returns	"Any"

native "0xB0AD1238A709B1A2"
	hash "0xB0AD1238A709B1A2"
	arguments {
		Any "p0",
	}
	ns "VEHICLE"
	returns	"Any"

native "0xB09D25E77C33EB3F"
	hash "0xB09D25E77C33EB3F"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",
	}
	ns "VEHICLE"
	returns	"Any"

native "_IS_VEHICLE_ROCKET_BOOST_ACTIVE"
	hash "0x3D34E80EED4AE3BE"
	arguments {
		Vehicle "vehicle",
	}
	ns "VEHICLE"
	returns	"BOOL"

native "_IS_THIS_MODEL_AN_AMPHIBIOUS_CAR"
	hash "0x633F6F44A537EBB6"
	arguments {
		Hash "model",
	}
	ns "VEHICLE"
	returns	"BOOL"

native "0xA1A9FC1C76A6730D"
	hash "0xA1A9FC1C76A6730D"
	arguments {
		Any "p0",
	}
	ns "VEHICLE"
	returns	"Any"

native "0x3DE51E9C80B116CF"
	hash "0x3DE51E9C80B116CF"
	arguments {
		Any "p0",
	}
	ns "VEHICLE"
	returns	"Any"

native "0x48C633E94A8142A7"
	hash "0x48C633E94A8142A7"
	arguments {
		Any "p0",
	}
	ns "VEHICLE"
	returns	"Any"

native "0x563B65A643ED072E"
	hash "0x563B65A643ED072E"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",
	}
	ns "VEHICLE"
	returns	"Any"

native "0x5EE5632F47AE9695"
	hash "0x5EE5632F47AE9695"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "VEHICLE"
	returns	"void"

native "0xE2F53F172B45EDE1"
	hash "0xE2F53F172B45EDE1"
	ns "VEHICLE"
	returns	"void"

native "0x80E3357FDEF45C21"
	hash "0x80E3357FDEF45C21"
	arguments {
		Vehicle "vehicle",

		BOOL "toggle",
	}
	ns "VEHICLE"
	returns	"void"

native "0x73561D4425A021A2"
	hash "0x73561D4425A021A2"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "VEHICLE"
	returns	"void"

native "0xED5EDE9E676643C9"
	hash "0xED5EDE9E676643C9"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "VEHICLE"
	returns	"void"

native "0xBD32E46AA95C1DD2"
	hash "0xBD32E46AA95C1DD2"
	arguments {
		Any "p0",
	}
	ns "VEHICLE"
	returns	"void"

native "0x1201E8A3290A3B98"
	hash "0x1201E8A3290A3B98"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "VEHICLE"
	returns	"void"

native "0x1F34B0626C594380"
	hash "0x1F34B0626C594380"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "VEHICLE"
	returns	"void"

native "0x72BECCF4B829522E"
	hash "0x72BECCF4B829522E"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "VEHICLE"
	returns	"void"

native "0x0D5F65A8F4EBDAB5"
	hash "0x0D5F65A8F4EBDAB5"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "VEHICLE"
	returns	"void"

native "0xB9562064627FF9DB"
	hash "0xB9562064627FF9DB"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "VEHICLE"
	returns	"void"

native "0xFC40CBF7B90CA77C"
	hash "0xFC40CBF7B90CA77C"
	arguments {
		Vehicle "vehicle",
	}
	ns "VEHICLE"
	returns	"void"

native "0xB2E0C0D6922D31F2"
	hash "0xB2E0C0D6922D31F2"
	arguments {
		Vehicle "vehicle",

		BOOL "toggle",
	}
	ns "VEHICLE"
	returns	"void"

native "0x26D99D5A82FD18E8"
	hash "0x26D99D5A82FD18E8"
	arguments {
		Any "p0",
	}
	ns "VEHICLE"
	returns	"void"

native "0xD4196117AF7BB974"
	hash "0xD4196117AF7BB974"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "VEHICLE"
	returns	"Any"

native "0xEDBC8405B3895CC9"
	hash "0xEDBC8405B3895CC9"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "VEHICLE"
	returns	"void"

native "0x1087BC8EC540DAEB"
	hash "0x1087BC8EC540DAEB"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "VEHICLE"
	returns	"void"

native "0xA247F9EF01D8082E"
	hash "0xA247F9EF01D8082E"
	arguments {
		Any "p0",
	}
	ns "VEHICLE"
	returns	"void"

native "0x428AD3E26C8D9EB0"
	hash "0x428AD3E26C8D9EB0"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",

		Any "p4",
	}
	ns "VEHICLE"
	returns	"void"

native "0x544996C0081ABDEB"
	hash "0x544996C0081ABDEB"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "VEHICLE"
	returns	"void"

native "0xAB31EF4DE6800CE9"
	hash "0xAB31EF4DE6800CE9"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "VEHICLE"
	returns	"void"

native "0x4056EA1105F5ABD7"
	hash "0x4056EA1105F5ABD7"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "VEHICLE"
	returns	"void"

native "0xFE205F38AAA58E5B"
	hash "0xFE205F38AAA58E5B"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "VEHICLE"
	returns	"void"

native "0x28B18377EB6E25F6"
	hash "0x28B18377EB6E25F6"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "VEHICLE"
	returns	"void"

native "0x84EA99C62CB3EF0C"
	hash "0x84EA99C62CB3EF0C"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",
	}
	ns "VEHICLE"
	returns	"void"

native "0x8EA86DF356801C7D"
	hash "0x8EA86DF356801C7D"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "VEHICLE"
	returns	"void"

native "0xC24075310A8B9CD1"
	hash "0xC24075310A8B9CD1"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",

		Any "p4",
	}
	ns "VEHICLE"
	returns	"void"

native "0x5B91B229243351A8"
	hash "0x5B91B229243351A8"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "VEHICLE"
	returns	"void"

native "0x1B212B26DD3C04DF"
	hash "0x1B212B26DD3C04DF"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "VEHICLE"
	returns	"void"

native "0x66E3AAFACE2D1EB8"
	hash "0x66E3AAFACE2D1EB8"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",
	}
	ns "VEHICLE"
	returns	"void"

native "0xC0ED6438E6D39BA8"
	hash "0xC0ED6438E6D39BA8"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",
	}
	ns "VEHICLE"
	returns	"void"

native "0x2A86A0475B6A1434"
	hash "0x2A86A0475B6A1434"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "VEHICLE"
	returns	"void"

native "0x4C815EB175086F84"
	hash "0x4C815EB175086F84"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "VEHICLE"
	returns	"Any"

native "_SET_VEHICLE_ROCKET_BOOST_ACTIVE"
	hash "0x81E1552E35DC3839"
	arguments {
		Vehicle "vehicle",

		BOOL "active",
	}
	ns "VEHICLE"
	returns	"void"

native "_SET_VEHICLE_ROCKET_BOOST_PERCENTAGE"
	hash "0xFEB2DDED3509562E"
	arguments {
		Vehicle "vehicle",

		float "percentage",
	}
	ns "VEHICLE"
	returns	"void"

native "0xEFC13B1CE30D755D"
	hash "0xEFC13B1CE30D755D"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "VEHICLE"
	returns	"void"

native "_SET_VEHICLE_ROCKET_BOOST_REFILL_TIME"
	hash "0xE00F2AB100B76E89"
	arguments {
		Vehicle "vehicle",

		float "time",
	}
	ns "VEHICLE"
	returns	"void"

native "0x78CEEE41F49F421F"
	hash "0x78CEEE41F49F421F"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "VEHICLE"
	returns	"void"

native "0x65B080555EA48149"
	hash "0x65B080555EA48149"
	arguments {
		Any "p0",
	}
	ns "VEHICLE"
	returns	"void"

native "0x7BBE7FF626A591FE"
	hash "0x7BBE7FF626A591FE"
	arguments {
		Any "p0",
	}
	ns "VEHICLE"
	returns	"void"

native "0x2FA2494B47FDD009"
	hash "0x2FA2494B47FDD009"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "VEHICLE"
	returns	"void"

native "0x878C75C09FBDB942"
	hash "0x878C75C09FBDB942"
	ns "VEHICLE"
	returns	"Any"

native "0xF06A16CA55D138D8"
	hash "0xF06A16CA55D138D8"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "VEHICLE"
	returns	"void"

native "0x1DDA078D12879EEE"
	hash "0x1DDA078D12879EEE"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",
	}
	ns "VEHICLE"
	returns	"void"

native "0xD3301660A57C9272"
	hash "0xD3301660A57C9272"
	arguments {
		Any "p0",
	}
	ns "VEHICLE"
	returns	"void"

native "0x870B8B7A766615C8"
	hash "0x870B8B7A766615C8"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",
	}
	ns "VEHICLE"
	returns	"void"

native "0xE4E2FD323574965C"
	hash "0xE4E2FD323574965C"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "VEHICLE"
	returns	"void"

native "0x065D03A9D6B2C6B5"
	hash "0x065D03A9D6B2C6B5"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "VEHICLE"
	returns	"void"

native "0x4E20D2A627011E8E"
	hash "0x4E20D2A627011E8E"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "VEHICLE"
	returns	"Any"

native "0xAF60E6A2936F982A"
	hash "0xAF60E6A2936F982A"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "VEHICLE"
	returns	"void"

native "0x76D26A22750E849E"
	hash "0x76D26A22750E849E"
	arguments {
		Any "p0",
	}
	ns "VEHICLE"
	returns	"void"

native "0x3B458DDB57038F08"
	hash "0x3B458DDB57038F08"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",
	}
	ns "VEHICLE"
	returns	"void"

native "0xD565F438137F0E10"
	hash "0xD565F438137F0E10"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "VEHICLE"
	returns	"void"

native "_SET_VEHICLE_INTERIOR_COLOUR"
	hash "0xF40DD601A65F7F19"
	arguments {
		Vehicle "vehicle",

		int "color",
	}
	ns "VEHICLE"
	returns	"void"

native "_SET_VEHICLE_DASHBOARD_COLOUR"
	hash "0x6089CDF6A57F326C"
	arguments {
		Vehicle "vehicle",

		int "color",
	}
	ns "VEHICLE"
	returns	"void"

native "0x6501129C9E0FFA05"
	hash "0x6501129C9E0FFA05"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "VEHICLE"
	returns	"void"

native "0xA6D3A8750DC73270"
	hash "0xA6D3A8750DC73270"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "VEHICLE"
	returns	"void"

native "0xBAA045B4E42F3C06"
	hash "0xBAA045B4E42F3C06"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "VEHICLE"
	returns	"void"

native "0x35BB21DE06784373"
	hash "0x35BB21DE06784373"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "VEHICLE"
	returns	"void"

native "0xCF9159024555488C"
	hash "0xCF9159024555488C"
	arguments {
		Any "p0",
	}
	ns "VEHICLE"
	returns	"void"

native "0xC4B3347BD68BD609"
	hash "0xC4B3347BD68BD609"
	arguments {
		Any "p0",
	}
	ns "VEHICLE"
	returns	"void"

native "0xF051D9BFB6BA39C0"
	hash "0xF051D9BFB6BA39C0"
	arguments {
		Any "p0",
	}
	ns "VEHICLE"
	returns	"void"

native "0xDCE97BDF8A0EABC8"
	hash "0xDCE97BDF8A0EABC8"
	ns "VEHICLE"
	returns	"Any"

native "0x737E398138550FFF"
	hash "0x737E398138550FFF"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "VEHICLE"
	returns	"void"

native "0x0581730AB9380412"
	hash "0x0581730AB9380412"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",

		Any "p4",

		Any "p5",
	}
	ns "VEHICLE"
	returns	"void"

native "0xBB2333BB87DDD87F"
	hash "0xBB2333BB87DDD87F"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "VEHICLE"
	returns	"void"

native "0x41290B40FA63E6DA"
	hash "0x41290B40FA63E6DA"
	arguments {
		Any "p0",
	}
	ns "VEHICLE"
	returns	"void"

native "0x86B4B6212CB8B627"
	hash "0x86B4B6212CB8B627"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "VEHICLE"
	returns	"void"

native "0x45A561A9421AB6AD"
	hash "0x45A561A9421AB6AD"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "VEHICLE"
	returns	"Any"

native "0x44CD1F493DB2A0A6"
	hash "0x44CD1F493DB2A0A6"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",
	}
	ns "VEHICLE"
	returns	"void"

native "0x1312DDD8385AEE4E"
	hash "0x1312DDD8385AEE4E"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "VEHICLE"
	returns	"void"

native "_RAISE_LOWERABLE_WHEELS"
	hash "0xF660602546D27BA8"
	arguments {
		Vehicle "vehicle",
	}
	ns "VEHICLE"
	returns	"void"

native "0x5335BE58C083E74E"
	hash "0x5335BE58C083E74E"
	arguments {
		Any "p0",
	}
	ns "VEHICLE"
	returns	"void"

native "0x7C06330BFDDA182E"
	hash "0x7C06330BFDDA182E"
	arguments {
		Any "p0",
	}
	ns "VEHICLE"
	returns	"void"

native "0x83F813570FF519DE"
	hash "0x83F813570FF519DE"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "VEHICLE"
	returns	"void"

native "0x756AE6E962168A04"
	hash "0x756AE6E962168A04"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "VEHICLE"
	returns	"void"

native "0x1BBAC99C0BC53656"
	hash "0x1BBAC99C0BC53656"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "VEHICLE"
	returns	"void"

native "0x9D30687C57BAA0BB"
	hash "0x9D30687C57BAA0BB"
	arguments {
		Any "p0",
	}
	ns "VEHICLE"
	returns	"void"

native "_VEHICLE_SET_CUSTOM_PARACHUTE_MODEL"
	hash "0x4D610C6B56031351"
	arguments {
		Vehicle "vehicle",

		Hash "parachuteModel",
	}
	ns "VEHICLE"
	returns	"void"
	doc [[!
<summary>
		parachuteModel = 230075693
</summary>
	]]

native "_VEHICLE_SET_CUSTOM_PARACHUTE_TEXTURE"
	hash "0xA74AD2439468C883"
	arguments {
		Vehicle "vehicle",

		int "colorIndex",
	}
	ns "VEHICLE"
	returns	"void"
	doc [[!
<summary>
		colorIndex = 0 
</summary>
	]]

native "_SET_RAMP_VEHICLE_RECEIVES_RAMP_DAMAGE"
	hash "0x28D034A93FE31BF5"
	arguments {
		Vehicle "vehicle",

		BOOL "receivesDamage",
	}
	ns "VEHICLE"
	returns	"void"

native "_SET_VEHICLE_PARACHUTE_ACTIVE"
	hash "0x0BFFB028B3DD0A97"
	arguments {
		Vehicle "vehicle",

		BOOL "active",
	}
	ns "VEHICLE"
	returns	"void"

native "0x547237AA71AB44DE"
	hash "0x547237AA71AB44DE"
	arguments {
		Any "p0",
	}
	ns "WATER"
	returns	"void"

native "0x2472622CE1F2D45F"
	hash "0x2472622CE1F2D45F"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",
	}
	ns "WEAPON"
	returns	"void"

native "0x9DA58CDBF6BDBC08"
	hash "0x9DA58CDBF6BDBC08"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",

		Any "p4",

		Any "p5",

		Any "p6",

		Any "p7",

		Any "p8",

		Any "p9",

		Any "p10",
	}
	ns "WEAPON"
	returns	"Any"

native "0x91EF34584710BE99"
	hash "0x91EF34584710BE99"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",

		Any "p4",

		Any "p5",

		Any "p6",

		Any "p7",
	}
	ns "WEAPON"
	returns	"Any"

native "0xCD79A550999D7D4F"
	hash "0xCD79A550999D7D4F"
	arguments {
		Any "p0",
	}
	ns "WEAPON"
	returns	"Any"

native "0x44F1012B69313374"
	hash "0x44F1012B69313374"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",
	}
	ns "WEAPON"
	returns	"void"

native "0x585847C5E4E11709"
	hash "0x585847C5E4E11709"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",
	}
	ns "WEAPON"
	returns	"Any"

native "_GET_PED_AMMO_TYPE_FROM_WEAPON_2"
	hash "0xF489B44DD5AF4BD9"
	arguments {
		Ped "ped",

		Hash "weaponHash",
	}
	ns "WEAPON"
	returns	"Hash"

native "0xA2C9AC24B4061285"
	hash "0xA2C9AC24B4061285"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "WEAPON"
	returns	"Any"

native "0xF0A60040BE558F2D"
	hash "0xF0A60040BE558F2D"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",
	}
	ns "WEAPON"
	returns	"Any"

native "0x6558AC7C17BFEF58"
	hash "0x6558AC7C17BFEF58"
	arguments {
		Any "p0",
	}
	ns "WEAPON"
	returns	"Any"

native "0x4D1CB8DC40208A17"
	hash "0x4D1CB8DC40208A17"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "WEAPON"
	returns	"Any"

native "0xB3EA4FEABF41464B"
	hash "0xB3EA4FEABF41464B"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "WEAPON"
	returns	"Any"

native "0x68F8BE6AF5CDF8A6"
	hash "0x68F8BE6AF5CDF8A6"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "WEAPON"
	returns	"void"

native "0xDAB963831DBFD3F4"
	hash "0xDAB963831DBFD3F4"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",

		Any "p4",
	}
	ns "WEAPON"
	returns	"Any"

native "0x0ABF535877897560"
	hash "0x0ABF535877897560"
	arguments {
		Any "p0",
	}
	ns "WEAPON"
	returns	"Any"

native "0x1E45B34ADEBEE48E"
	hash "0x1E45B34ADEBEE48E"
	ns "WEAPON"
	returns	"void"

native "0xEFF296097FF1E509"
	hash "0xEFF296097FF1E509"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "WEAPON"
	returns	"void"

native "0xB4771B9AAF4E68E4"
	hash "0xB4771B9AAF4E68E4"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",
	}
	ns "WEAPON"
	returns	"void"

native "0x9FE5633880ECD8ED"
	hash "0x9FE5633880ECD8ED"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",

		Any "p3",
	}
	ns "WEAPON"
	returns	"void"

native "0xECDC202B25E5CF48"
	hash "0xECDC202B25E5CF48"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",
	}
	ns "WEAPON"
	returns	"void"

native "0x4757F00BC6323CFE"
	hash "0x4757F00BC6323CFE"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "WEAPON"
	returns	"void"

native "0x977CA98939E82E4B"
	hash "0x977CA98939E82E4B"
	arguments {
		Any "p0",

		Any "p1",
	}
	ns "WEAPON"
	returns	"void"

native "0x5DA825A85D0EA6E6"
	hash "0x5DA825A85D0EA6E6"
	arguments {
		Any "p0",

		Any "p1",

		Any "p2",
	}
	ns "WEAPON"
	returns	"void"
