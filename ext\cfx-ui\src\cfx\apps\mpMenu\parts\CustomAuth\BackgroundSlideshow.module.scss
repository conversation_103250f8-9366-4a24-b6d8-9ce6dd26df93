.slideshow {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 5; // Above everything but below auth form (z-index: 10)
  overflow: hidden;

  // Prevent any flashing or jumping
  background-color: #1a1a1a; // Dark fallback color

  // Smooth container for crossfade
  isolation: isolate;
}

.backgroundImage {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;

  // Ensure high quality image rendering
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
  image-rendering: optimize-quality;

  // Hardware acceleration for smooth performance
  transform: translate3d(0, 0, 0);
  backface-visibility: hidden;
  will-change: opacity, transform;

  // Smooth crossfade transition
  transition: opacity 1000ms cubic-bezier(0.4, 0, 0.2, 1);

  &.current {
    z-index: 1;
  }

  &.next {
    z-index: 2;
  }
}

// Enhanced keyframes for ultra-smooth transitions
@keyframes smoothCrossfade {
  0% {
    opacity: 0;
    transform: scale(1.02);
  }

  100% {
    opacity: 1;
    transform: scale(1);
  }
}

// Subtle zoom effect for more dynamic transitions
@keyframes subtleZoom {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.01);
  }

  100% {
    transform: scale(1);
  }
}

// Remove preload optimization that might cause visual issues

// Performance optimizations for mobile
@media (max-width: 768px) {
  .backgroundImage {
    background-attachment: scroll; // Better performance on mobile
    transform: translate3d(0, 0, 0); // Force hardware acceleration

    // Faster transitions on mobile for better performance
    transition: opacity 600ms cubic-bezier(0.4, 0, 0.2, 1);
  }
}

// Reduce motion for accessibility
@media (prefers-reduced-motion: reduce) {
  .backgroundImage {
    transition-duration: 0.1s !important;
    animation: none !important;
  }

  .slideshow {
    animation: none !important;
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .slideshow::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.3);
    z-index: 10;
    pointer-events: none;
  }
}