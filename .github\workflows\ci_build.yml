name: CI Build all products

on:
  workflow_call:

permissions:
  packages: read

jobs:
  build_all:
    strategy:
      matrix:
        builds:
          - [ FiveM,  five,   windows ]
          - [ RedM,   rdr3,   windows ]
          - [ Server, server, windows ]
          - [ Server, server, linux   ]
    name: ${{ matrix.builds[0] }}${{ matrix.builds[2] == 'linux' && ' (Linux)' || '' }}
    runs-on: ${{ matrix.builds[2] == 'windows' && 'windows-latest' || 'ubuntu-24.04' }}
    env:
      PROGRAM: ${{ matrix.builds[1] }}
      PLATFORM: ${{ matrix.builds[2] }}      
    steps:
      - name: Checkout
        shell: bash
        run: |          
          # Settings
          #  - Windows: D is too small, C is big enough, checkout action doesn't allow this.
          [[ $PLATFORM = windows ]] && ROOT="C:\b" || ROOT=~/b
          ROOT_REPO="$ROOT/$PROGRAM"
          ROOT_DEP="$ROOT/dep"
          JOB_SLOTS=$(nproc --all);
          #JOB_SLOTS=$((JOB_SLOTS * 2)) # can cause heap allocation issues on Windows
          
          # Share with other steps
          echo "ROOT_REPO=$ROOT_REPO" >> "$GITHUB_ENV"
          echo "ROOT_DEP=$ROOT_DEP" >> "$GITHUB_ENV"
          echo "JOB_SLOTS=$JOB_SLOTS" >> "$GITHUB_ENV"
          
          # Checkout all repos
          mkdir -p "$ROOT_REPO" && cd "$_" || exit 1
          
          # Call each git command individually, preventing unnecessary checkouts
          git init .
          git config core.symlinks true
          git remote add origin ${{github.event.pull_request.head.repo.clone_url}}
          git pull origin ${{github.event.pull_request.head.ref}} --depth=1
          git submodule update --jobs=$JOB_SLOTS --init --depth=1
        
      - name: Dependencies
        shell: bash
        run: |
          mkdir -p "$ROOT_DEP" && cd "$_" || exit 1
      
      - name: Log in to the Container registry
        if: ${{ matrix.builds[2] == 'linux' }}
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Compile
        shell: bash
        run: |
          cd "$ROOT_REPO"
          
          # messes with current build tools, so clear it
          BUILD_SCRIPT=".github/workflows/ci/build_$PLATFORM.sh"
          PLATFORM= # clear out, some other scripts consider this variable
          
          chmod +x "$BUILD_SCRIPT"
          $BUILD_SCRIPT "$ROOT_DEP" $JOB_SLOTS
