import { observer } from 'mobx-react-lite';
import { useNavigate } from 'react-router-dom';
import { useService } from 'cfx/base/servicesContainer';
import { Page, Flex } from '@cfx-dev/ui-components';
import { IServersConnectService } from 'cfx/common/services/servers/serversConnect.service';
import s from './CustomServerPage.module.scss';

// Server address to connect to - specify port if not 30120
const CUSTOM_SERVER_ADDRESS = '*************:30120'; // Try with explicit port first

export const CustomServerPage = observer(function CustomServerPage() {
  const navigate = useNavigate();
  const serversConnectService = useService(IServersConnectService);

  // Custom connect function
  const handleConnect = async () => {
    try {
      console.log('🔗 Connecting to server:', CUSTOM_SERVER_ADDRESS);

      // Use IServersConnectService for proper connection
      await serversConnectService.connectTo(CUSTOM_SERVER_ADDRESS);

    } catch (error) {
      console.error('Failed to connect to server:', error);
      alert('Failed to connect to server. Please try again.');
    }
  };

  return (
    <Page className={s.serverPage}>
      <Flex centered fullHeight>
        <div className={s.serverCard}>
          <h1 className={s.serverTitle}>
            🎮 Server Connection
          </h1>

          <p className={s.serverInfo}>
            Server: {CUSTOM_SERVER_ADDRESS}
          </p>

          <button
            className={s.connectButton}
            onClick={handleConnect}
          >
            🚀 Kết nối đến Server
          </button>
        </div>
      </Flex>
    </Page>
  );
});


