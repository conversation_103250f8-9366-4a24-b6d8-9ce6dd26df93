#
# This is the main Apache HTTP server configuration file.  It contains the
# configuration directives that give the server its instructions.
#

# ServerRoot: The top of the directory tree under which the server's
# configuration, error, and log files are kept.
ServerRoot "C:/Apache24"

# Listen: Allows you to bind Apache to specific IP addresses and/or
# ports, instead of the default. See also the <VirtualHost>
# directive.
Listen 80

# Dynamic Shared Object (DSO) Support
# Core modules - REQUIRED
LoadModule access_compat_module modules/mod_access_compat.so
LoadModule actions_module modules/mod_actions.so
LoadModule alias_module modules/mod_alias.so
LoadModule allowmethods_module modules/mod_allowmethods.so
LoadModule asis_module modules/mod_asis.so
LoadModule auth_basic_module modules/mod_auth_basic.so
LoadModule authn_core_module modules/mod_authn_core.so
LoadModule authn_file_module modules/mod_authn_file.so
LoadModule authz_core_module modules/mod_authz_core.so
LoadModule authz_groupfile_module modules/mod_authz_groupfile.so
LoadModule authz_host_module modules/mod_authz_host.so
LoadModule authz_user_module modules/mod_authz_user.so
LoadModule autoindex_module modules/mod_autoindex.so
LoadModule cgi_module modules/mod_cgi.so
LoadModule dir_module modules/mod_dir.so
LoadModule env_module modules/mod_env.so
LoadModule include_module modules/mod_include.so
LoadModule isapi_module modules/mod_isapi.so
LoadModule log_config_module modules/mod_log_config.so
LoadModule logio_module modules/mod_logio.so
LoadModule mime_module modules/mod_mime.so
LoadModule negotiation_module modules/mod_negotiation.so
LoadModule setenvif_module modules/mod_setenvif.so

# Additional modules for proxy
LoadModule rewrite_module modules/mod_rewrite.so
LoadModule headers_module modules/mod_headers.so
LoadModule expires_module modules/mod_expires.so
LoadModule deflate_module modules/mod_deflate.so
LoadModule filter_module modules/mod_filter.so
LoadModule proxy_module modules/mod_proxy.so
LoadModule proxy_http_module modules/mod_proxy_http.so

# SSL modules - COMMENTED OUT for now
# LoadModule ssl_module modules/mod_ssl.so
# LoadModule socache_shmcb_module modules/mod_socache_shmcb.so
LoadModule http2_module modules/mod_http2.so

# Server Admin Email
ServerAdmin <EMAIL>

# ServerName gives the name and port that the server uses to identify itself.
ServerName jason.io.vn:80

# Deny access to the entirety of your server's filesystem. You must
# explicitly permit access to web content directories in other
# <Directory> blocks below.
<Directory />
    AllowOverride none
    Require all denied
</Directory>

# DocumentRoot: The directory out of which you will serve your
# documents. By default, all requests are taken from this directory, but
# symbolic links and aliases may be used to point to other locations.
DocumentRoot "C:/Apache24/htdocs"
<Directory "C:/Apache24/htdocs">
    Options Indexes FollowSymLinks
    AllowOverride None
    Require all granted
</Directory>

# DirectoryIndex: sets the file that Apache will serve if a directory
# is requested.
<IfModule dir_module>
    DirectoryIndex index.html
</IfModule>

# The following lines prevent .htaccess and .htpasswd files from being
# viewed by Web clients.
<Files ".ht*">
    Require all denied
</Files>

# ErrorLog: The location of the error log file.
ErrorLog "logs/error.log"

# LogLevel: Control the number of messages logged to the error_log.
LogLevel warn

<IfModule log_config_module>
    LogFormat "%h %l %u %t \"%r\" %>s %b \"%{Referer}i\" \"%{User-Agent}i\"" combined
    LogFormat "%h %l %u %t \"%r\" %>s %b" common

    <IfModule logio_module>
      LogFormat "%h %l %u %t \"%r\" %>s %b \"%{Referer}i\" \"%{User-Agent}i\" %I %O" combinedio
    </IfModule>

    CustomLog "logs/access.log" common
</IfModule>

<IfModule alias_module>
    ScriptAlias /cgi-bin/ "C:/Apache24/cgi-bin/"
</IfModule>

<IfModule cgid_module>
</IfModule>

<Directory "C:/Apache24/cgi-bin">
    AllowOverride None
    Options None
    Require all granted
</Directory>

<IfModule headers_module>
    RequestHeader unset Proxy early
</IfModule>

<IfModule mime_module>
    TypesConfig conf/mime.types
    AddType application/x-compress .Z
    AddType application/x-gzip .gz .tgz
    AddType text/html .shtml
    AddOutputFilter INCLUDES .shtml
</IfModule>

#
# API Proxy Configuration for Cloudflare
#

# HTTP Virtual Host - API Proxy (Cloudflare sẽ handle SSL)
<VirtualHost *:80>
    ServerName jason.io.vn
    ServerAlias www.jason.io.vn

    # Proxy cho API service
    ProxyPreserveHost On
    ProxyRequests Off

    # API routes
    ProxyPass /api/ http://localhost:3005/api/
    ProxyPassReverse /api/ http://localhost:3005/api/

    # Health check
    ProxyPass /health http://localhost:3005/health
    ProxyPassReverse /health http://localhost:3005/health

    # Root path cho API
    ProxyPass / http://localhost:3005/
    ProxyPassReverse / http://localhost:3005/

    # Basic compression
    <IfModule mod_deflate.c>
        SetOutputFilter DEFLATE
        SetEnvIfNoCase Request_URI \
            \.(?:gif|jpe?g|png|ico|svg|webp)$ no-gzip dont-vary
    </IfModule>

    # Headers cho Cloudflare proxy - ĐÃ XÓA CORS HEADERS
    <IfModule mod_headers.c>
        # Cloudflare headers
        Header always set X-Forwarded-Proto "%{HTTP:CF-Visitor}e"
        Header always set X-Forwarded-Host "jason.io.vn"
        Header always set X-Real-IP "%{HTTP:CF-Connecting-IP}e"

        # Security headers only - NO CORS
        Header always set X-Content-Type-Options nosniff
        Header always set X-Frame-Options SAMEORIGIN

        # ✅ ĐÃ XÓA CÁC CORS HEADERS ĐỂ TRÁNH DUPLICATE:
        # Header always set Access-Control-Allow-Origin "*"
        # Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
        # Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With"
    </IfModule>

    # Handle OPTIONS requests - để Node.js server xử lý
    <IfModule mod_rewrite.c>
        RewriteEngine On
        # Chuyển OPTIONS requests cho Node.js server xử lý
        # RewriteCond %{REQUEST_METHOD} OPTIONS
        # RewriteRule ^(.*)$ $1 [R=200,L]
    </IfModule>

    # Logs
    ErrorLog "logs/api_error.log"
    CustomLog "logs/api_access.log" combined
</VirtualHost>

# HTTPS Virtual Host - API Proxy SSL (chỉ khi có SSL certificate)
<IfModule mod_ssl.c>
    Listen 443 ssl

    <VirtualHost *:443>
        ServerName jason.io.vn
        ServerAlias www.jason.io.vn

        # SSL Configuration - TẠM THỜI COMMENT OUT
        SSLEngine on
        # SSLCertificateFile "conf/ssl/jason.io.vn.crt"
        # SSLCertificateKeyFile "conf/ssl/jason.io.vn.key"

        # Tạm thời dùng self-signed cert (tạo sau)
        SSLCertificateFile "conf/server.crt"
        SSLCertificateKeyFile "conf/server.key"

        # Modern SSL
        SSLProtocol all -SSLv3 -TLSv1 -TLSv1.1
        SSLCipherSuite ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256

        # HTTP/2 support
        <IfModule mod_http2.c>
            Protocols h2 http/1.1
        </IfModule>

        # Proxy cho API service
        ProxyPreserveHost On
        ProxyRequests Off

        # API routes
        ProxyPass /api/ http://localhost:3005/api/
        ProxyPassReverse /api/ http://localhost:3005/api/

        # Health check
        ProxyPass /health http://localhost:3005/health
        ProxyPassReverse /health http://localhost:3005/health

        # Root path
        ProxyPass / http://localhost:3005/
        ProxyPassReverse / http://localhost:3005/

        # Headers cho HTTPS proxy - ĐÃ XÓA CORS HEADERS
        <IfModule mod_headers.c>
            Header always set X-Forwarded-Proto "https"
            Header always set X-Forwarded-Host "jason.io.vn"
            Header always set Strict-Transport-Security "max-age=63072000; includeSubDomains"
            Header always set X-Content-Type-Options nosniff
            Header always set X-Frame-Options SAMEORIGIN

            # ✅ ĐÃ XÓA CÁC CORS HEADERS ĐỂ TRÁNH DUPLICATE:
            # Header always set Access-Control-Allow-Origin "*"
            # Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
            # Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With"
        </IfModule>

        # SSL Logs
        ErrorLog "logs/api_ssl_error.log"
        CustomLog "logs/api_ssl_access.log" combined
    </VirtualHost>
</IfModule>
