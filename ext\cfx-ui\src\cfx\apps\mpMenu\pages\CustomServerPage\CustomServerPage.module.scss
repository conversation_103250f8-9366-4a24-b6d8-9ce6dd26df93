// Custom Server Page Styles
.serverPage {
  min-height: 100vh;
  background: linear-gradient(135deg, 
    rgba(26, 26, 26, 0.95) 0%, 
    rgba(44, 44, 44, 0.9) 100%);
  backdrop-filter: blur(20px);
}

.serverCard {
  text-align: center;
  padding: 3rem;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 20px;
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 
    0 20px 60px rgba(0, 0, 0, 0.4),
    0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  max-width: 500px;
  margin: 0 auto;
  
  // Subtle animation
  animation: slideInUp 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.serverTitle {
  margin-bottom: 1.5rem;
  color: #fff;
  font-size: 2rem;
  font-weight: 700;
  background: linear-gradient(135deg, #FF8C42 0%, #7CB342 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.serverInfo {
  margin-bottom: 2rem;
  color: #ccc;
  font-size: 1.1rem;
  font-weight: 500;
}

.connectButton {
  background: linear-gradient(135deg, #FF8C42 0%, #7CB342 100%);
  border: none;
  border-radius: 12px;
  color: white;
  font-size: 1.2rem;
  font-weight: 600;
  padding: 16px 32px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 8px 25px rgba(255, 140, 66, 0.3);
  
  &:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 12px 35px rgba(255, 140, 66, 0.4);
    background: linear-gradient(135deg, #ff9d5c 0%, #8bc556 100%);
  }
  
  &:active {
    transform: translateY(0) scale(1.02);
  }
}

.navButton {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: white;
  font-size: 0.9rem;
  font-weight: 500;
  padding: 10px 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  
  &:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  }
}

// Animations
@keyframes slideInUp {
  0% {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

// Responsive design
@media (max-width: 768px) {
  .serverCard {
    padding: 2rem;
    margin: 1rem;
  }
  
  .serverTitle {
    font-size: 1.5rem;
  }
  
  .connectButton {
    font-size: 1rem;
    padding: 14px 28px;
  }
}

@media (max-width: 480px) {
  .serverCard {
    padding: 1.5rem;
    margin: 0.5rem;
  }
  
  .serverTitle {
    font-size: 1.25rem;
  }
  
  .connectButton {
    font-size: 0.9rem;
    padding: 12px 24px;
  }
}
