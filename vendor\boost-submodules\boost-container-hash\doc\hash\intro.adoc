////
Copyright 2005-2008 <PERSON>
Copyright 2022 <PERSON>
Copyright 2022 <PERSON>
Distributed under the Boost Software License, Version 1.0.
https://www.boost.org/LICENSE_1_0.txt
////

[#intro]
= Introduction
:idprefix: intro_

`boost::hash` is an enhanced implementation of the
https://en.wikipedia.org/wiki/Hash_function[hash function] object specified by
{cpp}11 as `std::hash`. It is the default hash function for
link:../../../unordered/index.html[Boost.Unordered],
link:../../../intrusive/index.html[Boost.Intrusive]'s unordered associative
containers, link:../../../multi_index/index.html[Boost.MultiIndex]'s hash
indices, and link:../../../bimap/index.html[Boost.Bimap]'s `unordered_set_of`.

Out of the box, `boost::hash` supports

* standard integral types (integers, character types, and `bool`);
* standard floating point types (`float`, `double`, `long double`);
* pointers (to objects and to functions, but not pointers to members)
  and `nullptr`;
* enumeration types;
* C arrays;
* `std::complex`;
* tuple-like types, such as `std::pair`, `std::tuple`, and user-defined
  types that specialize `std::tuple_size` and provide `get<I>`;
* sequence-like types, both standard and user-defined (sequence-like types
  have `begin()` and `end()` member functions returning iterators);
* unordered sequences, standard or user-defined (sequences for which the hash
  value does not depend on the element order, such as `std::unordered_set` and
  `std::unordered_map`);
* described structs and classes -- ones that have been annotated with the
  `BOOST_DESCRIBE_STRUCT` or `BOOST_DESCRIBE_CLASS` macros from
  link:../../../describe/index.html[Boost.Describe];
* `std::unique_ptr`, `std::shared_ptr`;
* `std::type_index`;
* `std::error_code`, `std::error_condition`;
* `std::optional`;
* `std::variant`, `std::monostate`.

`boost::hash` is extensible; it's possible for a user-defined type `X` to make
iself hashable via `boost::hash<X>` by defining an appropriate overload of the
function `hash_value`. Many, if not most, Boost types already contain the
necessary support.

`boost::hash` meets the requirements for `std::hash` specified in the {cpp}11
standard, namely, that for two different input values their corresponding hash
values are either guaranteed to be distinct, or the probability of their being
the same (a hash collision) is small. Standard unordered containers, and the
hash-based Boost containers, are designed to work well with such hash functions.

`boost::hash` does not meet the stronger requirements often placed on hash
functions in a more general context. In particular, the hash function is not
cryptographic, is not collision-resistant against a determined adversary, and
does not necessarily possess good "avalanche" properties; that is, small
(single bit) perturbations in the input do not necessarily result in large
(half bits changing) perturbations in the output.

In particular, `boost::hash` has traditionally been the identity function for
all integral types that fit into `std::size_t`, because this guarantees lack of
collisions and is as fast as possible.
