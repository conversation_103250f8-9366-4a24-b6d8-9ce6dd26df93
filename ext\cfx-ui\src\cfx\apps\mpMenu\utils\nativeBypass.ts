/**
 * Native Bypass cho Development
 * V<PERSON><PERSON><PERSON> qua tất cả native calls để chạy UI trong browser
 */

// Global bypass cho tất cả native functions
(window as any).invokeNative = function (native: string, arg: string) {
  console.log(`[BYPASS] Native call bypassed: ${native}`, arg);

  // <PERSON>h<PERSON>ng là<PERSON> cả, chỉ log
  return Promise.resolve();
};

// Mock nuiWindow object
(window as any).nuiTargetGame = 'gta5';
(window as any).nuiTargetGameBuild = 2944;
(window as any).nuiTargetGamePureLevel = 0;
(window as any).nuiSystemLanguages = ['en-us', 'vi-vn'];

(window as any).nuiSetAudioCategory = function (category: string) {
  console.log(`[BYPASS] Audio category set: ${category}`);
};

// Bypass cho các service calls
const originalFetch = window.fetch;
window.fetch = function (url: string | Request, init?: RequestInit) {
  console.log(`[BYPASS] Fetch call: ${url}`);

  // N<PERSON><PERSON> l<PERSON> native call, return empty response
  if (typeof url === 'string' && url.includes('native')) {
    return Promise.resolve(new Response('{}', {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    }));
  }

  return originalFetch(url, init);
};

// Bypass cho postMessage
const originalPostMessage = window.postMessage;
(window as any).postMessage = function (message: any, targetOrigin: string) {
  console.log(`[BYPASS] PostMessage: `, message);

  // Gọi original nếu cần
  return originalPostMessage.call(window, message, targetOrigin);
};

// Mock localStorage cho convars
const mockConvars = {
  'ui_streamerMode': 'false',
  'ui_quickAccessLocalhostPort': '30120',
  'ui_updateChannel': 'production',
  'ui_customBackdrop': '',
  'ui_preferLightColorScheme': 'false',
  'ui_preferBlurredBackdrop': 'true',
  'ui_premium': 'false',
  'ui_customBrandingEmoji': '',
  'ui_devMode': 'true'
};

// Override localStorage
const originalGetItem = localStorage.getItem;
const originalSetItem = localStorage.setItem;

localStorage.getItem = function (key: string) {
  if (key.startsWith('ui_') && mockConvars[key as keyof typeof mockConvars]) {
    console.log(`[BYPASS] LocalStorage get: ${key} = ${mockConvars[key as keyof typeof mockConvars]}`);
    return mockConvars[key as keyof typeof mockConvars];
  }
  return originalGetItem.call(localStorage, key);
};

localStorage.setItem = function (key: string, value: string) {
  if (key.startsWith('ui_')) {
    console.log(`[BYPASS] LocalStorage set: ${key} = ${value}`);
    (mockConvars as any)[key] = value;
    return;
  }
  return originalSetItem.call(localStorage, key, value);
};

// Bypass cho các error handlers
window.addEventListener('error', function (event) {
  if (event.message.includes('invokeNative') || event.message.includes('native')) {
    console.log('[BYPASS] Native error bypassed:', event.message);
    event.preventDefault();
    return false;
  }
});

window.addEventListener('unhandledrejection', function (event) {
  if (event.reason && event.reason.toString().includes('native')) {
    console.log('[BYPASS] Native promise rejection bypassed:', event.reason);
    event.preventDefault();
  }
});

console.log('[BYPASS] Native bypass system initialized');

export { };
