@import "variables";

.root {
  display: flex;
  align-items: center;
  justify-content: center;

  cursor: pointer;

  @include interactiveTransition;

  &:hover {
    background-color: rgba($fgColor, .1);

    .actions {
      opacity: 1;
    }
  }

  .content {
    flex-grow: 1;

    display: flex;
    align-items: center;
    justify-content: stretch;

    @include interactiveTransition;

    &:hover {
      background-color: rgba($acColor, 1);
    }

    .icon {
      padding: $q*2 $q*4;
      padding-right: 0;

      svg {
        font-size: $fs2;
      }
    }

    .info {
      flex-grow: 1;

      padding: $q*2 $q*4;

      .name {
        font-size: $fs2;
      }

      .path {
        padding-top: $q;

        font-size: $fs08;
        color: rgba($fgColor, .5);
      }
    }
  }

  .actions {
    opacity: 0;

    padding: $q*2;
  }
}
