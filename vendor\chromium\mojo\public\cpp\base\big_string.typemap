# Copyright 2018 The Chromium Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

mojom = "//mojo/public/mojom/base/big_string.mojom"
public_headers = []
traits_headers = [ "//mojo/public/cpp/base/big_string_mojom_traits.h" ]
sources = [
  "//mojo/public/cpp/base/big_string_mojom_traits.cc",
  "//mojo/public/cpp/base/big_string_mojom_traits.h",
]
public_deps = [
  "//mojo/public/cpp/base",
  "//mojo/public/cpp/base:shared_typemap_traits",
]

type_mappings = [ "mojo_base.mojom.BigString=::std::string" ]
