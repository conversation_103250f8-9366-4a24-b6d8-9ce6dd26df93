@import '../../../vars.scss';

.root {
  display: flex;
  flex-direction: column;

  width: $wePanelWidth;

  height: auto;
  max-height: $wePanelHeight;

  overflow-y: auto;
  overflow-x: visible;

  padding: 0;

  --height: #{$q*6};

  .header {
    display: flex;
    align-items: center;
    justify-content: center;

    height: var(--height);

    background-color: rgba($bgColor, .5);
    border-bottom: solid 1px rgba($fgColor, .1);

    .icon {
      flex-shrink: 0;

      display: flex;
      align-items: center;
      justify-content: center;

      width: $q*4;

      svg {
        flex-shrink: 0;
        font-size: $fs06;

        color: $scColor;
      }
    }

    .name {
      // weird flex hacks
      flex-grow: 1;
      width: 1px;

      display: flex;
      align-items: center;
      justify-content: center;

      padding: $q*1.5 $q;
      padding-left: 0;

      user-select: none;

      > span {
        width: 100%;
        @include ellipsis;
      }
    }
  }

  .block {
    padding: $q 0;

    --height: #{$q*4};

    .label {
      display: flex;
      align-items: center;
      justify-content: flex-start;

      height: var(--height);

      gap: $q;

      font-size: $fs08;
      font-weight: 100;
      color: rgba($fgColor, .75);

      user-select: none;
    }

    .label-row {
      display: flex;
      align-items: center;
      justify-content: stretch;

      .label {
        flex-grow: 1;
      }

      .controls {
        button {
          --height: #{$q*4};
        }
      }
    }

    .control {
      margin-top: $q;

      &.inputs {
        display: flex;
        align-items: center;
        justify-content: stretch;
        flex-direction: row;

        gap: $q;

        .input {
          flex-grow: 1;

          width: 25%;
        }
      }
    }
  }

  .controls {
    display: flex;

    button {
      display: flex;
      align-items: center;
      justify-content: center;

      width: var(--height);
      height: var(--height);

      color: $fgColor;
      background-color: transparent;

      @include fontPrimary;

      border: none;

      cursor: pointer;

      user-select: none;

      @include interactiveTransition;

      &:hover {
        background-color: rgba($fgColor, .25);
      }

      &:active {
        background-color: rgba($fgColor, .5);
      }
    }

    span {
      font-size: $fs08;

      color: rgba($fgColor, .75);
    }
  }

  .section {
    box-shadow: 0 -1px 0 rgba($fgColor, .1) inset;

    &.open {
      .content {
        display: block;
      }
    }

    &:last-of-type {
      box-shadow: none;
    }

    .toggle {
      display: flex;
      align-items: center;

      gap: $q*.5;

      padding: $q*.5 0;

      cursor: pointer;

      color: rgba($fgColor, .5);
      font-size: $fs08;
      font-weight: 100;

      .icon {
        display: flex;
        align-items: center;
        justify-content: center;

        width: $q*4;

        font-size: $fs06;
      }
    }

    .content {
      display: none;

      padding-left: $q*4;
      padding-right: $q;
      padding-bottom: $q;
    }
  }
}
