@import 'variables';

 // matching fxcode's status bar height
$statusBarHeight: 22px;

.root {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;

  width: var(--toolbar-width);

  z-index: $zToolbar;

  .resizer {
    $resizerWidth: 4px;

    position: fixed;

    top: 0;
    left: calc(var(--toolbar-width) - #{$resizerWidth * .5});
    bottom: 0;

    width: $resizerWidth;

    cursor: ew-resize;

    z-index: $zToolbar + 1;

    background-color: transparent;

    transition: background-color .2s ease;

    &.resizing,
    &:hover {
      background-color: rgba($fgColor, .2);
      transition-delay: .5s;
    }
  }

  .tools-bar {
    position: relative;

    width: var(--toolbar-width);
    height: 100%;

    font-size: 13px;

    background-color: $bgColor;
    box-shadow: -1px 0 0 0 rgba($fgColor, .05) inset;

    z-index: 2;

    .controls {
      position: absolute;

      top: 0;
      left: 0;

      width: var(--toolbar-width);
      height: $toolbarHeight;

      display: flex;
      align-items: stretch;
      justify-content: stretch;

      .project-name {
        flex-grow: 1;

        width: 1px;

        display: flex;
        align-items: center;

        padding: 0 $q*2;

        span {
          font-size: $fs1;
          line-height: 1;
          height: $fs1;
          width: 100%;

          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }

      button {
        display: flex;
        align-items: center;
        justify-content: center;

        height: $toolbarHeight;
        width: $toolbarHeight;

        color: $fgColor;
        background-color: transparent;

        border: none;
        outline: none;

        transition: all .2s ease;

        svg {
          // font-size: $fs1;
          color: rgba($fgColor, .75);
        }

        &:hover {
          color: $fgColor;
          background-color: $acColor;
          transition: none;

          svg {
            color: $fgColor;
          }
        }

        &:disabled {
          opacity: .5;
          pointer-events: none;
        }

        &.menu {
          cursor: pointer;

          svg {
            font-size: $fs1;
          }
        }
      }
    }

    .pane {
      position: absolute;

      top: $toolbarHeight;
      left: 0;
      bottom: $statusBarHeight;

      width: var(--toolbar-width);

      opacity: 0;
      pointer-events: none;

      transition: opacity .1s ease;

      &.active {
        opacity: 1;
        pointer-events: all;
      }
    }
  }
}
