# 🔐 GTA V API Auth Service

API xác thực hoàn chỉnh được xây dựng bằng Node.js với SQLite database và Gmail SMTP. Hệ thống cung cấp đăng ký, xác thực email và đăng nhập an toàn với nhiều tính năng bảo mật tiên tiến.

## ✨ Tính năng chính

### 🔒 Xác thực & Bảo mật
- **Đăng ký tài khoản** với validation đầy đủ
- **Xác thực email** qua mã 6 số gửi qua Gmail
- **Đăng nhập an toàn** với JWT token
- **Chống SQL injection** bằng prepared statements
- **Hash password** với bcrypt (12 rounds)
- **Rate limiting** cho tất cả endpoints
- **Account lockout** sau 5 lần đăng nhập sai
- **Security headers** với Helmet.js

### 📊 Quản lý dữ liệu
- **SQLite database** với auto-migration
- **<PERSON><PERSON><PERSON> l<PERSON>ch sử IP** đăng nhập chi tiết
- **Thống kê đăng nhập** theo IP và user
- **Foreign key constraints** đảm bảo tính toàn vẹn

### 📧 Email service
- **Gmail SMTP** với App Password
- **Email templates** đẹp mắt responsive
- **Thông báo đăng nhập** tự động
- **Mã xác thực** có thời gian hết hạn

## 🚀 Cài đặt nhanh

### 1. Clone và cài đặt dependencies

```bash
# Cài đặt packages
npm install
```

### 2. Cấu hình môi trường

```bash
# Copy file cấu hình mẫu
cp .env.example .env
```

Chỉnh sửa file `.env` với thông tin của bạn:

```env
# Server
PORT=3000
NODE_ENV=development

# JWT (QUAN TRỌNG: Thay đổi secret key!)
JWT_SECRET=your_super_secret_jwt_key_change_this_in_production_123456789
JWT_EXPIRES_IN=24h

# Gmail SMTP (Xem hướng dẫn bên dưới)
GMAIL_USER=<EMAIL>
GMAIL_APP_PASSWORD=your_16_character_app_password

# Database
DB_PATH=./database/users.db

# Bảo mật
BCRYPT_ROUNDS=12
VERIFICATION_CODE_EXPIRES=15
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_TIME=30
```

### 3. Cấu hình Gmail SMTP

**Bước quan trọng để gửi email xác thực:**

1. **Bật xác thực 2 bước** trong Gmail
2. **Tạo App Password:**
   - Vào [Google Account Security](https://myaccount.google.com/security)
   - Chọn "App passwords"
   - Tạo password cho "Mail"
   - Sử dụng mật khẩu 16 ký tự này cho `GMAIL_APP_PASSWORD`

📖 **Chi tiết:** Xem file `GMAIL_SETUP.md`

### 4. Chạy server

```bash
# Development
npm run dev

# Production
npm start
```

Server sẽ chạy tại: **http://http://*************:3005/**

## 📡 API Documentation

### Base URL
```
http://http://*************:3005//api/auth
```

### 1. 📝 Đăng ký tài khoản

```http
POST /api/auth/register
Content-Type: application/json

{
  "username": "testuser123",
  "email": "<EMAIL>",
  "password": "Password123!"
}
```

**Validation rules:**
- `username`: 3-30 ký tự, chỉ chữ cái, số, dấu gạch dưới
- `email`: Email hợp lệ, tối đa 100 ký tự
- `password`: 8-128 ký tự, có chữ hoa, thường, số, ký tự đặc biệt

**Response thành công:**
```json
{
  "success": true,
  "message": "Đăng ký thành công! Vui lòng kiểm tra email để xác thực tài khoản.",
  "data": {
    "userId": 1,
    "username": "testuser123",
    "email": "<EMAIL>",
    "expiresIn": "15 phút",
    "nextStep": "Kiểm tra email và sử dụng API /verify để xác thực tài khoản"
  }
}
```

### 2. 🔐 Xác thực email

```http
POST /api/auth/verify
Content-Type: application/json

{
  "email": "<EMAIL>",
  "code": "123456"
}
```

**Response thành công:**
```json
{
  "success": true,
  "message": "Xác thực thành công! Tài khoản của bạn đã được kích hoạt.",
  "data": {
    "userId": 1,
    "username": "testuser123",
    "email": "<EMAIL>",
    "verifiedAt": "2024-01-01T12:00:00.000Z",
    "nextStep": "Bây giờ bạn có thể đăng nhập bằng API /login"
  }
}
```

### 3. 🔑 Đăng nhập

```http
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "Password123!"
}
```

**Response thành công:**
```json
{
  "success": true,
  "message": "Đăng nhập thành công!",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "tokenType": "Bearer",
    "expiresIn": "24h",
    "user": {
      "id": 1,
      "username": "testuser123",
      "email": "<EMAIL>",
      "isVerified": true,
      "createdAt": "2024-01-01T12:00:00.000Z"
    },
    "loginInfo": {
      "ipAddress": "*************",
      "userAgent": "Mozilla/5.0...",
      "loginTime": "2024-01-01T12:00:00.000Z"
    }
  }
}
```

### 4. 🏥 Health Check

```http
GET /health
```

**Response:**
```json
{
  "success": true,
  "message": "GTA V API Auth Service đang hoạt động",
  "timestamp": "2024-01-01T12:00:00.000Z",
  "version": "1.0.0",
  "environment": "development",
  "uptime": 3600
}
```

## 🧪 Testing

### Chạy test tự động

```bash
npm test
```

Test suite sẽ kiểm tra:
- ✅ Server health check
- ✅ Đăng ký tài khoản
- ✅ Validation errors
- ✅ Email trùng lặp
- ✅ Xác thực mã sai
- ✅ Đăng nhập trước xác thực
- ✅ Mật khẩu sai

### Test thủ công với curl

```bash
# 1. Đăng ký
curl -X POST http://http://*************:3005//api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"username":"testuser","email":"<EMAIL>","password":"Password123!"}'

# 2. Xác thực (thay 123456 bằng mã từ email)
curl -X POST http://http://*************:3005//api/auth/verify \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","code":"123456"}'

# 3. Đăng nhập
curl -X POST http://http://*************:3005//api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"Password123!"}'
```

## 🛡️ Tính năng bảo mật

### 1. Chống SQL Injection
- Sử dụng **prepared statements** cho tất cả queries
- **Parameterized queries** với SQLite3
- **Input validation** và sanitization

### 2. Password Security
- **bcrypt hashing** với 12 rounds
- **Minimum requirements**: 8 ký tự, chữ hoa, thường, số, ký tự đặc biệt
- **No password storage** in plain text

### 3. Rate Limiting
- **Đăng ký**: 5 requests/15 phút
- **Đăng nhập**: 10 requests/15 phút
- **Xác thực**: 20 requests/15 phút
- **Per IP address** limiting

### 4. Account Protection
- **Account lockout** sau 5 lần đăng nhập sai
- **Lockout time**: 30 phút (configurable)
- **Email verification** bắt buộc
- **JWT token** với expiration

### 5. Security Headers
```javascript
X-Frame-Options: DENY
X-Content-Type-Options: nosniff
X-XSS-Protection: 1; mode=block
Referrer-Policy: strict-origin-when-cross-origin
Content-Security-Policy: default-src 'self'
```

## 📊 Database Schema

### Bảng `users`
```sql
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT UNIQUE NOT NULL,
    email TEXT UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    is_verified INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    login_attempts INTEGER DEFAULT 0,
    locked_until DATETIME NULL
);
```

### Bảng `verification_codes`
```sql
CREATE TABLE verification_codes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    code TEXT NOT NULL,
    type TEXT NOT NULL DEFAULT 'email_verification',
    expires_at DATETIME NOT NULL,
    used INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
);
```

### Bảng `login_history`
```sql
CREATE TABLE login_history (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,
    ip_address TEXT NOT NULL,
    user_agent TEXT,
    login_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    success INTEGER NOT NULL,
    action_type TEXT DEFAULT 'login',
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE SET NULL
);
```

## 🔧 Cấu trúc dự án

```
api_GTAv/
├── config/
│   └── database.js          # Cấu hình SQLite database
├── middleware/
│   └── security.js          # Rate limiting, validation, security
├── models/
│   └── User.js              # User model với prepared statements
├── routes/
│   └── auth.js              # Authentication routes
├── services/
│   └── emailService.js      # Gmail SMTP service
├── database/
│   └── users.db             # SQLite database (auto-created)
├── .env                     # Environment configuration
├── .env.example             # Environment template
├── server.js                # Main server file
├── test_api.js              # Test suite
├── package.json             # Dependencies
├── README.md                # This file
└── GMAIL_SETUP.md           # Gmail configuration guide
```

## 🚨 Production Checklist

### Bảo mật
- [ ] Thay đổi `JWT_SECRET` thành giá trị ngẫu nhiên mạnh
- [ ] Sử dụng HTTPS trong production
- [ ] Cấu hình reverse proxy (nginx/apache)
- [ ] Thiết lập firewall rules
- [ ] Enable database encryption
- [ ] Regular security updates

### Performance
- [ ] Database indexing optimization
- [ ] Connection pooling
- [ ] Caching strategy
- [ ] Load balancing
- [ ] Monitoring và logging

### Backup
- [ ] Automated database backups
- [ ] Environment configuration backup
- [ ] Disaster recovery plan

## 📝 Error Codes

| Code | Description |
|------|-------------|
| `EMAIL_EXISTS` | Email đã được sử dụng |
| `USER_NOT_FOUND` | Không tìm thấy user |
| `INVALID_CREDENTIALS` | Email/password sai |
| `ACCOUNT_LOCKED` | Tài khoản bị khóa |
| `ACCOUNT_UNVERIFIED` | Tài khoản chưa xác thực |
| `INVALID_CODE` | Mã xác thực không hợp lệ |
| `ALREADY_VERIFIED` | Tài khoản đã xác thực |
| `EMAIL_SEND_FAILED` | Không thể gửi email |
| `INTERNAL_ERROR` | Lỗi server |

## 🤝 Contributing

1. Fork the project
2. Create feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit changes (`git commit -m 'Add AmazingFeature'`)
4. Push to branch (`git push origin feature/AmazingFeature`)
5. Open Pull Request

## 📄 License

MIT License - Xem file [LICENSE](LICENSE) để biết thêm chi tiết.

## 🆘 Support

Nếu gặp vấn đề:

1. Kiểm tra [Issues](https://github.com/your-repo/issues)
2. Đọc file `GMAIL_SETUP.md` cho cấu hình Gmail
3. Chạy `npm test` để kiểm tra API
4. Kiểm tra logs server để debug

---

**🎉 Chúc bạn sử dụng API thành công!**
