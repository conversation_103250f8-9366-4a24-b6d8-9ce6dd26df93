# Copyright (c) 2013 The Chromium Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//build/config/chromecast_build.gni")
import("//build/config/compiler/compiler.gni")

declare_args() {
  # Stack traces will not include function names. Instead they will contain
  # file and offset information that can be used with
  # tools/valgrind/asan/asan_symbolize.py. By piping stderr through this script,
  # and also enabling symbol_level = 2, you can get much more detailed stack
  # traces with file names and line numbers, even in non-ASAN builds.
  #
  # It's useful to enable this by default for builds that will be stripped
  # before being run (e.g. chromecast cross-compiled builds); this results in
  # more consistent output after processing with asan_symbolize.py.
  print_unsymbolized_stack_traces =
      is_asan || is_lsan || is_msan || is_tsan ||
      (is_chromecast && current_toolchain != host_toolchain)
}

static_library("symbolize") {
  visibility = [ "//base/*" ]
  sources = [
    "config.h",
    "demangle.cc",
    "demangle.h",
    "glog/logging.h",
    "glog/raw_logging.h",
    "symbolize.cc",
    "symbolize.h",
    "utilities.h",
  ]

  defines = []
  if (print_unsymbolized_stack_traces) {
    defines += [ "PRINT_UNSYMBOLIZED_STACK_TRACES" ]
  }

  configs -= [ "//build/config/compiler:chromium_code" ]
  configs += [ "//build/config/compiler:no_chromium_code" ]
}
