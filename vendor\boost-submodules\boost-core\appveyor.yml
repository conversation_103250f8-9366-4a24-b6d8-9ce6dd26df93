# Copyright 2016-2021 <PERSON> Di<PERSON>v
# Distributed under the Boost Software License, Version 1.0.
# (See accompanying file LICENSE_1_0.txt or copy at http://boost.org/LICENSE_1_0.txt)

version: 1.0.{build}-{branch}

shallow_clone: true

branches:
  only:
    - master
    - develop
    - /feature\/.*/

environment:
  matrix:
    - APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2015
      TOOLSET: msvc-9.0,msvc-10.0,msvc-11.0
      ADDRMD: 32

    - APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2015
      TOOLSET: msvc-12.0,msvc-14.0
      ADDRMD: 32,64

    - APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2017
      TOOLSET: msvc-14.1
      ADDRMD: 32,64
      CXXSTD: 14,17

    # clang-win 32 bit fails to link with "unable to load mspdbcore.dll (error code: 126)"

    - APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2017
      TOOLSET: clang-win
      ADDRMD: 64
      CXXSTD: 14,17,latest

    - APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2019
      TOOLSET: clang-win
      ADDRMD: 64
      CXXSTD: 14,17,latest

    - APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2015
      ADDPATH: C:\cygwin\bin;
      TOOLSET: gcc
      CXXSTD: 03,11,14,1z

    - APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2015
      ADDPATH: C:\cygwin64\bin;
      TOOLSET: gcc
      CXXSTD: 03,11,14,1z

    - APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2015
      ADDPATH: C:\mingw\bin;
      TOOLSET: gcc
      CXXSTD: 03,11,14,1z

    - APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2015
      ADDPATH: C:\mingw-w64\x86_64-6.3.0-posix-seh-rt_v5-rev1\mingw64\bin;
      TOOLSET: gcc
      CXXSTD: 03,11,14,1z

    - APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2015
      ADDPATH: C:\mingw-w64\x86_64-7.2.0-posix-seh-rt_v5-rev1\mingw64\bin;
      TOOLSET: gcc
      CXXSTD: 03,11,14,1z

    - APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2015
      CMAKE: 1
    - APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2017
      CMAKE_SUBDIR: 1
    - APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2019
      CMAKE_INSTALL: 1

install:
  - set GIT_FETCH_JOBS=8
  - set BOOST_BRANCH=develop
  - if "%APPVEYOR_REPO_BRANCH%" == "master" set BOOST_BRANCH=master
  - cd ..
  - git clone -b %BOOST_BRANCH% https://github.com/boostorg/boost.git boost-root
  - cd boost-root
  - git submodule update --init tools/boostdep
  - xcopy /s /e /q %APPVEYOR_BUILD_FOLDER% libs\core\
  - python tools/boostdep/depinst/depinst.py --git_args "--jobs %GIT_FETCH_JOBS%" core
  - cmd /c bootstrap
  - b2 -d0 headers

build: off

test_script:
  - PATH=%ADDPATH%%PATH%
  - if not "%CXXSTD%" == "" set CXXSTD=cxxstd=%CXXSTD%
  - if not "%ADDRMD%" == "" set ADDRMD=address-model=%ADDRMD%
  - if "%CMAKE%%CMAKE_SUBDIR%%CMAKE_INSTALL%" == ""  b2 -j %NUMBER_OF_PROCESSORS% libs/core/test toolset=%TOOLSET% %CXXSTD% %ADDRMD% variant=debug,release embed-manifest-via=linker

  - if not "%CMAKE%" == ""  mkdir __build__ && cd __build__
  - if not "%CMAKE%" == ""  cmake -DBUILD_TESTING=ON -DBOOST_INCLUDE_LIBRARIES=core ..
  - if not "%CMAKE%" == ""  cmake --build . --target tests -j 3 --config Debug & ctest --output-on-failure --no-tests=error -j 3 -C Debug
  - if not "%CMAKE%" == ""  cmake --build . --target tests -j 3 --config Release & ctest --output-on-failure --no-tests=error -j 3 -C Release
  - if not "%CMAKE%" == ""  cmake --build . --target tests -j 3 --config MinSizeRel & ctest --output-on-failure --no-tests=error -j 3 -C MinSizeRel
  - if not "%CMAKE%" == ""  cmake --build . --target tests -j 3 --config RelWithDebInfo & ctest --output-on-failure --no-tests=error -j 3 -C RelWithDebInfo

  - if not "%CMAKE_SUBDIR%" == ""  cd libs/core/test/cmake_subdir_test && mkdir __build__ && cd __build__
  - if not "%CMAKE_SUBDIR%" == ""  cmake ..
  - if not "%CMAKE_SUBDIR%" == ""  cmake --build . --config Debug && cmake --build . --target check --config Debug
  - if not "%CMAKE_SUBDIR%" == ""  cmake --build . --config Release && cmake --build . --target check --config Release
  - if not "%CMAKE_SUBDIR%" == ""  cmake --build . --config MinSizeRel && cmake --build . --target check --config MinSizeRel
  - if not "%CMAKE_SUBDIR%" == ""  cmake --build . --config RelWithDebInfo && cmake --build . --target check --config RelWithDebInfo

  - if not "%CMAKE_INSTALL%" == ""  mkdir __build__ && cd __build__
  - if not "%CMAKE_INSTALL%" == ""  cmake -DBOOST_INCLUDE_LIBRARIES=core -DCMAKE_INSTALL_PREFIX=C:/cmake-prefix ..
  - if not "%CMAKE_INSTALL%" == ""  cmake --build . --target install --config Debug
  - if not "%CMAKE_INSTALL%" == ""  cmake --build . --target install --config Release
  - if not "%CMAKE_INSTALL%" == ""  cd ../libs/core/test/cmake_install_test && mkdir __build__ && cd __build__
  - if not "%CMAKE_INSTALL%" == ""  cmake -DCMAKE_INSTALL_PREFIX=C:/cmake-prefix ..
  - if not "%CMAKE_INSTALL%" == ""  cmake --build . --config Debug && cmake --build . --target check --config Debug
  - if not "%CMAKE_INSTALL%" == ""  cmake --build . --config Release && cmake --build . --target check --config Release
