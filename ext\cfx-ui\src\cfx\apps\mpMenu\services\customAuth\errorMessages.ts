/**
 * Vietnamese Error Message Mapping for FiveM Authentication
 * Maps API error messages and codes to user-friendly Vietnamese messages
 */

export interface ErrorMapping {
  pattern: string | RegExp;
  message: string;
}

// Server Error Codes mapping (theo ERROR_HANDLING_GUIDE.md)
export const serverErrorCodes: Record<string, string> = {
  // Authentication errors
  'EMAIL_EXISTS': 'Email này đã được sử dụng. Vui lòng sử dụng email khác hoặc đăng nhập.',
  'USER_NOT_FOUND': 'Không tìm thấy tài khoản với email này. Vui lòng kiểm tra lại hoặc đăng ký mới.',
  'INVALID_CREDENTIALS': 'Email hoặc mật khẩu không đúng. Vui lòng kiểm tra lại thông tin đăng nhập.',
  'ACCOUNT_LOCKED': 'Tài khoản tạm thời bị khóa do đăng nhập sai quá nhiều lần. <PERSON><PERSON> lòng thử lại sau.',
  'ACCOUNT_UNVERIFIED': 'Tà<PERSON> khoản chưa được xác thực email. Vui lòng kiểm tra email và xác thực tài khoản trước khi đăng nhập.',
  'ALREADY_VERIFIED': 'Tài khoản đã được xác thực. Bạn có thể đăng nhập ngay bây giờ.',

  // Verification errors
  'INVALID_CODE': 'Mã xác thực không đúng. Vui lòng kiểm tra lại mã trong email.',
  'CODE_EXPIRED': 'Mã xác thực đã hết hạn. Vui lòng yêu cầu mã mới.',
  'EMAIL_SEND_FAILED': 'Không thể gửi email xác thực. Vui lòng liên hệ hỗ trợ.',

  // Validation errors
  'VALIDATION_ERROR': 'Thông tin đầu vào không hợp lệ. Vui lòng kiểm tra lại.',
  'INVALID_EMAIL': 'Địa chỉ email không hợp lệ. Vui lòng nhập email đúng định dạng.',
  'INVALID_PASSWORD': 'Mật khẩu không đáp ứng yêu cầu bảo mật. Vui lòng chọn mật khẩu mạnh hơn.',
  'INVALID_USERNAME': 'Tên người dùng không hợp lệ. Vui lòng sử dụng 3-30 ký tự, chỉ chữ cái, số và dấu gạch dưới.',

  // Rate limiting
  'RATE_LIMIT_EXCEEDED': 'Bạn đã thực hiện quá nhiều lần thử. Vui lòng đợi một chút rồi thử lại.',
  'TOO_MANY_ATTEMPTS': 'Quá nhiều lần thử. Vui lòng đợi 15 phút rồi thử lại.',

  // Server errors
  'INTERNAL_ERROR': 'Đã xảy ra lỗi hệ thống. Vui lòng thử lại sau ít phút.',
  'SERVICE_UNAVAILABLE': 'Dịch vụ tạm thời không khả dụng. Vui lòng thử lại sau.',
  'DATABASE_ERROR': 'Lỗi cơ sở dữ liệu. Vui lòng thử lại sau.',
  'NETWORK_ERROR': 'Lỗi kết nối mạng. Vui lòng kiểm tra kết nối internet và thử lại.'
};

// Vietnamese error messages for authentication (legacy patterns)
export const vietnameseErrorMessages: ErrorMapping[] = [
  // Registration errors
  {
    pattern: /username.*already.*exists/i,
    message: 'Tên người dùng này đã được sử dụng. Vui lòng chọn tên khác.'
  },
  {
    pattern: /email.*already.*exists/i,
    message: 'Email này đã được đăng ký. Vui lòng sử dụng email khác hoặc đăng nhập.'
  },
  {
    pattern: /username.*required/i,
    message: 'Vui lòng nhập tên người dùng.'
  },
  {
    pattern: /email.*required/i,
    message: 'Vui lòng nhập địa chỉ email.'
  },
  {
    pattern: /password.*required/i,
    message: 'Vui lòng nhập mật khẩu.'
  },
  {
    pattern: /invalid.*email/i,
    message: 'Địa chỉ email không hợp lệ. Vui lòng kiểm tra lại.'
  },
  {
    pattern: /password.*too.*short/i,
    message: 'Mật khẩu phải có ít nhất 8 ký tự.'
  },
  {
    pattern: /password.*weak/i,
    message: 'Mật khẩu quá yếu. Vui lòng sử dụng mật khẩu mạnh hơn.'
  },
  {
    pattern: /registration.*failed/i,
    message: 'Đăng ký thất bại. Vui lòng thử lại sau.'
  },

  // Login errors
  {
    pattern: /invalid.*credentials/i,
    message: 'Email hoặc mật khẩu không đúng. Vui lòng kiểm tra lại.'
  },
  {
    pattern: /user.*not.*found/i,
    message: 'Không tìm thấy tài khoản với email này.'
  },
  {
    pattern: /account.*not.*verified/i,
    message: 'Tài khoản chưa được xác thực. Vui lòng kiểm tra email để xác thực.'
  },
  {
    pattern: /account.*locked/i,
    message: 'Tài khoản đã bị khóa. Vui lòng liên hệ hỗ trợ.'
  },
  {
    pattern: /login.*failed/i,
    message: 'Đăng nhập thất bại. Vui lòng thử lại.'
  },

  // Verification errors
  {
    pattern: /invalid.*verification.*code/i,
    message: 'Mã xác thực không đúng. Vui lòng kiểm tra lại.'
  },
  {
    pattern: /verification.*code.*expired/i,
    message: 'Mã xác thực đã hết hạn. Vui lòng yêu cầu mã mới.'
  },
  {
    pattern: /verification.*failed/i,
    message: 'Xác thực thất bại. Vui lòng thử lại.'
  },

  // Rate limiting errors
  {
    pattern: /rate.*limit.*exceeded/i,
    message: 'Bạn đã thực hiện quá nhiều lần thử. Vui lòng đợi một chút rồi thử lại.'
  },
  {
    pattern: /too.*many.*attempts/i,
    message: 'Quá nhiều lần thử. Vui lòng đợi 15 phút rồi thử lại.'
  },
  {
    pattern: /account.*temporarily.*locked/i,
    message: 'Tài khoản tạm thời bị khóa do quá nhiều lần thử sai. Vui lòng đợi 30 phút.'
  },

  // Network and server errors
  {
    pattern: /network.*error/i,
    message: 'Lỗi kết nối mạng. Vui lòng kiểm tra kết nối internet và thử lại.'
  },
  {
    pattern: /server.*error/i,
    message: 'Lỗi máy chủ. Vui lòng thử lại sau ít phút.'
  },
  {
    pattern: /service.*unavailable/i,
    message: 'Dịch vụ tạm thời không khả dụng. Vui lòng thử lại sau.'
  },
  {
    pattern: /timeout/i,
    message: 'Kết nối quá chậm. Vui lòng thử lại.'
  },
  {
    pattern: /failed.*to.*fetch/i,
    message: 'Không thể kết nối đến server. Vui lòng kiểm tra kết nối mạng.'
  },
  {
    pattern: /không.*thể.*kết.*nối.*đến.*server/i,
    message: 'Không thể kết nối đến server. Vui lòng kiểm tra kết nối mạng.'
  },
  {
    pattern: /server.*không.*phản.*hồi/i,
    message: 'Server không phản hồi. Vui lòng thử lại sau.'
  },
  {
    pattern: /http.*400/i,
    message: 'Yêu cầu không hợp lệ. Vui lòng kiểm tra thông tin và thử lại.'
  },
  {
    pattern: /http.*401/i,
    message: 'Không có quyền truy cập. Vui lòng đăng nhập lại.'
  },
  {
    pattern: /http.*403/i,
    message: 'Truy cập bị từ chối. Vui lòng liên hệ hỗ trợ.'
  },
  {
    pattern: /http.*404/i,
    message: 'Không tìm thấy dịch vụ. Vui lòng thử lại sau.'
  },
  {
    pattern: /http.*500/i,
    message: 'Lỗi máy chủ nội bộ. Vui lòng thử lại sau.'
  },

  // Password validation errors (from client-side validation)
  {
    pattern: /password.*at least.*8.*characters/i,
    message: 'Mật khẩu phải có ít nhất 8 ký tự.'
  },
  {
    pattern: /password.*lowercase.*letter/i,
    message: 'Mật khẩu phải chứa ít nhất một chữ cái thường.'
  },
  {
    pattern: /password.*uppercase.*letter/i,
    message: 'Mật khẩu phải chứa ít nhất một chữ cái hoa.'
  },
  {
    pattern: /password.*number/i,
    message: 'Mật khẩu phải chứa ít nhất một chữ số.'
  },
  {
    pattern: /password.*special.*character/i,
    message: 'Mật khẩu phải chứa ít nhất một ký tự đặc biệt.'
  },

  // Connection and fetch errors
  {
    pattern: /typeerror.*fetch/i,
    message: 'Lỗi kết nối. Vui lòng kiểm tra mạng và thử lại.'
  },
  {
    pattern: /cors.*error/i,
    message: 'Lỗi cấu hình server. Vui lòng liên hệ hỗ trợ.'
  },
  {
    pattern: /connection.*refused/i,
    message: 'Không thể kết nối đến server. Server có thể đang bảo trì.'
  },
  {
    pattern: /dns.*error/i,
    message: 'Lỗi phân giải tên miền. Vui lòng kiểm tra kết nối internet.'
  },

  // Generic fallback errors
  {
    pattern: /failed/i,
    message: 'Thao tác thất bại. Vui lòng thử lại.'
  },
  {
    pattern: /error/i,
    message: 'Đã xảy ra lỗi. Vui lòng thử lại sau.'
  }
];

/**
 * Transforms API error messages and codes to user-friendly Vietnamese messages
 * @param errorMessage - The original error message from API or validation
 * @param errorCode - Optional error code from server response
 * @returns User-friendly Vietnamese error message
 */
export function getVietnameseErrorMessage(errorMessage: string, errorCode?: string): string {
  if (!errorMessage && !errorCode) {
    return 'Đã xảy ra lỗi không xác định. Vui lòng thử lại.';
  }

  // Ưu tiên xử lý error code từ server trước
  if (errorCode && serverErrorCodes[errorCode]) {
    console.log(`🔍 Mapped error code ${errorCode} to Vietnamese message`);
    return serverErrorCodes[errorCode];
  }

  // Nếu không có error code, thử tìm trong error message
  if (errorMessage) {
    // Kiểm tra xem error message có chứa error code không
    for (const [code, message] of Object.entries(serverErrorCodes)) {
      if (errorMessage.includes(code)) {
        console.log(`🔍 Found error code ${code} in message, using Vietnamese mapping`);
        return message;
      }
    }

    // Tìm matching pattern trong legacy error messages
    for (const mapping of vietnameseErrorMessages) {
      if (typeof mapping.pattern === 'string') {
        if (errorMessage.toLowerCase().includes(mapping.pattern.toLowerCase())) {
          return mapping.message;
        }
      } else {
        // RegExp pattern
        if (mapping.pattern.test(errorMessage)) {
          return mapping.message;
        }
      }
    }
  }

  // Log unmapped errors for debugging
  if (process.env.NODE_ENV === 'development') {
    console.warn('Unmapped error:', { errorMessage, errorCode });
  }

  return 'Đã xảy ra lỗi. Vui lòng thử lại sau.';
}

/**
 * Parse error response từ server và extract error code + message
 * @param errorResponse - Response object từ server
 * @returns Object chứa parsed error info
 */
export function parseServerError(errorResponse: any): { code?: string; message: string; details?: any } {
  // Nếu response có structure chuẩn theo ERROR_HANDLING_GUIDE
  if (errorResponse && typeof errorResponse === 'object') {
    if (errorResponse.code && errorResponse.message) {
      return {
        code: errorResponse.code,
        message: errorResponse.message,
        details: errorResponse.details
      };
    }

    // Nếu có success: false structure
    if (errorResponse.success === false) {
      return {
        code: errorResponse.code,
        message: errorResponse.message || 'Unknown error',
        details: errorResponse.details || errorResponse.errors
      };
    }
  }

  // Fallback cho string error message
  if (typeof errorResponse === 'string') {
    return {
      message: errorResponse
    };
  }

  return {
    message: 'Unknown error occurred'
  };
}

/**
 * Transforms validation errors to Vietnamese messages
 * @param validationErrors - Object containing field validation errors
 * @returns Object with Vietnamese error messages
 */
export function getVietnameseValidationErrors(
  validationErrors: Record<string, string>
): Record<string, string> {
  const vietnameseErrors: Record<string, string> = {};

  for (const [field, error] of Object.entries(validationErrors)) {
    vietnameseErrors[field] = getVietnameseErrorMessage(error);
  }

  return vietnameseErrors;
}
