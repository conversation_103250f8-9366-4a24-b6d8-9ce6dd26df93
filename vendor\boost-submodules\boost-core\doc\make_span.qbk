[/
Copyright 2023 <PERSON>
(glen<PERSON><PERSON>@gmail.com)

Distributed under the Boost Software License, Version 1.0.
(http://www.boost.org/LICENSE_1_0.txt)
]

[section:make_span make_span]

[simplesect Authors]

* <PERSON>

[endsimplesect]

[section Overview]

The header <boost/core/make_span.hpp> provides function templates `make_span`
to conveniently create `span` objects. They are useful before C++17 where Class
Template Argument Deduction (CTAD) is not available.

[endsect]

[section Reference]

```
namespace boost {

template<class I>
constexpr span<I>
make_span(I* d, std::size_t n) noexcept;

template<class I>
constexpr span<I>
make_span(I* b, I* e) noexcept;

template<class T, std::size_t N>
constexpr span<T, N>
make_span(T(&a)[N]) noexcept;

template<class T, std::size_t N>
constexpr span<T, N>
make_span(std::array<T, N>& a) noexcept;

template<class T, std::size_t N>
constexpr span<const T, N>
make_span(const std::array<T, N>& a) noexcept;

template<class R>
span<remove_pointer_t<iterator_t<R> > >
make_span(R&& r);

} /* boost */
```

[section Functions]

[variablelist
[[`template<class I> span<I> make_span(I* f, std::size_t c);`]
[Returns `span<I>(f, c)`.]]
[[`template<class I> span<I> make_span(I* f, I* l);`]
[Returns `span<I>(f, l)`.]]
[[`template<class T, std::size_t N> span<T, N> make_span(T(&a)[N]);`]
[Returns `span<T, N>(a)`.]]
[[`template<class T, std::size_t N> span<T, N>
make_span(std::array<T, N>& a);`]
[Returns `span<T, N>(a)`.]]
[[`template<class T, std::size_t N> span<const T, N>
make_span(const std::array<T, N>& a);`]
[Returns `span<const T, N>(a)`.]]
[[`template<class R>
span<remove_pointer_t<iterator_t<R> > >
make_span(R&& r);`]
[Returns `span<remove_pointer_t<iterator_t<R> > >(std::forward<R>(r))`.]]]

[endsect]

[endsect]

[endsect]
