# Copyright 2017 The Chromium Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

mojom = "//mojo/public/mojom/base/thread_priority.mojom"
public_headers = [ "//base/threading/platform_thread.h" ]
traits_headers = [ "//mojo/public/cpp/base/thread_priority_mojom_traits.h" ]
sources = [
  "//mojo/public/cpp/base/thread_priority_mojom_traits.cc",
  "//mojo/public/cpp/base/thread_priority_mojom_traits.h",
]

type_mappings = [ "mojo_base.mojom.ThreadPriority=::base::ThreadPriority" ]
