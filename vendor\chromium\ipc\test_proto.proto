// Copyright 2017 The Chromium Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

syntax = "proto2";
option optimize_for = LITE_RUNTIME;
package ipc_message_utils_test;

// This is a simple dummy protocol buffer that is used for testing handling of
// protocol buffers in ipc_message_utils.

message TestMessage1 {
    optional int32 number = 1;
}

message TestMessage2 {
    repeated int32 numbers = 1;
    repeated string strings = 2;
    repeated TestMessage1 messages = 3;
}
