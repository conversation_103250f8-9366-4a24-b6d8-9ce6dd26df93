import 'backend/fs/fs.contribution';
import 'backend/app/app.contribution';
import 'backend/api/api.contribution';
import 'backend/git/git.contribution';
import 'backend/game/game.contribution';
import 'backend/task/task.contribution';
import 'backend/logger/logger.contribution';
import 'backend/status/status.contribution';
import 'backend/github/github.contribution';
import 'backend/output/output.contribution';
import 'backend/updater/updater.contribution';
import 'fxdk/project/node/project.contribution';
import 'backend/explorer/explorer.contribution';
import 'backend/features/features.contribution';
import 'backend/game-server/game-server.contribution';
import 'backend/world-editor/world-editor.contribution';
import 'backend/notification/notification.contribution';
import 'backend/system-resources/system-resources.contribution';

import 'backend/fxcode/fxcode.contribution';

import 'fxdk/contrib/importer/node/importer.contribution';

import 'backend/shell-backend';

// assets
import 'fxdk/contrib/assets/fxworld/node/fxworld.contribution';
import 'fxdk/contrib/assets/resource/node/resource.contribution';
