import { Page } from '@cfx-dev/ui-components';
import { observer } from 'mobx-react-lite';

export const ServicesPage = observer(function ServicesPage() {
  const services = [
    {
      title: 'Server Hosting',
      description: 'High-performance game servers with 99.9% uptime guarantee',
      features: ['DDoS Protection', 'Auto-scaling', '24/7 Monitoring'],
      icon: '🚀'
    },
    {
      title: 'Custom Development',
      description: 'Tailored gaming solutions built to your specifications',
      features: ['Custom Scripts', 'UI/UX Design', 'Performance Optimization'],
      icon: '⚡'
    },
    {
      title: '24/7 Support',
      description: 'Round-the-clock assistance from our expert team',
      features: ['Live Chat', 'Discord Support', 'Priority Response'],
      icon: '🛡️'
    }
  ];

  return (
    <Page>
      <div style={{
        padding: '3rem',
        textAlign: 'center',
        color: 'white',
        background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%)',
        backdropFilter: 'blur(25px)',
        borderRadius: '24px',
        margin: '2rem',
        boxShadow: '0 32px 64px -12px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.2) inset',
        border: '1px solid rgba(255, 255, 255, 0.2)'
      }}>
        <h1 style={{
          fontSize: '3rem',
          marginBottom: '1.5rem',
          background: 'linear-gradient(135deg, #ffffff 0%, #f7fafc 100%)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          backgroundClip: 'text',
          fontWeight: '800',
          letterSpacing: '-0.02em'
        }}>Our Services</h1>
        <p style={{
          fontSize: '1.3rem',
          lineHeight: '1.7',
          maxWidth: '900px',
          margin: '0 auto 3rem auto',
          opacity: '0.9',
          fontWeight: '500'
        }}>
          We offer a comprehensive range of premium gaming services designed to enhance
          your experience and take your gameplay to the next level.
        </p>
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(320px, 1fr))',
          gap: '2rem'
        }}>
          {services.map((service, index) => (
            <div key={index} style={{
              background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.06) 100%)',
              padding: '2.5rem',
              borderRadius: '20px',
              backdropFilter: 'blur(20px)',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
              cursor: 'pointer',
              textAlign: 'left'
            }}>
              <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>{service.icon}</div>
              <h3 style={{
                fontSize: '1.75rem',
                marginBottom: '1rem',
                fontWeight: '700',
                color: 'white'
              }}>{service.title}</h3>
              <p style={{
                opacity: '0.85',
                lineHeight: '1.6',
                marginBottom: '1.5rem',
                fontSize: '1.1rem'
              }}>{service.description}</p>
              <ul style={{
                listStyle: 'none',
                padding: 0,
                margin: 0
              }}>
                {service.features.map((feature, featureIndex) => (
                  <li key={featureIndex} style={{
                    padding: '0.5rem 0',
                    opacity: '0.8',
                    fontSize: '0.95rem',
                    display: 'flex',
                    alignItems: 'center'
                  }}>
                    <span style={{ marginRight: '0.5rem', color: '#4ade80' }}>✓</span>
                    {feature}
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      </div>
    </Page>
  );
});
