<?xml version='1.0'?>
<!DOCTYPE html PUBLIC '-//W3C//DTD XHTML 1.1 plus MathML 2.0//EN'
  'http://www.w3.org/TR/MathML2/dtd/xhtml-math11-f.dtd'
  [<!ENTITY mathml 'http://www.w3.org/1998/Math/MathML'>]>
<html xmlns='http://www.w3.org/1999/xhtml'>
<head><title>asinh1</title>
<!-- MathML created with MathCast Equation Editor version 0.89 -->
</head>
<body>
<math xmlns="http://www.w3.org/1998/Math/MathML" display="block">
  <mrow>
    <mtext>asinh</mtext>
    <mfenced>
      <mrow>
        <mi>x</mi>
      </mrow>
    </mfenced>
    <mo>=</mo>
    <mi>ln</mi>
    <mfenced>
      <mrow>
        <mi>x</mi>
        <mo>+</mo>
        <msqrt>
          <mrow>
            <msup>
              <mi>x</mi>
              <mn>2</mn>
            </msup>
            <mo>+</mo>
            <mn>1</mn>
          </mrow>
        </msqrt>
      </mrow>
    </mfenced>
  </mrow>
</math>
</body>
</html>
