.root {
  position: relative;

  width: 100%;
  height: 100%;

  background-color: ui.color-token('backdrop-300');

  &::before,
  &::after {
    position: absolute;

    left: 0;
    right: 0;

    height: ui.offset('small');

    z-index: 2;

    content: '';
    display: block;
  }

  &::before {
    top: 0;

    background-image: linear-gradient(0deg, transparent, ui.color-token('shadow-small'));
  }

  &::after {
    bottom: 0;

    background-image: linear-gradient(180deg, transparent, ui.color-token('shadow-small'));
  }

  * {
    user-select: text;
  }

  .wrapper {
    position: absolute;

    inset: 0;

    overflow-y: scroll;

    z-index: 1;
  }
}
