[section:error_eg <PERSON><PERSON>r Handling Example]

See [link math_toolkit.error_handling error handling documentation]
for a detailed explanation of the mechanism of handling errors,
including the common "bad" arguments to distributions and functions,
and how to use __policy_section to control it.

But, by default, *exceptions will be raised*, for domain errors,
pole errors, numeric overflow, and internal evaluation errors.
To avoid the exceptions from getting thrown and instead get 
an appropriate value returned, usually a NaN (domain errors
pole errors or internal errors), or infinity (from overflow), 
you need to change the policy.

[import ../../example/error_handling_example.cpp]

[error_handling_example]

[caution If throwing of exceptions is enabled (the default) but 
you do *not* have try & catch block,
then the program will terminate with an uncaught exception and probably abort.

Therefore to get the benefit of helpful error messages, enabling *all exceptions
and using try & catch* is recommended for most applications.

However, for simplicity, the is not done for most examples.]
  
[endsect] [/section:error_eg Error Handling Example]
[/ 
  Copyright 2007 <PERSON> and <PERSON>.
  Distributed under the Boost Software License, Version 1.0.
  (See accompanying file LICENSE_1_0.txt or copy at
  http://www.boost.org/LICENSE_1_0.txt).
]

