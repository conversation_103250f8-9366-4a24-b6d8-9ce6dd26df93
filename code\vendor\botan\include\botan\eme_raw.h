/*
* (C) 2015 <PERSON>
*
* <PERSON><PERSON> is released under the Simplified BSD License (see license.txt)
*/

#ifndef BOTAN_EME_RAW_H_
#define BOTAN_EME_RAW_H_

#include <botan/eme.h>

BOTAN_FUTURE_INTERNAL_HEADER(eme_raw.h)

namespace Botan {

class BOTAN_PUBLIC_API(2,0) EME_Raw final : public EME
   {
   public:
      size_t maximum_input_size(size_t i) const override;

      EME_Raw() = default;
   private:
      secure_vector<uint8_t> pad(const uint8_t[], size_t, size_t,
                             RandomNumberGenerator&) const override;

      secure_vector<uint8_t> unpad(uint8_t& valid_mask,
                                const uint8_t in[],
                                size_t in_len) const override;
   };

}

#endif
