<math  xmlns="http://www.w3.org/1998/Math/MathML" display="block" ><mrow >                          <msub><mrow ><mi >J</mi></mrow><mrow ><mi >&#x03BD;</mi></mrow></msub ><mrow ><mo class="MathClass-open">(</mo><mrow><mi >z</mi></mrow><mo class="MathClass-close">)</mo></mrow> <mo class="MathClass-rel">=</mo><msup><mrow > <mfenced separators="" open="("  close=")" ><mrow><mfrac><mrow ><mn>1</mn></mrow><mrow ><mn>2</mn></mrow></mfrac><mi >z</mi></mrow></mfenced></mrow><mrow ><mi >&#x03BD;</mi></mrow></msup ><munderover accentunder="false" accent="false"><mrow  ><mo mathsize="big" > &#x2211;</mo></mrow><mrow ><mi >k</mi><mo class="MathClass-rel">=</mo><mn>0</mn></mrow><mrow ><mi >&#x221E;</mi></mrow></munderover >   <mfrac><mrow ><msup><mrow ><mrow ><mo class="MathClass-open">(</mo><mrow><mo class="MathClass-bin">&#x2212;</mo><mfrac><mrow ><mn>1</mn></mrow><mrow ><mn>4</mn></mrow></mfrac><msup><mrow ><mi >z</mi></mrow><mrow ><mn>2</mn></mrow></msup ></mrow><mo class="MathClass-close">)</mo></mrow></mrow><mrow ><mi >k</mi></mrow></msup ></mrow><mrow ><mi >k</mi><mo class="MathClass-punc">!</mo><mi >&#x0393;</mi><mrow ><mo class="MathClass-open">(</mo><mrow><mi >&#x03BD;</mi> <mo class="MathClass-bin">+</mo> <mi >k</mi> <mo class="MathClass-bin">+</mo> <mn>1</mn></mrow><mo class="MathClass-close">)</mo></mrow></mrow></mfrac></mrow></math>