# Copyright 2018, 2019, 2021 <PERSON> Di<PERSON>v
# Distributed under the Boost Software License, Version 1.0.
# See accompanying file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt

cmake_minimum_required(VERSION 3.5...3.20)

project(cmake_install_test LANGUAGES CXX)

find_package(boost_container_hash REQUIRED)

add_executable(quick ../quick.cpp)
target_link_libraries(quick Boost::container_hash)

enable_testing()
add_test(quick quick)

add_custom_target(check COMMAND ${CMAKE_CTEST_COMMAND} --output-on-failure -C $<CONFIG>)
