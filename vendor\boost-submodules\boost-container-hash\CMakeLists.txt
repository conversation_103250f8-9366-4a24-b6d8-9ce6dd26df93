# Generated by `boostdep --cmake container_hash`
# Copyright 2020, 2021 <PERSON> Di<PERSON>v
# Distributed under the Boost Software License, Version 1.0.
# https://www.boost.org/LICENSE_1_0.txt

cmake_minimum_required(VERSION 3.8...3.20)

project(boost_container_hash VERSION "${BOOST_SUPERPROJECT_VERSION}" LANGUAGES CXX)

add_library(boost_container_hash INTERFACE)
add_library(Boost::container_hash ALIAS boost_container_hash)

target_include_directories(boost_container_hash INTERFACE include)

target_link_libraries(boost_container_hash
  INTERFACE
    Boost::config
    Boost::describe
    Boost::mp11
)

target_compile_features(boost_container_hash INTERFACE cxx_std_11)

if(BUILD_TESTING AND EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/test/CMakeLists.txt")

  add_subdirectory(test)

endif()
