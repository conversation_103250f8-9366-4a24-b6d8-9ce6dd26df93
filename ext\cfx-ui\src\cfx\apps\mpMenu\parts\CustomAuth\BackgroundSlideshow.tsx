import React, { useState, useEffect } from 'react';
import s from './BackgroundSlideshow.module.scss';

interface BackgroundSlideshowProps {
  images: string[];
  interval?: number; // milliseconds
  fadeDuration?: number; // milliseconds
}

export const BackgroundSlideshow: React.FC<BackgroundSlideshowProps> = ({
  images,
  interval = 5000, // 5 seconds default
  fadeDuration = 1000, // 1 second fade default
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [nextIndex, setNextIndex] = useState(1);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [imagesLoaded, setImagesLoaded] = useState(false);

  // Shuffle array function for random order
  const shuffleArray = (array: string[]) => {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  };

  // Create shuffled images array
  const [shuffledImages, setShuffledImages] = useState(() => shuffleArray(images));

  // Preload images for smooth transitions
  useEffect(() => {
    const preloadImages = async () => {
      const imagePromises = images.map((src) => {
        return new Promise((resolve, reject) => {
          const img = new Image();
          img.onload = resolve;
          img.onerror = reject;
          img.src = src;
        });
      });

      try {
        await Promise.all(imagePromises);
        setImagesLoaded(true);
        console.log('✅ All background images preloaded successfully');
      } catch (error) {
        console.warn('⚠️ Some background images failed to preload:', error);
        setImagesLoaded(true); // Continue anyway
      }
    };

    preloadImages();
  }, [images]);

  // Re-shuffle when we reach the end
  useEffect(() => {
    if (currentIndex >= shuffledImages.length - 1) {
      setShuffledImages(shuffleArray(images));
    }
  }, [currentIndex, images, shuffledImages.length]);

  useEffect(() => {
    if (images.length <= 1 || !imagesLoaded) return;

    const timer = setInterval(() => {
      // Start transition
      setIsTransitioning(true);

      // Calculate next index
      const nextIdx = (currentIndex + 1) % shuffledImages.length;
      setNextIndex(nextIdx);

      // After fade duration, complete the transition
      setTimeout(() => {
        setCurrentIndex(nextIdx);
        setIsTransitioning(false);
      }, fadeDuration);
    }, interval);

    return () => clearInterval(timer);
  }, [currentIndex, interval, shuffledImages.length, images.length, imagesLoaded, fadeDuration]);

  if (images.length === 0) {
    return null;
  }

  // Show loading state until images are preloaded
  if (!imagesLoaded) {
    return (
      <div className={s.slideshow}>
        <div
          className={`${s.backgroundImage} ${s.current}`}
          style={{
            backgroundImage: `url(${shuffledImages[0]})`,
            opacity: 0.7
          }}
        />
      </div>
    );
  }

  const currentImage = shuffledImages[currentIndex];
  const nextImage = shuffledImages[nextIndex];

  return (
    <div className={s.slideshow}>
      {/* Current image */}
      <div
        className={`${s.backgroundImage} ${s.current}`}
        style={{
          backgroundImage: `url(${currentImage})`,
          opacity: isTransitioning ? 0 : 1,
          transition: `opacity ${fadeDuration}ms ease-in-out`
        }}
      />

      {/* Next image for smooth crossfade */}
      <div
        className={`${s.backgroundImage} ${s.next}`}
        style={{
          backgroundImage: `url(${nextImage})`,
          opacity: isTransitioning ? 1 : 0,
          transition: `opacity ${fadeDuration}ms ease-in-out`
        }}
      />
    </div>
  );
};
