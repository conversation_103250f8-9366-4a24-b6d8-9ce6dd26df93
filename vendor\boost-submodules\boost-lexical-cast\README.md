# [Boost.LexicalCast](https://boost.org/libs/lexical_cast)
Boost.LexicalCast is one of the [Boost C++ Libraries](https://github.com/boostorg). This library is meant for general literal text conversions, such as an int represented a string, or vice-versa.

### Test results

@               | Build         | Tests coverage | More info
----------------|-------------- | -------------- |-----------
Develop branch: | [![CI](https://github.com/boostorg/lexical_cast/actions/workflows/ci.yml/badge.svg?branch=develop)](https://github.com/boostorg/lexical_cast/actions/workflows/ci.yml) [![Build status](https://ci.appveyor.com/api/projects/status/mwwanh1bpsnuv38h/branch/develop?svg=true)](https://ci.appveyor.com/project/apolukhin/lexical-cast/branch/develop)  | [![Coverage Status](https://coveralls.io/repos/boostorg/lexical_cast/badge.png?branch=develop)](https://coveralls.io/r/boostorg/lexical_cast?branch=develop) | [details...](https://www.boost.org/development/tests/develop/developer/lexical_cast.html)
Master branch:  | [![CI](https://github.com/boostorg/lexical_cast/actions/workflows/ci.yml/badge.svg?branch=master)](https://github.com/boostorg/lexical_cast/actions/workflows/ci.yml) [![Build status](https://ci.appveyor.com/api/projects/status/mwwanh1bpsnuv38h/branch/master?svg=true)](https://ci.appveyor.com/project/apolukhin/lexical-cast/branch/master)  | [![Coverage Status](https://coveralls.io/repos/boostorg/lexical_cast/badge.png?branch=master)](https://coveralls.io/r/boostorg/lexical_cast?branch=master) | [details...](https://www.boost.org/development/tests/master/developer/lexical_cast.html)

[Latest developer documentation](https://www.boost.org/doc/libs/develop/doc/html/boost_lexical_cast.html)

### License

Distributed under the [Boost Software License, Version 1.0](https://boost.org/LICENSE_1_0.txt).
