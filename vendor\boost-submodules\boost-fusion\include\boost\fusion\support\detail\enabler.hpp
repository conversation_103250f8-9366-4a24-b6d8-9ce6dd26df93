/*=============================================================================
    Copyright (c) 2015 <PERSON><PERSON><PERSON>

    Distributed under the Boost Software License, Version 1.0. (See accompanying
    file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
==============================================================================*/

#ifndef BOOST_FUSION_SUPPORT_DETAIL_ENABLER_12102015_0346
#define BOOST_FUSION_SUPPORT_DETAIL_ENABLER_12102015_0346

#include <boost/config.hpp>

namespace boost { namespace fusion { namespace detail
{

struct enabler_ {};
BOOST_STATIC_CONSTEXPR enabler_ enabler = {};

}}}

#endif

