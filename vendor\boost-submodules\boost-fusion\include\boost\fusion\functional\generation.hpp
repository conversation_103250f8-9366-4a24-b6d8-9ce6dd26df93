/*=============================================================================
    Copyright (c) 2007 Tobias <PERSON>winger
  
    Use modification and distribution are subject to the Boost Software 
    License, Version 1.0. (See accompanying file LICENSE_1_0.txt or copy at
    http://www.boost.org/LICENSE_1_0.txt).
==============================================================================*/

#if !defined(BOOST_FUSION_FUNCTIONAL_GENERATION_HPP_INCLUDED)
#define BOOST_FUSION_FUNCTIONAL_GENERATION_HPP_INCLUDED

#include <boost/fusion/support/config.hpp>
#include <boost/fusion/functional/generation/make_fused.hpp>
#include <boost/fusion/functional/generation/make_fused_procedure.hpp>
#include <boost/fusion/functional/generation/make_fused_function_object.hpp>
#include <boost/fusion/functional/generation/make_unfused.hpp>

#endif
