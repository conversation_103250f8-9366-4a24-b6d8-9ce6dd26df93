/*
 * This file is part of the CitizenFX project - http://citizen.re/
 *
 * See LICENSE and MENTIONS in the root of the source tree for information
 * regarding licensing.
 */

#include "StdInc.h"
#include "ToolComponentHelpers.h"

#include <boost/filesystem.hpp>

#ifdef IS_RDR3
#undef RAGE_FORMATS_GAME_NY
#undef RAGE_FORMATS_GAME
#define RAGE_FORMATS_GAME five
#define RAGE_FORMATS_GAME_FIVE
#include <gtaDrawable.h>
#include <phBound.h>
#include <fragType.h>

#undef RAGE_FORMATS_GAME_FIVE
#undef RAGE_FORMATS_GAME
#define RAGE_FORMATS_GAME rdr3
#define RAGE_FORMATS_GAME_RDR3
#include <gtaDrawable.h>
#include <phBound.h>
#include <fragType.h>

#include <convert/gtaDrawable_five_rdr3.h>
#include <convert/phBound_five_rdr3.h>

#include <optional>

rage::five::BlockMap* UnwrapRSC7(const wchar_t* fileName, rage::five::ResourceFlags* flags);

template<typename T>
static bool OutputFile(const T&& callback, int fileVersion, const std::wstring& fileName)
{
	auto bm = rage::rdr3::pgStreamManager::BeginPacking();

	callback();

	rage::rdr3::pgStreamManager::EndPacking();

	FILE* f = _wfopen(fileName.c_str(), L"wb");

	if (!f)
	{
		printf("... couldn't open output file for writing.\n");
		return false;
	}

	size_t outputSize = 0;

	bm->Save(fileVersion, [&](const void* d, size_t s)
	{
		fwrite(d, 1, s, f);

		outputSize += s;
	});

	wprintf(L"written %s successfully - compressed size %d\n", boost::filesystem::path(fileName).filename().c_str(), outputSize);

	fclose(f);

	return true;
}

template<typename TOutput, typename TInput, typename TBlockMap>
static bool AutoConvert(TBlockMap blockMap, const std::wstring& fileName, int fileVersion, const wchar_t* fileExtOverride = nullptr, const std::function<void(TOutput*)>& postCb = {})
{
	std::wstring fileExt = std::wstring(wcsrchr(fileName.c_str(), L'.'));

	if (fileExtOverride)
	{
		fileExt = fileExtOverride;
	}

	std::wstring outFileName(fileName);
	outFileName = outFileName.substr(0, outFileName.find_last_of('.')) + L"_nya.y" + fileExt.substr(2);

	return OutputFile([&]()
	{
		auto tgt = rage::convert<TOutput*>((TInput*)blockMap->blocks[0].data);

		if (postCb)
		{
			postCb(tgt);
		}
	},
	fileVersion, outFileName);
}

struct GameConfig_Five
{
	static auto UnwrapRSC(const wchar_t* fileName)
	{
		rage::five::ResourceFlags rf;
		return UnwrapRSC7(fileName, &rf);
	}

	using StreamManager = rage::five::pgStreamManager;
	using TBound = rage::five::phBound;
	using TDrawable = rage::five::gtaDrawable;
	using TFragment = rage::five::fragType;
	using TTxd = rage::five::pgDictionary<rage::five::grcTexturePC>;
	using TDwd = rage::five::pgDictionary<rage::five::gtaDrawable>;
};

template<typename TConfig>
static void ConvertFile(const boost::filesystem::path& path)
{
	std::wstring fileNameStr = path.wstring();
	const wchar_t* fileName = fileNameStr.c_str();

	auto bm = TConfig::UnwrapRSC(fileName);

	if (!bm)
	{
		wprintf(L"couldn't open input file %s...\n", path.filename().c_str());
		return;
	}

	std::wstring fileExt = std::wstring(wcsrchr(fileName, L'.'));

	TConfig::StreamManager::SetBlockInfo(bm);

	int fileVersion = 0;

	if (fileExt == L".ydr")
	{
		wprintf(L"converting drawable %s...\n", path.filename().c_str());

		AutoConvert<rage::rdr3::gtaDrawable, typename TConfig::TDrawable>(bm, fileName, 162);
	}
	else if (fileExt == L".yft")
	{
		wprintf(L"converting fragtype %s...\n", path.filename().c_str());

		AutoConvert<rage::rdr3::gtaDrawable, typename TConfig::TFragment>(bm, fileName, 162);
	}
	else if (fileExt == L".ybn")
	{
		wprintf(L"converting bound %s...\n", path.filename().c_str());

		AutoConvert<rage::rdr3::phBound, typename TConfig::TBound>(bm, fileName, 47);
	}
	else if (fileExt == L".ydd")
	{
		wprintf(L"converting dwd %s...\n", path.filename().c_str());

		AutoConvert<rage::rdr3::pgDictionary<rage::rdr3::gtaDrawable>, typename TConfig::TDwd>(bm, fileName, 162);
	}
	else if (fileExt == L".ytd")
	{
		wprintf(L"converting txd %s...\n", path.filename().c_str());

		AutoConvert<rage::rdr3::pgDictionary<rage::rdr3::grcTexturePC>, typename TConfig::TTxd>(bm, fileName, 2);
	}
	else
	{
		printf("unknown file extension...\n");

		return;
	}

	for (int i = 0; i < bm->physicalLen + bm->virtualLen; i++)
	{
		delete bm->blocks[i].data;
	}

	delete bm;
}

static void FormatsConvert_HandleArguments(boost::program_options::wcommand_line_parser& parser, std::function<void()> cb)
{
	boost::program_options::options_description desc;

	desc.add_options()("filename", boost::program_options::wvalue<std::vector<boost::filesystem::path>>()->required(), "The path of the file to convert.");

	boost::program_options::positional_options_description positional;
	positional.add("filename", -1);

	parser.options(desc).positional(positional);

	cb();
}

static void FormatsConvert_Run(const boost::program_options::variables_map& map)
{
	if (map.count("filename") == 0)
	{
		printf("Usage:\n\n   redm formats:convert filename<1-n>...\n\nCurrently, GTA:Five .#bn files are supported.\nSee your vendor for details.\n");
		return;
	}

	auto& entries = map["filename"].as<std::vector<boost::filesystem::path>>();

	for (auto& filePath : entries)
	{
		ConvertFile<GameConfig_Five>(filePath);
	}
}

static FxToolCommand formatsConvert("formats:convert", FormatsConvert_HandleArguments, FormatsConvert_Run);

#ifdef TXD_TEST
#undef RAGE_FORMATS_GAME_FIVE
#undef RAGE_FORMATS_GAME
#define RAGE_FORMATS_GAME rdr3
#define RAGE_FORMATS_GAME_RDR3

#include <gtaDrawable.h>

static void FormatsConvert_HandleArguments(boost::program_options::wcommand_line_parser& parser, std::function<void()> cb)
{
	boost::program_options::options_description desc;

	desc.add_options()
		("dummy", boost::program_options::wvalue<std::vector<boost::filesystem::path>>(), "The path of the file to convert.");

	parser.options(desc);

	cb();
}

template<typename T>
static bool OutputFile(const T&& callback, int fileVersion, const std::wstring& fileName)
{
	auto bm = rage::rdr3::pgStreamManager::BeginPacking();

	callback();

	rage::rdr3::pgStreamManager::EndPacking();

	FILE* f = _wfopen(fileName.c_str(), L"wb");

	if (!f)
	{
		printf("... couldn't open output file for writing.\n");
		return false;
	}

	size_t outputSize = 0;

	bm->Save(fileVersion, [&](const void* d, size_t s)
	{
		fwrite(d, 1, s, f);

		outputSize += s;
	});

	wprintf(L"written %s successfully - compressed size %d\n", boost::filesystem::path(fileName).filename().c_str(), outputSize);

	fclose(f);

	return true;
}

unsigned char dxt[14400] = {
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x20, 0x80, 0x3F, 0xA0, 0x0E, 0xE0, 0x02, 0x10, 0x01, 0x00, 0xE0, 0xF9, 0x01, 0x00, 0x00, 0xC0,
	0x10, 0xE0, 0x03, 0x0E, 0x30, 0xF0, 0x03, 0xFE, 0xFF, 0xFF, 0x01, 0x00, 0x20, 0x59, 0xDD, 0xFF,
	0x10, 0xE0, 0x83, 0x0E, 0x2C, 0xF0, 0x03, 0xFE, 0xFF, 0xFF, 0x01, 0x10, 0x00, 0xEE, 0xF0, 0xBF,
	0x20, 0x80, 0x3F, 0xA0, 0x0E, 0xD8, 0x02, 0x78, 0x00, 0x00, 0x06, 0x7E, 0x00, 0x00, 0x00, 0x03,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x80, 0x20, 0xF8, 0x1F, 0x00, 0xDF, 0x01, 0xA8, 0x16, 0x00, 0x00, 0x00, 0x98, 0x19, 0x18, 0x78,
	0xC0, 0xFF, 0x9F, 0x5E, 0xC7, 0x6E, 0x01, 0xFF, 0x01, 0xD5, 0x30, 0xFD, 0xB1, 0xFF, 0xF5, 0xFF,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x1F, 0x70, 0x60, 0xC1, 0x0F, 0x80, 0x01, 0x00, 0x00, 0xF4, 0x8F, 0xFD, 0x40, 0x0F, 0xE8,
	0xA0, 0x80, 0x3F, 0x00, 0x00, 0xD8, 0x02, 0xA8, 0x03, 0x00, 0x06, 0x06, 0x00, 0x00, 0x03, 0x03,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x90, 0xE0, 0x03, 0x0E, 0x2C, 0xD0, 0x02, 0x80, 0x00, 0x81, 0xC1, 0x0F, 0xFC, 0xF8, 0x8F, 0xFF,
	0x80, 0xD0, 0xFF, 0xFF, 0x73, 0xCE, 0xB9, 0xB5, 0xD6, 0xDA, 0xFF, 0xFB, 0xFB, 0xFF, 0x81, 0x01,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xC0, 0xFF, 0x9F, 0x4E, 0xDF, 0x6E, 0xFF, 0x1D, 0x00, 0xF1, 0x00, 0x90, 0x00, 0x30, 0x00, 0x10,
	0x20, 0x7F, 0x00, 0x1D, 0xB0, 0x05, 0x00, 0x0C, 0xF9, 0xE1, 0xE1, 0xE1, 0x01, 0x00, 0x01, 0x03,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0xE0, 0x03, 0x0E, 0x2C, 0xF0, 0xFB, 0xF9, 0xFF, 0xFF, 0x41, 0x0C, 0xE8, 0x00, 0x0F, 0xF4,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x1F, 0x70, 0x60, 0x81, 0x0E, 0x80, 0x81, 0x81, 0x81, 0xFF, 0xAF, 0xFF, 0xF1, 0x8F, 0xFE,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x40, 0xC0, 0x1F, 0x40, 0x07, 0x6C, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xF0, 0x00, 0xF0,
	0xC0, 0xFF, 0x9F, 0x4E, 0xDF, 0x6E, 0x01, 0xFF, 0x21, 0xFD, 0x40, 0xFE, 0x70, 0xFF, 0xA0, 0xFF,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xC0, 0xFF, 0x9F, 0xCE, 0xDE, 0x06, 0xFF, 0xFF, 0x21, 0x06, 0x90, 0x2D, 0xE3, 0x3E, 0xF8, 0x4E,
	0x10, 0x1F, 0x74, 0x70, 0xC1, 0x0F, 0x80, 0x01, 0x00, 0x00, 0xE8, 0x0F, 0xFE, 0xD8, 0x8F, 0xFD,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0xE0, 0x03, 0x0E, 0x2C, 0x50, 0x80, 0xE1, 0xE1, 0xE1, 0x01, 0x00, 0x00, 0x00, 0x00, 0xE0,
	0x80, 0xC2, 0xFF, 0xFF, 0x73, 0xCE, 0xB9, 0xB5, 0xD6, 0x92, 0xFC, 0xF9, 0x1B, 0x0A, 0x04, 0x06,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x48, 0xFC, 0xFF, 0xFF, 0x3F, 0x7D, 0xEE, 0x9E, 0x6F, 0xB3, 0xC7, 0xEC, 0xAF, 0x0B, 0xAB, 0x6A,
	0x18, 0xFC, 0xFF, 0xFF, 0xBF, 0x9B, 0x1D, 0x8F, 0x1B, 0x83, 0xD2, 0xE9, 0xFB, 0x7C, 0x7E, 0x7E,
	0xC0, 0xFF, 0x9F, 0x4E, 0xDF, 0x6E, 0xFF, 0x80, 0x50, 0xFF, 0x40, 0xFF, 0x40, 0xFE, 0x30, 0xFE,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0xE0, 0x03, 0x0E, 0x2C, 0xC0, 0xE3, 0xF9, 0xF9, 0xF9, 0x01, 0x04, 0x80, 0x00, 0x0C, 0xE8,
	0xA0, 0xFF, 0x7F, 0xFE, 0xBF, 0xDD, 0xA2, 0xA3, 0xFB, 0xFF, 0xFF, 0xFF, 0x01, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xC0, 0xFF, 0x9F, 0xCE, 0xDE, 0x06, 0xFF, 0xFF, 0x01, 0xE3, 0x00, 0xF6, 0x00, 0xFA, 0x20, 0xFD,
	0x18, 0xFC, 0xFF, 0xFF, 0x3F, 0xBB, 0x2D, 0x8F, 0x07, 0x87, 0xD6, 0x69, 0xA3, 0xA1, 0xC1, 0x60,
	0x80, 0xE0, 0xFF, 0xFF, 0x73, 0xCE, 0xB9, 0xB5, 0xD6, 0xFE, 0x00, 0xF0, 0x7B, 0x78, 0x78, 0x78,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x40, 0xC0, 0x1F, 0x40, 0x07, 0x6C, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xF0, 0x00, 0xF0,
	0xF0, 0xFF, 0xF3, 0x0F, 0x7E, 0x2B, 0x1A, 0x00, 0x00, 0x00, 0x00, 0x8F, 0xF8, 0xD8, 0x8F, 0xFE,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xC0, 0xFF, 0x9F, 0xCE, 0xDE, 0x06, 0xFF, 0xFF, 0x41, 0xFF, 0x80, 0xFF, 0xB1, 0xFF, 0xD2, 0xFF,
	0xC0, 0xFF, 0x9F, 0x4D, 0x0F, 0x6A, 0xFF, 0xFF, 0x01, 0xD2, 0x00, 0xE2, 0x00, 0xE4, 0x00, 0xF5,
	0xC0, 0xFF, 0x9F, 0x3E, 0xDF, 0x6E, 0xFF, 0x00, 0x41, 0xFE, 0x50, 0xFF, 0x50, 0xFF, 0x60, 0xFF,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x80, 0x0F, 0x00, 0xFF, 0x03, 0xC0, 0x35, 0x00, 0xD6, 0x02, 0x20, 0x00, 0x03, 0x00, 0x00, 0x3C,
	0x80, 0x0F, 0x00, 0xFF, 0x03, 0xC0, 0x39, 0x00, 0xD6, 0x02, 0x70, 0x06, 0x03, 0x00, 0x00, 0x36,
	0x10, 0xE0, 0x03, 0x0E, 0x2C, 0xE0, 0x00, 0x00, 0x00, 0xFE, 0x01, 0x00, 0x00, 0x00, 0xF0, 0x9B,
	0x20, 0x80, 0x3F, 0xA0, 0x0E, 0xD8, 0x02, 0x4C, 0x00, 0x00, 0x00, 0xFE, 0x01, 0x00, 0x00, 0x07,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x80, 0x03, 0x00, 0xFF, 0x03, 0xC0, 0x39, 0x00, 0xD6, 0x02, 0x40, 0x01, 0x83, 0xE1, 0x61, 0x79,
	0xC0, 0xFF, 0x9F, 0x4E, 0xDF, 0x6E, 0x47, 0x7F, 0xE1, 0xFF, 0xF7, 0xFF, 0xFD, 0xFF, 0xFF, 0xFF,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xC0, 0xFF, 0x9F, 0x2E, 0xDF, 0x5A, 0xFF, 0xFF, 0x25, 0x02, 0x22, 0x22, 0x22, 0x62, 0x22, 0xF0,
	0x88, 0xFE, 0xFF, 0xFF, 0x7F, 0x9C, 0xCD, 0x76, 0x4B, 0x83, 0xC1, 0xE3, 0x03, 0x0D, 0xF3, 0xC1,
	0xC0, 0xFF, 0x9F, 0x4D, 0x0F, 0x6E, 0xFF, 0xFF, 0x01, 0xF6, 0x00, 0xF8, 0x00, 0xFA, 0x10, 0xFC,
	0x10, 0x1F, 0x70, 0x60, 0xC1, 0x0F, 0x00, 0x80, 0x81, 0x81, 0xD9, 0x0F, 0xFE, 0xE8, 0x8F, 0xFE,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0xE0, 0x03, 0x0E, 0x2C, 0xD0, 0x00, 0x00, 0x80, 0xF9, 0x01, 0x00, 0x00, 0x00, 0x00, 0xE0,
	0x10, 0xE0, 0x03, 0x0E, 0x2C, 0xF0, 0x03, 0xF8, 0xFF, 0xFF, 0x01, 0x00, 0x00, 0xC8, 0x4A, 0xFF,
	0xC0, 0xFF, 0x7F, 0x4E, 0xDF, 0x6E, 0x01, 0xFF, 0x01, 0x21, 0x84, 0xEB, 0xFE, 0xFF, 0xFF, 0xFF,
	0x80, 0xF8, 0xFF, 0xFF, 0x73, 0xCE, 0xB9, 0xB5, 0xD6, 0xAE, 0xFF, 0xE3, 0x5B, 0xE1, 0xE1, 0x79,
	0x80, 0xC0, 0xFF, 0xFF, 0x73, 0xCE, 0xB9, 0xB5, 0xD6, 0xEE, 0xFF, 0xFB, 0x6B, 0x1F, 0x1E, 0x1E,
	0xA0, 0xFF, 0xFF, 0x1F, 0xBE, 0xDD, 0xA2, 0xA3, 0xCF, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x80, 0xF8, 0xFF, 0xFF, 0x73, 0xCE, 0xB9, 0xB5, 0xD6, 0x7E, 0xF6, 0xE5, 0xF7, 0x01, 0x00, 0x00,
	0xC0, 0xFF, 0x7F, 0x4E, 0xD7, 0x6E, 0x01, 0xFF, 0x17, 0x00, 0xCE, 0x48, 0xFF, 0xEF, 0xFF, 0xFF,
	0x10, 0xE0, 0x03, 0x0E, 0x2C, 0xF0, 0x03, 0x7E, 0xFE, 0xFF, 0x01, 0x00, 0x00, 0x5D, 0xF0, 0x9B,
	0x20, 0x80, 0x3F, 0x80, 0x0E, 0xD8, 0x02, 0xBC, 0x00, 0x00, 0x06, 0x7E, 0x00, 0x00, 0x00, 0x03,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0xE0, 0x03, 0x0E, 0x2C, 0x00, 0x00, 0x00, 0x80, 0x81, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xC0, 0xFF, 0x9F, 0x4E, 0xD7, 0x6E, 0x01, 0xFF, 0x01, 0xE4, 0x10, 0xFA, 0x50, 0xFE, 0xC2, 0xFF,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xC0, 0xFF, 0x9F, 0xCE, 0xDE, 0x06, 0xFF, 0xFF, 0x01, 0x60, 0x00, 0xA0, 0x00, 0xD3, 0x00, 0xF7,
	0xE0, 0xFF, 0x3F, 0x9B, 0xFD, 0xFF, 0x0F, 0x0A, 0x02, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00,
	0xC0, 0xFF, 0x9F, 0x4D, 0x0F, 0x6E, 0xFF, 0xFF, 0x31, 0xFD, 0x50, 0xFE, 0x70, 0xFF, 0xA0, 0xFF,
	0x10, 0x1F, 0x70, 0x60, 0x01, 0x0E, 0x80, 0x81, 0x81, 0x81, 0xF1, 0x1F, 0xFF, 0xF9, 0xAF, 0xFF,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x20, 0x80, 0x3F, 0x80, 0x0E, 0xD8, 0x02, 0x00, 0x00, 0x00, 0x00, 0xE0, 0x01, 0x00, 0x00, 0x00,
	0x10, 0xE0, 0x03, 0x0E, 0x2C, 0xF0, 0x83, 0xF9, 0xFF, 0xFF, 0x01, 0x00, 0x20, 0x80, 0x8A, 0xF9,
	0xC0, 0xFF, 0x7F, 0x4E, 0xDF, 0x6E, 0x03, 0xFF, 0x20, 0xE9, 0xD7, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xC0, 0xFF, 0x9F, 0x4E, 0xDF, 0x6E, 0xFF, 0x00, 0x72, 0xFD, 0x00, 0xB3, 0x00, 0x10, 0x00, 0x00,
	0x10, 0x1F, 0x70, 0x60, 0x01, 0xC0, 0xFB, 0xE1, 0x81, 0x01, 0x00, 0x00, 0x00, 0x03, 0xF0, 0x01,
	0x10, 0xE0, 0x03, 0x0E, 0x2C, 0xF0, 0xE3, 0xF9, 0xF9, 0xFF, 0x01, 0x00, 0x40, 0x40, 0x0A, 0xF0,
	0xF0, 0xFF, 0x73, 0x1E, 0x7F, 0xDB, 0x02, 0x00, 0x00, 0x00, 0xF8, 0xEF, 0xFF, 0xFF, 0xFF, 0xFF,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xC0, 0xFF, 0x9F, 0xCE, 0xDE, 0x06, 0xFF, 0xFF, 0x11, 0xFB, 0x40, 0xFE, 0x90, 0xFF, 0xD2, 0xFF,
	0xE0, 0xFF, 0x3F, 0x9B, 0xFD, 0xFF, 0x0B, 0x12, 0x02, 0x00, 0x00, 0x00, 0x56, 0x15, 0x15, 0xD5,
	0x10, 0xFF, 0x6F, 0x1E, 0xED, 0x1F, 0xF3, 0xF9, 0xFB, 0xFD, 0x01, 0x00, 0x20, 0x00, 0x06, 0xE0,
	0x10, 0x1F, 0x70, 0x60, 0xC1, 0x06, 0xE0, 0xE1, 0xE1, 0xE1, 0xF9, 0xAF, 0xFF, 0xFC, 0xEF, 0xFF,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x20, 0x80, 0x3F, 0x80, 0x0E, 0xD0, 0x02, 0x00, 0x00, 0x00, 0x00, 0x80, 0x01, 0x00, 0x00, 0x00,
	0x10, 0xE0, 0x03, 0x0E, 0x2C, 0xF0, 0xFB, 0xFF, 0xFF, 0xFF, 0x01, 0x02, 0xA4, 0x48, 0x0F, 0xFE,
	0x80, 0xE0, 0xFF, 0xFF, 0x73, 0xCE, 0xB9, 0xB5, 0xD6, 0xD2, 0xFF, 0xFF, 0x69, 0x66, 0x66, 0x66,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xC0, 0xFF, 0x9F, 0x4E, 0xDF, 0x6E, 0xFF, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xF3,
	0xC0, 0xFF, 0x9F, 0x4E, 0xDF, 0x6E, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x63, 0xB7, 0xFE,
	0x10, 0xFF, 0x73, 0x6C, 0xC9, 0x0F, 0x00, 0x80, 0x48, 0x79, 0x01, 0x00, 0x00, 0xE4, 0xF2, 0xDF,
	0x80, 0xCD, 0xFF, 0xFF, 0xF3, 0x9D, 0xB3, 0x7D, 0x29, 0xFD, 0xFF, 0xD5, 0x03, 0xFC, 0x01, 0x0E,
	0xC0, 0xFF, 0x9F, 0x6E, 0xE6, 0x9A, 0xFE, 0xFF, 0x00, 0x00, 0x68, 0x24, 0xFF, 0xCE, 0xFF, 0xFF,
	0x28, 0xFD, 0xFF, 0xFF, 0x3F, 0x3D, 0xCE, 0x36, 0x6F, 0xA1, 0x3E, 0x93, 0x00, 0x00, 0x0C, 0x8B,
	0xC0, 0xFF, 0x9F, 0x4E, 0xDF, 0x6A, 0xFF, 0x00, 0x11, 0xF9, 0x00, 0xB1, 0x00, 0x30, 0x00, 0x00,
	0xC0, 0xFF, 0x9F, 0x4E, 0xDF, 0x6E, 0x01, 0xFF, 0x51, 0xFE, 0xC2, 0xFF, 0xFB, 0xFF, 0xFF, 0xFF,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x88, 0xFF, 0xFF, 0xFF, 0x3F, 0x1D, 0xDE, 0xA6, 0x6F, 0x9F, 0xC6, 0xED, 0x79, 0x9E, 0x9E, 0x27,
	0x08, 0xFD, 0xFF, 0xFF, 0x3F, 0x9C, 0xCD, 0x6E, 0x3B, 0x83, 0xC1, 0xE2, 0xFB, 0xFF, 0x81, 0x01,
	0xC0, 0xFF, 0x9F, 0x2D, 0x0F, 0x5A, 0xFF, 0xFF, 0x01, 0x20, 0x00, 0x60, 0x00, 0xA0, 0x00, 0xF1,
	0x80, 0xC1, 0xFF, 0xFF, 0x73, 0xCE, 0xB9, 0xAD, 0xD6, 0xFE, 0x3D, 0xE8, 0x83, 0x01, 0xA1, 0x60,
	0x20, 0x7F, 0x00, 0x1D, 0xB0, 0x05, 0x08, 0x00, 0xE0, 0xF9, 0xF9, 0xF9, 0xFD, 0xFF, 0xFF, 0xFF,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x80, 0x03, 0xF8, 0xFF, 0x83, 0xCF, 0x39, 0x30, 0xB6, 0x02, 0x20, 0x01, 0xE3, 0xE1, 0x61, 0x79,
	0xC0, 0xFF, 0x9F, 0x4E, 0xDF, 0x6E, 0x49, 0x7F, 0xE0, 0xFF, 0xF9, 0xFF, 0xFE, 0xFF, 0xFF, 0xFF,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xC0, 0xFF, 0x9F, 0x4E, 0xDF, 0x6E, 0xFF, 0x98, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x20, 0xF8,
	0x10, 0x1F, 0x70, 0x60, 0xC1, 0x0F, 0x00, 0x00, 0x00, 0xE0, 0x01, 0x00, 0x88, 0xEB, 0xFF, 0xFF,
	0x10, 0x1F, 0x70, 0x60, 0xC1, 0x0C, 0x00, 0x80, 0xF9, 0xFF, 0x51, 0xED, 0xFF, 0xFF, 0xFF, 0xFF,
	0xC0, 0x3F, 0x80, 0x0E, 0xD0, 0x02, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
	0xA0, 0x80, 0x3F, 0x00, 0x00, 0x30, 0x01, 0x38, 0x83, 0x01, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x00,
	0x10, 0x1F, 0x64, 0xA0, 0x00, 0xF0, 0x03, 0x00, 0x06, 0x1E, 0x08, 0x0F, 0xA4, 0x00, 0x04, 0x20,
	0xC0, 0xFF, 0xDF, 0x6C, 0x36, 0x99, 0xFE, 0xEE, 0x01, 0x00, 0x00, 0x00, 0x03, 0x00, 0x0F, 0x00,
	0xC8, 0xFE, 0xFF, 0xFF, 0xBF, 0x39, 0x6D, 0x36, 0x9B, 0x62, 0xA6, 0xD2, 0xE9, 0x80, 0x00, 0x00,
	0xC0, 0xFF, 0x9F, 0x6E, 0xDE, 0x96, 0xFE, 0xFF, 0x07, 0x00, 0x2C, 0x00, 0xAF, 0x01, 0xEF, 0x04,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xC0, 0xFF, 0x9F, 0xCE, 0xDE, 0x06, 0xFF, 0x7F, 0x01, 0xF7, 0x20, 0xFD, 0x80, 0xFF, 0xE3, 0xFF,
	0x20, 0xFF, 0x3F, 0x9B, 0x1D, 0x0C, 0xFE, 0xFF, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x38, 0xFC, 0xFF, 0xFF, 0x3F, 0xDB, 0x4D, 0x7F, 0x07, 0x91, 0xDB, 0xE4, 0x62, 0x40, 0x28, 0x18,
	0xC0, 0xFF, 0x9F, 0x4E, 0xDF, 0x6E, 0xFF, 0x80, 0x00, 0xF5, 0x00, 0xF9, 0x10, 0xFC, 0x40, 0xFE,
	0xC0, 0x3F, 0x80, 0x0E, 0xD8, 0x02, 0x00, 0x00, 0xF0, 0xFF, 0xF0, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0xE0, 0x03, 0x0E, 0x2E, 0xF0, 0xFB, 0xF9, 0xFF, 0xFF, 0x01, 0x0C, 0xC4, 0xC0, 0x0E, 0xF0,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xF0, 0xFF, 0x73, 0xFE, 0x63, 0xDB, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xE4,
	0x10, 0x1F, 0x70, 0x60, 0xC1, 0x0F, 0x00, 0x00, 0x00, 0x80, 0x01, 0x02, 0xA8, 0xE1, 0xEF, 0xFF,
	0x90, 0x1F, 0x70, 0x60, 0x41, 0x09, 0xF0, 0xFF, 0xFF, 0xFF, 0x01, 0x00, 0xFC, 0xF8, 0xFF, 0xFF,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0xE0, 0x83, 0x0C, 0x12, 0x40, 0xE2, 0xE1, 0xE1, 0xF9, 0x01, 0x02, 0x20, 0x00, 0x06, 0xE0,
	0xC0, 0xFF, 0xDF, 0x6C, 0x36, 0x99, 0xCE, 0xFF, 0xF1, 0xFF, 0xF3, 0xFF, 0xFB, 0xFF, 0xFF, 0xFF,
	0xA0, 0xFF, 0xFF, 0xFF, 0x6F, 0x32, 0x31, 0x33, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xC0, 0xFF, 0xDF, 0x4C, 0x2F, 0x69, 0xFF, 0xFF, 0x00, 0xF6, 0x00, 0xE2, 0x00, 0xB1, 0x00, 0x90,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xE5, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x48, 0xFC, 0xFF, 0xFF, 0x3F, 0x7D, 0xCE, 0x8E, 0x6F, 0xB1, 0x41, 0xE9, 0x2F, 0x8B, 0xEB, 0x4A,
	0x08, 0xFD, 0xFF, 0xFF, 0xFF, 0x9B, 0xCD, 0x66, 0x2B, 0x83, 0x41, 0xA1, 0xFB, 0xFF, 0x81, 0x01,
	0x70, 0xFF, 0xEB, 0xFD, 0x3F, 0x28, 0xAE, 0xAA, 0xAA, 0x2A, 0x4B, 0x92, 0x04, 0x49, 0x92, 0xA4,
	0x90, 0xFF, 0x6B, 0x0E, 0xED, 0x9F, 0x03, 0x00, 0x00, 0x80, 0xE1, 0x1F, 0xFF, 0xFB, 0xEF, 0xFF,
	0x10, 0x1F, 0x70, 0x60, 0x81, 0x0F, 0x00, 0x80, 0x81, 0xE1, 0xE1, 0x1F, 0xFF, 0xFB, 0xDF, 0xFF,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0xE0, 0x03, 0x0E, 0x2C, 0x10, 0x00, 0x80, 0x81, 0xE1, 0x01, 0x00, 0x00, 0x00, 0x00, 0xA0,
	0xC0, 0xFF, 0x9F, 0x4E, 0xDF, 0x6E, 0x01, 0xFF, 0x10, 0xFB, 0x40, 0xFE, 0xA0, 0xFF, 0xF7, 0xFF,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xC0, 0xFF, 0x9F, 0x4E, 0xDF, 0x6E, 0xFF, 0x04, 0x01, 0x00, 0x00, 0x00, 0x00, 0x70, 0x00, 0xF8,
	0x10, 0x1F, 0x70, 0x60, 0xC1, 0x0F, 0x00, 0x00, 0xE0, 0xF9, 0x49, 0x2F, 0xFF, 0xFE, 0xFF, 0xFF,
	0xC0, 0x3F, 0x80, 0x0E, 0xD8, 0x02, 0x00, 0x00, 0xF0, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x20, 0x80, 0x3F, 0xC0, 0x0C, 0x30, 0x01, 0x18, 0x00, 0x00, 0x80, 0xE1, 0x01, 0x00, 0x00, 0xC0,
	0xC0, 0xFF, 0xDF, 0x6C, 0x36, 0x99, 0x00, 0xFF, 0x01, 0xD3, 0x10, 0xFA, 0x80, 0xFF, 0xE7, 0xFF,
	0xA0, 0xFF, 0xFF, 0xFF, 0x6F, 0x32, 0x31, 0x33, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0x6F, 0x32, 0x31, 0x33, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x88, 0xFD, 0xFF, 0xFF, 0xBF, 0xD9, 0x6C, 0x66, 0x97, 0x52, 0x25, 0x5F, 0xCF, 0x85, 0x45, 0xD5,
	0x68, 0xFD, 0xFF, 0xFF, 0x3F, 0x7D, 0x4E, 0x87, 0x6F, 0xB5, 0x5B, 0xE7, 0xAF, 0xAE, 0x56, 0x53,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xE0, 0xFF, 0xFF, 0x9C, 0xFE, 0xFF, 0xDB, 0xCA, 0xAE, 0xAA, 0xAA, 0x2A, 0x00, 0x00, 0x40, 0xC0,
	0xC0, 0xFF, 0x9F, 0xCE, 0xDE, 0x06, 0xFF, 0xFF, 0x41, 0xFE, 0xB2, 0xFF, 0xF8, 0xFF, 0x66, 0x66,
	0xC0, 0xFF, 0x9F, 0x0D, 0x0F, 0x3E, 0xFF, 0xFF, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0xBF, 0x37,
	0xC0, 0xFF, 0x9F, 0x4D, 0x0F, 0x6E, 0xFF, 0xFF, 0x01, 0x60, 0x00, 0xB1, 0x00, 0xE6, 0x10, 0xFB,
	0xC0, 0xFF, 0x9F, 0x4E, 0xDF, 0x6E, 0xFF, 0x00, 0x00, 0x60, 0x00, 0xB1, 0x00, 0xF6, 0x20, 0xFC,
	0x10, 0x1F, 0x70, 0x60, 0x41, 0x00, 0xE0, 0xF9, 0xF9, 0xF9, 0xF9, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0xE0, 0x03, 0x0E, 0x2C, 0xD0, 0xE0, 0xE1, 0xE1, 0x81, 0x01, 0x0C, 0xE0, 0x00, 0x02, 0x00,
	0xC0, 0xFF, 0x9F, 0x4E, 0xDF, 0x6E, 0xFF, 0x01, 0x02, 0x00, 0x01, 0x00, 0x28, 0x11, 0xDF, 0xCC,
	0xC0, 0xFF, 0x9F, 0x4E, 0xDF, 0x6E, 0xFF, 0x80, 0x00, 0x00, 0x00, 0x40, 0x52, 0xEA, 0xED, 0xFF,
	0x10, 0x1F, 0x70, 0x60, 0x41, 0x0E, 0x00, 0x80, 0xE1, 0xFF, 0xE1, 0xDF, 0xFF, 0xFF, 0xFF, 0xFF,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x40, 0xC0, 0x1F, 0x60, 0x06, 0x98, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF,
	0x10, 0xE0, 0x83, 0x0C, 0x12, 0xF0, 0xFB, 0xF9, 0xFF, 0xFF, 0x01, 0x06, 0xC4, 0x00, 0x9F, 0xFE,
	0xA0, 0xFF, 0x7F, 0xFE, 0x6F, 0x32, 0x31, 0x33, 0xFB, 0xFF, 0xFF, 0xFF, 0x01, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0x6F, 0x32, 0x31, 0x33, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0x6F, 0x32, 0x31, 0x33, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0x6F, 0x32, 0x31, 0x33, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xC0, 0xFF, 0x9F, 0x6E, 0xDE, 0x9A, 0xFE, 0xFF, 0x0E, 0x00, 0x1B, 0x00, 0x7E, 0x24, 0xFF, 0xCE,
	0x08, 0xFE, 0xFF, 0xFF, 0x3F, 0x9D, 0x4D, 0x8F, 0x6F, 0x7B, 0x5B, 0xA8, 0x00, 0x00, 0x00, 0x1E,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xBD, 0xA2, 0x9B, 0xA3, 0x01, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x00,
	0xC0, 0xFF, 0x3F, 0x4E, 0x97, 0x6E, 0xFF, 0xFF, 0x01, 0xED, 0xEF, 0xEE, 0xEE, 0xEE, 0xEE, 0xEE,
	0x10, 0x1F, 0x7C, 0x60, 0xC1, 0x0F, 0x00, 0x80, 0x01, 0x00, 0xD8, 0x1F, 0xFF, 0x98, 0x0F, 0x84,
	0x10, 0xE0, 0x03, 0x0E, 0x2C, 0x20, 0x00, 0x00, 0x06, 0x1E, 0x00, 0x00, 0x00, 0x00, 0x70, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x20, 0x80, 0x3F, 0xC0, 0x0C, 0x38, 0x01, 0xFC, 0x00, 0x00, 0xE0, 0xF9, 0x01, 0x00, 0x00, 0xC0,
	0x80, 0x0C, 0xF8, 0xFF, 0x83, 0xAC, 0x33, 0x48, 0x2C, 0x01, 0xF0, 0x83, 0x7A, 0x5F, 0x0E, 0x02,
	0x20, 0xFF, 0xBF, 0x99, 0x6D, 0x12, 0x2A, 0xFF, 0x03, 0x00, 0x48, 0xFD, 0xFD, 0xFF, 0xFF, 0xFF,
	0xD8, 0xFC, 0xFF, 0xFF, 0xBF, 0xF9, 0xCC, 0x5E, 0x9B, 0x52, 0x42, 0xDE, 0x02, 0x5E, 0xD5, 0x01,
	0x18, 0xFF, 0xFF, 0xFF, 0xBF, 0x99, 0x6D, 0x4E, 0x9B, 0x84, 0xA6, 0xDA, 0x03, 0x00, 0x7A, 0xFF,
	0xC0, 0xFF, 0xDF, 0xCC, 0x36, 0xFD, 0xFE, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0xCF, 0x16,
	0xA0, 0xFF, 0xFF, 0xFF, 0x6F, 0x32, 0x31, 0x33, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0x6F, 0x32, 0x31, 0x33, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x48, 0xFD, 0xFF, 0xFF, 0xBF, 0xF9, 0x8C, 0x96, 0x9B, 0x56, 0x30, 0xEA, 0xFB, 0x00, 0x00, 0x00,
	0xC0, 0xFF, 0x9F, 0x6E, 0xDE, 0x9A, 0xFE, 0xFF, 0x00, 0x00, 0x05, 0x00, 0x7E, 0x01, 0xFF, 0x19,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0x5F, 0xBF, 0xDD, 0xA2, 0xA3, 0x83, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xC0, 0xFF, 0x9F, 0x4E, 0xDF, 0x6E, 0xFF, 0x00, 0xDC, 0xFF, 0x40, 0xFC, 0x00, 0xC3, 0x00, 0x30,
	0x10, 0xE0, 0x03, 0x0E, 0x2C, 0xB0, 0x00, 0x06, 0x1E, 0x7E, 0x00, 0x00, 0x00, 0x00, 0x70, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0x80, 0x3F, 0x00, 0x00, 0x30, 0x01, 0x30, 0x03, 0x00, 0x00, 0x80, 0x01, 0x00, 0x00, 0xC0,
	0x10, 0xE0, 0x83, 0x0C, 0x12, 0xB0, 0x03, 0xE0, 0xF9, 0xFF, 0x01, 0x00, 0x00, 0x00, 0x84, 0xF0,
	0x10, 0xFF, 0x67, 0x9D, 0x20, 0xF0, 0x03, 0x80, 0xA0, 0xF3, 0xC1, 0x1C, 0xFE, 0xFE, 0xFF, 0xFF,
	0xC8, 0xFD, 0xFF, 0xFF, 0xBF, 0x99, 0xCD, 0x66, 0x9F, 0x82, 0x42, 0xA0, 0x49, 0xBD, 0xD6, 0xFD,
	0x20, 0xFF, 0x3F, 0x9B, 0x1D, 0x14, 0xFE, 0xFF, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x20, 0xFF, 0x3F, 0x9B, 0x1D, 0x0C, 0xFE, 0xFF, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x20, 0xFF, 0x3F, 0x9B, 0x1D, 0x0C, 0xFE, 0xFF, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x48, 0xFE, 0xFF, 0xFF, 0x3F, 0x9B, 0xCD, 0x56, 0x07, 0x85, 0xC1, 0xDC, 0xCB, 0x40, 0x00, 0x00,
	0x28, 0xFC, 0xFF, 0xFF, 0xFF, 0x99, 0x6D, 0x46, 0xAF, 0x82, 0xA6, 0x97, 0x01, 0x0C, 0x1E, 0x1E,
	0xA0, 0xFF, 0xFF, 0xFF, 0x6F, 0x32, 0x31, 0x33, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0x6F, 0x32, 0x31, 0x33, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0x6F, 0xBA, 0x31, 0x57, 0x83, 0x01, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x00,
	0xC0, 0xFF, 0x9F, 0x6E, 0xDE, 0x9A, 0xFE, 0xFF, 0x05, 0x00, 0x1A, 0x00, 0xAF, 0x01, 0xFF, 0x19,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x1F, 0x70, 0x60, 0xC1, 0x0F, 0x80, 0x01, 0x00, 0x00, 0xF4, 0x8F, 0xFD, 0x00, 0x0F, 0xC4,
	0x10, 0xE0, 0x03, 0x0E, 0x2C, 0x00, 0x00, 0x00, 0x06, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x20, 0x80, 0x3F, 0xC0, 0x0C, 0x38, 0x01, 0x10, 0x00, 0x00, 0x80, 0xE1, 0x01, 0x00, 0x00, 0xC0,
	0x80, 0x0A, 0xF8, 0xFF, 0x83, 0xAC, 0x33, 0x50, 0x2D, 0x81, 0xF0, 0x09, 0xE0, 0x79, 0x3F, 0x0C,
	0x10, 0xFF, 0x67, 0x9D, 0x60, 0xF3, 0x83, 0xC8, 0xF3, 0xFF, 0xE1, 0xEF, 0xFF, 0xFF, 0xFF, 0xFF,
	0x08, 0xFE, 0xFF, 0xFF, 0xFF, 0x9A, 0xCD, 0x66, 0xFB, 0x82, 0x41, 0xE1, 0x6B, 0x66, 0x66, 0x66,
	0x20, 0xFF, 0x3F, 0x9B, 0x1D, 0x0C, 0xFE, 0xFF, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x20, 0xFF, 0x3F, 0x9B, 0x1D, 0x0C, 0xFE, 0xFF, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x20, 0xFF, 0x3F, 0x9B, 0x1D, 0x0C, 0xFE, 0xFF, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x20, 0xFF, 0x3F, 0x9B, 0x1D, 0x0C, 0xFE, 0xFF, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x88, 0xFC, 0xFF, 0xFF, 0x3F, 0x9B, 0x7D, 0x5E, 0x07, 0x85, 0x2D, 0xDF, 0x01, 0x80, 0xA0, 0x69,
	0x88, 0xFE, 0xFF, 0xFF, 0x3F, 0xDB, 0x6C, 0x3E, 0xFF, 0x4C, 0xA6, 0x95, 0x1B, 0x0D, 0xF2, 0xC0,
	0xA0, 0xFF, 0xFF, 0xFF, 0x6F, 0x32, 0x31, 0x33, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0x6F, 0x32, 0x31, 0x33, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0x6F, 0x32, 0x31, 0x33, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xC8, 0xFE, 0xFF, 0xFF, 0xBF, 0xB9, 0x6D, 0x3E, 0x9B, 0x84, 0x26, 0xD5, 0xE1, 0xC0, 0x00, 0x00,
	0xC0, 0xFF, 0x9F, 0x6E, 0xDE, 0x9A, 0xFE, 0x7E, 0x01, 0x00, 0x04, 0x00, 0x2C, 0x00, 0x9F, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xF0, 0xFF, 0x73, 0xFE, 0x63, 0xDB, 0x02, 0x00, 0x00, 0x00, 0x00, 0x0E, 0x40, 0x00, 0x00, 0x00,
	0x10, 0x1F, 0x70, 0x60, 0x01, 0x30, 0xE3, 0xE1, 0x81, 0x81, 0x01, 0x10, 0x00, 0x04, 0xF0, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0xE0, 0x83, 0x0C, 0x14, 0xC0, 0x02, 0x80, 0xE1, 0xFF, 0x01, 0x00, 0x00, 0x00, 0x02, 0xE8,
	0x10, 0xFF, 0x67, 0x9D, 0x20, 0xF0, 0x03, 0x00, 0x00, 0xC9, 0x41, 0x08, 0xF5, 0xF3, 0xFF, 0xFF,
	0x90, 0xFF, 0x67, 0x9D, 0xE0, 0xFE, 0xFB, 0xFF, 0xFF, 0xFF, 0x51, 0x3F, 0xFF, 0xFF, 0xFF, 0xFF,
	0x20, 0xFF, 0x3F, 0x9B, 0x1D, 0x0C, 0xFE, 0xFF, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x20, 0xFF, 0x3F, 0x9B, 0x1D, 0x0C, 0xFE, 0xFF, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x20, 0xFF, 0x3F, 0x9B, 0x1D, 0x0C, 0xFE, 0xFF, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x20, 0xFF, 0x3F, 0x9B, 0x1D, 0x0C, 0xFE, 0xFF, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x20, 0xFF, 0x3F, 0x9B, 0x1D, 0x0C, 0xFE, 0xFF, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x48, 0xFF, 0xFF, 0xFF, 0x3F, 0x9B, 0x7D, 0x66, 0x07, 0x85, 0xAA, 0xE0, 0x7A, 0x9E, 0x26, 0x41,
	0xC0, 0xFF, 0x9F, 0x6D, 0x16, 0x9A, 0xFE, 0xFF, 0x21, 0xFB, 0xB3, 0xFF, 0xFC, 0xFF, 0xFF, 0xFF,
	0xA0, 0xFF, 0xFF, 0xFF, 0x6F, 0x32, 0x31, 0x33, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0x6F, 0x32, 0x31, 0x33, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0x6F, 0x32, 0x31, 0x33, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0x6F, 0x32, 0x31, 0x33, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x80, 0xC8, 0xFF, 0xFF, 0xE7, 0x9C, 0x73, 0x4A, 0x29, 0x7D, 0x1F, 0xDE, 0x02, 0x80, 0xE1, 0x79,
	0x90, 0x1F, 0x70, 0x60, 0xC1, 0x0F, 0x28, 0x5A, 0xFE, 0x7E, 0x0B, 0x10, 0x01, 0x09, 0x90, 0x03,
	0xA0, 0xFF, 0xFF, 0x3F, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x1F, 0x70, 0x60, 0xC1, 0x0F, 0x80, 0x01, 0x00, 0x00, 0xE8, 0x8F, 0xFD, 0xD0, 0x8F, 0xF8,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0x80, 0x3F, 0x00, 0x00, 0x38, 0x01, 0x30, 0x03, 0x00, 0x00, 0xE0, 0x01, 0x00, 0x00, 0xF0,
	0x10, 0xE0, 0x83, 0x0C, 0x14, 0xF0, 0xE3, 0xF9, 0xFF, 0xFF, 0x01, 0x00, 0x20, 0x80, 0x1C, 0xFE,
	0x10, 0xFF, 0x67, 0x9D, 0xE0, 0xF0, 0x03, 0x80, 0xC8, 0xF3, 0x99, 0xCF, 0xFF, 0xFF, 0xFF, 0xFF,
	0xC0, 0xFF, 0x1F, 0xCD, 0x7E, 0x09, 0xFF, 0xFF, 0xB1, 0xEE, 0xED, 0xEE, 0xEE, 0xEE, 0xEE, 0xEE,
	0x20, 0xFF, 0x3F, 0x9B, 0x1D, 0x0C, 0xFE, 0xFF, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x20, 0xFF, 0x3F, 0x9B, 0x1D, 0x0C, 0xFE, 0xFF, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x20, 0xFF, 0x3F, 0x9B, 0x1D, 0x0C, 0xFE, 0xFF, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x20, 0xFF, 0x3F, 0x9B, 0x1D, 0x0C, 0xFE, 0xFF, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x20, 0xFF, 0x3F, 0x9B, 0x1D, 0x14, 0xFE, 0xFF, 0x03, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00,
	0x2E, 0xFF, 0xFF, 0xFF, 0x76, 0x3D, 0xD7, 0x61, 0x37, 0x6D, 0x29, 0x49, 0x0A, 0xA5, 0x4F, 0x0F,
	0x98, 0xFC, 0xFF, 0xFF, 0x3F, 0x1B, 0x6D, 0x36, 0x03, 0x63, 0x26, 0x54, 0x73, 0x0E, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0x6F, 0x32, 0x31, 0x33, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0x6F, 0x32, 0x31, 0x33, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0x6F, 0x32, 0x31, 0x33, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0x6F, 0x32, 0x31, 0x33, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x80, 0xC8, 0xFF, 0xFF, 0xE7, 0x9C, 0x73, 0x4A, 0x29, 0x7D, 0x1F, 0xDE, 0x03, 0x80, 0xE1, 0x79,
	0x80, 0xC9, 0xFF, 0xE0, 0xE7, 0x0C, 0x72, 0x4A, 0x20, 0xF9, 0x07, 0xC2, 0xF8, 0x7F, 0x1E, 0x06,
	0x10, 0xE0, 0x03, 0x0E, 0x2C, 0x30, 0xE0, 0xE1, 0x81, 0x81, 0x01, 0x0E, 0x00, 0x00, 0x00, 0x00,
	0xC0, 0xFF, 0x9F, 0x4E, 0xDF, 0x6E, 0xFF, 0x0C, 0x08, 0x00, 0x09, 0x00, 0x1D, 0x00, 0x2F, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xC0, 0xFF, 0x9F, 0x4E, 0xDF, 0x6E, 0xFF, 0x00, 0x10, 0xFB, 0x00, 0xFA, 0x00, 0xF9, 0x00, 0xF8,
	0x80, 0x24, 0xF8, 0xE0, 0x83, 0x0F, 0x3A, 0xB0, 0xC0, 0x02, 0x00, 0x80, 0x00, 0x00, 0x03, 0x03,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0xE0, 0x83, 0x0C, 0x14, 0x30, 0x02, 0x80, 0xE1, 0xFF, 0x01, 0x00, 0x00, 0x00, 0x00, 0xE4,
	0x80, 0x3A, 0xF8, 0xFF, 0x83, 0xAC, 0x33, 0x48, 0x2C, 0x81, 0xFF, 0x01, 0x7A, 0xBF, 0xFD, 0x7F,
	0x90, 0xFF, 0x67, 0x9D, 0xE0, 0xFC, 0xFB, 0xFF, 0xFF, 0xFF, 0x41, 0x86, 0xF0, 0xEA, 0xEF, 0xFF,
	0xA0, 0xFF, 0xFF, 0xFF, 0xFF, 0x13, 0x62, 0x67, 0x5B, 0x55, 0x55, 0x55, 0xFD, 0xFF, 0xFF, 0xFF,
	0x20, 0xFF, 0x3F, 0x9B, 0x1D, 0x0C, 0xFE, 0xFF, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x20, 0xFF, 0x3F, 0x9B, 0x1D, 0x0C, 0xFE, 0xFF, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x20, 0xFF, 0x3F, 0x9B, 0x1D, 0x0C, 0xFE, 0xFF, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x20, 0xFF, 0x3F, 0x9B, 0x1D, 0x0C, 0xFE, 0xFF, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x48, 0xFE, 0xFF, 0xFF, 0x3F, 0x5B, 0xCD, 0x66, 0x07, 0x71, 0x41, 0x61, 0x03, 0x00, 0x40, 0xC4,
	0xC0, 0xFF, 0x9F, 0x6D, 0x16, 0x9A, 0xFE, 0xFF, 0x01, 0x20, 0x10, 0xC5, 0x92, 0xFE, 0xFC, 0xFF,
	0xE0, 0xFF, 0x3F, 0xDA, 0xFC, 0xFF, 0x8B, 0x35, 0xF9, 0xFF, 0xFF, 0xFF, 0xFD, 0xFF, 0xFF, 0xFF,
	0xA0, 0xFF, 0xFF, 0xFF, 0x6F, 0x32, 0x31, 0x33, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0x6F, 0x32, 0x31, 0x33, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0x6F, 0x32, 0x31, 0x33, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0x6F, 0x32, 0x31, 0x33, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xD0, 0xFF, 0x7F, 0x90, 0x8C, 0xAC, 0x03, 0x00, 0x00, 0x80, 0x01, 0x00, 0x20, 0x40, 0x8A, 0xF4,
	0x80, 0xC9, 0xFF, 0xE0, 0xE7, 0x0C, 0x72, 0x4A, 0x40, 0x75, 0x06, 0xC0, 0xF8, 0x7F, 0x1E, 0x06,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x80, 0x01, 0x00, 0xFF, 0x03, 0xC0, 0x39, 0x00, 0xD4, 0x02, 0x00, 0x00, 0x82, 0x81, 0x80, 0x80,
	0x90, 0xFF, 0xF3, 0x6D, 0x63, 0xF0, 0xF3, 0xF1, 0xF1, 0xF1, 0x01, 0x40, 0x00, 0x0E, 0x40, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x80, 0xC0, 0xFF, 0xFF, 0x73, 0xCE, 0xB9, 0xB5, 0xD6, 0x7E, 0x0F, 0xDA, 0x63, 0x60, 0x48, 0x58,
	0x10, 0x1F, 0x70, 0x60, 0x01, 0x00, 0xF8, 0xF9, 0xFF, 0xFF, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x80, 0x20, 0xF8, 0x1F, 0x00, 0x9C, 0x01, 0x48, 0x09, 0x00, 0x00, 0x80, 0x99, 0x99, 0x99, 0x79,
	0x10, 0xE0, 0x83, 0x0C, 0x12, 0xF0, 0x83, 0xF9, 0xFF, 0xFF, 0x01, 0x00, 0x20, 0x80, 0x8A, 0xF9,
	0x10, 0xFF, 0x67, 0x9D, 0x60, 0xF0, 0x03, 0x00, 0x80, 0xC0, 0x51, 0x4F, 0xFF, 0xFF, 0xFF, 0xFF,
	0xC0, 0xFF, 0xDF, 0xCC, 0x36, 0x09, 0xFF, 0xFF, 0x11, 0xE8, 0xA3, 0xEE, 0xFC, 0xEE, 0xEF, 0xEE,
	0x20, 0xFF, 0x3F, 0x9B, 0x1D, 0x0C, 0xFE, 0xFF, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x20, 0xFF, 0x3F, 0x9B, 0x1D, 0x0C, 0xFE, 0xFF, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x20, 0xFF, 0x3F, 0x9B, 0x1D, 0x0C, 0xFE, 0xFF, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x20, 0xFF, 0x3F, 0x9B, 0x1D, 0x0C, 0xFE, 0xFF, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xC0, 0xFF, 0x9F, 0xCD, 0x16, 0xFE, 0xFE, 0xFF, 0x5B, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0xF2,
	0xB8, 0xFC, 0xFF, 0xFF, 0x3F, 0x5B, 0x6D, 0x3E, 0x0B, 0x6D, 0xA6, 0xD6, 0x03, 0x80, 0x68, 0x1F,
	0xC8, 0xFC, 0xFF, 0xFF, 0x3F, 0x1B, 0x6D, 0x36, 0x07, 0x61, 0xA6, 0x54, 0xEB, 0x1C, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0x6F, 0x32, 0x31, 0x33, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0x6F, 0x32, 0x31, 0x33, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0x6F, 0x32, 0x31, 0x33, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0x6F, 0x32, 0x31, 0x33, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0x6F, 0x32, 0x31, 0x33, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xC0, 0xFF, 0xDF, 0x6C, 0x36, 0x99, 0xFE, 0x00, 0x01, 0x00, 0x00, 0x50, 0x00, 0xE7, 0x91, 0xFF,
	0x10, 0x1F, 0x64, 0x90, 0xC0, 0x0B, 0x00, 0x80, 0xE1, 0xFF, 0xE9, 0xEF, 0xFF, 0xFF, 0xFF, 0xFF,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x80, 0x01, 0x00, 0xFF, 0x03, 0xB0, 0x37, 0x00, 0x32, 0x02, 0x00, 0x00, 0x01, 0x81, 0x81, 0x80,
	0x80, 0xC2, 0xFF, 0xFF, 0xEF, 0xCD, 0x79, 0x94, 0xB6, 0x86, 0xF2, 0xED, 0x1B, 0x10, 0x12, 0x0E,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xC0, 0xFF, 0x9F, 0x3E, 0xDF, 0x6E, 0xFF, 0x00, 0x10, 0xFC, 0x20, 0xFD, 0x40, 0xFE, 0x60, 0xFF,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x20, 0x80, 0x3F, 0xC0, 0x0C, 0x38, 0x01, 0x8C, 0x00, 0x00, 0x80, 0xF9, 0x01, 0x00, 0x00, 0xC0,
	0x80, 0x0C, 0xF8, 0xFF, 0x83, 0x9C, 0x33, 0x60, 0x29, 0x01, 0xF0, 0x03, 0x78, 0x5F, 0x0E, 0x02,
	0x90, 0xFF, 0x67, 0x9D, 0x60, 0xFB, 0xFB, 0xFF, 0xFF, 0xFF, 0x01, 0x00, 0x64, 0x08, 0x8F, 0xFD,
	0xC0, 0xFF, 0x1F, 0xCD, 0x76, 0x09, 0xFF, 0xFF, 0xA1, 0xEE, 0xED, 0xEE, 0xEE, 0xEE, 0xEE, 0xEE,
	0x20, 0xFF, 0x3F, 0x9B, 0x1D, 0x0C, 0xFE, 0xFF, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x20, 0xFF, 0x3F, 0x9B, 0x1D, 0x0C, 0xFE, 0xFF, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x20, 0xFF, 0x3F, 0x9B, 0x1D, 0x14, 0xFE, 0xFF, 0x03, 0x00, 0x00, 0x28, 0x00, 0x00, 0x00, 0x00,
	0xC0, 0xFF, 0x9F, 0x8D, 0x16, 0xC6, 0xFE, 0xFF, 0x13, 0x11, 0x11, 0x11, 0x01, 0x10, 0x42, 0xF9,
	0xC0, 0xFF, 0x9F, 0x6D, 0x16, 0x9A, 0xFE, 0xFF, 0x01, 0x00, 0x00, 0x31, 0x52, 0xD9, 0xFC, 0xFF,
	0xC0, 0xFF, 0x9F, 0x6D, 0x16, 0x9A, 0xFE, 0xFF, 0x21, 0xB6, 0xC7, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
	0xA0, 0xFF, 0xFF, 0xFF, 0x7F, 0x32, 0x31, 0x33, 0xBB, 0xAA, 0xAA, 0xAA, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0x6F, 0x32, 0x31, 0x33, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0x6F, 0x32, 0x31, 0x33, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0x6F, 0x32, 0x31, 0x33, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0x6F, 0x32, 0x31, 0x33, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0x6C, 0x32, 0x31, 0x33, 0x03, 0x00, 0x00, 0x80, 0x01, 0x00, 0x00, 0x00,
	0x10, 0x1F, 0x64, 0x90, 0xC0, 0x0F, 0x00, 0x00, 0x00, 0x80, 0x01, 0x02, 0xC8, 0xA0, 0xDF, 0xFF,
	0x20, 0x7F, 0x80, 0x19, 0x70, 0x02, 0x0C, 0x01, 0x80, 0xE1, 0xFF, 0xFF, 0xFD, 0xFF, 0xFF, 0xFF,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x80, 0x01, 0xF8, 0xFF, 0x03, 0x8D, 0x3D, 0x60, 0x86, 0x03, 0x00, 0x0C, 0x00, 0xE1, 0x61, 0xE0,
	0x10, 0xFF, 0x6F, 0x2E, 0xED, 0xF4, 0xFB, 0xF9, 0xF9, 0xFB, 0xF9, 0xAF, 0xFF, 0xFC, 0xEF, 0xFF,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x1F, 0x70, 0x60, 0xC1, 0x0F, 0x00, 0x80, 0x81, 0x81, 0xE1, 0x1F, 0xFF, 0xFA, 0xBF, 0xFF,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x80, 0x10, 0xF8, 0x1B, 0x80, 0x3C, 0x01, 0x48, 0x09, 0x00, 0x00, 0x80, 0x01, 0x86, 0xFF, 0x1F,
	0x10, 0x5F, 0xEB, 0xA8, 0x12, 0xF0, 0x03, 0x00, 0x00, 0xF4, 0x41, 0x8A, 0xF4, 0xE9, 0xCF, 0xFF,
	0x5A, 0xFF, 0xFF, 0xDB, 0xF2, 0x3C, 0x8F, 0xD3, 0x34, 0x4D, 0x29, 0x49, 0x92, 0x98, 0x74, 0x42,
	0xC0, 0xFF, 0xDF, 0xCC, 0x36, 0x09, 0xFF, 0xFF, 0xA1, 0xFF, 0x30, 0xC9, 0x00, 0x10, 0x00, 0x00,
	0xD8, 0xFC, 0xFF, 0xFF, 0x3F, 0x9B, 0x6D, 0x3E, 0x07, 0x7F, 0x26, 0x56, 0x02, 0x34, 0xFD, 0x01,
	0xD8, 0xFC, 0xFF, 0xFF, 0x3F, 0x7B, 0x6D, 0x3E, 0x0B, 0x77, 0xA6, 0xD5, 0x01, 0xD2, 0xDF, 0x00,
	0xC0, 0xFF, 0x9F, 0x6D, 0x16, 0x9A, 0xFE, 0xFF, 0x01, 0x10, 0x75, 0xB9, 0xFF, 0xFF, 0xFF, 0xFF,
	0xC0, 0xFF, 0x9F, 0x6D, 0xFE, 0x99, 0xFE, 0xFF, 0x21, 0x85, 0xEC, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
	0xD0, 0xFF, 0xFF, 0xBF, 0xD2, 0x2C, 0x4B, 0x55, 0x55, 0x55, 0x59, 0x6D, 0xDB, 0xB6, 0x6D, 0xDB,
	0xA0, 0xFF, 0xFF, 0xFF, 0x6F, 0x32, 0x31, 0x33, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0x6F, 0x32, 0x31, 0x33, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0x6F, 0x32, 0x31, 0x33, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0x6F, 0x32, 0x31, 0x33, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0x6F, 0x32, 0x31, 0x33, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0x6F, 0x32, 0x31, 0x33, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xC0, 0xFF, 0xDF, 0x6C, 0x36, 0x99, 0xFE, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x50, 0x20, 0xF9,
	0x10, 0x1F, 0x64, 0x90, 0xC0, 0x0F, 0x00, 0x00, 0x80, 0xF9, 0xC1, 0x1C, 0xFE, 0xFE, 0xFF, 0xFF,
	0xE0, 0x7F, 0x80, 0x19, 0x00, 0x00, 0x34, 0x01, 0xE0, 0xFF, 0xFF, 0xFF, 0xF1, 0xFF, 0xFF, 0xFF,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x20, 0x80, 0x3F, 0xA0, 0x0C, 0x28, 0x01, 0x00, 0x00, 0x00, 0x00, 0x80, 0x01, 0x00, 0x00, 0x00,
	0x80, 0x0C, 0xF8, 0xFF, 0x83, 0xCF, 0x31, 0xF8, 0xD6, 0x00, 0xF0, 0x81, 0xF2, 0x58, 0x3E, 0x16,
	0x88, 0xFF, 0xFF, 0xFF, 0xBF, 0x9C, 0x4E, 0xA7, 0x57, 0xB7, 0x5B, 0x6D, 0xA1, 0xED, 0xE9, 0xFB,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x80, 0xF9, 0xFF, 0xFF, 0x73, 0xCE, 0xB9, 0xB5, 0xD6, 0xFE, 0x3C, 0xFE, 0xFB, 0x60, 0xE1, 0x66,
	0x10, 0x1F, 0x70, 0x60, 0x81, 0x03, 0xE0, 0xE1, 0xE1, 0xF9, 0xF9, 0xEF, 0xFF, 0xFF, 0xFF, 0xFF,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x80, 0x1A, 0xD0, 0x9F, 0x80, 0xF8, 0x01, 0x50, 0x2A, 0x01, 0x0A, 0x00, 0xA8, 0xD4, 0xC4, 0xF0,
	0x20, 0x61, 0xB0, 0x6D, 0x76, 0x3A, 0xCD, 0xFF, 0xF3, 0x55, 0x55, 0x51, 0xFD, 0xFF, 0xFF, 0xFF,
	0x08, 0x7C, 0xCD, 0xFF, 0xFD, 0xCC, 0x67, 0x06, 0x9F, 0x4E, 0x26, 0xD3, 0x73, 0x70, 0x70, 0x58,
	0xA0, 0xFF, 0xFF, 0xFF, 0x6F, 0x32, 0x31, 0x33, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0x6F, 0x32, 0x31, 0x33, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0x6F, 0x32, 0x31, 0x33, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0x6F, 0x32, 0x31, 0x33, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0x6F, 0x32, 0x31, 0x33, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0x6F, 0x32, 0x31, 0x33, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0x6F, 0x32, 0x31, 0x33, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0x6F, 0x32, 0x31, 0x33, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0x6F, 0x32, 0x31, 0x33, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0x6F, 0x32, 0x31, 0x33, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0x7F, 0x67, 0x32, 0x31, 0x33, 0x03, 0x00, 0x00, 0xA0, 0x01, 0x00, 0x00, 0x00,
	0x10, 0x1F, 0x64, 0xA0, 0xC0, 0x0F, 0x00, 0x00, 0x00, 0x80, 0x01, 0x00, 0xA8, 0xA1, 0xEF, 0xFF,
	0x10, 0x1F, 0x64, 0xA0, 0x40, 0x09, 0x00, 0xE0, 0xF9, 0xFF, 0xE9, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0x80, 0x3F, 0x00, 0x00, 0x20, 0x01, 0x28, 0x03, 0x00, 0x00, 0x80, 0x01, 0x00, 0x00, 0xC0,
	0x80, 0x0F, 0xF8, 0xFF, 0x03, 0x9C, 0x33, 0x38, 0x0A, 0x01, 0x10, 0x01, 0x00, 0x00, 0xE0, 0x37,
	0x80, 0x0A, 0xF8, 0xFF, 0x83, 0xCC, 0x33, 0x48, 0x36, 0x81, 0xF1, 0x19, 0xE0, 0x7F, 0x3F, 0x02,
	0x80, 0xC9, 0xFF, 0xFF, 0x67, 0xCD, 0x79, 0x72, 0xB6, 0x36, 0xFF, 0x3F, 0x7B, 0x1E, 0x06, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xDF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x80, 0x01, 0x00, 0x00, 0x00,
	0x10, 0x1F, 0x70, 0x60, 0xC1, 0x0F, 0x00, 0x00, 0x00, 0x80, 0x01, 0x0F, 0xF9, 0xE8, 0xBF, 0xFF,
	0x20, 0x7F, 0xC0, 0x1C, 0xB0, 0x05, 0x00, 0x00, 0xF8, 0xFF, 0xFF, 0xFF, 0x01, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x90, 0xE0, 0x83, 0x0D, 0x14, 0x40, 0x81, 0x01, 0x00, 0x00, 0xB8, 0x0D, 0xFC, 0x00, 0x0E, 0x00,
	0x10, 0xF7, 0x37, 0x9D, 0xD4, 0x0F, 0x04, 0x0C, 0x5E, 0xFF, 0x01, 0x20, 0x00, 0x16, 0x70, 0x33,
	0x98, 0x80, 0xDB, 0x7F, 0xFE, 0x2C, 0x69, 0x06, 0x9F, 0x4E, 0x26, 0x53, 0x19, 0x1E, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0x6F, 0x32, 0x31, 0x33, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0x6F, 0x32, 0x31, 0x33, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0x6F, 0x32, 0x31, 0x33, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0x6F, 0x32, 0x31, 0x33, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0x6F, 0x32, 0x31, 0x33, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0x6F, 0x32, 0x31, 0x33, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0x6F, 0x32, 0x31, 0x33, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xC0, 0xFF, 0xDF, 0x6C, 0x36, 0x99, 0xFE, 0xF5, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xF1,
	0xC0, 0xFF, 0xDF, 0x6C, 0x36, 0x99, 0xFE, 0x0B, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x74, 0xFB,
	0x10, 0x1F, 0x64, 0x90, 0xC0, 0x0F, 0x00, 0x00, 0x00, 0xE0, 0x01, 0x00, 0x88, 0xEB, 0xFF, 0xFF,
	0x10, 0x1F, 0x64, 0x90, 0xC0, 0x0C, 0x00, 0x80, 0xF9, 0xFF, 0x99, 0xEF, 0xFF, 0xFF, 0xFF, 0xFF,
	0xA0, 0x7F, 0x00, 0x00, 0x60, 0x02, 0x30, 0x03, 0xF8, 0xFF, 0xFF, 0xFF, 0xFD, 0xFF, 0xFF, 0xFF,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x80, 0x20, 0xA8, 0x13, 0x00, 0xAD, 0x01, 0x88, 0x11, 0x00, 0x00, 0x80, 0x98, 0x99, 0x99, 0x79,
	0x80, 0x04, 0xB8, 0x3F, 0x03, 0x9E, 0x33, 0xF8, 0x2D, 0x81, 0xC2, 0xC1, 0x02, 0xC0, 0x51, 0x35,
	0x10, 0xFE, 0x67, 0x9E, 0x2C, 0xF0, 0x03, 0x00, 0x80, 0xA0, 0x41, 0x16, 0xFA, 0xFD, 0xFF, 0xFF,
	0x90, 0xFF, 0x67, 0x9E, 0x2C, 0xFA, 0xF3, 0xFF, 0xFF, 0xFF, 0xC9, 0x3A, 0xFF, 0xFF, 0xFF, 0xFF,
	0xA0, 0xFF, 0xFF, 0xFF, 0x5F, 0xDD, 0x96, 0xA3, 0xFB, 0xFF, 0xFF, 0xFF, 0xFD, 0xFF, 0xFF, 0xFF,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xDF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x80, 0x01, 0x00, 0x00, 0x00,
	0x80, 0xC7, 0xFF, 0xFF, 0x73, 0xCE, 0xBB, 0xB5, 0xD6, 0xFE, 0x0A, 0xCA, 0x83, 0x41, 0x71, 0x16,
	0x80, 0xC9, 0xFF, 0xE0, 0x73, 0x0E, 0xBA, 0xB5, 0xE0, 0x0A, 0x00, 0xC0, 0x18, 0x06, 0x06, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x20, 0x80, 0x3F, 0xC0, 0x0C, 0x30, 0x01, 0x80, 0xF8, 0x81, 0x01, 0x00, 0xC0, 0x00, 0x00, 0x00,
	0x10, 0x1F, 0x64, 0xB0, 0x00, 0xF0, 0x03, 0x00, 0x7E, 0xFE, 0xAF, 0x0F, 0x64, 0x00, 0x00, 0x00,
	0x10, 0x1F, 0x64, 0x90, 0xC0, 0x0F, 0x00, 0x00, 0x00, 0xFE, 0x01, 0x30, 0x05, 0xBF, 0xFB, 0xFF,
	0x10, 0x1F, 0x64, 0x90, 0xC0, 0x0F, 0x00, 0x00, 0x00, 0xFE, 0x01, 0x00, 0x00, 0x25, 0xF7, 0xFF,
	0xC0, 0xFF, 0xDF, 0x3C, 0x36, 0x85, 0xFE, 0x80, 0x00, 0x00, 0x00, 0x00, 0x66, 0x56, 0xFF, 0xFF,
	0x10, 0x1F, 0x64, 0x90, 0xC0, 0x0F, 0x00, 0x00, 0x00, 0xE0, 0x01, 0x00, 0x00, 0xDB, 0xF6, 0xFF,
	0x10, 0x1F, 0x64, 0x90, 0xC0, 0x0F, 0x00, 0x00, 0x00, 0xFE, 0x01, 0x00, 0x00, 0x24, 0xFB, 0xFF,
	0x10, 0x1F, 0x64, 0x90, 0xC0, 0x0F, 0x00, 0x00, 0x00, 0xFE, 0x01, 0x80, 0x44, 0xB6, 0xFF, 0xFF,
	0x80, 0xCD, 0xFF, 0xE0, 0xE7, 0x0C, 0x74, 0x4A, 0x80, 0x7D, 0x02, 0xC0, 0x00, 0xF4, 0x1F, 0x00,
	0x80, 0xCE, 0xFF, 0xE0, 0xE7, 0x0C, 0x72, 0x4A, 0x20, 0x5D, 0x03, 0xC2, 0xC8, 0x7F, 0x00, 0x00,
	0x20, 0x7F, 0x80, 0x19, 0x70, 0x02, 0x38, 0x00, 0x80, 0xFF, 0xFF, 0xFF, 0xFD, 0xFF, 0xFF, 0xFF,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x80, 0x08, 0xA8, 0x58, 0x82, 0xDD, 0x2F, 0x88, 0xD4, 0x01, 0x10, 0x00, 0x01, 0x80, 0xE1, 0x41,
	0x90, 0xE0, 0x83, 0x0F, 0x3E, 0xF0, 0x03, 0x28, 0xFB, 0xFD, 0xF1, 0x59, 0x4E, 0x9C, 0x44, 0x49,
	0x90, 0xFF, 0x7C, 0xE7, 0x07, 0xF0, 0x03, 0x54, 0xFF, 0xFF, 0x87, 0xE6, 0x6E, 0x7F, 0xF7, 0x9B,
	0xC0, 0xE9, 0x34, 0xAD, 0x26, 0x12, 0x05, 0x7F, 0x00, 0x10, 0xAA, 0xCB, 0xFF, 0xFF, 0xFF, 0xFF,
	0x80, 0x08, 0xAD, 0x1F, 0x6B, 0x9D, 0x33, 0x8C, 0x2E, 0x8D, 0xFF, 0x7F, 0xA3, 0xFC, 0x7F, 0xDF,
	0x80, 0x49, 0xD5, 0xDF, 0x67, 0x9D, 0x73, 0x83, 0x2A, 0xD5, 0xFF, 0x7F, 0xB1, 0xAE, 0xAA, 0xAA,
	0x26, 0xFF, 0xFF, 0xFF, 0x73, 0xAD, 0xDF, 0x93, 0xE7, 0x92, 0x73, 0x85, 0x4B, 0x9E, 0xE4, 0x49,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xFF, 0xBF, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xA0, 0xFF, 0xFF, 0xDF, 0xBE, 0xDD, 0xA2, 0xA3, 0x03, 0x00, 0x00, 0x80, 0x01, 0x00, 0x00, 0x00,
	0xC0, 0xFF, 0x9F, 0x4E, 0xDF, 0x6E, 0xFF, 0x07, 0x01, 0x00, 0x00, 0x00, 0x00, 0x40, 0x73, 0xFB,
	0x10, 0x1F, 0x70, 0x60, 0xC1, 0x0F, 0x00, 0x00, 0x00, 0xE0, 0x01, 0x06, 0xF5, 0xF4, 0xFF, 0xFF,
	0x10, 0x1F, 0x70, 0x60, 0x41, 0x01, 0x80, 0xF9, 0xFF, 0xFF, 0xF9, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x80, 0x01, 0xA0, 0x3B, 0x00, 0xFD, 0x11, 0x88, 0x18, 0x00, 0x10, 0x00, 0x60, 0xE0, 0x01, 0x81,
	0x10, 0xEE, 0xD0, 0xB6, 0xC6, 0x0F, 0xF8, 0xF3, 0xF3, 0xF7, 0x05, 0x40, 0x00, 0x16, 0x70, 0x0B,
	0x10, 0xA8, 0x3A, 0x4D, 0xE2, 0xEF, 0x00, 0x01, 0xA1, 0xC9, 0x01, 0x00, 0x00, 0x00, 0x82, 0xF0,
	0x10, 0x14, 0x68, 0x10, 0xC1, 0x0F, 0x00, 0x00, 0x00, 0x80, 0x01, 0x00, 0x64, 0xAA, 0xFF, 0xFF,
	0x80, 0x0A, 0x9F, 0x00, 0xEA, 0x0C, 0x74, 0x5C, 0x60, 0x7E, 0x00, 0x82, 0xA0, 0xF4, 0x07, 0x00,
	0x80, 0x8E, 0xFF, 0x60, 0xE7, 0x0C, 0xB4, 0x52, 0xE0, 0xD5, 0x0A, 0x40, 0x54, 0xFF, 0x01, 0x00,
	0x80, 0xCD, 0xDF, 0x00, 0xF0, 0x0A, 0x40, 0x05, 0x00, 0x54, 0x00, 0x80, 0x04, 0x5E, 0x01, 0x00,
	0x80, 0xCE, 0xFF, 0xC0, 0x73, 0x0E, 0xB6, 0xB5, 0x20, 0xD6, 0x0A, 0x40, 0xAE, 0xFE, 0x01, 0x00,
	0x80, 0xCE, 0xFF, 0xC0, 0x73, 0x0E, 0xB6, 0xAD, 0x20, 0x56, 0x0A, 0xC0, 0x2C, 0xFF, 0x01, 0x00,
	0x80, 0xCE, 0xFF, 0x80, 0x73, 0x0E, 0xB8, 0xB5, 0x80, 0x4A, 0x03, 0xC0, 0xC8, 0x1F, 0x00, 0x00,
	0x10, 0x1F, 0x70, 0x60, 0x01, 0x01, 0x80, 0xFF, 0xFF, 0xFF, 0xF1, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
};


static void FormatsConvert_Run(const boost::program_options::variables_map& map)
{
	OutputFile([]()
	{
		auto txd = new(false) rage::rdr3::pgDictionary<rage::rdr3::grcTexturePC>();
		txd->SetBlockMap();

		rage::rdr3::pgDictionary<rage::rdr3::grcTexturePC> newTextures;

		for (auto& entry : { "hats_locked",
			"horse_stow",
			"radial_slot_bg_6_0_wide",
			"radial_slot_bg_6_2_wide",
			"radial_slot_bg_8_0_horse_items",
			"radial_slot_bg_8_0_player_items",
			"radial_slot_bg_8_1_horse_items",
			"radial_slot_bg_8_1_player_items",
			"radial_slot_bg_8_2",
			"radial_slot_bg_8_3",
			"radial_slot_bg_8_4",
			"radial_slot_bg_8_5",
			"radial_slot_bg_8_6",
			"radial_slot_bg_8_7",
			"radial_slot_bg_8_7_player_items",
			"wheel_bg_1b",
			"wheel_header_bg",
			"wheel_select_big",
			"wheel_slot_gutter_line_0",
			"wheel_slot_gutter_line_1",
			"wheel_slot_gutter_line_2",
			"wheel_slot_mask_6_0_extended",
			"wheel_slot_mask_6_2",
			"wheel_slot_mask_6_3",
			"wheel_slot_mask_6_4",
			"wheel_slot_mask_6_5",
			"wheel_slot_mask_6_6",
			"wheel_slot_mask_upperleft",
			"wheel_slot_mask_uppermid",
			"wheel_slot_mask_upperright" })
		{
			auto texture = new(false) rage::rdr3::grcTexturePC();
			rage::rdr3::grcTexturePC* fiveTexture = new(false) rage::rdr3::grcTexturePC(
				120,
				120,
				0x62,
				0,
				1,
				dxt
			);

			fiveTexture->SetName(entry);

			newTextures.Add(HashString(entry), fiveTexture);
		}

		txd->SetFrom(&newTextures);
	}, 2, L"hud_quick_select.ytd");
}

static FxToolCommand formatsConvert("formats:txd", FormatsConvert_HandleArguments, FormatsConvert_Run);
#endif
#endif
