import {
  Button,
  Flex,
  Flex<PERSON><PERSON>ricter,
  Pad,
  Text,
  Title,
} from '@cfx-dev/ui-components';
import { observer } from 'mobx-react-lite';
import React from 'react';
import { createPortal } from 'react-dom';

import { CurrentGameBrand } from 'cfx/base/gameRuntime';
import { AuthStep, useCustomAuthService } from 'cfx/apps/mpMenu/services/customAuth/customAuth.service';
import { useLegalService } from 'cfx/apps/mpMenu/services/legal/legal.service';

import { mpMenu } from '../../mpMenu';
import { RegisterForm } from './RegisterForm';
import { VerifyForm } from './VerifyForm';
import { LoginForm } from './LoginForm';
import { BackgroundSlideshow } from './BackgroundSlideshow';

import s from './CustomAuth.module.scss';

// Background images list
const BACKGROUND_IMAGES = [
  require('../../../../../assets/images/05b1de299c79fd87f705632ed6386708.jpg'),
  require('../../../../../assets/images/8cbbae59e6963d71ed170541c63c2112.jpg'),
  require('../../../../../assets/images/9f97b1291d17c25219e6440386a3beee.jpg'),
  require('../../../../../assets/images/560d9cfb2f4aa35690b627c335c358da.jpg'),
  require('../../../../../assets/images/836eade90d395afd011ab184b17f2f71.jpg'),
  require('../../../../../assets/images/9098cbf7d4bfbcdcaa0a741aa0278c17.jpg'),
  require('../../../../../assets/images/a7a78b188fa75acc434e2cd191cb9115.jpg'),
];

export const CustomAuth = observer(function CustomAuth() {
  const authService = useCustomAuthService();
  const legalService = useLegalService();
  const [healthCheckPassed, setHealthCheckPassed] = React.useState<boolean | null>(null);

  // Check API health on mount
  React.useEffect(() => {
    authService.checkHealth().then(setHealthCheckPassed);
  }, [authService]);

  // Complete auth flow when user is authenticated
  React.useEffect(() => {
    console.log('🔍 CustomAuth useEffect - Authentication state changed:', {
      isAuthenticated: authService.isAuthenticated,
      currentStep: authService.currentStep,
      user: authService.user
    });

    if (authService.isAuthenticated) {
      console.log('✅ User is authenticated, completing auth flow...');
      legalService.completeAuthFlow();
    }
  }, [authService.isAuthenticated, authService.currentStep, legalService]);

  // Debug effect to monitor authentication state changes
  React.useEffect(() => {
    console.log('🔄 CustomAuth - Auth state monitor:', {
      isAuthenticated: authService.isAuthenticated,
      currentStep: authService.currentStep,
      hasUser: !!authService.user,
      userEmail: authService.user?.email
    });
  }, [authService.isAuthenticated, authService.currentStep, authService.user]);

  const renderCurrentStep = () => {
    // Show health check status with modern styling
    if (healthCheckPassed === false) {
      return (
        <div className={s.formContainer}>
          <div className={s.formHeader}>
            <h1 className={s.title}>Dịch Vụ Không Khả Dụng</h1>
            <p className={s.subtitle}>
              Dịch vụ xác thực hiện tại không khả dụng. Vui lòng thử lại sau.
            </p>
          </div>
          <button
            className={s.modernButton}
            onClick={() => authService.checkHealth().then(setHealthCheckPassed)}
          >
            Thử Lại Kết Nối
          </button>
        </div>
      );
    }

    if (healthCheckPassed === null) {
      return (
        <div className={s.formContainer}>
          <div className={s.formHeader}>
            <h1 className={s.title}>Đang Kết Nối...</h1>
            <p className={s.subtitle}>
              Đang kiểm tra trạng thái dịch vụ xác thực
            </p>
          </div>
          <div className={s.loadingIndicator} style={{ justifyContent: 'center', padding: '2rem' }}>
            <div className={s.spinner}></div>
            <span>Vui lòng đợi...</span>
          </div>
        </div>
      );
    }

    switch (authService.currentStep) {
      case AuthStep.REGISTER:
        return <RegisterForm />;
      case AuthStep.VERIFY:
        return <VerifyForm />;
      case AuthStep.LOGIN:
        return <LoginForm />;
      case AuthStep.AUTHENTICATED:
        // If we reach here, it means the user is authenticated but still showing CustomAuth
        // This should trigger navigation to home
        console.log('⚠️ CustomAuth - User is authenticated but still in auth flow, triggering navigation...');
        return (
          <div className={s.formContainer}>
            <div className={s.formHeader}>
              <h1 className={s.title}>Đăng Nhập Thành Công!</h1>
              <p className={s.subtitle}>Đang chuyển hướng đến trang chủ...</p>
            </div>
            <div className={s.loadingIndicator} style={{ justifyContent: 'center', padding: '2rem' }}>
              <div className={s.spinner}></div>
              <span>Vui lòng đợi...</span>
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  // Create the auth content
  const authContent = (
    <>
      {/* Background slideshow */}
      <BackgroundSlideshow
        images={BACKGROUND_IMAGES}
        interval={6000} // 6 seconds between transitions
        fadeDuration={1500} // 1.5 second fade duration
      />

      {/* Auth form overlay */}
      <div className={s.root}>
        <div className={s.formContainer}>
          <div className={s.formContent}>
            {renderCurrentStep()}
          </div>
        </div>
      </div>
    </>
  );

  // Use React Portal to render outside of #cfxui-root to bypass viewport constraints
  return createPortal(authContent, document.body);
});
