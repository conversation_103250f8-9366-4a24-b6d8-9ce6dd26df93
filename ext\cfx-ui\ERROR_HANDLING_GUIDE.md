# 🛡️ Hướng dẫn Error Handling - API GTA V Auth Service

## 📋 Tổng quan

API đã được cải tiến với hệ thống error handling hoàn chỉnh, đảm bảo:
- **Thông báo rõ ràng** cho người dùng cuối
- **Mã lỗi chuẩn** để client xử lý logic
- **Chi tiết hỗ trợ** để debug và troubleshooting
- **Tính nhất quán** trong tất cả responses

## 🎯 Nguyên tắc thiết kế

### 1. **Thông báo thân thiện với người dùng**
- Sử dụng ngôn ngữ dễ hiểu, tr<PERSON>h thuật ngữ kỹ thuật
- Đưa ra hướng dẫn cụ thể về cách khắc phục
- Giải thích tại sao lỗi xảy ra và làm thế nào để tránh

### 2. **Mã lỗi chuẩn hóa**
- Mỗi loại lỗi có mã riêng biệt (ERROR_CODES)
- Client có thể dựa vào mã để xử lý logic
- Dễ dàng tracking và monitoring

### 3. **Chi tiết hỗ trợ**
- Thông tin debug trong development mode
- Gợi ý troubleshooting
- Links đến documentation liên quan

## 📊 Cấu trúc Response chuẩn

### Response thành công
```json
{
  "success": true,
  "message": "Thông báo thành công",
  "timestamp": "2024-01-01T12:00:00.000Z",
  "data": {
    // Dữ liệu trả về
  }
}
```

### Response lỗi
```json
{
  "success": false,
  "message": "Thông báo lỗi cho người dùng",
  "code": "ERROR_CODE",
  "timestamp": "2024-01-01T12:00:00.000Z",
  "details": {
    // Chi tiết hỗ trợ (chỉ trong development)
  }
}
```

## 🔍 Danh sách mã lỗi

| Mã lỗi | Mô tả | HTTP Status | Xử lý đề xuất |
|---------|-------|-------------|---------------|
| `EMAIL_EXISTS` | Email đã được sử dụng | 400 | Đề xuất đăng nhập hoặc dùng email khác |
| `USER_NOT_FOUND` | Không tìm thấy user | 404 | Kiểm tra email hoặc đăng ký mới |
| `INVALID_CREDENTIALS` | Sai email/password | 401 | Hiển thị số lần thử còn lại |
| `ACCOUNT_LOCKED` | Tài khoản bị khóa | 423 | Hiển thị thời gian mở khóa |
| `ACCOUNT_UNVERIFIED` | Chưa xác thực email | 403 | Hướng dẫn xác thực |
| `ALREADY_VERIFIED` | Đã xác thực rồi | 400 | Chuyển đến đăng nhập |
| `INVALID_CODE` | Mã xác thực sai | 400 | Hướng dẫn nhập lại |
| `EMAIL_SEND_FAILED` | Không gửi được email | 500 | Liên hệ hỗ trợ |
| `VALIDATION_ERROR` | Lỗi validation | 400 | Hiển thị chi tiết từng field |
| `RATE_LIMIT_EXCEEDED` | Quá nhiều requests | 429 | Hiển thị thời gian chờ |
| `INTERNAL_ERROR` | Lỗi server | 500 | Thử lại sau |

## 🎨 Ví dụ responses chi tiết

### 1. Đăng ký thành công
```json
{
  "success": true,
  "message": "Đăng ký tài khoản thành công! Chúng tôi đã gửi mã xác thực đến email của bạn.",
  "timestamp": "2024-01-01T12:00:00.000Z",
  "data": {
    "userId": 123,
    "username": "testuser",
    "email": "<EMAIL>",
    "accountStatus": "UNVERIFIED",
    "verification": {
      "required": true,
      "expiresIn": "15 phút",
      "instruction": "Vui lòng kiểm tra email và sử dụng mã 6 số để xác thực tài khoản"
    },
    "nextStep": {
      "action": "verify_email",
      "endpoint": "/api/auth/verify",
      "description": "Xác thực email bằng mã đã gửi"
    }
  }
}
```

### 2. Tài khoản chưa xác thực
```json
{
  "success": false,
  "message": "Tài khoản chưa được xác thực email. Vui lòng kiểm tra email và xác thực tài khoản trước khi đăng nhập.",
  "code": "ACCOUNT_UNVERIFIED",
  "timestamp": "2024-01-01T12:00:00.000Z",
  "details": {
    "accountStatus": "UNVERIFIED",
    "requiredAction": "verify_email",
    "instruction": "Tìm email xác thực trong hộp thư hoặc thư mục spam",
    "helpText": "Nếu không nhận được email, vui lòng liên hệ hỗ trợ"
  }
}
```

### 3. Tài khoản bị khóa
```json
{
  "success": false,
  "message": "Tài khoản tạm thời bị khóa do đăng nhập sai quá nhiều lần. Vui lòng thử lại sau 25 phút.",
  "code": "ACCOUNT_LOCKED",
  "timestamp": "2024-01-01T12:00:00.000Z",
  "details": {
    "accountStatus": "LOCKED",
    "lockUntil": "2024-01-01T12:25:00.000Z",
    "remainingMinutes": 25,
    "reason": "Quá nhiều lần đăng nhập thất bại",
    "instruction": "Đợi hết thời gian khóa hoặc liên hệ hỗ trợ để mở khóa",
    "preventionTip": "Đảm bảo nhập đúng email và mật khẩu để tránh bị khóa"
  }
}
```

### 4. Validation errors
```json
{
  "success": false,
  "message": "Dữ liệu đầu vào không hợp lệ. Vui lòng kiểm tra lại thông tin.",
  "code": "VALIDATION_ERROR",
  "timestamp": "2024-01-01T12:00:00.000Z",
  "errors": [
    {
      "field": "email",
      "message": "Email không hợp lệ",
      "value": "invalid-email"
    },
    {
      "field": "password",
      "message": "Mật khẩu phải từ 8-128 ký tự",
      "value": "123"
    }
  ]
}
```

### 5. Đăng nhập thành công
```json
{
  "success": true,
  "message": "Đăng nhập thành công! Chào mừng bạn quay trở lại.",
  "timestamp": "2024-01-01T12:00:00.000Z",
  "data": {
    "authentication": {
      "token": "eyJhbGciOiJIUzI1NiIs...",
      "tokenType": "Bearer",
      "expiresIn": "24h",
      "usage": "Thêm token vào header: Authorization: Bearer <token>"
    },
    "user": {
      "id": 123,
      "username": "testuser",
      "email": "<EMAIL>",
      "accountStatus": "ACTIVE",
      "isVerified": true,
      "createdAt": "2024-01-01T10:00:00.000Z"
    },
    "session": {
      "ipAddress": "*************",
      "userAgent": "Mozilla/5.0...",
      "loginTime": "2024-01-01T12:00:00.000Z",
      "location": "Việt Nam"
    }
  }
}
```

## 💡 Hướng dẫn xử lý cho Client

### 1. **Kiểm tra success field**
```javascript
if (response.success) {
    // Xử lý thành công
    handleSuccess(response.data);
} else {
    // Xử lý lỗi
    handleError(response.code, response.message, response.details);
}
```

### 2. **Xử lý theo mã lỗi**
```javascript
function handleError(code, message, details) {
    switch(code) {
        case 'ACCOUNT_UNVERIFIED':
            showVerificationPrompt(details.instruction);
            break;
            
        case 'ACCOUNT_LOCKED':
            showLockoutMessage(details.remainingMinutes);
            break;
            
        case 'INVALID_CREDENTIALS':
            showLoginError(message, details.remainingAttempts);
            break;
            
        case 'VALIDATION_ERROR':
            showValidationErrors(response.errors);
            break;
            
        default:
            showGenericError(message);
    }
}
```

### 3. **Hiển thị thông báo**
```javascript
// Thông báo thành công
function showSuccess(message) {
    toast.success(message);
}

// Thông báo lỗi với hướng dẫn
function showError(message, helpText) {
    toast.error(message);
    if (helpText) {
        showHelpTooltip(helpText);
    }
}

// Validation errors
function showValidationErrors(errors) {
    errors.forEach(error => {
        highlightField(error.field, error.message);
    });
}
```

## 🔧 Cấu hình cho Development

### Environment variables
```env
NODE_ENV=development  # Hiển thị chi tiết lỗi
```

### Logging
- Tất cả lỗi được log với timestamp
- Chi tiết stack trace trong development
- Structured logging cho monitoring

## 📈 Monitoring và Analytics

### Metrics cần theo dõi
- Tỷ lệ lỗi theo endpoint
- Thời gian response
- Số lượng tài khoản bị khóa
- Tỷ lệ xác thực email thành công

### Alerting
- Spike trong error rates
- Quá nhiều tài khoản bị khóa
- Email service down
- Database connection issues

## 🎯 Best Practices

### 1. **Cho Developer**
- Luôn sử dụng ResponseHandler thay vì tự tạo response
- Log chi tiết lỗi để debug
- Test tất cả error scenarios
- Cập nhật documentation khi thêm error codes mới

### 2. **Cho Client Developer**
- Xử lý tất cả error codes có thể
- Hiển thị thông báo thân thiện
- Implement retry logic cho network errors
- Cache error messages để offline support

### 3. **Cho UX/UI**
- Thiết kế error states rõ ràng
- Cung cấp action buttons (retry, help)
- Sử dụng progressive disclosure cho chi tiết
- Test với real error scenarios

---

**🎉 Kết quả:** API hiện có error handling hoàn chỉnh, thân thiện với người dùng và dễ dàng maintain!
