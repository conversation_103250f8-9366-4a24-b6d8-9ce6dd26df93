@use "~@cfx-dev/ui-components/dist/styles-scss/ui" as ui;

// Custom light color scheme
$fg: #2C2C2C; // Đen cho text
$bg: #F5E6A8; // Vàng kem cho background

.cfxui-theme-gta5-light {
  @include ui.define-main-colors($bg, $fg, ('primary': #FF8C42, // <PERSON> chính
      'secondary': #7CB342, // Xanh l<PERSON>
      'accent': #2C2C2C, // Đen cho accent
      'warning': #FF8C42, // Cam cho warning
      'success': #7CB342, // Xanh lá cho success
      'danger': #d9534f // Giữ đỏ cho danger
    ));

  // Asset overrides
  @include ui.def('checkered-pattern', url(assets/images/checkered_light.svg));

  // Color overrides
  @include ui.def ('text-opacity-10', .20);
  @include ui.define-color-token('text-a10', ui.color('main', 950, .20));
  @include ui.def ('text-opacity-25', .35);
  @include ui.define-color-token('text-a25', ui.color('main', 950, .35));
  @include ui.def ('text-opacity-50', .60);
  @include ui.define-color-token('text-a50', ui.color('main', 950, .60));
  @include ui.def ('text-opacity-75', .85);
  @include ui.define-color-token('text-a75', ui.color('main', 950, .85));

  @include ui.define-color-token('text-warning', ui.color('warning', 700));

  @include ui.define-color-token('shadow-small', rgba(0, 0, 0, .3));
  @include ui.define-color-token('shadow-large', rgba(0, 0, 0, .3));

  @include ui.define-color-token('backdrop', ui.color('main', 50, .75));
  @include ui.define-color-token('backdrop-shader', ui.color('main', 100, .75));

  @include ui.define-color-token('button-primary-text', ui.color('main'));
  @include ui.define-color-token('button-primary-hover-text', ui.color('main'));
  @include ui.define-color-token('button-primary-active-text', ui.color('main', 950));

  @include ui.define-color-token('premium-badge-text', ui.color('main'));
  @include ui.define-color-token('premium-badge-background', ui.color('main', 950));

  @include ui.define-color-token('loaf-text', ui.color('main', 950, .75));
  @include ui.define-color-token('loaf-background', ui.color('main', 950, .075));


  // Custom color classes with new palette for light theme
  .color-1 {
    color: #FF8C42; // Cam chính
  }

  .color-2 {
    color: #7CB342; // Xanh lá
  }

  .color-3 {
    color: #2C2C2C; // Đen
  }

  .color-4 {
    color: darken(#FF8C42, 15%); // Cam đậm
  }

  .color-5 {
    color: darken(#7CB342, 15%); // Xanh lá đậm
  }

  .color-6 {
    color: lighten(#2C2C2C, 20%); // Xám
  }

  .color-8 {
    color: #FF8C42; // Cam
  }

  .color-9 {
    color: lighten(#2C2C2C, 30%); // Xám nhạt
  }

  // Too thin otherwise
  --font-weight-normal: 400;
  --font-weight-bold: 500;
  --font-weight-bolder: 600;
}