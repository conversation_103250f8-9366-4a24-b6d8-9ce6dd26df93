#include <sdkddkver.h>

namespace CitiLaunch
{
    [default_interface]
    runtimeclass BackdropBrush : Windows.UI.Xaml.Media.XamlCompositionBrushBase
    {
        BackdropBrush();
    }
}

namespace Microsoft.Graphics.Canvas.Effects
{
    [version(NTDDI_WINTHRESHOLD)]
    typedef struct Matrix5x4
    {
        float M11, M12, M13, M14;
        float M21, M22, M23, M24;
        float M31, M32, M33, M34;
        float M41, M42, M43, M44;
        float M51, M52, M53, M54;
    } Matrix5x4;
}