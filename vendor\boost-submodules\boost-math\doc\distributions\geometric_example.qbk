[section:geometric_eg Geometric Distribution Examples]
                       
[import ../../example/geometric_examples.cpp]
[geometric_eg1_1]
[geometric_eg1_2]

See full source C++ of this example at
[@../../example/geometric_examples.cpp geometric_examples.cpp]
                       
[link math_toolkit.stat_tut.weg.neg_binom_eg.neg_binom_conf See negative_binomial confidence interval example.]

[endsect] [/section:geometric_eg Geometric Distribution Examples]

[/ geometric.qbk
  Copyright 2010 <PERSON> and <PERSON>.
  Distributed under the Boost Software License, Version 1.0.
  (See accompanying file LICENSE_1_0.txt or copy at
  http://www.boost.org/LICENSE_1_0.txt).
]

