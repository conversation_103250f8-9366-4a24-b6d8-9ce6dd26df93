@echo off
echo ========================================
echo    GangHaiCity Standalone Tester
echo ========================================
echo.

:: Kiểm tra xem có file standalone không
if not exist "GangHaiCity_Standalone\GangHaiCity.exe" (
    echo ERROR: Standalone build not found!
    echo Please run build_standalone.bat first.
    pause
    exit /b 1
)

echo Test 1: Checking standalone executable...
if exist "GangHaiCity_Standalone\GangHaiCity.exe" (
    echo ✓ GangHaiCity.exe found
) else (
    echo ✗ GangHaiCity.exe not found
    pause
    exit /b 1
)

echo.
echo Test 2: Checking standalone mode detection...
echo This test will copy the exe to a temporary location and run it.
echo.

:: Tạo thư mục test tạm thời
set TEST_DIR=temp_standalone_test
if exist "%TEST_DIR%" rmdir /s /q "%TEST_DIR%"
mkdir "%TEST_DIR%"

:: Copy exe to test directory
copy "GangHaiCity_Standalone\GangHaiCity.exe" "%TEST_DIR%\" >nul

echo ✓ Copied executable to test directory: %TEST_DIR%
echo.

echo Test 3: Running standalone mode...
echo The executable should show custom interface instead of FiveM.
echo Press any key to start the test...
pause >nul

:: Chạy test
cd "%TEST_DIR%"
start "" "GangHaiCity.exe"
cd ..

echo.
echo Test started! Check if:
echo 1. A custom window appears (not FiveM)
echo 2. The window shows "GangHaiCity - Standalone Mode"
echo 3. There are custom buttons and interface elements
echo.
echo If you see the custom interface, the standalone mode is working correctly!
echo.

:: Cleanup option
echo.
set /p cleanup="Do you want to clean up test files? (y/n): "
if /i "%cleanup%"=="y" (
    if exist "%TEST_DIR%" rmdir /s /q "%TEST_DIR%"
    echo ✓ Test files cleaned up
)

echo.
echo Test completed!
pause
