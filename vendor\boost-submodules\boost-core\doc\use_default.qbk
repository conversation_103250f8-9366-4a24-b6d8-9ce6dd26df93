[/
Copyright 2019 <PERSON>
(<EMAIL>)

Distributed under the Boost Software License, Version 1.0.
(http://www.boost.org/LICENSE_1_0.txt)
]

[section:use_default use_default]

[section Overview]

The header <boost/core/use_default.hpp> provides the type `boost::use_default`
which is used by other Boost libraries as a sentinel type in templates to
indicate defaults.

[endsect]

[section Example]

```
template<class Derived, class Base,
   class Value = boost::use_default,
   class CategoryOrTraversal = boost::use_default,
   class Reference = boost::use_default,
   class Difference = boost::use_default>
class iterator_adaptor;

template<class Value>
class node_iterator
    : public iterator_adaptor<node_iterator<Value>, Value*,
        boost::use_default, boost::forward_traversal_tag>;
```

[endsect]

[section Reference]

```
namespace boost {
  struct use_default { };
}
```

[endsect]

[endsect]
