/**
 * Development Bypass Script
 * Chạy script n<PERSON><PERSON> để bypass hoàn toàn tất cả native calls
 */

const fs = require('fs');
const path = require('path');

console.log('🚀 FiveM UI Development Bypass Script');
console.log('=====================================');

// Backup original files
function backupFile(filePath) {
    const backupPath = filePath + '.backup';
    if (!fs.existsSync(backupPath)) {
        fs.copyFileSync(filePath, backupPath);
        console.log(`✅ Backed up: ${filePath}`);
    }
}

// Restore original files
function restoreFile(filePath) {
    const backupPath = filePath + '.backup';
    if (fs.existsSync(backupPath)) {
        fs.copyFileSync(backupPath, filePath);
        console.log(`✅ Restored: ${filePath}`);
    }
}

// Apply bypass to a file
function applyBypass(filePath, searchPattern, replacement) {
    if (!fs.existsSync(filePath)) {
        console.log(`❌ File not found: ${filePath}`);
        return false;
    }
    
    backupFile(filePath);
    
    let content = fs.readFileSync(filePath, 'utf8');
    
    if (content.includes(searchPattern)) {
        content = content.replace(searchPattern, replacement);
        fs.writeFileSync(filePath, content);
        console.log(`✅ Applied bypass to: ${filePath}`);
        return true;
    } else {
        console.log(`⚠️  Pattern not found in: ${filePath}`);
        return false;
    }
}

// Main bypass function
function applyAllBypasses() {
    console.log('\n🔧 Applying bypasses...\n');
    
    // 1. Bypass mpMenu.ts
    const mpMenuPath = path.join(__dirname, 'src/cfx/apps/mpMenu/mpMenu.ts');
    applyBypass(
        mpMenuPath,
        'nuiWindow.invokeNative(native, arg);',
        `// BYPASS: Native call bypassed
        console.log('[BYPASS] Native call:', native, arg);
        // nuiWindow.invokeNative(native, arg);`
    );
    
    // 2. Bypass convars service
    const convarsPath = path.join(__dirname, 'src/cfx/apps/mpMenu/services/convars/convars.service.ts');
    if (fs.existsSync(convarsPath)) {
        applyBypass(
            convarsPath,
            'mpMenu.invokeNative(\'getConvars\');',
            `// BYPASS: getConvars bypassed
            console.log('[BYPASS] getConvars called');
            // mpMenu.invokeNative('getConvars');
            
            // Mock convars response
            setTimeout(() => {
                this.handleConvarsSet({
                    vars: [
                        { key: 'ui_streamerMode', value: 'false' },
                        { key: 'ui_quickAccessLocalhostPort', value: '30120' },
                        { key: 'ui_updateChannel', value: 'production' },
                        { key: 'ui_customBackdrop', value: '' },
                        { key: 'ui_preferLightColorScheme', value: 'false' },
                        { key: 'ui_preferBlurredBackdrop', value: 'true' },
                        { key: 'ui_premium', value: 'false' },
                        { key: 'ui_customBrandingEmoji', value: '' }
                    ]
                });
            }, 100);`
        );
    }
    
    // 3. Create bypass webpack config
    const webpackConfigPath = path.join(__dirname, 'config/webpack.config.bypass.js');
    const webpackBypassContent = `
const path = require('path');
const originalConfig = require('./webpack.config.js');

module.exports = (env, argv) => {
    const config = originalConfig(env, argv);
    
    // Add bypass plugin
    config.plugins.push(
        new (require('webpack')).DefinePlugin({
            '__CFXUI_BYPASS__': JSON.stringify(true)
        })
    );
    
    // Add bypass alias
    config.resolve.alias = {
        ...config.resolve.alias,
        'bypass': path.resolve(__dirname, '../src/cfx/apps/mpMenu/utils/nativeBypass.ts')
    };
    
    return config;
};
`;
    
    fs.writeFileSync(webpackConfigPath, webpackBypassContent);
    console.log(`✅ Created bypass webpack config`);
    
    // 4. Update package.json scripts
    const packageJsonPath = path.join(__dirname, 'package.json');
    if (fs.existsSync(packageJsonPath)) {
        const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
        
        if (!packageJson.scripts['serve:bypass']) {
            packageJson.scripts['serve:bypass'] = 'webpack serve --config config/webpack.config.bypass.js --mode development --env app=mpMenu';
            packageJson.scripts['build:bypass'] = 'webpack --config config/webpack.config.bypass.js --mode production --env app=mpMenu';
            
            fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
            console.log(`✅ Added bypass scripts to package.json`);
        }
    }
    
    console.log('\n✅ All bypasses applied successfully!');
    console.log('\n🚀 Run commands:');
    console.log('   yarn serve:bypass    # Start with bypass');
    console.log('   yarn build:bypass    # Build with bypass');
    console.log('   node dev-bypass.js restore  # Restore original files');
}

// Restore function
function restoreAllFiles() {
    console.log('\n🔄 Restoring original files...\n');
    
    const filesToRestore = [
        'src/cfx/apps/mpMenu/mpMenu.ts',
        'src/cfx/apps/mpMenu/services/convars/convars.service.ts'
    ];
    
    filesToRestore.forEach(file => {
        const fullPath = path.join(__dirname, file);
        restoreFile(fullPath);
    });
    
    // Remove bypass files
    const bypassFiles = [
        'config/webpack.config.bypass.js'
    ];
    
    bypassFiles.forEach(file => {
        const fullPath = path.join(__dirname, file);
        if (fs.existsSync(fullPath)) {
            fs.unlinkSync(fullPath);
            console.log(`✅ Removed: ${file}`);
        }
    });
    
    console.log('\n✅ All files restored!');
}

// Main execution
const command = process.argv[2];

if (command === 'restore') {
    restoreAllFiles();
} else {
    applyAllBypasses();
}

console.log('\n🎉 Done!');
