[/
  Copyright 2023 <PERSON>
  Distributed under the Boost Software License, Version 1.0.
  https://boost.org/LICENSE_1_0.txt
]

[section:max_align max_align]

[simplesect Authors]

* <PERSON>

[endsimplesect]

[section Header <boost/core/max_align.hpp>]

The header `<boost/core/max_align.hpp>` defines the type
`boost::core::max_align_t`, a portable equivalent of
`std::max_align_t`, and the constant `boost::core::max_align`,
the alignment of `max_align_t`.

[section Synopsis]

``
namespace boost
{
namespace core
{

union max_align_t;

constexpr std::size_t max_align = alignof(max_align_t);

} // namespace core
} // namespace boost
``

[endsect]

[endsect]

[endsect]
