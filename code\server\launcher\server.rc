/*
 * This file is part of the CitizenFX project - http://citizen.re/
 *
 * See <PERSON><PERSON><PERSON><PERSON> and MENTIONS in the root of the source tree for information
 * regarding licensing.
 */

// Microsoft Visual C++ generated resource script.
//
#define IDI_ICON1                       1
#include <windows.h>

LANGUAGE LANG_ENGLISH, SUBLANG_ENGLISH_US


/////////////////////////////////////////////////////////////////////////////
//
// Icon
//

// Icon with lowest ID value placed first to ensure application icon
// remains consistent on all systems.
IDI_ICON1               ICON                    "server.ico"
1						RT_MANIFEST				"server.manifest"

/////////////////////////////////////////////////////////////////////////////
//
// Version
//
VS_VERSION_INFO VERSIONINFO
 FILEVERSION 1,0,0,0
 PRODUCTVERSION 1,0,0,0
 FILEFLAGSMASK 0x3fL
#ifdef _DEBUG
 FILEFLAGS 0x1L
#else
 FILEFLAGS 0x0L
#endif
 FILEOS 0x40004L
 FILETYPE 0x1L
 FILESUBTYPE 0x0L
BEGIN
    BLOCK "StringFileInfo"
    BEGIN
        BLOCK "040904b0"
        BEGIN
            VALUE "CompanyName", "Cfx.re"
			VALUE "FileDescription", "Cfx.re Platform Server (FXServer)"
            VALUE "FileVersion", "*******"
            VALUE "InternalName", "Citizen"
            VALUE "LegalCopyright", "(C) 2017-2020 Cfx.re"
            VALUE "OriginalFilename", "FXServer.exe"
            VALUE "ProductName", "CitizenFX"
            VALUE "ProductVersion", "*******"
        END
    END
    BLOCK "VarFileInfo"
    BEGIN
        VALUE "Translation", 0x409, 1200
    END
END
