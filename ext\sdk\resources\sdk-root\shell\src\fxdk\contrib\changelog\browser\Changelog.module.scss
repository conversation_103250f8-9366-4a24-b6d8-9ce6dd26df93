@import "variables";

.root {
  .older {
    display: flex;
    align-items: center;
    justify-content: center;

    margin-bottom: $q*6;

    font-size: $fs08;
    font-weight: 100;
    color: rgba($fgColor, .5);
    letter-spacing: 1px;

    &::before {
      display: block;
      content: '';

      flex-grow: 1;

      margin-right: $q*2;

      border-bottom: dashed 1px rgba($fgColor, .5);
    }

    &::after {
      display: block;
      content: '';

      flex-grow: 1;

      margin-left: $q*2;

      border-bottom: dashed 1px rgba($fgColor, .5);
    }
  }

  .entry {
    margin-bottom: $q*12;

    &:first-child {
      margin-bottom: $q*6;
    }

    &:last-child {
      margin-bottom: 0;
    }

    .title {
      @include fontSecondary;
      font-size: $fs2;
      font-weight: 400;
    }

    .content {
      @include fontPrimary;

      margin-top: $q*4;

      * {
        line-height: 1.3;
        font-weight: 100;
        font-size: $fs1;
      }

      p {
        margin: $q*2 0;
      }

      strong {
        font-weight: 500;
      }

      ul {
        margin-left: $q*6;

        li {
          line-height: 1.2;

          margin-bottom: $q*3;
        }
      }

      kbd {
        background-color: rgba($fgColor, .1);
        padding: 0 $q;

        border-radius: 2px;
      }
    }
  }
}

.modal {
  display: flex;
  flex-direction: column;

  height: $modalContentHeight;

  .changelog {
    flex-grow: 1;

    overflow-y: auto;

    padding: $q*4;
  }
}
