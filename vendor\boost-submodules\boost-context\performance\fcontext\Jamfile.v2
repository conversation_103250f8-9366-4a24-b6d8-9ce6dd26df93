
#          Copyright Oliver <PERSON> 2009.
# Distributed under the Boost Software License, Version 1.0.
#    (See accompanying file LICENSE_1_0.txt or copy at
#          http://www.boost.org/LICENSE_1_0.txt)

# For more information, see http://www.boost.org/

import common ;
import feature ;
import indirect ;
import modules ;
import os ;
import toolset ;

project boost/context/performance/fcontext
    : requirements
      <library>/boost/chrono//boost_chrono
      <library>/boost/context//boost_context
      <library>/boost/program_options//boost_program_options
      <target-os>linux,<toolset>gcc,<segmented-stacks>on:<cxxflags>-fsplit-stack
      <target-os>linux,<toolset>gcc,<segmented-stacks>on:<cxxflags>-DBOOST_USE_SEGMENTED_STACKS
      <toolset>clang,<segmented-stacks>on:<cxxflags>-fsplit-stack
      <toolset>clang,<segmented-stacks>on:<cxxflags>-DBOOST_USE_SEGMENTED_STACKS
      <link>static
      <optimization>speed
      <threading>multi
      <variant>release
      <cxxflags>-DBOOST_DISABLE_ASSERTS
    ;

exe performance
   : performance.cpp
   ;
