// Copyright 2018 The Chromium Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

package org.chromium.base.natives;

// This file is autogenerated by
//     base/android/jni_generator/jni_registration_generator.py
// Please do not change its content.

public class GEN_JNI {
  public static final boolean TESTING_ENABLED = false;
  public static final boolean REQUIRE_MOCK = false;

  public static native void org_chromium_example_SampleProxyJni_foo(long nativePtr);

  public static native int org_chromium_example_SampleProxyJni_bar(int x, int y);

  public static native String org_chromium_example_SampleProxyJni_foobar(String x, String y);

}
