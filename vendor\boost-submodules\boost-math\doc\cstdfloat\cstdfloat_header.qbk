[article Standardized Floating-Point typedefs for C and C++
   [id float_t]
    [quickbook 1.6]
    [copyright 2014  <PERSON>, <PERSON>, <PERSON>]
    [license
        Distributed under the Boost Software License, Version 1.0.
        (See accompanying file LICENSE_1_0.txt or copy at
        [@http://www.boost.org/LICENSE_1_0.txt])
    ]
    [authors [<PERSON><PERSON><PERSON>, <PERSON>],  [<PERSON>, <PERSON>], [<PERSON><PERSON><PERSON>, <PERSON>] ]
    [/last-revision $Date$]
    [/version 1.8.3]
]

[def __IEEE754  [@http://en.wikipedia.org/wiki/IEEE_floating_point IEEE_floating_point]]
[def __N3626  [@http://www.open-std.org/jtc1/sc22/wg21/docs/papers/2013/n3626.pdf N3626]]
[def __N1703 [@http://www.open-std.org/jtc1/sc22/wg14/www/docs/n1703.pdf N1703]]

[note A printer-friendly PDF version of this manual is also available.]

[include cstdfloat.qbk]

[/ cstdfloat_header.qbk
  Copyright 2014 <PERSON>, <PERSON> and <PERSON>.
  Distributed under the Boost Software License, Version 1.0.
  (See accompanying file LICENSE_1_0.txt or copy at
  http://www.boost.org/LICENSE_1_0.txt).
]

