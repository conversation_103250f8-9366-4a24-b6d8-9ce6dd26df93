# Copyright 2018 The Chromium Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

mojom = "//mojo/public/mojom/base/ref_counted_memory.mojom"
public_headers = [ "//base/memory/ref_counted_memory.h" ]
traits_headers = [ "//mojo/public/cpp/base/ref_counted_memory_mojom_traits.h" ]
sources = [
  "//mojo/public/cpp/base/ref_counted_memory_mojom_traits.cc",
  "//mojo/public/cpp/base/ref_counted_memory_mojom_traits.h",
]
public_deps = [
  "//base",
  "//mojo/public/cpp/base",
  "//mojo/public/cpp/base:shared_typemap_traits",
]
type_mappings = [ "mojo.common.mojom.RefCountedMemory=::scoped_refptr<::base::RefCountedMemory>[copyable_pass_by_value,nullable_is_same_type]" ]
