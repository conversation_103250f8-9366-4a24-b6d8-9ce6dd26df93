// Copyright 2014 The Chromium Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     base/android/jni_generator/jni_generator.py
// For
//     android/view/MotionEvent

#ifndef android_view_MotionEvent_JNI
#define android_view_MotionEvent_JNI

#include <jni.h>

#include "base/android/jni_generator/jni_generator_helper.h"


// Step 1: Forward declarations.

JNI_REGISTRATION_EXPORT extern const char kClassPath_android_view_MotionEvent[];
const char kClassPath_android_view_MotionEvent[] = "android/view/MotionEvent";
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_REGISTRATION_EXPORT std::atomic<jclass> g_android_view_MotionEvent_clazz(nullptr);
#ifndef android_view_MotionEvent_clazz_defined
#define android_view_MotionEvent_clazz_defined
inline jclass android_view_MotionEvent_clazz(JNIEnv* env) {
  return base::android::LazyGetClass(env, kClassPath_android_view_MotionEvent,
      &g_android_view_MotionEvent_clazz);
}
#endif


// Step 2: Constants (optional).

namespace JNI_MotionEvent {

enum Java_MotionEvent_constant_fields {
  INVALID_POINTER_ID = -1,
  ACTION_MASK = 255,
  ACTION_DOWN = 0,
  ACTION_UP = 1,
  ACTION_MOVE = 2,
  ACTION_CANCEL = 3,
  ACTION_OUTSIDE = 4,
  ACTION_POINTER_DOWN = 5,
  ACTION_POINTER_UP = 6,
  ACTION_HOVER_MOVE = 7,
  ACTION_SCROLL = 8,
  ACTION_HOVER_ENTER = 9,
  ACTION_HOVER_EXIT = 10,
  ACTION_POINTER_INDEX_MASK = 65280,
  ACTION_POINTER_INDEX_SHIFT = 8,
  ACTION_POINTER_1_DOWN = 5,
  ACTION_POINTER_2_DOWN = 261,
  ACTION_POINTER_3_DOWN = 517,
  ACTION_POINTER_1_UP = 6,
  ACTION_POINTER_2_UP = 262,
  ACTION_POINTER_3_UP = 518,
  ACTION_POINTER_ID_MASK = 65280,
  ACTION_POINTER_ID_SHIFT = 8,
  FLAG_WINDOW_IS_OBSCURED = 1,
  EDGE_TOP = 1,
  EDGE_BOTTOM = 2,
  EDGE_LEFT = 4,
  EDGE_RIGHT = 8,
  AXIS_X = 0,
  AXIS_Y = 1,
  AXIS_PRESSURE = 2,
  AXIS_SIZE = 3,
  AXIS_TOUCH_MAJOR = 4,
  AXIS_TOUCH_MINOR = 5,
  AXIS_TOOL_MAJOR = 6,
  AXIS_TOOL_MINOR = 7,
  AXIS_ORIENTATION = 8,
  AXIS_VSCROLL = 9,
  AXIS_HSCROLL = 10,
  AXIS_Z = 11,
  AXIS_RX = 12,
  AXIS_RY = 13,
  AXIS_RZ = 14,
  AXIS_HAT_X = 15,
  AXIS_HAT_Y = 16,
  AXIS_LTRIGGER = 17,
  AXIS_RTRIGGER = 18,
  AXIS_THROTTLE = 19,
  AXIS_RUDDER = 20,
  AXIS_WHEEL = 21,
  AXIS_GAS = 22,
  AXIS_BRAKE = 23,
  AXIS_DISTANCE = 24,
  AXIS_TILT = 25,
  AXIS_GENERIC_1 = 32,
  AXIS_GENERIC_2 = 33,
  AXIS_GENERIC_3 = 34,
  AXIS_GENERIC_4 = 35,
  AXIS_GENERIC_5 = 36,
  AXIS_GENERIC_6 = 37,
  AXIS_GENERIC_7 = 38,
  AXIS_GENERIC_8 = 39,
  AXIS_GENERIC_9 = 40,
  AXIS_GENERIC_10 = 41,
  AXIS_GENERIC_11 = 42,
  AXIS_GENERIC_12 = 43,
  AXIS_GENERIC_13 = 44,
  AXIS_GENERIC_14 = 45,
  AXIS_GENERIC_15 = 46,
  AXIS_GENERIC_16 = 47,
  BUTTON_PRIMARY = 1,
  BUTTON_SECONDARY = 2,
  BUTTON_TERTIARY = 4,
  BUTTON_BACK = 8,
  BUTTON_FORWARD = 16,
  TOOL_TYPE_UNKNOWN = 0,
  TOOL_TYPE_FINGER = 1,
  TOOL_TYPE_STYLUS = 2,
  TOOL_TYPE_MOUSE = 3,
  TOOL_TYPE_ERASER = 4,
};


}  // namespace JNI_MotionEvent
// Step 3: Method stubs.
namespace JNI_MotionEvent {


static std::atomic<jmethodID> g_android_view_MotionEvent_finalize(nullptr);
static void Java_MotionEvent_finalize(JNIEnv* env, const base::android::JavaRef<jobject>& obj)
    __attribute__ ((unused));
static void Java_MotionEvent_finalize(JNIEnv* env, const base::android::JavaRef<jobject>& obj) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env));

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "finalize",
          "()V",
          &g_android_view_MotionEvent_finalize);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id);
}

static std::atomic<jmethodID>
    g_android_view_MotionEvent_obtainAVME_J_J_I_I_LAVMEPP_LAVMEPC_I_I_F_F_I_I_I_I(nullptr);
static base::android::ScopedJavaLocalRef<jobject>
    Java_MotionEvent_obtainAVME_J_J_I_I_LAVMEPP_LAVMEPC_I_I_F_F_I_I_I_I(JNIEnv* env, jlong p0,
    jlong p1,
    JniIntWrapper p2,
    JniIntWrapper p3,
    const base::android::JavaRef<jobjectArray>& p4,
    const base::android::JavaRef<jobjectArray>& p5,
    JniIntWrapper p6,
    JniIntWrapper p7,
    jfloat p8,
    jfloat p9,
    JniIntWrapper p10,
    JniIntWrapper p11,
    JniIntWrapper p12,
    JniIntWrapper p13) __attribute__ ((unused));
static base::android::ScopedJavaLocalRef<jobject>
    Java_MotionEvent_obtainAVME_J_J_I_I_LAVMEPP_LAVMEPC_I_I_F_F_I_I_I_I(JNIEnv* env, jlong p0,
    jlong p1,
    JniIntWrapper p2,
    JniIntWrapper p3,
    const base::android::JavaRef<jobjectArray>& p4,
    const base::android::JavaRef<jobjectArray>& p5,
    JniIntWrapper p6,
    JniIntWrapper p7,
    jfloat p8,
    jfloat p9,
    JniIntWrapper p10,
    JniIntWrapper p11,
    JniIntWrapper p12,
    JniIntWrapper p13) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, clazz,
      android_view_MotionEvent_clazz(env), NULL);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "obtain",
"(JJII[Landroid/view/MotionEvent$PointerProperties;[Landroid/view/MotionEvent$PointerCoords;IIFFIIII)Landroid/view/MotionEvent;",
          &g_android_view_MotionEvent_obtainAVME_J_J_I_I_LAVMEPP_LAVMEPC_I_I_F_F_I_I_I_I);

  jobject ret =
      env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, p0, p1, as_jint(p2), as_jint(p3), p4.obj(), p5.obj(),
              as_jint(p6), as_jint(p7), p8, p9, as_jint(p10), as_jint(p11), as_jint(p12),
              as_jint(p13));
  return base::android::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID>
    g_android_view_MotionEvent_obtainAVME_J_J_I_I_AI_LAVMEPC_I_F_F_I_I_I_I(nullptr);
static base::android::ScopedJavaLocalRef<jobject>
    Java_MotionEvent_obtainAVME_J_J_I_I_AI_LAVMEPC_I_F_F_I_I_I_I(JNIEnv* env, jlong p0,
    jlong p1,
    JniIntWrapper p2,
    JniIntWrapper p3,
    const base::android::JavaRef<jintArray>& p4,
    const base::android::JavaRef<jobjectArray>& p5,
    JniIntWrapper p6,
    jfloat p7,
    jfloat p8,
    JniIntWrapper p9,
    JniIntWrapper p10,
    JniIntWrapper p11,
    JniIntWrapper p12) __attribute__ ((unused));
static base::android::ScopedJavaLocalRef<jobject>
    Java_MotionEvent_obtainAVME_J_J_I_I_AI_LAVMEPC_I_F_F_I_I_I_I(JNIEnv* env, jlong p0,
    jlong p1,
    JniIntWrapper p2,
    JniIntWrapper p3,
    const base::android::JavaRef<jintArray>& p4,
    const base::android::JavaRef<jobjectArray>& p5,
    JniIntWrapper p6,
    jfloat p7,
    jfloat p8,
    JniIntWrapper p9,
    JniIntWrapper p10,
    JniIntWrapper p11,
    JniIntWrapper p12) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, clazz,
      android_view_MotionEvent_clazz(env), NULL);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "obtain",
          "(JJII[I[Landroid/view/MotionEvent$PointerCoords;IFFIIII)Landroid/view/MotionEvent;",
          &g_android_view_MotionEvent_obtainAVME_J_J_I_I_AI_LAVMEPC_I_F_F_I_I_I_I);

  jobject ret =
      env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, p0, p1, as_jint(p2), as_jint(p3), p4.obj(), p5.obj(),
              as_jint(p6), p7, p8, as_jint(p9), as_jint(p10), as_jint(p11), as_jint(p12));
  return base::android::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID>
    g_android_view_MotionEvent_obtainAVME_J_J_I_F_F_F_F_I_F_F_I_I(nullptr);
static base::android::ScopedJavaLocalRef<jobject>
    Java_MotionEvent_obtainAVME_J_J_I_F_F_F_F_I_F_F_I_I(JNIEnv* env, jlong p0,
    jlong p1,
    JniIntWrapper p2,
    jfloat p3,
    jfloat p4,
    jfloat p5,
    jfloat p6,
    JniIntWrapper p7,
    jfloat p8,
    jfloat p9,
    JniIntWrapper p10,
    JniIntWrapper p11) __attribute__ ((unused));
static base::android::ScopedJavaLocalRef<jobject>
    Java_MotionEvent_obtainAVME_J_J_I_F_F_F_F_I_F_F_I_I(JNIEnv* env, jlong p0,
    jlong p1,
    JniIntWrapper p2,
    jfloat p3,
    jfloat p4,
    jfloat p5,
    jfloat p6,
    JniIntWrapper p7,
    jfloat p8,
    jfloat p9,
    JniIntWrapper p10,
    JniIntWrapper p11) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, clazz,
      android_view_MotionEvent_clazz(env), NULL);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "obtain",
          "(JJIFFFFIFFII)Landroid/view/MotionEvent;",
          &g_android_view_MotionEvent_obtainAVME_J_J_I_F_F_F_F_I_F_F_I_I);

  jobject ret =
      env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, p0, p1, as_jint(p2), p3, p4, p5, p6, as_jint(p7), p8, p9,
              as_jint(p10), as_jint(p11));
  return base::android::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID>
    g_android_view_MotionEvent_obtainAVME_J_J_I_I_F_F_F_F_I_F_F_I_I(nullptr);
static base::android::ScopedJavaLocalRef<jobject>
    Java_MotionEvent_obtainAVME_J_J_I_I_F_F_F_F_I_F_F_I_I(JNIEnv* env, jlong p0,
    jlong p1,
    JniIntWrapper p2,
    JniIntWrapper p3,
    jfloat p4,
    jfloat p5,
    jfloat p6,
    jfloat p7,
    JniIntWrapper p8,
    jfloat p9,
    jfloat p10,
    JniIntWrapper p11,
    JniIntWrapper p12) __attribute__ ((unused));
static base::android::ScopedJavaLocalRef<jobject>
    Java_MotionEvent_obtainAVME_J_J_I_I_F_F_F_F_I_F_F_I_I(JNIEnv* env, jlong p0,
    jlong p1,
    JniIntWrapper p2,
    JniIntWrapper p3,
    jfloat p4,
    jfloat p5,
    jfloat p6,
    jfloat p7,
    JniIntWrapper p8,
    jfloat p9,
    jfloat p10,
    JniIntWrapper p11,
    JniIntWrapper p12) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, clazz,
      android_view_MotionEvent_clazz(env), NULL);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "obtain",
          "(JJIIFFFFIFFII)Landroid/view/MotionEvent;",
          &g_android_view_MotionEvent_obtainAVME_J_J_I_I_F_F_F_F_I_F_F_I_I);

  jobject ret =
      env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, p0, p1, as_jint(p2), as_jint(p3), p4, p5, p6, p7,
              as_jint(p8), p9, p10, as_jint(p11), as_jint(p12));
  return base::android::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_android_view_MotionEvent_obtainAVME_J_J_I_F_F_I(nullptr);
static base::android::ScopedJavaLocalRef<jobject> Java_MotionEvent_obtainAVME_J_J_I_F_F_I(JNIEnv*
    env, jlong p0,
    jlong p1,
    JniIntWrapper p2,
    jfloat p3,
    jfloat p4,
    JniIntWrapper p5) __attribute__ ((unused));
static base::android::ScopedJavaLocalRef<jobject> Java_MotionEvent_obtainAVME_J_J_I_F_F_I(JNIEnv*
    env, jlong p0,
    jlong p1,
    JniIntWrapper p2,
    jfloat p3,
    jfloat p4,
    JniIntWrapper p5) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, clazz,
      android_view_MotionEvent_clazz(env), NULL);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "obtain",
          "(JJIFFI)Landroid/view/MotionEvent;",
          &g_android_view_MotionEvent_obtainAVME_J_J_I_F_F_I);

  jobject ret =
      env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, p0, p1, as_jint(p2), p3, p4, as_jint(p5));
  return base::android::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_android_view_MotionEvent_obtainAVME_AVME(nullptr);
static base::android::ScopedJavaLocalRef<jobject> Java_MotionEvent_obtainAVME_AVME(JNIEnv* env,
    const base::android::JavaRef<jobject>& p0) __attribute__ ((unused));
static base::android::ScopedJavaLocalRef<jobject> Java_MotionEvent_obtainAVME_AVME(JNIEnv* env,
    const base::android::JavaRef<jobject>& p0) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, clazz,
      android_view_MotionEvent_clazz(env), NULL);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "obtain",
          "(Landroid/view/MotionEvent;)Landroid/view/MotionEvent;",
          &g_android_view_MotionEvent_obtainAVME_AVME);

  jobject ret =
      env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, p0.obj());
  return base::android::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_android_view_MotionEvent_obtainNoHistory(nullptr);
static base::android::ScopedJavaLocalRef<jobject> Java_MotionEvent_obtainNoHistory(JNIEnv* env,
    const base::android::JavaRef<jobject>& p0) __attribute__ ((unused));
static base::android::ScopedJavaLocalRef<jobject> Java_MotionEvent_obtainNoHistory(JNIEnv* env,
    const base::android::JavaRef<jobject>& p0) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, clazz,
      android_view_MotionEvent_clazz(env), NULL);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "obtainNoHistory",
          "(Landroid/view/MotionEvent;)Landroid/view/MotionEvent;",
          &g_android_view_MotionEvent_obtainNoHistory);

  jobject ret =
      env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, p0.obj());
  return base::android::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_android_view_MotionEvent_recycle(nullptr);
static void Java_MotionEvent_recycle(JNIEnv* env, const base::android::JavaRef<jobject>& obj)
    __attribute__ ((unused));
static void Java_MotionEvent_recycle(JNIEnv* env, const base::android::JavaRef<jobject>& obj) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env));

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "recycle",
          "()V",
          &g_android_view_MotionEvent_recycle);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id);
}

static std::atomic<jmethodID> g_android_view_MotionEvent_getDeviceId(nullptr);
static jint Java_MotionEvent_getDeviceId(JNIEnv* env, const base::android::JavaRef<jobject>& obj)
    __attribute__ ((unused));
static jint Java_MotionEvent_getDeviceId(JNIEnv* env, const base::android::JavaRef<jobject>& obj) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getDeviceId",
          "()I",
          &g_android_view_MotionEvent_getDeviceId);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_android_view_MotionEvent_getSource(nullptr);
static jint Java_MotionEvent_getSource(JNIEnv* env, const base::android::JavaRef<jobject>& obj)
    __attribute__ ((unused));
static jint Java_MotionEvent_getSource(JNIEnv* env, const base::android::JavaRef<jobject>& obj) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getSource",
          "()I",
          &g_android_view_MotionEvent_getSource);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_android_view_MotionEvent_setSource(nullptr);
static void Java_MotionEvent_setSource(JNIEnv* env, const base::android::JavaRef<jobject>& obj,
    JniIntWrapper p0) __attribute__ ((unused));
static void Java_MotionEvent_setSource(JNIEnv* env, const base::android::JavaRef<jobject>& obj,
    JniIntWrapper p0) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env));

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "setSource",
          "(I)V",
          &g_android_view_MotionEvent_setSource);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id, as_jint(p0));
}

static std::atomic<jmethodID> g_android_view_MotionEvent_getAction(nullptr);
static jint Java_MotionEvent_getAction(JNIEnv* env, const base::android::JavaRef<jobject>& obj)
    __attribute__ ((unused));
static jint Java_MotionEvent_getAction(JNIEnv* env, const base::android::JavaRef<jobject>& obj) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getAction",
          "()I",
          &g_android_view_MotionEvent_getAction);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_android_view_MotionEvent_getActionMasked(nullptr);
static jint Java_MotionEvent_getActionMasked(JNIEnv* env, const base::android::JavaRef<jobject>&
    obj) __attribute__ ((unused));
static jint Java_MotionEvent_getActionMasked(JNIEnv* env, const base::android::JavaRef<jobject>&
    obj) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getActionMasked",
          "()I",
          &g_android_view_MotionEvent_getActionMasked);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_android_view_MotionEvent_getActionIndex(nullptr);
static jint Java_MotionEvent_getActionIndex(JNIEnv* env, const base::android::JavaRef<jobject>& obj)
    __attribute__ ((unused));
static jint Java_MotionEvent_getActionIndex(JNIEnv* env, const base::android::JavaRef<jobject>& obj)
    {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getActionIndex",
          "()I",
          &g_android_view_MotionEvent_getActionIndex);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_android_view_MotionEvent_getFlags(nullptr);
static jint Java_MotionEvent_getFlags(JNIEnv* env, const base::android::JavaRef<jobject>& obj)
    __attribute__ ((unused));
static jint Java_MotionEvent_getFlags(JNIEnv* env, const base::android::JavaRef<jobject>& obj) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getFlags",
          "()I",
          &g_android_view_MotionEvent_getFlags);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_android_view_MotionEvent_getDownTime(nullptr);
static jlong Java_MotionEvent_getDownTime(JNIEnv* env, const base::android::JavaRef<jobject>& obj)
    __attribute__ ((unused));
static jlong Java_MotionEvent_getDownTime(JNIEnv* env, const base::android::JavaRef<jobject>& obj) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getDownTime",
          "()J",
          &g_android_view_MotionEvent_getDownTime);

  jlong ret =
      env->CallLongMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_android_view_MotionEvent_getEventTime(nullptr);
static jlong Java_MotionEvent_getEventTime(JNIEnv* env, const base::android::JavaRef<jobject>& obj)
    __attribute__ ((unused));
static jlong Java_MotionEvent_getEventTime(JNIEnv* env, const base::android::JavaRef<jobject>& obj)
    {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getEventTime",
          "()J",
          &g_android_view_MotionEvent_getEventTime);

  jlong ret =
      env->CallLongMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_android_view_MotionEvent_getXF(nullptr);
static jfloat Java_MotionEvent_getXF(JNIEnv* env, const base::android::JavaRef<jobject>& obj)
    __attribute__ ((unused));
static jfloat Java_MotionEvent_getXF(JNIEnv* env, const base::android::JavaRef<jobject>& obj) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getX",
          "()F",
          &g_android_view_MotionEvent_getXF);

  jfloat ret =
      env->CallFloatMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_android_view_MotionEvent_getYF(nullptr);
static jfloat Java_MotionEvent_getYF(JNIEnv* env, const base::android::JavaRef<jobject>& obj)
    __attribute__ ((unused));
static jfloat Java_MotionEvent_getYF(JNIEnv* env, const base::android::JavaRef<jobject>& obj) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getY",
          "()F",
          &g_android_view_MotionEvent_getYF);

  jfloat ret =
      env->CallFloatMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_android_view_MotionEvent_getPressureF(nullptr);
static jfloat Java_MotionEvent_getPressureF(JNIEnv* env, const base::android::JavaRef<jobject>& obj)
    __attribute__ ((unused));
static jfloat Java_MotionEvent_getPressureF(JNIEnv* env, const base::android::JavaRef<jobject>& obj)
    {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getPressure",
          "()F",
          &g_android_view_MotionEvent_getPressureF);

  jfloat ret =
      env->CallFloatMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_android_view_MotionEvent_getSizeF(nullptr);
static jfloat Java_MotionEvent_getSizeF(JNIEnv* env, const base::android::JavaRef<jobject>& obj)
    __attribute__ ((unused));
static jfloat Java_MotionEvent_getSizeF(JNIEnv* env, const base::android::JavaRef<jobject>& obj) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getSize",
          "()F",
          &g_android_view_MotionEvent_getSizeF);

  jfloat ret =
      env->CallFloatMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_android_view_MotionEvent_getTouchMajorF(nullptr);
static jfloat Java_MotionEvent_getTouchMajorF(JNIEnv* env, const base::android::JavaRef<jobject>&
    obj) __attribute__ ((unused));
static jfloat Java_MotionEvent_getTouchMajorF(JNIEnv* env, const base::android::JavaRef<jobject>&
    obj) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getTouchMajor",
          "()F",
          &g_android_view_MotionEvent_getTouchMajorF);

  jfloat ret =
      env->CallFloatMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_android_view_MotionEvent_getTouchMinorF(nullptr);
static jfloat Java_MotionEvent_getTouchMinorF(JNIEnv* env, const base::android::JavaRef<jobject>&
    obj) __attribute__ ((unused));
static jfloat Java_MotionEvent_getTouchMinorF(JNIEnv* env, const base::android::JavaRef<jobject>&
    obj) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getTouchMinor",
          "()F",
          &g_android_view_MotionEvent_getTouchMinorF);

  jfloat ret =
      env->CallFloatMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_android_view_MotionEvent_getToolMajorF(nullptr);
static jfloat Java_MotionEvent_getToolMajorF(JNIEnv* env, const base::android::JavaRef<jobject>&
    obj) __attribute__ ((unused));
static jfloat Java_MotionEvent_getToolMajorF(JNIEnv* env, const base::android::JavaRef<jobject>&
    obj) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getToolMajor",
          "()F",
          &g_android_view_MotionEvent_getToolMajorF);

  jfloat ret =
      env->CallFloatMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_android_view_MotionEvent_getToolMinorF(nullptr);
static jfloat Java_MotionEvent_getToolMinorF(JNIEnv* env, const base::android::JavaRef<jobject>&
    obj) __attribute__ ((unused));
static jfloat Java_MotionEvent_getToolMinorF(JNIEnv* env, const base::android::JavaRef<jobject>&
    obj) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getToolMinor",
          "()F",
          &g_android_view_MotionEvent_getToolMinorF);

  jfloat ret =
      env->CallFloatMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_android_view_MotionEvent_getOrientationF(nullptr);
static jfloat Java_MotionEvent_getOrientationF(JNIEnv* env, const base::android::JavaRef<jobject>&
    obj) __attribute__ ((unused));
static jfloat Java_MotionEvent_getOrientationF(JNIEnv* env, const base::android::JavaRef<jobject>&
    obj) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getOrientation",
          "()F",
          &g_android_view_MotionEvent_getOrientationF);

  jfloat ret =
      env->CallFloatMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_android_view_MotionEvent_getAxisValueF_I(nullptr);
static jfloat Java_MotionEvent_getAxisValueF_I(JNIEnv* env, const base::android::JavaRef<jobject>&
    obj, JniIntWrapper p0) __attribute__ ((unused));
static jfloat Java_MotionEvent_getAxisValueF_I(JNIEnv* env, const base::android::JavaRef<jobject>&
    obj, JniIntWrapper p0) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getAxisValue",
          "(I)F",
          &g_android_view_MotionEvent_getAxisValueF_I);

  jfloat ret =
      env->CallFloatMethod(obj.obj(),
          call_context.base.method_id, as_jint(p0));
  return ret;
}

static std::atomic<jmethodID> g_android_view_MotionEvent_getPointerCount(nullptr);
static jint Java_MotionEvent_getPointerCount(JNIEnv* env, const base::android::JavaRef<jobject>&
    obj) __attribute__ ((unused));
static jint Java_MotionEvent_getPointerCount(JNIEnv* env, const base::android::JavaRef<jobject>&
    obj) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getPointerCount",
          "()I",
          &g_android_view_MotionEvent_getPointerCount);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_android_view_MotionEvent_getPointerId(nullptr);
static jint Java_MotionEvent_getPointerId(JNIEnv* env, const base::android::JavaRef<jobject>& obj,
    JniIntWrapper p0) __attribute__ ((unused));
static jint Java_MotionEvent_getPointerId(JNIEnv* env, const base::android::JavaRef<jobject>& obj,
    JniIntWrapper p0) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getPointerId",
          "(I)I",
          &g_android_view_MotionEvent_getPointerId);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id, as_jint(p0));
  return ret;
}

static std::atomic<jmethodID> g_android_view_MotionEvent_getToolType(nullptr);
static jint Java_MotionEvent_getToolType(JNIEnv* env, const base::android::JavaRef<jobject>& obj,
    JniIntWrapper p0) __attribute__ ((unused));
static jint Java_MotionEvent_getToolType(JNIEnv* env, const base::android::JavaRef<jobject>& obj,
    JniIntWrapper p0) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getToolType",
          "(I)I",
          &g_android_view_MotionEvent_getToolType);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id, as_jint(p0));
  return ret;
}

static std::atomic<jmethodID> g_android_view_MotionEvent_findPointerIndex(nullptr);
static jint Java_MotionEvent_findPointerIndex(JNIEnv* env, const base::android::JavaRef<jobject>&
    obj, JniIntWrapper p0) __attribute__ ((unused));
static jint Java_MotionEvent_findPointerIndex(JNIEnv* env, const base::android::JavaRef<jobject>&
    obj, JniIntWrapper p0) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "findPointerIndex",
          "(I)I",
          &g_android_view_MotionEvent_findPointerIndex);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id, as_jint(p0));
  return ret;
}

static std::atomic<jmethodID> g_android_view_MotionEvent_getXF_I(nullptr);
static jfloat Java_MotionEvent_getXF_I(JNIEnv* env, const base::android::JavaRef<jobject>& obj,
    JniIntWrapper p0) __attribute__ ((unused));
static jfloat Java_MotionEvent_getXF_I(JNIEnv* env, const base::android::JavaRef<jobject>& obj,
    JniIntWrapper p0) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getX",
          "(I)F",
          &g_android_view_MotionEvent_getXF_I);

  jfloat ret =
      env->CallFloatMethod(obj.obj(),
          call_context.base.method_id, as_jint(p0));
  return ret;
}

static std::atomic<jmethodID> g_android_view_MotionEvent_getYF_I(nullptr);
static jfloat Java_MotionEvent_getYF_I(JNIEnv* env, const base::android::JavaRef<jobject>& obj,
    JniIntWrapper p0) __attribute__ ((unused));
static jfloat Java_MotionEvent_getYF_I(JNIEnv* env, const base::android::JavaRef<jobject>& obj,
    JniIntWrapper p0) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getY",
          "(I)F",
          &g_android_view_MotionEvent_getYF_I);

  jfloat ret =
      env->CallFloatMethod(obj.obj(),
          call_context.base.method_id, as_jint(p0));
  return ret;
}

static std::atomic<jmethodID> g_android_view_MotionEvent_getPressureF_I(nullptr);
static jfloat Java_MotionEvent_getPressureF_I(JNIEnv* env, const base::android::JavaRef<jobject>&
    obj, JniIntWrapper p0) __attribute__ ((unused));
static jfloat Java_MotionEvent_getPressureF_I(JNIEnv* env, const base::android::JavaRef<jobject>&
    obj, JniIntWrapper p0) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getPressure",
          "(I)F",
          &g_android_view_MotionEvent_getPressureF_I);

  jfloat ret =
      env->CallFloatMethod(obj.obj(),
          call_context.base.method_id, as_jint(p0));
  return ret;
}

static std::atomic<jmethodID> g_android_view_MotionEvent_getSizeF_I(nullptr);
static jfloat Java_MotionEvent_getSizeF_I(JNIEnv* env, const base::android::JavaRef<jobject>& obj,
    JniIntWrapper p0) __attribute__ ((unused));
static jfloat Java_MotionEvent_getSizeF_I(JNIEnv* env, const base::android::JavaRef<jobject>& obj,
    JniIntWrapper p0) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getSize",
          "(I)F",
          &g_android_view_MotionEvent_getSizeF_I);

  jfloat ret =
      env->CallFloatMethod(obj.obj(),
          call_context.base.method_id, as_jint(p0));
  return ret;
}

static std::atomic<jmethodID> g_android_view_MotionEvent_getTouchMajorF_I(nullptr);
static jfloat Java_MotionEvent_getTouchMajorF_I(JNIEnv* env, const base::android::JavaRef<jobject>&
    obj, JniIntWrapper p0) __attribute__ ((unused));
static jfloat Java_MotionEvent_getTouchMajorF_I(JNIEnv* env, const base::android::JavaRef<jobject>&
    obj, JniIntWrapper p0) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getTouchMajor",
          "(I)F",
          &g_android_view_MotionEvent_getTouchMajorF_I);

  jfloat ret =
      env->CallFloatMethod(obj.obj(),
          call_context.base.method_id, as_jint(p0));
  return ret;
}

static std::atomic<jmethodID> g_android_view_MotionEvent_getTouchMinorF_I(nullptr);
static jfloat Java_MotionEvent_getTouchMinorF_I(JNIEnv* env, const base::android::JavaRef<jobject>&
    obj, JniIntWrapper p0) __attribute__ ((unused));
static jfloat Java_MotionEvent_getTouchMinorF_I(JNIEnv* env, const base::android::JavaRef<jobject>&
    obj, JniIntWrapper p0) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getTouchMinor",
          "(I)F",
          &g_android_view_MotionEvent_getTouchMinorF_I);

  jfloat ret =
      env->CallFloatMethod(obj.obj(),
          call_context.base.method_id, as_jint(p0));
  return ret;
}

static std::atomic<jmethodID> g_android_view_MotionEvent_getToolMajorF_I(nullptr);
static jfloat Java_MotionEvent_getToolMajorF_I(JNIEnv* env, const base::android::JavaRef<jobject>&
    obj, JniIntWrapper p0) __attribute__ ((unused));
static jfloat Java_MotionEvent_getToolMajorF_I(JNIEnv* env, const base::android::JavaRef<jobject>&
    obj, JniIntWrapper p0) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getToolMajor",
          "(I)F",
          &g_android_view_MotionEvent_getToolMajorF_I);

  jfloat ret =
      env->CallFloatMethod(obj.obj(),
          call_context.base.method_id, as_jint(p0));
  return ret;
}

static std::atomic<jmethodID> g_android_view_MotionEvent_getToolMinorF_I(nullptr);
static jfloat Java_MotionEvent_getToolMinorF_I(JNIEnv* env, const base::android::JavaRef<jobject>&
    obj, JniIntWrapper p0) __attribute__ ((unused));
static jfloat Java_MotionEvent_getToolMinorF_I(JNIEnv* env, const base::android::JavaRef<jobject>&
    obj, JniIntWrapper p0) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getToolMinor",
          "(I)F",
          &g_android_view_MotionEvent_getToolMinorF_I);

  jfloat ret =
      env->CallFloatMethod(obj.obj(),
          call_context.base.method_id, as_jint(p0));
  return ret;
}

static std::atomic<jmethodID> g_android_view_MotionEvent_getOrientationF_I(nullptr);
static jfloat Java_MotionEvent_getOrientationF_I(JNIEnv* env, const base::android::JavaRef<jobject>&
    obj, JniIntWrapper p0) __attribute__ ((unused));
static jfloat Java_MotionEvent_getOrientationF_I(JNIEnv* env, const base::android::JavaRef<jobject>&
    obj, JniIntWrapper p0) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getOrientation",
          "(I)F",
          &g_android_view_MotionEvent_getOrientationF_I);

  jfloat ret =
      env->CallFloatMethod(obj.obj(),
          call_context.base.method_id, as_jint(p0));
  return ret;
}

static std::atomic<jmethodID> g_android_view_MotionEvent_getAxisValueF_I_I(nullptr);
static jfloat Java_MotionEvent_getAxisValueF_I_I(JNIEnv* env, const base::android::JavaRef<jobject>&
    obj, JniIntWrapper p0,
    JniIntWrapper p1) __attribute__ ((unused));
static jfloat Java_MotionEvent_getAxisValueF_I_I(JNIEnv* env, const base::android::JavaRef<jobject>&
    obj, JniIntWrapper p0,
    JniIntWrapper p1) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getAxisValue",
          "(II)F",
          &g_android_view_MotionEvent_getAxisValueF_I_I);

  jfloat ret =
      env->CallFloatMethod(obj.obj(),
          call_context.base.method_id, as_jint(p0), as_jint(p1));
  return ret;
}

static std::atomic<jmethodID> g_android_view_MotionEvent_getPointerCoords(nullptr);
static void Java_MotionEvent_getPointerCoords(JNIEnv* env, const base::android::JavaRef<jobject>&
    obj, JniIntWrapper p0,
    const base::android::JavaRef<jobject>& p1) __attribute__ ((unused));
static void Java_MotionEvent_getPointerCoords(JNIEnv* env, const base::android::JavaRef<jobject>&
    obj, JniIntWrapper p0,
    const base::android::JavaRef<jobject>& p1) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env));

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getPointerCoords",
          "(ILandroid/view/MotionEvent$PointerCoords;)V",
          &g_android_view_MotionEvent_getPointerCoords);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id, as_jint(p0), p1.obj());
}

static std::atomic<jmethodID> g_android_view_MotionEvent_getPointerProperties(nullptr);
static void Java_MotionEvent_getPointerProperties(JNIEnv* env, const
    base::android::JavaRef<jobject>& obj, JniIntWrapper p0,
    const base::android::JavaRef<jobject>& p1) __attribute__ ((unused));
static void Java_MotionEvent_getPointerProperties(JNIEnv* env, const
    base::android::JavaRef<jobject>& obj, JniIntWrapper p0,
    const base::android::JavaRef<jobject>& p1) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env));

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getPointerProperties",
          "(ILandroid/view/MotionEvent$PointerProperties;)V",
          &g_android_view_MotionEvent_getPointerProperties);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id, as_jint(p0), p1.obj());
}

static std::atomic<jmethodID> g_android_view_MotionEvent_getMetaState(nullptr);
static jint Java_MotionEvent_getMetaState(JNIEnv* env, const base::android::JavaRef<jobject>& obj)
    __attribute__ ((unused));
static jint Java_MotionEvent_getMetaState(JNIEnv* env, const base::android::JavaRef<jobject>& obj) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getMetaState",
          "()I",
          &g_android_view_MotionEvent_getMetaState);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_android_view_MotionEvent_getButtonState(nullptr);
static jint Java_MotionEvent_getButtonState(JNIEnv* env, const base::android::JavaRef<jobject>& obj)
    __attribute__ ((unused));
static jint Java_MotionEvent_getButtonState(JNIEnv* env, const base::android::JavaRef<jobject>& obj)
    {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getButtonState",
          "()I",
          &g_android_view_MotionEvent_getButtonState);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_android_view_MotionEvent_getRawX(nullptr);
static jfloat Java_MotionEvent_getRawX(JNIEnv* env, const base::android::JavaRef<jobject>& obj)
    __attribute__ ((unused));
static jfloat Java_MotionEvent_getRawX(JNIEnv* env, const base::android::JavaRef<jobject>& obj) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getRawX",
          "()F",
          &g_android_view_MotionEvent_getRawX);

  jfloat ret =
      env->CallFloatMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_android_view_MotionEvent_getRawY(nullptr);
static jfloat Java_MotionEvent_getRawY(JNIEnv* env, const base::android::JavaRef<jobject>& obj)
    __attribute__ ((unused));
static jfloat Java_MotionEvent_getRawY(JNIEnv* env, const base::android::JavaRef<jobject>& obj) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getRawY",
          "()F",
          &g_android_view_MotionEvent_getRawY);

  jfloat ret =
      env->CallFloatMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_android_view_MotionEvent_getXPrecision(nullptr);
static jfloat Java_MotionEvent_getXPrecision(JNIEnv* env, const base::android::JavaRef<jobject>&
    obj) __attribute__ ((unused));
static jfloat Java_MotionEvent_getXPrecision(JNIEnv* env, const base::android::JavaRef<jobject>&
    obj) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getXPrecision",
          "()F",
          &g_android_view_MotionEvent_getXPrecision);

  jfloat ret =
      env->CallFloatMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_android_view_MotionEvent_getYPrecision(nullptr);
static jfloat Java_MotionEvent_getYPrecision(JNIEnv* env, const base::android::JavaRef<jobject>&
    obj) __attribute__ ((unused));
static jfloat Java_MotionEvent_getYPrecision(JNIEnv* env, const base::android::JavaRef<jobject>&
    obj) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getYPrecision",
          "()F",
          &g_android_view_MotionEvent_getYPrecision);

  jfloat ret =
      env->CallFloatMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_android_view_MotionEvent_getHistorySize(nullptr);
static jint Java_MotionEvent_getHistorySize(JNIEnv* env, const base::android::JavaRef<jobject>& obj)
    __attribute__ ((unused));
static jint Java_MotionEvent_getHistorySize(JNIEnv* env, const base::android::JavaRef<jobject>& obj)
    {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getHistorySize",
          "()I",
          &g_android_view_MotionEvent_getHistorySize);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_android_view_MotionEvent_getHistoricalEventTime(nullptr);
static jlong Java_MotionEvent_getHistoricalEventTime(JNIEnv* env, const
    base::android::JavaRef<jobject>& obj, JniIntWrapper p0) __attribute__ ((unused));
static jlong Java_MotionEvent_getHistoricalEventTime(JNIEnv* env, const
    base::android::JavaRef<jobject>& obj, JniIntWrapper p0) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getHistoricalEventTime",
          "(I)J",
          &g_android_view_MotionEvent_getHistoricalEventTime);

  jlong ret =
      env->CallLongMethod(obj.obj(),
          call_context.base.method_id, as_jint(p0));
  return ret;
}

static std::atomic<jmethodID> g_android_view_MotionEvent_getHistoricalXF_I(nullptr);
static jfloat Java_MotionEvent_getHistoricalXF_I(JNIEnv* env, const base::android::JavaRef<jobject>&
    obj, JniIntWrapper p0) __attribute__ ((unused));
static jfloat Java_MotionEvent_getHistoricalXF_I(JNIEnv* env, const base::android::JavaRef<jobject>&
    obj, JniIntWrapper p0) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getHistoricalX",
          "(I)F",
          &g_android_view_MotionEvent_getHistoricalXF_I);

  jfloat ret =
      env->CallFloatMethod(obj.obj(),
          call_context.base.method_id, as_jint(p0));
  return ret;
}

static std::atomic<jmethodID> g_android_view_MotionEvent_getHistoricalYF_I(nullptr);
static jfloat Java_MotionEvent_getHistoricalYF_I(JNIEnv* env, const base::android::JavaRef<jobject>&
    obj, JniIntWrapper p0) __attribute__ ((unused));
static jfloat Java_MotionEvent_getHistoricalYF_I(JNIEnv* env, const base::android::JavaRef<jobject>&
    obj, JniIntWrapper p0) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getHistoricalY",
          "(I)F",
          &g_android_view_MotionEvent_getHistoricalYF_I);

  jfloat ret =
      env->CallFloatMethod(obj.obj(),
          call_context.base.method_id, as_jint(p0));
  return ret;
}

static std::atomic<jmethodID> g_android_view_MotionEvent_getHistoricalPressureF_I(nullptr);
static jfloat Java_MotionEvent_getHistoricalPressureF_I(JNIEnv* env, const
    base::android::JavaRef<jobject>& obj, JniIntWrapper p0) __attribute__ ((unused));
static jfloat Java_MotionEvent_getHistoricalPressureF_I(JNIEnv* env, const
    base::android::JavaRef<jobject>& obj, JniIntWrapper p0) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getHistoricalPressure",
          "(I)F",
          &g_android_view_MotionEvent_getHistoricalPressureF_I);

  jfloat ret =
      env->CallFloatMethod(obj.obj(),
          call_context.base.method_id, as_jint(p0));
  return ret;
}

static std::atomic<jmethodID> g_android_view_MotionEvent_getHistoricalSizeF_I(nullptr);
static jfloat Java_MotionEvent_getHistoricalSizeF_I(JNIEnv* env, const
    base::android::JavaRef<jobject>& obj, JniIntWrapper p0) __attribute__ ((unused));
static jfloat Java_MotionEvent_getHistoricalSizeF_I(JNIEnv* env, const
    base::android::JavaRef<jobject>& obj, JniIntWrapper p0) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getHistoricalSize",
          "(I)F",
          &g_android_view_MotionEvent_getHistoricalSizeF_I);

  jfloat ret =
      env->CallFloatMethod(obj.obj(),
          call_context.base.method_id, as_jint(p0));
  return ret;
}

static std::atomic<jmethodID> g_android_view_MotionEvent_getHistoricalTouchMajorF_I(nullptr);
static jfloat Java_MotionEvent_getHistoricalTouchMajorF_I(JNIEnv* env, const
    base::android::JavaRef<jobject>& obj, JniIntWrapper p0) __attribute__ ((unused));
static jfloat Java_MotionEvent_getHistoricalTouchMajorF_I(JNIEnv* env, const
    base::android::JavaRef<jobject>& obj, JniIntWrapper p0) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getHistoricalTouchMajor",
          "(I)F",
          &g_android_view_MotionEvent_getHistoricalTouchMajorF_I);

  jfloat ret =
      env->CallFloatMethod(obj.obj(),
          call_context.base.method_id, as_jint(p0));
  return ret;
}

static std::atomic<jmethodID> g_android_view_MotionEvent_getHistoricalTouchMinorF_I(nullptr);
static jfloat Java_MotionEvent_getHistoricalTouchMinorF_I(JNIEnv* env, const
    base::android::JavaRef<jobject>& obj, JniIntWrapper p0) __attribute__ ((unused));
static jfloat Java_MotionEvent_getHistoricalTouchMinorF_I(JNIEnv* env, const
    base::android::JavaRef<jobject>& obj, JniIntWrapper p0) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getHistoricalTouchMinor",
          "(I)F",
          &g_android_view_MotionEvent_getHistoricalTouchMinorF_I);

  jfloat ret =
      env->CallFloatMethod(obj.obj(),
          call_context.base.method_id, as_jint(p0));
  return ret;
}

static std::atomic<jmethodID> g_android_view_MotionEvent_getHistoricalToolMajorF_I(nullptr);
static jfloat Java_MotionEvent_getHistoricalToolMajorF_I(JNIEnv* env, const
    base::android::JavaRef<jobject>& obj, JniIntWrapper p0) __attribute__ ((unused));
static jfloat Java_MotionEvent_getHistoricalToolMajorF_I(JNIEnv* env, const
    base::android::JavaRef<jobject>& obj, JniIntWrapper p0) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getHistoricalToolMajor",
          "(I)F",
          &g_android_view_MotionEvent_getHistoricalToolMajorF_I);

  jfloat ret =
      env->CallFloatMethod(obj.obj(),
          call_context.base.method_id, as_jint(p0));
  return ret;
}

static std::atomic<jmethodID> g_android_view_MotionEvent_getHistoricalToolMinorF_I(nullptr);
static jfloat Java_MotionEvent_getHistoricalToolMinorF_I(JNIEnv* env, const
    base::android::JavaRef<jobject>& obj, JniIntWrapper p0) __attribute__ ((unused));
static jfloat Java_MotionEvent_getHistoricalToolMinorF_I(JNIEnv* env, const
    base::android::JavaRef<jobject>& obj, JniIntWrapper p0) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getHistoricalToolMinor",
          "(I)F",
          &g_android_view_MotionEvent_getHistoricalToolMinorF_I);

  jfloat ret =
      env->CallFloatMethod(obj.obj(),
          call_context.base.method_id, as_jint(p0));
  return ret;
}

static std::atomic<jmethodID> g_android_view_MotionEvent_getHistoricalOrientationF_I(nullptr);
static jfloat Java_MotionEvent_getHistoricalOrientationF_I(JNIEnv* env, const
    base::android::JavaRef<jobject>& obj, JniIntWrapper p0) __attribute__ ((unused));
static jfloat Java_MotionEvent_getHistoricalOrientationF_I(JNIEnv* env, const
    base::android::JavaRef<jobject>& obj, JniIntWrapper p0) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getHistoricalOrientation",
          "(I)F",
          &g_android_view_MotionEvent_getHistoricalOrientationF_I);

  jfloat ret =
      env->CallFloatMethod(obj.obj(),
          call_context.base.method_id, as_jint(p0));
  return ret;
}

static std::atomic<jmethodID> g_android_view_MotionEvent_getHistoricalAxisValueF_I_I(nullptr);
static jfloat Java_MotionEvent_getHistoricalAxisValueF_I_I(JNIEnv* env, const
    base::android::JavaRef<jobject>& obj, JniIntWrapper p0,
    JniIntWrapper p1) __attribute__ ((unused));
static jfloat Java_MotionEvent_getHistoricalAxisValueF_I_I(JNIEnv* env, const
    base::android::JavaRef<jobject>& obj, JniIntWrapper p0,
    JniIntWrapper p1) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getHistoricalAxisValue",
          "(II)F",
          &g_android_view_MotionEvent_getHistoricalAxisValueF_I_I);

  jfloat ret =
      env->CallFloatMethod(obj.obj(),
          call_context.base.method_id, as_jint(p0), as_jint(p1));
  return ret;
}

static std::atomic<jmethodID> g_android_view_MotionEvent_getHistoricalXF_I_I(nullptr);
static jfloat Java_MotionEvent_getHistoricalXF_I_I(JNIEnv* env, const
    base::android::JavaRef<jobject>& obj, JniIntWrapper p0,
    JniIntWrapper p1) __attribute__ ((unused));
static jfloat Java_MotionEvent_getHistoricalXF_I_I(JNIEnv* env, const
    base::android::JavaRef<jobject>& obj, JniIntWrapper p0,
    JniIntWrapper p1) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getHistoricalX",
          "(II)F",
          &g_android_view_MotionEvent_getHistoricalXF_I_I);

  jfloat ret =
      env->CallFloatMethod(obj.obj(),
          call_context.base.method_id, as_jint(p0), as_jint(p1));
  return ret;
}

static std::atomic<jmethodID> g_android_view_MotionEvent_getHistoricalYF_I_I(nullptr);
static jfloat Java_MotionEvent_getHistoricalYF_I_I(JNIEnv* env, const
    base::android::JavaRef<jobject>& obj, JniIntWrapper p0,
    JniIntWrapper p1) __attribute__ ((unused));
static jfloat Java_MotionEvent_getHistoricalYF_I_I(JNIEnv* env, const
    base::android::JavaRef<jobject>& obj, JniIntWrapper p0,
    JniIntWrapper p1) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getHistoricalY",
          "(II)F",
          &g_android_view_MotionEvent_getHistoricalYF_I_I);

  jfloat ret =
      env->CallFloatMethod(obj.obj(),
          call_context.base.method_id, as_jint(p0), as_jint(p1));
  return ret;
}

static std::atomic<jmethodID> g_android_view_MotionEvent_getHistoricalPressureF_I_I(nullptr);
static jfloat Java_MotionEvent_getHistoricalPressureF_I_I(JNIEnv* env, const
    base::android::JavaRef<jobject>& obj, JniIntWrapper p0,
    JniIntWrapper p1) __attribute__ ((unused));
static jfloat Java_MotionEvent_getHistoricalPressureF_I_I(JNIEnv* env, const
    base::android::JavaRef<jobject>& obj, JniIntWrapper p0,
    JniIntWrapper p1) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getHistoricalPressure",
          "(II)F",
          &g_android_view_MotionEvent_getHistoricalPressureF_I_I);

  jfloat ret =
      env->CallFloatMethod(obj.obj(),
          call_context.base.method_id, as_jint(p0), as_jint(p1));
  return ret;
}

static std::atomic<jmethodID> g_android_view_MotionEvent_getHistoricalSizeF_I_I(nullptr);
static jfloat Java_MotionEvent_getHistoricalSizeF_I_I(JNIEnv* env, const
    base::android::JavaRef<jobject>& obj, JniIntWrapper p0,
    JniIntWrapper p1) __attribute__ ((unused));
static jfloat Java_MotionEvent_getHistoricalSizeF_I_I(JNIEnv* env, const
    base::android::JavaRef<jobject>& obj, JniIntWrapper p0,
    JniIntWrapper p1) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getHistoricalSize",
          "(II)F",
          &g_android_view_MotionEvent_getHistoricalSizeF_I_I);

  jfloat ret =
      env->CallFloatMethod(obj.obj(),
          call_context.base.method_id, as_jint(p0), as_jint(p1));
  return ret;
}

static std::atomic<jmethodID> g_android_view_MotionEvent_getHistoricalTouchMajorF_I_I(nullptr);
static jfloat Java_MotionEvent_getHistoricalTouchMajorF_I_I(JNIEnv* env, const
    base::android::JavaRef<jobject>& obj, JniIntWrapper p0,
    JniIntWrapper p1) __attribute__ ((unused));
static jfloat Java_MotionEvent_getHistoricalTouchMajorF_I_I(JNIEnv* env, const
    base::android::JavaRef<jobject>& obj, JniIntWrapper p0,
    JniIntWrapper p1) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getHistoricalTouchMajor",
          "(II)F",
          &g_android_view_MotionEvent_getHistoricalTouchMajorF_I_I);

  jfloat ret =
      env->CallFloatMethod(obj.obj(),
          call_context.base.method_id, as_jint(p0), as_jint(p1));
  return ret;
}

static std::atomic<jmethodID> g_android_view_MotionEvent_getHistoricalTouchMinorF_I_I(nullptr);
static jfloat Java_MotionEvent_getHistoricalTouchMinorF_I_I(JNIEnv* env, const
    base::android::JavaRef<jobject>& obj, JniIntWrapper p0,
    JniIntWrapper p1) __attribute__ ((unused));
static jfloat Java_MotionEvent_getHistoricalTouchMinorF_I_I(JNIEnv* env, const
    base::android::JavaRef<jobject>& obj, JniIntWrapper p0,
    JniIntWrapper p1) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getHistoricalTouchMinor",
          "(II)F",
          &g_android_view_MotionEvent_getHistoricalTouchMinorF_I_I);

  jfloat ret =
      env->CallFloatMethod(obj.obj(),
          call_context.base.method_id, as_jint(p0), as_jint(p1));
  return ret;
}

static std::atomic<jmethodID> g_android_view_MotionEvent_getHistoricalToolMajorF_I_I(nullptr);
static jfloat Java_MotionEvent_getHistoricalToolMajorF_I_I(JNIEnv* env, const
    base::android::JavaRef<jobject>& obj, JniIntWrapper p0,
    JniIntWrapper p1) __attribute__ ((unused));
static jfloat Java_MotionEvent_getHistoricalToolMajorF_I_I(JNIEnv* env, const
    base::android::JavaRef<jobject>& obj, JniIntWrapper p0,
    JniIntWrapper p1) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getHistoricalToolMajor",
          "(II)F",
          &g_android_view_MotionEvent_getHistoricalToolMajorF_I_I);

  jfloat ret =
      env->CallFloatMethod(obj.obj(),
          call_context.base.method_id, as_jint(p0), as_jint(p1));
  return ret;
}

static std::atomic<jmethodID> g_android_view_MotionEvent_getHistoricalToolMinorF_I_I(nullptr);
static jfloat Java_MotionEvent_getHistoricalToolMinorF_I_I(JNIEnv* env, const
    base::android::JavaRef<jobject>& obj, JniIntWrapper p0,
    JniIntWrapper p1) __attribute__ ((unused));
static jfloat Java_MotionEvent_getHistoricalToolMinorF_I_I(JNIEnv* env, const
    base::android::JavaRef<jobject>& obj, JniIntWrapper p0,
    JniIntWrapper p1) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getHistoricalToolMinor",
          "(II)F",
          &g_android_view_MotionEvent_getHistoricalToolMinorF_I_I);

  jfloat ret =
      env->CallFloatMethod(obj.obj(),
          call_context.base.method_id, as_jint(p0), as_jint(p1));
  return ret;
}

static std::atomic<jmethodID> g_android_view_MotionEvent_getHistoricalOrientationF_I_I(nullptr);
static jfloat Java_MotionEvent_getHistoricalOrientationF_I_I(JNIEnv* env, const
    base::android::JavaRef<jobject>& obj, JniIntWrapper p0,
    JniIntWrapper p1) __attribute__ ((unused));
static jfloat Java_MotionEvent_getHistoricalOrientationF_I_I(JNIEnv* env, const
    base::android::JavaRef<jobject>& obj, JniIntWrapper p0,
    JniIntWrapper p1) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getHistoricalOrientation",
          "(II)F",
          &g_android_view_MotionEvent_getHistoricalOrientationF_I_I);

  jfloat ret =
      env->CallFloatMethod(obj.obj(),
          call_context.base.method_id, as_jint(p0), as_jint(p1));
  return ret;
}

static std::atomic<jmethodID> g_android_view_MotionEvent_getHistoricalAxisValueF_I_I_I(nullptr);
static jfloat Java_MotionEvent_getHistoricalAxisValueF_I_I_I(JNIEnv* env, const
    base::android::JavaRef<jobject>& obj, JniIntWrapper p0,
    JniIntWrapper p1,
    JniIntWrapper p2) __attribute__ ((unused));
static jfloat Java_MotionEvent_getHistoricalAxisValueF_I_I_I(JNIEnv* env, const
    base::android::JavaRef<jobject>& obj, JniIntWrapper p0,
    JniIntWrapper p1,
    JniIntWrapper p2) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getHistoricalAxisValue",
          "(III)F",
          &g_android_view_MotionEvent_getHistoricalAxisValueF_I_I_I);

  jfloat ret =
      env->CallFloatMethod(obj.obj(),
          call_context.base.method_id, as_jint(p0), as_jint(p1), as_jint(p2));
  return ret;
}

static std::atomic<jmethodID> g_android_view_MotionEvent_getHistoricalPointerCoords(nullptr);
static void Java_MotionEvent_getHistoricalPointerCoords(JNIEnv* env, const
    base::android::JavaRef<jobject>& obj, JniIntWrapper p0,
    JniIntWrapper p1,
    const base::android::JavaRef<jobject>& p2) __attribute__ ((unused));
static void Java_MotionEvent_getHistoricalPointerCoords(JNIEnv* env, const
    base::android::JavaRef<jobject>& obj, JniIntWrapper p0,
    JniIntWrapper p1,
    const base::android::JavaRef<jobject>& p2) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env));

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getHistoricalPointerCoords",
          "(IILandroid/view/MotionEvent$PointerCoords;)V",
          &g_android_view_MotionEvent_getHistoricalPointerCoords);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id, as_jint(p0), as_jint(p1), p2.obj());
}

static std::atomic<jmethodID> g_android_view_MotionEvent_getEdgeFlags(nullptr);
static jint Java_MotionEvent_getEdgeFlags(JNIEnv* env, const base::android::JavaRef<jobject>& obj)
    __attribute__ ((unused));
static jint Java_MotionEvent_getEdgeFlags(JNIEnv* env, const base::android::JavaRef<jobject>& obj) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getEdgeFlags",
          "()I",
          &g_android_view_MotionEvent_getEdgeFlags);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_android_view_MotionEvent_setEdgeFlags(nullptr);
static void Java_MotionEvent_setEdgeFlags(JNIEnv* env, const base::android::JavaRef<jobject>& obj,
    JniIntWrapper p0) __attribute__ ((unused));
static void Java_MotionEvent_setEdgeFlags(JNIEnv* env, const base::android::JavaRef<jobject>& obj,
    JniIntWrapper p0) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env));

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "setEdgeFlags",
          "(I)V",
          &g_android_view_MotionEvent_setEdgeFlags);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id, as_jint(p0));
}

static std::atomic<jmethodID> g_android_view_MotionEvent_setAction(nullptr);
static void Java_MotionEvent_setAction(JNIEnv* env, const base::android::JavaRef<jobject>& obj,
    JniIntWrapper p0) __attribute__ ((unused));
static void Java_MotionEvent_setAction(JNIEnv* env, const base::android::JavaRef<jobject>& obj,
    JniIntWrapper p0) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env));

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "setAction",
          "(I)V",
          &g_android_view_MotionEvent_setAction);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id, as_jint(p0));
}

static std::atomic<jmethodID> g_android_view_MotionEvent_offsetLocation(nullptr);
static void Java_MotionEvent_offsetLocation(JNIEnv* env, const base::android::JavaRef<jobject>& obj,
    jfloat p0,
    jfloat p1) __attribute__ ((unused));
static void Java_MotionEvent_offsetLocation(JNIEnv* env, const base::android::JavaRef<jobject>& obj,
    jfloat p0,
    jfloat p1) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env));

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "offsetLocation",
          "(FF)V",
          &g_android_view_MotionEvent_offsetLocation);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id, p0, p1);
}

static std::atomic<jmethodID> g_android_view_MotionEvent_setLocation(nullptr);
static void Java_MotionEvent_setLocation(JNIEnv* env, const base::android::JavaRef<jobject>& obj,
    jfloat p0,
    jfloat p1) __attribute__ ((unused));
static void Java_MotionEvent_setLocation(JNIEnv* env, const base::android::JavaRef<jobject>& obj,
    jfloat p0,
    jfloat p1) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env));

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "setLocation",
          "(FF)V",
          &g_android_view_MotionEvent_setLocation);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id, p0, p1);
}

static std::atomic<jmethodID> g_android_view_MotionEvent_transform(nullptr);
static void Java_MotionEvent_transform(JNIEnv* env, const base::android::JavaRef<jobject>& obj,
    const base::android::JavaRef<jobject>& p0) __attribute__ ((unused));
static void Java_MotionEvent_transform(JNIEnv* env, const base::android::JavaRef<jobject>& obj,
    const base::android::JavaRef<jobject>& p0) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env));

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "transform",
          "(Landroid/graphics/Matrix;)V",
          &g_android_view_MotionEvent_transform);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id, p0.obj());
}

static std::atomic<jmethodID> g_android_view_MotionEvent_addBatchV_J_F_F_F_F_I(nullptr);
static void Java_MotionEvent_addBatchV_J_F_F_F_F_I(JNIEnv* env, const
    base::android::JavaRef<jobject>& obj, jlong p0,
    jfloat p1,
    jfloat p2,
    jfloat p3,
    jfloat p4,
    JniIntWrapper p5) __attribute__ ((unused));
static void Java_MotionEvent_addBatchV_J_F_F_F_F_I(JNIEnv* env, const
    base::android::JavaRef<jobject>& obj, jlong p0,
    jfloat p1,
    jfloat p2,
    jfloat p3,
    jfloat p4,
    JniIntWrapper p5) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env));

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "addBatch",
          "(JFFFFI)V",
          &g_android_view_MotionEvent_addBatchV_J_F_F_F_F_I);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id, p0, p1, p2, p3, p4, as_jint(p5));
}

static std::atomic<jmethodID> g_android_view_MotionEvent_addBatchV_J_LAVMEPC_I(nullptr);
static void Java_MotionEvent_addBatchV_J_LAVMEPC_I(JNIEnv* env, const
    base::android::JavaRef<jobject>& obj, jlong p0,
    const base::android::JavaRef<jobjectArray>& p1,
    JniIntWrapper p2) __attribute__ ((unused));
static void Java_MotionEvent_addBatchV_J_LAVMEPC_I(JNIEnv* env, const
    base::android::JavaRef<jobject>& obj, jlong p0,
    const base::android::JavaRef<jobjectArray>& p1,
    JniIntWrapper p2) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env));

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "addBatch",
          "(J[Landroid/view/MotionEvent$PointerCoords;I)V",
          &g_android_view_MotionEvent_addBatchV_J_LAVMEPC_I);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id, p0, p1.obj(), as_jint(p2));
}

static std::atomic<jmethodID> g_android_view_MotionEvent_toString(nullptr);
static base::android::ScopedJavaLocalRef<jstring> Java_MotionEvent_toString(JNIEnv* env, const
    base::android::JavaRef<jobject>& obj) __attribute__ ((unused));
static base::android::ScopedJavaLocalRef<jstring> Java_MotionEvent_toString(JNIEnv* env, const
    base::android::JavaRef<jobject>& obj) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env), NULL);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "toString",
          "()Ljava/lang/String;",
          &g_android_view_MotionEvent_toString);

  jstring ret =
      static_cast<jstring>(env->CallObjectMethod(obj.obj(),
          call_context.base.method_id));
  return base::android::ScopedJavaLocalRef<jstring>(env, ret);
}

static std::atomic<jmethodID> g_android_view_MotionEvent_actionToString(nullptr);
static base::android::ScopedJavaLocalRef<jstring> Java_MotionEvent_actionToString(JNIEnv* env,
    JniIntWrapper p0) __attribute__ ((unused));
static base::android::ScopedJavaLocalRef<jstring> Java_MotionEvent_actionToString(JNIEnv* env,
    JniIntWrapper p0) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, clazz,
      android_view_MotionEvent_clazz(env), NULL);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "actionToString",
          "(I)Ljava/lang/String;",
          &g_android_view_MotionEvent_actionToString);

  jstring ret =
      static_cast<jstring>(env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, as_jint(p0)));
  return base::android::ScopedJavaLocalRef<jstring>(env, ret);
}

static std::atomic<jmethodID> g_android_view_MotionEvent_axisToString(nullptr);
static base::android::ScopedJavaLocalRef<jstring> Java_MotionEvent_axisToString(JNIEnv* env,
    JniIntWrapper p0) __attribute__ ((unused));
static base::android::ScopedJavaLocalRef<jstring> Java_MotionEvent_axisToString(JNIEnv* env,
    JniIntWrapper p0) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, clazz,
      android_view_MotionEvent_clazz(env), NULL);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "axisToString",
          "(I)Ljava/lang/String;",
          &g_android_view_MotionEvent_axisToString);

  jstring ret =
      static_cast<jstring>(env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, as_jint(p0)));
  return base::android::ScopedJavaLocalRef<jstring>(env, ret);
}

static std::atomic<jmethodID> g_android_view_MotionEvent_axisFromString(nullptr);
static jint Java_MotionEvent_axisFromString(JNIEnv* env, const base::android::JavaRef<jstring>& p0)
    __attribute__ ((unused));
static jint Java_MotionEvent_axisFromString(JNIEnv* env, const base::android::JavaRef<jstring>& p0)
    {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, clazz,
      android_view_MotionEvent_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "axisFromString",
          "(Ljava/lang/String;)I",
          &g_android_view_MotionEvent_axisFromString);

  jint ret =
      env->CallStaticIntMethod(clazz,
          call_context.base.method_id, p0.obj());
  return ret;
}

static std::atomic<jmethodID> g_android_view_MotionEvent_writeToParcel(nullptr);
static void Java_MotionEvent_writeToParcel(JNIEnv* env, const base::android::JavaRef<jobject>& obj,
    const base::android::JavaRef<jobject>& p0,
    JniIntWrapper p1) __attribute__ ((unused));
static void Java_MotionEvent_writeToParcel(JNIEnv* env, const base::android::JavaRef<jobject>& obj,
    const base::android::JavaRef<jobject>& p0,
    JniIntWrapper p1) {
  jclass clazz = android_view_MotionEvent_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      android_view_MotionEvent_clazz(env));

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "writeToParcel",
          "(Landroid/os/Parcel;I)V",
          &g_android_view_MotionEvent_writeToParcel);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id, p0.obj(), as_jint(p1));
}

}  // namespace JNI_MotionEvent

// Step 4: Generated test functions (optional).


#endif  // android_view_MotionEvent_JNI
