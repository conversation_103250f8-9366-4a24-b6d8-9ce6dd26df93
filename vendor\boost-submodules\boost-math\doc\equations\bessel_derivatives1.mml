<?xml version="1.0"?>
<math xmlns="http://www.w3.org/1998/Math/MathML">
 <mrow>
    <mtable columnalign="right center left">
    <mtr>
     <mtd>
      <mrow>
       <mi>J</mi>
       <msub>
        <mi>'</mi>
        <mi>v</mi>
       </msub>
       <mrow>
        <mo stretchy="false">(</mo>
        <mrow>
         <mi>x</mi>
        </mrow>
        <mo stretchy="false">)</mo>
       </mrow>
      </mrow>
     </mtd>
     <mtd>
      <mrow>
       <mrow>
        <mrow/>
        <mo stretchy="false">=</mo>
        <mrow/>
       </mrow>
      </mrow>
     </mtd>
     <mtd>
      <mrow>
       <mfrac>
        <mrow>
         <msub>
          <mi>J</mi>
          <mrow>
           <mrow>
            <mi>v</mi>
            <mo stretchy="false">−</mo>
            <mn>1</mn>
           </mrow>
          </mrow>
         </msub>
         <mrow>
          <mrow>
           <mo stretchy="false">(</mo>
           <mrow>
            <mi>x</mi>
           </mrow>
           <mo stretchy="false">)</mo>
          </mrow>
          <mo stretchy="false">−</mo>
          <msub>
           <mi>J</mi>
           <mrow>
            <mrow>
             <mi>v</mi>
             <mo stretchy="false">+</mo>
             <mn>1</mn>
            </mrow>
           </mrow>
          </msub>
         </mrow>
         <mrow>
          <mo stretchy="false">(</mo>
          <mrow>
           <mi>x</mi>
          </mrow>
          <mo stretchy="false">)</mo>
         </mrow>
        </mrow>
        <mn>2</mn>
       </mfrac>
      </mrow>
     </mtd>
    </mtr>
    <mtr>
     <mtd>
      <mrow>
       <mrow>
        <mi>Y</mi>
        <msub>
         <mi>'</mi>
         <mi>v</mi>
        </msub>
        <mrow>
         <mo stretchy="false">(</mo>
         <mrow>
          <mi>x</mi>
         </mrow>
         <mo stretchy="false">)</mo>
        </mrow>
       </mrow>
      </mrow>
     </mtd>
     <mtd>
      <mrow>
       <mrow>
        <mrow/>
        <mo stretchy="false">=</mo>
        <mrow/>
       </mrow>
      </mrow>
     </mtd>
     <mtd>
      <mrow>
       <mfrac>
        <mrow>
         <msub>
          <mi>Y</mi>
          <mrow>
           <mrow>
            <mi>v</mi>
            <mo stretchy="false">−</mo>
            <mn>1</mn>
           </mrow>
          </mrow>
         </msub>
         <mrow>
          <mrow>
           <mo stretchy="false">(</mo>
           <mrow>
            <mi>x</mi>
           </mrow>
           <mo stretchy="false">)</mo>
          </mrow>
          <mo stretchy="false">−</mo>
          <msub>
           <mi>Y</mi>
           <mrow>
            <mrow>
             <mi>v</mi>
             <mo stretchy="false">+</mo>
             <mn>1</mn>
            </mrow>
           </mrow>
          </msub>
         </mrow>
         <mrow>
          <mo stretchy="false">(</mo>
          <mrow>
           <mi>x</mi>
          </mrow>
          <mo stretchy="false">)</mo>
         </mrow>
        </mrow>
        <mn>2</mn>
       </mfrac>
      </mrow>
     </mtd>
    </mtr>
    <mtr>
     <mtd>
      <mrow>
       <mrow>
        <mi>I</mi>
        <msub>
         <mi>'</mi>
         <mi>v</mi>
        </msub>
        <mrow>
         <mo stretchy="false">(</mo>
         <mrow>
          <mi>x</mi>
         </mrow>
         <mo stretchy="false">)</mo>
        </mrow>
       </mrow>
      </mrow>
     </mtd>
     <mtd>
      <mrow>
       <mrow>
        <mrow/>
        <mo stretchy="false">=</mo>
        <mrow/>
       </mrow>
      </mrow>
     </mtd>
     <mtd>
      <mrow>
       <mfrac>
        <mrow>
         <msub>
          <mi>I</mi>
          <mrow>
           <mrow>
            <mi>v</mi>
            <mo stretchy="false">−</mo>
            <mn>1</mn>
           </mrow>
          </mrow>
         </msub>
         <mrow>
          <mrow>
           <mo stretchy="false">(</mo>
           <mrow>
            <mi>x</mi>
           </mrow>
           <mo stretchy="false">)</mo>
          </mrow>
          <mo stretchy="false">+</mo>
          <msub>
           <mi>I</mi>
           <mrow>
            <mrow>
             <mi>v</mi>
             <mo stretchy="false">+</mo>
             <mn>1</mn>
            </mrow>
           </mrow>
          </msub>
         </mrow>
         <mrow>
          <mo stretchy="false">(</mo>
          <mrow>
           <mi>x</mi>
          </mrow>
          <mo stretchy="false">)</mo>
         </mrow>
        </mrow>
        <mn>2</mn>
       </mfrac>
      </mrow>
     </mtd>
    </mtr>
    <mtr>
     <mtd>
      <mrow>
       <mrow>
        <mi>K</mi>
        <msub>
         <mi>'</mi>
         <mi>v</mi>
        </msub>
        <mrow>
         <mo stretchy="false">(</mo>
         <mrow>
          <mi>x</mi>
         </mrow>
         <mo stretchy="false">)</mo>
        </mrow>
       </mrow>
      </mrow>
     </mtd>
     <mtd>
      <mrow>
       <mrow>
        <mrow/>
        <mo stretchy="false">=</mo>
        <mrow/>
       </mrow>
      </mrow>
     </mtd>
     <mtd>
      <mrow>
       <mfrac>
        <mrow>
         <msub>
          <mi>K</mi>
          <mrow>
           <mrow>
            <mi>v</mi>
            <mo stretchy="false">−</mo>
            <mn>1</mn>
           </mrow>
          </mrow>
         </msub>
         <mrow>
          <mrow>
           <mo stretchy="false">(</mo>
           <mrow>
            <mi>x</mi>
           </mrow>
           <mo stretchy="false">)</mo>
          </mrow>
          <mo stretchy="false">+</mo>
          <msub>
           <mi>K</mi>
           <mrow>
            <mrow>
             <mi>v</mi>
             <mo stretchy="false">+</mo>
             <mn>1</mn>
            </mrow>
           </mrow>
          </msub>
         </mrow>
         <mrow>
          <mo stretchy="false">(</mo>
          <mrow>
           <mi>x</mi>
          </mrow>
          <mo stretchy="false">)</mo>
         </mrow>
        </mrow>
        <mrow>
         <mo stretchy="false">−</mo>
         <mn>2</mn>
        </mrow>
       </mfrac>
      </mrow>
     </mtd>
    </mtr>
    <mtr>
     <mtd>
      <mrow>
       <mrow>
        <mi>j</mi>
        <msub>
         <mi>'</mi>
         <mi>n</mi>
        </msub>
       </mrow>
      </mrow>
     </mtd>
     <mtd>
      <mrow>
       <mrow>
        <mrow/>
        <mo stretchy="false">=</mo>
        <mrow/>
       </mrow>
      </mrow>
     </mtd>
     <mtd>
      <mrow>
       <mrow>
        <mfenced open="(" close=")">
         <mrow>
          <mfrac>
           <mi>n</mi>
           <mi>x</mi>
          </mfrac>
         </mrow>
        </mfenced>
        <msub>
         <mi>j</mi>
         <mi>n</mi>
        </msub>
        <mrow>
         <mrow>
          <mo stretchy="false">(</mo>
          <mrow>
           <mi>x</mi>
          </mrow>
          <mo stretchy="false">)</mo>
         </mrow>
         <mo stretchy="false">−</mo>
         <msub>
          <mi>j</mi>
          <mrow>
           <mrow>
            <mi>n</mi>
            <mo stretchy="false">+</mo>
            <mn>1</mn>
           </mrow>
          </mrow>
         </msub>
        </mrow>
        <mrow>
         <mo stretchy="false">(</mo>
         <mrow>
          <mi>x</mi>
         </mrow>
         <mo stretchy="false">)</mo>
        </mrow>
       </mrow>
      </mrow>
     </mtd>
    </mtr>
    <mtr>
     <mtd>
      <mrow>
       <mrow>
        <mi>y</mi>
        <msub>
         <mi>'</mi>
         <mi>n</mi>
        </msub>
       </mrow>
      </mrow>
     </mtd>
     <mtd>
      <mrow>
       <mrow>
        <mrow/>
        <mo stretchy="false">=</mo>
        <mrow/>
       </mrow>
      </mrow>
     </mtd>
     <mtd>
      <mrow>
       <mrow>
        <mfenced open="(" close=")">
         <mrow>
          <mfrac>
           <mi>n</mi>
           <mi>x</mi>
          </mfrac>
         </mrow>
        </mfenced>
        <msub>
         <mi>y</mi>
         <mi>n</mi>
        </msub>
        <mrow>
         <mrow>
          <mo stretchy="false">(</mo>
          <mrow>
           <mi>x</mi>
          </mrow>
          <mo stretchy="false">)</mo>
         </mrow>
         <mo stretchy="false">−</mo>
         <msub>
          <mi>y</mi>
          <mrow>
           <mrow>
            <mi>n</mi>
            <mo stretchy="false">+</mo>
            <mn>1</mn>
           </mrow>
          </mrow>
         </msub>
        </mrow>
        <mrow>
         <mo stretchy="false">(</mo>
         <mrow>
          <mi>x</mi>
         </mrow>
         <mo stretchy="false">)</mo>
        </mrow>
       </mrow>
      </mrow>
     </mtd>
    </mtr>
   </mtable>
  </mrow>
</math>
