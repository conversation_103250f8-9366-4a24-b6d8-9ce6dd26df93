#if MONO_V2
namespace CitizenFX.FiveM
#else
namespace CitizenFX.Core
#endif
{
	public enum WeaponHash : uint
	{
		Knife = 2578778090u,
		Nightstick = 1737195953u,
		Hammer = 1317494643u,
		Bat = 2508868239u,
		GolfClub = 1141786504u,
		Crowbar = 2227010557u,
		Bottle = 4192643659u,
		SwitchBlade = 3756226112u,
		Pistol = 453432689u,
		CombatPistol = 1593441988u,
		APPistol = 584646201u,
		Pistol50 = 2578377531u,
		FlareGun = 1198879012u,
		MarksmanPistol = 3696079510u,
		Revolver = 3249783761u,
		MicroSMG = 324215364u,
		SMG = 736523883u,
		AssaultSMG = 4024951519u,
		CombatPDW = 171789620u,
		AssaultRifle = 3220176749u,
		CarbineRifle = 2210333304u,
		AdvancedRifle = 2937143193u,
		CompactRifle = 1649403952u,
		MG = 2634544996u,
		CombatMG = 2144741730u,
		PumpShotgun = 487013001u,
		SawnOffShotgun = 2017895192u,
		AssaultShotgun = 3800352039u,
		BullpupShotgun = 2640438543u,
		DoubleBarrelShotgun = 4019527611u,
		StunGun = 911657153u,
		SniperRifle = 100416529u,
		HeavySniper = 205991906u,
		GrenadeLauncher = 2726580491u,
		GrenadeLauncherSmoke = 1305664598u,
		RPG = 2982836145u,
		Minigun = 1119849093u,
		Grenade = 2481070269u,
		StickyBomb = 741814745u,
		SmokeGrenade = 4256991824u,
		BZGas = 2694266206u,
		Molotov = 615608432u,
		FireExtinguisher = 101631238u,
		PetrolCan = 883325847u,
		SNSPistol = 3218215474u,
		SpecialCarbine = 3231910285u,
		HeavyPistol = 3523564046u,
		BullpupRifle = 2132975508u,
		HomingLauncher = 1672152130u,
		ProximityMine = 2874559379u,
		Snowball = 126349499u,
		VintagePistol = 137902532u,
		Dagger = 2460120199u,
		Firework = 2138347493u,
		Musket = 2828843422u,
		MarksmanRifle = 3342088282u,
		HeavyShotgun = 984333226u,
		Gusenberg = 1627465347u,
		Hatchet = 4191993645u,
		Railgun = 1834241177u,
		Unarmed = 2725352035u,
		KnuckleDuster = 3638508604u,
		Machete = 3713923289u,
		MachinePistol = 3675956304u,
		Flashlight = 2343591895u,
		Ball = 600439132u,
		Flare = 1233104067u,
		NightVision = 2803906140u,
		Parachute = 4222310262u,
		SweeperShotgun = 317205821u,
		BattleAxe = 3441901897u,
		CompactGrenadeLauncher = 125959754u,
		MiniSMG = 3173288789u,
		PipeBomb = 3125143736u,
		PoolCue = 2484171525u,
		Wrench = 419712736u,
		//mpgunrunning
		PistolMk2 = 0xBFE256D4,
		AssaultRifleMk2 = 0x394F415C,
		CarbineRifleMk2 = 0xFAD1F1C9,
		CombatMGMk2 = 0xDBBD7280,
		HeavySniperMk2 = 0xA914799,
		SMGMk2 = 0x78A97CD0,
		//mpchristmas2017
        RevolverMk2 = 0xCB96392F,
        SpecialCarbineMk2 = 0x969C3D67,
        MarksmanRifleMk2 = 0x6A6C02E0,
        PumpShotgunMk2 = 0x555AF99A,
        SNSPistolMk2 = 0x88374054,
        BullpupRifleMk2 = 0x84D6FAFD,
        DoubleAction = 0x97EA20B8,
        //mpbattle
        StoneHatchet = 0x3813FC08,
        //mpchristmas2018
        RayPistol = 0xAF3696A1,
        RayMinigun = 0xB62D1F67,
        RayCarbine = 0x476BF155,
		//mpheist3
		CeramicPistol = 0x2B5EF5EC,
		HazardCan = 0xBA536372,
		NacyRevolver = 0x917F6C8C,
		Tranquilizer = 0x32A888BD,
		//mpheist4
		GadgetPistol = 0x57A4368C,
		CombatShotgun = 0x5A96BA4,
		MilitaryRifle = 0x9D1F17E6, 
		//mpsecurity
		HeavyRifle = 0xC78D71B4,
		FertukuterCan = 0x184140A1,
		Stungun_MP = 0x45CD9CF3,
		EmpLauncher = 0xDB26713A,
		//mpsum2
		TacticalRifle = 0xD1D5F52B,
		MetalDetector = 0xDBA2E809,
		PrecisionRifle = 0x6E7DDDEC,
		//mpchristmas3
		CandyCane = 0x6589186A,
		AcidPackage = 0xF7F1E25E,
		PistolXM3 = 0x1BC4FDB9,
		RailgunXM3 = 0xFEA23564,
		//mp2023_01
		TecPistol = 0x14E5AFD5,
		//mp2023_02
		BattleRifle = 0x72B66B11,
		SnowLauncher = 0x3BF5575,
		HackingDevice = 0x1CF5FA48,
		//mp2024_01
		Stunrod = 0xDAC00025
	}

	public enum VehicleWeaponHash
	{
		Invalid = -1,
		Tank = 1945616459,
		SpaceRocket = -123497569,
		PlaneRocket = -821520672,
		PlayerLaser = -268631733,
		PlayerBullet = 1259576109,
		PlayerBuzzard = 1186503822,
		PlayerHunter = -1625648674,
		PlayerLazer = -494786007,
		EnemyLaser = 1566990507,
		SearchLight = -844344963,
		Radar = -764006018
	}

	public enum AmmoType : uint
	{
		Melee = 0,
		FireExtinguisher = 0x5106B43C,
		Flare = 0x6BCCF76F,
		FlareGun = 0x45F0E965,
		PetrolCan = 0xCA6318A1,
		Shotgun = 0x90083D3B,
		Pistol = 0x743D4F54,
		Ball = 0xFF956666,
		Snowball = 0x8218416D,
		Sniper = 0x4C98087B,
		AssaultRifle = 0xD05319F,
		SMG = 0x6C7D23B8,
		Molotov = 0x5633F9D5,
		StunGun = 0xB02EADE0,
		MG = 0x6AA1343F,
		GrenadeLauncher = 0x3BCCA5EE,
		RPG = 0x67DD81F2,
		Minigun = 0x9FC5C882,
		Firework = 0xAF23EE0F,
		Railgun = 0x794446FD,
		HomingLauncher = 0x99150E2D,
		Grenade = 0x3BD313B1,
		StickyBomb = 0x5424B617,
		ProximityMine = 0xAF2208A7,
		PipeBomb = 0x155663F8,
		SmokeGrenade = 0xE60E08A6,
		BZGas = 0x9B747EA4,
		RayPistol = 0xA50ABB74
	}
}
