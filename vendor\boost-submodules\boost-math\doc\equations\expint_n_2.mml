<math xmlns="http://www.w3.org/1998/Math/MathML" display="block">
  <mrow>
    <msub>
      <mi>E</mi>
      <mi>n</mi>
    </msub>
    <mfenced>
      <mrow>
        <mi>x</mi>
      </mrow>
    </mfenced>
    <mo>=</mo>
    <mfrac>
      <msup>
        <mfenced>
          <mrow>
            <mo>&#x2212;</mo>
            <mi>z</mi>
          </mrow>
        </mfenced>
        <mrow>
          <mi>n</mi>
          <mo>&#x2212;</mo>
          <mn>1</mn>
        </mrow>
      </msup>
      <mrow>
        <mfenced>
          <mrow>
            <mi>n</mi>
            <mo>&#x2212;</mo>
            <mn>1</mn>
          </mrow>
        </mfenced>
        <mo>!</mo>
      </mrow>
    </mfrac>
    <mfenced>
      <mrow>
        <mi>&#x03C8;</mi>
        <mfenced>
          <mrow>
            <mi>n</mi>
          </mrow>
        </mfenced>
        <mo>&#x2212;</mo>
        <mi>log</mi>
        <mfenced>
          <mrow>
            <mi>z</mi>
          </mrow>
        </mfenced>
      </mrow>
    </mfenced>
    <mo>&#x2212;</mo>
    <munderover>
      <mo>&#x2211;</mo>
      <mrow>
        <mi>k</mi>
        <mo>=</mo>
        <mn>0,</mn>
        <mi>k</mi>
        <mo>&#x2260;</mo>
        <mi>n</mi>
        <mo>&#x2212;</mo>
        <mn>1</mn>
      </mrow>
      <mi>&#x221E;</mi>
    </munderover>
    <mfrac>
      <mrow>
        <msup>
          <mfenced>
            <mrow>
              <mo>&#x2212;</mo>
              <mn>1</mn>
            </mrow>
          </mfenced>
          <mi>k</mi>
        </msup>
        <msup>
          <mi>z</mi>
          <mi>k</mi>
        </msup>
      </mrow>
      <mrow>
        <mfenced>
          <mrow>
            <mi>k</mi>
            <mo>&#x2212;</mo>
            <mi>n</mi>
            <mo>+</mo>
            <mn>1</mn>
          </mrow>
        </mfenced>
        <mi>k</mi>
        <mo>!</mo>
      </mrow>
    </mfrac>
  </mrow>
</math>