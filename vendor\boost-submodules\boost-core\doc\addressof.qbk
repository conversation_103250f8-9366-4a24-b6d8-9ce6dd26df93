[/
  Copyright 2014 <PERSON>

  Distributed under the Boost Software License, Version 1.0.

  See accompanying file LICENSE_1_0.txt
  or copy at http://boost.org/LICENSE_1_0.txt
]

[section:addressof addressof]

[simplesect Authors]

* <PERSON>
* <PERSON>
* Peter <PERSON>
* <PERSON>

[endsimplesect]

[section Header <boost/core/addressof.hpp>]

The header `<boost/core/addressof.hpp>` defines the function
template `boost::addressof`. `boost::addressof(x)` returns the
address of `x`. Ordinarily, this address can be obtained by
`&x`, but the unary `&` operator can be overloaded. `boost::addressof`
avoids calling used-defined `operator&()`.

`boost::addressof` was originally contributed by <PERSON>
based on ideas from discussion with <PERSON>.

[section Synopsis]

``
namespace boost
{
    template<class T> T* addressof( T& x );
}
``

[endsect]

[section Example]

``
#include <boost/core/addressof.hpp>

struct useless_type { };

class nonaddressable {
    useless_type operator&() const;
};

void f() {
    nonaddressable x;
    nonaddressable* xp = boost::addressof(x);
    // nonaddressable* xpe = &x; /* error */
}
``

[endsect]

[section Notes]

In C++11 and above, `boost::addressof` is conditionally
`constexpr` when possible. This is indicated by
`BOOST_CORE_NO_CONSTEXPR_ADDRESSOF` not being defined.

With supported compilers, `boost::addressof` is always
`constexpr` by leveraging compiler intrinsics. This is
indicated by `BOOST_CORE_HAS_BUILTIN_ADDRESSOF` being
defined.

[endsect]

[endsect]

[endsect]
