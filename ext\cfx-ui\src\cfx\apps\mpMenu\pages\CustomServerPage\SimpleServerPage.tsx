import { observer } from 'mobx-react-lite';
import React from 'react';
import { useService } from 'cfx/base/servicesContainer';
import { Page } from '@cfx-dev/ui-components';
import { IServersConnectService } from 'cfx/common/services/servers/serversConnect.service';
import { useCustomAuthService } from 'cfx/apps/mpMenu/services/customAuth/customAuth.service';
import { BackgroundSlideshow } from 'cfx/apps/mpMenu/parts/CustomAuth/BackgroundSlideshow';

// Server connection info - KHÔNG hiển thị IP cho user
const CUSTOM_SERVER_ADDRESS = '*************:30120';
const SERVER_DISPLAY_NAME = 'Gang Hai City'; // Tên hiển thị thay vì IP

// Background images - same as login screen
const BACKGROUND_IMAGES = [
  require('../../../../../assets/images/05b1de299c79fd87f705632ed6386708.jpg'),
  require('../../../../../assets/images/8cbbae59e6963d71ed170541c63c2112.jpg'),
  require('../../../../../assets/images/9f97b1291d17c25219e6440386a3beee.jpg'),
  require('../../../../../assets/images/560d9cfb2f4aa35690b627c335c358da.jpg'),
  require('../../../../../assets/images/836eade90d395afd011ab184b17f2f71.jpg'),
  require('../../../../../assets/images/9098cbf7d4bfbcdcaa0a741aa0278c17.jpg'),
  require('../../../../../assets/images/a7a78b188fa75acc434e2cd191cb9115.jpg'),
];

export const SimpleServerPage = observer(function SimpleServerPage() {
  const serversConnectService = useService(IServersConnectService);
  const customAuthService = useCustomAuthService();

  // Add CSS animation styles for GTA V theme
  React.useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      @keyframes slideIn {
        0% {
          opacity: 0;
          transform: translateY(40px) scale(0.9);
          filter: blur(10px);
        }
        50% {
          opacity: 0.8;
          transform: translateY(10px) scale(0.95);
          filter: blur(2px);
        }
        100% {
          opacity: 1;
          transform: translateY(0) scale(1);
          filter: blur(0);
        }
      }

      @keyframes pulse {
        0%, 100% {
          opacity: 1;
          transform: scale(1);
        }
        50% {
          opacity: 0.7;
          transform: scale(1.1);
        }
      }

      @keyframes glow {
        0%, 100% {
          box-shadow: 0 0 5px rgba(255, 140, 66, 0.3);
        }
        50% {
          box-shadow: 0 0 20px rgba(255, 140, 66, 0.6);
        }
      }

      @keyframes fadeInUp {
        from {
          opacity: 0;
          transform: translateY(20px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      /* Smooth transitions for all interactive elements */
      button {
        transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
      }

      /* Enhanced glassmorphism effect */
      .glass-effect {
        backdrop-filter: blur(20px) saturate(180%);
        -webkit-backdrop-filter: blur(20px) saturate(180%);
      }


    `;
    document.head.appendChild(style);
    return () => {
      document.head.removeChild(style);
    };
  }, []);

  // Direct connect function - chỉ dùng IP:PORT
  const handleConnect = () => {
    console.log('🔘 Button clicked! Starting connection process...');
    console.log('🔗 Server address:', CUSTOM_SERVER_ADDRESS);
    console.log('🔧 Service available:', !!serversConnectService);

    try {
      console.log('🚀 Attempting to connect to server:', CUSTOM_SERVER_ADDRESS);
      serversConnectService.connectTo(CUSTOM_SERVER_ADDRESS);
      console.log('✅ Connection request sent successfully!');
    } catch (error) {
      console.error('❌ Failed to connect to server:', error);
      alert(`Failed to connect to server: ${error.message || 'Unknown error'}. Please try again.`);
    }
  };

  const handleLogout = () => {
    console.log('🚪 Logging out...');
    customAuthService.logout();
  };

  // Get user info - KHÔNG hiển thị IP
  const userName = customAuthService.user?.username || 'Player';

  // Generate avatar based on username (like Gmail/Google)
  const generateAvatar = (name: string) => {
    const initials = name
      .split(' ')
      .map(word => word.charAt(0).toUpperCase())
      .join('')
      .substring(0, 2);

    // Generate consistent color based on name
    const colors = [
      '#FF8C42', '#7CB342', '#42A5F5', '#AB47BC',
      '#FF7043', '#26A69A', '#FFA726', '#66BB6A',
      '#EF5350', '#5C6BC0', '#FFCA28', '#26C6DA'
    ];

    let hash = 0;
    for (let i = 0; i < name.length; i++) {
      hash = name.charCodeAt(i) + ((hash << 5) - hash);
    }
    const colorIndex = Math.abs(hash) % colors.length;

    return {
      initials,
      backgroundColor: colors[colorIndex]
    };
  };

  const avatar = generateAvatar(userName);



  return (
    <Page>
      {/* Background slideshow - same as login */}
      <div style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        width: '100vw',
        height: '100vh',
        zIndex: 1
      }}>
        <BackgroundSlideshow
          images={BACKGROUND_IMAGES}
          interval={6000} // 6 seconds between transitions
          fadeDuration={1500} // 1.5 second fade duration
        />
      </div>



      {/* GTA V Style Server Connection Interface */}
      <div style={{
        position: 'relative',
        zIndex: 10,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '100vh',
        padding: '2rem'
      }}>
        {/* Main Container - GTA V Style */}
        <div style={{
          background: 'linear-gradient(145deg, rgba(15, 15, 25, 0.95) 0%, rgba(25, 25, 35, 0.9) 100%)',
          backdropFilter: 'blur(25px)',
          borderRadius: '24px',
          padding: '0',
          maxWidth: '520px',
          width: '100%',
          border: '2px solid rgba(255, 140, 66, 0.3)',
          boxShadow: `
            0 30px 60px -12px rgba(0, 0, 0, 0.7),
            0 0 0 1px rgba(255, 255, 255, 0.05),
            inset 0 1px 0 rgba(255, 255, 255, 0.1)
          `,
          animation: 'slideIn 0.6s cubic-bezier(0.16, 1, 0.3, 1)',
          overflow: 'hidden',
          position: 'relative'
        }}>
          {/* Header Section */}
          <div style={{
            background: 'linear-gradient(135deg, rgba(255, 140, 66, 0.15) 0%, rgba(124, 179, 66, 0.1) 100%)',
            padding: '2rem 2.5rem 1.5rem',
            borderBottom: '1px solid rgba(255, 140, 66, 0.2)',
            position: 'relative'
          }}>
            {/* Decorative elements */}
            <div style={{
              position: 'absolute',
              top: '10px',
              right: '20px',
              width: '60px',
              height: '3px',
              background: 'linear-gradient(90deg, #FF8C42, #7CB342)',
              borderRadius: '2px',
              opacity: 0.8
            }} />

            {/* Avatar */}
            <div style={{
              display: 'flex',
              justifyContent: 'center',
              marginBottom: '1.5rem'
            }}>
              <div style={{
                width: '80px',
                height: '80px',
                borderRadius: '50%',
                backgroundColor: avatar.backgroundColor,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '2rem',
                fontWeight: '700',
                color: 'white',
                textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)',
                border: '3px solid rgba(255, 255, 255, 0.2)',
                boxShadow: `
                  0 8px 25px rgba(0, 0, 0, 0.3),
                  0 0 0 1px rgba(255, 255, 255, 0.1),
                  inset 0 1px 0 rgba(255, 255, 255, 0.2)
                `,
                animation: 'fadeInUp 0.6s ease-out 0.2s both'
              }}>
                {avatar.initials}
              </div>
            </div>

            {/* Welcome Message */}
            <div style={{
              fontSize: '1.8rem',
              fontWeight: '700',
              background: 'linear-gradient(135deg, #ffffff 0%, #e0e0e0 100%)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              marginBottom: '0.5rem',
              textAlign: 'center',
              letterSpacing: '0.5px'
            }}>
              Chào mừng trở lại
            </div>

            {/* Username */}
            <div style={{
              fontSize: '1.3rem',
              fontWeight: '600',
              color: '#FF8C42',
              textAlign: 'center',
              textShadow: '0 2px 8px rgba(255, 140, 66, 0.3)',
              marginBottom: '1rem'
            }}>
              {userName}
            </div>

            {/* Server Info */}
            <div style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '12px',
              padding: '12px 20px',
              background: 'rgba(0, 0, 0, 0.3)',
              borderRadius: '12px',
              border: '1px solid rgba(255, 140, 66, 0.2)'
            }}>
              <div style={{
                width: '12px',
                height: '12px',
                background: '#7CB342',
                borderRadius: '50%',
                boxShadow: '0 0 12px rgba(124, 179, 66, 0.6)',
                animation: 'pulse 2s infinite'
              }} />
              <span style={{
                color: 'rgba(255, 255, 255, 0.9)',
                fontSize: '1rem',
                fontWeight: '500'
              }}>
                {SERVER_DISPLAY_NAME}
              </span>
            </div>
          </div>

          {/* Content Section */}
          <div style={{ padding: '2.5rem' }}>
            {/* Status Message */}
            <div style={{
              textAlign: 'center',
              marginBottom: '2.5rem'
            }}>
              <div style={{
                fontSize: '1.1rem',
                color: 'rgba(255, 255, 255, 0.8)',
                lineHeight: '1.6',
                marginBottom: '1rem'
              }}>
                Thành phố đã sẵn sàng cho cuộc nhập vai của bạn
              </div>
              <div style={{
                fontSize: '0.9rem',
                color: 'rgba(255, 255, 255, 0.6)',
                fontStyle: 'italic'
              }}>
                Nhấn kết nối để bắt đầu trải nghiệm
              </div>
            </div>

            {/* Connect Button - GTA V Style */}
            <div style={{ marginBottom: '1.5rem' }}>
              <button
                onClick={handleConnect}
                style={{
                  background: 'linear-gradient(135deg, #FF8C42 0%, #7CB342 100%)',
                  border: 'none',
                  borderRadius: '16px',
                  padding: '18px 32px',
                  fontSize: '1.2rem',
                  fontWeight: '700',
                  color: 'white',
                  cursor: 'pointer',
                  width: '100%',
                  transition: 'all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
                  boxShadow: `
                    0 12px 30px rgba(255, 140, 66, 0.4),
                    0 0 0 1px rgba(255, 255, 255, 0.1),
                    inset 0 1px 0 rgba(255, 255, 255, 0.2)
                  `,
                  textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)',
                  position: 'relative',
                  overflow: 'hidden',
                  textTransform: 'uppercase',
                  letterSpacing: '1px'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.transform = 'translateY(-3px) scale(1.02)';
                  e.currentTarget.style.boxShadow = `
                    0 20px 40px rgba(255, 140, 66, 0.5),
                    0 0 0 1px rgba(255, 255, 255, 0.2),
                    inset 0 1px 0 rgba(255, 255, 255, 0.3)
                  `;
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.transform = 'translateY(0) scale(1)';
                  e.currentTarget.style.boxShadow = `
                    0 12px 30px rgba(255, 140, 66, 0.4),
                    0 0 0 1px rgba(255, 255, 255, 0.1),
                    inset 0 1px 0 rgba(255, 255, 255, 0.2)
                  `;
                }}
                onMouseDown={(e) => {
                  e.currentTarget.style.transform = 'translateY(-1px) scale(0.98)';
                }}
                onMouseUp={(e) => {
                  e.currentTarget.style.transform = 'translateY(-3px) scale(1.02)';
                }}
              >
                <span style={{ position: 'relative', zIndex: 2 }}>
                  🎮 Kết nối Server
                </span>
              </button>
            </div>

            {/* Secondary Actions */}
            <div style={{
              display: 'flex',
              justifyContent: 'center'
            }}>
              {/* Logout Button - Centered */}
              <button
                onClick={handleLogout}
                style={{
                  background: 'rgba(255, 59, 48, 0.2)',
                  border: '1px solid rgba(255, 59, 48, 0.4)',
                  borderRadius: '12px',
                  padding: '12px 24px',
                  fontSize: '0.9rem',
                  fontWeight: '500',
                  color: 'rgba(255, 255, 255, 0.9)',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  backdropFilter: 'blur(10px)',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.background = 'rgba(255, 59, 48, 0.3)';
                  e.currentTarget.style.transform = 'translateY(-1px)';
                  e.currentTarget.style.boxShadow = '0 4px 12px rgba(255, 59, 48, 0.3)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.background = 'rgba(255, 59, 48, 0.2)';
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.boxShadow = 'none';
                }}
              >
                🚪 Đăng xuất
              </button>
            </div>
          </div>

          {/* Bottom Accent */}
          <div style={{
            height: '4px',
            background: 'linear-gradient(90deg, #FF8C42 0%, #7CB342 100%)',
            borderRadius: '0 0 22px 22px'
          }} />
        </div>
      </div>
    </Page>
  );
});
