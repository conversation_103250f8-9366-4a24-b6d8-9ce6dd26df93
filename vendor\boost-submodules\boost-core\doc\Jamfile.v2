# Copyright 2014 <PERSON>
# (<EMAIL>)
#
# Distributed under the Boost Software License,
# Version 1.0. (See accompanying file LICENSE_1_0.txt
# or copy at http://boost.org/LICENSE_1_0.txt)

import project ;
import doxygen ;
import quickbook ;

path-constant INCLUDES : ../../.. ;

doxygen ref_reference
  :
    $(INCLUDES)/boost/core/ref.hpp
  :
    <doxygen:param>ENABLE_PREPROCESSING=YES
    <doxygen:param>EXPAND_ONLY_PREDEF=YES
    <doxygen:param>EXTRACT_ALL=NO
    <doxygen:param>EXTRACT_PRIVATE=NO
    <doxygen:param>HIDE_UNDOC_MEMBERS=YES
    <doxygen:param>MACRO_EXPANSION=YES
    <doxygen:param>"PREDEFINED=BOOST_CORE_DOXYGEN \\
                    BOOST_SYMBOL_VISIBLE= \\
                    BOOST_FORCEINLINE=inline \\
                    BOOST_GPU_ENABLED= \\
                    BOOST_STATIC_ASSERT(x)= \\
                    BOOST_STATIC_ASSERT_MSG(x,y)= \\
                    BOOST_STATIC_CONSTANT(x,y)=\"static constexpr x y\" \\
                    BOOST_RV_REF(x)=\"x&&\" \\
                    BOOST_NESTED_TEMPLATE=template \\
                    BOOST_CONSTEXPR=constexpr \\
                    BOOST_CONSTEXPR_OR_CONST=constexpr \\
                    BOOST_NOEXCEPT=noexcept \\
                    BOOST_NOEXCEPT_IF(x)=noexcept(x) \\
                    BOOST_NOEXCEPT_OR_NOTHROW=noexcept \\
                    BOOST_COPY_ASSIGN_REF(x)=\"x const&\" \\
                    BOOST_DEFAULTED_FUNCTION(x,y)=\"x = default;\" \\
                    BOOST_DELETED_FUNCTION(x)=\"x = delete;\" \\
                    BOOST_EXPLICIT_OPERATOR_BOOL()=\"explicit operator bool() const;\" \\
                    BOOST_REF_CONST=const"
  ;

xml core : core.qbk ;

boostbook standalone
  :
    core
  :
    <dependency>ref_reference
    <xsl:param>boost.root=../../../..
    <xsl:param>generate.section.toc.level=1
    <xsl:param>toc.max.depth=1
    <format>pdf:<xsl:param>boost.url.prefix="http://www.boost.org/doc/libs/release/libs/core/doc/html"
  ;

###############################################################################
alias boostdoc ;
explicit boostdoc ;
alias boostrelease : standalone ;
explicit boostrelease ;
