# Generated by `boostdep --cmake core`
# Copyright 2020, 2021 <PERSON> Di<PERSON>
# Distributed under the Boost Software License, Version 1.0.
# https://www.boost.org/LICENSE_1_0.txt

cmake_minimum_required(VERSION 3.5...3.20)

project(boost_core VERSION "${BOOST_SUPERPROJECT_VERSION}" LANGUAGES CXX)

add_library(boost_core INTERFACE)
add_library(Boost::core ALIAS boost_core)

target_include_directories(boost_core INTERFACE include)

target_link_libraries(boost_core
  INTERFACE
    Boost::assert
    Boost::config
    Boost::static_assert
    Boost::throw_exception
)

if(CMAKE_VERSION VERSION_GREATER 3.18 AND CMAKE_GENERATOR MATCHES "Visual Studio")

  file(GLOB_RECURSE boost_core_IDEFILES CONFIGURE_DEPENDS include/*.hpp)
  source_group(TREE ${PROJECT_SOURCE_DIR}/include FILES ${boost_core_IDEFILES} PREFIX "Header Files")
  list(APPEND boost_core_IDEFILES extra/boost_core.natvis)
  target_sources(boost_core PRIVATE ${boost_core_IDEFILES})

endif()

if(BUILD_TESTING AND EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/test/CMakeLists.txt")

  add_subdirectory(test)

endif()
