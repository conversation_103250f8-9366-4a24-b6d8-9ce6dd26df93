// Modern Continuity Section with Enhanced Glass Morphism
.root {
  display: grid;

  grid-template-columns: repeat(12, 1fr);
  grid-template-rows: repeat(3, 1fr);
  grid-auto-flow: column;

  gap: ui.offset('medium');

  // Smooth entrance animation
  animation: gridFadeIn 1s cubic-bezier(0.16, 1, 0.3, 1);

  // Enhanced container styling
  padding: ui.offset('large');
  background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.04) 0%,
      rgba(255, 255, 255, 0.01) 100%);
  backdrop-filter: blur(8px);
  border: none !important;
  border-radius: ui.border-radius('large');

  // Clean without shadows or borders
  box-shadow: none !important;

  .tilePlay {
    grid-column: span 9;
    grid-row: span 3;
  }

  .tileSupporters,
  .tileFavorites,
  .tileHistory {
    grid-column: span 3;
  }

  &.withLocalhost {
    .tilePlay {
      grid-row: 1 / span 2;
    }

    .tileLocalhost {
      grid-column: span 9;
      grid-row: 3 span 1;
    }
  }

  &.withLast {
    .tilePlay {
      grid-column: span 5;
    }

    .tileLocalhost {
      grid-column: span 5;
      grid-row: 3 span 1;
    }

    .tileSupporters,
    .tileFavorites,
    .tileHistory {
      grid-column: span 3;
    }

    .slotLast {
      grid-column: span 4;
      grid-row: span 3;
    }
  }

  .slotLast {
    padding: 0 ui.offset();
  }

  .tile {
    display: flex;
    align-items: center;

    gap: ui.offset('normal');
    height: ui.q(8);

    padding: ui.offset('normal') calc(ui.offset('normal') * 1.5);

    @include ui.font-size('normal');
    @include ui.font-weight('normal');
    @include ui.font-color('text-a75');

    letter-spacing: 1px;

    // Modern glass morphism tile design
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.1) 0%,
        rgba(255, 255, 255, 0.05) 100%);
    backdrop-filter: blur(12px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: ui.border-radius('medium');

    text-decoration: none;
    cursor: pointer;

    // Enhanced transitions
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    // Subtle glow effect
    box-shadow:
      inset 0 1px 0 rgba(255, 255, 255, 0.2),
      0 4px 16px rgba(0, 0, 0, 0.1);

    // Staggered animation delay for tiles
    &:nth-child(1) {
      animation-delay: 0.1s;
    }

    &:nth-child(2) {
      animation-delay: 0.2s;
    }

    &:nth-child(3) {
      animation-delay: 0.3s;
    }

    &:nth-child(4) {
      animation-delay: 0.4s;
    }

    &:hover {
      // Enhanced hover effect
      background: linear-gradient(135deg,
          rgba(255, 255, 255, 0.15) 0%,
          rgba(255, 255, 255, 0.08) 100%);
      border-color: rgba(102, 126, 234, 0.3);
      box-shadow:
        inset 0 1px 0 rgba(255, 255, 255, 0.3),
        0 8px 32px rgba(0, 0, 0, 0.15),
        0 0 0 1px rgba(102, 126, 234, 0.2);
      transform: translateY(-2px);

      .icon {
        transform: scale(1.2) rotate(5deg);
        color: rgba(102, 126, 234, 0.8);
      }
    }

    .icon {
      display: flex;
      align-items: center;

      @include ui.font-color('text-a50');
    }
  }


  .tilePlay {
    position: relative;

    flex-grow: 1;

    align-items: flex-start;

    height: auto;

    gap: ui.offset('large');
    padding: ui.offset('large') ui.offset('large');

    // Quantum-inspired holographic design for main play button
    background:
      conic-gradient(from 45deg at 30% 70%,
        rgba(102, 126, 234, 0.2) 0deg,
        rgba(240, 147, 251, 0.18) 90deg,
        rgba(168, 85, 247, 0.16) 180deg,
        rgba(79, 172, 254, 0.18) 270deg,
        rgba(102, 126, 234, 0.2) 360deg),
      radial-gradient(ellipse at top left, rgba(102, 126, 234, 0.15) 0%, transparent 60%),
      radial-gradient(ellipse at bottom right, rgba(240, 147, 251, 0.12) 0%, transparent 60%),
      linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.08) 100%);

    backdrop-filter: blur(32px) saturate(1.5) brightness(1.1);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: ui.border-radius('xxlarge');

    // Clean shadow system without colored shadows
    box-shadow:
      0 32px 128px rgba(0, 0, 0, 0.2),
      inset 0 2px 0 rgba(255, 255, 255, 0.4),
      inset 0 -2px 0 rgba(0, 0, 0, 0.1),
      0 0 0 1px rgba(255, 255, 255, 0.1);

    overflow: hidden;

    // Quantum physics-inspired transitions
    transition: all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);

    // Quantum field background pattern
    &::before {
      content: '';
      position: absolute;
      top: -100%;
      left: -100%;
      width: 300%;
      height: 300%;
      background:
        radial-gradient(circle at 25% 25%, rgba(102, 126, 234, 0.05) 2px, transparent 2px),
        radial-gradient(circle at 75% 75%, rgba(240, 147, 251, 0.05) 2px, transparent 2px);
      background-size: 30px 30px, 40px 40px;
      animation: quantumField 25s linear infinite;
      z-index: 0;
    }

    // Clean hover effect without colored shadows
    &:hover {
      transform: translateY(-8px) scale(1.03) rotateX(2deg);
      box-shadow:
        0 48px 192px rgba(0, 0, 0, 0.25),
        inset 0 2px 0 rgba(255, 255, 255, 0.5),
        0 0 0 3px rgba(255, 255, 255, 0.2);

      border: 2px solid rgba(255, 255, 255, 0.4);

      &::before {
        animation-duration: 15s;
      }
    }

    .icon {
      color: ui.color('text-a50');

      @include ui.font-size('xxxlarge');

      z-index: 1;
    }

    .title {
      @include ui.font-size('xxxlarge');
      @include ui.font-weight('bolder');

      span {
        font-weight: 750;
        color: transparent;
        -webkit-background-clip: text;
        background-image: linear-gradient(135deg, ui.color-token('play-button-background-1'), ui.color-token('play-button-background-2'));
      }

      z-index: 1;
    }

    .subtitle {
      @include ui.font-size('normal');
      @include ui.font-weight('normal');
      @include ui.font-color('text-a50');
    }

    @mixin mask-image() {
      $c1: transparent;
      $c2: rgba(0, 0, 0, .25);

      -webkit-mask-image: linear-gradient(-45deg,
          $c1,
          $c1 calc(48%),
          $c2 calc(50% - 2px),
          $c2 calc(50% + 2px),
          $c1 calc(52%),
          $c1 );
      -webkit-mask-size: 300%;
    }

    @keyframes shine {
      0% {
        opacity: 0;
        @include mask-image();
        -webkit-mask-position-x: 100%;
      }

      50% {
        opacity: 1;
        @include mask-image();
        -webkit-mask-position-x: 50%;
      }

      100% {
        opacity: 0;
        @include mask-image();
        -webkit-mask-position-x: 0%;
      }
    }

    &::after {
      display: block;
      content: '';

      position: absolute;
      inset: 0;

      background-color: white;
      opacity: 0;

      z-index: 1;
    }

    &:hover::after {
      animation: shine .5s linear;
    }
  }
}

// Enhanced keyframe animations for modern design
@keyframes gridFadeIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(20px);
  }

  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes patternFloat {
  0% {
    transform: translate(0, 0) rotate(0deg);
  }

  33% {
    transform: translate(10px, -10px) rotate(120deg);
  }

  66% {
    transform: translate(-5px, 5px) rotate(240deg);
  }

  100% {
    transform: translate(0, 0) rotate(360deg);
  }
}

@keyframes tileSlideIn {
  from {
    opacity: 0;
    transform: translateX(-30px) scale(0.9);
  }

  to {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

// Advanced quantum-inspired animations
@keyframes quantumField {
  0% {
    transform: translate(0, 0) rotate(0deg) scale(1);
  }

  25% {
    transform: translate(15px, -10px) rotate(90deg) scale(1.05);
  }

  50% {
    transform: translate(-10px, 15px) rotate(180deg) scale(0.95);
  }

  75% {
    transform: translate(10px, -5px) rotate(270deg) scale(1.02);
  }

  100% {
    transform: translate(0, 0) rotate(360deg) scale(1);
  }
}

@keyframes holographicSpin {
  0% {
    transform: rotate(0deg) scale(1);
    opacity: 0.3;
  }

  50% {
    transform: rotate(180deg) scale(1.1);
    opacity: 0.6;
  }

  100% {
    transform: rotate(360deg) scale(1);
    opacity: 0.3;
  }
}

@keyframes holographicEntrance {
  0% {
    opacity: 0;
    transform: translateY(50px) scale(0.8) rotateX(20deg);
    filter: blur(20px) hue-rotate(0deg);
  }

  50% {
    opacity: 0.8;
    transform: translateY(10px) scale(0.95) rotateX(5deg);
    filter: blur(5px) hue-rotate(180deg);
  }

  100% {
    opacity: 1;
    transform: translateY(0) scale(1) rotateX(0deg);
    filter: blur(0px) hue-rotate(360deg);
  }
}

@keyframes holographicPulse {

  0%,
  100% {
    transform: scale(1);
    filter: brightness(1) saturate(1);
  }

  50% {
    transform: scale(1.02);
    filter: brightness(1.1) saturate(1.2);
  }
}

@keyframes prismaticShift {
  0% {
    filter: hue-rotate(0deg) saturate(1);
  }

  25% {
    filter: hue-rotate(90deg) saturate(1.2);
  }

  50% {
    filter: hue-rotate(180deg) saturate(1.4);
  }

  75% {
    filter: hue-rotate(270deg) saturate(1.2);
  }

  100% {
    filter: hue-rotate(360deg) saturate(1);
  }
}

@keyframes iconGlow {

  0%,
  100% {
    filter: drop-shadow(0 0 8px rgba(102, 126, 234, 0.6));
  }

  50% {
    filter: drop-shadow(0 0 16px rgba(240, 147, 251, 0.8));
  }
}