# Copyright (c) 2013 The Chromium Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//build/config/nacl/config.gni")

static_library("libevent") {
  sources = [
    "buffer.c",
    "evbuffer.c",
    "evdns.c",
    "evdns.h",
    "event-config.h",
    "event-internal.h",
    "event.c",
    "event.h",
    "event_tagging.c",
    "evhttp.h",
    "evrpc-internal.h",
    "evrpc.c",
    "evrpc.h",
    "evsignal.h",
    "evutil.c",
    "evutil.h",
    "http-internal.h",
    "http.c",
    "log.c",
    "log.h",
    "min_heap.h",
    "poll.c",
    "select.c",
    "signal.c",
    "strlcpy-internal.h",
    "strlcpy.c",
  ]

  defines = [ "HAVE_CONFIG_H" ]

  if (is_mac || is_ios) {
    sources += [
      "kqueue.c",
      "mac/config.h",
      "mac/event-config.h",
    ]
    include_dirs = [ "mac" ]
  } else if (is_linux) {
    sources += [
      "epoll.c",
      "linux/config.h",
      "linux/event-config.h",
    ]
    include_dirs = [ "linux" ]
  } else if (is_android) {
    sources += [
      "android/config.h",
      "android/event-config.h",
      "epoll.c",
    ]
    include_dirs = [ "android" ]
  } else if (is_nacl_nonsfi) {
    sources -= [
      "evdns.c",
      "event_tagging.c",
      "evrpc.c",
      "http.c",
      "select.c",
      "signal.c",
    ]
    sources += [
      "nacl_nonsfi/config.h",
      "nacl_nonsfi/event-config.h",
      "nacl_nonsfi/random.c",
      "nacl_nonsfi/signal_stub.c",
    ]
    include_dirs = [ "nacl_nonsfi" ]
  }

  configs -= [ "//build/config/compiler:chromium_code" ]
  configs += [ "//build/config/compiler:no_chromium_code" ]
}
