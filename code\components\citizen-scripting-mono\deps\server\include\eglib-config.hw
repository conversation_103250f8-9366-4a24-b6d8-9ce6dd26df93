#ifndef __EGLIB_CONFIG_H
#define __EGLIB_CONFIG_H

/*
 * System-dependent settings
 */
#define G_OS_WIN32 1

#ifdef _MSC_VER

#include <io.h>

#define G_GNUC_PRETTY_FUNCTION   __FUNCTION__
#define G_GNUC_UNUSED            
#define G_BYTE_ORDER             1234
#define G_GNUC_NORETURN          
#define G_BREAKPOINT()           __debugbreak()
#define MAXPATHLEN 242

typedef uintptr_t gsize;
typedef intptr_t gssize;
typedef int pid_t;

#define G_DIR_SEPARATOR          '\\'
#define G_DIR_SEPARATOR_S        "\\"
#define G_SEARCHPATH_SEPARATOR_S ";"
#define G_SEARCHPATH_SEPARATOR   ';'
#define G_GSIZE_FORMAT   "d"
#define GPOINTER_TO_INT(ptr)   ((gint)(intptr_t) (ptr))
#define GPOINTER_TO_UINT(ptr)  ((guint)(intptr_t) (ptr))
#define GINT_TO_POINTER(v)     ((gpointer)(intptr_t) (v))
#define GUINT_TO_POINTER(v)    ((gpointer)(intptr_t) (v))

#define STDOUT_FILENO (int)(intptr_t)stdout
#define STDERR_FILENO (int)(intptr_t)stderr

/* FIXME: what should this be ?*/
#define X_OK 4 /* This is really read */
#define WNOHANG 1
#define F_SETFD 1
#define FD_CLOEXEC 1

#undef inline
#define inline __inline

#define strtok_r strtok_s

#undef G_HAVE_UNISTD_H
#undef G_HAVE_SYS_TIME_H
#undef G_HAVE_SYS_WAIT_H
#undef G_HAVE_PWD_H
#undef G_HAVE_STRNDUP
#define G_HAVE_GETOPT_H 1

/* disable the following warnings 
 * C4100: The formal parameter is not referenced in the body of the function. The unreferenced parameter is ignored. 
 * C4127: conditional expression is constant
*/
#pragma warning(disable:4100 4127)
#endif

typedef void * GPid;
#endif
