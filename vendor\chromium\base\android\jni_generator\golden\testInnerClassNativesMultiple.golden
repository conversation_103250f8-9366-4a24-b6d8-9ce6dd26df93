// Copyright 2014 The Chromium Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     base/android/jni_generator/jni_generator.py
// For
//     org/chromium/TestJni

#ifndef org_chromium_TestJni_JNI
#define org_chromium_TestJni_JNI

#include <jni.h>

#include "base/android/jni_generator/jni_generator_helper.h"


// Step 1: Forward declarations.

JNI_REGISTRATION_EXPORT extern const char kClassPath_org_chromium_TestJni[];
const char kClassPath_org_chromium_TestJni[] = "org/chromium/TestJni";

JNI_REGISTRATION_EXPORT extern const char kClassPath_org_chromium_TestJni_00024MyInnerClass[];
const char kClassPath_org_chromium_TestJni_00024MyInnerClass[] =
    "org/chromium/TestJni$MyInnerClass";

JNI_REGISTRATION_EXPORT extern const char kClassPath_org_chromium_TestJni_00024MyOtherInnerClass[];
const char kClassPath_org_chromium_TestJni_00024MyOtherInnerClass[] =
    "org/chromium/TestJni$MyOtherInnerClass";
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_REGISTRATION_EXPORT std::atomic<jclass> g_org_chromium_TestJni_clazz(nullptr);
#ifndef org_chromium_TestJni_clazz_defined
#define org_chromium_TestJni_clazz_defined
inline jclass org_chromium_TestJni_clazz(JNIEnv* env) {
  return base::android::LazyGetClass(env, kClassPath_org_chromium_TestJni,
      &g_org_chromium_TestJni_clazz);
}
#endif
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_REGISTRATION_EXPORT std::atomic<jclass> g_org_chromium_TestJni_00024MyInnerClass_clazz(nullptr);
#ifndef org_chromium_TestJni_00024MyInnerClass_clazz_defined
#define org_chromium_TestJni_00024MyInnerClass_clazz_defined
inline jclass org_chromium_TestJni_00024MyInnerClass_clazz(JNIEnv* env) {
  return base::android::LazyGetClass(env, kClassPath_org_chromium_TestJni_00024MyInnerClass,
      &g_org_chromium_TestJni_00024MyInnerClass_clazz);
}
#endif
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_REGISTRATION_EXPORT std::atomic<jclass>
    g_org_chromium_TestJni_00024MyOtherInnerClass_clazz(nullptr);
#ifndef org_chromium_TestJni_00024MyOtherInnerClass_clazz_defined
#define org_chromium_TestJni_00024MyOtherInnerClass_clazz_defined
inline jclass org_chromium_TestJni_00024MyOtherInnerClass_clazz(JNIEnv* env) {
  return base::android::LazyGetClass(env, kClassPath_org_chromium_TestJni_00024MyOtherInnerClass,
      &g_org_chromium_TestJni_00024MyOtherInnerClass_clazz);
}
#endif


// Step 2: Constants (optional).


// Step 3: Method stubs.
static jint JNI_MyInnerClass_Init(JNIEnv* env, const base::android::JavaParamRef<jobject>& jcaller);

JNI_GENERATOR_EXPORT jint Java_org_chromium_TestJni_00024MyInnerClass_nativeInit(
    JNIEnv* env,
    jobject jcaller) {
  return JNI_MyInnerClass_Init(env, base::android::JavaParamRef<jobject>(env, jcaller));
}

static jint JNI_MyOtherInnerClass_Init(JNIEnv* env, const base::android::JavaParamRef<jobject>&
    jcaller);

JNI_GENERATOR_EXPORT jint Java_org_chromium_TestJni_00024MyOtherInnerClass_nativeInit(
    JNIEnv* env,
    jobject jcaller) {
  return JNI_MyOtherInnerClass_Init(env, base::android::JavaParamRef<jobject>(env, jcaller));
}


// Step 4: Generated test functions (optional).


#endif  // org_chromium_TestJni_JNI
