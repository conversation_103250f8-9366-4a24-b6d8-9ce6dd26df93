// Copyright 2014 The Chromium Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     base/android/jni_generator/jni_generator.py
// For
//     org/chromium/example/jni_generator/SampleForAnnotationProcessor

#ifndef org_chromium_example_jni_generator_SampleForAnnotationProcessor_JNI
#define org_chromium_example_jni_generator_SampleForAnnotationProcessor_JNI

#include <jni.h>

#include "base/android/jni_generator/jni_generator_helper.h"


// Step 1: Forward declarations.


// Step 2: Constants (optional).


// Step 3: Method stubs.
static void JNI_SampleForAnnotationProcessor_Foo(JNIEnv* env);

JNI_GENERATOR_EXPORT void Java_J_N_MygCV2jQ(
    JNIEnv* env,
    jclass jcaller) {
  return JNI_SampleForAnnotationProcessor_Foo(env);
}

static base::android::ScopedJavaLocalRef<jobject> JNI_SampleForAnnotationProcessor_Bar(JNIEnv* env,
    const base::android::JavaParamRef<jobject>& sample);

JNI_GENERATOR_EXPORT jobject Java_J_N_MHuqnmXT(
    JNIEnv* env,
    jclass jcaller,
    jobject sample) {
  return JNI_SampleForAnnotationProcessor_Bar(env, base::android::JavaParamRef<jobject>(env,
      sample)).Release();
}

static base::android::ScopedJavaLocalRef<jstring> JNI_SampleForAnnotationProcessor_RevString(JNIEnv*
    env, const base::android::JavaParamRef<jstring>& stringToReverse);

JNI_GENERATOR_EXPORT jstring Java_J_N_MM5Xkwyy(
    JNIEnv* env,
    jclass jcaller,
    jstring stringToReverse) {
  return JNI_SampleForAnnotationProcessor_RevString(env, base::android::JavaParamRef<jstring>(env,
      stringToReverse)).Release();
}

static base::android::ScopedJavaLocalRef<jobjectArray>
    JNI_SampleForAnnotationProcessor_SendToNative(JNIEnv* env, const
    base::android::JavaParamRef<jobjectArray>& strs);

JNI_GENERATOR_EXPORT jobjectArray Java_J_N_MAC2QhR9(
    JNIEnv* env,
    jclass jcaller,
    jobjectArray strs) {
  return JNI_SampleForAnnotationProcessor_SendToNative(env,
      base::android::JavaParamRef<jobjectArray>(env, strs)).Release();
}

static base::android::ScopedJavaLocalRef<jobjectArray>
    JNI_SampleForAnnotationProcessor_SendSamplesToNative(JNIEnv* env, const
    base::android::JavaParamRef<jobjectArray>& strs);

JNI_GENERATOR_EXPORT jobjectArray Java_J_N_MGhRh4Nd(
    JNIEnv* env,
    jclass jcaller,
    jobjectArray strs) {
  return JNI_SampleForAnnotationProcessor_SendSamplesToNative(env,
      base::android::JavaParamRef<jobjectArray>(env, strs)).Release();
}

static jboolean JNI_SampleForAnnotationProcessor_HasPhalange(JNIEnv* env);

JNI_GENERATOR_EXPORT jboolean Java_J_N_MW0aEs4h(
    JNIEnv* env,
    jclass jcaller) {
  return JNI_SampleForAnnotationProcessor_HasPhalange(env);
}

static base::android::ScopedJavaLocalRef<jintArray>
    JNI_SampleForAnnotationProcessor_TestAllPrimitives(JNIEnv* env, jint zint,
    const base::android::JavaParamRef<jintArray>& ints,
    jlong zlong,
    const base::android::JavaParamRef<jlongArray>& longs,
    jshort zshort,
    const base::android::JavaParamRef<jshortArray>& shorts,
    jchar zchar,
    const base::android::JavaParamRef<jcharArray>& chars,
    jbyte zbyte,
    const base::android::JavaParamRef<jbyteArray>& bytes,
    jdouble zdouble,
    const base::android::JavaParamRef<jdoubleArray>& doubles,
    jfloat zfloat,
    const base::android::JavaParamRef<jfloatArray>& floats,
    jboolean zbool,
    const base::android::JavaParamRef<jbooleanArray>& bools);

JNI_GENERATOR_EXPORT jintArray Java_J_N_MlVFI4RX(
    JNIEnv* env,
    jclass jcaller,
    jint zint,
    jintArray ints,
    jlong zlong,
    jlongArray longs,
    jshort zshort,
    jshortArray shorts,
    jchar zchar,
    jcharArray chars,
    jbyte zbyte,
    jbyteArray bytes,
    jdouble zdouble,
    jdoubleArray doubles,
    jfloat zfloat,
    jfloatArray floats,
    jboolean zbool,
    jbooleanArray bools) {
  return JNI_SampleForAnnotationProcessor_TestAllPrimitives(env, zint,
      base::android::JavaParamRef<jintArray>(env, ints), zlong,
      base::android::JavaParamRef<jlongArray>(env, longs), zshort,
      base::android::JavaParamRef<jshortArray>(env, shorts), zchar,
      base::android::JavaParamRef<jcharArray>(env, chars), zbyte,
      base::android::JavaParamRef<jbyteArray>(env, bytes), zdouble,
      base::android::JavaParamRef<jdoubleArray>(env, doubles), zfloat,
      base::android::JavaParamRef<jfloatArray>(env, floats), zbool,
      base::android::JavaParamRef<jbooleanArray>(env, bools)).Release();
}

static void JNI_SampleForAnnotationProcessor_TestSpecialTypes(JNIEnv* env, const
    base::android::JavaParamRef<jclass>& clazz,
    const base::android::JavaParamRef<jobjectArray>& classes,
    const base::android::JavaParamRef<jthrowable>& throwable,
    const base::android::JavaParamRef<jobjectArray>& throwables,
    const base::android::JavaParamRef<jstring>& string,
    const base::android::JavaParamRef<jobjectArray>& strings,
    const base::android::JavaParamRef<jobject>& tStruct,
    const base::android::JavaParamRef<jobjectArray>& structs,
    const base::android::JavaParamRef<jobject>& obj,
    const base::android::JavaParamRef<jobjectArray>& objects);

JNI_GENERATOR_EXPORT void Java_J_N_MqOnlud7(
    JNIEnv* env,
    jclass jcaller,
    jclass clazz,
    jobjectArray classes,
    jthrowable throwable,
    jobjectArray throwables,
    jstring string,
    jobjectArray strings,
    jobject tStruct,
    jobjectArray structs,
    jobject obj,
    jobjectArray objects) {
  return JNI_SampleForAnnotationProcessor_TestSpecialTypes(env,
      base::android::JavaParamRef<jclass>(env, clazz),
      base::android::JavaParamRef<jobjectArray>(env, classes),
      base::android::JavaParamRef<jthrowable>(env, throwable),
      base::android::JavaParamRef<jobjectArray>(env, throwables),
      base::android::JavaParamRef<jstring>(env, string),
      base::android::JavaParamRef<jobjectArray>(env, strings),
      base::android::JavaParamRef<jobject>(env, tStruct),
      base::android::JavaParamRef<jobjectArray>(env, structs),
      base::android::JavaParamRef<jobject>(env, obj), base::android::JavaParamRef<jobjectArray>(env,
      objects));
}

static base::android::ScopedJavaLocalRef<jthrowable>
    JNI_SampleForAnnotationProcessor_ReturnThrowable(JNIEnv* env);

JNI_GENERATOR_EXPORT jthrowable Java_J_N_MuZc76Vt(
    JNIEnv* env,
    jclass jcaller) {
  return JNI_SampleForAnnotationProcessor_ReturnThrowable(env).Release();
}

static base::android::ScopedJavaLocalRef<jobjectArray>
    JNI_SampleForAnnotationProcessor_ReturnThrowables(JNIEnv* env);

JNI_GENERATOR_EXPORT jobjectArray Java_J_N_MAD53J7V(
    JNIEnv* env,
    jclass jcaller) {
  return JNI_SampleForAnnotationProcessor_ReturnThrowables(env).Release();
}

static base::android::ScopedJavaLocalRef<jclass>
    JNI_SampleForAnnotationProcessor_ReturnClass(JNIEnv* env);

JNI_GENERATOR_EXPORT jclass Java_J_N_M_00024ZgOi4g(
    JNIEnv* env,
    jclass jcaller) {
  return JNI_SampleForAnnotationProcessor_ReturnClass(env).Release();
}

static base::android::ScopedJavaLocalRef<jobjectArray>
    JNI_SampleForAnnotationProcessor_ReturnClasses(JNIEnv* env);

JNI_GENERATOR_EXPORT jobjectArray Java_J_N_M_00024hL1577(
    JNIEnv* env,
    jclass jcaller) {
  return JNI_SampleForAnnotationProcessor_ReturnClasses(env).Release();
}

static base::android::ScopedJavaLocalRef<jstring>
    JNI_SampleForAnnotationProcessor_ReturnString(JNIEnv* env);

JNI_GENERATOR_EXPORT jstring Java_J_N_M0k1OjBK(
    JNIEnv* env,
    jclass jcaller) {
  return JNI_SampleForAnnotationProcessor_ReturnString(env).Release();
}

static base::android::ScopedJavaLocalRef<jobjectArray>
    JNI_SampleForAnnotationProcessor_ReturnStrings(JNIEnv* env);

JNI_GENERATOR_EXPORT jobjectArray Java_J_N_Mssfs54E(
    JNIEnv* env,
    jclass jcaller) {
  return JNI_SampleForAnnotationProcessor_ReturnStrings(env).Release();
}

static base::android::ScopedJavaLocalRef<jobject>
    JNI_SampleForAnnotationProcessor_ReturnStruct(JNIEnv* env);

JNI_GENERATOR_EXPORT jobject Java_J_N_MSzcel_1H(
    JNIEnv* env,
    jclass jcaller) {
  return JNI_SampleForAnnotationProcessor_ReturnStruct(env).Release();
}

static base::android::ScopedJavaLocalRef<jobjectArray>
    JNI_SampleForAnnotationProcessor_ReturnStructs(JNIEnv* env);

JNI_GENERATOR_EXPORT jobjectArray Java_J_N_MIBrQLK4(
    JNIEnv* env,
    jclass jcaller) {
  return JNI_SampleForAnnotationProcessor_ReturnStructs(env).Release();
}

static base::android::ScopedJavaLocalRef<jobject>
    JNI_SampleForAnnotationProcessor_ReturnObject(JNIEnv* env);

JNI_GENERATOR_EXPORT jobject Java_J_N_M_00024hPywjv(
    JNIEnv* env,
    jclass jcaller) {
  return JNI_SampleForAnnotationProcessor_ReturnObject(env).Release();
}

static base::android::ScopedJavaLocalRef<jobjectArray>
    JNI_SampleForAnnotationProcessor_ReturnObjects(JNIEnv* env);

JNI_GENERATOR_EXPORT jobjectArray Java_J_N_MPpCU1l5(
    JNIEnv* env,
    jclass jcaller) {
  return JNI_SampleForAnnotationProcessor_ReturnObjects(env).Release();
}


// Step 4: Generated test functions (optional).


#endif  // org_chromium_example_jni_generator_SampleForAnnotationProcessor_JNI
