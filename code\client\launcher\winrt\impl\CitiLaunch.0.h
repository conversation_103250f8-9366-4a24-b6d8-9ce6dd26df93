// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.201201.7

#ifndef WINRT_CitiLaunch_0_H
#define WINRT_CitiLaunch_0_H
WINRT_EXPORT namespace winrt::CitiLaunch
{
    struct IBackdropBrush;
    struct BackdropBrush;
}
namespace winrt::impl
{
    template <> struct category<CitiLaunch::IBackdropBrush>{ using type = interface_category; };
    template <> struct category<CitiLaunch::BackdropBrush>{ using type = class_category; };
    template <> inline constexpr auto& name_v<CitiLaunch::BackdropBrush> = L"CitiLaunch.BackdropBrush";
    template <> inline constexpr auto& name_v<CitiLaunch::IBackdropBrush> = L"CitiLaunch.IBackdropBrush";
    template <> inline constexpr guid guid_v<CitiLaunch::IBackdropBrush>{ 0xB3CC91C9,0xD1CB,0x5350,{ 0xBC,0x5C,0xA0,0x76,0xF5,0xC5,0x0B,0xF7 } }; // B3CC91C9-D1CB-5350-BC5C-A076F5C50BF7
    template <> struct default_interface<CitiLaunch::BackdropBrush>{ using type = CitiLaunch::IBackdropBrush; };
    template <> struct abi<CitiLaunch::IBackdropBrush>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
        };
    };
    template <typename D>
    struct consume_CitiLaunch_IBackdropBrush
    {
    };
    template <> struct consume<CitiLaunch::IBackdropBrush>
    {
        template <typename D> using type = consume_CitiLaunch_IBackdropBrush<D>;
    };
}
#endif
