// Copyright (c) 2012 The Chromium Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#ifndef BASE_TEST_TEST_LISTENER_IOS_H_
#define BASE_TEST_TEST_LISTENER_IOS_H_

namespace base {
namespace test_listener_ios {

// Register an IOSRunLoopListener.
void RegisterTestEndListener();

}  // namespace test_listener_ios
}  // namespace base

#endif  // BASE_TEST_TEST_LISTENER_IOS_H_
