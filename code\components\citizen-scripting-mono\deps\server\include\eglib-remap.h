#define g_array_append monoeg_g_array_append
#define g_array_append_vals monoeg_g_array_append_vals
#define g_array_free monoeg_g_array_free
#define g_array_insert_vals monoeg_g_array_insert_vals
#define g_array_new monoeg_g_array_new
#define g_array_remove_index monoeg_g_array_remove_index
#define g_array_remove_index_fast monoeg_g_array_remove_index_fast
#define g_array_set_size monoeg_g_array_set_size
#define g_array_sized_new monoeg_g_array_sized_new
#define g_ascii_strdown monoeg_g_ascii_strdown
#define g_ascii_strncasecmp monoeg_g_ascii_strncasecmp
#define g_ascii_tolower monoeg_g_ascii_tolower
#define g_ascii_xdigit_value monoeg_g_ascii_xdigit_value
#define g_build_path monoeg_g_build_path
#define g_byte_array_append monoeg_g_byte_array_append
#define g_byte_array_free monoeg_g_byte_array_free
#define g_byte_array_new monoeg_g_byte_array_new
#define g_clear_error monoeg_g_clear_error
#define g_convert monoeg_g_convert
#define g_convert_error_quark monoeg_g_convert_error_quark
#define g_dir_close monoeg_g_dir_close
#define g_dir_open monoeg_g_dir_open
#define g_dir_read_name monoeg_g_dir_read_name
#define g_dir_rewind monoeg_g_dir_rewind
#define g_mkdir_with_parents monoeg_g_mkdir_with_parents
#define g_direct_equal monoeg_g_direct_equal
#define g_direct_hash monoeg_g_direct_hash
#define g_error_free monoeg_g_error_free
#define g_error_new monoeg_g_error_new
#define g_error_vnew monoeg_g_error_vnew
#define g_file_error_quark monoeg_g_file_error_quark
#define g_file_error_from_errno monoeg_g_file_error_from_errno
#define g_file_get_contents monoeg_g_file_get_contents
#define g_file_set_contents monoeg_g_file_set_contents
#define g_file_open_tmp monoeg_g_file_open_tmp
#define g_file_test monoeg_g_file_test
#define g_filename_from_uri monoeg_g_filename_from_uri
#define g_filename_from_utf8 monoeg_g_filename_from_utf8
#define g_filename_to_uri monoeg_g_filename_to_uri
#define g_find_program_in_path monoeg_g_find_program_in_path
#define g_fprintf monoeg_g_fprintf
#define g_free monoeg_g_free
#define g_get_charset monoeg_g_get_charset
#define g_get_current_dir monoeg_g_get_current_dir
#define g_get_current_time monoeg_g_get_current_time
#define g_get_home_dir monoeg_g_get_home_dir
#define g_get_prgname monoeg_g_get_prgname
#define g_get_tmp_dir monoeg_g_get_tmp_dir
#define g_get_user_name monoeg_g_get_user_name
#define g_getenv monoeg_g_getenv
#define g_hash_table_destroy monoeg_g_hash_table_destroy
#define g_hash_table_find monoeg_g_hash_table_find
#define g_hash_table_foreach monoeg_g_hash_table_foreach
#define g_hash_table_foreach_remove monoeg_g_hash_table_foreach_remove
#define g_hash_table_foreach_steal monoeg_g_hash_table_foreach_steal
#define g_hash_table_get_keys monoeg_g_hash_table_get_keys
#define g_hash_table_get_values monoeg_g_hash_table_get_values
#define g_hash_table_insert_replace monoeg_g_hash_table_insert_replace
#define g_hash_table_lookup monoeg_g_hash_table_lookup
#define g_hash_table_lookup_extended monoeg_g_hash_table_lookup_extended
#define g_hash_table_new monoeg_g_hash_table_new
#define g_hash_table_new_full monoeg_g_hash_table_new_full
#define g_hash_table_remove monoeg_g_hash_table_remove
#define g_hash_table_steal monoeg_g_hash_table_steal
#define g_hash_table_size monoeg_g_hash_table_size
#define g_hash_table_print_stats monoeg_g_hash_table_print_stats
#define g_hash_table_remove_all monoeg_g_hash_table_remove_all
#define g_hash_table_iter_init monoeg_g_hash_table_iter_init
#define g_hash_table_iter_next monoeg_g_hash_table_iter_next
#define g_iconv monoeg_g_iconv
#define g_iconv_close monoeg_g_iconv_close
#define g_iconv_open monoeg_g_iconv_open
#define g_int_equal monoeg_g_int_equal
#define g_int_hash monoeg_g_int_hash
#define g_list_alloc monoeg_g_list_alloc
#define g_list_append monoeg_g_list_append
#define g_list_concat monoeg_g_list_concat
#define g_list_copy monoeg_g_list_copy
#define g_list_delete_link monoeg_g_list_delete_link
#define g_list_find monoeg_g_list_find
#define g_list_find_custom monoeg_g_list_find_custom
#define g_list_first monoeg_g_list_first
#define g_list_foreach monoeg_g_list_foreach
#define g_list_free monoeg_g_list_free
#define g_list_free_1 monoeg_g_list_free_1
#define g_list_index monoeg_g_list_index
#define g_list_insert_before monoeg_g_list_insert_before
#define g_list_insert_sorted monoeg_g_list_insert_sorted
#define g_list_last monoeg_g_list_last
#define g_list_length monoeg_g_list_length
#define g_list_nth monoeg_g_list_nth
#define g_list_nth_data monoeg_g_list_nth_data
#define g_list_prepend monoeg_g_list_prepend
#define g_list_remove monoeg_g_list_remove
#define g_list_remove_all monoeg_g_list_remove_all
#define g_list_remove_link monoeg_g_list_remove_link
#define g_list_reverse monoeg_g_list_reverse
#define g_list_sort monoeg_g_list_sort
#define g_locale_from_utf8 monoeg_g_locale_from_utf8
#define g_locale_to_utf8 monoeg_g_locale_to_utf8
#define g_log monoeg_g_log
#define g_log_set_always_fatal monoeg_g_log_set_always_fatal
#define g_log_set_fatal_mask monoeg_g_log_set_fatal_mask
#define g_logv monoeg_g_logv
#define g_markup_parse_context_end_parse monoeg_g_markup_parse_context_end_parse
#define g_markup_parse_context_free monoeg_g_markup_parse_context_free
#define g_markup_parse_context_new monoeg_g_markup_parse_context_new
#define g_markup_parse_context_parse monoeg_g_markup_parse_context_parse
#define g_memdup monoeg_g_memdup
#define g_module_build_path monoeg_g_module_build_path
#define g_module_close monoeg_g_module_close
#define g_module_error monoeg_g_module_error
#define g_module_open monoeg_g_module_open
#define g_module_symbol monoeg_g_module_symbol
#define g_path_get_basename monoeg_g_path_get_basename
#define g_path_get_dirname monoeg_g_path_get_dirname
#define g_path_is_absolute monoeg_g_path_is_absolute
#define g_pattern_match_string monoeg_g_pattern_match_string
#define g_pattern_spec_free monoeg_g_pattern_spec_free
#define g_pattern_spec_new monoeg_g_pattern_spec_new
#define g_print monoeg_g_print
#define g_printf monoeg_g_printf
#define g_printerr monoeg_g_printerr
#define g_propagate_error monoeg_g_propagate_error
#define g_ptr_array_add monoeg_g_ptr_array_add
#define g_ptr_array_foreach monoeg_g_ptr_array_foreach
#define g_ptr_array_free monoeg_g_ptr_array_free
#define g_ptr_array_new monoeg_g_ptr_array_new
#define g_ptr_array_remove monoeg_g_ptr_array_remove
#define g_ptr_array_remove_fast monoeg_g_ptr_array_remove_fast
#define g_ptr_array_remove_index monoeg_g_ptr_array_remove_index
#define g_ptr_array_remove_index_fast monoeg_g_ptr_array_remove_index_fast
#define g_ptr_array_set_size monoeg_g_ptr_array_set_size
#define g_ptr_array_sized_new monoeg_g_ptr_array_sized_new
#define g_ptr_array_sort monoeg_g_ptr_array_sort
#define g_ptr_array_sort_with_data monoeg_g_ptr_array_sort_with_data
#define g_qsort_with_data monoeg_g_qsort_with_data
#define g_queue_free monoeg_g_queue_free
#define g_queue_is_empty monoeg_g_queue_is_empty
#define g_queue_foreach monoeg_g_queue_foreach
#define g_queue_new monoeg_g_queue_new
#define g_queue_pop_head monoeg_g_queue_pop_head
#define g_queue_push_head monoeg_g_queue_push_head
#define g_queue_push_tail monoeg_g_queue_push_tail
#define g_set_error monoeg_g_set_error
#define g_set_prgname monoeg_g_set_prgname
#define g_setenv monoeg_g_setenv
#define g_shell_parse_argv monoeg_g_shell_parse_argv
#define g_shell_quote monoeg_g_shell_quote
#define g_shell_unquote monoeg_g_shell_unquote
#define g_slist_alloc monoeg_g_slist_alloc
#define g_slist_append monoeg_g_slist_append
#define g_slist_concat monoeg_g_slist_concat
#define g_slist_copy monoeg_g_slist_copy
#define g_slist_delete_link monoeg_g_slist_delete_link
#define g_slist_find monoeg_g_slist_find
#define g_slist_find_custom monoeg_g_slist_find_custom
#define g_slist_foreach monoeg_g_slist_foreach
#define g_slist_free monoeg_g_slist_free
#define g_slist_free_1 monoeg_g_slist_free_1
#define g_slist_index monoeg_g_slist_index
#define g_slist_insert_before monoeg_g_slist_insert_before
#define g_slist_insert_sorted monoeg_g_slist_insert_sorted
#define g_slist_last monoeg_g_slist_last
#define g_slist_length monoeg_g_slist_length
#define g_slist_nth monoeg_g_slist_nth
#define g_slist_nth_data monoeg_g_slist_nth_data
#define g_slist_prepend monoeg_g_slist_prepend
#define g_slist_remove monoeg_g_slist_remove
#define g_slist_remove_all monoeg_g_slist_remove_all
#define g_slist_remove_link monoeg_g_slist_remove_link
#define g_slist_reverse monoeg_g_slist_reverse
#define g_slist_sort monoeg_g_slist_sort
#define g_snprintf monoeg_g_snprintf
#define g_spaced_primes_closest monoeg_g_spaced_primes_closest
#define g_spawn_async_with_pipes monoeg_g_spawn_async_with_pipes
#define g_spawn_command_line_sync monoeg_g_spawn_command_line_sync
#define g_sprintf monoeg_g_sprintf
#define g_stpcpy monoeg_g_stpcpy
#define g_str_equal monoeg_g_str_equal
#define g_str_has_prefix monoeg_g_str_has_prefix
#define g_str_has_suffix monoeg_g_str_has_suffix
#define g_str_hash monoeg_g_str_hash
#define g_strchomp monoeg_g_strchomp
#define g_strchug monoeg_g_strchug
#define g_strconcat monoeg_g_strconcat
#define g_strdelimit monoeg_g_strdelimit
#define g_strdown monoeg_g_strdown
#define g_strdup_printf monoeg_g_strdup_printf
#define g_strdup_vprintf monoeg_g_strdup_vprintf
#define g_strerror monoeg_g_strerror
#define g_strescape monoeg_g_strescape
#define g_strfreev monoeg_g_strfreev
#define g_strdupv monoeg_g_strdupv
#define g_string_append monoeg_g_string_append
#define g_string_append_c monoeg_g_string_append_c
#define g_string_append_len monoeg_g_string_append_len
#define g_string_append_unichar monoeg_g_string_append_unichar
#define g_string_append_printf monoeg_g_string_append_printf
#define g_string_append_vprintf monoeg_g_string_append_vprintf
#define g_string_erase monoeg_g_string_erase
#define g_string_free monoeg_g_string_free
#define g_string_insert monoeg_g_string_insert
#define g_string_new monoeg_g_string_new
#define g_string_new_len monoeg_g_string_new_len
#define g_string_prepend monoeg_g_string_prepend
#define g_string_printf monoeg_g_string_printf
#define g_string_set_size monoeg_g_string_set_size
#define g_string_sized_new monoeg_g_string_sized_new
#define g_string_truncate monoeg_g_string_truncate
#define g_strjoin monoeg_g_strjoin
#define g_strjoinv monoeg_g_strjoinv
#define g_strlcpy monoeg_g_strlcpy
#define g_strndup monoeg_g_strndup
#define g_strnfill monoeg_g_strnfill
#define g_strreverse monoeg_g_strreverse
#define g_strsplit monoeg_g_strsplit
#define g_strsplit_set monoeg_g_strsplit_set
#define g_strv_length monoeg_g_strv_length
#define g_timer_destroy monoeg_g_timer_destroy
#define g_timer_elapsed monoeg_g_timer_elapsed
#define g_timer_new monoeg_g_timer_new
#define g_timer_start monoeg_g_timer_start
#define g_timer_stop monoeg_g_timer_stop
#define g_trailingBytesForUTF8 monoeg_g_trailingBytesForUTF8
#define g_ucs4_to_utf8 monoeg_g_ucs4_to_utf8
#define g_ucs4_to_utf16 monoeg_g_ucs4_to_utf16
#define g_unichar_case monoeg_g_unichar_case
#define g_unichar_isxdigit monoeg_g_unichar_isxdigit
#define g_unichar_tolower monoeg_g_unichar_tolower
#define g_unichar_totitle monoeg_g_unichar_totitle
#define g_unichar_toupper monoeg_g_unichar_toupper
#define g_unichar_type monoeg_g_unichar_type
#define g_unichar_xdigit_value monoeg_g_unichar_xdigit_value
#define g_unsetenv monoeg_g_unsetenv
#define g_usleep monoeg_g_usleep
#define g_utf16_to_ucs4 monoeg_g_utf16_to_ucs4
#define g_utf16_to_utf8 monoeg_g_utf16_to_utf8
#define g_utf8_get_char monoeg_g_utf8_get_char
#define g_utf8_offset_to_pointer monoeg_g_utf8_offset_to_pointer
#define g_utf8_pointer_to_offset monoeg_g_utf8_pointer_to_offset
#define g_utf8_strdown monoeg_g_utf8_strdown
#define g_utf8_strlen monoeg_g_utf8_strlen
#define g_utf8_strup monoeg_g_utf8_strup
#define g_utf8_to_ucs4_fast monoeg_g_utf8_to_ucs4_fast
#define g_utf8_to_utf16 monoeg_g_utf8_to_utf16
#define g_utf8_validate monoeg_g_utf8_validate
#define g_unichar_to_utf8 monoeg_g_unichar_to_utf8
#define g_unichar_is_space monoeg_g_unichar_is_space
#define g_unicode_break_type monoeg_g_unicode_break_type
#define g_utf8_offset_to_pointer monoeg_g_utf8_offset_to_pointer
#define g_utf8_pointer_to_offset monoeg_g_utf8_pointer_to_offset
#define g_utf8_to_ucs4_fast monoeg_g_utf8_to_ucs4_fast
#define g_win32_getlocale monoeg_g_win32_getlocale
#define g_assertion_message monoeg_assertion_message
#define g_malloc monoeg_malloc
#define g_malloc0 monoeg_malloc0
#define g_ptr_array_grow monoeg_ptr_array_grow
#define g_realloc monoeg_realloc
#define g_try_malloc monoeg_try_malloc
#define g_try_realloc monoeg_try_realloc
#define g_strdup monoeg_strdup
#define g_ucs4_to_utf16_len monoeg_ucs4_to_utf16_len
#define g_utf16_to_ucs4_len monoeg_utf16_to_ucs4_len

#define g_ascii_strcasecmp monoeg_ascii_strcasecmp
#define g_ascii_strup monoeg_ascii_strup
#define g_ascii_toupper monoeg_ascii_toupper
#define g_unichar_break_type monoeg_unichar_break_type
#define g_unichar_isspace monoeg_unichar_isspace
#define g_unichar_to_utf16 monoeg_unichar_to_utf16
#define g_utf8_find_prev_char monoeg_utf8_find_prev_char
#define g_utf8_get_char_validated monoeg_utf8_get_char_validated
#define g_utf8_prev_char monoeg_utf8_prev_char
#define g_utf8_to_ucs4 monoeg_utf8_to_ucs4


#define g_log_default_handler monoeg_log_default_handler
#define g_log_set_default_handler monoeg_log_set_default_handler
#define g_set_print_handler monoeg_set_print_handler
#define g_set_printerr_handler monoeg_set_printerr_handler
