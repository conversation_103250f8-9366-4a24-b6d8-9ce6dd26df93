# Copyright (c) 2013 The Chromium Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//base/allocator/allocator.gni")
import("//build/buildflag_header.gni")
import("//build/config/allocator.gni")
import("//build/config/compiler/compiler.gni")

declare_args() {
  # Provide a way to force disable debugallocation in Debug builds,
  # e.g. for profiling (it's more rare to profile Debug builds,
  # but people sometimes need to do that).
  enable_debugallocation = is_debug

  # Provide a way to build tcmalloc with a low memory footprint.
  use_tcmalloc_small_but_slow = false
}

# This "allocator" meta-target will forward to the default allocator according
# to the build settings.
group("allocator") {
  public_deps = []
  deps = []

  if (use_allocator == "tcmalloc") {
    deps += [ ":tcmalloc" ]
  }
}

config("tcmalloc_flags") {
  defines = [
    "TCMALLOC_USE_DOUBLYLINKED_FREELIST",
    "TCMALLOC_DISABLE_HUGE_ALLOCATIONS",
  ]
  if (enable_debugallocation) {
    defines += [
      # Use debugallocation for Debug builds to catch problems early
      # and cleanly, http://crbug.com/30715 .
      "TCMALLOC_FOR_DEBUGALLOCATION",
    ]
  }
  if (use_allocator_shim) {
    defines += [ "TCMALLOC_DONT_REPLACE_SYSTEM_ALLOC" ]
  }
  if (use_tcmalloc_small_but_slow) {
    defines += [ "TCMALLOC_SMALL_BUT_SLOW" ]
  }
  if (is_clang) {
    cflags = [
      # tcmalloc initializes some fields in the wrong order.
      "-Wno-reorder",

      # tcmalloc contains some unused local template specializations.
      "-Wno-unused-function",

      # tcmalloc uses COMPILE_ASSERT without static_assert but with typedefs.
      "-Wno-unused-local-typedefs",

      # for magic2_ in debugallocation.cc (only built in Debug builds) typedefs.
      "-Wno-unused-private-field",
    ]
  } else {
    cflags = []
  }

  if (is_linux || is_android) {
    # We enable all warnings by default, but upstream disables a few.
    # Keep "-Wno-*" flags in sync with upstream by comparing against:
    # http://code.google.com/p/google-perftools/source/browse/trunk/Makefile.am
    cflags += [
      "-Wno-sign-compare",
      "-Wno-unused-result",
    ]
  }
}

if (use_allocator == "tcmalloc") {
  # tcmalloc currently won't compile on Android.
  source_set("tcmalloc") {
    tcmalloc_dir = "//third_party/tcmalloc/chromium"

    # Don't check tcmalloc's includes. These files include various files like
    # base/foo.h and they actually refer to tcmalloc's forked copy of base
    # rather than the regular one, which confuses the header checker.
    check_includes = false

    sources = [
      # Generated for our configuration from tcmalloc's build
      # and checked in.
      "$tcmalloc_dir/src/config.h",
      "$tcmalloc_dir/src/config_android.h",
      "$tcmalloc_dir/src/config_linux.h",
      "$tcmalloc_dir/src/config_win.h",

      # tcmalloc native and forked files.
      "$tcmalloc_dir/src/base/abort.cc",
      "$tcmalloc_dir/src/base/abort.h",
      "$tcmalloc_dir/src/base/arm_instruction_set_select.h",
      "$tcmalloc_dir/src/base/atomicops-internals-arm-generic.h",
      "$tcmalloc_dir/src/base/atomicops-internals-arm-v6plus.h",
      "$tcmalloc_dir/src/base/atomicops-internals-linuxppc.h",
      "$tcmalloc_dir/src/base/atomicops-internals-macosx.h",
      "$tcmalloc_dir/src/base/atomicops-internals-windows.h",
      "$tcmalloc_dir/src/base/atomicops-internals-x86.cc",
      "$tcmalloc_dir/src/base/atomicops-internals-x86.h",
      "$tcmalloc_dir/src/base/atomicops.h",
      "$tcmalloc_dir/src/base/commandlineflags.h",

      # We don't list dynamic_annotations.c since its copy is already
      # present in the dynamic_annotations target.
      "$tcmalloc_dir/src/base/elf_mem_image.cc",
      "$tcmalloc_dir/src/base/elf_mem_image.h",
      "$tcmalloc_dir/src/base/linuxthreads.cc",
      "$tcmalloc_dir/src/base/linuxthreads.h",
      "$tcmalloc_dir/src/base/logging.cc",
      "$tcmalloc_dir/src/base/logging.h",
      "$tcmalloc_dir/src/base/low_level_alloc.cc",
      "$tcmalloc_dir/src/base/low_level_alloc.h",
      "$tcmalloc_dir/src/base/spinlock.cc",
      "$tcmalloc_dir/src/base/spinlock.h",
      "$tcmalloc_dir/src/base/spinlock_internal.cc",
      "$tcmalloc_dir/src/base/spinlock_internal.h",
      "$tcmalloc_dir/src/base/sysinfo.cc",
      "$tcmalloc_dir/src/base/sysinfo.h",
      "$tcmalloc_dir/src/base/vdso_support.cc",
      "$tcmalloc_dir/src/base/vdso_support.h",
      "$tcmalloc_dir/src/central_freelist.cc",
      "$tcmalloc_dir/src/central_freelist.h",
      "$tcmalloc_dir/src/common.cc",
      "$tcmalloc_dir/src/common.h",

      # #included by debugallocation_shim.cc
      #"$tcmalloc_dir/src/debugallocation.cc",
      "$tcmalloc_dir/src/fake_stacktrace_scope.cc",
      "$tcmalloc_dir/src/free_list.cc",
      "$tcmalloc_dir/src/free_list.h",
      "$tcmalloc_dir/src/gperftools/heap-profiler.h",
      "$tcmalloc_dir/src/gperftools/malloc_extension.h",
      "$tcmalloc_dir/src/gperftools/malloc_hook.h",
      "$tcmalloc_dir/src/gperftools/stacktrace.h",
      "$tcmalloc_dir/src/internal_logging.cc",
      "$tcmalloc_dir/src/internal_logging.h",
      "$tcmalloc_dir/src/linked_list.h",
      "$tcmalloc_dir/src/malloc_extension.cc",
      "$tcmalloc_dir/src/malloc_hook-inl.h",
      "$tcmalloc_dir/src/malloc_hook.cc",
      "$tcmalloc_dir/src/maybe_emergency_malloc.h",
      "$tcmalloc_dir/src/maybe_threads.cc",
      "$tcmalloc_dir/src/maybe_threads.h",
      "$tcmalloc_dir/src/page_heap.cc",
      "$tcmalloc_dir/src/page_heap.h",
      "$tcmalloc_dir/src/raw_printer.cc",
      "$tcmalloc_dir/src/raw_printer.h",
      "$tcmalloc_dir/src/sampler.cc",
      "$tcmalloc_dir/src/sampler.h",
      "$tcmalloc_dir/src/span.cc",
      "$tcmalloc_dir/src/span.h",
      "$tcmalloc_dir/src/stack_trace_table.cc",
      "$tcmalloc_dir/src/stack_trace_table.h",
      "$tcmalloc_dir/src/stacktrace.cc",
      "$tcmalloc_dir/src/static_vars.cc",
      "$tcmalloc_dir/src/static_vars.h",
      "$tcmalloc_dir/src/symbolize.cc",
      "$tcmalloc_dir/src/symbolize.h",
      "$tcmalloc_dir/src/system-alloc.cc",
      "$tcmalloc_dir/src/system-alloc.h",

      # #included by debugallocation_shim.cc
      #"$tcmalloc_dir/src/tcmalloc.cc",
      #"$tcmalloc_dir/src/tcmalloc.h",
      "$tcmalloc_dir/src/thread_cache.cc",
      "$tcmalloc_dir/src/thread_cache.h",
      "$tcmalloc_dir/src/windows/port.cc",
      "$tcmalloc_dir/src/windows/port.h",
      "debugallocation_shim.cc",

      # These are both #included by allocator_shim for maximal linking.
      #"generic_allocators.cc",
      #"win_allocator.cc",
    ]

    # Not included on mips64el.
    if (current_cpu == "mips64el") {
      sources -= [
        "$tcmalloc_dir/src/base/linuxthreads.cc",
        "$tcmalloc_dir/src/base/linuxthreads.h",
      ]
    }

    # Disable the heap checker in tcmalloc.
    defines = [ "NO_HEAP_CHECK" ]

    include_dirs = [
      ".",
      "$tcmalloc_dir/src/base",
      "$tcmalloc_dir/src",
    ]

    configs -= [ "//build/config/compiler:chromium_code" ]
    configs += [
      "//build/config/compiler:no_chromium_code",
      ":tcmalloc_flags",
    ]

    # Thumb mode disabled due to bug in clang integrated assembler
    # TODO(https://llvm.org/bugs/show_bug.cgi?id=31058)
    configs -= [ "//build/config/compiler:compiler_arm_thumb" ]
    configs += [ "//build/config/compiler:compiler_arm" ]

    # TODO(crbug.com/633719) Make tcmalloc work with AFDO on GCC if possible.
    if (!is_clang) {
      configs -= [ "//build/config/compiler:afdo" ]
    }

    deps = [ ":buildflags" ]

    if (enable_profiling) {
      sources += [
        "$tcmalloc_dir/src/base/thread_lister.c",
        "$tcmalloc_dir/src/base/thread_lister.h",
        "$tcmalloc_dir/src/heap-profile-table.cc",
        "$tcmalloc_dir/src/heap-profile-table.h",
        "$tcmalloc_dir/src/heap-profiler.cc",
        "$tcmalloc_dir/src/memory_region_map.cc",
        "$tcmalloc_dir/src/memory_region_map.h",
        "$tcmalloc_dir/src/profile-handler.cc",
        "$tcmalloc_dir/src/profile-handler.h",
        "$tcmalloc_dir/src/profiledata.cc",
        "$tcmalloc_dir/src/profiledata.h",
        "$tcmalloc_dir/src/profiler.cc",
      ]
      defines += [ "ENABLE_PROFILING=1" ]
    }

    if (is_linux || is_android) {
      sources -= [
        "$tcmalloc_dir/src/system-alloc.h",
        "$tcmalloc_dir/src/windows/port.cc",
        "$tcmalloc_dir/src/windows/port.h",
      ]

      # Compiling tcmalloc with -fvisibility=default is only necessary when
      # not using the allocator shim, which provides the correct visibility
      # annotations for those symbols which need to be exported (see
      # //base/allocator/allocator_shim_override_glibc_weak_symbols.h and
      # //base/allocator/allocator_shim_internals.h for the definition of
      # SHIM_ALWAYS_EXPORT).
      if (!use_allocator_shim) {
        configs -= [ "//build/config/gcc:symbol_visibility_hidden" ]
        configs += [ "//build/config/gcc:symbol_visibility_default" ]
      }

      ldflags = [
        # Don't let linker rip this symbol out, otherwise the heap&cpu
        # profilers will not initialize properly on startup.
        "-Wl,-uIsHeapProfilerRunning,-uProfilerStart",

        # Do the same for heap leak checker.
        "-Wl,-u_Z21InitialMallocHook_NewPKvj,-u_Z22InitialMallocHook_MMapPKvS0_jiiix,-u_Z22InitialMallocHook_SbrkPKvi",
        "-Wl,-u_Z21InitialMallocHook_NewPKvm,-u_Z22InitialMallocHook_MMapPKvS0_miiil,-u_Z22InitialMallocHook_SbrkPKvl",
        "-Wl,-u_ZN15HeapLeakChecker12IgnoreObjectEPKv,-u_ZN15HeapLeakChecker14UnIgnoreObjectEPKv",
      ]
    }

    # Make sure the allocation library is optimized as much as possible when
    # we"re in release mode.
    if (!is_debug) {
      configs -= [ "//build/config/compiler:default_optimization" ]
      configs += [ "//build/config/compiler:optimize_max" ]
    }

    deps += [ "//base/third_party/dynamic_annotations" ]
  }
}  # use_allocator == "tcmalloc"

buildflag_header("buildflags") {
  header = "buildflags.h"
  flags = [ "USE_ALLOCATOR_SHIM=$use_allocator_shim" ]
  if (use_allocator == "tcmalloc") {
    flags += [ "USE_TCMALLOC=1" ]
  } else {
    flags += [ "USE_TCMALLOC=0" ]
  }
}

# Used to shim malloc symbols on Android. see //base/allocator/README.md.
config("wrap_malloc_symbols") {
  ldflags = [
    "-Wl,-wrap,calloc",
    "-Wl,-wrap,free",
    "-Wl,-wrap,malloc",
    "-Wl,-wrap,memalign",
    "-Wl,-wrap,posix_memalign",
    "-Wl,-wrap,pvalloc",
    "-Wl,-wrap,realloc",
    "-Wl,-wrap,valloc",
  ]
}
