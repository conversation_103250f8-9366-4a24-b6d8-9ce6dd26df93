# Copyright 2018, 2019, 2021, 2022 <PERSON> Di<PERSON>v
# Distributed under the Boost Software License, Version 1.0.
# See accompanying file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt

include(BoostTestJamfile OPTIONAL RESULT_VARIABLE HAVE_BOOST_TEST)

if(HAVE_BOOST_TEST)

boost_test_jamfile(FILE Jamfile.v2
  LINK_LIBRARIES Boost::container_hash Boost::core Boost::utility Boost::unordered)

endif()
