Logic, part of collection of the [Boost C++ Libraries](http://github.com/boostorg), provides `boost::logic::tribool` for 3-state boolean logic.

### License

Distributed under the [Boost Software License, Version 1.0](http://www.boost.org/LICENSE_1_0.txt).

### Properties

* C++03
* Header Only

### Build Status

Branch          | GHA CI | Appveyor | Coverity Scan | codecov.io | Deps | Docs | Tests |
:-------------: | ------ | -------- | ------------- | ---------- | ---- | ---- | ----- |
[`master`](https://github.com/boostorg/logic/tree/master) | [![Build Status](https://github.com/boostorg/logic/actions/workflows/ci.yml/badge.svg?branch=master)](https://github.com/boostorg/logic/actions?query=branch:master) | [![Build status](https://ci.appveyor.com/api/projects/status/a898pj8spmo2t3x9/branch/master?svg=true)](https://ci.appveyor.com/project/jeking3/logic-vv3ct/branch/master) | [![Coverity Scan Build Status](https://scan.coverity.com/projects/16173/badge.svg)](https://scan.coverity.com/projects/boostorg-logic) | [![codecov](https://codecov.io/gh/boostorg/logic/branch/master/graph/badge.svg)](https://codecov.io/gh/boostorg/logic/branch/master)| [![Deps](https://img.shields.io/badge/deps-master-brightgreen.svg)](https://pdimov.github.io/boostdep-report/master/logic.html) | [![Documentation](https://img.shields.io/badge/docs-master-brightgreen.svg)](http://www.boost.org/doc/libs/master/doc/html/tribool.html) | [![Enter the Matrix](https://img.shields.io/badge/matrix-master-brightgreen.svg)](http://www.boost.org/development/tests/master/developer/logic.html)
[`develop`](https://github.com/boostorg/logic/tree/develop) | [![Build Status](https://github.com/boostorg/logic/actions/workflows/ci.yml/badge.svg?branch=develop)](https://github.com/boostorg/logic/actions?query=branch:develop) | [![Build status](https://ci.appveyor.com/api/projects/status/a898pj8spmo2t3x9/branch/develop?svg=true)](https://ci.appveyor.com/project/jeking3/logic-vv3ct/branch/develop) | [![Coverity Scan Build Status](https://scan.coverity.com/projects/16173/badge.svg)](https://scan.coverity.com/projects/boostorg-logic) | [![codecov](https://codecov.io/gh/boostorg/logic/branch/develop/graph/badge.svg)](https://codecov.io/gh/boostorg/logic/branch/develop) | [![Deps](https://img.shields.io/badge/deps-develop-brightgreen.svg)](https://pdimov.github.io/boostdep-report/develop/logic.html) | [![Documentation](https://img.shields.io/badge/docs-develop-brightgreen.svg)](http://www.boost.org/doc/libs/develop/doc/html/tribool.html) | [![Enter the Matrix](https://img.shields.io/badge/matrix-develop-brightgreen.svg)](http://www.boost.org/development/tests/develop/developer/logic.html)

### Directories

| Name        | Purpose                        |
| ----------- | ------------------------------ |
| `doc`       | documentation                  |
| `example`   | examples                       |
| `include`   | headers                        |
| `test`      | unit tests                     |

### More information

* [Ask questions](http://stackoverflow.com/questions/ask?tags=c%2B%2B,boost,boost-logic)
* [Report bugs](https://github.com/boostorg/logic/issues): Be sure to mention Boost version, platform and compiler you're using. A small compilable code sample to reproduce the problem is always good as well.
* Submit your patches as pull requests against **develop** branch. Note that by submitting patches you agree to license your modifications under the [Boost Software License, Version 1.0](http://www.boost.org/LICENSE_1_0.txt).
* Discussions about the library are held on the [Boost developers mailing list](http://www.boost.org/community/groups.html#main). Be sure to read the [discussion policy](http://www.boost.org/community/policy.html) before posting and add the `[logic]` tag at the beginning of the subject line.

