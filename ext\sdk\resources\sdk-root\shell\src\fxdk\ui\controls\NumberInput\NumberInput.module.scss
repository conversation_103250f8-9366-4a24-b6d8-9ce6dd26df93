@import "variables";

.root {
  position: relative;

  font-size: $fs08;

  .label {
    position: absolute;

    top: $q;
    left: $q;

    color: rgba($fgColor, .75);

    z-index: 1;

    user-select: none;
    pointer-events: none;
  }

  input {
    width: 100%;

    padding: $q*0.5;

    border: none;
    border-radius: 2px;

    @include fontPrimary;
    font-size: $fs08;
    font-weight: 100;

    text-align: right;

    color: $fgColor;
    background-color: rgba($fgColor, .15);

    cursor: ew-resize;

    @include interactiveTransition;

    &:hover {
      box-shadow: 0 0 0 1px rgba($fgColor, .5) inset;
    }
    &:focus {
      box-shadow: 0 0 0 1px $acColor inset;
    }
  }
}
