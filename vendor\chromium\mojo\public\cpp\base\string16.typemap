# Copyright 2016 The Chromium Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

mojom = "//mojo/public/mojom/base/string16.mojom"
public_headers = [ "//base/strings/string16.h" ]
traits_headers = [ "//mojo/public/cpp/base/string16_mojom_traits.h" ]
sources = [
  "//mojo/public/cpp/base/string16_mojom_traits.cc",
  "//mojo/public/cpp/base/string16_mojom_traits.h",
]
public_deps = [
  "//mojo/public/cpp/base:shared_typemap_traits",
]

type_mappings = [
  "mojo_base.mojom.BigString16=::base::string16",
  "mojo_base.mojom.String16=::base::string16",
]
