---
ns: CFX
apiset: client
game: gta5
---
## REGISTER_STREAMING_FILE_FROM_KVS

```c
void REGISTER_STREAMING_FILE_FROM_KVS(char* kvsKey);
```

**Experimental**: This native may be altered or removed in future versions of CitizenFX without warning.

Registers a KVP value as an asset with the GTA streaming module system. This function currently won't work.

## Parameters
* **kvsKey**: The KVP key in the current resource to register as an asset.