[/
  Copyright 2014 <PERSON>

  Distributed under the Boost Software License, Version 1.0.

  See accompanying file LICENSE_1_0.txt
  or copy at http://boost.org/LICENSE_1_0.txt
]

[section:is_same is_same]

[simplesect Authors]

* <PERSON>

[endsimplesect]

[section Header <boost/core/is_same.hpp>]

[warning This component is deprecated and will be removed in a future release.
Users are recommended to use `boost::is_same` from
[@http://www.boost.org/doc/libs/release/libs/type_traits/doc/html/index.html Boost.TypeTraits]
or `std::is_same` from C++ standard library `<type_traits>` instead.]

The header `<boost/core/is_same.hpp>` defines the class template
`boost::core::is_same<T1,T2>`. It defines a nested integral constant
`value` which is `true` when `T1` and `T2` are the same type, and
`false` when they are not.

In tandem with `BOOST_TEST_TRAIT_TRUE` and `BOOST_TEST_TRAIT_FALSE`,
`is_same` is useful for writing tests for traits classes that have
to define specific nested types.

[section Synopsis]

``
namespace boost
{

namespace core
{
    template<class T1, class T2> struct is_same;
}

}
``

[endsect]

[section Example]

``
#include <boost/core/lightweight_test_trait.hpp>
#include <boost/core/is_same.hpp>

template<class T> struct X
{
    typedef T& type;
};

using boost::core::is_same;

int main()
{
    BOOST_TEST_TRAIT_TRUE(( is_same<X<int>::type, int&> ));
    return boost::report_errors();
}
``

[endsect]

[endsect]

[endsect]
