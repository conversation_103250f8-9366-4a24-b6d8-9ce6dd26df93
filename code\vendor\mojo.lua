local old_files = files

local prj_root = path.getabsolute('../')

function process_includedirs_mojo(loc, list)
	local r = ""

	for _, v in ipairs(list) do
		r = r .. ' -I "' .. path.getrelative(loc, v) .. '"'
	end

	return r
end

local cache = {
}

function gen_typemap_args(path)
	if cache[path] then return cache[path] end
	
	local r, e = os.outputof(table.concat({
		pythonExecutable,
		'"' .. prj_root .. '/../vendor/chromium/mojo/public/tools/bindings/format_typemap_generator_args.py"',
		path
	}, ' '))

	-- escape any arguments with < or >
	r = r:gsub('[<>]', function(a) return '^' .. a end)
	r = r:gsub('type_mappings=(.*)$', function(a)
		if not a:match('const') then
			return 'type_mappings=' .. a
		end

		return '"type_mappings=' .. a .. '"'
	end)

	cache[path] = r
	
	return r
end

local typemaps = {}
local old_project = project

function project(x)
	local p = old_project(x)

	if x then
		typemaps = {}
	end

	return p
end

function files(x)
	old_files(x)
	
	local added = false

	if type(x) == 'table' then
		for _, v in ipairs(x) do
			if v:endswith('.mojom') or v:endswith('.typemap') then
				if v:endswith('.typemap') then
					local m = os.matchfiles(v)
					for key, value in pairs(m) do
						table.insert(typemaps, path.getbasename(value))
					end
				end

				if not added and not v:endswith('.typemap') then
					includedirs { '%{cfg and (cfg.buildtarget.directory .. "/gen/") or ""}', '%{cfg and (cfg.buildtarget.directory .. "/gen/vendor/chromium/") or ""}', '%{cfg and (cfg.buildtarget.directory .. "/code/") or ""}' }
					
					filter 'files:**.typemap'
					
					buildcommands {
						'echo TYPEMAP %{file.name}',
						table.concat({
							pythonExecutable,
							'"' .. prj_root .. '/../vendor/chromium/mojo/public/tools/bindings/generate_type_mappings.py"',
							'%{gen_typemap_args(file.abspath)}',
							'--output',
							'%{cfg.buildtarget.directory .. "/gen/typemap_" .. prj.name .. "_" .. file.basename}',
						}, ' '),
					}

					buildoutputs { '%{cfg.buildtarget.directory .. "/gen/typemap_" .. prj.name .. "_" .. file.basename}' }

					filter 'files:**.mojom'
					
					buildcommands {
						'echo MOJOP %{file.name}',
						table.concat({
							pythonExecutable,
							'"' .. prj_root .. '/../vendor/chromium/mojo/public/tools/bindings/mojom_bindings_generator.py"',
							'parse',
							'-o',
							'%{path.getrelative(".", cfg.buildtarget.directory .. "/gen/")}',
							'--enable_feature',
							'file_path_is_string16',
							'-d',
							'%{path.getrelative(".", "' .. prj_root .. '/../")}',
							'%{(file and (file.abspath) or ""):remove_null()}'
						}, ' '),
					}

					buildoutputs { '%{cfg.buildtarget.directory .. "/gen/" .. path.getrelative("' .. prj_root .. '/../", file.abspath):gsub(".mojom$", "")}.p' }
					
					filter 'files:**.p'
					
					local function gen_cmd(arg)
						local typemapArgs = {}

						for _, value in ipairs(typemaps) do
							table.insert(typemapArgs, '--typemap')
							table.insert(typemapArgs, '%{cfg.buildtarget.directory .. "/gen/typemap_" .. prj.name .. "_' .. value .. '"}')
						end

						return table.concat({
							pythonExecutable,
							'"' .. prj_root .. '/../vendor/chromium/mojo/public/tools/bindings/mojom_bindings_generator.py"',
							'generate',
							'-g',
							'c++',
							'--bytecode_path',
							'%{path.getrelative(".", cfg.buildtarget.directory .. "/gen/")}',
							'--gen_dir',
							'%{path.getrelative(".", cfg.buildtarget.directory .. "/gen/")}',
							'-d',
							'%{path.getrelative(".", "' .. prj_root .. '/../")}',
							table.concat(typemapArgs, ' '),
							'-o',
							'%{path.getrelative(".", cfg.buildtarget.directory .. "/gen/")}',
							arg or '',
							'%{process_includedirs_mojo(prj.location, prj.includedirs):remove_null()}',
							-- path reorder hell! we want to change from bin/five/debug/gen/* to ../../../*
							'%{(file and (path.getrelative(prj.location, path.getabsolute(path.getrelative(cfg.buildtarget.directory .. "/gen/", file.abspath:gsub(".p$", ".mojom")), "' .. prj_root .. '/../"))) or ""):remove_null()}'
						}, ' ')
					end
					
					buildcommands {
						'echo MOJOM %{file.name:gsub(".p$", ".mojom")}',
						gen_cmd(),
						gen_cmd('--generate_non_variant_code'),
						gen_cmd('--generate_non_variant_code --generate_message_ids')
					}
					
					for _, val in ipairs(typemaps) do
						buildinputs { '%{cfg.buildtarget.directory .. "/gen/typemap_" .. prj.name .. "_' .. val .. '"}' }
					end

					buildoutputs { '%{file.abspath:gsub(".p$", ".mojom")}.cc' }

					filter {}
					
					added = true
				end
				
				if v:endswith('.mojom') then
					old_files {
						'%{cfg.buildtarget.directory .. "/gen/"}' .. path.getrelative(prj_root .. '/../', path.getabsolute('.', v)):gsub('.mojom$', '') .. '.p',
						'%{cfg.buildtarget.directory .. "/gen/"}' .. path.getrelative(prj_root .. '/../', path.getabsolute('.', v)) .. '.cc',
						'%{cfg.buildtarget.directory .. "/gen/"}' .. path.getrelative(prj_root .. '/../', path.getabsolute('.', v)) .. '-shared.cc'
					}
				end
			end
		end
	end
end

return {
	include = function()
		includedirs {
			"../vendor/chromium/",
		}
		
		links { 'powrprof', 'setupapi', 'shlwapi', 'propsys', 'dbghelp', 'wbemuuid', 'version' }
	end,
	
	run = function()
		language 'C++'
		kind 'SharedLib'
		dependson 'CfxPrebuild' -- for py dependencies
		
		links { 'ws2_32', 'userenv', 'delayimp' }
		
		prebuildcommands {
			'{MKDIR} %{path.getrelative(".", cfg.buildtarget.directory .. "/gen/")}',
			table.concat({
				pythonExecutable,
				'"' .. prj_root .. '/../vendor/chromium/mojo/public/tools/bindings/mojom_bindings_generator.py"',
				'precompile',
				'-o',
				'%{path.getrelative(".", cfg.buildtarget.directory .. "/gen/")}',
			}, ' '),
		}
		
		if os.istarget('windows') then
			buildoptions { '/wd4251', '/wd4275', '/wd4067', '/wd4267', '/wd4996', '/MP' }
			linkoptions { '/DELAYLOAD:powrprof.dll' }
			defines { "NOMINMAX", "WIN32_LEAN_AND_MEAN", "DCHECK_ALWAYS_ON", "COMPONENT_BUILD", "IS_MOJO_CPP_PLATFORM_IMPL=1", "IS_MOJO_CPP_BINDINGS_IMPL=1", "IS_MOJO_CPP_BINDINGS_BASE_IMPL=1", "IS_MOJO_BASE_MOJOM_IMPL=1", "IS_MOJO_BASE_IMPL=1", "IS_MOJO_CORE_EMBEDDER_IMPL=1", "IS_IPC_IMPL=1", "BASE_IMPLEMENTATION=1", "WIN32", "IPC_MESSAGE_SUPPORT_IMPL=1", "MOJO_SYSTEM_IMPL_IMPLEMENTATION=1", "MOJO_CPP_SYSTEM_IMPLEMENTATION=1" }
		end
		
		
		files_project '../vendor/chromium/mojo/public/cpp/base/' {
			"application_state.typemap",
			"big_buffer.typemap",
			"big_string.typemap",
			"file_error.typemap",
			"file_info.typemap",
			"file_path.typemap",
			"file.typemap",
			"generic_pending_receiver.typemap",
			"read_only_buffer.typemap",
			"memory_allocator_dump_cross_process_uid.typemap",
			"memory_pressure_level.typemap",
			"message_pump_type.typemap",
			"process_id.typemap",
			"ref_counted_memory.typemap",
			"shared_memory.typemap",
			"string16.typemap",
			"logfont_win.typemap",
			"text_direction.typemap",
			"thread_priority.typemap",
			"time.typemap",
			"token.typemap",
			"unguessable_token.typemap",
			"values.typemap",
		}
		
		files_project '../vendor/chromium/ipc/' {
			"message.typemap"
		}
		
		files_project '../vendor/chromium/mojo/public/mojom/base/' {
			--"application_state.mojom",
			"big_buffer.mojom",
			"big_string.mojom",
			"file.mojom",
			"file_error.mojom",
			"file_info.mojom",
			"file_path.mojom",
			"generic_pending_receiver.mojom",
			"memory_allocator_dump_cross_process_uid.mojom",
			"memory_pressure_level.mojom",
			"message_pump_type.mojom",
			"process_id.mojom",
			"read_only_buffer.mojom",
			"ref_counted_memory.mojom",
			"shared_memory.mojom",
			"string16.mojom",
			"text_direction.mojom",
			"thread_priority.mojom",
			"time.mojom",
			"token.mojom",
			"unguessable_token.mojom",
			"values.mojom",
		}
		
		files_project '../vendor/chromium/mojo/public/interfaces/bindings/' {
			"interface_control_messages.mojom",
			"native_struct.mojom",
			"pipe_control_messages.mojom",
		}
		
		files_project '../vendor/chromium/ipc/' {
			"ipc.mojom",
			
			"message_mojom_traits.cc",
		
			"ipc_channel.h",
			"ipc_channel_common.cc",
			"ipc_channel_factory.cc",
			"ipc_channel_factory.h",
			"ipc_channel_handle.h",
			"ipc_channel_mojo.cc",
			"ipc_channel_mojo.h",
			"ipc_channel_proxy.cc",
			"ipc_channel_proxy.h",
			"ipc_channel_reader.cc",
			"ipc_channel_reader.h",
			"ipc_listener.h",
			"ipc_logging.cc",
			"ipc_logging.h",
			"ipc_message_macros.h",
			"ipc_message_pipe_reader.cc",
			"ipc_message_pipe_reader.h",
			"ipc_message_start.h",
			"ipc_message_templates.h",
			"ipc_message_templates_impl.h",
			"ipc_mojo_bootstrap.cc",
			"ipc_mojo_bootstrap.h",
			"ipc_sender.h",
			"ipc_sync_channel.cc",
			"ipc_sync_channel.h",
			"ipc_sync_message_filter.cc",
			"ipc_sync_message_filter.h",
			"message_filter.cc",
			"message_filter.h",
			"message_filter_router.cc",
			"message_filter_router.h",
			"message_router.cc",
			"message_router.h",
			"message_view.cc",
			
    "handle_attachment_win.cc",
    "handle_attachment_win.h",
    "handle_win.cc",
    "handle_win.h",
    "ipc_message.cc",
    "ipc_message.h",
    "ipc_message_attachment.cc",
    "ipc_message_attachment.h",
    "ipc_message_attachment_set.cc",
    "ipc_message_attachment_set.h",
    "ipc_message_support_export.h",
    "ipc_mojo_handle_attachment.cc",
    "ipc_mojo_handle_attachment.h",
    "ipc_mojo_message_helper.cc",
    "ipc_mojo_message_helper.h",
    "ipc_platform_file.cc",
    "ipc_platform_file.h",
    "ipc_sync_message.cc",
    "ipc_sync_message.h",
    --"mach_port_attachment_mac.cc",
    "mach_port_attachment_mac.h",
    --"mach_port_mac.cc",
    "mach_port_mac.h",
    
    "native_handle_type_converters.cc",
    "native_handle_type_converters.h",
		}
		
		files_project '../vendor/chromium/base/' {
			"allocator/allocator_check.cc",
			"allocator/allocator_check.h",
			"allocator/allocator_extension.cc",
			"allocator/allocator_extension.h",
			"at_exit.cc",
			"at_exit.h",
			"atomic_ref_count.h",
			"atomic_sequence_num.h",
			"atomicops.h",
			"atomicops_internals_atomicword_compat.h",
			"atomicops_internals_portable.h",
			"atomicops_internals_x86_msvc.h",
			"auto_reset.h",
			"barrier_closure.cc",
			"barrier_closure.h",
			"base64.cc",
			"base64.h",
			"base64url.cc",
			"base64url.h",
			"base_export.h",
			"base_switches.cc",
			"base_switches.h",
			"big_endian.cc",
			"big_endian.h",
			"bind.h",
			"bind_helpers.h",
			"bind_internal.h",
			"bit_cast.h",
			"bits.h",
			"build_time.cc",
			"build_time.h",
			"callback.h",
			"callback_forward.h",
			"callback_helpers.cc",
			"callback_helpers.h",
			"callback_internal.cc",
			"callback_internal.h",
			"callback_list.h",
			"cancelable_callback.h",
			"command_line.cc",
			"command_line.h",
			"compiler_specific.h",
			"component_export.h",
			"containers/adapters.h",
			"containers/buffer_iterator.h",
			"containers/checked_iterators.h",
			"containers/checked_range.h",
			"containers/circular_deque.h",
			"containers/flat_map.h",
			"containers/flat_set.h",
			"containers/flat_tree.h",
			"containers/id_map.h",
			"containers/intrusive_heap.cc",
			"containers/intrusive_heap.h",
			"containers/linked_list.h",
			"containers/mru_cache.h",
			"containers/small_map.h",
			"containers/span.h",
			"containers/stack.h",
			"containers/stack_container.h",
			"containers/unique_ptr_adapters.h",
			"containers/util.h",
			"containers/vector_buffer.h",
			"cpu.cc",
			"cpu.h",
			"critical_closure.h",
			"debug/activity_analyzer.cc",
			"debug/activity_analyzer.h",
			"debug/activity_tracker.cc",
			"debug/activity_tracker.h",
			"debug/alias.cc",
			"debug/alias.h",
			"debug/asan_invalid_access.cc",
			"debug/asan_invalid_access.h",
			"debug/crash_logging.cc",
			"debug/crash_logging.h",
			"debug/debugger.cc",
			"debug/debugger.h",
			"debug/dump_without_crashing.cc",
			"debug/dump_without_crashing.h",
			"debug/leak_annotations.h",
			"debug/leak_tracker.h",
			"debug/profiler.cc",
			"debug/profiler.h",
			"debug/stack_trace.cc",
			"debug/stack_trace.h",
			"debug/task_trace.cc",
			"debug/task_trace.h",
			"deferred_sequenced_task_runner.cc",
			"deferred_sequenced_task_runner.h",
			"enterprise_util.h",
			"environment.cc",
			"environment.h",
			"export_template.h",
			"feature_list.cc",
			"feature_list.h",
			--"file_descriptor_store.cc",
			"file_descriptor_store.h",
			"file_version_info.h",
			"files/dir_reader_fallback.h",
			"files/file.cc",
			"files/file.h",
			"files/file_enumerator.cc",
			"files/file_enumerator.h",
			"files/file_path.cc",
			"files/file_path.h",
			"files/file_path_constants.cc",
			"files/file_path_watcher.cc",
			"files/file_path_watcher.h",
			"files/file_proxy.cc",
			"files/file_proxy.h",
			"files/file_tracing.cc",
			"files/file_tracing.h",
			"files/file_util.cc",
			"files/file_util.h",
			"files/important_file_writer.cc",
			"files/important_file_writer.h",
			"files/memory_mapped_file.cc",
			"files/memory_mapped_file.h",
			"files/platform_file.h",
			"files/scoped_file.cc",
			"files/scoped_file.h",
			"files/scoped_temp_dir.cc",
			"files/scoped_temp_dir.h",
			"format_macros.h",
			"gtest_prod_util.h",
			"guid.cc",
			"guid.h",
			"hash/hash.cc",
			"hash/hash.h",
			"hash/md5_nacl.cc",
			"hash/sha1.cc",
			"immediate_crash.h",
			"json/json_common.h",
			"json/json_file_value_serializer.cc",
			"json/json_file_value_serializer.h",
			"json/json_parser.cc",
			"json/json_parser.h",
			"json/json_reader.cc",
			"json/json_reader.h",
			"json/json_string_value_serializer.cc",
			"json/json_string_value_serializer.h",
			"json/json_value_converter.cc",
			"json/json_value_converter.h",
			"json/json_writer.cc",
			"json/json_writer.h",
			"json/string_escape.cc",
			"json/string_escape.h",
			"lazy_instance.h",
			"lazy_instance_helpers.cc",
			"lazy_instance_helpers.h",
			--"linux_util.cc",
			"linux_util.h",
			"location.cc",
			"location.h",
			"logging.cc",
			"logging.h",
			"macros.h",
			"memory/aligned_memory.cc",
			"memory/aligned_memory.h",
			"memory/discardable_memory.cc",
			"memory/discardable_memory.h",
			"memory/discardable_memory_allocator.cc",
			"memory/discardable_memory_allocator.h",
			"memory/discardable_memory_internal.h",
			"memory/discardable_shared_memory.cc",
			"memory/discardable_shared_memory.h",
			"memory/free_deleter.h",
			"memory/memory_pressure_listener.cc",
			"memory/memory_pressure_listener.h",
			"memory/memory_pressure_monitor.cc",
			"memory/memory_pressure_monitor.h",
			"memory/platform_shared_memory_region.cc",
			"memory/platform_shared_memory_region.h",
			"memory/ptr_util.h",
			"memory/raw_scoped_refptr_mismatch_checker.h",
			"memory/read_only_shared_memory_region.cc",
			"memory/read_only_shared_memory_region.h",
			"memory/ref_counted.cc",
			"memory/ref_counted.h",
			"memory/ref_counted_delete_on_sequence.h",
			"memory/ref_counted_memory.cc",
			"memory/ref_counted_memory.h",
			"memory/scoped_policy.h",
			"memory/scoped_refptr.h",
			"memory/shared_memory_hooks.h",
			"memory/shared_memory_mapping.cc",
			"memory/shared_memory_mapping.h",
			"memory/shared_memory_tracker.cc",
			"memory/shared_memory_tracker.h",
			"memory/singleton.h",
			"memory/unsafe_shared_memory_region.cc",
			"memory/unsafe_shared_memory_region.h",
			"memory/weak_ptr.cc",
			"memory/weak_ptr.h",
			"memory/writable_shared_memory_region.cc",
			"memory/writable_shared_memory_region.h",
			"message_loop/message_loop.cc",
			"message_loop/message_loop.h",
			"message_loop/message_loop_current.cc",
			"message_loop/message_loop_current.h",
			"message_loop/message_pump.cc",
			"message_loop/message_pump.h",
			"message_loop/message_pump_default.cc",
			"message_loop/message_pump_default.h",
			"message_loop/message_pump_for_io.h",
			"message_loop/message_pump_for_ui.h",
			--"message_loop/message_pump_glib.cc",
			"message_loop/message_pump_glib.h",
			"message_loop/message_pump_type.h",
			"message_loop/timer_slack.h",
			"message_loop/work_id_provider.cc",
			"message_loop/work_id_provider.h",
			"metrics/bucket_ranges.cc",
			"metrics/bucket_ranges.h",
			"metrics/crc32.cc",
			"metrics/crc32.h",
			"metrics/dummy_histogram.cc",
			"metrics/dummy_histogram.h",
			"metrics/field_trial.cc",
			"metrics/field_trial.h",
			"metrics/field_trial_param_associator.cc",
			"metrics/field_trial_param_associator.h",
			"metrics/field_trial_params.cc",
			"metrics/field_trial_params.h",
			"metrics/histogram.cc",
			"metrics/histogram.h",
			"metrics/histogram_base.cc",
			"metrics/histogram_base.h",
			"metrics/histogram_delta_serialization.cc",
			"metrics/histogram_delta_serialization.h",
			"metrics/histogram_flattener.h",
			"metrics/histogram_functions.cc",
			"metrics/histogram_functions.h",
			"metrics/histogram_macros.h",
			"metrics/histogram_macros_internal.h",
			"metrics/histogram_macros_local.h",
			"metrics/histogram_samples.cc",
			"metrics/histogram_samples.h",
			"metrics/histogram_snapshot_manager.cc",
			"metrics/histogram_snapshot_manager.h",
			"metrics/metrics_hashes.cc",
			"metrics/metrics_hashes.h",
			"metrics/persistent_histogram_allocator.cc",
			"metrics/persistent_histogram_allocator.h",
			"metrics/persistent_memory_allocator.cc",
			"metrics/persistent_memory_allocator.h",
			"metrics/persistent_sample_map.cc",
			"metrics/persistent_sample_map.h",
			"metrics/record_histogram_checker.h",
			"metrics/sample_map.cc",
			"metrics/sample_map.h",
			"metrics/sample_vector.cc",
			"metrics/sample_vector.h",
			"metrics/single_sample_metrics.cc",
			"metrics/single_sample_metrics.h",
			"metrics/sparse_histogram.cc",
			"metrics/sparse_histogram.h",
			"metrics/statistics_recorder.cc",
			"metrics/statistics_recorder.h",
			"metrics/ukm_source_id.cc",
			"metrics/ukm_source_id.h",
			"metrics/user_metrics.cc",
			"metrics/user_metrics.h",
			"metrics/user_metrics_action.h",
			"native_library.cc",
			"native_library.h",
			"no_destructor.h",
			"observer_list.h",
			"observer_list_internal.cc",
			"observer_list_internal.h",
			"observer_list_threadsafe.cc",
			"observer_list_threadsafe.h",
			"observer_list_types.cc",
			"observer_list_types.h",
			"one_shot_event.cc",
			"one_shot_event.h",
			"optional.h",
			--"os_compat_nacl.cc",
			"os_compat_nacl.h",
			"parameter_pack.h",
			"path_service.cc",
			"path_service.h",
			"pending_task.cc",
			"pending_task.h",
			"pickle.cc",
			"pickle.h",
			"post_task_and_reply_with_result_internal.h",
			"power_monitor/power_monitor.cc",
			"power_monitor/power_monitor.h",
			"power_monitor/power_monitor_device_source.cc",
			"power_monitor/power_monitor_device_source.h",
			"power_monitor/power_monitor_source.cc",
			"power_monitor/power_monitor_source.h",
			"power_monitor/power_observer.h",
			"process/environment_internal.cc",
			"process/environment_internal.h",
			"process/kill.cc",
			"process/kill.h",
			"process/launch.cc",
			"process/launch.h",
			"process/memory.cc",
			"process/memory.h",
			"process/process.h",
			"process/process_handle.cc",
			"process/process_handle.h",
			"process/process_info.h",
			"process/process_iterator.cc",
			"process/process_iterator.h",
			"process/process_metrics.cc",
			"process/process_metrics.h",
			"profiler/arm_cfi_table.cc",
			"profiler/arm_cfi_table.h",
			"profiler/frame.cc",
			"profiler/frame.h",
			"profiler/metadata_recorder.cc",
			"profiler/metadata_recorder.h",
			"profiler/native_unwinder.h",
			"profiler/profile_builder.cc",
			"profiler/profile_builder.h",
			"profiler/register_context.h",
			"profiler/sample_metadata.cc",
			"profiler/sample_metadata.h",
			"profiler/sampling_profiler_thread_token.cc",
			"profiler/sampling_profiler_thread_token.h",
			"profiler/stack_buffer.cc",
			"profiler/stack_buffer.h",
			"profiler/stack_copier.cc",
			"profiler/stack_copier.h",
			"profiler/stack_copier_suspend.cc",
			"profiler/stack_copier_suspend.h",
			"profiler/stack_sampler.cc",
			"profiler/stack_sampler.h",
			"profiler/stack_sampler_impl.cc",
			"profiler/stack_sampler_impl.h",
			"profiler/stack_sampling_profiler.cc",
			"profiler/stack_sampling_profiler.h",
			"profiler/suspendable_thread_delegate.h",
			"profiler/thread_delegate.h",
			"profiler/unwinder.h",
			"rand_util.cc",
			"rand_util.h",
			--"rand_util_nacl.cc",
			"run_loop.cc",
			"run_loop.h",
			"sampling_heap_profiler/lock_free_address_hash_set.cc",
			"sampling_heap_profiler/lock_free_address_hash_set.h",
			"sampling_heap_profiler/module_cache.cc",
			"sampling_heap_profiler/module_cache.h",
			"sampling_heap_profiler/poisson_allocation_sampler.cc",
			"sampling_heap_profiler/poisson_allocation_sampler.h",
			"sampling_heap_profiler/sampling_heap_profiler.cc",
			"sampling_heap_profiler/sampling_heap_profiler.h",
			"scoped_clear_last_error.h",
			"scoped_generic.h",
			"scoped_native_library.cc",
			"scoped_native_library.h",
			"scoped_observer.h",
			"sequence_checker.h",
			"sequence_checker_impl.cc",
			"sequence_checker_impl.h",
			"sequence_token.cc",
			"sequence_token.h",
			"sequenced_task_runner.cc",
			"sequenced_task_runner.h",
			"sequenced_task_runner_helpers.h",
			"single_thread_task_runner.h",
			"stl_util.h",
			"strings/char_traits.h",
			"strings/latin1_string_conversions.cc",
			"strings/latin1_string_conversions.h",
			"strings/nullable_string16.cc",
			"strings/nullable_string16.h",
			"strings/pattern.cc",
			"strings/pattern.h",
			"strings/safe_sprintf.cc",
			"strings/safe_sprintf.h",
			"strings/strcat.cc",
			"strings/strcat.h",
			--"strings/string16.cc",
			"strings/string16.h",
			"strings/string_number_conversions.cc",
			"strings/string_number_conversions.h",
			
			"third_party/double_conversion/double-conversion/bignum-dtoa.cc",
			"third_party/double_conversion/double-conversion/bignum-dtoa.h",
			"third_party/double_conversion/double-conversion/bignum.cc",
			"third_party/double_conversion/double-conversion/bignum.h",
			"third_party/double_conversion/double-conversion/cached-powers.cc",
			"third_party/double_conversion/double-conversion/cached-powers.h",
			"third_party/double_conversion/double-conversion/diy-fp.h",
			"third_party/double_conversion/double-conversion/double-conversion.h",
			"third_party/double_conversion/double-conversion/double-to-string.cc",
			"third_party/double_conversion/double-conversion/double-to-string.h",
			"third_party/double_conversion/double-conversion/fast-dtoa.cc",
			"third_party/double_conversion/double-conversion/fast-dtoa.h",
			"third_party/double_conversion/double-conversion/fixed-dtoa.cc",
			"third_party/double_conversion/double-conversion/fixed-dtoa.h",
			"third_party/double_conversion/double-conversion/ieee.h",
			"third_party/double_conversion/double-conversion/string-to-double.cc",
			"third_party/double_conversion/double-conversion/string-to-double.h",
			"third_party/double_conversion/double-conversion/strtod.cc",
			"third_party/double_conversion/double-conversion/strtod.h",
			"third_party/double_conversion/double-conversion/utils.h",
			
			"strings/string_piece.cc",
			"strings/string_piece.h",
			"strings/string_piece_forward.h",
			"strings/string_split.cc",
			"strings/string_split.h",
			"strings/string_tokenizer.h",
			"strings/string_util.cc",
			"strings/string_util.h",
			"strings/string_util_constants.cc",
			"strings/stringize_macros.h",
			"strings/stringprintf.cc",
			"strings/stringprintf.h",
			"strings/sys_string_conversions.h",
			"strings/utf_offset_string_conversions.cc",
			"strings/utf_offset_string_conversions.h",
			"strings/utf_string_conversion_utils.cc",
			"strings/utf_string_conversion_utils.h",
			"strings/utf_string_conversions.cc",
			"strings/utf_string_conversions.h",
			"supports_user_data.cc",
			"supports_user_data.h",
			"sync_socket.h",
			"synchronization/atomic_flag.cc",
			"synchronization/atomic_flag.h",
			"synchronization/condition_variable.h",
			"synchronization/lock.cc",
			"synchronization/lock.h",
			"synchronization/lock_impl.h",
			"synchronization/waitable_event.h",
			"synchronization/waitable_event_watcher.h",
			"sys_byteorder.h",
			"syslog_logging.cc",
			"syslog_logging.h",
			"system/sys_info.cc",
			"system/sys_info.h",
			"system/sys_info_internal.h",
			"system/system_monitor.cc",
			"system/system_monitor.h",
			"task/cancelable_task_tracker.cc",
			"task/cancelable_task_tracker.h",
			"task/common/checked_lock.h",
			"task/common/checked_lock_impl.cc",
			"task/common/checked_lock_impl.h",
			"task/common/intrusive_heap.h",
			"task/common/operations_controller.cc",
			"task/common/operations_controller.h",
			"task/common/scoped_defer_task_posting.cc",
			"task/common/scoped_defer_task_posting.h",
			"task/common/task_annotator.cc",
			"task/common/task_annotator.h",
			"task/lazy_task_runner.cc",
			"task/lazy_task_runner.h",
			"task/post_job.cc",
			"task/post_job.h",
			"task/post_task.cc",
			"task/post_task.h",
			"task/scoped_set_task_priority_for_current_thread.cc",
			"task/scoped_set_task_priority_for_current_thread.h",
			"task/sequence_manager/associated_thread_id.cc",
			"task/sequence_manager/associated_thread_id.h",
			"task/sequence_manager/atomic_flag_set.cc",
			"task/sequence_manager/atomic_flag_set.h",
			"task/sequence_manager/enqueue_order.h",
			"task/sequence_manager/enqueue_order_generator.cc",
			"task/sequence_manager/enqueue_order_generator.h",
			"task/sequence_manager/lazily_deallocated_deque.h",
			"task/sequence_manager/lazy_now.cc",
			"task/sequence_manager/lazy_now.h",
			"task/sequence_manager/real_time_domain.cc",
			"task/sequence_manager/real_time_domain.h",
			"task/sequence_manager/sequence_manager.cc",
			"task/sequence_manager/sequence_manager.h",
			"task/sequence_manager/sequence_manager_impl.cc",
			"task/sequence_manager/sequence_manager_impl.h",
			"task/sequence_manager/sequenced_task_source.h",
			"task/sequence_manager/task_queue.cc",
			"task/sequence_manager/task_queue.h",
			"task/sequence_manager/task_queue_impl.cc",
			"task/sequence_manager/task_queue_impl.h",
			"task/sequence_manager/task_queue_selector.cc",
			"task/sequence_manager/task_queue_selector.h",
			"task/sequence_manager/task_queue_selector_logic.h",
			"task/sequence_manager/task_time_observer.h",
			"task/sequence_manager/tasks.cc",
			"task/sequence_manager/tasks.h",
			"task/sequence_manager/thread_controller.h",
			"task/sequence_manager/thread_controller_impl.cc",
			"task/sequence_manager/thread_controller_impl.h",
			"task/sequence_manager/thread_controller_with_message_pump_impl.cc",
			"task/sequence_manager/thread_controller_with_message_pump_impl.h",
			"task/sequence_manager/time_domain.cc",
			"task/sequence_manager/time_domain.h",
			"task/sequence_manager/work_deduplicator.cc",
			"task/sequence_manager/work_deduplicator.h",
			"task/sequence_manager/work_queue.cc",
			"task/sequence_manager/work_queue.h",
			"task/sequence_manager/work_queue_sets.cc",
			"task/sequence_manager/work_queue_sets.h",
			"task/simple_task_executor.cc",
			"task/simple_task_executor.h",
			"task/single_thread_task_executor.cc",
			"task/single_thread_task_executor.h",
			"task/single_thread_task_runner_thread_mode.h",
			"task/task_executor.cc",
			"task/task_executor.h",
			"task/task_features.cc",
			"task/task_features.h",
			"task/task_observer.h",
			"task/task_traits.cc",
			"task/task_traits.h",
			"task/task_traits_extension.h",
			"task/thread_pool/delayed_task_manager.cc",
			"task/thread_pool/delayed_task_manager.h",
			"task/thread_pool/environment_config.cc",
			"task/thread_pool/environment_config.h",
			"task/thread_pool/initialization_util.cc",
			"task/thread_pool/initialization_util.h",
			"task/thread_pool/job_task_source.cc",
			"task/thread_pool/job_task_source.h",
			"task/thread_pool/pooled_parallel_task_runner.cc",
			"task/thread_pool/pooled_parallel_task_runner.h",
			"task/thread_pool/pooled_sequenced_task_runner.cc",
			"task/thread_pool/pooled_sequenced_task_runner.h",
			"task/thread_pool/pooled_single_thread_task_runner_manager.cc",
			"task/thread_pool/pooled_single_thread_task_runner_manager.h",
			"task/thread_pool/pooled_task_runner_delegate.cc",
			"task/thread_pool/pooled_task_runner_delegate.h",
			"task/thread_pool/priority_queue.cc",
			"task/thread_pool/priority_queue.h",
			"task/thread_pool/sequence.cc",
			"task/thread_pool/sequence.h",
			"task/thread_pool/sequence_sort_key.cc",
			"task/thread_pool/sequence_sort_key.h",
			"task/thread_pool/service_thread.cc",
			"task/thread_pool/service_thread.h",
			"task/thread_pool/task.cc",
			"task/thread_pool/task.h",
			"task/thread_pool/task_source.cc",
			"task/thread_pool/task_source.h",
			"task/thread_pool/task_tracker.cc",
			"task/thread_pool/task_tracker.h",
			"task/thread_pool/thread_group.cc",
			"task/thread_pool/thread_group.h",
			"task/thread_pool/thread_group_impl.cc",
			"task/thread_pool/thread_group_impl.h",
			"task/thread_pool/thread_group_native.cc",
			"task/thread_pool/thread_group_native.h",
			"task/thread_pool/thread_pool_impl.cc",
			"task/thread_pool/thread_pool_impl.h",
			"task/thread_pool/thread_pool_instance.cc",
			"task/thread_pool/thread_pool_instance.h",
			"task/thread_pool/tracked_ref.h",
			"task/thread_pool/worker_thread.cc",
			"task/thread_pool/worker_thread.h",
			"task/thread_pool/worker_thread_observer.h",
			"task/thread_pool/worker_thread_stack.cc",
			"task/thread_pool/worker_thread_stack.h",
			"task_runner.cc",
			"task_runner.h",
			"task_runner_util.h",
			"template_util.h",
			"test/malloc_wrapper.h",
			"test/spin_wait.h",
			"third_party/cityhash/city.cc",
			"third_party/cityhash/city.h",
			"third_party/icu/icu_utf.cc",
			"third_party/icu/icu_utf.h",
			"third_party/nspr/prtime.cc",
			"third_party/nspr/prtime.h",
			"third_party/superfasthash/superfasthash.c",
			"thread_annotations.h",
			"threading/platform_thread.cc",
			"threading/platform_thread.h",
			"threading/post_task_and_reply_impl.cc",
			"threading/post_task_and_reply_impl.h",
			"threading/scoped_blocking_call.cc",
			"threading/scoped_blocking_call.h",
			"threading/scoped_thread_priority.cc",
			"threading/scoped_thread_priority.h",
			"threading/sequence_bound.h",
			"threading/sequence_local_storage_map.cc",
			"threading/sequence_local_storage_map.h",
			"threading/sequence_local_storage_slot.cc",
			"threading/sequence_local_storage_slot.h",
			"threading/sequenced_task_runner_handle.cc",
			"threading/sequenced_task_runner_handle.h",
			"threading/simple_thread.cc",
			"threading/simple_thread.h",
			"threading/thread.cc",
			"threading/thread.h",
			"threading/thread_checker.h",
			"threading/thread_checker_impl.cc",
			"threading/thread_checker_impl.h",
			"threading/thread_collision_warner.cc",
			"threading/thread_collision_warner.h",
			"threading/thread_id_name_manager.cc",
			"threading/thread_id_name_manager.h",
			"threading/thread_local.h",
			"threading/thread_local_internal.h",
			"threading/thread_local_storage.cc",
			"threading/thread_local_storage.h",
			"threading/thread_restrictions.cc",
			"threading/thread_restrictions.h",
			"threading/thread_task_runner_handle.cc",
			"threading/thread_task_runner_handle.h",
			"threading/watchdog.cc",
			"threading/watchdog.h",
			"time/clock.cc",
			"time/clock.h",
			"time/default_clock.cc",
			"time/default_clock.h",
			"time/default_tick_clock.cc",
			"time/default_tick_clock.h",
			"time/tick_clock.cc",
			"time/tick_clock.h",
			"time/time.cc",
			"time/time.h",
			"time/time_override.cc",
			"time/time_override.h",
			"time/time_to_iso8601.cc",
			"time/time_to_iso8601.h",
			"timer/elapsed_timer.cc",
			"timer/elapsed_timer.h",
			"timer/hi_res_timer_manager.h",
			"timer/lap_timer.cc",
			"timer/lap_timer.h",
			"timer/timer.cc",
			"timer/timer.h",
			"token.cc",
			"token.h",
			"trace_event/auto_open_close_event.h",
			"trace_event/blame_context.cc",
			"trace_event/blame_context.h",
			"trace_event/builtin_categories.cc",
			"trace_event/builtin_categories.h",
			"trace_event/category_registry.cc",
			"trace_event/category_registry.h",
			"trace_event/common/trace_event_common.h",
			"trace_event/event_name_filter.cc",
			"trace_event/event_name_filter.h",
			"trace_event/heap_profiler.h",
			"trace_event/heap_profiler_allocation_context.cc",
			"trace_event/heap_profiler_allocation_context.h",
			"trace_event/heap_profiler_allocation_context_tracker.cc",
			"trace_event/heap_profiler_allocation_context_tracker.h",
			"trace_event/heap_profiler_event_filter.cc",
			"trace_event/heap_profiler_event_filter.h",
			"trace_event/log_message.cc",
			"trace_event/log_message.h",
			"trace_event/malloc_dump_provider.cc",
			"trace_event/malloc_dump_provider.h",
			"trace_event/memory_allocator_dump.cc",
			"trace_event/memory_allocator_dump.h",
			"trace_event/memory_allocator_dump_guid.cc",
			"trace_event/memory_allocator_dump_guid.h",
			"trace_event/memory_dump_manager.cc",
			"trace_event/memory_dump_manager.h",
			"trace_event/memory_dump_manager_test_utils.h",
			"trace_event/memory_dump_provider.h",
			"trace_event/memory_dump_provider_info.cc",
			"trace_event/memory_dump_provider_info.h",
			"trace_event/memory_dump_request_args.cc",
			"trace_event/memory_dump_request_args.h",
			"trace_event/memory_dump_scheduler.cc",
			"trace_event/memory_dump_scheduler.h",
			"trace_event/memory_infra_background_whitelist.cc",
			"trace_event/memory_infra_background_whitelist.h",
			"trace_event/memory_usage_estimator.cc",
			"trace_event/memory_usage_estimator.h",
			"trace_event/process_memory_dump.cc",
			"trace_event/process_memory_dump.h",
			"trace_event/thread_instruction_count.cc",
			"trace_event/thread_instruction_count.h",
			"trace_event/trace_arguments.cc",
			"trace_event/trace_arguments.h",
			"trace_event/trace_buffer.cc",
			"trace_event/trace_buffer.h",
			"trace_event/trace_category.h",
			"trace_event/trace_config.cc",
			"trace_event/trace_config.h",
			"trace_event/trace_config_category_filter.cc",
			"trace_event/trace_config_category_filter.h",
			"trace_event/trace_event.h",
			"trace_event/trace_event_filter.cc",
			"trace_event/trace_event_filter.h",
			"trace_event/trace_event_impl.cc",
			"trace_event/trace_event_impl.h",
			"trace_event/trace_event_memory_overhead.cc",
			"trace_event/trace_event_memory_overhead.h",
			"trace_event/trace_log.cc",
			"trace_event/trace_log.h",
			"trace_event/trace_log_constants.cc",
			"trace_event/traced_value.cc",
			"trace_event/traced_value.h",
			"trace_event/tracing_agent.cc",
			"trace_event/tracing_agent.h",
			"traits_bag.h",
			"tuple.h",
			"unguessable_token.cc",
			"unguessable_token.h",
			"updateable_sequenced_task_runner.h",
			"value_conversions.cc",
			"value_conversions.h",
			"value_iterators.cc",
			"value_iterators.h",
			"values.cc",
			"values.h",
			"version.cc",
			"version.h",
			"vlog.cc",
			"vlog.h",
			
			"base_paths.cc",
			"base_paths.h",
			"metrics/persistent_histogram_storage.cc",
			"metrics/persistent_histogram_storage.h",
			
			--"allocator/allocator_shim.cc",
			"allocator/partition_allocator/page_allocator.cc",
			"allocator/partition_allocator/address_space_randomization.cc",
			"allocator/partition_allocator/oom_callback.cc",
			"allocator/partition_allocator/spin_lock.cc",
			"allocator/partition_allocator/random.cc",
			"allocator/partition_allocator/memory_reclaimer.cc",
			"allocator/partition_allocator/partition_bucket.cc",
			"allocator/partition_allocator/partition_page.cc",
			"allocator/partition_allocator/partition_oom.cc",
			"allocator/partition_allocator/partition_root_base.cc",
			"allocator/partition_allocator/partition_alloc.cc",
			
			"allocator/allocator_shim_internals.h",
			"allocator/allocator_shim_override_cpp_symbols.h",
			"allocator/allocator_shim_override_libc_symbols.h",
			
			"allocator/allocator_shim.h",
			"allocator/allocator_shim_internals.h",
			
			"../crypto/random.cc",
		}
		
		if os.istarget('windows') then
			files_project '../vendor/chromium/base/' {
				--"debug/close_handle_hook_win.cc",
				"debug/close_handle_hook_win.h",
				"debug/debugger_win.cc",
				"debug/gdi_debug_util_win.cc",
				"debug/gdi_debug_util_win.h",
				--"debug/invalid_access_win.cc",
				"debug/invalid_access_win.h",
				"debug/stack_trace_win.cc",
				"enterprise_util_win.cc",
				"file_version_info_win.cc",
				"file_version_info_win.h",
				"files/file_path_watcher_win.cc",
				"files/file_util_win.cc",
				"files/file_win.cc",
				"files/memory_mapped_file_win.cc",
				"logging_win.cc",
				"logging_win.h",
				"message_loop/message_pump_win.cc",
				"message_loop/message_pump_win.h",
				"native_library_win.cc",
				"process/kill_win.cc",
				"process/launch_win.cc",
				"process/memory_win.cc",
				"process/process_handle_win.cc",
				"process/process_info_win.cc",
				"process/process_iterator_win.cc",
				"process/process_metrics_win.cc",
				"process/process_win.cc",
				"profiler/native_unwinder_win.cc",
				"profiler/native_unwinder_win.h",
				"profiler/stack_sampler_win.cc",
				"profiler/suspendable_thread_delegate_win.cc",
				"profiler/suspendable_thread_delegate_win.h",
				"sampling_heap_profiler/module_cache_win.cc",
				"scoped_clear_last_error_win.cc",
				"strings/string_util_win.h",
				"strings/sys_string_conversions_win.cc",
				"sync_socket_win.cc",
				"synchronization/condition_variable_win.cc",
				"synchronization/lock_impl_win.cc",
				"synchronization/waitable_event_watcher_win.cc",
				"synchronization/waitable_event_win.cc",
				"task/thread_pool/thread_group_native_win.cc",
				"task/thread_pool/thread_group_native_win.h",
				"threading/platform_thread_win.cc",
				"threading/platform_thread_win.h",
				"threading/thread_local_storage_win.cc",
				"timer/hi_res_timer_manager_win.cc",
				"trace_event/trace_event_etw_export_win.cc",
				"trace_event/trace_event_etw_export_win.h",
				"win/async_operation.h",
				"win/atl.h",
				"win/com_init_check_hook.cc",
				"win/com_init_check_hook.h",
				"win/com_init_util.cc",
				"win/com_init_util.h",
				"win/core_winrt_util.cc",
				"win/core_winrt_util.h",
				"win/current_module.h",
				"win/embedded_i18n/language_selector.cc",
				"win/embedded_i18n/language_selector.h",
				"win/enum_variant.cc",
				"win/enum_variant.h",
				"win/event_trace_consumer.h",
				"win/event_trace_controller.cc",
				"win/event_trace_controller.h",
				"win/event_trace_provider.cc",
				"win/event_trace_provider.h",
				"win/hstring_compare.cc",
				"win/hstring_compare.h",
				"win/hstring_reference.cc",
				"win/hstring_reference.h",
				"win/i18n.cc",
				"win/i18n.h",
				"win/iat_patch_function.cc",
				"win/iat_patch_function.h",
				"win/map.h",
				"win/message_window.cc",
				"win/message_window.h",
				"win/object_watcher.cc",
				"win/object_watcher.h",
				"win/patch_util.cc",
				"win/patch_util.h",
				"win/pe_image.cc",
				"win/post_async_results.h",
				"win/process_startup_helper.cc",
				"win/process_startup_helper.h",
				"win/propvarutil.h",
				"win/reference.h",
				"win/registry.cc",
				"win/registry.h",
				"win/resource_util.cc",
				"win/resource_util.h",
				"win/scoped_bstr.cc",
				"win/scoped_bstr.h",
				"win/scoped_co_mem.h",
				"win/scoped_com_initializer.cc",
				"win/scoped_com_initializer.h",
				"win/scoped_gdi_object.h",
				"win/scoped_handle.cc",
				"win/scoped_handle.h",
				"win/scoped_handle_verifier.cc",
				"win/scoped_handle_verifier.h",
				"win/scoped_hdc.h",
				"win/scoped_hglobal.h",
				"win/scoped_hstring.cc",
				"win/scoped_hstring.h",
				"win/scoped_process_information.cc",
				"win/scoped_process_information.h",
				"win/scoped_propvariant.h",
				"win/scoped_safearray.h",
				"win/scoped_select_object.h",
				"win/scoped_variant.cc",
				"win/scoped_variant.h",
				"win/scoped_windows_thread_environment.h",
				"win/scoped_winrt_initializer.cc",
				"win/scoped_winrt_initializer.h",
				"win/shlwapi.h",
				"win/shortcut.cc",
				"win/shortcut.h",
				"win/sphelper.h",
				"win/startup_information.cc",
				"win/startup_information.h",
				"win/typed_event_handler.h",
				"win/vector.cc",
				"win/vector.h",
				"win/win_util.cc",
				"win/win_util.h",
				"win/wincrypt_shim.h",
				"win/windows_defines.inc",
				"win/windows_types.h",
				"win/windows_undefines.inc",
				"win/windows_version.cc",
				"win/windows_version.h",
				"win/windowsx_shim.h",
				"win/winrt_foundation_helpers.h",
				"win/winrt_storage_util.cc",
				"win/winrt_storage_util.h",
				"win/wmi.cc",
				"win/wmi.h",
				"win/wrapped_window_proc.cc",
				"win/wrapped_window_proc.h",
				
				"base_paths_win.cc",
				"base_paths_win.h",
				
				"allocator/allocator_shim_default_dispatch_to_winheap.cc",
				"allocator/allocator_shim_override_ucrt_symbols_win.h",
				"allocator/winheap_stubs_win.cc",
				"allocator/winheap_stubs_win.h",
				
				"files/file_enumerator_win.cc",
				"memory/platform_shared_memory_region_win.cc",
				"power_monitor/power_monitor_device_source_win.cc",
				"profiler/win32_stack_frame_unwinder.cc",
				"profiler/win32_stack_frame_unwinder.h",
				"rand_util_win.cc",
				"system/sys_info_win.cc",
				"time/time_win.cc",
				"time/time_win_features.cc",
				"time/time_win_features.h",
			}
		end
		
		files_project '../vendor/chromium/mojo/core/' {
"channel.h",
"configuration.h",
"connection_params.h",
"core.h",
"data_pipe_consumer_dispatcher.h",
"data_pipe_control_message.h",
"data_pipe_producer_dispatcher.h",
"dispatcher.h",
"embedder/configuration.h",
"embedder/process_error_callback.h",
"entrypoints.h",
"handle_signals_state.h",
"handle_table.h",
"invitation_dispatcher.h",
"message_pipe_dispatcher.h",
"node_controller.h",
"options_validation.h",
"platform_handle_dispatcher.h",
"platform_handle_utils.h",
"platform_shared_memory_mapping.h",
"request_context.h",
"scoped_process_handle.h",
"shared_buffer_dispatcher.h",
"user_message_impl.h",

"atomic_flag.h",
"broker.h",
"broker_win.cc",
"channel.cc",
"channel_win.cc",
"configuration.cc",
"connection_params.cc",
"core.cc",
"data_pipe_consumer_dispatcher.cc",
"data_pipe_control_message.cc",
"data_pipe_producer_dispatcher.cc",
"dispatcher.cc",
"entrypoints.cc",
"handle_table.cc",
"invitation_dispatcher.cc",
"message_pipe_dispatcher.cc",
"node_channel.cc",
"node_channel.h",
"node_controller.cc",
"platform_handle_dispatcher.cc",
"platform_handle_in_transit.cc",
"platform_handle_in_transit.h",
"platform_handle_utils.cc",
"platform_shared_memory_mapping.cc",
"request_context.cc",
"scoped_process_handle.cc",
"shared_buffer_dispatcher.cc",
"user_message_impl.cc",
"watch.cc",
"watch.h",
"watcher_dispatcher.cc",
"watcher_dispatcher.h",
"watcher_set.cc",
"watcher_set.h",

        "broker_host.cc",
        "broker_host.h",
		}
		
		files_project '../vendor/chromium/mojo/core/ports/' {
    "event.cc",
    "event.h",
    "message_filter.h",
    "message_queue.cc",
    "message_queue.h",
    "name.cc",
    "name.h",
    "node.cc",
    "node.h",
    "node_delegate.h",
    "port.cc",
    "port.h",
    "port_locker.cc",
    "port_locker.h",
    "port_ref.cc",
    "port_ref.h",
    "user_data.h",
    "user_message.cc",
    "user_message.h",
		}
		
		files_project '../vendor/chromium/mojo/core/embedder/' {
			"embedder.cc",
			"scoped_ipc_support.cc",
		}
		
		files_project '../vendor/chromium/mojo/public/c/system/' {
			"thunks.cc"
		}
		
		files_project '../vendor/chromium/mojo/public/cpp/system/' {
    "buffer.cc",
    "buffer.h",
    "core.h",
    "data_pipe.cc",
    "data_pipe.h",
    "data_pipe_drainer.cc",
    "data_pipe_drainer.h",
    "data_pipe_producer.cc",
    "data_pipe_producer.h",
    "data_pipe_utils.cc",
    "data_pipe_utils.h",
    "file_data_source.cc",
    "file_data_source.h",
    "filtered_data_source.cc",
    "filtered_data_source.h",
    "functions.h",
    "handle.h",
    "handle_signal_tracker.cc",
    "handle_signal_tracker.h",
    "handle_signals_state.h",
    "invitation.cc",
    "invitation.h",
    "isolated_connection.cc",
    "isolated_connection.h",
    "message.h",
    "message_pipe.cc",
    "message_pipe.h",
    "platform_handle.cc",
    "platform_handle.h",
    "scope_to_message_pipe.cc",
    "scope_to_message_pipe.h",
    "simple_watcher.cc",
    "simple_watcher.h",
    "string_data_source.cc",
    "string_data_source.h",
    "system_export.h",
    "trap.cc",
    "trap.h",
    "wait.cc",
    "wait.h",
    "wait_set.cc",
    "wait_set.h",
		}
		
		files_project '../vendor/chromium/mojo/public/cpp/platform/' {
	"named_platform_channel.cc",
    "named_platform_channel_win.cc",
    "platform_channel.cc",
    "platform_channel_endpoint.cc",
    "platform_channel_server_endpoint.cc",
    "platform_handle.cc",
		}
		
		defines { 'IS_MOJO_BASE_IMPL' }
		
		files_project '../vendor/chromium/mojo/public/cpp/base/' {
		    "big_buffer.cc",
			"big_buffer.h",
			"shared_memory_utils.cc",
			"shared_memory_utils.h",
			
    "big_buffer_mojom_traits.cc",
    "big_buffer_mojom_traits.h",
    "file_info_mojom_traits.cc",
    "file_info_mojom_traits.h",
    "file_mojom_traits.cc",
    "file_mojom_traits.h",
    "file_path_mojom_traits.cc",
    "file_path_mojom_traits.h",
    "generic_pending_receiver_mojom_traits.cc",
    "generic_pending_receiver_mojom_traits.h",
    "read_only_buffer_mojom_traits.cc",
    "read_only_buffer_mojom_traits.h",
    "shared_memory_mojom_traits.cc",
    "shared_memory_mojom_traits.h",
    "time_mojom_traits.cc",
    "time_mojom_traits.h",
    "token_mojom_traits.cc",
    "token_mojom_traits.h",
    "unguessable_token_mojom_traits.cc",
    "unguessable_token_mojom_traits.h",
    "values_mojom_traits.cc",
    "values_mojom_traits.h",
		}
		
		files_project '../vendor/chromium/mojo/public/cpp/bindings/' {
    "array_data_view.h",
    "array_traits.h",
    "array_traits_span.h",
    "array_traits_stl.h",
    "associated_group.h",
    "associated_group_controller.h",
    "clone_traits.h",
    "connection_group.cc",
    "connection_group.h",
    "deprecated_interface_types_forward.h",
    "disconnect_reason.h",
    "enum_traits.h",
    "enum_utils.h",
    "equals_traits.h",
    "features.cc",
    "features.h",
    "interface_data_view.h",
    "interface_id.h",
    "lib/array_internal.cc",
    "lib/array_internal.h",
    "lib/array_serialization.h",
    "lib/associated_group.cc",
    "lib/associated_group_controller.cc",
    "lib/bindings_internal.h",
    "lib/buffer.cc",
    "lib/buffer.h",
    "lib/fixed_buffer.cc",
    "lib/fixed_buffer.h",
    "lib/handle_serialization.h",
    "lib/hash_util.h",
    "lib/map_data_internal.h",
    "lib/map_serialization.h",
    "lib/may_auto_lock.h",
    "lib/message.cc",
    "lib/message_header_validator.cc",
    "lib/message_internal.cc",
    "lib/message_internal.h",
    "lib/pending_receiver_state.cc",
    "lib/pending_receiver_state.h",
    "lib/pending_remote_state.cc",
    "lib/pending_remote_state.h",
    "lib/scoped_interface_endpoint_handle.cc",
    "lib/serialization.h",
    "lib/serialization_context.cc",
    "lib/serialization_context.h",
    "lib/serialization_forward.h",
    "lib/serialization_util.h",
    "lib/string_serialization.h",
    "lib/template_util.h",
    "lib/tracing_helper.h",
    "lib/unserialized_message_context.cc",
    "lib/unserialized_message_context.h",
    "lib/validate_params.h",
    "lib/validation_context.cc",
    "lib/validation_context.h",
    "lib/validation_errors.cc",
    "lib/validation_errors.h",
    "lib/validation_util.cc",
    "lib/validation_util.h",
    "map_data_view.h",
    "map_traits.h",
    "map_traits_flat_map.h",
    "map_traits_stl.h",
    "message.h",
    "message_header_validator.h",
    "scoped_interface_endpoint_handle.h",
    "string_data_view.h",
    "string_traits.h",
    "string_traits_stl.h",
    "string_traits_string_piece.h",
    "struct_forward.h",
    "struct_ptr.h",
    "struct_traits.h",
    "type_converter.h",
    "union_traits.h",

    "associated_binding.h",
    "associated_binding_set.h",
    "associated_interface_ptr.h",
    "associated_interface_ptr_info.h",
    "associated_interface_request.h",
    "associated_receiver.h",
    "associated_receiver_set.h",
    "associated_remote.h",
    "async_flusher.cc",
    "async_flusher.h",
    "binder_map.cc",
    "binder_map.h",
    "binding.h",
    "binding_set.h",
    "callback_helpers.h",
    "connection_error_callback.h",
    "connector.h",
    "generic_pending_receiver.cc",
    "generic_pending_receiver.h",
    "interface_endpoint_client.h",
    "interface_endpoint_controller.h",
    "interface_ptr.h",
    "interface_ptr_info.h",
    "interface_ptr_set.h",
    "interface_request.h",
    "lib/associated_binding.cc",
    "lib/associated_interface_ptr.cc",
    "lib/associated_interface_ptr_state.cc",
    "lib/associated_interface_ptr_state.h",
    "lib/binding_state.cc",
    "lib/binding_state.h",
    "lib/connector.cc",
    "lib/control_message_handler.cc",
    "lib/control_message_handler.h",
    "lib/control_message_proxy.cc",
    "lib/control_message_proxy.h",
    "lib/generated_code_util.cc",
    "lib/generated_code_util.h",
    "lib/interface_endpoint_client.cc",
    "lib/interface_ptr_state.cc",
    "lib/interface_ptr_state.h",
    "lib/interface_serialization.h",
    "lib/message_dispatcher.cc",
    "lib/message_quota_checker.cc",
    "lib/message_quota_checker.h",
    "lib/multiplex_router.cc",
    "lib/multiplex_router.h",
    "lib/native_enum_data.h",
    "lib/native_enum_serialization.h",
    "lib/native_struct_serialization.cc",
    "lib/native_struct_serialization.h",
    "lib/pipe_control_message_handler.cc",
    "lib/pipe_control_message_proxy.cc",
    "lib/sequence_local_sync_event_watcher.cc",
    "lib/sync_call_restrictions.cc",
    "lib/sync_event_watcher.cc",
    "lib/sync_handle_registry.cc",
    "lib/sync_handle_watcher.cc",
    "lib/task_runner_helper.cc",
    "lib/task_runner_helper.h",
    "message_dispatcher.h",
    "native_enum.h",
    "pending_associated_receiver.h",
    "pending_associated_remote.h",
    "pending_flush.cc",
    "pending_flush.h",
    "pending_receiver.h",
    "pending_remote.h",
    "pipe_control_message_handler.h",
    "pipe_control_message_handler_delegate.h",
    "pipe_control_message_proxy.h",
    "raw_ptr_impl_ref_traits.h",
    "receiver.h",
    "receiver_set.h",
    "remote.h",
    "remote_set.h",
    "self_owned_associated_receiver.h",
    "self_owned_receiver.h",
    "sequence_local_sync_event_watcher.h",
    "service_factory.cc",
    "service_factory.h",
    "shared_associated_remote.h",
    "shared_remote.h",
    "strong_associated_binding.h",
    "strong_binding.h",
    "strong_binding_set.h",
    "sync_call_restrictions.h",
    "sync_event_watcher.h",
    "sync_handle_registry.h",
    "sync_handle_watcher.h",
    "thread_safe_forwarder_base.cc",
    "thread_safe_forwarder_base.h",
    "thread_safe_interface_ptr.h",
    "unique_associated_receiver_set.h",
    "unique_ptr_impl_ref_traits.h",
    "unique_receiver_set.h",

		}
	end
}
