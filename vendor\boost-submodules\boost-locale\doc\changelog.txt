//
// Copyright (c) 2009-2011 <PERSON><PERSON><PERSON> (Tonkikh)
// Copyright (c) 2021-2024 <PERSON>
//
// Distributed under the Boost Software License, Version 1.0.
// https://www.boost.org/LICENSE_1_0.txt

/*!
\page changelog Changelog

- 1.85.0
    - Breaking changes
        - `collator` does no longer derive from `std::collator` avoiding possible type confusion
        - `date_time` stores time in second resolution for the ICU backend, where sub-second resolution was added/fixed in Boost 1.81
        - `comparator` in case of errors now throws on construction instead of on use as documented
        - Character encoding conversion using IConv throws a `std::runtime_error` when the used IConv library is detected to be faulty (instead of running into an infinite loop)
- 1.84.0
    - Breaking changes
        - `to_title` for the WinAPI backend returns the string unchanged instead of an empty string
- 1.83.0
    - Breaking changes
        - Bitwise/binary operators (left/right shift, binary and/or/xor/not) are no longer supported in message catalog files matching GNU gettext behavior
        - Std backend on Windows uses the classic locale instead of the system locale when the requested locale does not exist (now same as on other OSs)
        - `localization_backend_manager::get` is deprecated and should be replaced by `localization_backend_manager::create` or simply using the generator
    - Other improvements and fixes
        - Introduce converter classes as alternative to `to_utf`/`from_utf`/`between`
        - Fix UB on invalid index in format strings
        - Fix conversion of UTF to e.g. iso-2022-jp on Windows without ICU
        - Use `long long` as the count parameter for pluralized translations
        - Make `localization_backend_manager` movable
        - Add missing `noexcept` to move assignments/constructors
- 1.82.0
    - Breaking changes
        - `get_system_locale` and dependents will now correctly favor `$LC_ALL` over `LC_CTYPE` as defined by POSIX
        - `utf8_codecvt::out` returns `partial` for trailing (UTF) surrogates
    - Other improvements and fixes
        - Add `boost::locale::util::locale_data` to parse a locale
        - `boost::locale::info::encoding()` result is in uppercase as documented, e.g. "UTF-8" instead of "utf-8"
        - Support M49 country codes such as `en_001` or `en_150`
        - Treat `en_US_POSIX` as an alias for the `C` locale
- 1.81.0
    - Breaking changes
        - Require C++11 or higher
        - Modernize code (C++11 features instead of Boost replacements, consistent formatting)
        - Error on use of `-sICU_LINK_LOCALE` and `-sICU_LINK`, use `-sICU_*_NAME` when `-sICU_PATH` is not enough
        - Convert `character_facet_type` & `locale_category_type` to the enum classes `char_facet_t` & `category_t`
    - Other improvements and fixes
        - Fix build on macOS with iconv
        - Fix int-overflow on negative roll of years in `date_time`
        - Assume and use UTF-16 encoding for Windows `wchar_t-codecvt`
        - Fix rounding issues with calendar time
        - Make `basic_format` movable allowing it to be returned from functions
    	- Fix conversion of e.g. codepage iso-2022-jp on Windows
    	- Add more Windows codepages, e.g. cp1025, and various ISO and IBM codepages
        - Performance improvements
- 1.80.0
    - Deprecated support for C++03 and earlier, C++11 will be required in the next release
    - Provide `-sICU_LINK_LOCALE` as a temporary replacement for `-sICU_LINK` which is incompatible with Boost.Regex.
    - `-sICU_LINK_LOCALE` and `-sICU_LINK` are deprecated and will be replaced by `ICU_*_NAME` options to be compatible with Boost.Regex
    - Fix UB/assertion failure in the ICU `collator` implementation when transforming empty strings
    - Fix some issues related to visibility of classes in shared libraries (Unix only)
    - Fix compatibility with C++20 mode
    - Fix compatibility with `BOOST_USE_WINDOWS_H`
    - Fix build failures due to missing includes
    - Handle or suppress many warnings which makes the build log cleaner
- 1.67.0
    - Added support of unique_ptr interface in addition to C++2003 auto_ptr - in order to support C++2017, now you can use BOOST_LOCALE_HIDE_AUTO_PTR definition to remove auto_ptr from the interfaces and prevent deprecated watnings
    - Fixed test problem with ICU >60.1
    - Fix of solaris build
    - Fixed wired FreeBSD/clang issue on optimized build. Probably compiler workaround
    - Added workaround for failing MSVC tests due to 932 codepage codecvt issue
    - Fixed bugs 6851, 12572, 12453
    - Fixed missing throw in case of failure in icu/date_time
    - Fixed build agains Boost.Thread v4
    - Fixed Year of week instead of year ICU backend formatting
    - Fixed formatting test for ICU 56.1 and above
- 1.60.0
    - Implemented generic codecvt facet and add general purpose utf8_codecvt facet
    - Added posix locale support for FreeBSD 10.0 and above
    - Fixed issues 10017 (sun redefinition on SunOS), 11163 (set_default_messages_domain incorrect behavior), 11673 - build issues
    - Some warning cleanup
    - Fixed tests for latest ICU versions
    - Added workaround for `libc++` issues
    - Added new defines `BOOST_LOCALE_ENABLE_CHAR16_T` and `BOOST_LOCALE_ENABLE_CHAR32_T` to enable C++11 `char16_t` and `char32_t` instead of deprecated ones
- 1.53.0 - Bug fixes: 7743, 7386, 7734, 7701, 7368, 7762:
    -  7743 - security related bug fix, some invalid UTF-8 sequences where accepted as valid
    -  7386 - invalid Windows codepage names used
    -  7734 - fixed missing documentation, caused by a error in Doxygen formatting
    -  7701 - fixed missing \c std:: in some places
    -  7368, 7762 - Spelling, grammar, typos
- 1.49.0
   - Fixed incorrect use of MultiByteToWideChar in detection of invalid input sequences
- 1.48.0 - First Release

*/
