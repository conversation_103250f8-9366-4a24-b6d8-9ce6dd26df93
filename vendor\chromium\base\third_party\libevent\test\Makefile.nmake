
CFLAGS=/I.. /I../include /I../WIN32-Code /I../compat /DWIN32 /DHAVE_CONFIG_H

CFLAGS=$(CFLAGS) /Ox /W3 /wd4996 /nologo

REGRESS_OBJS=regress.obj regress_http.obj regress_dns.obj \
        regress_rpc.obj regress.gen.obj \

OTHER_OBJS=test-init.obj test-eof.obj test-weof.obj test-time.obj \
	bench.obj bench_cascade.obj bench_http.obj bench_httpclient.obj

PROGRAMS=regress.exe \
	test-init.exe test-eof.exe test-weof.exe test-time.exe

# Disabled for now:
#	bench.exe bench_cascade.exe bench_http.exe bench_httpclient.exe


LIBS=..\libevent.lib ws2_32.lib advapi32.lib

all: $(PROGRAMS)

regress.exe: $(REGRESS_OBJS)
	$(CC) $(CFLAGS) $(LIBS) $(REGRESS_OBJS)

test-init.exe: test-init.obj
	$(CC) $(CFLAGS) $(LIBS) test-init.obj
test-eof.exe: test-eof.obj
	$(CC) $(CFLAGS) $(LIBS) test-eof.obj
test-weof.exe: test-weof.obj
	$(CC) $(CFLAGS) $(LIBS) test-weof.obj
test-time.exe: test-time.obj
	$(CC) $(CFLAGS) $(LIBS) test-time.obj

bench.exe: bench.obj
	$(CC) $(CFLAGS) $(LIBS) bench.obj
bench_cascade.exe: bench_cascade.obj
	$(CC) $(CFLAGS) $(LIBS) bench_cascade.obj
bench_http.exe: bench_http.obj
	$(CC) $(CFLAGS) $(LIBS) bench_http.obj
bench_httpclient.exe: bench_httpclient.obj
	$(CC) $(CFLAGS) $(LIBS) bench_httpclient.obj

clean:
	-del $(REGRESS_OBJS)
	-del $(OTHER_OBJS)
	-del regress.exe
