import { Page } from '@cfx-dev/ui-components';
import { observer } from 'mobx-react-lite';

export const ContactPage = observer(function ContactPage() {
  const contactMethods = [
    {
      title: 'Email Support',
      description: 'Get help via email with detailed responses',
      contact: '<EMAIL>',
      responseTime: 'Response within 24 hours',
      icon: '📧',
      color: '#3b82f6'
    },
    {
      title: 'Discord Community',
      description: 'Join our active community for real-time support',
      contact: 'discord.gg/cfx-platform',
      responseTime: 'Instant community support',
      icon: '💬',
      color: '#8b5cf6'
    },
    {
      title: 'Live Chat',
      description: 'Chat with our support team in real-time',
      contact: 'Available 24/7',
      responseTime: 'Immediate response',
      icon: '🚀',
      color: '#10b981'
    }
  ];

  return (
    <Page>
      <div style={{
        padding: '3rem',
        textAlign: 'center',
        color: 'white',
        background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%)',
        backdropFilter: 'blur(25px)',
        borderRadius: '24px',
        margin: '2rem',
        boxShadow: '0 32px 64px -12px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.2) inset',
        border: '1px solid rgba(255, 255, 255, 0.2)'
      }}>
        <h1 style={{
          fontSize: '3rem',
          marginBottom: '1.5rem',
          background: 'linear-gradient(135deg, #ffffff 0%, #f7fafc 100%)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          backgroundClip: 'text',
          fontWeight: '800',
          letterSpacing: '-0.02em'
        }}>Contact Us</h1>
        <p style={{
          fontSize: '1.3rem',
          lineHeight: '1.7',
          maxWidth: '900px',
          margin: '0 auto 3rem auto',
          opacity: '0.9',
          fontWeight: '500'
        }}>
          Get in touch with our expert team for support, inquiries, partnerships,
          or any questions about our platform. We're here to help!
        </p>
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(320px, 1fr))',
          gap: '2rem'
        }}>
          {contactMethods.map((method, index) => (
            <div key={index} style={{
              background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.06) 100%)',
              padding: '2.5rem',
              borderRadius: '20px',
              backdropFilter: 'blur(20px)',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
              cursor: 'pointer',
              textAlign: 'left',
              position: 'relative' as const,
              overflow: 'hidden'
            }}>
              <div style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                height: '4px',
                background: `linear-gradient(90deg, ${method.color}, transparent)`
              }}></div>
              <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>{method.icon}</div>
              <h3 style={{
                fontSize: '1.75rem',
                marginBottom: '1rem',
                fontWeight: '700',
                color: 'white'
              }}>{method.title}</h3>
              <p style={{
                opacity: '0.85',
                lineHeight: '1.6',
                marginBottom: '1.5rem',
                fontSize: '1.1rem'
              }}>{method.description}</p>
              <div style={{
                background: 'rgba(255, 255, 255, 0.1)',
                padding: '1rem',
                borderRadius: '12px',
                marginBottom: '1rem'
              }}>
                <p style={{
                  fontWeight: '600',
                  marginBottom: '0.5rem',
                  color: method.color
                }}>{method.contact}</p>
                <p style={{
                  fontSize: '0.9rem',
                  opacity: '0.7',
                  margin: 0
                }}>{method.responseTime}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </Page>
  );
});
