/**
 * \file
 * Copyright 2016 Microsoft
 * Licensed under the MIT license. See LICENSE file in the project root for full license information.
 */
#ifndef __MONO_METADATA_ICALL_WINDOWS_INTERNALS_H__
#define __MONO_METADATA_ICALL_WINDOWS_INTERNALS_H__

#include <config.h>
#include <glib.h>

#ifdef HOST_WIN32
#include "mono/metadata/icall-internals.h"
#include "mono/metadata/object.h"
#include "mono/metadata/object-internals.h"
#include "mono/metadata/class.h"
#include "mono/metadata/class-internals.h"
#endif /* HOST_WIN32 */
#endif /* __MONO_METADATA_ICALL_WINDOWS_INTERNALS_H__ */
