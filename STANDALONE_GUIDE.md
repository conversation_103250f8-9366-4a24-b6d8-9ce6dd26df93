# Hướng dẫn tạo GangHaiCity Standalone

## Tổng quan

Giải pháp này cho phép bạn tạo một file `GangHaiCity.exe` duy nhất có thể chạy độc lập mà không cần các file dependencies kh<PERSON><PERSON>, và hiển thị giao diện tùy chỉnh thay vì khởi động FiveM.

## Cách thức hoạt động

### 1. Logic phát hiện Standalone Mode
- Khi `GangHaiCity.exe` chạy, nó sẽ kiểm tra xem có file `CoreRT.dll` trong cùng thư mục không
- Nếu KHÔNG có `CoreRT.dll` → Chạy ở **Standalone Mode** (giao diện tùy chỉnh)
- Nếu CÓ `CoreRT.dll` → Chạy ở **Normal Mode** (FiveM launcher)

### 2. Giao diện Standalone
- Hiển thị cửa sổ tùy chỉnh với branding "GangHaiCity"
- <PERSON><PERSON> c<PERSON> nút: "Khởi động Game", "Cài đặt", "Thoát"
- <PERSON>ia<PERSON> diện có thể tùy chỉnh hoàn toàn theo ý muốn

## Cách sử dụng

### Bước 1: Build Standalone Version
```batch
# Chạy script build
build_standalone.bat
```

### Bước 2: Test Standalone Mode
```batch
# Chạy script test
test_standalone.bat
```

### Bước 3: Sử dụng
- File `GangHaiCity.exe` trong thư mục `GangHaiCity_Standalone` có thể copy đi bất kỳ đâu
- Chạy file này sẽ hiển thị giao diện tùy chỉnh, KHÔNG phải FiveM

## Tùy chỉnh giao diện

### Sửa đổi giao diện trong `StandaloneMode.cpp`:

```cpp
// Thay đổi tiêu đề cửa sổ
CreateWindowW(L"GangHaiCityStandalone", L"Tên mới của bạn", ...)

// Thêm nút mới
CreateWindowW(L"BUTTON", L"Nút mới", 
             WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
             x, y, width, height, hwnd, (HMENU)ID_MỚI, 
             GetModuleHandle(NULL), NULL);

// Xử lý sự kiện nút mới trong WM_COMMAND
case ID_MỚI:
    // Code xử lý khi click nút
    break;
```

### Thay đổi màu sắc và font:
```cpp
// Trong WM_PAINT
HBRUSH brush = CreateSolidBrush(RGB(R, G, B)); // Màu background
SetTextColor(hdc, RGB(R, G, B)); // Màu text
```

## Các file quan trọng

1. **`code/client/launcher/StandaloneMode.cpp`** - Logic giao diện standalone
2. **`code/client/launcher/StandaloneMode.h`** - Header file
3. **`code/client/launcher/Main.cpp`** - Đã được sửa để tích hợp standalone mode
4. **`build_standalone.bat`** - Script build tự động
5. **`test_standalone.bat`** - Script test

## Troubleshooting

### Vấn đề: Vẫn khởi động FiveM
**Nguyên nhân:** File `CoreRT.dll` vẫn có trong cùng thư mục
**Giải pháp:** Đảm bảo copy chỉ file `GangHaiCity.exe` ra thư mục riêng

### Vấn đề: Lỗi build
**Nguyên nhân:** Thiếu Visual Studio 2022 hoặc dependencies
**Giải pháp:** 
1. Cài đặt Visual Studio 2022 với C++ development tools
2. Chạy `prebuild.cmd` trước khi build

### Vấn đề: Giao diện không hiển thị đúng
**Nguyên nhân:** Lỗi trong code giao diện
**Giải pháp:** Kiểm tra lại code trong `StandaloneMode.cpp`

## Mở rộng

### Thêm chức năng khởi động game thực tế:
```cpp
case 1001: // Nút "Khởi động Game"
    // Khởi động GTA V hoặc game khác
    ShellExecuteW(NULL, L"open", L"path\\to\\game.exe", NULL, NULL, SW_SHOW);
    break;
```

### Thêm cửa sổ cài đặt:
```cpp
case 1002: // Nút "Cài đặt"
    // Tạo dialog box cài đặt
    DialogBox(GetModuleHandle(NULL), MAKEINTRESOURCE(IDD_SETTINGS), hwnd, SettingsDialogProc);
    break;
```

### Lưu cài đặt vào registry hoặc file:
```cpp
// Lưu vào registry
HKEY hKey;
RegCreateKeyEx(HKEY_CURRENT_USER, L"Software\\GangHaiCity", ...);
RegSetValueEx(hKey, L"Setting1", 0, REG_DWORD, (BYTE*)&value, sizeof(value));
```

## Kết luận

Với giải pháp này, bạn có thể:
- ✅ Tạo file exe duy nhất chạy độc lập
- ✅ Hiển thị giao diện tùy chỉnh thay vì FiveM
- ✅ Copy file exe đi bất kỳ đâu mà vẫn hoạt động
- ✅ Tùy chỉnh giao diện theo ý muốn
- ✅ Thêm các chức năng riêng

File `GangHaiCity.exe` sau khi build sẽ hoàn toàn độc lập và hiển thị giao diện đã tùy chỉnh!
