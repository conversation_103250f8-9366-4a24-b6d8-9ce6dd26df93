[/
 / Copyright (c) 2018 <PERSON><PERSON>
 /
 / Distributed under the Boost Software License, Version 1.0. (See accompanying
 / file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
 /]

[section:uncaught_exceptions uncaught_exceptions]

[simplesect Authors]

* <PERSON><PERSON>

[endsimplesect]

[section Header <boost/core/uncaught_exceptions.hpp>]

The header `<boost/core/uncaught_exceptions.hpp>` defines the `boost::core::uncaught_exceptions` function,
which is a more portable implementation of the same named function introduced in C++17. The function
returns the number of the currently pending exceptions. When that function returns a value greater than 0,
throwing an exception from a destructor can terminate the program.

Unfortunately, the function cannot be implemented on every pre-C++17 compiler, although the most commonly
used compilers are supported. When the compiler does not provide the necessary functionality,
`boost::core::uncaught_exceptions` returns a non-zero value if at least one exception is pending (i.e. not
necessarily the number of pending exceptions), and `BOOST_CORE_UNCAUGHT_EXCEPTIONS_EMULATED` macro
is defined.

[section Example]
``
class my_class
{
private:
    const unsigned int m_exception_count;

public:
    my_class() : m_exception_count(boost::core::uncaught_exceptions())
    {
    }

    ~my_class() noexcept(false)
    {
        if (m_exception_count == boost::core::uncaught_exceptions())
            do_something_potentially_throwing();
    }
};
``
[endsect]

[endsect]

[endsect]
