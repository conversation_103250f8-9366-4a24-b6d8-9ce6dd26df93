name: Build Alpine server building image

on:
  workflow_dispatch:

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: fivem-builder-linux-alpine

jobs:
  build-and-push-image:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
      attestations: write
      id-token: write
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
            sparse-checkout: code/tools/ci/docker-builder
            sparse-checkout-cone-mode: true
            fetch-depth: 1
            submodules: false
      - name: Convert repo name to lowercase
        id: repo_name
        run: echo "REPO_NAME_LOWER=${GITHUB_REPOSITORY,,}" >> $GITHUB_ENV

      - name: Log in to the Container registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      - name: Extract metadata (tags, labels) for Docker
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.REPO_NAME_LOWER }}/${{ env.IMAGE_NAME }}
      - name: Build and push Docker image
        id: push
        uses: docker/build-push-action@v6
        with:
          file: code/tools/ci/docker-builder/Dockerfile
          push: true
          tags:  ${{ env.REGISTRY }}/${{ env.REPO_NAME_LOWER }}/${{ env.IMAGE_NAME }}:latest
          labels: ${{ steps.meta.outputs.labels }}
      
      - name: Generate artifact attestation
        uses: actions/attest-build-provenance@v2
        with:
          subject-name: ${{ env.REGISTRY }}/${{ env.REPO_NAME_LOWER }}/${{ env.IMAGE_NAME }}
          subject-digest: ${{ steps.push.outputs.digest }}
          push-to-registry: true
