<div class="tsd-signature tsd-kind-icon">{{#compact}}
    {{{wbr name}}}
    {{#if typeParameters}}
        &lt;
        {{#each typeParameters}}
            {{#if @index}},&nbsp;{{/if}}
            {{name}}
        {{/each}}
        &gt;
    {{/if}}
    <span class="tsd-signature-symbol">{{#if isOptional}}?{{/if}}:</span>&nbsp;{{#with type}}{{>type}}{{/with}}
    {{#if defaultValue}}
        <span class="tsd-signature-symbol">
        &nbsp;=&nbsp;
        {{defaultValue}}
        </span>
    {{/if}}
{{/compact}}</div>

{{> member.sources}}

{{> comment}}

{{#if typeParameters}}
    <h4 class="tsd-type-parameters-title">Type parameters</h4>
    {{> typeParameters}}
{{/if}}

{{#if type.declaration}}
    <div class="tsd-type-declaration">
        <h4>Type declaration</h4>
        {{#with type.declaration}}
            {{> parameter}}
        {{/with}}
    </div>
{{/if}}
