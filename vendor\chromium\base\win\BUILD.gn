# Copyright (c) 2016 The Chromium Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//build/buildflag_header.gni")

declare_args() {
  # Indicates if the handle verifier should operate in a single module mode. By
  # default a single instance gets shared by all the modules.
  single_module_mode_handle_verifier = false
}

# Ensure that the handle verifier is always used in a single module mode for the
# component builds.
if (is_component_build) {
  single_module_mode_handle_verifier = true
}

buildflag_header("base_win_buildflags") {
  header = "base_win_buildflags.h"
  header_dir = "base/win"
  flags = [
    "SINGLE_MODULE_MODE_HANDLE_VERIFIER=$single_module_mode_handle_verifier",
  ]
}

static_library("pe_image") {
  sources = [
    "../no_destructor.h",
    "current_module.h",
    "pe_image.cc",
    "pe_image.h",
  ]
}
