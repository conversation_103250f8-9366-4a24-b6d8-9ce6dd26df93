import {
  Button,
  Flex,
  Input,
  Text,
  TextBlock,
  Indicator,
} from '@cfx-dev/ui-components';
import { observer } from 'mobx-react-lite';
import React, { useState } from 'react';

import { AuthStep, useCustomAuthService } from 'cfx/apps/mpMenu/services/customAuth/customAuth.service';
import { getVietnameseErrorMessage } from 'cfx/apps/mpMenu/services/customAuth/errorMessages';

import s from './CustomAuth.module.scss';

// Modern icons for visual enhancement
const EmailIcon = () => (
  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
    <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z" />
  </svg>
);

const LockIcon = () => (
  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
    <path d="M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2zm-6 9c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zm3.1-9H8.9V6c0-1.71 1.39-3.1 3.1-3.1 1.71 0 3.1 1.39 3.1 3.1v2z" />
  </svg>
);

const EyeIcon = () => (
  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
    <path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z" />
  </svg>
);

const EyeOffIcon = () => (
  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
    <path d="M12 7c2.76 0 5 2.24 5 5 0 .65-.13 1.26-.36 1.83l2.92 2.92c1.51-1.26 2.7-2.89 3.43-4.75-1.73-4.39-6-7.5-11-7.5-1.4 0-2.74.25-3.98.7l2.16 2.16C10.74 7.13 11.35 7 12 7zM2 4.27l2.28 2.28.46.46C3.08 8.3 1.78 10.02 1 12c1.73 4.39 6 7.5 11 7.5 1.55 0 3.03-.3 4.38-.84l.42.42L19.73 22 21 20.73 3.27 3 2 4.27zM7.53 9.8l1.55 1.55c-.05.21-.08.43-.08.65 0 1.66 1.34 3 3 3 .22 0 .44-.03.65-.08l1.55 1.55c-.67.33-1.41.53-2.2.53-2.76 0-5-2.24-5-5 0-.79.2-1.53.53-2.2zm4.31-.78l3.15 3.15.02-.16c0-1.66-1.34-3-3-3l-.17.01z" />
  </svg>
);

const ErrorIcon = () => (
  <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
  </svg>
);

export const LoginForm = observer(function LoginForm() {
  const authService = useCustomAuthService();
  const [formData, setFormData] = useState({
    email: '',
    password: '',
  });
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [showPassword, setShowPassword] = useState(false);

  const validateForm = () => {
    const errors: Record<string, string> = {};

    if (!formData.email.trim()) {
      errors.email = getVietnameseErrorMessage('Email is required');
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email.trim())) {
      errors.email = getVietnameseErrorMessage('Please enter a valid email address');
    }

    if (!formData.password.trim()) {
      errors.password = getVietnameseErrorMessage('Password is required');
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    authService.clearError();

    try {
      console.log('🔐 LoginForm - Attempting login...');
      await authService.login(formData.email.trim(), formData.password.trim());

      console.log('✅ LoginForm - Login successful, checking auth state:', {
        isAuthenticated: authService.isAuthenticated,
        currentStep: authService.currentStep,
        user: authService.user
      });

      // Force a small delay to ensure state is updated
      setTimeout(() => {
        console.log('🔄 LoginForm - Post-login state check:', {
          isAuthenticated: authService.isAuthenticated,
          currentStep: authService.currentStep
        });
      }, 200);

    } catch (error) {
      console.log('❌ LoginForm - Login failed:', error);
      // Error is already handled by the service and set in authService.error
      // The UI will automatically display it via the error state
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Clear validation error for this field
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  return (
    <>
      {/* Modern Header */}
      <div className={s.formHeader}>
        <h1 className={s.title}>Sign in</h1>
      </div>

      <form onSubmit={handleSubmit}>
        {/* Email Field */}
        <div className={s.inputGroup}>
          <label className={s.inputLabel}>Your email</label>
          <input
            className={`${s.modernInput} ${validationErrors.email ? s.error : ''}`}
            type="email"
            placeholder="<EMAIL>"
            value={formData.email}
            onChange={(e) => handleInputChange('email', e.target.value)}
            disabled={authService.isLoading}
          />
          {validationErrors.email && (
            <div className={s.errorMessage}>
              <ErrorIcon />
              {validationErrors.email}
            </div>
          )}
        </div>

        {/* Password Field */}
        <div className={s.inputGroup}>
          <div className={s.passwordHeader}>
            <label className={s.inputLabel}>Password</label>
            <button type="button" className={s.forgotPassword}>
              Forgot password?
            </button>
          </div>
          <div className={s.passwordInputContainer}>
            <input
              className={`${s.modernInput} ${validationErrors.password ? s.error : ''}`}
              type={showPassword ? "text" : "password"}
              placeholder="••••••••••"
              value={formData.password}
              onChange={(e) => handleInputChange('password', e.target.value)}
              disabled={authService.isLoading}
            />
            <button
              type="button"
              className={s.passwordToggle}
              onClick={() => setShowPassword(!showPassword)}
              disabled={authService.isLoading}
            >
              {showPassword ? <EyeOffIcon /> : <EyeIcon />}
            </button>
          </div>
          {validationErrors.password && (
            <div className={s.errorMessage}>
              <ErrorIcon />
              {validationErrors.password}
            </div>
          )}
        </div>

        {/* Service Error Display */}
        {authService.error && (
          <div className={s.inputGroup}>
            <div className={s.errorMessage}>
              <ErrorIcon />
              {authService.error}
            </div>

          </div>
        )}

        {/* Submit Button */}
        <button
          type="submit"
          className={s.modernButton}
          disabled={authService.isLoading}
        >
          {authService.isLoading ? (
            <div className={s.loadingIndicator}>
              <div className={s.spinner}></div>
              Signing in...
            </div>
          ) : (
            'Sign in'
          )}
        </button>

        {/* Footer Link */}
        <div className={s.authToggle}>
          <p>Chưa có tài khoản?</p>
          <button
            type="button"
            className={s.toggleButton}
            onClick={() => authService.setStep(AuthStep.REGISTER)}
            disabled={authService.isLoading}
          >
            Đăng Ký
          </button>
        </div>
      </form>
    </>
  );
});
