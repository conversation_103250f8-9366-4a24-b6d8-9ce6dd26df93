<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1 plus MathML 2.0//EN" "http://www.w3.org/TR/MathML2/dtd/xhtml-math11-f.dtd" [<!ENTITY mathml 'http://www.w3.org/1998/Math/MathML'>]>
<html xmlns="http://www.w3.org/1999/xhtml">
<head><title>bessel23</title>
<!-- MathML created with MathCast Equation Editor version 0.92 -->
</head>
<body>
<math xmlns="http://www.w3.org/1998/Math/MathML" display="block">
  <mrow>
    <msub>
      <mi>K</mi>
      <mn>0</mn>
    </msub>
    <mfenced>
      <mrow>
        <mi>x</mi>
      </mrow>
    </mfenced>
    <mo>=</mo>
    <mi>R</mi>
    <mfenced>
      <mrow>
        <mfrac>
          <mn>1</mn>
          <mi>x</mi>
        </mfrac>
      </mrow>
    </mfenced>
    <mfrac>
      <mrow>
        <msup>
          <mi>e</mi>
          <mrow>
            <mo>&#x2212;</mo>
            <mi>x</mi>
          </mrow>
        </msup>
      </mrow>
      <msqrt>
        <mi>x</mi>
      </msqrt>
    </mfrac>
    <mspace width="1.0em"/>
    <mo>;</mo>
    <mspace width="1.0em"/>
    <mi>x</mi>
    <mo>&gt;</mo>
    <mn>1</mn>
  </mrow>
</math></body>
</html>