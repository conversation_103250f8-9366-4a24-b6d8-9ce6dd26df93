import React from 'react';
import { observer } from 'mobx-react-lite';
import { useCustomAuthService } from 'cfx/apps/mpMenu/services/customAuth/customAuth.service';
import { CustomAuth } from 'cfx/apps/mpMenu/parts/CustomAuth/CustomAuth';

interface AuthGuardProps {
  children: React.ReactNode;
}

export const AuthGuard = observer(function AuthGuard({ children }: AuthGuardProps) {
  const customAuthService = useCustomAuthService();

  // If user is not authenticated, show login/register screen
  if (!customAuthService.isAuthenticated) {
    return <CustomAuth />;
  }

  // If user is authenticated, show the protected content
  return <>{children}</>;
});
