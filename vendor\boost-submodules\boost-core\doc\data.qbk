[/
Copyright 2023 <PERSON>
(<EMAIL>)

Distributed under the Boost Software License, Version 1.0.
(http://www.boost.org/LICENSE_1_0.txt)
]

[section:data data]

[simplesect Authors]

* <PERSON>

[endsimplesect]

[section Overview]

The header <boost/core/data.hpp> provides function templates `data`
to obtain the pointer to the first element in a range.

[endsect]

[section Reference]

```
namespace boost {

template<class C>
constexpr auto
data(C& c) noexcept(noexcept(c.data())) -> decltype(c.data());

template<class C>
constexpr auto
data(const C& c) noexcept(noexcept(c.data())) -> decltype(c.data());

template<class T, std::size_t N>
constexpr T*
data(T(&a)[N]) noexcept;

template<class T>
constexpr const T*
data(std::initializer_list<T> l) noexcept;

} /* boost */
```

[section Functions]

[variablelist
[[`template<class C> constexpr auto data(C& c) noexcept(noexcept(c.data())) ->
decltype(c.data());`]
[Returns `c.data()`.]]
[[`template<class C> constexpr auto data(const C& c)
noexcept(noexcept(c.data())) -> decltype(c.data());`]
[Returns `c.data()`.]]
[[`template<class T, std::size_t N> constexpr T* data(T(&a)[N]) noexcept;`]
[Returns `a`.]]
[[`template<class T> constexpr const T* data(std::initializer_list<T> l)
noexcept;`]
[Returns `l.begin()`.]]]

[endsect]

[endsect]

[endsect]
