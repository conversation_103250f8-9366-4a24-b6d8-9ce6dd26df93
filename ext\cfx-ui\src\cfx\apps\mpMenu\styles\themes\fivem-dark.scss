@use "~@cfx-dev/ui-components/dist/styles-scss/ui" as ui;

// Custom color scheme
$fg: #F5E6A8; // Vàng kem cho text
$bg: #2C2C2C; // Đen chính cho background

.cfxui-theme-gta5-dark {
  @include ui.define-main-colors($bg, $fg, ('primary': #FF8C42, // <PERSON> chính
      'secondary': #7CB342, // Xanh lá
      'accent': #F5E6A8, // Vàng kem
      'warning': #FF8C42, // Cam cho warning
      'success': #7CB342, // Xanh lá cho success
      'danger': #d9534f // Giữ đỏ cho danger
    ));

  // Custom color overrides
  @include ui.define-color-token('text', $fg);
  @include ui.define-color-token('text-muted', darken($fg, 15%));
  @include ui.define-color-token('text-secondary', darken($fg, 25%));
  @include ui.define-color-token('background', $bg);
  @include ui.define-color-token('surface', lighten($bg, 5%));
  @include ui.define-color-token('surface-elevated', lighten($bg, 10%));
  @include ui.define-color-token('border', lighten($bg, 15%));
  @include ui.define-color-token('border-light', lighten($bg, 20%));

  // Button colors
  @include ui.define-color-token('button-primary-background', #FF8C42);
  @include ui.define-color-token('button-primary-text', #2C2C2C);
  @include ui.define-color-token('button-primary-hover-background', lighten(#FF8C42, 10%));
  @include ui.define-color-token('button-secondary-background', #7CB342);
  @include ui.define-color-token('button-secondary-text', #2C2C2C);

  // Custom color classes with new palette
  .color-1 {
    color: #FF8C42;
  }

  // Cam chính
  .color-2 {
    color: #7CB342;
  }

  // Xanh lá
  .color-3 {
    color: #F5E6A8;
  }

  // Vàng kem
  .color-4 {
    color: lighten(#FF8C42, 20%);
  }

  // Cam nhạt
  .color-5 {
    color: lighten(#7CB342, 20%);
  }

  // Xanh lá nhạt
  .color-6 {
    color: darken(#F5E6A8, 20%);
  }

  // Vàng kem đậm
  .color-8 {
    color: #FF8C42;
  }

  // Cam
  .color-9 {
    color: lighten(#2C2C2C, 40%);
  }

  // Xám

  // Invert black indicators for date/time inputs
  ::-webkit-calendar-picker-indicator {
    filter: invert(1);
  }
}