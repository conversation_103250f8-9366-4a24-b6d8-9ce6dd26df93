import 'fxdk/contrib/welcome/browser/welcome.contribution';
import 'fxdk/contrib/updater/browser/updater.contribution';
import 'fxdk/contrib/toolbar/browser/toolbar.contribution';
import 'fxdk/contrib/changelog/browser/changelog.contribution';
import 'fxdk/contrib/importer/browser/importer.contribution';

import 'fxdk/contrib/intro/browser/intro.contribution';
import 'fxdk/contrib/hasher/browser/hasher.contribution';

import 'personalities/fxcode/fxcode.contribution';
import 'personalities/world-editor/world-editor.contribution';

import 'fxdk/project/browser/project.contribution';

import 'fxdk/project/contrib/creator/creator.contribution';
import 'fxdk/project/contrib/opener/opener.contribution';
import 'fxdk/project/contrib/settings/settings.contribution';
import 'fxdk/project/contrib/builder/builder.contribution';

// assets
import 'fxdk/contrib/assets/resource/browser/resource.contribution';
import 'fxdk/contrib/assets/fxworld/browser/fxworld.contribution';

// Preload all stores
import 'store/ConfirmationsState';
import 'store/GameLoadingState';
import 'store/GameState';
import 'store/KeyboardLayout';
import 'store/NotificationState';
import 'store/OutputState';
import 'store/ServerState';
import 'store/ShellState';
import 'store/StatusState';
import 'store/TaskState';
import 'store/ToolbarState';
