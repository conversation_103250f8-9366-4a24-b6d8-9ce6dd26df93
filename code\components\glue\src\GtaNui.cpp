#include <StdInc.h>
#include <CefOverlay.h>

#include <fiDevice.h>
#include <DrawCommands.h>
#include <grcTexture.h>
#include <InputHook.h>

#include <HostSharedData.h>
#include <CfxState.h>
#include <CrossBuildRuntime.h>

#include <tbb/concurrent_queue.h>

#include "ResumeComponent.h"

#include <wrl.h>

#if __has_include(<GameAudioState.h>)
#include <GameAudioState.h>
#endif

#include <Error.h>

namespace WRL = Microsoft::WRL;

using nui::GITexture;
using nui::GITextureFormat;
using nui::ResultingRectangle;

class GtaNuiInterface : public nui::GameInterface
{
private:
	uint32_t m_oldBlendState;

	uint32_t m_oldRasterizerState;

	uint32_t m_oldDepthStencilState;

	bool m_targetMouseFocus = true;

	bool m_lastTargetMouseFocus = true;

	bool m_flushMouse = true;

public:
	virtual void GetGameResolution(int* width, int* height) override;

	virtual fwRefContainer<GITexture> CreateTexture(int width, int height, GITextureFormat format, void* pixelData) override;

	virtual fwRefContainer<GITexture> CreateTextureBacking(int width, int height, GITextureFormat format) override;

	virtual fwRefContainer<GITexture> CreateTextureFromShareHandle(HANDLE shareHandle) override
	{
		assert(!"don't do that on vulkan games");

		return nullptr;
	}

	virtual fwRefContainer<GITexture> CreateTextureFromShareHandle(HANDLE shareHandle, int width, int height) override;

	virtual void SetTexture(fwRefContainer<GITexture> texture, bool pm) override;

	virtual void DrawRectangles(int numRectangles, const ResultingRectangle* rectangles) override;

	virtual void UnsetTexture() override;

	virtual void SetGameMouseFocus(bool val, bool flushMouse = true) override
	{
		m_targetMouseFocus = val;
		m_flushMouse = flushMouse;
	}

	void UpdateMouseFocus()
	{
		if (m_targetMouseFocus != m_lastTargetMouseFocus)
		{
			InputHook::SetGameMouseFocus(m_targetMouseFocus, m_flushMouse);
			m_lastTargetMouseFocus = m_targetMouseFocus;
			m_flushMouse = true;
		}
	}

	virtual HWND GetHWND() override
	{
		return CoreGetGameWindow();
	}

	virtual void BlitTexture(fwRefContainer<GITexture> dst, fwRefContainer<GITexture> src) override
	{
#ifdef GTA_FIVE
		::GetD3D11DeviceContext()->CopyResource((ID3D11Resource*)dst->GetNativeTexture(), (ID3D11Resource*)src->GetNativeTexture());
#endif
	}

	virtual ID3D11Device* GetD3D11Device() override
	{
#ifdef GTA_FIVE
		return ::GetD3D11Device();
#endif

		return NULL;
	}

	virtual ID3D11DeviceContext* GetD3D11DeviceContext() override
	{
#ifdef GTA_FIVE
		return ::GetD3D11DeviceContext();
#endif

		return NULL;
	}

	virtual fwRefContainer<GITexture> CreateTextureFromD3D11Texture(ID3D11Texture2D* texture) override
	{
		// unused
		return NULL;
	}

	virtual bool RequestMediaAccess(const std::string& frameOrigin, const std::string& url, int permissions, const std::function<void(bool, int)>& onComplete) override;

#ifdef INPUT_HOOK_HOST_CURSOR_SUPPORT
	virtual bool CanDrawHostCursor() override;

	virtual void SetHostCursorEnabled(bool enabled) override;

	virtual void SetHostCursor(HCURSOR cursor) override;
#endif
};

static tbb::concurrent_queue<std::function<void()>> g_onRenderQueue;
static tbb::concurrent_queue<std::function<void()>> g_earlyOnRenderQueue;

class GtaNuiTextureBase : public nui::GITexture
{
public:
	virtual rage::grcTexture* GetTexture() = 0;
};

class GtaNuiTexture final : public GtaNuiTextureBase
{
private:
	rage::grcTexture* m_texture;

	bool m_overriddenTexture;

	bool m_overriddenSRV;

public:
	explicit GtaNuiTexture(rage::grcTexture* texture)
		: m_texture(texture), m_overriddenTexture(false), m_overriddenSRV(false)
	{

	}

	explicit GtaNuiTexture(std::function<rage::grcTexture*(GtaNuiTexture*)> fn)
		: m_texture(nullptr), m_overriddenTexture(false), m_overriddenSRV(false)
	{
		g_onRenderQueue.push([self = fwRefContainer(this), fn]()
		{
			if (self.GetRefCount() > 1) // Don't bother creating the texture if we are the only reference left
			{
				std::unique_lock _(self->TextureLock);

				self->m_texture = fn(self.GetRef());

				self->OnMaterialize(self->m_texture);
				self->OnMaterialize.Reset();
			}
		});
	}

	virtual ~GtaNuiTexture()
	{
		auto texture = m_texture;
		m_texture = nullptr;

#ifdef GTA_FIVE
		// if overridden stuff i.e. not managed by grcResourceCache, manually release
		if (m_overriddenTexture)
		{
			auto baseTexture = texture->texture;

			if (baseTexture)
			{
				g_onRenderQueue.push([baseTexture]()
				{
					baseTexture->Release();
				});

				texture->texture = NULL;
			}
		}

		if (m_overriddenSRV)
		{
			auto baseSRV = texture->srv;

			if (baseSRV)
			{
				g_onRenderQueue.push([baseSRV]()
				{
					baseSRV->Release();
				});

				texture->srv = NULL;
			}
		}
#endif

		g_onRenderQueue.push([texture]()
		{
			delete texture;
		});
	}

	inline void MarkOverriddenTexture()
	{
		m_overriddenTexture = true;
	}

	inline void MarkOverriddenSRV()
	{
		m_overriddenSRV = true;
	}

	virtual rage::grcTexture* GetTexture() override { return m_texture; }

	virtual void* GetNativeTexture() override
	{
#ifdef GTA_FIVE
		if (!m_texture)
		{
			return nullptr;
		}

		return m_texture->texture;
#else
		return m_texture;
#endif
	}

	virtual void* GetHostTexture() override
	{
		return m_texture;
	}

	virtual void WithHostTexture(std::function<void(void*)>&& callback) override
	{
		std::unique_lock _(TextureLock);

		if (auto texture = GetHostTexture())
		{
			callback(texture);
		}
		else
		{
			OnMaterialize.Connect([callback = std::move(callback)](void* texture)
			{
				callback(texture);
			});
		}
	}

	virtual bool Map(int numSubLevels, int subLevel, nui::GILockedTexture* lockedTexture, nui::GILockFlags flags) override
	{
#ifdef GTA_FIVE
		rage::grcLockedTexture rlt;

		if (m_texture->Map(numSubLevels, subLevel, &rlt, (rage::grcLockFlags)flags))
		{
			lockedTexture->format = rlt.format;
			lockedTexture->height = rlt.height;
			lockedTexture->level = rlt.level;
			lockedTexture->numSubLevels = rlt.numSubLevels;
			lockedTexture->pBits = rlt.pBits;
			lockedTexture->pitch = rlt.pitch;
			lockedTexture->width = rlt.width;

			return true;
		}
#elif defined(GTA_NY)
		if (!m_texture || !m_texture->m_pITexture)
		{
			return false;
		}

		D3DLOCKED_RECT lockedRect;
		if (SUCCEEDED(m_texture->m_pITexture->LockRect(0, &lockedRect, NULL, (flags == nui::GILockFlags::WriteDiscard) ? D3DLOCK_DISCARD : 0)))
		{
			lockedTexture->pBits = lockedRect.pBits;
			lockedTexture->pitch = lockedRect.Pitch;

			return true;
		}
#endif

		return false;
	}

	virtual void Unmap(nui::GILockedTexture* lockedTexture) override
	{
#ifdef GTA_FIVE
		rage::grcLockedTexture rlt;
		rlt.format = lockedTexture->format;
		rlt.height = lockedTexture->height;
		rlt.level = lockedTexture->level;
		rlt.numSubLevels = lockedTexture->numSubLevels;
		rlt.pBits = lockedTexture->pBits;
		rlt.pitch = lockedTexture->pitch;
		rlt.width = lockedTexture->width;

		m_texture->Unmap(&rlt);
#elif defined(GTA_NY)
		m_texture->m_pITexture->UnlockRect(0);
#endif
	}

private:
	fwEvent<void*> OnMaterialize;
	std::mutex TextureLock;
};

#ifdef IS_RDR3
class GtaNuiDynamicTexture final : public GtaNuiTextureBase
{
private:
	rage::sga::ext::DynamicTexture2* m_texture;

public:
	explicit GtaNuiDynamicTexture(std::function<rage::sga::ext::DynamicTexture2*(GtaNuiDynamicTexture*)> fn)
		: m_texture(nullptr)
	{
		g_onRenderQueue.push([self = fwRefContainer(this), fn]()
		{
			if (self.GetRefCount() > 1) // Don't bother creating the texture if we are the only reference left
			{
				self->m_texture = fn(self.GetRef());
			}
		});
	}

	virtual ~GtaNuiDynamicTexture()
	{
		auto texture = m_texture;
		m_texture = nullptr;

		g_onRenderQueue.push([texture]()
		{
			delete texture;
		});
	}

	inline rage::grcTexture* GetTexture()
	{
		if (m_texture)
		{
			return static_cast<rage::grcTexture*>(m_texture->GetTexture());
		}

		return nullptr;
	}

	virtual void* GetNativeTexture() override
	{
		return GetTexture();
	}

	virtual void* GetHostTexture() override
	{
		return m_texture;
	}

	virtual bool Map(int numSubLevels, int subLevel, nui::GILockedTexture* lockedTexture, nui::GILockFlags flags) override
	{
		if (m_texture)
		{
			m_texture->MakeReady(rage::sga::GraphicsContext::GetCurrent());

			rage::sga::MapData mapData;
			if (m_texture->Map(nullptr, mapData))
			{
				lockedTexture->pBits = mapData.GetBuffer();
				lockedTexture->pitch = mapData.GetStride();

				lastMapData = mapData;

				return true;
			}
		}

		return false;
	}

	virtual void Unmap(nui::GILockedTexture* lockedTexture) override
	{
		m_texture->Unmap(nullptr, lastMapData);
	}

private:
	rage::sga::MapData lastMapData;
};
#endif

void GtaNuiInterface::GetGameResolution(int* width, int* height)
{
	int w, h;
	::GetGameResolution(w, h);

	if (w == 0 || h == 0)
	{
		w = 1919;
		h = 1080;
	}

	*width = w;
	*height = h;
}

fwRefContainer<GITexture> GtaNuiInterface::CreateTexture(int width, int height, GITextureFormat format, void* pixelData)
{
#ifdef GTA_FIVE
	rage::sysMemAllocator::UpdateAllocatorValue();

	rage::grcTextureReference reference;
	memset(&reference, 0, sizeof(reference));
	reference.width = width;
	reference.height = height;
	reference.depth = 1;
	reference.stride = width * 4;
	reference.format = (format == GITextureFormat::ARGB) ? 11 : -1; // dxt5?
	reference.pixelData = (uint8_t*)pixelData;

	rage::grcTexture* texture = rage::grcTextureFactory::getInstance()->createImage(&reference, nullptr);

	return new GtaNuiTexture(texture);
#else
	auto pixelMem = std::make_shared<std::vector<uint8_t>>(width * height * 4);
	memcpy(pixelMem->data(), pixelData, pixelMem->size());

	return new GtaNuiTexture([width, height, format, pixelMem](GtaNuiTexture*)
	{
		rage::grcTextureReference reference;
		memset(&reference, 0, sizeof(reference));
		reference.width = width;
		reference.height = height;
		reference.depth = 1;
		reference.stride = width * 4;
#if GTA_NY
		reference.format = 1;
#else
		reference.format = (format == GITextureFormat::ARGB) ? 11 : -1; // dxt5?
#endif
		reference.pixelData = (uint8_t*)pixelMem->data();

		return rage::grcTextureFactory::getInstance()->createImage(&reference, nullptr);
	});
#endif
}

fwRefContainer<GITexture> GtaNuiInterface::CreateTextureBacking(int width, int height, GITextureFormat format)
{
#ifndef GTA_NY
	rage::sysMemAllocator::UpdateAllocatorValue();
#endif

	assert(format == GITextureFormat::ARGB);

#if defined(GTA_FIVE)
	return new GtaNuiTexture([width, height](GtaNuiTexture*)
	{
		rage::grcManualTextureDef textureDef;
		memset(&textureDef, 0, sizeof(textureDef));
		textureDef.isStaging = 1;
		textureDef.usage = 1;
		textureDef.arraySize = 1;
		return rage::grcTextureFactory::getInstance()->createManualTexture(width, height, 2 /* maps to BGRA DXGI format */, nullptr, true, &textureDef);
	});
#elif defined(IS_RDR3)
	return new GtaNuiDynamicTexture([width, height](GtaNuiDynamicTexture*)
	{
		rage::sga::ImageParams ip;
		ip.width = width;
		ip.height = height;
		ip.depth = 1;
		ip.levels = 1;
		ip.dimension = 1;
		ip.bufferFormat = rage::sga::BufferFormat::B8G8R8A8_UNORM;

		auto texture = new rage::sga::ext::DynamicTexture2();
		texture->Init(3, nullptr, ip, 0, 2, nullptr, 8, 1, nullptr);
		return texture;
	});
#else
	return new GtaNuiTexture([width, height](GtaNuiTexture*)
	{
		rage::grcManualTextureDef textureDef;
		memset(&textureDef, 0, sizeof(textureDef));
		textureDef.isStaging = 0;
		textureDef.arraySize = 1;

#ifdef GTA_NY
		return rage::grcTextureFactory::getInstance()->createManualTexture(width, height, FORMAT_A8R8G8B8, true, &textureDef);
#else
		return rage::grcTextureFactory::getInstance()->createManualTexture(width, height, 2 /* maps to BGRA DXGI format */, nullptr, true, &textureDef);
#endif
	});
#endif
}

#include <d3d12.h>

#pragma comment(lib, "vulkan-1.lib")

#ifdef IS_RDR3
#include <VulkanHelper.h>
#endif

fwRefContainer<GITexture> GtaNuiInterface::CreateTextureFromShareHandle(HANDLE shareHandle, int width, int height)
{
#ifndef GTA_NY
	rage::sysMemAllocator::UpdateAllocatorValue();
#endif

#ifdef GTA_FIVE
	ID3D11Device* device = ::GetD3D11Device();

	WRL::ComPtr<ID3D11Texture2D> resource;
	if (SUCCEEDED(device->OpenSharedResource(shareHandle, IID_PPV_ARGS(&resource))))
	{
		return new GtaNuiTexture([device, resource](GtaNuiTexture* texture)
		{
			D3D11_TEXTURE2D_DESC desc;
			resource->GetDesc(&desc);

			struct
			{
				void* vtbl;
				ID3D11Device* rawDevice;
			}* deviceStuff = (decltype(deviceStuff))device;

			rage::grcManualTextureDef textureDef;
			memset(&textureDef, 0, sizeof(textureDef));
			textureDef.isStaging = 1;
			textureDef.usage = 1;
			textureDef.arraySize = 1;

			auto texRef = rage::grcTextureFactory::getInstance()->createManualTexture(desc.Width, desc.Height, 2 /* maps to BGRA DXGI format */, nullptr, true, &textureDef);

			if (texRef)
			{
				if (texRef->texture)
				{
#ifdef GTA_FIVE
					rage::grcResourceCache::GetInstance()->QueueDelete(texRef->texture);
					rage::grcResourceCache::GetInstance()->FlushQueue();
#else
					texRef->texture->Release();
#endif
					texRef->texture = NULL;
				}

				resource.CopyTo(&texRef->texture);

				if (texRef->srv)
				{
					texRef->srv->Release();
				}

				deviceStuff->rawDevice->CreateShaderResourceView(resource.Get(), nullptr, &texRef->srv);
			}

			texture->MarkOverriddenSRV();
			texture->MarkOverriddenTexture();

			return texRef;
		});
	}
#elif GTA_NY
	// ?
#else
	if (GetCurrentGraphicsAPI() == GraphicsAPI::D3D12)
	{
		ID3D12Device* device = (ID3D12Device*)GetGraphicsDriverHandle();

		ID3D12Resource* resource = nullptr;
		if (SUCCEEDED(device->OpenSharedHandle(shareHandle, __uuidof(ID3D12Resource), (void**)&resource)))
		{
			return new GtaNuiTexture([resource, shareHandle](GtaNuiTexture* texture)
			{
				ID3D12Resource* oldTexture = nullptr;

				auto desc = resource->GetDesc();

				auto width = desc.Width;
				auto height = desc.Height;

				rage::grcManualTextureDef textureDef;
				memset(&textureDef, 0, sizeof(textureDef));
				textureDef.isStaging = 0;
				textureDef.arraySize = 1;

				auto texRef = (rage::sga::TextureD3D12*)rage::grcTextureFactory::getInstance()->createManualTexture(width, height, 2, nullptr, true, &textureDef);

				if (texRef)
				{
					rage::sga::Driver_Destroy_Texture(texRef);

					texRef->resource = resource;

					rage::sga::TextureViewDesc srvDesc;
					srvDesc.mipLevels = 1;
					srvDesc.arrayStart = 0;
					srvDesc.dimension = 4;
					srvDesc.arraySize = 1;

					rage::sga::Driver_Create_ShaderResourceView(texRef, srvDesc);

					CloseHandle(shareHandle);
				}

				return (rage::grcTexture*)texRef;
			});
		}
	}
	else if (GetCurrentGraphicsAPI() == GraphicsAPI::Vulkan)
	{
		// meanwhile in Vulkan, this is infinitely annoying
		return new GtaNuiTexture([shareHandle, width, height](GtaNuiTexture* texture)
		{
			std::vector<uint8_t> pixelData(size_t(width) * size_t(height) * 4);

			rage::grcTextureReference reference;
			memset(&reference, 0, sizeof(reference));
			reference.width = width;
			reference.height = height;
			reference.depth = 1;
			reference.stride = width * 4;
			reference.format = 11;
			reference.pixelData = (uint8_t*)pixelData.data();

			auto texRef = (rage::sga::TextureVK*)rage::grcTextureFactory::getInstance()->createImage(&reference, nullptr);

			if (texRef)
			{
				rage::sga::Driver_Destroy_Texture(texRef);

				// Vulkan API magic time (copy/pasted from samples on GH)
				VkDevice device = (VkDevice)GetGraphicsDriverHandle();
				
				VkExtent3D Extent = { width, height, 1 };

				VkExternalMemoryImageCreateInfo ExternalMemoryImageCreateInfo = { VK_STRUCTURE_TYPE_EXTERNAL_MEMORY_IMAGE_CREATE_INFO };
				ExternalMemoryImageCreateInfo.handleTypes = VK_EXTERNAL_MEMORY_HANDLE_TYPE_D3D11_TEXTURE_BIT;
				VkImageCreateInfo ImageCreateInfo = { VK_STRUCTURE_TYPE_IMAGE_CREATE_INFO };
				ImageCreateInfo.pNext = &ExternalMemoryImageCreateInfo;
				ImageCreateInfo.imageType = VK_IMAGE_TYPE_2D;
				ImageCreateInfo.format = VK_FORMAT_B8G8R8A8_UNORM;
				ImageCreateInfo.extent = Extent;
				ImageCreateInfo.mipLevels = 1;
				ImageCreateInfo.arrayLayers = 1;
				ImageCreateInfo.samples = VK_SAMPLE_COUNT_1_BIT;
				ImageCreateInfo.tiling = VK_IMAGE_TILING_OPTIMAL;
				ImageCreateInfo.usage = VK_IMAGE_USAGE_SAMPLED_BIT;
				ImageCreateInfo.sharingMode = VK_SHARING_MODE_EXCLUSIVE;
				ImageCreateInfo.initialLayout = VK_IMAGE_LAYOUT_UNDEFINED;

				VkImage Image;
				VkResult result = vkCreateImage(device, &ImageCreateInfo, nullptr, &Image);

				if (result != VK_SUCCESS)
				{
					FatalError("Failed to create a Vulkan image. VkResult: %s", ResultToString(result));
				}

				VkMemoryRequirements MemoryRequirements;
				vkGetImageMemoryRequirements(device, Image, &MemoryRequirements);

				VkMemoryDedicatedAllocateInfo MemoryDedicatedAllocateInfo = { VK_STRUCTURE_TYPE_MEMORY_DEDICATED_ALLOCATE_INFO };
				MemoryDedicatedAllocateInfo.image = Image;
				VkImportMemoryWin32HandleInfoKHR ImportMemoryWin32HandleInfo = { VK_STRUCTURE_TYPE_IMPORT_MEMORY_WIN32_HANDLE_INFO_KHR };
				ImportMemoryWin32HandleInfo.pNext = &MemoryDedicatedAllocateInfo;
				ImportMemoryWin32HandleInfo.handleType = VK_EXTERNAL_MEMORY_HANDLE_TYPE_D3D11_TEXTURE_BIT;
				ImportMemoryWin32HandleInfo.handle = shareHandle;
				VkMemoryAllocateInfo MemoryAllocateInfo = { VK_STRUCTURE_TYPE_MEMORY_ALLOCATE_INFO };
				MemoryAllocateInfo.pNext = &ImportMemoryWin32HandleInfo;
				MemoryAllocateInfo.allocationSize = MemoryRequirements.size;

				unsigned long typeIndex;
				_BitScanForward(&typeIndex, MemoryRequirements.memoryTypeBits);
				MemoryAllocateInfo.memoryTypeIndex = typeIndex;

				static auto _vkBindImageMemory2 = (PFN_vkBindImageMemory2)vkGetDeviceProcAddr(device, "vkBindImageMemory2");

				VkDeviceMemory ImageMemory;
				result = vkAllocateMemory(device, &MemoryAllocateInfo, nullptr, &ImageMemory);

				if (result != VK_SUCCESS)
				{
					FatalError("Failed to allocate memory for Vulkan. VkResult: %s", ResultToString(result));
				}

				VkBindImageMemoryInfo BindImageMemoryInfo = { VK_STRUCTURE_TYPE_BIND_IMAGE_MEMORY_INFO };
				BindImageMemoryInfo.image = Image;
				BindImageMemoryInfo.memory = ImageMemory;

				result = _vkBindImageMemory2(device, 1, &BindImageMemoryInfo);

				if (result != VK_SUCCESS)
				{
					FatalError("Failed to bind Vulkan image memory. VkResult: %s", ResultToString(result));
				}

				auto newImage = new rage::sga::TextureVK::ImageData;
				//memcpy(newImage, texRef->image, sizeof(*newImage));
				memset(newImage, 0, sizeof(*newImage));
				// these come from a fast allocator(?)
				//delete texRef->image;
				texRef->image = newImage;

				texRef->image->image = Image;
				texRef->image->memory = ImageMemory;

				rage::sga::TextureViewDesc srvDesc;
				srvDesc.mipLevels = 1;
				srvDesc.arrayStart = 0;
				srvDesc.dimension = 4;
				srvDesc.arraySize = 1;

				rage::sga::Driver_Create_ShaderResourceView(texRef, srvDesc);

				CloseHandle(shareHandle);
			}

			return (rage::grcTexture*)texRef;
		});
	}
#endif

	return new GtaNuiTexture(nullptr);
}

static thread_local fwRefContainer<nui::GITexture> g_currentTexture;

void GtaNuiInterface::SetTexture(fwRefContainer<GITexture> texture, bool pm)
{
#ifndef GTA_NY
	rage::sysMemAllocator::UpdateAllocatorValue();
#endif

	g_currentTexture = texture;

	SetTextureGtaIm(static_cast<GtaNuiTextureBase*>(texture.GetRef())->GetTexture());

#if _HAVE_GRCORE_NEWSTATES
	m_oldRasterizerState = GetRasterizerState();
	SetRasterizerState(GetStockStateIdentifier(RasterizerStateNoCulling));

	m_oldBlendState = GetBlendState();
	SetBlendState(GetStockStateIdentifier(pm ? BlendStatePremultiplied : BlendStateDefault));

	m_oldDepthStencilState = GetDepthStencilState();
	SetDepthStencilState(GetStockStateIdentifier(DepthStencilStateNoDepth));
#else
	SetRenderState(0, grcCullModeNone);
	SetRenderState(2, pm ? 13 : 0); // alpha blending m8
#endif

	PushDrawBlitImShader();
}

void GtaNuiInterface::DrawRectangles(int numRectangles, const nui::ResultingRectangle* rectangles)
{
	for (int i = 0; i < numRectangles; i++)
	{
		auto rectangle = &rectangles[i];

		rage::grcBegin(4, 4);

		auto& rect = rectangle->rectangle;
		uint32_t color = *(uint32_t*)&rectangle->color;

		// this swaps ABGR (as CRGBA is ABGR in little-endian) to ARGB by rotating left
		if (!rage::grcTexture::IsRenderSystemColorSwapped())
		{
			color = (color & 0xFF00FF00) | _rotl(color & 0x00FF00FF, 16);
		}

		auto u1 = 0.0f;
		auto v1 = 0.0f;
		auto u2 = 1.0f;
		auto v2 = 1.0f;

		rage::grcVertex(rect.fX1, rect.fY1, 0.0f, 0.0f, 0.0f, -1.0f, color, u1, v1);
		rage::grcVertex(rect.fX2, rect.fY1, 0.0f, 0.0f, 0.0f, -1.0f, color, u2, v1);
		rage::grcVertex(rect.fX1, rect.fY2, 0.0f, 0.0f, 0.0f, -1.0f, color, u1, v2);
		rage::grcVertex(rect.fX2, rect.fY2, 0.0f, 0.0f, 0.0f, -1.0f, color, u2, v2);

		rage::grcEnd();
	}
}

void GtaNuiInterface::UnsetTexture()
{
	PopDrawBlitImShader();

#ifdef _HAVE_GRCORE_NEWSTATES
	SetRasterizerState(m_oldRasterizerState);
	SetBlendState(m_oldBlendState);
	SetDepthStencilState(m_oldDepthStencilState);
#endif

	g_currentTexture = {};
}

extern bool HandleMediaRequest(const std::string& frameOrigin, const std::string& url, int permissions, const std::function<void(bool, int)>& onComplete);

bool GtaNuiInterface::RequestMediaAccess(const std::string& frameOrigin, const std::string& url, int permissions, const std::function<void(bool, int)>& onComplete)
{
	return HandleMediaRequest(frameOrigin, url, permissions, onComplete);
}

#ifdef INPUT_HOOK_HOST_CURSOR_SUPPORT
bool GtaNuiInterface::CanDrawHostCursor()
{
	return true;
}

void GtaNuiInterface::SetHostCursor(HCURSOR cursor)
{
	SetClassLongPtr(CoreGetGameWindow(), GCLP_HCURSOR,
		static_cast<LONG>(reinterpret_cast<LONG_PTR>(cursor)));

	SetCursor(cursor);
}

void GtaNuiInterface::SetHostCursorEnabled(bool enabled)
{
	InputHook::SetHostCursorEnabled(enabled);
}
#endif

static GtaNuiInterface nuiGi;

static void DoRender()
{
	std::function<void()> fn;

	while (g_earlyOnRenderQueue.try_pop(fn))
	{
		fn();
	}

	while (g_onRenderQueue.try_pop(fn))
	{
		fn();
	}

	nuiGi.OnRender();
}

static InitFunction initFunction([]()
{
#if __has_include(<GameAudioState.h>)
	nuiGi.QueryShouldMute.Connect([](bool& shouldMute)
	{
		shouldMute = shouldMute || ShouldMuteGameAudio();
	});
#endif

	OnGrcCreateDevice.Connect([]()
	{
		nuiGi.OnInitRenderer();
	});

	OnPostFrontendRender.Connect([]()
	{
		if (IsOnRenderThread())
		{
			DoRender();
		}
		else
		{
			uintptr_t a = 0, b = 0;
			EnqueueGenericDrawCommand([](uintptr_t, uintptr_t)
			{
				DoRender();
			}, &a, &b);
		}
		
#if !IS_RDR3 && !GTA_NY
		if (nui::HasMainUI() || nui::HasFrame("loadingScreen"))
		{
			GfxForceVsync(true);
		}
		else
		{
			GfxForceVsync(false);
		}
#endif

		nuiGi.UpdateMouseFocus();
	}, -1000);

	rage::fiDevice::OnInitialMount.Connect([]()
	{
		nuiGi.OnInitVfs();
	}, 100);

	static HostSharedData<CfxState> initState("CfxInitState");

	if (initState->IsGameProcess())
	{
		nui::Initialize(&nuiGi);
	}

	InputHook::QueryInputTarget.Connect([](std::vector<InputTarget*>& targets)
	{
		return nuiGi.QueryInputTarget(targets);
	}, 20);

	InputHook::DeprecatedOnWndProc.Connect([](HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam, bool& pass, LRESULT& lresult)
	{
		nuiGi.OnWndProc(hWnd, msg, wParam, lParam, pass, lresult);
	}, 20);

	InputHook::QueryMayLockCursor.Connect([](int& a)
	{
		nuiGi.QueryMayLockCursor(a);
	});
});
