@import "variables";

.root {
  position: relative;

  display: flex;
  align-items: stretch;
  justify-content: stretch;

  overflow: hidden;

  @include interactiveTransition;

  .progress {
    position: absolute;

    left: 0;
    bottom: 0;

    height: $q*0.5;

    background-color: rgba($fgColor, .5);

    @keyframes shrimp {
      0% {
        width: 100%;
      }
      100% {
        width: 0px;
      }
    }

    animation-name: shrimp;
    animation-timing-function: linear;
  }

  .actions {
    display: flex;
    flex-direction: column;
    align-items: stretch;
    justify-content: stretch;

    cursor: pointer;

    .button {
      flex-grow: 1;

      display: flex;
      align-items: center;
      justify-content: center;

      padding: $q;

      font-size: $q*4;

      &:hover {
        background-color: rgba($fgColor, .25);
      }
    }
  }

  .content {
    flex-grow: 1;

    display: flex;
    align-items: center;
    justify-content: stretch;

    padding: $q*2;

    .icon {
      display: flex;
      align-items: center;
      justify-content: center;

      font-size: $fs1;
    }

    .text {
      flex-grow: 1;

      @include fontPrimary();
      font-size: $fs08;

      margin-left: $q*2;

      .commands {
        display: flex;
        flex-wrap: wrap;

        gap: $q*2;

        margin-top: $q*2;

        .command {
          padding: $q $q*2;

          font-size: $fs08;
          color: $fgColor;
          background-color: rgba($bgColor, .5);

          border-radius: $q * .5;

          cursor: pointer;

          @include interactiveTransition;

          &:hover {
            background-color: rgba($bgColor, .75);
          }
        }
      }
    }
  }

  &.info {
    background-color: rgba($fgColor, .25);
  }

  &.warning {
    background-color: rgba($wrColor, .5);
  }

  &.error {
    background-color: rgba($erColor, .5);
  }
}
