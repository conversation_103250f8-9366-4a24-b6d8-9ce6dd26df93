@import "variables";

.root {
  position: relative;

  display: flex;

  height: $q*9;

  box-shadow: 0 0 0 2px rgba($fgColor, .25) inset;

  user-select: none;

  &.disabled {
    opacity: .5;
    pointer-events: none;
  }

  &.descripted {
    margin-bottom: $q*4;
  }

  .description {
    position: absolute;
    bottom: 0;

    padding-top: $q*2;

    transform: translateY(100%);

    color: rgba($fgColor, .5);
    font-size: $fs08;
    white-space: nowrap;
  }

  .option {
    display: flex;
    align-items: center;
    justify-content: center;

    padding: $q*2 $q*3;

    cursor: pointer;

    &:hover {
      background-color: rgba($fgColor, .25);
    }

    &.active {
      color: $bgColor;
      background-color: rgba($fgColor, .75);
    }
  }
}
