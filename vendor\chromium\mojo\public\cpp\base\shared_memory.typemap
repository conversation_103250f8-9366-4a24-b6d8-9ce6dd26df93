# Copyright 2018 The Chromium Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

mojom = "//mojo/public/mojom/base/shared_memory.mojom"
public_headers = [
  "//base/memory/platform_shared_memory_region.h",
  "//base/memory/read_only_shared_memory_region.h",
  "//base/memory/unsafe_shared_memory_region.h",
  "//base/memory/writable_shared_memory_region.h",
]
traits_headers = [ "//mojo/public/cpp/base/shared_memory_mojom_traits.h" ]
public_deps = [
  "//base",
  "//mojo/public/cpp/base:shared_typemap_traits",
]
type_mappings = [
  "mojo_base.mojom.PlatformSharedMemoryHandle=::base::subtle::PlatformSharedMemoryRegion::ScopedPlatformHandle[move_only]",
  "mojo_base.mojom.PlatformSharedMemoryRegion=::base::subtle::PlatformSharedMemoryRegion[move_only]",
  "mojo_base.mojom.PlatformSharedMemoryRegion.Mode=::base::subtle::PlatformSharedMemoryRegion::Mode",
  "mojo_base.mojom.ReadOnlySharedMemoryRegion=::base::ReadOnlySharedMemoryRegion[move_only,nullable_is_same_type]",
  "mojo_base.mojom.UnsafeSharedMemoryRegion=::base::UnsafeSharedMemoryRegion[move_only,nullable_is_same_type]",
  "mojo_base.mojom.WritableSharedMemoryRegion=::base::WritableSharedMemoryRegion[move_only,nullable_is_same_type]",
]
