/*=============================================================================
    Copyright (c) 2001-2011 <PERSON>

    Distributed under the Boost Software License, Version 1.0. (See accompanying
    file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

    This is an auto-generated file. Do not edit!
==============================================================================*/

#if FUSION_MAX_ZIP_SEQUENCES <= 10
#include <boost/fusion/algorithm/transformation/detail/preprocessed/zip10.hpp>
#elif FUSION_MAX_ZIP_SEQUENCES <= 20
#include <boost/fusion/algorithm/transformation/detail/preprocessed/zip20.hpp>
#elif FUSION_MAX_ZIP_SEQUENCES <= 30
#include <boost/fusion/algorithm/transformation/detail/preprocessed/zip30.hpp>
#elif FUSION_MAX_ZIP_SEQUENCES <= 40
#include <boost/fusion/algorithm/transformation/detail/preprocessed/zip40.hpp>
#elif FUSION_MAX_ZIP_SEQUENCES <= 50
#include <boost/fusion/algorithm/transformation/detail/preprocessed/zip50.hpp>
#else
#error "FUSION_MAX_ZIP_SEQUENCES out of bounds for preprocessed headers"
#endif
