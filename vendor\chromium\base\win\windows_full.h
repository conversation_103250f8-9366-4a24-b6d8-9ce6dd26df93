// Copyright (c) 2014 The Chromium Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// This header is needed so that mojo typemap files can specify their dependence
// on Windows.h. This can be removed once https://crbug.com/798763 is resolved.

#ifndef BASE_WIN_WINDOWS_FULL_H
#define BASE_WIN_WINDOWS_FULL_H

#include <windows.h>

#endif  // BASE_WIN_WINDOWS_FULL_H
