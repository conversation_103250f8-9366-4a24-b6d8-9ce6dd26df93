<?xml version='1.0'?>
<!DOCTYPE html PUBLIC '-//W3C//DTD XHTML 1.1 plus MathML 2.0//EN'
  'http://www.w3.org/TR/MathML2/dtd/xhtml-math11-f.dtd'
  [<!ENTITY mathml 'http://www.w3.org/1998/Math/MathML'>]>
<html xmlns='http://www.w3.org/1999/xhtml'>
<head><title>bessel_yn_small_z</title>
<!-- MathML created with MathCast Equation Editor version 0.89 -->
</head>
<body>
<math xmlns="http://www.w3.org/1998/Math/MathML" display="block">
  <mrow>
    <msub>
      <mi>Y</mi>
      <mi>n</mi>
    </msub>
    <mfenced>
      <mrow>
        <mi>z</mi>
      </mrow>
    </mfenced>
    <mspace width="1em"/>
    <mo>=</mo>
    <mspace width="1em"/>
    <mo>&#x2212;</mo>
    <mfenced>
      <mrow>
        <mi>n</mi>
        <mo>&#x2212;</mo>
        <mn>1</mn>
      </mrow>
    </mfenced>
    <mfrac>
      <mo>!</mo>
      <mi>&#x03C0;</mi>
    </mfrac>
    <msup>
      <mfenced>
        <mrow>
          <mfrac>
            <mi>z</mi>
            <mn>2</mn>
          </mfrac>
        </mrow>
      </mfenced>
      <mrow>
        <mo>&#x2212;</mo>
        <mi>n</mi>
      </mrow>
    </msup>
    <mspace width="1em"/>
    <mo>;</mo>
    <mi>z</mi>
    <mo>&lt;</mo>
    <mi>&#x03B5;</mi>
    <mspace width="1em"/>
    <mo>&#x2227;</mo>
    <mspace width="1em"/>
    <mi>n</mi>
    <mo>></mo>
    <mn>2</mn>
  </mrow>
</math>
</body>
</html>
