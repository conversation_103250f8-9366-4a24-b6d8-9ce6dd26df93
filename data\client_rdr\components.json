["legitimacy", "net:http-server", "net:tcp-server", "net:base", "net:packet", "rage:allocator:rdr3", "rage:scripting:rdr3", "rage:graphics:rdr3", "font-renderer", "rage:input:rdr3", "rage:nutsnbolts:rdr3", "conhost:v2", "rage:device:rdr3", "gta:streaming:rdr3", "gta:game:rdr3", "gta:mission-cleanup:rdr3", "citizen:level-loader:rdr3", "vfs:core", "vfs:impl:rage", "citizen:resources:core", "citizen:resources:metadata:lua", "citizen:resources:client", "citizen:resources:gta", "citizen:scripting:core", "citizen:scripting:lua", "citizen:scripting:mono", "citizen:scripting:mono-v2", "citizen:scripting:v8client", "citizen:scripting:v8node", "citizen:scripting:v8-v12.4", "gta:core:rdr3", "http-client", "pool-sizes-state", "debug:net", "gta:net:rdr3", "citizen:legacy-net:resources", "net", "profiles", "steam", "scripting:gta", "glue", "loading-screens:rdr3", "nui:core", "nui:gsclient", "scrbind:base", "nui:resources", "citizen:game:ipc", "devtools:rdr3", "extra-natives:rdr3", "citizen:playernames:rdr3", "rage:formats:x", "devcon", "adhesive", "tool:formats", "citizen:dev<PERSON><PERSON>", "lovely-script", "discord", "voip:mumble"]