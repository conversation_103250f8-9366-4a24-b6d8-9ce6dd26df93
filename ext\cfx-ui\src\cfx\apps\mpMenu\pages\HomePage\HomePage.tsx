import { <PERSON><PERSON>, <PERSON>, But<PERSON>, Text, Box } from '@cfx-dev/ui-components';
import { observer } from 'mobx-react-lite';
import React from 'react';
import { useService } from 'cfx/base/servicesContainer';
import { IServersService } from 'cfx/common/services/servers/servers.service';
import { IServersConnectService } from 'cfx/common/services/servers/serversConnect.service';
import { IServerView } from 'cfx/common/services/servers/types';
import { useCustomAuthService } from 'cfx/apps/mpMenu/services/customAuth/customAuth.service';

// Server ID to display (from FiveM API)
const TARGET_SERVER_ID = '4d4ze8';

export const HomePage = observer(function HomePage() {
  const serversService = useService(IServersService);
  const serversConnectService = useService(IServersConnectService);
  const customAuthService = useCustomAuthService();

  const [server, setServer] = React.useState<IServerView | null>(null);
  const [loading, setLoading] = React.useState(true);

  // Load specific server data
  React.useEffect(() => {
    const loadServer = async () => {
      try {
        console.log('🔍 Loading server:', TARGET_SERVER_ID);

        // Try to get server from service
        let serverData: IServerView | null = serversService.getServer(TARGET_SERVER_ID) || null;

        if (!serverData) {
          // Load server data if not available
          await serversService.loadServerDetailedData(TARGET_SERVER_ID);
          serverData = serversService.getServer(TARGET_SERVER_ID) || null;
        }

        if (!serverData) {
          // Try loading live data
          const liveData = await serversService.loadServerLiveData(TARGET_SERVER_ID);
          serverData = liveData || null;
        }

        console.log('📊 Server data loaded:', serverData);
        setServer(serverData);
      } catch (error) {
        console.error('❌ Failed to load server:', error);
        setServer(null);
      } finally {
        setLoading(false);
      }
    };

    loadServer();
  }, [serversService]);

  const handleConnect = () => {
    if (server) {
      console.log('🚀 Connecting to server:', server);
      serversConnectService.connectTo(server);
    }
  };

  if (loading) {
    return (
      <Page>
        <Flex fullHeight centered>
          <Text>Đang tải thông tin server...</Text>
        </Flex>
      </Page>
    );
  }

  // Show fallback if no server data
  if (!server) {
    return (
      <Page>
        <Flex fullHeight centered>
          <Box style={{
            background: 'rgba(0, 0, 0, 0.85)',
            padding: '2rem',
            borderRadius: '12px',
            textAlign: 'center',
            maxWidth: '400px',
            border: '1px solid rgba(255, 255, 255, 0.1)'
          }}>
            <div style={{ marginBottom: '0.5rem', color: '#FF8C42' }}>
              <Text size="large" weight="bold">
                🔶 Gang Hai City
              </Text>
            </div>

            <div style={{ marginBottom: '1rem' }}>
              <Text opacity="75" size="small">
                Server: {TARGET_SERVER_ID}
              </Text>
            </div>

            <div style={{ marginBottom: '1.5rem' }}>
              <Text opacity="50" size="small">
                Click the button to connect directly to the server
              </Text>
            </div>

            <div style={{
              background: 'linear-gradient(135deg, #FF8C42 0%, #7CB342 100%)',
              borderRadius: '6px',
              padding: '2px',
              marginBottom: '1rem'
            }}>
              <Button
                size="large"
                theme="primary"
                text="🚀 Kết nối đến Server"
                onClick={() => {
                  console.log('🚀 Connecting to server ID:', TARGET_SERVER_ID);
                  serversConnectService.connectTo(TARGET_SERVER_ID);
                }}
              />
            </div>

            <div>
              <Button
                size="normal"
                theme="default"
                text="🚪 Đăng xuất"
                onClick={() => {
                  console.log('🚪 Logging out...');
                  customAuthService.logout();
                }}
              />
            </div>
          </Box>
        </Flex>
      </Page>
    );
  }

  return (
    <Page>
      <Flex fullHeight centered>
        <Box style={{
          background: 'rgba(0, 0, 0, 0.85)',
          padding: '2rem',
          borderRadius: '12px',
          textAlign: 'center',
          maxWidth: '400px',
          border: '1px solid rgba(255, 255, 255, 0.1)'
        }}>
          <div style={{ marginBottom: '0.5rem', color: '#FF8C42' }}>
            <Text size="large" weight="bold">
              🔶 {server.hostname || server.projectName || 'Custom Server'}
            </Text>
          </div>

          <div style={{ marginBottom: '1rem' }}>
            <Text opacity="75" size="small">
              Server: {server.id || TARGET_SERVER_ID}
            </Text>
          </div>

          <div style={{ marginBottom: '1.5rem' }}>
            <Text opacity="50" size="small">
              {server.projectDescription || 'Click the button to connect directly to the server'}
            </Text>
          </div>

          <div style={{
            background: 'linear-gradient(135deg, #FF8C42 0%, #7CB342 100%)',
            borderRadius: '6px',
            padding: '2px',
            marginBottom: '1rem'
          }}>
            <Button
              size="large"
              theme="primary"
              text="🚀 Kết nối đến Server"
              onClick={handleConnect}
            />
          </div>

          <div>
            <Button
              size="normal"
              theme="default"
              text="🚪 Đăng xuất"
              onClick={() => {
                console.log('🚪 Logging out...');
                customAuthService.logout();
              }}
            />
          </div>

          {(server.playersCurrent !== undefined || server.playersMax !== undefined) && (
            <div style={{ marginTop: '1rem' }}>
              <Text opacity="50" size="small">
                Players: {server.playersCurrent || 0}/{server.playersMax || 0}
              </Text>
            </div>
          )}
        </Box>
      </Flex>
    </Page>
  );
});
