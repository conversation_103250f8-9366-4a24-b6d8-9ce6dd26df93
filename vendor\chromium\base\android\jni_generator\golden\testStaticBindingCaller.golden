// Copyright 2014 The Chromium Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     base/android/jni_generator/jni_generator.py
// For
//     org/chromium/foo/Foo

#ifndef org_chromium_foo_Foo_JNI
#define org_chromium_foo_Foo_JNI

#include <jni.h>

#include "base/android/jni_generator/jni_generator_helper.h"


// Step 1: Forward declarations.

JNI_REGISTRATION_EXPORT extern const char kClassPath_org_chromium_foo_Foo[];
const char kClassPath_org_chromium_foo_Foo[] = "org/chromium/foo/Foo";
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_REGISTRATION_EXPORT std::atomic<jclass> g_org_chromium_foo_Foo_clazz(nullptr);
#ifndef org_chromium_foo_Foo_clazz_defined
#define org_chromium_foo_Foo_clazz_defined
inline jclass org_chromium_foo_Foo_clazz(JNIEnv* env) {
  return base::android::LazyGetClass(env, kClassPath_org_chromium_foo_Foo,
      &g_org_chromium_foo_Foo_clazz);
}
#endif


// Step 2: Constants (optional).


// Step 3: Method stubs.
static void JNI_Foo_ShouldBindCaller(JNIEnv* env, const base::android::JavaParamRef<jobject>&
    caller);

JNI_GENERATOR_EXPORT void Java_org_chromium_foo_Foo_nativeShouldBindCaller(
    JNIEnv* env,
    jclass jcaller,
    jobject caller) {
  return JNI_Foo_ShouldBindCaller(env, base::android::JavaParamRef<jobject>(env, caller));
}

static void JNI_Foo_ShouldBindCaller(JNIEnv* env, const base::android::JavaParamRef<jobject>&
    caller,
    jint a);

JNI_GENERATOR_EXPORT void Java_org_chromium_foo_Foo_nativeShouldBindCaller(
    JNIEnv* env,
    jclass jcaller,
    jobject caller,
    jint a) {
  return JNI_Foo_ShouldBindCaller(env, base::android::JavaParamRef<jobject>(env, caller), a);
}

JNI_GENERATOR_EXPORT void Java_org_chromium_foo_Foo_nativeFoo(
    JNIEnv* env,
    jclass jcaller,
    jlong nativeNativeObject,
    jobject caller) {
  NativeObject* native = reinterpret_cast<NativeObject*>(nativeNativeObject);
  CHECK_NATIVE_PTR(env, jcaller, native, "Foo");
  return native->Foo(env, base::android::JavaParamRef<jobject>(env, caller));
}

JNI_GENERATOR_EXPORT void Java_org_chromium_foo_Foo_nativeFoo(
    JNIEnv* env,
    jclass jcaller,
    jlong nativeNativeObject,
    jobject caller,
    jint a) {
  NativeObject* native = reinterpret_cast<NativeObject*>(nativeNativeObject);
  CHECK_NATIVE_PTR(env, jcaller, native, "Foo");
  return native->Foo(env, base::android::JavaParamRef<jobject>(env, caller), a);
}

JNI_GENERATOR_EXPORT void Java_org_chromium_foo_Foo_nativeCallNativeMethod(
    JNIEnv* env,
    jobject jcaller,
    jlong nativePtr) {
  Ptr* native = reinterpret_cast<Ptr*>(nativePtr);
  CHECK_NATIVE_PTR(env, jcaller, native, "CallNativeMethod");
  return native->CallNativeMethod(env, base::android::JavaParamRef<jobject>(env, jcaller));
}

JNI_GENERATOR_EXPORT void Java_org_chromium_foo_Foo_nativeCallWithQualifiedObject(
    JNIEnv* env,
    jobject jcaller,
    jlong nativePtr) {
  Foo::Bar* native = reinterpret_cast<Foo::Bar*>(nativePtr);
  CHECK_NATIVE_PTR(env, jcaller, native, "CallWithQualifiedObject");
  return native->CallWithQualifiedObject(env, base::android::JavaParamRef<jobject>(env, jcaller));
}


// Step 4: Generated test functions (optional).


#endif  // org_chromium_foo_Foo_JNI
