/*
 * This file is part of the CitizenFX project - http://citizen.re/
 *
 * See LICENSE and MENTIONS in the root of the source tree for information
 * regarding licensing.
 */

#pragma once

#include <cstdint>

namespace rage
{
static constexpr size_t INPUT_PUSH_TO_TALK = 249;

/// parEnumDefinition can be found at "0C 00 01 00 E0 7D 86 3D" - 0x10
enum ioParameterMask : int32_t
{
	IOMT_KEYBOARD = 0x0, // 0xE38B382A
	IOMT_MOUSE_AXIS = 0x200, // 0x140D3D7E
	IOMT_MOUSE_WHEEL = 0x400, // 0x2AE06A4A
	IOMT_MOUSE_BUTTON = 0x800, // 0xEC193160
	IOMT_PAD_AXIS = 0x1000, // 0x5A190AB4
	IOMT_PAD_INDEX = 0x2000, // 0x6A1EB78C
	IOMT_JOYSTICK_POV = 0x4000, // 0x68BA5FB3
	IOMT_JOYSTICK_BUTTON = 0x8000, // 0xB0629D0A
	IOMT_JOYSTICK_AXIS = 0x10000, // 0x2D02D576
	IOMT_PAD_BUTTON = 0x20000, // 0xB1639048
	IOMT_JOYSTICK_AXIS_NEGATIVE = 0x40000, // 0xFC6BC167
	IOMT_JOYSTICK_AXIS_POSITIVE = 0x80000, // 0x0722DC0F
};

enum ioMapperSource : int32_t
{
	IOMS_UNDEFINED = -1,
	IOMS_KEYBOARD = 0,
	IOMS_MOUSE_ABSOLUTEAXIS = 1,
	IOMS_MOUSE_CENTEREDAXIS = 2,
	IOMS_MOUSE_RELATIVEAXIS = 3,
	IOMS_MOUSE_SCALEDAXIS = 4,
	IOMS_MOUSE_NORMALIZED = 5,
	IOMS_MOUSE_WHEEL = 6,
	IOMS_MOUSE_BUTTON = 7,
	IOMS_MOUSE_BUTTONANY = 8,
	IOMS_PAD_DIGITALBUTTON = 9,
	IOMS_PAD_DIGITALBUTTONANY = 10,
	IOMS_PAD_ANALOGBUTTON = 11,
	IOMS_PAD_AXIS = 12,
	IOMS_JOYSTICK_BUTTON = 13,
	IOMS_JOYSTICK_AXIS = 14,
	IOMS_JOYSTICK_IAXIS = 15,
	IOMS_JOYSTICK_AXIS_NEGATIVE = 16,
	IOMS_JOYSTICK_AXIS_POSITIVE = 17,
	IOMS_JOYSTICK_POV = 18,
	IOMS_JOYSTICK_POV_AXIS = 19,
	IOMS_PAD_DEBUGBUTTON = 20,
	IOMS_DIGITALBUTTON_AXIS = 21,
	IOMS_MKB_AXIS = 22,
	IOMS_TOUCHPAD_ABSOLUTE_AXIS = 23,
	IOMS_TOUCHPAD_CENTERED_AXIS = 24,
	IOMS_GAME_CONTROLLED = 25, // Added in prior to b1604
	IOMS_FORCE32 = 2147483647,
};
}
