---
ns: CFX
apiset: shared
---
## GET_NUM_RESOURCE_METADATA

```c
int GET_NUM_RESOURCE_METADATA(char* resourceName, char* metadataKey);
```

Gets the amount of metadata values with the specified key existing in the specified resource's manifest.
See also: [Resource manifest](https://docs.fivem.net/docs/scripting-reference/resource-manifest/resource-manifest/)

## Parameters
* **resourceName**: The resource name.
* **metadataKey**: The key to look up in the resource manifest.

## Return value
