// Copyright 2014 The Chromium Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     base/android/jni_generator/jni_generator.py
// For
//     org/chromium/example/jni_generator/SampleForTests

#ifndef org_chromium_example_jni_generator_SampleForTests_JNI
#define org_chromium_example_jni_generator_SampleForTests_JNI

#include <jni.h>

#include "base/android/jni_generator/jni_generator_helper.h"


// Step 1: Forward declarations.

JNI_REGISTRATION_EXPORT extern const char
    kClassPath_org_chromium_example_jni_1generator_SampleForTests[];
const char kClassPath_org_chromium_example_jni_1generator_SampleForTests[] =
    "org/chromium/example/jni_generator/SampleForTests";

JNI_REGISTRATION_EXPORT extern const char
    kClassPath_org_chromium_example_jni_1generator_SampleForTests_00024MyInnerClass[];
const char kClassPath_org_chromium_example_jni_1generator_SampleForTests_00024MyInnerClass[] =
    "org/chromium/example/jni_generator/SampleForTests$MyInnerClass";

JNI_REGISTRATION_EXPORT extern const char
    kClassPath_org_chromium_example_jni_1generator_SampleForTests_00024MyOtherInnerClass[];
const char kClassPath_org_chromium_example_jni_1generator_SampleForTests_00024MyOtherInnerClass[] =
    "org/chromium/example/jni_generator/SampleForTests$MyOtherInnerClass";
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_REGISTRATION_EXPORT std::atomic<jclass>
    g_org_chromium_example_jni_1generator_SampleForTests_clazz(nullptr);
#ifndef org_chromium_example_jni_1generator_SampleForTests_clazz_defined
#define org_chromium_example_jni_1generator_SampleForTests_clazz_defined
inline jclass org_chromium_example_jni_1generator_SampleForTests_clazz(JNIEnv* env) {
  return base::android::LazyGetClass(env,
      kClassPath_org_chromium_example_jni_1generator_SampleForTests,
      &g_org_chromium_example_jni_1generator_SampleForTests_clazz);
}
#endif
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_REGISTRATION_EXPORT std::atomic<jclass>
    g_org_chromium_example_jni_1generator_SampleForTests_00024MyInnerClass_clazz(nullptr);
#ifndef org_chromium_example_jni_1generator_SampleForTests_00024MyInnerClass_clazz_defined
#define org_chromium_example_jni_1generator_SampleForTests_00024MyInnerClass_clazz_defined
inline jclass org_chromium_example_jni_1generator_SampleForTests_00024MyInnerClass_clazz(JNIEnv*
    env) {
  return base::android::LazyGetClass(env,
      kClassPath_org_chromium_example_jni_1generator_SampleForTests_00024MyInnerClass,
      &g_org_chromium_example_jni_1generator_SampleForTests_00024MyInnerClass_clazz);
}
#endif
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_REGISTRATION_EXPORT std::atomic<jclass>
    g_org_chromium_example_jni_1generator_SampleForTests_00024MyOtherInnerClass_clazz(nullptr);
#ifndef org_chromium_example_jni_1generator_SampleForTests_00024MyOtherInnerClass_clazz_defined
#define org_chromium_example_jni_1generator_SampleForTests_00024MyOtherInnerClass_clazz_defined
inline jclass
    org_chromium_example_jni_1generator_SampleForTests_00024MyOtherInnerClass_clazz(JNIEnv* env) {
  return base::android::LazyGetClass(env,
      kClassPath_org_chromium_example_jni_1generator_SampleForTests_00024MyOtherInnerClass,
      &g_org_chromium_example_jni_1generator_SampleForTests_00024MyOtherInnerClass_clazz);
}
#endif


// Step 2: Constants (optional).


// Step 3: Method stubs.
JNI_GENERATOR_EXPORT jint
    Java_org_chromium_example_jni_1generator_SampleForTests_nativeStaticMethod(
    JNIEnv* env,
    jclass jcaller,
    jlong nativeTest,
    jint arg1) {
  Test* native = reinterpret_cast<Test*>(nativeTest);
  CHECK_NATIVE_PTR(env, jcaller, native, "StaticMethod", 0);
  return native->StaticMethod(env, arg1);
}

JNI_GENERATOR_EXPORT jint Java_org_chromium_example_jni_1generator_SampleForTests_nativeMethod(
    JNIEnv* env,
    jobject jcaller,
    jlong nativeTest,
    jint arg1) {
  Test* native = reinterpret_cast<Test*>(nativeTest);
  CHECK_NATIVE_PTR(env, jcaller, native, "Method", 0);
  return native->Method(env, base::android::JavaParamRef<jobject>(env, jcaller), arg1);
}

static jint JNI_MyInnerClass_Init(JNIEnv* env, const base::android::JavaParamRef<jobject>& jcaller);

JNI_GENERATOR_EXPORT jint
    Java_org_chromium_example_jni_1generator_SampleForTests_00024MyInnerClass_nativeInit(
    JNIEnv* env,
    jobject jcaller) {
  return JNI_MyInnerClass_Init(env, base::android::JavaParamRef<jobject>(env, jcaller));
}

static jint JNI_MyOtherInnerClass_Init(JNIEnv* env, const base::android::JavaParamRef<jobject>&
    jcaller);

JNI_GENERATOR_EXPORT jint
    Java_org_chromium_example_jni_1generator_SampleForTests_00024MyOtherInnerClass_nativeInit(
    JNIEnv* env,
    jobject jcaller) {
  return JNI_MyOtherInnerClass_Init(env, base::android::JavaParamRef<jobject>(env, jcaller));
}


static std::atomic<jmethodID>
    g_org_chromium_example_jni_1generator_SampleForTests_testMethodWithParam(nullptr);
static void Java_SampleForTests_testMethodWithParam(JNIEnv* env, const
    base::android::JavaRef<jobject>& obj, JniIntWrapper iParam) {
  jclass clazz = org_chromium_example_jni_1generator_SampleForTests_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_chromium_example_jni_1generator_SampleForTests_clazz(env));

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "testMethodWithParam",
          "(I)V",
          &g_org_chromium_example_jni_1generator_SampleForTests_testMethodWithParam);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id, as_jint(iParam));
}

static std::atomic<jmethodID>
    g_org_chromium_example_jni_1generator_SampleForTests_testMethodWithParamAndReturn(nullptr);
static base::android::ScopedJavaLocalRef<jstring>
    Java_SampleForTests_testMethodWithParamAndReturn(JNIEnv* env, const
    base::android::JavaRef<jobject>& obj, JniIntWrapper iParam) {
  jclass clazz = org_chromium_example_jni_1generator_SampleForTests_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_chromium_example_jni_1generator_SampleForTests_clazz(env), NULL);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "testMethodWithParamAndReturn",
          "(I)Ljava/lang/String;",
          &g_org_chromium_example_jni_1generator_SampleForTests_testMethodWithParamAndReturn);

  jstring ret =
      static_cast<jstring>(env->CallObjectMethod(obj.obj(),
          call_context.base.method_id, as_jint(iParam)));
  return base::android::ScopedJavaLocalRef<jstring>(env, ret);
}

static std::atomic<jmethodID>
    g_org_chromium_example_jni_1generator_SampleForTests_testStaticMethodWithParam(nullptr);
static jint Java_SampleForTests_testStaticMethodWithParam(JNIEnv* env, JniIntWrapper iParam) {
  jclass clazz = org_chromium_example_jni_1generator_SampleForTests_clazz(env);
  CHECK_CLAZZ(env, clazz,
      org_chromium_example_jni_1generator_SampleForTests_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "testStaticMethodWithParam",
          "(I)I",
          &g_org_chromium_example_jni_1generator_SampleForTests_testStaticMethodWithParam);

  jint ret =
      env->CallStaticIntMethod(clazz,
          call_context.base.method_id, as_jint(iParam));
  return ret;
}

static std::atomic<jmethodID>
    g_org_chromium_example_jni_1generator_SampleForTests_testMethodWithNoParam(nullptr);
static jdouble Java_SampleForTests_testMethodWithNoParam(JNIEnv* env) {
  jclass clazz = org_chromium_example_jni_1generator_SampleForTests_clazz(env);
  CHECK_CLAZZ(env, clazz,
      org_chromium_example_jni_1generator_SampleForTests_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "testMethodWithNoParam",
          "()D",
          &g_org_chromium_example_jni_1generator_SampleForTests_testMethodWithNoParam);

  jdouble ret =
      env->CallStaticDoubleMethod(clazz,
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID>
    g_org_chromium_example_jni_1generator_SampleForTests_testStaticMethodWithNoParam(nullptr);
static base::android::ScopedJavaLocalRef<jstring>
    Java_SampleForTests_testStaticMethodWithNoParam(JNIEnv* env) {
  jclass clazz = org_chromium_example_jni_1generator_SampleForTests_clazz(env);
  CHECK_CLAZZ(env, clazz,
      org_chromium_example_jni_1generator_SampleForTests_clazz(env), NULL);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "testStaticMethodWithNoParam",
          "()Ljava/lang/String;",
          &g_org_chromium_example_jni_1generator_SampleForTests_testStaticMethodWithNoParam);

  jstring ret =
      static_cast<jstring>(env->CallStaticObjectMethod(clazz,
          call_context.base.method_id));
  return base::android::ScopedJavaLocalRef<jstring>(env, ret);
}

// Step 4: Generated test functions (optional).


#endif  // org_chromium_example_jni_generator_SampleForTests_JNI
