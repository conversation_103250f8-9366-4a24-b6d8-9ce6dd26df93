
# Boost.Context Library Build Jamfile

#          Copyright Oliver Kowalke 2009.
# Distributed under the Boost Software License, Version 1.0.
#    (See accompanying file LICENSE_1_0.txt or copy at
#          http://www.boost.org/LICENSE_1_0.txt)

import common ;
import feature ;
import indirect ;
import modules ;
import os ;
import toolset ;
 import ../../config/checks/config : requires ;

feature.feature segmented-stacks : on : optional propagated composite ;
feature.compose <segmented-stacks>on : <define>BOOST_USE_SEGMENTED_STACKS ;

feature.feature htm : tsx : optional propagated composite ;
feature.compose <htm>tsx : <define>BOOST_USE_TSX ;

feature.feature valgrind : on : optional propagated composite ;
feature.compose <valgrind>on : <define>BOOST_USE_VALGRIND ;

project boost/context
    : requirements
      <target-os>windows:<define>_WIN32_WINNT=0x0601
      <target-os>linux,<toolset>gcc,<segmented-stacks>on:<cxxflags>-fsplit-stack
      <target-os>linux,<toolset>gcc,<segmented-stacks>on:<cxxflags>-DBOOST_USE_SEGMENTED_STACKS
      <toolset>gcc,<segmented-stacks>on:<linkflags>"-static-libgcc"
      <toolset>clang,<segmented-stacks>on:<cxxflags>-fsplit-stack
      <toolset>clang,<segmented-stacks>on:<cxxflags>-DBOOST_USE_SEGMENTED_STACKS
      <toolset>clang,<segmented-stacks>on:<linkflags>"-static-libgcc"
      <toolset>intel,<link>shared:<define>BOOST_CONTEXT_EXPORT=EXPORT
      <toolset>intel,<link>static:<define>BOOST_CONTEXT_EXPORT=
      <toolset>msvc,<link>shared:<define>BOOST_CONTEXT_EXPORT=EXPORT
      <toolset>msvc,<link>static:<define>BOOST_CONTEXT_EXPORT=
      <toolset>clang-win,<link>shared:<define>BOOST_CONTEXT_EXPORT=EXPORT
      <toolset>clang-win,<link>static:<define>BOOST_CONTEXT_EXPORT=
      <toolset>embarcadero,<address-model>32,<link>shared:<define>BOOST_CONTEXT_EXPORT=EXPORT
      <toolset>embarcadero,<address-model>32,<link>static:<define>BOOST_CONTEXT_EXPORT=
      <toolset>borland,<link>shared:<define>BOOST_CONTEXT_EXPORT=EXPORT
      <toolset>borland,<link>static:<define>BOOST_CONTEXT_EXPORT=
      <link>shared:<define>BOOST_CONTEXT_DYN_LINK=1
      <define>BOOST_CONTEXT_SOURCE
      <threading>multi
      <toolset>msvc,<address-model>32:<asmflags>/safeseh
    : usage-requirements
      <link>shared:<define>BOOST_CONTEXT_DYN_LINK=1
      <optimization>speed:<define>BOOST_DISABLE_ASSERTS
      <variant>release:<define>BOOST_DISABLE_ASSERTS
    : source-location ../src
    ;


local rule default_binary_format ( )
{
    local tmp = elf ;
    if [ os.name ] = "NT" { tmp = pe ; }
    else if [ os.name ] = "CYGWIN" { tmp = pe ; }
    else if [ os.name ] = "AIX" { tmp = xcoff ; }
    else if [ os.name ] = "MACOSX" { tmp = mach-o ; }
    return $(tmp) ;
}

feature.feature binary-format
   : elf
     mach-o
     pe
     xcoff
   : propagated
   ;
feature.set-default binary-format : [ default_binary_format ] ;


local rule default_abi ( )
{
    local tmp = sysv ;
    if [ os.name ] = "NT" { tmp = ms ; }
    else if [ os.name ] = "CYGWIN" { tmp = ms ; }
    else if [ os.platform ] = "ARM" { tmp = aapcs ; }
    else if [ os.platform ] = "ARM64" { tmp = aapcs ; }
    else if [ os.platform ] = "MIPS32" { tmp = o32 ; }
    else if [ os.platform ] = "MIPS64" { tmp = n64 ; }
    return $(tmp) ;
}

feature.feature abi
   : aapcs
     eabi
     ms
     n32
     n64
     o32
     o64
     sysv
     x32
   : propagated
   ;
feature.set-default abi : [ default_abi ] ;


feature.feature context-impl
    : fcontext
      ucontext
      winfib
    : propagated
      composite
    ;
feature.set-default context-impl : fcontext ;
feature.compose <context-impl>ucontext : <define>BOOST_USE_UCONTEXT ;
feature.compose <context-impl>winfib : <define>BOOST_USE_WINFIB ;

# ARM
# ARM/AAPCS/ELF
alias asm_sources
   : asm/make_arm_aapcs_elf_gas.S
     asm/jump_arm_aapcs_elf_gas.S
     asm/ontop_arm_aapcs_elf_gas.S
   : <abi>aapcs
     <address-model>32
     <architecture>arm
     <binary-format>elf
     <toolset>clang
   ;

alias asm_sources
   : asm/make_arm_aapcs_elf_gas.S
     asm/jump_arm_aapcs_elf_gas.S
     asm/ontop_arm_aapcs_elf_gas.S
   : <abi>aapcs
     <address-model>32
     <architecture>arm
     <binary-format>elf
     <toolset>gcc
   ;

alias asm_sources
   : asm/make_arm_aapcs_elf_gas.S
     asm/jump_arm_aapcs_elf_gas.S
     asm/ontop_arm_aapcs_elf_gas.S
   : <abi>aapcs
     <address-model>32
     <architecture>arm
     <binary-format>elf
     <toolset>qcc
   ;

# ARM/AAPCS/MACH-O
alias asm_sources
   : asm/make_arm_aapcs_macho_gas.S
     asm/jump_arm_aapcs_macho_gas.S
     asm/ontop_arm_aapcs_macho_gas.S
   : <abi>aapcs
     <address-model>32
     <architecture>arm
     <binary-format>mach-o
     <toolset>clang
   ;

alias asm_sources
   : asm/make_arm_aapcs_macho_gas.S
     asm/jump_arm_aapcs_macho_gas.S
     asm/ontop_arm_aapcs_macho_gas.S
   : <abi>aapcs
     <address-model>32
     <architecture>arm
     <binary-format>mach-o
     <toolset>darwin
   ;

# ARM/AAPCS/PE
alias asm_sources
   : asm/make_arm_aapcs_pe_armasm.asm
     asm/jump_arm_aapcs_pe_armasm.asm
     asm/ontop_arm_aapcs_pe_armasm.asm
     untested.cpp
   : <abi>aapcs
     <address-model>32
     <architecture>arm
     <binary-format>pe
     <toolset>msvc
   ;

# ARM64
# ARM64/AAPCS/ELF
alias asm_sources
   : asm/make_arm64_aapcs_elf_gas.S
     asm/jump_arm64_aapcs_elf_gas.S
     asm/ontop_arm64_aapcs_elf_gas.S
   : <abi>aapcs
     <address-model>64
     <architecture>arm
     <binary-format>elf
     <toolset>clang
   ;

alias asm_sources
   : asm/make_arm64_aapcs_elf_gas.S
     asm/jump_arm64_aapcs_elf_gas.S
     asm/ontop_arm64_aapcs_elf_gas.S
   : <abi>aapcs
     <address-model>64
     <architecture>arm
     <binary-format>elf
     <toolset>gcc
   ;

# ARM64/AAPCS/MACH-O
alias asm_sources
   : asm/make_arm64_aapcs_macho_gas.S
     asm/jump_arm64_aapcs_macho_gas.S
     asm/ontop_arm64_aapcs_macho_gas.S
   : <abi>aapcs
     <address-model>64
     <architecture>arm
     <binary-format>mach-o
     <toolset>clang
   ;

alias asm_sources
   : asm/make_arm64_aapcs_macho_gas.S
     asm/jump_arm64_aapcs_macho_gas.S
     asm/ontop_arm64_aapcs_macho_gas.S
   : <abi>aapcs
     <address-model>64
     <architecture>arm
     <binary-format>mach-o
     <toolset>darwin
   ;

alias asm_sources
   : asm/make_arm64_aapcs_macho_gas.S
     asm/jump_arm64_aapcs_macho_gas.S
     asm/ontop_arm64_aapcs_macho_gas.S
   : <abi>aapcs
     <address-model>64
     <architecture>arm
     <binary-format>mach-o
     <toolset>gcc
   ;

# ARM64/AAPCS/PE
alias asm_sources
   : asm/make_arm64_aapcs_pe_armasm.asm
     asm/jump_arm64_aapcs_pe_armasm.asm
     asm/ontop_arm64_aapcs_pe_armasm.asm
   : <abi>aapcs
     <address-model>64
     <architecture>arm
     <binary-format>pe
     <toolset>msvc
   ;

# LOONGARCH64
# LOONGARCH64/SYSV/ELF
alias asm_sources
   : asm/make_loongarch64_sysv_elf_gas.S
     asm/jump_loongarch64_sysv_elf_gas.S
     asm/ontop_loongarch64_sysv_elf_gas.S
   : <abi>sysv
     <address-model>64
     <architecture>loongarch
     <binary-format>elf
     <toolset>gcc
   ;

# MIPS
# MIPS32/O32/ELF
alias asm_sources
   : asm/make_mips32_o32_elf_gas.S
     asm/jump_mips32_o32_elf_gas.S
     asm/ontop_mips32_o32_elf_gas.S
   : <abi>o32
     <address-model>32
     <architecture>mips
     <binary-format>elf
     <toolset>clang
   ;

alias asm_sources
   : asm/make_mips32_o32_elf_gas.S
     asm/jump_mips32_o32_elf_gas.S
     asm/ontop_mips32_o32_elf_gas.S
   : <abi>o32
     <address-model>32
     <architecture>mips
     <binary-format>elf
     <toolset>gcc
   ;

# MIPS64/N64/ELF
alias asm_sources
   : asm/make_mips64_n64_elf_gas.S
     asm/jump_mips64_n64_elf_gas.S
     asm/ontop_mips64_n64_elf_gas.S
   : <abi>n64
     <address-model>64
     <architecture>mips
     <binary-format>elf
     <toolset>clang
   ;

alias asm_sources
   : asm/make_mips64_n64_elf_gas.S
     asm/jump_mips64_n64_elf_gas.S
     asm/ontop_mips64_n64_elf_gas.S
   : <abi>n64
     <address-model>64
     <architecture>mips
     <binary-format>elf
     <toolset>gcc
   ;

# POWERPC_32
# POWERPC_32/SYSV/ELF
alias asm_sources
   : asm/make_ppc32_sysv_elf_gas.S
     asm/jump_ppc32_sysv_elf_gas.S
     asm/ontop_ppc32_sysv_elf_gas.S
     asm/tail_ontop_ppc32_sysv.cpp
   : <abi>sysv
     <address-model>32
     <architecture>power
     <binary-format>elf
     <toolset>clang
   ;

alias asm_sources
   : asm/make_ppc32_sysv_elf_gas.S
     asm/jump_ppc32_sysv_elf_gas.S
     asm/ontop_ppc32_sysv_elf_gas.S
     asm/tail_ontop_ppc32_sysv.cpp
   : <abi>sysv
     <address-model>32
     <architecture>power
     <binary-format>elf
     <toolset>gcc
   ;

# POWERPC_32/SYSV/MACH-O
alias asm_sources
   : asm/make_ppc32_sysv_macho_gas.S
     asm/jump_ppc32_sysv_macho_gas.S
     asm/ontop_ppc32_sysv_macho_gas.S
     asm/tail_ontop_ppc32_sysv.cpp
   : <abi>sysv
     <address-model>32
     <architecture>power
     <binary-format>mach-o
     <toolset>clang
   ;

alias asm_sources
   : asm/make_ppc32_sysv_macho_gas.S
     asm/jump_ppc32_sysv_macho_gas.S
     asm/ontop_ppc32_sysv_macho_gas.S
     asm/tail_ontop_ppc32_sysv.cpp
   : <abi>sysv
     <address-model>32
     <architecture>power
     <binary-format>mach-o
     <toolset>gcc
   ;

alias asm_sources
   : asm/make_ppc32_sysv_macho_gas.S
     asm/jump_ppc32_sysv_macho_gas.S
     asm/ontop_ppc32_sysv_macho_gas.S
     asm/tail_ontop_ppc32_sysv.cpp
   : <abi>sysv
     <address-model>32
     <architecture>power
     <binary-format>mach-o
     <toolset>darwin
   ;

#POWERPC_32/SYSV/XCOFF
alias asm_sources
   : asm/make_ppc32_sysv_xcoff_gas.S
     asm/jump_ppc32_sysv_xcoff_gas.S
     asm/ontop_ppc32_sysv_xcoff_gas.S
   : <abi>sysv
     <address-model>32
     <architecture>power
     <binary-format>xcoff
     <toolset>clang
   ;

alias asm_sources
   : asm/make_ppc32_sysv_xcoff_gas.S
     asm/jump_ppc32_sysv_xcoff_gas.S
     asm/ontop_ppc32_sysv_xcoff_gas.S
   : <abi>sysv
     <address-model>32
     <architecture>power
     <binary-format>xcoff
     <toolset>gcc
   ;

# POWERPC_64
# POWERPC_64/SYSV/ELF
alias asm_sources
   : asm/make_ppc64_sysv_elf_gas.S
     asm/jump_ppc64_sysv_elf_gas.S
     asm/ontop_ppc64_sysv_elf_gas.S
   : <abi>sysv
     <address-model>64
     <architecture>power
     <binary-format>elf
     <toolset>clang
   ;

alias asm_sources
   : asm/make_ppc64_sysv_elf_gas.S
     asm/jump_ppc64_sysv_elf_gas.S
     asm/ontop_ppc64_sysv_elf_gas.S
   : <abi>sysv
     <address-model>64
     <architecture>power
     <binary-format>elf
     <toolset>gcc
   ;

# POWERPC_64/SYSV/MACH-O
alias asm_sources
   : asm/make_ppc64_sysv_macho_gas.S
     asm/jump_ppc64_sysv_macho_gas.S
     asm/ontop_ppc64_sysv_macho_gas.S
     untested.cpp
   : <abi>sysv
     <address-model>64
     <architecture>power
     <binary-format>mach-o
     <toolset>clang
   ;

alias asm_sources
   : asm/make_ppc64_sysv_macho_gas.S
     asm/jump_ppc64_sysv_macho_gas.S
     asm/ontop_ppc64_sysv_macho_gas.S
     untested.cpp
   : <abi>sysv
     <address-model>64
     <architecture>power
     <binary-format>mach-o
     <toolset>gcc
   ;

alias asm_sources
   : asm/make_ppc64_sysv_macho_gas.S
     asm/jump_ppc64_sysv_macho_gas.S
     asm/ontop_ppc64_sysv_macho_gas.S
     untested.cpp
   : <abi>sysv
     <address-model>64
     <architecture>power
     <binary-format>mach-o
     <toolset>darwin
   ;

# POWERPC_64/SYSV/XCOFF
alias asm_sources
   : asm/make_ppc64_sysv_xcoff_gas.S
     asm/jump_ppc64_sysv_xcoff_gas.S
     asm/ontop_ppc64_sysv_xcoff_gas.S
   : <abi>sysv
     <address-model>64
     <architecture>power
     <binary-format>xcoff
     <toolset>clang
   ;

alias asm_sources
   : asm/make_ppc64_sysv_xcoff_gas.S
     asm/jump_ppc64_sysv_xcoff_gas.S
     asm/ontop_ppc64_sysv_xcoff_gas.S
   : <abi>sysv
     <address-model>64
     <architecture>power
     <binary-format>xcoff
     <toolset>gcc
   ;

# POWERPC universal
# POWERPC_32_64/SYSV/MACH-O
alias asm_sources
   : asm/make_ppc32_ppc64_sysv_macho_gas.S
     asm/jump_ppc32_ppc64_sysv_macho_gas.S
     asm/ontop_ppc32_ppc64_sysv_macho_gas.S
   : <abi>sysv
     <address-model>32_64
     <architecture>power
     <binary-format>mach-o
   ;

# RISCV64
# RISCV64/SYSV/ELF
alias asm_sources
   : asm/make_riscv64_sysv_elf_gas.S
     asm/jump_riscv64_sysv_elf_gas.S
     asm/ontop_riscv64_sysv_elf_gas.S
   : <abi>sysv
     <address-model>64
     <architecture>riscv
     <binary-format>elf
     <toolset>clang
   ;

alias asm_sources
   : asm/make_riscv64_sysv_elf_gas.S
     asm/jump_riscv64_sysv_elf_gas.S
     asm/ontop_riscv64_sysv_elf_gas.S
   : <abi>sysv
     <address-model>64
     <architecture>riscv
     <binary-format>elf
     <toolset>gcc
   ;

# SPARC64
# SPARC64/SYSV/ELF
alias asm_sources
   : asm/make_sparc64_sysv_elf_gas.S
     asm/jump_sparc64_sysv_elf_gas.S
     asm/ontop_sparc64_sysv_elf_gas.S
   : <abi>sysv
     <address-model>64
     <architecture>sparc
     <binary-format>elf
     <toolset>clang
   ;

alias asm_sources
   : asm/make_sparc64_sysv_elf_gas.S
     asm/jump_sparc64_sysv_elf_gas.S
     asm/ontop_sparc64_sysv_elf_gas.S
   : <abi>sysv
     <address-model>64
     <architecture>sparc
     <binary-format>elf
     <toolset>gcc
   ;

# S390X
# S390X/SYSV/ELF
alias asm_sources
   : asm/make_s390x_sysv_elf_gas.S
     asm/jump_s390x_sysv_elf_gas.S
     asm/ontop_s390x_sysv_elf_gas.S
   : <abi>sysv
     <address-model>64
     <architecture>s390x
     <binary-format>elf
     <toolset>gcc
   ;

alias asm_sources
   : asm/make_s390x_sysv_elf_gas.S
     asm/jump_s390x_sysv_elf_gas.S
     asm/ontop_s390x_sysv_elf_gas.S
   : <abi>sysv
     <address-model>64
     <architecture>s390x
     <binary-format>elf
     <toolset>clang
   ;

# X86
# X86/SYSV/ELF
alias asm_sources
   : asm/make_i386_sysv_elf_gas.S
     asm/jump_i386_sysv_elf_gas.S
     asm/ontop_i386_sysv_elf_gas.S
   : <abi>sysv
     <address-model>32
     <architecture>x86
     <binary-format>elf
     <toolset>clang
   ;

alias asm_sources
   : asm/make_i386_sysv_elf_gas.S
     asm/jump_i386_sysv_elf_gas.S
     asm/ontop_i386_sysv_elf_gas.S
   : <abi>sysv
     <address-model>32
     <architecture>x86
     <binary-format>elf
     <toolset>gcc
   ;

alias asm_sources
   : asm/make_i386_sysv_elf_gas.S
     asm/jump_i386_sysv_elf_gas.S
     asm/ontop_i386_sysv_elf_gas.S
   : <abi>sysv
     <address-model>32
     <architecture>x86
     <binary-format>elf
     <toolset>intel
   ;

# X86/SYSV/MACH-O
alias asm_sources
   : asm/make_i386_sysv_macho_gas.S
     asm/jump_i386_sysv_macho_gas.S
     asm/ontop_i386_sysv_macho_gas.S
   : <abi>sysv
     <address-model>32
     <architecture>x86
     <binary-format>mach-o
     <toolset>clang
   ;

alias asm_sources
   : asm/make_i386_sysv_macho_gas.S
     asm/jump_i386_sysv_macho_gas.S
     asm/ontop_i386_sysv_macho_gas.S
   : <abi>sysv
     <address-model>32
     <architecture>x86
     <binary-format>mach-o
     <toolset>darwin
   ;

# X86/MS/PE
alias asm_sources
   : asm/make_i386_ms_pe_gas.asm
     asm/jump_i386_ms_pe_gas.asm
     asm/ontop_i386_ms_pe_gas.asm
     dummy.cpp
   : <abi>ms
     <address-model>32
     <architecture>x86
     <binary-format>pe
     <toolset>clang
   ;

alias asm_sources
   : asm/make_i386_ms_pe_masm.asm
     asm/jump_i386_ms_pe_masm.asm
     asm/ontop_i386_ms_pe_masm.asm
     dummy.cpp
   : <abi>ms
     <address-model>32
     <architecture>x86
     <binary-format>pe
     <toolset>clang-win
   ;

alias asm_sources
   : asm/make_i386_ms_pe_gas.asm
     asm/jump_i386_ms_pe_gas.asm
     asm/ontop_i386_ms_pe_gas.asm
     dummy.cpp
   : <abi>ms
     <address-model>32
     <architecture>x86
     <binary-format>pe
     <toolset>gcc
   ;

alias asm_sources
   : asm/make_i386_ms_pe_masm.asm
     asm/jump_i386_ms_pe_masm.asm
     asm/ontop_i386_ms_pe_masm.asm
     dummy.cpp
   : <abi>ms
     <address-model>32
     <architecture>x86
     <binary-format>pe
     <toolset>intel
   ;

alias asm_sources
   : asm/make_i386_ms_pe_masm.asm
     asm/jump_i386_ms_pe_masm.asm
     asm/ontop_i386_ms_pe_masm.asm
     dummy.cpp
   : <abi>ms
     <address-model>32
     <architecture>x86
     <binary-format>pe
     <toolset>msvc
   ;

# Embarcadero on Windows for 32-bits, bcc32x, uses tasm32,
# which is masm compatible
# This is configured in the embarcadero toolset as using
# 'tasm32 /ml'

alias asm_sources
   : asm/make_i386_ms_pe_masm.asm
     asm/jump_i386_ms_pe_masm.asm
     asm/ontop_i386_ms_pe_masm.asm
     dummy.cpp
   : <abi>ms
     <address-model>32
     <architecture>x86
     <binary-format>pe
     <toolset>embarcadero
   ;

# Borland on Windows for 32-bits, bcc32c, uses tasm32,
# which is masm compatible
# This is configured in the borland toolset as using
# 'tasm32 /ml'

alias asm_sources
   : asm/make_i386_ms_pe_masm.asm
     asm/jump_i386_ms_pe_masm.asm
     asm/ontop_i386_ms_pe_masm.asm
     dummy.cpp
   : <abi>ms
     <address-model>32
     <architecture>x86
     <binary-format>pe
     <toolset>borland
   ;

# X86_64
# X86_64/SYSV/ELF
alias asm_sources
   : asm/make_x86_64_sysv_elf_gas.S
     asm/jump_x86_64_sysv_elf_gas.S
     asm/ontop_x86_64_sysv_elf_gas.S
   : <abi>sysv
     <address-model>64
     <architecture>x86
     <binary-format>elf
     <toolset>clang
   ;

alias asm_sources
   : asm/make_x86_64_sysv_elf_gas.S
     asm/jump_x86_64_sysv_elf_gas.S
     asm/ontop_x86_64_sysv_elf_gas.S
   : <abi>sysv
     <address-model>64
     <architecture>x86
     <binary-format>elf
     <toolset>gcc
   ;

alias asm_sources
   : asm/make_x86_64_sysv_elf_gas.S
     asm/jump_x86_64_sysv_elf_gas.S
     asm/ontop_x86_64_sysv_elf_gas.S
   : <abi>sysv
     <address-model>64
     <architecture>x86
     <binary-format>elf
     <toolset>intel
   ;

# X86_64/SYSV/MACH-O
alias asm_sources
   : asm/make_x86_64_sysv_macho_gas.S
     asm/jump_x86_64_sysv_macho_gas.S
     asm/ontop_x86_64_sysv_macho_gas.S
   : <abi>sysv
     <address-model>64
     <architecture>x86
     <binary-format>mach-o
     <toolset>clang
   ;

alias asm_sources
   : asm/make_x86_64_sysv_macho_gas.S
     asm/jump_x86_64_sysv_macho_gas.S
     asm/ontop_x86_64_sysv_macho_gas.S
   : <abi>sysv
     <address-model>64
     <architecture>x86
     <binary-format>mach-o
     <toolset>gcc
   ;

alias asm_sources
   : asm/make_x86_64_sysv_macho_gas.S
     asm/jump_x86_64_sysv_macho_gas.S
     asm/ontop_x86_64_sysv_macho_gas.S
   : <abi>sysv
     <address-model>64
     <architecture>x86
     <binary-format>mach-o
     <toolset>darwin
   ;

alias asm_sources
   : asm/make_x86_64_sysv_macho_gas.S
     asm/jump_x86_64_sysv_macho_gas.S
     asm/ontop_x86_64_sysv_macho_gas.S
   : <abi>sysv
     <address-model>64
     <architecture>x86
     <binary-format>mach-o
     <toolset>intel
   ;

# X86_64/MS/PE
alias asm_sources
   : asm/make_x86_64_ms_pe_gas.asm
     asm/jump_x86_64_ms_pe_gas.asm
     asm/ontop_x86_64_ms_pe_gas.asm
     dummy.cpp
   : <abi>ms
     <address-model>64
     <architecture>x86
     <binary-format>pe
     <toolset>clang
   ;

alias asm_sources
   : asm/make_x86_64_ms_pe_masm.asm
     asm/jump_x86_64_ms_pe_masm.asm
     asm/ontop_x86_64_ms_pe_masm.asm
     dummy.cpp
   : <abi>ms
     <address-model>64
     <architecture>x86
     <binary-format>pe
     <toolset>clang-win
   ;

alias asm_sources
   : asm/make_x86_64_ms_pe_gas.asm
     asm/jump_x86_64_ms_pe_gas.asm
     asm/ontop_x86_64_ms_pe_gas.asm
     dummy.cpp
   : <abi>ms
     <address-model>64
     <architecture>x86
     <binary-format>pe
     <toolset>gcc
   ;

alias asm_sources
   : asm/make_x86_64_ms_pe_masm.asm
     asm/jump_x86_64_ms_pe_masm.asm
     asm/ontop_x86_64_ms_pe_masm.asm
     dummy.cpp
   : <abi>ms
     <address-model>64
     <architecture>x86
     <binary-format>pe
     <toolset>intel
   ;

alias asm_sources
   : asm/make_x86_64_ms_pe_masm.asm
     asm/jump_x86_64_ms_pe_masm.asm
     asm/ontop_x86_64_ms_pe_masm.asm
     dummy.cpp
   : <abi>ms
     <address-model>64
     <architecture>x86
     <binary-format>pe
     <toolset>msvc
   ;
   
# Embarcadero on Windows, bcc64, uses the sysv-elf format for 64-bits
# This is configured in the embarcadero toolset as using bcc64 with the
# command line option '-x assembler-with-cpp'.

alias asm_sources
   : asm/make_x86_64_sysv_elf_gas.S
     asm/jump_x86_64_sysv_elf_gas.S
     asm/ontop_x86_64_sysv_elf_gas.S
   : <abi>ms
     <address-model>64
     <architecture>x86
     <binary-format>pe
     <toolset>embarcadero
   ;

# X86_64/SYSV/X32
alias asm_sources
   : asm/make_x86_64_sysv_elf_gas.S
     asm/jump_x86_64_sysv_elf_gas.S
     asm/ontop_x86_64_sysv_elf_gas.S
   : <abi>x32
     <address-model>32
     <architecture>x86
     <binary-format>elf
     <toolset>clang
   ;

alias asm_sources
   : asm/make_x86_64_sysv_elf_gas.S
     asm/jump_x86_64_sysv_elf_gas.S
     asm/ontop_x86_64_sysv_elf_gas.S
   : <abi>x32
     <address-model>32
     <architecture>x86
     <binary-format>elf
     <toolset>gcc
   ;

alias asm_sources
   : asm/make_x86_64_sysv_elf_gas.S
     asm/jump_x86_64_sysv_elf_gas.S
     asm/ontop_x86_64_sysv_elf_gas.S
   : <abi>x32
     <address-model>32
     <architecture>x86
     <binary-format>elf
     <toolset>intel
   ;

#X86 universal
alias asm_sources
   : asm/make_i386_x86_64_sysv_macho_gas.S
     asm/jump_i386_x86_64_sysv_macho_gas.S
     asm/ontop_i386_x86_64_sysv_macho_gas.S
   : <abi>sysv
     <address-model>32_64
     <architecture>x86
     <binary-format>mach-o
   ;

# COMBINED
alias asm_sources
   : asm/make_combined_sysv_macho_gas.S
     asm/jump_combined_sysv_macho_gas.S
     asm/ontop_combined_sysv_macho_gas.S
   : <abi>sysv
     <architecture>arm+x86
     <binary-format>mach-o
   ;

explicit asm_sources ;


# fcontext_t
alias impl_sources
    : asm_sources
    : <context-impl>fcontext
    ;

# ucontext_t
alias impl_sources
    : continuation.cpp
      fiber.cpp
    : <context-impl>ucontext
    [ requires cxx11_auto_declarations
               cxx11_constexpr
               cxx11_defaulted_functions
               cxx11_final
               cxx11_hdr_thread
               cxx11_hdr_tuple
               cxx11_lambdas
               cxx11_noexcept
               cxx11_nullptr
               cxx11_rvalue_references
               cxx11_template_aliases
               cxx11_thread_local
               cxx11_variadic_templates ]
    ;

# WinFiber
alias impl_sources
    : continuation.cpp
      fiber.cpp
    : <context-impl>winfib
    [ requires cxx11_auto_declarations
               cxx11_constexpr
               cxx11_defaulted_functions
               cxx11_final
               cxx11_hdr_thread
               cxx11_hdr_tuple
               cxx11_lambdas
               cxx11_noexcept
               cxx11_nullptr
               cxx11_rvalue_references
               cxx11_template_aliases
               cxx11_thread_local
               cxx11_variadic_templates ]
    ;

explicit impl_sources ;

alias stack_traits_sources
    : windows/stack_traits.cpp
    : <target-os>windows
    ;

alias stack_traits_sources
    : posix/stack_traits.cpp
    ;

explicit stack_traits_sources ;

lib boost_context
   : impl_sources
     stack_traits_sources
   ;

boost-install boost_context ;
