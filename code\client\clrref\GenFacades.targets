<?xml version="1.0" encoding="utf-8"?>
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <UsingTask
    TaskName="GenFacadesTask"
    AssemblyFile="$(ProjectDir)\packages\Microsoft.DotNet.GenFacades.6.0.0-beta.21063.5\tools\net472\Microsoft.DotNet.GenFacades.dll"
  />

  <Target Name="GenFacades" AfterTargets="Build">
    <Copy
      SourceFiles="$(TargetDir)\..\CitizenFX.Core.xml"
      DestinationFiles="$(TargetDir)\$(TargetName).xml"
    />
    <GenFacadesTask
      FacadePath="$(TargetDir)\.."
      Seeds="$(TargetDir)\..\CitizenFX.Core.dll"
      Contracts="$(TargetPath)"
    />
  </Target>
</Project>
