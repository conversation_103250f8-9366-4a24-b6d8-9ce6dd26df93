// Copyright 2014 The Chromium Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// This is a catch-all header that includes everything.
//
// Note: This header should be compilable as C.

#ifndef MOJO_PUBLIC_C_SYSTEM_CORE_H_
#define MOJO_PUBLIC_C_SYSTEM_CORE_H_

#include "mojo/public/c/system/buffer.h"
#include "mojo/public/c/system/data_pipe.h"
#include "mojo/public/c/system/functions.h"
#include "mojo/public/c/system/invitation.h"
#include "mojo/public/c/system/macros.h"
#include "mojo/public/c/system/message_pipe.h"
#include "mojo/public/c/system/platform_handle.h"
#include "mojo/public/c/system/quota.h"
#include "mojo/public/c/system/system_export.h"
#include "mojo/public/c/system/trap.h"
#include "mojo/public/c/system/types.h"

#endif  // MOJO_PUBLIC_C_SYSTEM_CORE_H_
