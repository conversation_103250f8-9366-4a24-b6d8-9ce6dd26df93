/*=============================================================================
    Copyright (c) 2001-2011 <PERSON>
    Copyright (c) 2005-2006 <PERSON>

    Di<PERSON>ributed under the Boost Software License, Version 1.0. (See accompanying
    file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
==============================================================================*/
#if !defined(BOOST_FUSION_SEQUENCE_CONTAINER_DEQUE_24112006_2036)
#define BOOST_FUSION_SEQUENCE_CONTAINER_DEQUE_24112006_2036

#include <boost/fusion/support/config.hpp>
#include <boost/fusion/container/deque/deque_fwd.hpp>
#include <boost/fusion/container/deque/deque.hpp>
#include <boost/fusion/container/deque/convert.hpp>

#endif

