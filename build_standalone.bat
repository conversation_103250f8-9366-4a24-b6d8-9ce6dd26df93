@echo off
setlocal enabledelayedexpansion

echo ========================================
echo    GangHaiCity Standalone Builder
echo ========================================
echo.
echo This script will build GangHaiCity from scratch following the official docs.
echo Make sure you have already run: git clone https://github.com/citizenfx/fivem.git -c core.symlinks=true
echo.

:: Kiểm tra xem có đang ở trong thư mục fivem không
if not exist "code" (
    echo ERROR: This script must be run from the fivem repository root directory!
    echo Please make sure you are in the directory where you cloned the repository.
    echo.
    echo Expected structure:
    echo   fivem/
    echo   ├── code/
    echo   ├── data/
    echo   ├── docs/
    echo   └── vendor/
    echo.
    pause
    exit /b 1
)

echo ✓ Found fivem repository structure
echo.

:: Kiểm tra dependencies
echo Checking dependencies...

:: Kiểm tra Visual Studio 2022
if not exist "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MsBuild.exe" (
    if not exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MsBuild.exe" (
        if not exist "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MsBuild.exe" (
            echo ERROR: Visual Studio 2022 not found!
            echo Please install Visual Studio 2022 with the following workloads:
            echo - .NET desktop development
            echo - Desktop development with C++
            echo - Windows application development
            echo And individual components:
            echo - .NET Framework 4.6 targeting pack
            echo - Windows 11 SDK (10.0.22000.0)
            pause
            exit /b 1
        ) else (
            set "MSBUILD_PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MsBuild.exe"
        )
    ) else (
        set "MSBUILD_PATH=C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MsBuild.exe"
    )
) else (
    set "MSBUILD_PATH=C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MsBuild.exe"
)

echo ✓ Visual Studio 2022 found: %MSBUILD_PATH%

:: Kiểm tra PowerShell 7
pwsh -Command "exit 0" >nul 2>&1
if errorlevel 1 (
    echo ERROR: PowerShell 7 not found!
    echo Please install PowerShell 7 from: https://aka.ms/powershell-release?tag=stable
    pause
    exit /b 1
)
echo ✓ PowerShell 7 found

:: Kiểm tra Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python not found!
    echo Please install Python 3.8 or higher from: https://python.org/
    pause
    exit /b 1
)
echo ✓ Python found

:: Kiểm tra MSYS2
if not exist "C:\msys64" (
    echo ERROR: MSYS2 not found at C:\msys64\
    echo Please install MSYS2 from: https://www.msys2.org/
    pause
    exit /b 1
)
echo ✓ MSYS2 found

:: Kiểm tra Node.js và Yarn
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js not found!
    echo Please install Node.js from: https://nodejs.org/
    pause
    exit /b 1
)
echo ✓ Node.js found

yarn --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Yarn not found!
    echo Please install Yarn from: https://classic.yarnpkg.com/en/docs/install/
    pause
    exit /b 1
)
echo ✓ Yarn found

echo.
echo All dependencies found! Starting build process...
echo.

:: Step 1: Update git submodules
echo Step 1: Updating git submodules...
echo This may take a while depending on your internet connection...
git submodule update --jobs=16 --init
if errorlevel 1 (
    echo ERROR: Git submodule update failed!
    echo Please check your internet connection and try again.
    pause
    exit /b 1
)
echo ✓ Git submodules updated successfully
echo.

:: Step 2: Install Python setuptools (for Python 3.12+)
echo Step 2: Installing Python setuptools...
pip install setuptools
if errorlevel 1 (
    echo WARNING: Failed to install setuptools, but continuing...
    echo This is only required for Python 3.12+
)
echo ✓ Python setuptools installed
echo.

:: Step 3: Download Chrome
echo Step 3: Downloading Chrome for CEF...
echo This downloads the correct Chrome version for 64-bit projects...
pwsh ./fxd.ps1 get-chrome
if errorlevel 1 (
    echo ERROR: Chrome download failed!
    echo Please check your internet connection and try again.
    pause
    exit /b 1
)
echo ✓ Chrome downloaded successfully
echo.

:: Step 4: Run prebuild (build bindings of game natives)
echo Step 4: Running prebuild (building game natives bindings)...
echo This step builds bindings of game natives and may take some time...
call prebuild.cmd
if errorlevel 1 (
    echo ERROR: Prebuild failed!
    echo This step is crucial for building the project.
    pause
    exit /b 1
)
echo ✓ Prebuild completed successfully
echo.

:: Step 5: Generate project files
echo Step 5: Generating project files for FiveM...
pwsh ./fxd.ps1 gen -game five
if errorlevel 1 (
    echo ERROR: Project generation failed!
    echo Please check the error messages above.
    pause
    exit /b 1
)
echo ✓ Project files generated successfully
echo.

:: Step 6: Build the project
echo Step 6: Building the project...
echo This step compiles all the code and may take 10-30 minutes depending on your system...
echo.

if not exist "code\build\five" (
    echo ERROR: Build directory not found! Project generation may have failed.
    pause
    exit /b 1
)

cd code\build\five

echo Building with MSBuild...
echo Configuration: Release
echo Platform: x64
echo.

"%MSBUILD_PATH%" CitizenMP.sln -t:build -restore -p:RestorePackagesConfig=true -p:preferredtoolarchitecture=x64 -p:configuration=release -maxcpucount:4 -v:minimal -fl1 "-flp1:logfile=build.log;errorsonly"

if errorlevel 1 (
    echo.
    echo ========================================
    echo           BUILD FAILED!
    echo ========================================
    echo.
    echo Build failed! Check the error messages above.
    echo Build log saved to: code\build\five\build.log
    echo.
    echo Common solutions:
    echo 1. Make sure all dependencies are installed correctly
    echo 2. Try running 'fxd gen -game five' again
    echo 3. Check if antivirus is blocking the build
    echo 4. Make sure you have enough disk space
    echo.
    cd ..\..\..
    pause
    exit /b 1
)

cd ..\..\..

echo ✓ Build completed successfully!
echo.

:: Step 7: Verify build output
echo Step 7: Verifying build output...
if not exist "code\build\five\Release\GangHaiCity.exe" (
    echo ERROR: GangHaiCity.exe not found in build output!
    echo Expected location: code\build\five\Release\GangHaiCity.exe
    echo.
    echo The build may have completed but the executable was not created.
    echo Please check the build log for errors.
    pause
    exit /b 1
)

echo ✓ GangHaiCity.exe found in build output
echo.

:: Step 8: Create standalone package
echo Step 8: Creating standalone package...
set STANDALONE_DIR=GangHaiCity_Standalone
if exist "%STANDALONE_DIR%" (
    echo Removing existing standalone directory...
    rmdir /s /q "%STANDALONE_DIR%"
)
mkdir "%STANDALONE_DIR%"

echo Copying main executable...
copy "code\build\five\Release\GangHaiCity.exe" "%STANDALONE_DIR%\" >nul
if not exist "%STANDALONE_DIR%\GangHaiCity.exe" (
    echo ERROR: Failed to copy GangHaiCity.exe!
    pause
    exit /b 1
)

echo ✓ Main executable copied

:: Step 9: Create documentation and launcher
echo Step 9: Creating documentation and launcher scripts...

echo Creating README file...
(
echo GangHaiCity - Standalone Version
echo ================================
echo.
echo Đây là phiên bản standalone của GangHaiCity được build từ source code FiveM.
echo.
echo Cách sử dụng:
echo 1. Chạy GangHaiCity.exe trực tiếp
echo 2. Hoặc sử dụng Launch.bat để khởi động
echo 3. Giao diện tùy chỉnh sẽ hiển thị thay vì FiveM launcher
echo.
echo Đặc điểm:
echo - File exe có thể chạy độc lập ở bất kỳ đâu
echo - Không cần cài đặt FiveM hoặc các dependencies khác
echo - Giao diện được tùy chỉnh riêng cho GangHaiCity
echo - Tự động phát hiện chế độ standalone
echo.
echo Build Information:
echo - Phiên bản: Standalone Build
echo - Ngày build: %date% %time%
echo - Build từ: FiveM source code với modifications
echo.
echo Lưu ý kỹ thuật:
echo - Executable sử dụng logic phát hiện standalone mode
echo - Khi không có CoreRT.dll trong cùng thư mục, sẽ chạy giao diện tùy chỉnh
echo - Khi có đầy đủ dependencies, sẽ hoạt động như FiveM bình thường
) > "%STANDALONE_DIR%\README.txt"

echo Creating launcher script...
(
echo @echo off
echo title GangHaiCity Standalone Launcher
echo echo ========================================
echo echo    GangHaiCity Standalone Launcher
echo echo ========================================
echo echo.
echo echo Starting GangHaiCity in standalone mode...
echo echo This will show the custom interface instead of FiveM.
echo echo.
echo start "" "GangHaiCity.exe"
echo echo.
echo echo GangHaiCity has been started!
echo echo You can close this window now.
echo timeout /t 3 /nobreak ^>nul
) > "%STANDALONE_DIR%\Launch.bat"

echo Creating test script...
(
echo @echo off
echo title GangHaiCity Standalone Test
echo echo Testing GangHaiCity standalone mode...
echo echo.
echo echo This script will test if the standalone mode works correctly.
echo echo.
echo pause
echo.
echo echo Running GangHaiCity.exe...
echo start "" "GangHaiCity.exe"
echo echo.
echo echo If you see a custom interface with "GangHaiCity - Standalone Mode" title,
echo echo then the standalone build is working correctly!
echo echo.
echo echo If you see FiveM launcher instead, there might be an issue with the build.
echo echo.
echo pause
) > "%STANDALONE_DIR%\Test.bat"

echo ✓ Documentation and scripts created
echo.

:: Step 10: Final verification and summary
echo Step 10: Final verification...

echo Checking standalone package contents...
if exist "%STANDALONE_DIR%\GangHaiCity.exe" (
    echo ✓ GangHaiCity.exe
) else (
    echo ✗ GangHaiCity.exe - MISSING!
)

if exist "%STANDALONE_DIR%\README.txt" (
    echo ✓ README.txt
) else (
    echo ✗ README.txt - MISSING!
)

if exist "%STANDALONE_DIR%\Launch.bat" (
    echo ✓ Launch.bat
) else (
    echo ✗ Launch.bat - MISSING!
)

if exist "%STANDALONE_DIR%\Test.bat" (
    echo ✓ Test.bat
) else (
    echo ✗ Test.bat - MISSING!
)

echo.
echo ========================================
echo        BUILD COMPLETED SUCCESSFULLY!
echo ========================================
echo.
echo 🎉 GangHaiCity Standalone has been built successfully!
echo.
echo 📁 Location: %STANDALONE_DIR%\
echo 📄 Main executable: GangHaiCity.exe
echo.
echo 🚀 How to test:
echo   1. Navigate to %STANDALONE_DIR%
echo   2. Run GangHaiCity.exe or Launch.bat
echo   3. You should see a custom interface instead of FiveM
echo.
echo 📋 What you can do now:
echo   • Copy GangHaiCity.exe to any location and run it
echo   • The exe will automatically detect standalone mode
echo   • Customize the interface in code\client\launcher\StandaloneMode.cpp
echo   • Rebuild using this script to apply changes
echo.
echo 💡 Tips:
echo   • The exe works standalone because it detects missing CoreRT.dll
echo   • You can modify the interface by editing StandaloneMode.cpp
echo   • Run Test.bat to verify the standalone functionality
echo.
echo Build completed at: %date% %time%
echo.
pause
