import os
import subprocess
import sys

inputs = [
	'lib/assert/strict.js',	
	'lib/assert.js',	
	'lib/async_hooks.js',	
	'lib/buffer.js',	
	'lib/child_process.js',	
	'lib/cluster.js',	
	'lib/console.js',	
	'lib/constants.js',	
	'lib/crypto.js',	
	'lib/dgram.js',	
	'lib/diagnostics_channel.js',	
	'lib/dns/promises.js',	
	'lib/dns.js',	
	'lib/domain.js',	
	'lib/events.js',	
	'lib/fs/promises.js',	
	'lib/fs.js',	
	'lib/http.js',	
	'lib/http2.js',	
	'lib/https.js',	
	'lib/inspector.js',	
	'lib/internal/abort_controller.js',	
	'lib/internal/assert/assertion_error.js',	
	'lib/internal/assert/calltracker.js',	
	'lib/internal/assert.js',	
	'lib/internal/async_hooks.js',	
	'lib/internal/blob.js',	
	'lib/internal/blocklist.js',	
	'lib/internal/bootstrap/environment.js',	
	'lib/internal/bootstrap/loaders.js',	
	'lib/internal/bootstrap/node.js',	
	'lib/internal/bootstrap/pre_execution.js',	
	'lib/internal/bootstrap/switches/does_not_own_process_state.js',	
	'lib/internal/bootstrap/switches/does_own_process_state.js',	
	'lib/internal/bootstrap/switches/is_main_thread.js',	
	'lib/internal/bootstrap/switches/is_not_main_thread.js',	
	'lib/internal/buffer.js',	
	'lib/internal/child_process/serialization.js',	
	'lib/internal/child_process.js',	
	'lib/internal/cli_table.js',	
	'lib/internal/cluster/child.js',	
	'lib/internal/cluster/primary.js',	
	'lib/internal/cluster/round_robin_handle.js',	
	'lib/internal/cluster/shared_handle.js',	
	'lib/internal/cluster/utils.js',	
	'lib/internal/cluster/worker.js',	
	'lib/internal/console/constructor.js',	
	'lib/internal/console/global.js',	
	'lib/internal/constants.js',	
	'lib/internal/crypto/aes.js',	
	'lib/internal/crypto/certificate.js',	
	'lib/internal/crypto/cipher.js',	
	'lib/internal/crypto/diffiehellman.js',	
	'lib/internal/crypto/dsa.js',	
	'lib/internal/crypto/ec.js',	
	'lib/internal/crypto/hash.js',	
	'lib/internal/crypto/hashnames.js',	
	'lib/internal/crypto/hkdf.js',	
	'lib/internal/crypto/keygen.js',	
	'lib/internal/crypto/keys.js',	
	'lib/internal/crypto/mac.js',	
	'lib/internal/crypto/pbkdf2.js',	
	'lib/internal/crypto/random.js',	
	'lib/internal/crypto/rsa.js',	
	'lib/internal/crypto/scrypt.js',	
	'lib/internal/crypto/sig.js',	
	'lib/internal/crypto/util.js',	
	'lib/internal/crypto/webcrypto.js',	
	'lib/internal/crypto/x509.js',	
	'lib/internal/debugger/inspect.js',	
	'lib/internal/debugger/inspect_client.js',	
	'lib/internal/debugger/inspect_repl.js',	
	'lib/internal/dgram.js',	
	'lib/internal/dns/promises.js',	
	'lib/internal/dns/utils.js',	
	'lib/internal/dtrace.js',	
	'lib/internal/encoding.js',	
	'lib/internal/errors.js',	
	'lib/internal/error_serdes.js',	
	'lib/internal/event_target.js',	
	'lib/internal/fixed_queue.js',	
	'lib/internal/freelist.js',	
	'lib/internal/freeze_intrinsics.js',	
	'lib/internal/fs/cp/cp-sync.js',	
	'lib/internal/fs/cp/cp.js',	
	'lib/internal/fs/dir.js',	
	'lib/internal/fs/promises.js',	
	'lib/internal/fs/read_file_context.js',	
	'lib/internal/fs/rimraf.js',	
	'lib/internal/fs/streams.js',	
	'lib/internal/fs/sync_write_stream.js',	
	'lib/internal/fs/utils.js',	
	'lib/internal/fs/watchers.js',	
	'lib/internal/heap_utils.js',	
	'lib/internal/histogram.js',	
	'lib/internal/http.js',	
	'lib/internal/http2/compat.js',	
	'lib/internal/http2/core.js',	
	'lib/internal/http2/util.js',	
	'lib/internal/idna.js',	
	'lib/internal/inspector_async_hook.js',	
	'lib/internal/js_stream_socket.js',	
	'lib/internal/legacy/processbinding.js',	
	'lib/internal/linkedlist.js',	
	'lib/internal/main/check_syntax.js',	
	'lib/internal/main/eval_stdin.js',	
	'lib/internal/main/eval_string.js',	
	'lib/internal/main/inspect.js',	
	'lib/internal/main/print_help.js',	
	'lib/internal/main/prof_process.js',	
	'lib/internal/main/repl.js',	
	'lib/internal/main/run_main_module.js',	
	'lib/internal/main/worker_thread.js',	
	'lib/internal/modules/cjs/helpers.js',	
	'lib/internal/modules/cjs/loader.js',	
	'lib/internal/modules/esm/create_dynamic_module.js',	
	'lib/internal/modules/esm/get_format.js',	
	'lib/internal/modules/esm/get_source.js',	
	'lib/internal/modules/esm/loader.js',	
	'lib/internal/modules/esm/module_job.js',	
	'lib/internal/modules/esm/module_map.js',	
	'lib/internal/modules/esm/resolve.js',	
	'lib/internal/modules/esm/transform_source.js',	
	'lib/internal/modules/esm/translators.js',	
	'lib/internal/modules/package_json_reader.js',	
	'lib/internal/modules/run_main.js',	
	'lib/internal/net.js',	
	'lib/internal/options.js',	
	'lib/internal/perf/event_loop_delay.js',	
	'lib/internal/perf/event_loop_utilization.js',	
	'lib/internal/perf/nodetiming.js',	
	'lib/internal/perf/observe.js',	
	'lib/internal/perf/performance.js',	
	'lib/internal/perf/performance_entry.js',	
	'lib/internal/perf/timerify.js',	
	'lib/internal/perf/usertiming.js',	
	'lib/internal/perf/utils.js',	
	'lib/internal/per_context/domexception.js',	
	'lib/internal/per_context/messageport.js',	
	'lib/internal/per_context/primordials.js',	
	'lib/internal/policy/manifest.js',	
	'lib/internal/policy/sri.js',	
	'lib/internal/priority_queue.js',	
	'lib/internal/process/esm_loader.js',	
	'lib/internal/process/execution.js',	
	'lib/internal/process/per_thread.js',	
	'lib/internal/process/policy.js',	
	'lib/internal/process/promises.js',	
	'lib/internal/process/report.js',	
	'lib/internal/process/signal.js',	
	'lib/internal/process/task_queues.js',	
	'lib/internal/process/warning.js',	
	'lib/internal/process/worker_thread_only.js',	
	'lib/internal/querystring.js',	
	'lib/internal/readline/callbacks.js',	
	'lib/internal/readline/emitKeypressEvents.js',	
	'lib/internal/readline/utils.js',	
	'lib/internal/repl/await.js',	
	'lib/internal/repl/history.js',	
	'lib/internal/repl/utils.js',	
	'lib/internal/repl.js',	
	'lib/internal/socketaddress.js',	
	'lib/internal/socket_list.js',	
	'lib/internal/source_map/prepare_stack_trace.js',	
	'lib/internal/source_map/source_map.js',	
	'lib/internal/source_map/source_map_cache.js',	
	'lib/internal/streams/add-abort-signal.js',	
	'lib/internal/streams/buffer_list.js',	
	'lib/internal/streams/compose.js',	
	'lib/internal/streams/destroy.js',	
	'lib/internal/streams/duplex.js',	
	'lib/internal/streams/duplexify.js',	
	'lib/internal/streams/end-of-stream.js',	
	'lib/internal/streams/from.js',	
	'lib/internal/streams/lazy_transform.js',	
	'lib/internal/streams/legacy.js',	
	'lib/internal/streams/passthrough.js',	
	'lib/internal/streams/pipeline.js',	
	'lib/internal/streams/readable.js',	
	'lib/internal/streams/state.js',	
	'lib/internal/streams/transform.js',	
	'lib/internal/streams/utils.js',	
	'lib/internal/streams/writable.js',	
	'lib/internal/stream_base_commons.js',	
	'lib/internal/test/binding.js',	
	'lib/internal/test/transfer.js',	
	'lib/internal/timers.js',	
	'lib/internal/tls/parse-cert-string.js',	
	'lib/internal/tls/secure-context.js',	
	'lib/internal/tls/secure-pair.js',	
	'lib/internal/trace_events_async_hooks.js',	
	'lib/internal/tty.js',	
	'lib/internal/url.js',	
	'lib/internal/util/comparisons.js',	
	'lib/internal/util/debuglog.js',	
	'lib/internal/util/inspect.js',	
	'lib/internal/util/inspector.js',	
	'lib/internal/util/iterable_weak_map.js',	
	'lib/internal/util/types.js',	
	'lib/internal/util.js',	
	'lib/internal/v8_prof_polyfill.js',	
	'lib/internal/v8_prof_processor.js',	
	'lib/internal/validators.js',	
	'lib/internal/vm/module.js',	
	'lib/internal/watchdog.js',	
	'lib/internal/webstreams/encoding.js',	
	'lib/internal/webstreams/queuingstrategies.js',	
	'lib/internal/webstreams/readablestream.js',	
	'lib/internal/webstreams/transfer.js',	
	'lib/internal/webstreams/transformstream.js',	
	'lib/internal/webstreams/util.js',	
	'lib/internal/webstreams/writablestream.js',	
	'lib/internal/worker/io.js',	
	'lib/internal/worker/js_transferable.js',	
	'lib/internal/worker.js',	
	'lib/module.js',	
	'lib/net.js',	
	'lib/os.js',	
	'lib/path/posix.js',	
	'lib/path/win32.js',	
	'lib/path.js',	
	'lib/perf_hooks.js',	
	'lib/process.js',	
	'lib/punycode.js',	
	'lib/querystring.js',	
	'lib/readline.js',	
	'lib/repl.js',	
	'lib/stream/consumers.js',	
	'lib/stream/promises.js',	
	'lib/stream/web.js',	
	'lib/stream.js',	
	'lib/string_decoder.js',	
	'lib/sys.js',	
	'lib/timers/promises.js',	
	'lib/timers.js',	
	'lib/tls.js',	
	'lib/trace_events.js',	
	'lib/tty.js',	
	'lib/url.js',	
	'lib/util/types.js',	
	'lib/util.js',	
	'lib/v8.js',	
	'lib/vm.js',	
	'lib/wasi.js',	
	'lib/worker_threads.js',	
	'lib/zlib.js',	
	'lib/_http_agent.js',	
	'lib/_http_client.js',	
	'lib/_http_common.js',	
	'lib/_http_incoming.js',	
	'lib/_http_outgoing.js',	
	'lib/_http_server.js',	
	'lib/_stream_duplex.js',	
	'lib/_stream_passthrough.js',	
	'lib/_stream_readable.js',	
	'lib/_stream_transform.js',	
	'lib/_stream_wrap.js',	
	'lib/_stream_writable.js',	
	'lib/_tls_common.js',	
	'lib/_tls_wrap.js',
	'deps/v8/tools/splaytree.mjs',
    'deps/v8/tools/codemap.mjs',
    'deps/v8/tools/consarray.mjs',
    'deps/v8/tools/csvparser.mjs',
    'deps/v8/tools/profile.mjs',
    'deps/v8/tools/profile_view.mjs',
    'deps/v8/tools/logreader.mjs',
    'deps/v8/tools/arguments.mjs',
    'deps/v8/tools/tickprocessor.mjs',
    'deps/v8/tools/sourcemap.mjs',
    'deps/v8/tools/tickprocessor-driver.mjs',
	'deps/acorn/acorn/dist/acorn.js',
	'deps/acorn/acorn-walk/dist/walk.js',
	'deps/cjs-module-lexer/lexer.js',
    'deps/cjs-module-lexer/dist/lexer.js',
	'lib/_third_party_main.js',
	'config.gypi',
]

deps = [
	'deps/v8/tools/splaytree.mjs',
    'deps/v8/tools/codemap.mjs',
    'deps/v8/tools/consarray.mjs',
    'deps/v8/tools/csvparser.mjs',
    'deps/v8/tools/profile.mjs',
    'deps/v8/tools/profile_view.mjs',
    'deps/v8/tools/logreader.mjs',
    'deps/v8/tools/arguments.mjs',
    'deps/v8/tools/tickprocessor.mjs',
    'deps/v8/tools/sourcemap.mjs',
    'deps/v8/tools/tickprocessor-driver.mjs',
	'deps/acorn/acorn/dist/acorn.js',
	'deps/acorn/acorn-walk/dist/walk.js',
	'deps/cjs-module-lexer/lexer.js',
    'deps/cjs-module-lexer/dist/lexer.js',
]

noderoot = sys.argv[1]

mtimes = []

for inFile in deps:
	mtimes = mtimes + [ os.path.getmtime(os.path.join(noderoot, inFile)) ]

mtimes = mtimes + [ os.path.getmtime(sys.argv[0]) ]

mtimes.sort()
mtimes.reverse()

minputs = []

for inFile in deps:
	minputs = minputs + [ inFile.replace('/', os.path.sep) ]

outFile = os.path.join(noderoot, 'src/node_javascript.cc')

if not os.path.exists(outFile) or os.path.getmtime(outFile) < mtimes[0]:
	subprocess.check_call([sys.executable, 'tools/js2c.py', '--directory', 'lib', '--target', 'src/node_javascript.cc', 'config.gypi'] + deps, cwd = noderoot)
