What's New In Libevent 1.4:

0. About this document

  This document describes the key differences between Libevent 1.3 and
  Libevent 1.4, from a user's point of view.  It was most recently
  updated based on features from libevent 1.4.2-rc.

1. Packaging Issues.

1.1. The great library division.

  The libevent source now builds two libraries: libevent_core and
  libevent_extra.  The libevent_core library includes event loops,
  timers, buffer code, and various small compatibility functions.  The
  libevent_extra library includes code for HTTP, DNS, RPC, and so on.
  Thus, if you're writing software that only uses libevent's event
  loop, you should link against only the libevent_core library,
  whereas if you're writing software that uses libevent's protocol
  support as well, you need to link libevent_extra as well.

  For backward compatibility, libevent also builds a library called
  "libevent" that includes everything.

1.2. The event-config.h header

  Libevent configure script now builds two headers from its configure
  script: config.h (which it uses internally) and event-config.h
  (which it installs as a header file).  All of the macros in
  event-config.h are modified so that they're safe to include in other
  projects.  This allows libevent's header files (like event.h and
  evutil.h) information about platform configuration.

  What does this mean for you?  As of 1.4.x, it should never be
  necessary to include extra files or define extra types before you
  include event.h (or any other libevent header); event.h can now look
  at the information in event-config.h and figure out what it needs to
  include.

1.3. Documentation

  Libevent now includes better doxygen documentation.  It's not
  perfect or complete, though; if you find a mistake, please let us
  know.

1.4. Libtool usage

  We now use libtool's library versioning support correctly.  If we
  don't mess this up, it means that binaries linked against old
  version of libevent should continue working when we make changes to
  libevent that don't break backward compatibility.

1.5. Portability

  Libevent now builds with MSVC again.  We've only tested it with MSVC
  2005, and the project files might not be right.  Please let us know
  if you run into any issues.

  Libevent now builds on platforms where /bin/sh is not bash.

  Libevent's regression test no longer requires Python to be
  installed.

2. New and Improved APIs:

  (This list includes functions that are new, functions whose behavior
  has changed, and functions that were included in previous releases
  but which never actually worked before.)

2.1. Utility functions are defined in evutil.h

  Libevent now exposes a small set of functions for cross-platform
  network programming in evutil.h, on the theory that they've been
  useful enough to us that other people may likely want to use them
  too.  These are mainly workarounds for Windows issues for now: they
  include evutil_socketpair (to fake socketpair on platforms that
  don't have it) and evutil_make_socket_nonblocking (to make a socket
  nonblocking in a cross-platform way.  See the header for more
  information.

2.2. In the libevent core.

  The event_base_free() function now works.  Previously, it would
  crash with an assertion failure if there were events pending on a
  base.  Now, it simply deletes all the pending events and frees the
  base.  Be careful -- this might leak fds and memory associated with
  the old events.  To avoid leaks, you should still remove all the
  events and free their resources before you delete the base.

  Libevent should now work properly with fork().  Just call
  event_reinit() on your event base after the fork call, and it should
  work okay.  Please let us know about any bugs you find.

  There's a new event_base_new() function that acts just like
  event_init(), but does not replace the default base.  If you are
  using multiple event bases in your code, you should just use
  event_base_new() instead of event_init(), to avoid accidental bugs.

  There's new event_loopbreak() function to make a current event loop
  stop exiting and return.  Unlike event_loopexit, it stops subsequent
  pending events from getting executed.  This behavior is useful for
  scripting languages to implement exceptions from inside callbacks.

  There's a new event_base_get_method() function, for use in place of
  event_get_method() in multi-base applications.

2.3. New in HTTP.

  There's an evhttp_connection_set_local_address() function you can
  use to set the local address of an HTTP connection.

  HTTP/1.1 chunking now correctly ends chunks with '\r\n'.

2.4. New in DNS

  Instead of picking your method for generating DNS transaction IDs at
  startup, you can use evdns_set_transaction_id() to provide a
  transaction ID function at runtime.

  The "class" field in evdns_server_request is now renamed to
  dns_question_class, so that it won't break compilation under C++.
  This uses some preprocessor hacks so that C code using the old name
  won't break.  Eventually, the old name will be deprecated entirely;
  please don't use it.

2.5. New in RPC

  There are now hooks on RPC input and output; can be used to
  implement RPC independent processing such as compression or
  authentication.

  RPC tags can now be up to 32 bits.  This is wire-compatible, but
  changes some of the types in the APIs.  Please let us know if this
  is problematic for you.

3. Big bugfixes

  We've done a lot, with help from users on different platforms, to
  make the different backends behave more similarly with respect to
  signals and timeouts.  The kqueue and solaris backends were the big
  offenders previously, but they should be better now.  Windows should
  be better too, though it's likely that problems remain there.

  The libevent headers (though not the source files!) should now build
  cleanly on C++.

  (For more bugfixes, see the ChangeLog file.  These are only the
  biggies.)

4. Big performance improvements

  Libevent now uses a min-heap rather than a red-black tree to track
  timeouts.  This means that finding the next timeout to fire is now
  O(1) instead of (lg n).

  The win32 select-based backend now uses a red-black tree to map
  SOCKET handles to event structures.  This changes the performance
  characteristics of the event loop on win32 from O(n^2) to O(n lg n).
  Not perfect, but better.

5. Removed code and features

  The rtsig backend is now removed.  It hasn't even compiled for a
  while, and nobody seemed to miss it very much.  All the platforms
  that have rtsig seem to have a better option instead these days.
  Please let us know if rtsig was crucial for you.

