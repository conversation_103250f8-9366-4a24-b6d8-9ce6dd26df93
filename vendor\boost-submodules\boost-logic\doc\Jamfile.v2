# Tribool library

# Copyright (C) 2002-2003 <PERSON>

# Use, modification and distribution is subject to the Boost Software License,
# Version 1.0. (See accompanying file LICENSE_1_0.txt or copy at
# http://www.boost.org/LICENSE_1_0.txt)

# For more information, see http://www.boost.org/

project boost-sandbox/utility/doc ;
import boostbook ;
import doxygen ;

doxygen reference : [ glob ../../../boost/logic/tribool.hpp ]
                    [ glob ../../../boost/logic/tribool_fwd.hpp ]
                    [ glob ../../../boost/logic/tribool_io.hpp ]
                  ;
boostbook tribool 
    : 
    tribool.boostbook 
    :
        <xsl:param>boost.root=../../../..
        <format>pdf:<xsl:param>boost.url.prefix=http://www.boost.org/doc/libs/release/doc/html
        <dependency>reference
    ;

###############################################################################
alias boostdoc
    : tribool/<format>docbook
    :
    :
    : ;
explicit boostdoc ;
alias boostrelease ;
explicit boostrelease ;
