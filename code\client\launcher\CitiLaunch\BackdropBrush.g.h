// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.201201.7

#pragma once
#include "winrt/CitiLaunch.h"
#include "winrt/Windows.UI.Composition.h"
#include "winrt/Windows.UI.Xaml.h"
#include "winrt/Windows.UI.Xaml.Media.h"
namespace winrt::CitiLaunch::implementation
{
    template <typename D, typename... I>
    struct __declspec(empty_bases) BackdropBrush_base : implements<D, CitiLaunch::BackdropBrush, composing, Windows::UI::Xaml::Media::IXamlCompositionBrushBaseOverrides, Windows::UI::Xaml::Media::IBrushOverrides2, I...>,
        impl::require<D, Windows::UI::Xaml::Media::IXamlCompositionBrushBase, Windows::UI::Xaml::Media::IXamlCompositionBrushBaseProtected, Windows::UI::Xaml::Media::IBrush, Windows::UI::Composition::IAnimationObject, Windows::UI::Xaml::IDependencyObject, Windows::UI::Xaml::IDependencyObject2>,
        impl::base<D, Windows::UI::Xaml::Media::XamlCompositionBrushBase, Windows::UI::Xaml::Media::Brush, Windows::UI::Xaml::DependencyObject>,
        Windows::UI::Xaml::Media::IXamlCompositionBrushBaseOverridesT<D>, Windows::UI::Xaml::Media::IBrushOverrides2T<D>
    {
        using base_type = BackdropBrush_base;
        using class_type = CitiLaunch::BackdropBrush;
        using implements_type = typename BackdropBrush_base::implements_type;
        using implements_type::implements_type;
        using composable_base = Windows::UI::Xaml::Media::XamlCompositionBrushBase;
        hstring GetRuntimeClassName() const
        {
            return L"CitiLaunch.BackdropBrush";
        }
        BackdropBrush_base()
        {
            impl::call_factory<Windows::UI::Xaml::Media::XamlCompositionBrushBase, Windows::UI::Xaml::Media::IXamlCompositionBrushBaseFactory>([&](Windows::UI::Xaml::Media::IXamlCompositionBrushBaseFactory const& f) { [[maybe_unused]] auto winrt_impl_discarded = f.CreateInstance(*this, this->m_inner); });
        }
    };
}
namespace winrt::CitiLaunch::factory_implementation
{
    template <typename D, typename T, typename... I>
    struct __declspec(empty_bases) BackdropBrushT : implements<D, Windows::Foundation::IActivationFactory, I...>
    {
        using instance_type = CitiLaunch::BackdropBrush;

        hstring GetRuntimeClassName() const
        {
            return L"CitiLaunch.BackdropBrush";
        }
        auto ActivateInstance() const
        {
            return make<T>();
        }
    };
}

#if defined(WINRT_FORCE_INCLUDE_BACKDROPBRUSH_XAML_G_H) || __has_include("CitiLaunch/BackdropBrush.xaml.g.h")
#include "CitiLaunch/BackdropBrush.xaml.g.h"
#else

namespace winrt::CitiLaunch::implementation
{
    template <typename D, typename... I>
    using BackdropBrushT = BackdropBrush_base<D, I...>;
}

#endif
