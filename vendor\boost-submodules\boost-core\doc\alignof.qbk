[/
  Copyright 2023 <PERSON>v
  Distributed under the Boost Software License, Version 1.0.
  https://boost.org/LICENSE_1_0.txt
]

[section:alignof alignof]

[simplesect Authors]

* <PERSON>v

[endsimplesect]

[section Header <boost/core/alignof.hpp>]

The header `<boost/core/alignof.hpp>` defines the macro `BOOST_CORE_ALIGNOF`,
a portable equivalent of the `alignof` operator from C++11.

[section Example]

``
#include <boost/core/alignof.hpp>
#include <cstddef>

constexpr std::size_t alignment_of_double = BOOST_CORE_ALIGNOF(double);
``

[endsect]

[endsect]

[endsect]
