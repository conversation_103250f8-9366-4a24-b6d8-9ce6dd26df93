[section:skew_normal_dist Skew Normal Distribution]

``#include <boost/math/distributions/skew_normal.hpp>``

   namespace boost{ namespace math{

   template <class RealType = double,
             class ``__Policy``   = ``__policy_class`` >
   class skew_normal_distribution;

   typedef skew_normal_distribution<> normal;

   template <class RealType, class ``__Policy``>
   class skew_normal_distribution
   {
   public:
      typedef RealType value_type;
      typedef Policy   policy_type;
      // Constructor:
      skew_normal_distribution(RealType location = 0, RealType scale = 1, RealType shape = 0);
      // Accessors:
      RealType location()const; // mean if normal.
      RealType scale()const; // width, standard deviation if normal.
      RealType shape()const; // The distribution is right skewed if shape > 0 and is left skewed if shape < 0.
                             // The distribution is normal if shape is zero.
   };

   }} // namespaces

The skew normal distribution is a variant of the most well known
Gaussian statistical distribution.

The skew normal distribution with shape zero resembles the
[@http://en.wikipedia.org/wiki/Normal_distribution Normal Distribution],
hence the latter can be regarded as a special case of the more generic skew normal distribution.

If the standard (mean = 0, scale = 1) normal distribution probability density function is

[equation normal01_pdf]

and the cumulative distribution function

[equation normal01_cdf]

then the [@http://en.wikipedia.org/wiki/Probability_density_function PDF]
of the [@http://en.wikipedia.org/wiki/Skew_normal_distribution skew normal distribution]
with shape parameter [alpha], defined by O'Hagan and Leonhard (1976) is

[equation skew_normal_pdf0]

Given [@http://en.wikipedia.org/wiki/Location_parameter location] [xi],
[@http://en.wikipedia.org/wiki/Scale_parameter scale] [omega],
and [@http://en.wikipedia.org/wiki/Shape_parameter shape] [alpha],
it can be
[@http://en.wikipedia.org/wiki/Skew_normal_distribution transformed],
to the form:

[equation skew_normal_pdf]

and [@http://en.wikipedia.org/wiki/Cumulative_distribution_function CDF]:

[equation skew_normal_cdf]

where ['T(h,a)] is Owen's T function, and ['[Phi](x)] is the normal distribution.

The variation the PDF and CDF with its parameters is illustrated
in the following graphs:

[graph skew_normal_pdf]
[graph skew_normal_cdf]

[h4 Member Functions]

   skew_normal_distribution(RealType location = 0, RealType scale = 1, RealType shape = 0);

Constructs a skew_normal distribution with location [xi],
scale [omega] and shape [alpha].

Requires scale > 0, otherwise __domain_error is called.

   RealType location()const;

returns the location [xi] of this distribution,

   RealType scale()const;

returns the scale [omega] of this distribution,

   RealType shape()const;

returns the shape [alpha] of this distribution.

(Location and scale function match other similar distributions,
allowing the functions `find_location` and `find_scale` to be used generically).

[note While the shape parameter may be chosen arbitrarily (finite),
the resulting [*skewness] of the distribution is in fact limited to about (-1, 1);
strictly, the interval is (-0.9952717, 0.9952717).

A parameter [delta] is related to the shape [alpha] by
[delta] = [alpha] / (1 + [alpha][pow2]),
and used in the expression for skewness
[equation skew_normal_skewness]
] [/note]

[h4 References]

* [@http://azzalini.stat.unipd.it/SN/ Skew-Normal Probability Distribution] for many links and bibliography.
* [@http://azzalini.stat.unipd.it/SN/Intro/intro.html A very brief introduction to the skew-normal distribution]
by Adelchi Azzalini (2005-11-2).
* See a [@http://www.tri.org.au/azzalini.html skew-normal function animation].

[h4 Non-member Accessors]

All the [link math_toolkit.dist_ref.nmp usual non-member accessor functions]
that are generic to all distributions are supported: __usual_accessors.

The domain of the random variable is ['-[max_value], +[min_value]].
Infinite values are not supported.

There are no [@http://en.wikipedia.org/wiki/Closed-form_expression closed-form expression]
known for the mode and median, but these are computed for the

* mode - by finding the maximum of the PDF.
* median - by computing `quantile(1/2)`.

The maximum of the PDF is sought through searching the root of f'(x)=0.

Both involve iterative methods that will have lower accuracy than other estimates.

[h4 Testing]

__R using library(sn) described at
[@http://azzalini.stat.unipd.it/SN/  Skew-Normal Probability Distribution],
and at [@http://cran.r-project.org/web/packages/sn/sn.pd R skew-normal(sn) package].

Package sn provides functions related to the skew-normal (SN)
and the skew-t (ST) probability distributions,
both for the univariate and for the the multivariate case,
including regression models.

__Mathematica was also used to generate some more accurate spot test data.

[h4 Accuracy]

The skew_normal distribution with shape = zero is implemented as a special case,
equivalent to the normal distribution in terms of the
[link math_toolkit.sf_erf.error_function error function],
and therefore should have excellent accuracy.

The PDF and mean, variance, skewness and kurtosis are also accurately evaluated using
[@http://en.wikipedia.org/wiki/Analytical_expression analytical expressions].
The CDF requires [@http://en.wikipedia.org/wiki/Owen%27s_T_function Owen's T function]
that is evaluated using a Boost C++ __owens_t implementation of the algorithms of
M. Patefield and D. Tandy, Journal of Statistical Software, 5(5), 1-25 (2000);
the complicated accuracy of this function is discussed in detail at __owens_t.

The median and mode are calculated by iterative root finding, and both will be less accurate.

[h4 Implementation]

In the following table, [xi] is the location of the distribution,
and [omega] is its scale, and [alpha] is its shape.

[table
[[Function][Implementation Notes]]
[[pdf][Using:[equation skew_normal_pdf] ]]
[[cdf][Using: [equation skew_normal_cdf][br]
where ['T(h,a)] is Owen's T function, and ['[Phi](x)] is the normal distribution. ]]
[[cdf complement][Using: complement of normal distribution + 2 * Owens_t]]
[[quantile][Maximum of the pdf is sought through searching the root of f'(x)=0]]
[[quantile from the complement][-quantile(SN(-location [xi], scale [omega], -shape[alpha]), p)]]
[[location][location [xi]]]
[[scale][scale [omega]]]
[[shape][shape [alpha]]]
[[median][quantile(1/2)]]
[[mean][[equation skew_normal_mean]]]
[[mode][Maximum of the pdf is sought through searching the root of f'(x)=0]]
[[variance][[equation skew_normal_variance] ]]
[[skewness][[equation skew_normal_skewness] ]]
[[kurtosis][kurtosis excess-3]]
[[kurtosis excess] [ [equation skew_normal_kurt_ex] ]]
] [/table]

[endsect] [/section:skew_normal_dist skew_Normal]

[/ skew_normal.qbk
  Copyright 2012 Benjamin Sobotta, John Maddock and Paul A. Bristow.
  Distributed under the Boost Software License, Version 1.0.
  (See accompanying file LICENSE_1_0.txt or copy at
  http://www.boost.org/LICENSE_1_0.txt).
]

