// Copyright (c) 2012 The Chromium Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#ifndef BASE_BASE_PATHS_H_
#define BASE_BASE_PATHS_H_

// This file declares path keys for the base module.  These can be used with
// the PathService to access various special directories and files.

#include "build/build_config.h"

#if defined(OS_WIN)
#include "base/base_paths_win.h"
#elif defined(OS_MACOSX)
#include "base/base_paths_mac.h"
#elif defined(OS_ANDROID)
#include "base/base_paths_android.h"
#endif

#if defined(OS_POSIX) || defined(OS_FUCHSIA)
#include "base/base_paths_posix.h"
#endif

namespace base {

enum BasePathKey {
  PATH_START = 0,

  DIR_CURRENT,       // Current directory.
  DIR_EXE,           // Directory containing FILE_EXE.
  DIR_MODULE,        // Directory containing FILE_MODULE.
  DIR_ASSETS,        // Directory that contains application assets.
  DIR_TEMP,          // Temporary directory.
  DIR_HOME,          // User's root home directory. On Windows this will look
                     // like "C:\Users\<USER>