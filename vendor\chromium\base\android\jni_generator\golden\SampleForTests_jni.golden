// Copyright 2014 The Chromium Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     base/android/jni_generator/jni_generator.py
// For
//     org/chromium/example/jni_generator/SampleForTests

#ifndef org_chromium_example_jni_generator_SampleForTests_JNI
#define org_chromium_example_jni_generator_SampleForTests_JNI

#include <jni.h>

#include "base/android/jni_generator/jni_generator_helper.h"


// Step 1: Forward declarations.

JNI_REGISTRATION_EXPORT extern const char
    kClassPath_org_chromium_example_jni_1generator_SampleForTests[];
const char kClassPath_org_chromium_example_jni_1generator_SampleForTests[] =
    "org/chromium/example/jni_generator/SampleForTests";

JNI_REGISTRATION_EXPORT extern const char
    kClassPath_org_chromium_example_jni_1generator_SampleForTests_00024InnerStructA[];
const char kClassPath_org_chromium_example_jni_1generator_SampleForTests_00024InnerStructA[] =
    "org/chromium/example/jni_generator/SampleForTests$InnerStructA";

JNI_REGISTRATION_EXPORT extern const char
    kClassPath_org_chromium_example_jni_1generator_SampleForTests_00024InnerStructB[];
const char kClassPath_org_chromium_example_jni_1generator_SampleForTests_00024InnerStructB[] =
    "org/chromium/example/jni_generator/SampleForTests$InnerStructB";
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_REGISTRATION_EXPORT std::atomic<jclass>
    g_org_chromium_example_jni_1generator_SampleForTests_clazz(nullptr);
#ifndef org_chromium_example_jni_1generator_SampleForTests_clazz_defined
#define org_chromium_example_jni_1generator_SampleForTests_clazz_defined
inline jclass org_chromium_example_jni_1generator_SampleForTests_clazz(JNIEnv* env) {
  return base::android::LazyGetClass(env,
      kClassPath_org_chromium_example_jni_1generator_SampleForTests,
      &g_org_chromium_example_jni_1generator_SampleForTests_clazz);
}
#endif
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_REGISTRATION_EXPORT std::atomic<jclass>
    g_org_chromium_example_jni_1generator_SampleForTests_00024InnerStructA_clazz(nullptr);
#ifndef org_chromium_example_jni_1generator_SampleForTests_00024InnerStructA_clazz_defined
#define org_chromium_example_jni_1generator_SampleForTests_00024InnerStructA_clazz_defined
inline jclass org_chromium_example_jni_1generator_SampleForTests_00024InnerStructA_clazz(JNIEnv*
    env) {
  return base::android::LazyGetClass(env,
      kClassPath_org_chromium_example_jni_1generator_SampleForTests_00024InnerStructA,
      &g_org_chromium_example_jni_1generator_SampleForTests_00024InnerStructA_clazz);
}
#endif
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_REGISTRATION_EXPORT std::atomic<jclass>
    g_org_chromium_example_jni_1generator_SampleForTests_00024InnerStructB_clazz(nullptr);
#ifndef org_chromium_example_jni_1generator_SampleForTests_00024InnerStructB_clazz_defined
#define org_chromium_example_jni_1generator_SampleForTests_00024InnerStructB_clazz_defined
inline jclass org_chromium_example_jni_1generator_SampleForTests_00024InnerStructB_clazz(JNIEnv*
    env) {
  return base::android::LazyGetClass(env,
      kClassPath_org_chromium_example_jni_1generator_SampleForTests_00024InnerStructB,
      &g_org_chromium_example_jni_1generator_SampleForTests_00024InnerStructB_clazz);
}
#endif


// Step 2: Constants (optional).


// Step 3: Method stubs.
namespace base {
namespace android {

static jlong JNI_SampleForTests_Init(JNIEnv* env, const base::android::JavaParamRef<jobject>&
    caller,
    const base::android::JavaParamRef<jstring>& param);

JNI_GENERATOR_EXPORT jlong
    Java_org_chromium_base_natives_GEN_1JNI_org_1chromium_1example_1jni_11generator_1SampleForTests_1init(
    JNIEnv* env,
    jclass jcaller,
    jobject caller,
    jstring param) {
  return JNI_SampleForTests_Init(env, base::android::JavaParamRef<jobject>(env, caller),
      base::android::JavaParamRef<jstring>(env, param));
}

JNI_GENERATOR_EXPORT void
    Java_org_chromium_base_natives_GEN_1JNI_org_1chromium_1example_1jni_11generator_1SampleForTests_1destroy(
    JNIEnv* env,
    jclass jcaller,
    jlong nativeCPPClass,
    jobject caller) {
  CPPClass* native = reinterpret_cast<CPPClass*>(nativeCPPClass);
  CHECK_NATIVE_PTR(env, jcaller, native, "Destroy");
  return native->Destroy(env, base::android::JavaParamRef<jobject>(env, caller));
}

static jdouble JNI_SampleForTests_GetDoubleFunction(JNIEnv* env, const
    base::android::JavaParamRef<jobject>& caller);

JNI_GENERATOR_EXPORT jdouble
    Java_org_chromium_base_natives_GEN_1JNI_org_1chromium_1example_1jni_11generator_1SampleForTests_1getDoubleFunction(
    JNIEnv* env,
    jclass jcaller,
    jobject caller) {
  return JNI_SampleForTests_GetDoubleFunction(env, base::android::JavaParamRef<jobject>(env,
      caller));
}

static jfloat JNI_SampleForTests_GetFloatFunction(JNIEnv* env);

JNI_GENERATOR_EXPORT jfloat
    Java_org_chromium_base_natives_GEN_1JNI_org_1chromium_1example_1jni_11generator_1SampleForTests_1getFloatFunction(
    JNIEnv* env,
    jclass jcaller) {
  return JNI_SampleForTests_GetFloatFunction(env);
}

static void JNI_SampleForTests_SetNonPODDatatype(JNIEnv* env, const
    base::android::JavaParamRef<jobject>& caller,
    const base::android::JavaParamRef<jobject>& rect);

JNI_GENERATOR_EXPORT void
    Java_org_chromium_base_natives_GEN_1JNI_org_1chromium_1example_1jni_11generator_1SampleForTests_1setNonPODDatatype(
    JNIEnv* env,
    jclass jcaller,
    jobject caller,
    jobject rect) {
  return JNI_SampleForTests_SetNonPODDatatype(env, base::android::JavaParamRef<jobject>(env,
      caller), base::android::JavaParamRef<jobject>(env, rect));
}

static base::android::ScopedJavaLocalRef<jobject> JNI_SampleForTests_GetNonPODDatatype(JNIEnv* env,
    const base::android::JavaParamRef<jobject>& caller);

JNI_GENERATOR_EXPORT jobject
    Java_org_chromium_base_natives_GEN_1JNI_org_1chromium_1example_1jni_11generator_1SampleForTests_1getNonPODDatatype(
    JNIEnv* env,
    jclass jcaller,
    jobject caller) {
  return JNI_SampleForTests_GetNonPODDatatype(env, base::android::JavaParamRef<jobject>(env,
      caller)).Release();
}

JNI_GENERATOR_EXPORT jint
    Java_org_chromium_base_natives_GEN_1JNI_org_1chromium_1example_1jni_11generator_1SampleForTests_1method(
    JNIEnv* env,
    jclass jcaller,
    jlong nativeCPPClass,
    jobject caller) {
  CPPClass* native = reinterpret_cast<CPPClass*>(nativeCPPClass);
  CHECK_NATIVE_PTR(env, jcaller, native, "Method", 0);
  return native->Method(env, base::android::JavaParamRef<jobject>(env, caller));
}

JNI_GENERATOR_EXPORT jdouble
    Java_org_chromium_base_natives_GEN_1JNI_org_1chromium_1example_1jni_11generator_1SampleForTests_1methodOtherP0(
    JNIEnv* env,
    jclass jcaller,
    jlong nativePtr,
    jobject caller) {
  CPPClass::InnerClass* native = reinterpret_cast<CPPClass::InnerClass*>(nativePtr);
  CHECK_NATIVE_PTR(env, jcaller, native, "MethodOtherP0", 0);
  return native->MethodOtherP0(env, base::android::JavaParamRef<jobject>(env, caller));
}

JNI_GENERATOR_EXPORT void
    Java_org_chromium_base_natives_GEN_1JNI_org_1chromium_1example_1jni_11generator_1SampleForTests_1addStructB(
    JNIEnv* env,
    jclass jcaller,
    jlong nativeCPPClass,
    jobject caller,
    jobject b) {
  CPPClass* native = reinterpret_cast<CPPClass*>(nativeCPPClass);
  CHECK_NATIVE_PTR(env, jcaller, native, "AddStructB");
  return native->AddStructB(env, base::android::JavaParamRef<jobject>(env, caller),
      base::android::JavaParamRef<jobject>(env, b));
}

JNI_GENERATOR_EXPORT void
    Java_org_chromium_base_natives_GEN_1JNI_org_1chromium_1example_1jni_11generator_1SampleForTests_1iterateAndDoSomethingWithStructB(
    JNIEnv* env,
    jclass jcaller,
    jlong nativeCPPClass,
    jobject caller) {
  CPPClass* native = reinterpret_cast<CPPClass*>(nativeCPPClass);
  CHECK_NATIVE_PTR(env, jcaller, native, "IterateAndDoSomethingWithStructB");
  return native->IterateAndDoSomethingWithStructB(env, base::android::JavaParamRef<jobject>(env,
      caller));
}

JNI_GENERATOR_EXPORT jstring
    Java_org_chromium_base_natives_GEN_1JNI_org_1chromium_1example_1jni_11generator_1SampleForTests_1returnAString(
    JNIEnv* env,
    jclass jcaller,
    jlong nativeCPPClass,
    jobject caller) {
  CPPClass* native = reinterpret_cast<CPPClass*>(nativeCPPClass);
  CHECK_NATIVE_PTR(env, jcaller, native, "ReturnAString", NULL);
  return native->ReturnAString(env, base::android::JavaParamRef<jobject>(env, caller)).Release();
}


static std::atomic<jmethodID>
    g_org_chromium_example_jni_1generator_SampleForTests_javaMethod(nullptr);
static jint Java_SampleForTests_javaMethod(JNIEnv* env, const base::android::JavaRef<jobject>& obj,
    JniIntWrapper foo,
    JniIntWrapper bar) {
  jclass clazz = org_chromium_example_jni_1generator_SampleForTests_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_chromium_example_jni_1generator_SampleForTests_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "javaMethod",
          "(II)I",
          &g_org_chromium_example_jni_1generator_SampleForTests_javaMethod);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id, as_jint(foo), as_jint(bar));
  return ret;
}

static std::atomic<jmethodID>
    g_org_chromium_example_jni_1generator_SampleForTests_staticJavaMethod(nullptr);
static jboolean Java_SampleForTests_staticJavaMethod(JNIEnv* env) {
  jclass clazz = org_chromium_example_jni_1generator_SampleForTests_clazz(env);
  CHECK_CLAZZ(env, clazz,
      org_chromium_example_jni_1generator_SampleForTests_clazz(env), false);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "staticJavaMethod",
          "()Z",
          &g_org_chromium_example_jni_1generator_SampleForTests_staticJavaMethod);

  jboolean ret =
      env->CallStaticBooleanMethod(clazz,
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID>
    g_org_chromium_example_jni_1generator_SampleForTests_packagePrivateJavaMethod(nullptr);
static void Java_SampleForTests_packagePrivateJavaMethod(JNIEnv* env, const
    base::android::JavaRef<jobject>& obj) {
  jclass clazz = org_chromium_example_jni_1generator_SampleForTests_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_chromium_example_jni_1generator_SampleForTests_clazz(env));

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "packagePrivateJavaMethod",
          "()V",
          &g_org_chromium_example_jni_1generator_SampleForTests_packagePrivateJavaMethod);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id);
}

static std::atomic<jmethodID>
    g_org_chromium_example_jni_1generator_SampleForTests_methodWithGenericParams(nullptr);
static void Java_SampleForTests_methodWithGenericParams(JNIEnv* env, const
    base::android::JavaRef<jobject>& obj, const base::android::JavaRef<jobject>& foo,
    const base::android::JavaRef<jobject>& bar) {
  jclass clazz = org_chromium_example_jni_1generator_SampleForTests_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_chromium_example_jni_1generator_SampleForTests_clazz(env));

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "methodWithGenericParams",
          "(Ljava/util/Map;Ljava/util/LinkedList;)V",
          &g_org_chromium_example_jni_1generator_SampleForTests_methodWithGenericParams);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id, foo.obj(), bar.obj());
}

static std::atomic<jmethodID>
    g_org_chromium_example_jni_1generator_SampleForTests_Constructor(nullptr);
static base::android::ScopedJavaLocalRef<jobject> Java_SampleForTests_Constructor(JNIEnv* env,
    JniIntWrapper foo,
    JniIntWrapper bar) {
  jclass clazz = org_chromium_example_jni_1generator_SampleForTests_clazz(env);
  CHECK_CLAZZ(env, clazz,
      org_chromium_example_jni_1generator_SampleForTests_clazz(env), NULL);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "<init>",
          "(II)V",
          &g_org_chromium_example_jni_1generator_SampleForTests_Constructor);

  jobject ret =
      env->NewObject(clazz,
          call_context.base.method_id, as_jint(foo), as_jint(bar));
  return base::android::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID>
    g_org_chromium_example_jni_1generator_SampleForTests_methodThatThrowsException(nullptr);
static void Java_SampleForTests_methodThatThrowsException(JNIEnv* env, const
    base::android::JavaRef<jobject>& obj) {
  jclass clazz = org_chromium_example_jni_1generator_SampleForTests_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_chromium_example_jni_1generator_SampleForTests_clazz(env));

  jni_generator::JniJavaCallContextUnchecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "methodThatThrowsException",
          "()V",
          &g_org_chromium_example_jni_1generator_SampleForTests_methodThatThrowsException);

     env->CallVoidMethod(obj.obj(),
          call_context.method_id);
}

static std::atomic<jmethodID>
    g_org_chromium_example_jni_1generator_SampleForTests_javaMethodWithAnnotatedParam(nullptr);
static void Java_SampleForTests_javaMethodWithAnnotatedParam(JNIEnv* env, const
    base::android::JavaRef<jobject>& obj, JniIntWrapper foo,
    JniIntWrapper bar,
    JniIntWrapper baz,
    JniIntWrapper bat) {
  jclass clazz = org_chromium_example_jni_1generator_SampleForTests_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_chromium_example_jni_1generator_SampleForTests_clazz(env));

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "javaMethodWithAnnotatedParam",
          "(IIII)V",
          &g_org_chromium_example_jni_1generator_SampleForTests_javaMethodWithAnnotatedParam);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id, as_jint(foo), as_jint(bar), as_jint(baz), as_jint(bat));
}

static std::atomic<jmethodID>
    g_org_chromium_example_jni_1generator_SampleForTests_00024InnerStructA_create(nullptr);
static base::android::ScopedJavaLocalRef<jobject> Java_InnerStructA_create(JNIEnv* env, jlong l,
    JniIntWrapper i,
    const base::android::JavaRef<jstring>& s) {
  jclass clazz = org_chromium_example_jni_1generator_SampleForTests_00024InnerStructA_clazz(env);
  CHECK_CLAZZ(env, clazz,
      org_chromium_example_jni_1generator_SampleForTests_00024InnerStructA_clazz(env), NULL);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "create",
          "(JILjava/lang/String;)Lorg/chromium/example/jni_generator/SampleForTests$InnerStructA;",
          &g_org_chromium_example_jni_1generator_SampleForTests_00024InnerStructA_create);

  jobject ret =
      env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, l, as_jint(i), s.obj());
  return base::android::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID>
    g_org_chromium_example_jni_1generator_SampleForTests_addStructA(nullptr);
static void Java_SampleForTests_addStructA(JNIEnv* env, const base::android::JavaRef<jobject>& obj,
    const base::android::JavaRef<jobject>& a) {
  jclass clazz = org_chromium_example_jni_1generator_SampleForTests_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_chromium_example_jni_1generator_SampleForTests_clazz(env));

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "addStructA",
          "(Lorg/chromium/example/jni_generator/SampleForTests$InnerStructA;)V",
          &g_org_chromium_example_jni_1generator_SampleForTests_addStructA);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id, a.obj());
}

static std::atomic<jmethodID>
    g_org_chromium_example_jni_1generator_SampleForTests_iterateAndDoSomething(nullptr);
static void Java_SampleForTests_iterateAndDoSomething(JNIEnv* env, const
    base::android::JavaRef<jobject>& obj) {
  jclass clazz = org_chromium_example_jni_1generator_SampleForTests_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_chromium_example_jni_1generator_SampleForTests_clazz(env));

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "iterateAndDoSomething",
          "()V",
          &g_org_chromium_example_jni_1generator_SampleForTests_iterateAndDoSomething);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id);
}

static std::atomic<jmethodID>
    g_org_chromium_example_jni_1generator_SampleForTests_00024InnerStructB_getKey(nullptr);
static jlong Java_InnerStructB_getKey(JNIEnv* env, const base::android::JavaRef<jobject>& obj) {
  jclass clazz = org_chromium_example_jni_1generator_SampleForTests_00024InnerStructB_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_chromium_example_jni_1generator_SampleForTests_00024InnerStructB_clazz(env), 0);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getKey",
          "()J",
          &g_org_chromium_example_jni_1generator_SampleForTests_00024InnerStructB_getKey);

  jlong ret =
      env->CallLongMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID>
    g_org_chromium_example_jni_1generator_SampleForTests_00024InnerStructB_getValue(nullptr);
static base::android::ScopedJavaLocalRef<jstring> Java_InnerStructB_getValue(JNIEnv* env, const
    base::android::JavaRef<jobject>& obj) {
  jclass clazz = org_chromium_example_jni_1generator_SampleForTests_00024InnerStructB_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_chromium_example_jni_1generator_SampleForTests_00024InnerStructB_clazz(env), NULL);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getValue",
          "()Ljava/lang/String;",
          &g_org_chromium_example_jni_1generator_SampleForTests_00024InnerStructB_getValue);

  jstring ret =
      static_cast<jstring>(env->CallObjectMethod(obj.obj(),
          call_context.base.method_id));
  return base::android::ScopedJavaLocalRef<jstring>(env, ret);
}

static std::atomic<jmethodID>
    g_org_chromium_example_jni_1generator_SampleForTests_getInnerInterface(nullptr);
static base::android::ScopedJavaLocalRef<jobject> Java_SampleForTests_getInnerInterface(JNIEnv* env)
    {
  jclass clazz = org_chromium_example_jni_1generator_SampleForTests_clazz(env);
  CHECK_CLAZZ(env, clazz,
      org_chromium_example_jni_1generator_SampleForTests_clazz(env), NULL);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "getInnerInterface",
          "()Lorg/chromium/example/jni_generator/SampleForTests$InnerInterface;",
          &g_org_chromium_example_jni_1generator_SampleForTests_getInnerInterface);

  jobject ret =
      env->CallStaticObjectMethod(clazz,
          call_context.base.method_id);
  return base::android::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID>
    g_org_chromium_example_jni_1generator_SampleForTests_getInnerEnum(nullptr);
static base::android::ScopedJavaLocalRef<jobject> Java_SampleForTests_getInnerEnum(JNIEnv* env) {
  jclass clazz = org_chromium_example_jni_1generator_SampleForTests_clazz(env);
  CHECK_CLAZZ(env, clazz,
      org_chromium_example_jni_1generator_SampleForTests_clazz(env), NULL);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "getInnerEnum",
          "()Lorg/chromium/example/jni_generator/SampleForTests$InnerEnum;",
          &g_org_chromium_example_jni_1generator_SampleForTests_getInnerEnum);

  jobject ret =
      env->CallStaticObjectMethod(clazz,
          call_context.base.method_id);
  return base::android::ScopedJavaLocalRef<jobject>(env, ret);
}

}  // namespace android
}  // namespace base

// Step 4: Generated test functions (optional).


#endif  // org_chromium_example_jni_generator_SampleForTests_JNI
