[/
Copyright 2019 <PERSON>
(glen<PERSON><EMAIL>)

Distributed under the Boost Software License, Version 1.0.
(http://www.boost.org/LICENSE_1_0.txt)
]

[section:default_allocator default_allocator]

[simplesect Authors]

* <PERSON>

[endsimplesect]

[section Overview]

The header <boost/core/default_allocator.hpp> provides the class template
`boost::default_allocator` to serve as a minimal default allocator that:

* Like C++2a's `std::allocator`, does not provide members such as `construct()`
and `destroy()` to be eligible for optimizations by allocator-aware code that
detects the absence of these members to provide more optimal construction.
* Supports `BOOST_NO_EXCEPTIONS` in allocation.
* Does not have `std` as an associated namespace.

[endsect]

[section Examples]

The following snippet shows the use of this allocator as the default allocator
for a container.

```
template<class Key, class Compare = std::less<Key>,
    class Allocator = boost::default_allocator<Key> >
class FlatSet;
```

Facilities like `make_shared` can be implemented using `allocate_shared` with
`default_allocator`.

```
template<class T, class... Args>
enable_if_t<!is_array_v<T>, shared_ptr<T> >
make_shared(Args&&... args)
{
    return allocate_shared<T>(boost::default_allocator<remove_cv_t<T> >(),
        std::forward<Args>(args)...);
}
```

[endsect]

[section Reference]

```
namespace boost {

template<class T>
struct default_allocator {
    typedef T value_type;
    typedef T* pointer;
    typedef const T* const_pointer;
    typedef std::add_lvalue_reference_t<T> reference;
    typedef std::add_lvalue_reference_t<const T> const_reference;
    typedef std::size_t size_type;
    typedef std::ptrdiff_t difference_type;
    typedef ``['true_type]`` propagate_on_container_move_assignment;
    typedef ``['true_type]`` is_always_equal;

    template<class U>
    struct rebind {
        typedef default_allocator<U> other;
    };

    constexpr default_allocator() = default;

    template<class U>
    constexpr default_allocator(const default_allocator<U>&) noexcept { }

    constexpr std::size_t max_size() const noexcept;
    T* allocate(std::size_t n);
    void deallocate(T* p, std::size_t);
};

template<class T, class U>
constexpr bool operator==(const default_allocator<T>&,
    const default_allocator<U>&) noexcept;

template<class T, class U>
constexpr bool operator!=(const default_allocator<T>&,
    const default_allocator<U>&) noexcept;

} /* boost */
```

[section Members]

[variablelist
[[`constexpr std::size_t max_size() const noexcept;`]
[[variablelist
[[Returns][The largest value `N` for which the call `allocate(N)` might
succeed.]]]]]
[[`T* allocate(std::size_t n);`]
[[variablelist
[[Returns]
[A pointer to the initial element of an array of storage of size
`n * sizeof(T)`, aligned appropriately for objects of type `T`.]]
[[Remarks][The storage is obtained by calling `::operator new`.]]
[[Throws][`std::bad_alloc` if the storage cannot be obtained.]]]]]
[[`void deallocate(T* p, std::size_t n);`]
[[variablelist
[[Requires]
[`p` shall be a pointer value obtained from `allocate()`. `n` shall equal the
value passed as the first argument to the invocation of `allocate` which
returned `p`.]]
[[Effects][Deallocates the storage referenced by `p`.]]
[[Remarks][Uses `::operator delete`.]]]]]]

[endsect]

[section Operators]

[variablelist
[[`template<class T, class U> constexpr bool operator==(const
default_allocator<T>&, const default_allocator<U>&) noexcept;`]
[[variablelist
[[Returns][`true`.]]]]]
[[`template<class T, class U> constexpr bool operator!=(const
default_allocator<T>&, const default_allocator<U>&) noexcept;`]
[[variablelist
[[Returns][`false`.]]]]]]

[endsect]

[endsect]

[endsect]
