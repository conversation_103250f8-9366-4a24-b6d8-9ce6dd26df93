#include <StdInc.h>
#include <VFSRagePackfile7.h>

#include <botan/sha2_64.h>
#include <botan/pubkey.h>
#include <botan/rsa.h>
#include <botan/ber_dec.h>
#include <botan/auto_rng.h>

static uint8_t rsa<PERSON>ey[270] = {
	0x30, 0x82, 0x01, 0x0A, 0x02, 0x82, 0x01, 0x01, 0x00, 0xAA, 0x7F, 0x7B, 0x7F, 0xED, 0x07, 0x68,
	0xC1, 0xC0, 0xC3, 0xB0, 0x20, 0xDD, 0xA8, 0x29, 0xDD, 0x56, 0x38, 0x8A, 0xD0, 0x6C, 0x45, 0xDC,
	0xB8, 0xD5, 0xDD, 0xFC, 0x1B, 0x31, 0x1D, 0x3A, 0x72, 0xEB, 0x5F, 0xC7, 0x8C, 0x53, 0x30, 0xCA,
	0x31, 0x2E, 0x3C, 0x5C, 0x06, 0xD8, 0xF3, 0x8C, 0xC1, 0xA4, 0x90, 0x22, 0x46, 0xD9, 0xE1, 0x72,
	0xA9, 0x50, 0x00, 0x8E, 0x2A, 0x57, 0xF3, 0x70, 0x80, 0xFA, 0x2C, 0xE8, 0x58, 0x47, 0x9A, 0x1B,
	0x89, 0x37, 0xD1, 0x1E, 0x16, 0xCC, 0x3D, 0x75, 0xA8, 0x92, 0x42, 0xAC, 0x9A, 0xB4, 0x26, 0x58,
	0x0B, 0xAB, 0x44, 0xC0, 0x1E, 0x56, 0xAF, 0x55, 0x89, 0x00, 0x57, 0x2F, 0x43, 0x2A, 0xC6, 0xFB,
	0x3C, 0xD7, 0xAC, 0xBF, 0x03, 0x22, 0xA1, 0x70, 0x2F, 0x23, 0x47, 0x21, 0xDA, 0x84, 0x3D, 0xFF,
	0x95, 0x30, 0xC3, 0xD0, 0x46, 0x6F, 0xF5, 0x53, 0x70, 0xE7, 0xBE, 0xFE, 0x3C, 0x39, 0xA7, 0x7D,
	0x2F, 0x9E, 0x8F, 0xD7, 0x0F, 0xB7, 0xF9, 0xFD, 0x68, 0x5C, 0x2A, 0xC4, 0xFD, 0xF0, 0x1A, 0x55,
	0x37, 0xF0, 0x61, 0x73, 0x8E, 0x60, 0x7F, 0x81, 0xC0, 0x8C, 0x0D, 0xF2, 0xC4, 0x5C, 0x3D, 0x00,
	0xBA, 0xEC, 0xC7, 0x13, 0xB7, 0x38, 0xDC, 0x1C, 0xDD, 0x2F, 0xC2, 0x94, 0x20, 0xAF, 0x48, 0x70,
	0x59, 0xA1, 0x9B, 0x55, 0x72, 0x7B, 0x6F, 0xDB, 0x88, 0x91, 0xEE, 0x18, 0x0A, 0x43, 0x47, 0x6D,
	0xCF, 0xA1, 0x6B, 0x4B, 0x87, 0x2E, 0x6C, 0xD8, 0x60, 0x89, 0x56, 0xAB, 0x45, 0xCF, 0x22, 0x01,
	0xF4, 0x5F, 0x8E, 0xD2, 0x32, 0xF4, 0x6C, 0x66, 0x48, 0x14, 0x82, 0x5B, 0xB8, 0x2F, 0x5A, 0xD7,
	0xC8, 0xAF, 0xBE, 0x05, 0xBD, 0x54, 0xD6, 0xD2, 0x71, 0x66, 0x20, 0xCF, 0xDE, 0x17, 0xDA, 0x94,
	0xCE, 0x06, 0xEB, 0xA6, 0xAB, 0xF8, 0x50, 0xEC, 0xA9, 0x02, 0x03, 0x01, 0x00, 0x01
};

static bool ValidatePackfile(const std::vector<uint8_t>& headerSignature, const std::vector<uint8_t>& entries)
{
	auto hash = Botan::HashFunction::create("SHA-384");
	hash->update(entries);

	auto hashData = hash->final();

	Botan::BigInt n, e;

	Botan::BER_Decoder(rsaKey, sizeof(rsaKey))
	.start_cons(Botan::SEQUENCE)
	.decode(n)
	.decode(e)
	.end_cons();

	Botan::AutoSeeded_RNG rng;
	auto pk = Botan::RSA_PublicKey(n, e);

	auto signer = std::make_unique<Botan::PK_Verifier>(pk, "EMSA_PKCS1(SHA-384)");

	return signer->verify_message(hashData, headerSignature);
}

static InitFunction initFunction([]
{
	vfs::RagePackfile7::SetValidationCallback(ValidatePackfile);
});
