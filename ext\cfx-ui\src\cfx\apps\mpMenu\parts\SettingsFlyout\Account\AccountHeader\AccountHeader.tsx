import {
  Avatar,
  Button,
  Flex,
  Text,
} from '@cfx-dev/ui-components';
import { observer } from 'mobx-react-lite';

import { useStreamerMode } from 'cfx/apps/mpMenu/services/convars/convars.service';
import { useCustomAuthService, AuthUser } from 'cfx/apps/mpMenu/services/customAuth/customAuth.service';
import { useLegalService } from 'cfx/apps/mpMenu/services/legal/legal.service';
import { $L } from 'cfx/common/services/intl/l10n';

export const AccountHeader = observer(function AccountHeader() {
  const CustomAuthService = useCustomAuthService();
  const LegalService = useLegalService();

  // Only use custom auth - completely remove CFX authentication
  if (CustomAuthService.isAuthenticated && CustomAuthService.user && CustomAuthService.user.username) {
    return (
      <CustomAuthHeaderSignedIn />
    );
  }

  // Show login button if not authenticated with custom auth
  const handleCustomAuthClick = () => {
    LegalService.forceShowAuth();
  };

  return (
    <Button size="large" text="Đăng nhập" theme="primary" onClick={handleCustomAuthClick} />
  );
});

const CustomAuthHeaderSignedIn = observer(function CustomAuthHeaderSignedIn() {
  const CustomAuthService = useCustomAuthService();
  const user = CustomAuthService.user;

  // Add null safety checks
  if (!user || !user.username || !user.email) {
    return (
      <Flex centered="axis" repell>
        <span>Loading user data...</span>
        <Button size="small" text="Sign Out" theme="transparent" onClick={CustomAuthService.logout} />
      </Flex>
    );
  }

  const joinDate = user.createdAt ? new Date(user.createdAt).toLocaleDateString() : 'Unknown';
  const userInitial = user.username.charAt(0).toUpperCase();

  const node = useStreamerMode()
    ? (
      <span>{'<HIDDEN>'}</span>
    )
    : (
      <Flex centered gap="large">
        <div style={{
          width: '40px',
          height: '40px',
          borderRadius: '50%',
          backgroundColor: '#f0f0f0',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          fontSize: '18px',
          fontWeight: 'bold',
          color: '#333'
        }}>
          {userInitial}
        </div>

        <Flex vertical gap="thin">
          <Text size="large">{user.username}</Text>
          <Text size="small" opacity="75">{user.email}</Text>
          <Text size="small" opacity="50">Member since {joinDate}</Text>
        </Flex>
      </Flex>
    );

  return (
    <Flex centered="axis" repell>
      {node}

      <Button size="small" text="Sign Out" theme="transparent" onClick={CustomAuthService.logout} />
    </Flex>
  );
});

// CFX AccountHeaderSignedIn removed - only using custom auth
