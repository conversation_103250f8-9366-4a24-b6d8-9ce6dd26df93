[/
  Copyright 2014 <PERSON>

  Distributed under the Boost Software License, Version 1.0.

  See accompanying file LICENSE_1_0.txt
  or copy at http://boost.org/LICENSE_1_0.txt
]

[section:typeinfo typeinfo]

[simplesect Authors]

* <PERSON>

[endsimplesect]

[section Header <boost/core/typeinfo.hpp>]

The header `<boost/core/typeinfo.hpp>` defines a class
`boost::core::typeinfo`, which is an alias for `std::type_info`
when RTTI is enabled, and is a reasonable substitute when RTTI
is not supported.

The macro `BOOST_CORE_TYPEID`, when applied to a type `T`, is the
equivalent of `typeid(T)` and produces a reference to a const
`typeinfo` object.

The function `boost::core::demangled_name` takes a
`boost::core::typeinfo const & ti` and either returns `ti.name()`,
when that string doesn't need to be demangled, or
`boost::core::demangle(ti.name())`, when it does. The return type
of `boost::core::demangled_name` is `char const*` in the first case
and `std::string` in the second.

[section Synopsis]

``
namespace boost
{

namespace core
{
    class typeinfo;
    /* char const* or std::string */ demangled_name( typeinfo const & ti );
}

}

#define BOOST_CORE_TYPEID(T) /*unspecified*/
``

[endsect]

[section Example]

``
#include <boost/core/typeinfo.hpp>
#include <iostream>

template<class T1, class T2> struct X
{
};

int main()
{
    typedef X<void const*, void(*)(float)> T;

    boost::core::typeinfo const & ti = BOOST_CORE_TYPEID(T);

    std::cout << boost::core::demangled_name( ti ) << std::endl;
}
``

[endsect]

[endsect]

[endsect]
