////
Copyright 2005-2008 <PERSON>
Copyright 2022 <PERSON>
Copyright 2022 <PERSON>
Distributed under the Boost Software License, Version 1.0.
https://www.boost.org/LICENSE_1_0.txt
////

[#links]
= Links
:idprefix: links_

*A Proposal to Add Hash Tables to the Standard Library* +
http://www.open-std.org/JTC1/SC22/WG21/docs/papers/2003/n1456.html

The hash table proposal explains much of the design. The hash function object is discussed in Section D.

---

*The {cpp} Standard Library Technical Report* +
http://www.open-std.org/jtc1/sc22/wg21/docs/papers/2005/n1836.pdf

Contains the hash function specification in section 6.3.2.

---

*Library Extension Technical Report Issues List* +
http://www.open-std.org/jtc1/sc22/wg21/docs/papers/2005/n1837.pdf

The library implements the extension described in Issue 6.18, pages 63-67.

---

*Methods for Identifying Versioned and Plagiarised Documents* +
_<PERSON>, <PERSON> +
https://people.eng.unimelb.edu.au/jzobel/fulltext/jasist03thz.pdf

Contains the hash function that the initial implementation of `boost::hash_combine` was based on.

---

*Performance in Practice of String Hashing Functions* +
_M.V. Ramakrishna, J. Zobel_ +
In Proc. Int. Conf. on Database Systems for Advanced Applications, pages 215-223, Melbourne, Australia, April 1997. +
https://www.comp.nus.edu.sg/~lingtw/dasfaa_proceedings/DASFAA97/P215.pdf

Referenced in the above paper as the source of the hash function.

---

*MurmurHash3 hash function source* +
_Austin Appleby_ +
https://github.com/aappleby/smhasher/blob/61a0530f28277f2e850bfc39600ce61d02b518de/src/MurmurHash3.cpp#L65-L90

Austin Appleby's 32 and 64 bit finalization mixing functions that
introduced the "xmxmx" general form of a high quality bijective
transformation that approximates a random permutation.

---

*SMHasher hash function test suite* +
_Austin Appleby_ +
https://github.com/aappleby/smhasher

Contains a battery of tests for evaluating hash functions. The current
64 bit implementation of `boost::hash` for strings passes SMHasher.
Previous iterations did not.

---

*Better Bit Mixing - Improving on MurmurHash3's 64-bit Finalizer* +
_David Stafford_ +
https://zimbry.blogspot.com/2011/09/better-bit-mixing-improving-on.html

Describes the so-called "variant 13" mixing function, an improvement
over `fmix64` from MurmurHash3, made famous by its adoption by the
`splitmix64` http://xorshift.di.unimi.it/splitmix64.c[random number generator].

---

*Stronger, better, morer, Moremur; a better Murmur3-type mixer* +
_Pelle Evensen_ +
https://mostlymangling.blogspot.com/2019/12/stronger-better-morer-moremur-better.html

Describes Moremur, an improvement over MurmurHash3 `fmix64` and Stafford
"variant 13".

---

*Improved mx3 and the RRC test* +
_Jon Maiga_ +
http://jonkagstrom.com/mx3/mx3_rev2.html

Contains another improvement over MurmurHash3 `fmix64` and "variant 13". This
is what the current implementation of `boost::hash_combine` uses when
`std::size_t` is 64 bits.

---

*Prospecting for Hash Functions* +
_Chris Wellons_ +
https://nullprogram.com/blog/2018/07/31/

Describes https://github.com/skeeto/hash-prospector[Hash Prospector],
a utility for discovering and evaluating mixing functions.

---

*New best known functions* +
_"TheIronBorn"_ +
https://github.com/skeeto/hash-prospector/issues/19

Describes a good 32 bit mixing function, used by the current implementation
of `boost::hash_combine` when `std::size_t` is 32 bits.
