# (C) Copyright 2008 <PERSON>
#
# Distributed under the Boost Software License, Version 1.0. (See accompanying 
# file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

project context/doc ;

import boostbook ;
import quickbook ;
import modules ;

path-constant here : . ;

boostbook context
    :
        context.qbk
    :
        # Path for links to Boost:
        <xsl:param>boost.root=../../../..
        # HTML options first:
        # How far down we chunk nested sections, basically all of them:
        <xsl:param>chunk.section.depth=3
        # Don't put the first section on the same page as the TOC:
        <xsl:param>chunk.first.sections=1
        # How far down sections get TOC's
        <xsl:param>toc.section.depth=10
        # Max depth in each TOC:
        <xsl:param>toc.max.depth=3
        # How far down we go with TOC's
        <xsl:param>generate.section.toc.level=10
        # Absolute path for images:
        <format>pdf:<xsl:param>img.src.path=$(here)/html/
    ;

###############################################################################
alias boostdoc ;
explicit boostdoc ;
alias boostrelease : context ;
explicit boostrelease ;
