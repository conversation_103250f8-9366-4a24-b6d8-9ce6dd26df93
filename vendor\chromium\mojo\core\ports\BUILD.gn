# Copyright 2016 The Chromium Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//testing/test.gni")

component("ports") {
  output_name = "mojo_core_ports"

  sources = [
    "event.cc",
    "event.h",
    "message_filter.h",
    "message_queue.cc",
    "message_queue.h",
    "name.cc",
    "name.h",
    "node.cc",
    "node.h",
    "node_delegate.h",
    "port.cc",
    "port.h",
    "port_locker.cc",
    "port_locker.h",
    "port_ref.cc",
    "port_ref.h",
    "user_data.h",
    "user_message.cc",
    "user_message.h",
  ]

  defines = [ "IS_MOJO_CORE_PORTS_IMPL" ]

  public_deps = [ "//base" ]

  if (!is_nacl) {
    deps = [ "//crypto" ]
  }
}

source_set("tests") {
  testonly = true

  sources = [
    "name_unittest.cc",
    "ports_unittest.cc",
  ]

  deps = [
    ":ports",
    "//base",
    "//base/test:test_support",
    "//testing/gtest",
  ]
}
