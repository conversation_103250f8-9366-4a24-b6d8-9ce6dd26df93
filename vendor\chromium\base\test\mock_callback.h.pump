$$ This is a pump file for generating file templates.  Pump is a python
$$ script that is part of the Google Test suite of utilities.  Description
$$ can be found here:
$$
$$ https://github.com/google/googletest/blob/master/googletest/docs/PumpManual.md
$$
$$ MAX_ARITY controls the number of arguments that MockCallback supports.
$$ It is choosen to match the number GMock supports.
$var MAX_ARITY = 10
$$
// Copyright 2017 The Chromium Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// Analogous to GMock's built-in MockFunction, but for base::Callback instead of
// std::function. It takes the full callback type as a parameter, so that it can
// support both OnceCallback and RepeatingCallback. Furthermore, this file
// defines convenience typedefs in the form of MockOnceCallback<Signature>,
// MockRepeatingCallback<Signature>, MockOnceClosure and MockRepeatingClosure.
//
// Use:
//   using FooCallback = base::RepeatingCallback<int(std::string)>;
//
//   TEST(FooTest, RunsCallbackWithBarArgument) {
//     base::MockCallback<FooCallback> callback;
//     EXPECT_CALL(callback, Run("bar")).WillOnce(Return(1));
//     Foo(callback.Get());
//   }
//
// Or equivalently:
//
//   TEST(FooTest, RunsCallbackWithBarArgument) {
//     base::MockRepeatingCallback<int(std::string)> callback;
//     EXPECT_CALL(callback, Run("bar")).WillOnce(Return(1));
//     Foo(callback.Get());
//   }
//
//
// Can be used with StrictMock and NiceMock. Caller must ensure that it outlives
// any base::Callback obtained from it.

#ifndef BASE_TEST_MOCK_CALLBACK_H_
#define BASE_TEST_MOCK_CALLBACK_H_

#include "base/bind.h"
#include "base/callback.h"
#include "base/macros.h"
#include "testing/gmock/include/gmock/gmock.h"

namespace base {

// clang-format off

template <typename F>
class MockCallback;

template <typename Signature>
using MockOnceCallback = MockCallback<OnceCallback<Signature>>;
template <typename Signature>
using MockRepeatingCallback = MockCallback<RepeatingCallback<Signature>>;

using MockOnceClosure = MockCallback<OnceClosure>;
using MockRepeatingClosure = MockCallback<RepeatingClosure>;

$range i 0..MAX_ARITY
$for i [[
$range j 1..i
$var run_type = [[R($for j, [[A$j]])]]

template <typename R$for j [[, typename A$j]]>
class MockCallback<RepeatingCallback<$run_type>> {
 public:
  MockCallback() = default;
  MOCK_METHOD$(i)_T(Run, $run_type);

  RepeatingCallback<$run_type> Get() {
    return BindRepeating(&MockCallback::Run, Unretained(this));
  }

 private:
  DISALLOW_COPY_AND_ASSIGN(MockCallback);
};

template <typename R$for j [[, typename A$j]]>
class MockCallback<OnceCallback<$run_type>> {
 public:
  MockCallback() = default;
  MOCK_METHOD$(i)_T(Run, $run_type);

  OnceCallback<$run_type> Get() {
    return BindOnce(&MockCallback::Run, Unretained(this));
  }

 private:
  DISALLOW_COPY_AND_ASSIGN(MockCallback);
};

]]

// clang-format on

}  // namespace base

#endif  // BASE_TEST_MOCK_CALLBACK_H_
