# Copyright 2016 The Chromium Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

mojom = "//mojo/public/mojom/base/time.mojom"
public_headers = [ "//base/time/time.h" ]
traits_headers = [ "//mojo/public/cpp/base/time_mojom_traits.h" ]
public_deps = [
  "//base",
  "//mojo/public/cpp/base:shared_typemap_traits",
]

type_mappings = [
  "mojo_base.mojom.Time=::base::Time[copyable_pass_by_value]",
  "mojo_base.mojom.TimeDelta=::base::TimeDelta[copyable_pass_by_value]",
  "mojo_base.mojom.TimeTicks=::base::TimeTicks[copyable_pass_by_value]",
]
