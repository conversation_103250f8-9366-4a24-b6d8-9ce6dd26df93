# Copyright 2016 The Chromium Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

mojom = "//mojo/public/mojom/base/unguessable_token.mojom"
public_headers = [ "//base/unguessable_token.h" ]
traits_headers = [ "//mojo/public/cpp/base/unguessable_token_mojom_traits.h" ]
public_deps = [
  "//mojo/public/cpp/base:shared_typemap_traits",
]

type_mappings = [ "mojo_base.mojom.UnguessableToken=::base::UnguessableToken" ]
