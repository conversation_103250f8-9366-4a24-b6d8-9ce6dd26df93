Compiled from "InputStream.java"
public abstract class java.io.InputStream extends java.lang.Object implements java.io.Closeable
  SourceFile: "InputStream.java"
  minor version: 0
  major version: 49
  Constant pool:
const #1 = Method #6.#39; //  java/lang/Object."<init>":()V
const #2 = class  #40;  //  java/lang/RuntimeException
const #3 = String #41;  //  Stub!
const #4 = Method #2.#42; //  java/lang/RuntimeException."<init>":(Ljava/lang/String;)V
const #5 = class  #43;  //  java/io/InputStream
const #6 = class  #44;  //  java/lang/Object
const #7 = class  #45;  //  java/io/Closeable
const #8 = Asciz  <init>;
const #9 = Asciz  ()V;
const #10 = Asciz Code;
const #11 = Asciz LineNumberTable;
const #12 = Asciz LocalVariableTable;
const #13 = Asciz this;
const #14 = Asciz Ljava/io/InputStream;;
const #15 = Asciz available;
const #16 = Asciz ()I;
const #17 = Asciz Exceptions;
const #18 = class #46;  //  java/io/IOException
const #19 = Asciz close;
const #20 = Asciz mark;
const #21 = Asciz (I)V;
const #22 = Asciz readlimit;
const #23 = Asciz I;
const #24 = Asciz markSupported;
const #25 = Asciz ()Z;
const #26 = Asciz read;
const #27 = Asciz ([B)I;
const #28 = Asciz buffer;
const #29 = Asciz [B;
const #30 = Asciz ([BII)I;
const #31 = Asciz byteOffset;
const #32 = Asciz byteCount;
const #33 = Asciz reset;
const #34 = Asciz skip;
const #35 = Asciz (J)J;
const #36 = Asciz J;
const #37 = Asciz SourceFile;
const #38 = Asciz InputStream.java;
const #39 = NameAndType #8:#9;//  "<init>":()V
const #40 = Asciz java/lang/RuntimeException;
const #41 = Asciz Stub!;
const #42 = NameAndType #8:#47;//  "<init>":(Ljava/lang/String;)V
const #43 = Asciz java/io/InputStream;
const #44 = Asciz java/lang/Object;
const #45 = Asciz java/io/Closeable;
const #46 = Asciz java/io/IOException;
const #47 = Asciz (Ljava/lang/String;)V;

{
public java.io.InputStream();
  Signature: ()V
  Code:
   Stack=3, Locals=1, Args_size=1
   0: aload_0
   1: invokespecial #1; //Method java/lang/Object."<init>":()V
   4: new #2; //class java/lang/RuntimeException
   7: dup
   8: ldc #3; //String Stub!
   10:  invokespecial #4; //Method java/lang/RuntimeException."<init>":(Ljava/lang/String;)V
   13:  athrow
  LineNumberTable:
   line 5: 0

  LocalVariableTable:
   Start  Length  Slot  Name   Signature
   0      14      0    this       Ljava/io/InputStream;


public int available()   throws java.io.IOException;
  Signature: ()I
  Code:
   Stack=3, Locals=1, Args_size=1
   0: new #2; //class java/lang/RuntimeException
   3: dup
   4: ldc #3; //String Stub!
   6: invokespecial #4; //Method java/lang/RuntimeException."<init>":(Ljava/lang/String;)V
   9: athrow
  LineNumberTable:
   line 6: 0

  LocalVariableTable:
   Start  Length  Slot  Name   Signature
   0      10      0    this       Ljava/io/InputStream;

  Exceptions:
   throws java.io.IOException
public void close()   throws java.io.IOException;
  Signature: ()V
  Code:
   Stack=3, Locals=1, Args_size=1
   0: new #2; //class java/lang/RuntimeException
   3: dup
   4: ldc #3; //String Stub!
   6: invokespecial #4; //Method java/lang/RuntimeException."<init>":(Ljava/lang/String;)V
   9: athrow
  LineNumberTable:
   line 7: 0

  LocalVariableTable:
   Start  Length  Slot  Name   Signature
   0      10      0    this       Ljava/io/InputStream;

  Exceptions:
   throws java.io.IOException
public void mark(int);
  Signature: (I)V
  Code:
   Stack=3, Locals=2, Args_size=2
   0: new #2; //class java/lang/RuntimeException
   3: dup
   4: ldc #3; //String Stub!
   6: invokespecial #4; //Method java/lang/RuntimeException."<init>":(Ljava/lang/String;)V
   9: athrow
  LineNumberTable:
   line 8: 0

  LocalVariableTable:
   Start  Length  Slot  Name   Signature
   0      10      0    this       Ljava/io/InputStream;
   0      10      1    readlimit       I


public boolean markSupported();
  Signature: ()Z
  Code:
   Stack=3, Locals=1, Args_size=1
   0: new #2; //class java/lang/RuntimeException
   3: dup
   4: ldc #3; //String Stub!
   6: invokespecial #4; //Method java/lang/RuntimeException."<init>":(Ljava/lang/String;)V
   9: athrow
  LineNumberTable:
   line 9: 0

  LocalVariableTable:
   Start  Length  Slot  Name   Signature
   0      10      0    this       Ljava/io/InputStream;


public abstract int read()   throws java.io.IOException;
  Signature: ()I
  Exceptions:
   throws java.io.IOException
public int read(byte[])   throws java.io.IOException;
  Signature: ([B)I
  Code:
   Stack=3, Locals=2, Args_size=2
   0: new #2; //class java/lang/RuntimeException
   3: dup
   4: ldc #3; //String Stub!
   6: invokespecial #4; //Method java/lang/RuntimeException."<init>":(Ljava/lang/String;)V
   9: athrow
  LineNumberTable:
   line 11: 0

  LocalVariableTable:
   Start  Length  Slot  Name   Signature
   0      10      0    this       Ljava/io/InputStream;
   0      10      1    buffer       [B

  Exceptions:
   throws java.io.IOException
public int read(byte[], int, int)   throws java.io.IOException;
  Signature: ([BII)I
  Code:
   Stack=3, Locals=4, Args_size=4
   0: new #2; //class java/lang/RuntimeException
   3: dup
   4: ldc #3; //String Stub!
   6: invokespecial #4; //Method java/lang/RuntimeException."<init>":(Ljava/lang/String;)V
   9: athrow
  LineNumberTable:
   line 12: 0

  LocalVariableTable:
   Start  Length  Slot  Name   Signature
   0      10      0    this       Ljava/io/InputStream;
   0      10      1    buffer       [B
   0      10      2    byteOffset       I
   0      10      3    byteCount       I

  Exceptions:
   throws java.io.IOException
public synchronized void reset()   throws java.io.IOException;
  Signature: ()V
  Code:
   Stack=3, Locals=1, Args_size=1
   0: new #2; //class java/lang/RuntimeException
   3: dup
   4: ldc #3; //String Stub!
   6: invokespecial #4; //Method java/lang/RuntimeException."<init>":(Ljava/lang/String;)V
   9: athrow
  LineNumberTable:
   line 13: 0

  LocalVariableTable:
   Start  Length  Slot  Name   Signature
   0      10      0    this       Ljava/io/InputStream;

  Exceptions:
   throws java.io.IOException
public long skip(long)   throws java.io.IOException;
  Signature: (J)J
  Code:
   Stack=3, Locals=3, Args_size=2
   0: new #2; //class java/lang/RuntimeException
   3: dup
   4: ldc #3; //String Stub!
   6: invokespecial #4; //Method java/lang/RuntimeException."<init>":(Ljava/lang/String;)V
   9: athrow
  LineNumberTable:
   line 14: 0

  LocalVariableTable:
   Start  Length  Slot  Name   Signature
   0      10      0    this       Ljava/io/InputStream;
   0      10      1    byteCount       J

  Exceptions:
   throws java.io.IOException
}

