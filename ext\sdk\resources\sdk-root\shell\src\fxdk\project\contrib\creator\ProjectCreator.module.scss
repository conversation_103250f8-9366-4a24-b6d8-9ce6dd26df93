@import "variables";

.root {
  display: flex;
  flex-direction: column;

  width: 100%;

  .name-input {
    padding: 0 $q*4;
    margin-top: $q*4;

    .input {
      width: 100%;
    }
  }

  .checkbox {
    padding: $q*4;
    padding-bottom: $q;
    margin-top: $q*2;
  }

  .explorer-hint {
    padding: $q $q*4;
    margin-top: $q*4;

    @include fontPrimary;
    font-size: $fs08;
    color: rgba($fgColor, .5);
  }
}
