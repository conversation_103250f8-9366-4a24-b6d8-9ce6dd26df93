# Copyright (c) 2012 The Chromium Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//build/buildflag_header.gni")
import("//build/config/nacl/config.gni")
import("//ipc/features.gni")
import("//mojo/public/tools/bindings/mojom.gni")
import("//testing/test.gni")
import("//third_party/protobuf/proto_library.gni")
import("//tools/ipc_fuzzer/ipc_fuzzer.gni")

buildflag_header("ipc_buildflags") {
  header = "ipc_buildflags.h"

  flags = [ "IPC_MESSAGE_LOG_ENABLED=$enable_ipc_logging" ]
}

component("ipc") {
  sources = [
    # These are the param_traits sources needed by all platforms,
    # including ios. The rest are added in a conditional block below.
    "ipc_message_utils.cc",
    "ipc_message_utils.h",
    "ipc_mojo_param_traits.cc",
    "ipc_mojo_param_traits.h",
    "param_traits_log_macros.h",
    "param_traits_macros.h",
    "param_traits_read_macros.h",
    "param_traits_write_macros.h",
    "struct_constructor_macros.h",
  ]

  if (!is_ios) {
    sources += [
      # Most sources go here since ios only needs the param_traits
      # code.
      "ipc_channel.h",
      "ipc_channel_common.cc",
      "ipc_channel_factory.cc",
      "ipc_channel_factory.h",
      "ipc_channel_handle.h",
      "ipc_channel_mojo.cc",
      "ipc_channel_mojo.h",
      "ipc_channel_proxy.cc",
      "ipc_channel_proxy.h",
      "ipc_channel_reader.cc",
      "ipc_channel_reader.h",
      "ipc_listener.h",
      "ipc_logging.cc",
      "ipc_logging.h",
      "ipc_message_macros.h",
      "ipc_message_pipe_reader.cc",
      "ipc_message_pipe_reader.h",
      "ipc_message_start.h",
      "ipc_message_templates.h",
      "ipc_message_templates_impl.h",
      "ipc_mojo_bootstrap.cc",
      "ipc_mojo_bootstrap.h",
      "ipc_sender.h",
      "ipc_sync_channel.cc",
      "ipc_sync_channel.h",
      "ipc_sync_message_filter.cc",
      "ipc_sync_message_filter.h",
      "message_filter.cc",
      "message_filter.h",
      "message_filter_router.cc",
      "message_filter_router.h",
      "message_router.cc",
      "message_router.h",
    ]
  }

  if (is_nacl && !is_nacl_nonsfi) {
    sources += [
      "ipc_channel_nacl.cc",
      "ipc_channel_nacl.h",
    ]
  } else {
    sources += [ "ipc_channel.cc" ]
  }

  defines = [ "IS_IPC_IMPL" ]

  public_deps = [
    ":ipc_buildflags",
    ":message_support",
    ":mojom",
    ":native_handle_type_converters",
    ":param_traits",
    "//base/util/type_safety",
    "//mojo/public/cpp/base",
    "//mojo/public/cpp/bindings",
    "//mojo/public/cpp/system",
  ]

  if (!is_nacl_nonsfi) {
    sources += [ "ipc_message_protobuf_utils.h" ]

    public_deps += [ "//third_party/protobuf:protobuf_lite" ]
  }

  deps = [ "//base" ]

  if (enable_ipc_fuzzer) {
    public_configs = [ "//tools/ipc_fuzzer:ipc_fuzzer_config" ]
  }

  if (!is_nacl && !is_ios) {
    sources += [
      "trace_ipc_message.cc",
      "trace_ipc_message.h",
    ]
    public_deps += [ "//services/tracing/public/cpp" ]
  }
}

component("message_support") {
  sources = [
    "handle_attachment_win.cc",
    "handle_attachment_win.h",
    "handle_win.cc",
    "handle_win.h",
    "ipc_message.cc",
    "ipc_message.h",
    "ipc_message_attachment.cc",
    "ipc_message_attachment.h",
    "ipc_message_attachment_set.cc",
    "ipc_message_attachment_set.h",
    "ipc_message_support_export.h",
    "ipc_mojo_handle_attachment.cc",
    "ipc_mojo_handle_attachment.h",
    "ipc_mojo_message_helper.cc",
    "ipc_mojo_message_helper.h",
    "ipc_platform_file.cc",
    "ipc_platform_file.h",
    "ipc_sync_message.cc",
    "ipc_sync_message.h",
    "mach_port_attachment_mac.cc",
    "mach_port_attachment_mac.h",
    "mach_port_mac.cc",
    "mach_port_mac.h",
  ]

  if (is_posix || is_fuchsia) {
    sources += [
      "ipc_platform_file_attachment_posix.cc",
      "ipc_platform_file_attachment_posix.h",
    ]
  }

  if (is_fuchsia) {
    sources += [
      "handle_attachment_fuchsia.cc",
      "handle_attachment_fuchsia.h",
    ]
  }

  defines = [ "IPC_MESSAGE_SUPPORT_IMPL" ]

  public_deps = [
    ":ipc_buildflags",
    ":param_traits",
    "//base",
    "//mojo/public/cpp/system",
  ]

  if (is_win || is_mac) {
    # On Windows HandleAttachmentWin needs to generate random IDs.
    # On Mac MachPortAttachmentMac needs to generate random IDs.
    deps = [ "//crypto" ]
  }
}

source_set("native_handle_type_converters") {
  sources = [
    "native_handle_type_converters.cc",
    "native_handle_type_converters.h",
  ]
  public_deps = [
    ":message_support",
    "//mojo/public/interfaces/bindings:bindings_headers",
  ]
}

mojom_component("mojom") {
  output_prefix = "ipc_mojom"
  macro_prefix = "IPC_MOJOM"
  sources = [ "ipc.mojom" ]
  public_deps = [
    "//mojo/public/interfaces/bindings",
    "//mojo/public/mojom/base",
  ]

  # Don't generate a variant sources since we depend on generated internal
  # bindings types and we don't generate or build variants of those.
  disable_variants = true
}

mojom("mojom_constants") {
  sources = [ "constants.mojom" ]
}

mojom("test_interfaces") {
  testonly = true
  sources = [ "ipc_test.mojom" ]
  support_lazy_serialization = true
}

# This is provided as a separate target so other targets can provide param
# traits implementations without necessarily linking to all of IPC.
source_set("param_traits") {
  public = [ "ipc_param_traits.h" ]
}

if (!is_ios) {
  source_set("run_all_unittests") {
    testonly = true

    sources = [ "run_all_unittests.cc" ]

    deps = [
      "//base",
      "//base/test:test_support",
      "//mojo/core/embedder",
      "//mojo/core/test:test_support",
    ]
  }

  proto_library("test_proto") {
    sources = [ "test_proto.proto" ]
  }

  test("ipc_tests") {
    sources = [
      "ipc_channel_mojo_unittest.cc",
      "ipc_channel_proxy_unittest.cc",
      "ipc_channel_reader_unittest.cc",
      "ipc_fuzzing_tests.cc",
      "ipc_message_unittest.cc",
      "ipc_message_utils_unittest.cc",
      "ipc_mojo_bootstrap_unittest.cc",
      "ipc_sync_channel_unittest.cc",
      "ipc_sync_message_unittest.cc",
      "ipc_sync_message_unittest.h",
      "sync_socket_unittest.cc",
    ]

    if (is_posix || is_fuchsia) {
      sources += [ "ipc_message_attachment_set_posix_unittest.cc" ]
    }

    if (!is_ios) {
      sources += [ "ipc_send_fds_test.cc" ]
    }

    deps = [
      ":ipc",
      ":run_all_unittests",
      ":test_interfaces",
      ":test_proto",
      ":test_support",
      "//base",
      "//base:i18n",
      "//base/test:test_support",
      "//crypto",
      "//mojo/core/embedder",
      "//mojo/core/test:test_support",
      "//testing/gtest",
    ]

    if (is_mac) {
      deps += [ "//sandbox/mac:seatbelt" ]
    }

    if (is_fuchsia) {
      sources -= [
        # No AF_UNIX domain sockets on Fuchsia.
        "sync_socket_unittest.cc",
      ]
    }

    if (!is_nacl_nonsfi) {
      sources += [ "ipc_message_protobuf_utils_unittest.cc" ]
    }
  }

  test("ipc_perftests") {
    sources = [
      "ipc_cpu_perftest.cc",
      "ipc_mojo_perftest.cc",
      "ipc_perftest_messages.cc",
      "ipc_perftest_messages.h",
      "ipc_perftest_util.cc",
      "ipc_perftest_util.h",
      "run_all_perftests.cc",
    ]

    deps = [
      ":ipc",
      ":test_interfaces",
      ":test_support",
      "//base",
      "//base:i18n",
      "//base/test:test_support",
      "//mojo/core/embedder",
      "//mojo/core/test:test_support",
      "//mojo/core/test:test_support_impl",
      "//testing/gtest",
    ]
  }

  static_library("test_support") {
    testonly = true
    sources = [
      "ipc_security_test_util.cc",
      "ipc_security_test_util.h",
      "ipc_test_base.cc",
      "ipc_test_base.h",
      "ipc_test_channel_listener.cc",
      "ipc_test_channel_listener.h",
      "ipc_test_sink.cc",
      "ipc_test_sink.h",
    ]
    public_deps = [ ":ipc" ]
    deps = [
      "//base",
      "//base/test:test_support",
      "//mojo/core/test:test_support",
      "//testing/gtest",
    ]
  }
}
