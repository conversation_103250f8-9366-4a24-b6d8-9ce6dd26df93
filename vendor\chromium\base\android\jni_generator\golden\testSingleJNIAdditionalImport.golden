// Copyright 2014 The Chromium Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     base/android/jni_generator/jni_generator.py
// For
//     org/chromium/foo/Foo

#ifndef org_chromium_foo_Foo_JNI
#define org_chromium_foo_Foo_JNI

#include <jni.h>

#include "base/android/jni_generator/jni_generator_helper.h"


// Step 1: Forward declarations.

JNI_REGISTRATION_EXPORT extern const char kClassPath_org_chromium_foo_Foo[];
const char kClassPath_org_chromium_foo_Foo[] = "org/chromium/foo/Foo";
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_REGISTRATION_EXPORT std::atomic<jclass> g_org_chromium_foo_Foo_clazz(nullptr);
#ifndef org_chromium_foo_Foo_clazz_defined
#define org_chromium_foo_Foo_clazz_defined
inline jclass org_chromium_foo_Foo_clazz(JNIEnv* env) {
  return base::android::LazyGetClass(env, kClassPath_org_chromium_foo_Foo,
      &g_org_chromium_foo_Foo_clazz);
}
#endif


// Step 2: Constants (optional).


// Step 3: Method stubs.
static void JNI_Foo_DoSomething(JNIEnv* env, const base::android::JavaParamRef<jobject>& callback);

JNI_GENERATOR_EXPORT void Java_org_chromium_foo_Foo_nativeDoSomething(
    JNIEnv* env,
    jclass jcaller,
    jobject callback) {
  return JNI_Foo_DoSomething(env, base::android::JavaParamRef<jobject>(env, callback));
}


static std::atomic<jmethodID> g_org_chromium_foo_Foo_calledByNative(nullptr);
static void Java_Foo_calledByNative(JNIEnv* env, const base::android::JavaRef<jobject>& callback) {
  jclass clazz = org_chromium_foo_Foo_clazz(env);
  CHECK_CLAZZ(env, clazz,
      org_chromium_foo_Foo_clazz(env));

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "calledByNative",
          "(Lorg/chromium/foo/Bar$Callback;)V",
          &g_org_chromium_foo_Foo_calledByNative);

     env->CallStaticVoidMethod(clazz,
          call_context.base.method_id, callback.obj());
}

// Step 4: Generated test functions (optional).


#endif  // org_chromium_foo_Foo_JNI
