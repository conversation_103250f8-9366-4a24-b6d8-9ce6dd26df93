<?xml version="1.0"?>
<!DOCTYPE fontconfig SYSTEM "fonts.dtd">
<fontconfig>

  <!-- Cache location. -->
  <cachedir>/fontconfig_caches</cachedir>

  <!-- GCS-synced fonts. -->
  <dir>/test_fonts</dir>

  <!-- Default properties. -->
  <match target="font">
    <edit name="embeddedbitmap" mode="append_last">
      <bool>false</bool>
    </edit>
  </match>

  <match target="pattern">
    <test qual="any" name="family">
      <string>Times</string>
    </test>
    <edit name="family" mode="assign">
      <string>Tinos</string>
    </edit>
  </match>

  <match target="pattern">
    <test qual="any" name="family">
      <string>sans</string>
    </test>
    <edit name="family" mode="assign">
      <string>DejaVu Sans</string>
    </edit>
  </match>

  <match target="pattern">
    <test qual="any" name="family">
      <string>sans serif</string>
    </test>
    <edit name="family" mode="assign">
      <string>Arimo</string>
    </edit>
  </match>

  <!-- Some layout tests specify Helvetica as a family and we need to make sure
       that we don't fallback to Tinos for them -->
  <match target="pattern">
    <test qual="any" name="family">
      <string>Helvetica</string>
    </test>
    <edit name="family" mode="assign">
      <string>Arimo</string>
    </edit>
  </match>

  <match target="pattern">
    <test qual="any" name="family">
      <string>sans-serif</string>
    </test>
    <edit name="family" mode="assign">
      <string>Arimo</string>
    </edit>
  </match>

  <match target="pattern">
    <test qual="any" name="family">
      <string>serif</string>
    </test>
    <edit name="family" mode="assign">
      <string>Tinos</string>
    </edit>
  </match>

  <match target="pattern">
    <test qual="any" name="family">
      <string>mono</string>
    </test>
    <edit name="family" mode="assign">
      <string>Cousine</string>
    </edit>
  </match>

  <match target="pattern">
    <test qual="any" name="family">
      <string>monospace</string>
    </test>
    <edit name="family" mode="assign">
      <string>Cousine</string>
    </edit>
  </match>

  <match target="pattern">
    <test qual="any" name="family">
      <string>Courier</string>
    </test>
    <edit name="family" mode="assign">
      <string>Cousine</string>
    </edit>
  </match>

  <match target="pattern">
    <test qual="any" name="family">
      <string>cursive</string>
    </test>
    <edit name="family" mode="assign">
      <string>Comic Sans MS</string>
    </edit>
  </match>

  <match target="pattern">
    <test qual="any" name="family">
      <string>fantasy</string>
    </test>
    <edit name="family" mode="assign">
      <string>Impact</string>
    </edit>
  </match>

  <match target="pattern">
    <test qual="any" name="family">
      <string>Monaco</string>
    </test>
    <edit name="family" mode="assign">
      <string>Tinos</string>
    </edit>
  </match>

  <match target="pattern">
    <test qual="any" name="family">
      <string>Arial</string>
    </test>
    <edit name="family" mode="assign">
      <string>Arimo</string>
    </edit>
  </match>

  <match target="pattern">
    <test qual="any" name="family">
      <string>Courier New</string>
    </test>
    <edit name="family" mode="assign">
      <string>Cousine</string>
    </edit>
  </match>

  <match target="pattern">
    <test qual="any" name="family">
      <string>Georgia</string>
    </test>
    <edit name="family" mode="assign">
      <string>Gelasio</string>
    </edit>
  </match>

  <match target="pattern">
    <test qual="any" name="family">
      <string>Times New Roman</string>
    </test>
    <edit name="family" mode="assign">
      <string>Tinos</string>
    </edit>
  </match>

  <match target="pattern">
    <test qual="any" name="family">
      <string>Verdana</string>
    </test>
    <!-- NOT metrically compatible! -->
    <edit name="family" mode="assign">
      <string>Arimo</string>
    </edit>
  </match>

  <!-- TODO(thomasanderson): Move these configs to be test-specific. -->
  <match target="pattern">
    <test name="family" compare="eq">
      <string>NonAntiAliasedSans</string>
    </test>
    <edit name="family" mode="assign">
      <string>Arimo</string>
    </edit>
    <edit name="antialias" mode="assign">
      <bool>false</bool>
    </edit>
  </match>

  <match target="pattern">
    <test name="family" compare="eq">
      <string>SlightHintedGeorgia</string>
    </test>
    <edit name="family" mode="assign">
      <string>Gelasio</string>
    </edit>
    <edit name="hintstyle" mode="assign">
      <const>hintslight</const>
    </edit>
  </match>

  <match target="pattern">
    <test name="family" compare="eq">
      <string>NonHintedSans</string>
    </test>
    <edit name="family" mode="assign">
      <string>Arimo</string>
    </edit>
    <!-- These deliberately contradict each other. The 'hinting' preference
         should take priority -->
    <edit name="hintstyle" mode="assign">
      <const>hintfull</const>
    </edit>
   <edit name="hinting" mode="assign">
      <bool>false</bool>
    </edit>
  </match>

  <match target="pattern">
    <test name="family" compare="eq">
      <string>AutohintedSerif</string>
    </test>
    <edit name="family" mode="assign">
      <string>Arimo</string>
    </edit>
    <edit name="autohint" mode="assign">
      <bool>true</bool>
    </edit>
    <edit name="hintstyle" mode="assign">
      <const>hintmedium</const>
    </edit>
  </match>

  <match target="pattern">
    <test name="family" compare="eq">
      <string>HintedSerif</string>
    </test>
    <edit name="family" mode="assign">
      <string>Arimo</string>
    </edit>
    <edit name="autohint" mode="assign">
      <bool>false</bool>
    </edit>
    <edit name="hintstyle" mode="assign">
      <const>hintmedium</const>
    </edit>
  </match>

  <match target="pattern">
    <test name="family" compare="eq">
      <string>FullAndAutoHintedSerif</string>
    </test>
    <edit name="family" mode="assign">
      <string>Arimo</string>
    </edit>
    <edit name="autohint" mode="assign">
      <bool>true</bool>
    </edit>
    <edit name="hintstyle" mode="assign">
      <const>hintfull</const>
    </edit>
  </match>

  <match target="pattern">
    <test name="family" compare="eq">
      <string>SubpixelEnabledArial</string>
    </test>
    <edit name="family" mode="assign">
      <string>Arimo</string>
    </edit>
    <edit name="rgba" mode="assign">
      <const>rgb</const>
    </edit>
  </match>

  <match target="pattern">
    <test name="family" compare="eq">
      <string>SubpixelDisabledArial</string>
    </test>
    <edit name="family" mode="assign">
      <string>Arimo</string>
    </edit>
    <edit name="rgba" mode="assign">
      <const>none</const>
    </edit>
  </match>

  <match target="pattern">
    <!-- FontConfig doesn't currently provide a well-defined way to turn on
         subpixel positioning.  This is just an arbitrary pattern to use after
         turning subpixel positioning on globally to ensure that we don't have
         issues with our style getting cached for other tests. -->
    <test name="family" compare="eq">
      <string>SubpixelPositioning</string>
    </test>
    <edit name="family" mode="assign">
      <string>Tinos</string>
    </edit>
  </match>

  <match target="pattern">
    <!-- See comments above -->
    <test name="family" compare="eq">
      <string>SubpixelPositioningAhem</string>
    </test>
    <edit name="family" mode="assign">
      <string>ahem</string>
    </edit>
  </match>

  <match target="pattern">
    <test name="family" compare="eq">
      <string>SlightHintedTimesNewRoman</string>
    </test>
    <edit name="family" mode="assign">
      <string>Tinos</string>
    </edit>
    <edit name="hintstyle" mode="assign">
      <const>hintslight</const>
    </edit>
  </match>

  <!-- When we encounter a character that the current font doesn't
       support, gfx::GetFallbackFontForChar() returns the first font
       that does have a glyph for the character. The list of fonts is
       sorted by a pattern that includes the current locale, but doesn't
       include a font family (which means that the fallback font depends
       on the locale but not on the current font).

       DejaVu Sans is commonly the only font that supports some
       characters, such as "⇧", and even when other candidates are
       available, DejaVu Sans is commonly first among them, because of
       the way Fontconfig is ordinarily configured. For example, the
       configuration in the Fonconfig source lists DejaVu Sans under the
       sans-serif generic family, and appends sans-serif to patterns
       that don't already include a generic family (such as the pattern
       in gfx::GetFallbackFontForChar()).

       To get the same fallback font in the layout tests, we could
       duplicate this configuration here, or more directly, simply
       append DejaVu Sans to all patterns. -->
  <match target="pattern">
    <edit name="family" mode="append_last">
      <string>DejaVu Sans</string>
    </edit>
  </match>

</fontconfig>
