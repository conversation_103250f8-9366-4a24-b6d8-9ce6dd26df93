# WATCH OUT!  This makefile is a work in progress.  It is probably missing
# tons of important things.  DO NOT RELY ON IT TO BUILD A GOOD LIBEVENT.

# Needed for correctness
CFLAGS=/Iinclude /Icompat /IWIN32-Code /DWIN32 /DHAVE_CONFIG_H /I.

# For optimization and warnings
CFLAGS=$(CFLAGS) /Ox /W3 /wd4996 /nologo

# XXXX have a debug mode

LIBFLAGS=/nologo


CORE_OBJS=event.obj buffer.obj evbuffer.obj \
	log.obj evutil.obj \
	strlcpy.obj signal.obj win32.obj
EXTRA_OBJS=event_tagging.obj http.obj evdns.obj evrpc.obj

ALL_OBJS=$(CORE_OBJS) $(WIN_OBJS) $(EXTRA_OBJS)
STATIC_LIBS=libevent_core.lib libevent_extras.lib libevent.lib


all: static_libs tests

static_libs: $(STATIC_LIBS)

win32.obj: WIN32-Code\win32.c
	$(CC) $(CFLAGS) /c WIN32-Code\win32.c

libevent_core.lib: $(CORE_OBJS)
	lib $(LIBFLAGS) $(CORE_OBJS) /out:libevent_core.lib 

libevent_extras.lib: $(EXTRA_OBJS)
	lib $(LIBFLAGS) $(EXTRA_OBJS) /out:libevent_extras.lib

libevent.lib: $(CORE_OBJ) $(EXTRA_OBJS)
	lib $(LIBFLAGS) $(CORE_OBJS) $(EXTRA_OBJS) /out:libevent.lib

clean:
	del $(ALL_OBJS)
	del $(STATIC_LIBS)
	cd test
	$(MAKE) /F Makefile.nmake clean

tests:
	cd test
	$(MAKE) /F Makefile.nmake
