// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.201201.7

#ifndef WINRT_Microsoft_Graphics_Canvas_Effects_2_H
#define WINRT_Microsoft_Graphics_Canvas_Effects_2_H
#include "winrt/impl/Microsoft.Graphics.Canvas.Effects.1.h"
WINRT_EXPORT namespace winrt::Microsoft::Graphics::Canvas::Effects
{
    struct Matrix5x4
    {
        float M11;
        float M12;
        float M13;
        float M14;
        float M21;
        float M22;
        float M23;
        float M24;
        float M31;
        float M32;
        float M33;
        float M34;
        float M41;
        float M42;
        float M43;
        float M44;
        float M51;
        float M52;
        float M53;
        float M54;
    };
    inline bool operator==(Matrix5x4 const& left, Matrix5x4 const& right) noexcept
    {
        return left.M11 == right.M11 && left.M12 == right.M12 && left.M13 == right.M13 && left.M14 == right.M14 && left.M21 == right.M21 && left.M22 == right.M22 && left.M23 == right.M23 && left.M24 == right.M24 && left.M31 == right.M31 && left.M32 == right.M32 && left.M33 == right.M33 && left.M34 == right.M34 && left.M41 == right.M41 && left.M42 == right.M42 && left.M43 == right.M43 && left.M44 == right.M44 && left.M51 == right.M51 && left.M52 == right.M52 && left.M53 == right.M53 && left.M54 == right.M54;
    }
    inline bool operator!=(Matrix5x4 const& left, Matrix5x4 const& right) noexcept
    {
        return !(left == right);
    }
}
#endif
