import {
  Indicator,
  Box,
  Flex,
  Pad,
  Modal,
} from '@cfx-dev/ui-components';
import { observer } from 'mobx-react-lite';
import React from 'react';

import { $L } from 'cfx/common/services/intl/l10n';

import { AdaptiveCardPresenter } from './AdaptiveCardPresenter/AdaptiveCardPresenter';
import { BuildSwitchInfo } from './BuildSwitchInfo';
import { BuildSwitchRequest } from './BuildSwitchRequest';
import { ConnectFailed } from './ConnectFailed';
import { ConnectStatus } from './ConnectStatus';
import { ServerHeader } from './ServerHeader';
import { useMpMenuServersConnectService } from '../../services/servers/serversConnect.mpMenu';

export const LegacyConnectingModal = observer(function LegacyConnectingModal() {
  const service = useMpMenuServersConnectService();

  if (!service.showModal) {
    return null;
  }

  let node: React.ReactNode;

  if (service.resolvingServer) {
    node = (
      <ResolvingServer />
    );
  } else if (service.state) {
    switch (service.state.type) {
      case 'connecting': {
        node = (
          <ResolvingServer />
        );
        break;
      }

      case 'status': {
        node = (
          <ConnectStatus state={service.state} onCancel={service.cancel} />
        );
        break;
      }

      case 'failed': {
        node = (
          <ConnectFailed state={service.state} server={service.server!} onClose={service.cancel} />
        );
        break;
      }

      case 'card': {
        node = (
          <AdaptiveCardPresenter card={service.state.card} onCancel={service.cancel} />
        );
        break;
      }

      case 'buildSwitchRequest': {
        node = (
          <BuildSwitchRequest state={service.state} onCancel={service.cancel} />
        );
        break;
      }
      case 'buildSwitchInfo': {
        node = (
          <BuildSwitchInfo state={service.state} onCancel={service.cancel} />
        );
        break;
      }
    }
  }

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      background: 'rgba(0, 0, 0, 0.85)',
      backdropFilter: 'blur(10px)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000,
      animation: 'fadeIn 0.3s ease-out'
    }}>
      <div style={{
        maxWidth: '500px',
        width: '90%',
        position: 'relative'
      }}>
        {!!service.server && service.showServer && (
          <div style={{ marginBottom: '1rem' }}>
            <ServerHeader server={service.server} />
          </div>
        )}

        {node}
      </div>

      {/* Add fadeIn animation */}
      <style>{`
        @keyframes fadeIn {
          from { opacity: 0; }
          to { opacity: 1; }
        }
      `}</style>
    </div>
  );
});

function ResolvingServer() {
  return (
    <div style={{
      padding: '3rem',
      background: 'linear-gradient(145deg, rgba(15, 15, 25, 0.95) 0%, rgba(25, 25, 35, 0.9) 100%)',
      backdropFilter: 'blur(25px)',
      borderRadius: '20px',
      border: '2px solid rgba(255, 140, 66, 0.3)',
      boxShadow: '0 30px 60px -12px rgba(0, 0, 0, 0.7)',
      textAlign: 'center',
      position: 'relative',
      overflow: 'hidden'
    }}>
      {/* Background accent */}
      <div style={{
        position: 'absolute',
        top: '10px',
        right: '20px',
        width: '60px',
        height: '3px',
        background: 'linear-gradient(90deg, #FF8C42, #7CB342)',
        borderRadius: '2px',
        opacity: 0.8
      }} />

      {/* Gang Hai City Logo */}
      <div style={{
        fontSize: '1.8rem',
        fontWeight: '700',
        color: '#FF8C42',
        marginBottom: '1.5rem',
        textShadow: '0 2px 8px rgba(255, 140, 66, 0.3)'
      }}>
        🏙️ Gang Hai City
      </div>

      {/* Enhanced Loading Spinner */}
      <div style={{
        width: '80px',
        height: '80px',
        border: '4px solid rgba(255, 140, 66, 0.2)',
        borderTop: '4px solid #FF8C42',
        borderRadius: '50%',
        animation: 'spin 1s linear infinite',
        margin: '0 auto 2rem',
        boxShadow: '0 0 20px rgba(255, 140, 66, 0.3)'
      }} />

      {/* Connection Status */}
      <div style={{
        fontSize: '1.2rem',
        color: 'rgba(255, 255, 255, 0.9)',
        marginBottom: '1rem',
        fontWeight: '600'
      }}>
        Đang kết nối tới máy chủ...
      </div>

      {/* Loading Dots */}
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        gap: '8px',
        marginBottom: '1rem'
      }}>
        {[1, 2, 3].map(i => (
          <div
            key={i}
            style={{
              width: '8px',
              height: '8px',
              background: '#FF8C42',
              borderRadius: '50%',
              animation: `loadingDots 1.5s infinite ${(i - 1) * 0.2}s`
            }}
          />
        ))}
      </div>

      {/* Progress Bar */}
      <div style={{
        width: '100%',
        height: '6px',
        background: 'rgba(255, 255, 255, 0.1)',
        borderRadius: '3px',
        overflow: 'hidden',
        border: '1px solid rgba(255, 140, 66, 0.2)',
        position: 'relative'
      }}>
        <div style={{
          height: '100%',
          background: 'linear-gradient(90deg, #FF8C42 0%, #7CB342 100%)',
          borderRadius: '3px',
          width: '100%',
          animation: 'progressSlide 2s ease-in-out infinite',
          boxShadow: '0 0 10px rgba(255, 140, 66, 0.5)'
        }} />
      </div>

      {/* Add CSS animations */}
      <style>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        @keyframes loadingDots {
          0%, 20% { opacity: 0.3; transform: scale(0.8); }
          50% { opacity: 1; transform: scale(1.2); }
          80%, 100% { opacity: 0.3; transform: scale(0.8); }
        }

        @keyframes progressSlide {
          0% { transform: translateX(-100%); }
          50% { transform: translateX(0%); }
          100% { transform: translateX(100%); }
        }
      `}</style>
    </div>
  );
}
