# Copyright 2020-2021 <PERSON>
# Copyright 2021 <PERSON><PERSON>
# Copyright 2021 <PERSON>
# Copyright 2022 <PERSON> III
#
# Distributed under the Boost Software License, Version 1.0.
# https://www.boost.org/LICENSE_1_0.txt

---
name: CI

on:
  pull_request:
  push:
    branches:
      - master
      - develop
      - bugfix/**
      - feature/**
      - fix/**
      - pr/**

concurrency:
  group: ${{format('{0}:{1}', github.repository, github.ref)}}
  cancel-in-progress: true

env:
  GIT_FETCH_JOBS: 8
  NET_RETRY_COUNT: 5
  B2_CI_VERSION: 1
  B2_VARIANT: debug,release
  B2_LINK: shared,static
  LCOV_BRANCH_COVERAGE: 0
  CODECOV_NAME: Github Actions
  CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}

jobs:
  CheckFormatting:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: DoozyX/clang-format-lint-action@v0.14
        with:
          exclude: './doc'
          clangFormatVersion: 14
      - name: Check line endings
        run: |
          if grep -r -l $'\r' --exclude-dir="\.git" --exclude-dir="doc" --exclude="*.mo"; then
              echo "Found files windows style line endings (CRLF). Please fix!"
              exit 1
          fi
      - name: Check for tabs
        run: |
          if grep -r -l $'\t' --exclude-dir="\.git" --exclude-dir="doc" --exclude="*.mo"; then
              echo "Found files with TABs. Please fix!"
              exit 1
          fi

  Create_Boost_Documentation:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Fetch Boost.CI
        uses: actions/checkout@v4
        with:
          repository: boostorg/boost-ci
          ref: master
          path: boost-ci-cloned
      - name: Get CI scripts folder
        run: cp -r boost-ci-cloned/ci . && rm -rf boost-ci-cloned
      - name: Setup Boost
        run: source ci/github/install.sh
      - name: Create documentation
        run: |
            sudo apt-get install -y doxygen
            B2_TARGETS=libs/$SELF/doc ci/build.sh

  posix:
    defaults:
      run:
        shell: bash

    strategy:
      fail-fast: false
      matrix:
        include:
          # Linux, gcc
          # GCC 5 is the first to have enough C++11 support, so don't test anything older except for one to verify the checks
          - { compiler: gcc-4.9,   cxxstd: '03,11',       os: ubuntu-20.04, container: 'ubuntu:16.04' }
          - { compiler: gcc-5,     cxxstd: '03,11,14,1z', os: ubuntu-22.04, container: 'ubuntu:18.04' }
          - { compiler: gcc-6,     cxxstd: '11,14,17',    os: ubuntu-22.04, container: 'ubuntu:18.04' }
          - { compiler: gcc-7,     cxxstd: '11,14,17',    os: ubuntu-22.04, container: 'ubuntu:18.04' }
          - { compiler: gcc-8,     cxxstd: '11,14,17,2a', os: ubuntu-22.04, container: 'ubuntu:18.04' }
          - { compiler: gcc-9,     cxxstd: '11,14,17,2a', os: ubuntu-22.04, container: 'ubuntu:18.04' }
          - { compiler: gcc-10,    cxxstd: '11,14,17,20', os: ubuntu-20.04 }
          - { compiler: gcc-11,    cxxstd: '11,14,17,20', os: ubuntu-20.04 }
          - { name: GCC w/ sanitizers, sanitize: yes,
              compiler: gcc-12,    cxxstd: '11,14,17,20', os: ubuntu-22.04 }
          - { name: Collect coverage, coverage: yes,
              compiler: gcc-8,     cxxstd: '11,2a',       os: ubuntu-20.04, install: 'g++-8-multilib', address-model: '32,64' }

          # Linux, clang
          - { compiler: clang-3.5, cxxstd: '03,11',       os: ubuntu-20.04, container: 'ubuntu:16.04' }
          - { compiler: clang-3.6, cxxstd: '11,14',       os: ubuntu-20.04, container: 'ubuntu:16.04' }
          - { compiler: clang-3.7, cxxstd: '11,14',       os: ubuntu-20.04, container: 'ubuntu:16.04' }
          - { compiler: clang-3.8, cxxstd: '11,14',       os: ubuntu-20.04, container: 'ubuntu:16.04' }
          - { compiler: clang-3.9, cxxstd: '11,14',       os: ubuntu-22.04, container: 'ubuntu:18.04' }
          - { compiler: clang-4.0, cxxstd: '11,14',       os: ubuntu-22.04, container: 'ubuntu:18.04' }
          - { compiler: clang-5.0, cxxstd: '11,14,1z',    os: ubuntu-22.04, container: 'ubuntu:18.04' }
          - { compiler: clang-6.0, cxxstd: '11,14,17',    os: ubuntu-22.04, container: 'ubuntu:18.04' }
          - { compiler: clang-7,   cxxstd: '11,14,17',    os: ubuntu-22.04, container: 'ubuntu:18.04' }
          # Note: clang-8 does not fully support C++20, so it is not compatible with some libstdc++ versions in this mode
          - { compiler: clang-8,   cxxstd: '11,14,17,2a', os: ubuntu-22.04, container: 'ubuntu:18.04', install: 'clang-8 g++-7', gcc_toolchain: 7 }
          - { compiler: clang-9,   cxxstd: '11,14,17,2a', os: ubuntu-20.04 }
          - { compiler: clang-10,  cxxstd: '11,14,17,20', os: ubuntu-20.04 }
          - { compiler: clang-11,  cxxstd: '11,14,17,20', os: ubuntu-20.04 }
          - { compiler: clang-12,  cxxstd: '11,14,17,20', os: ubuntu-20.04 }
          # Clang isn't compatible with libstdc++-13, so use the slightly older one
          - { compiler: clang-13,  cxxstd: '11,14,17,20', os: ubuntu-22.04, install: 'clang-13 g++-12', gcc_toolchain: 12 }
          - { compiler: clang-14,  cxxstd: '11,14,17,20', os: ubuntu-22.04, install: 'clang-14 g++-12', gcc_toolchain: 12 }
          - { compiler: clang-15,  cxxstd: '11,14,17,20', os: ubuntu-22.04, install: 'clang-15 g++-12', gcc_toolchain: 12 }

          # libc++
          - { compiler: clang-6.0, cxxstd: '11,14',       os: ubuntu-22.04, container: 'ubuntu:18.04', stdlib: libc++, install: 'clang-6.0 libc++-dev libc++abi-dev' }
          - { name: Clang w/ sanitizers, sanitize: yes,
              compiler: clang-12,  cxxstd: '11,14,17,20', os: ubuntu-20.04, stdlib: libc++, install: 'clang-12 libc++-12-dev libc++abi-12-dev' }

          # OSX, clang
          - { name: MacOS w/ clang and sanitizers,
              compiler: clang,     cxxstd: '11,14,17,2a', os: macos-11, ubsan: yes }

    timeout-minutes: 120
    runs-on: ${{matrix.os}}
    container: ${{matrix.container}}
    env: {B2_USE_CCACHE: 1}

    steps:
      - name: Setup environment
        run: |
            if [ -f "/etc/debian_version" ]; then
                echo "DEBIAN_FRONTEND=noninteractive" >> $GITHUB_ENV
                export DEBIAN_FRONTEND=noninteractive
            fi
            if [ -n "${{matrix.container}}" ] && [ -f "/etc/debian_version" ]; then
                apt-get -o Acquire::Retries=$NET_RETRY_COUNT update
                apt-get -o Acquire::Retries=$NET_RETRY_COUNT install -y sudo software-properties-common curl
                # Need (newer) git, and the older Ubuntu container may require requesting the key manually using port 80
                curl -sSL --retry ${NET_RETRY_COUNT:-5} 'http://keyserver.ubuntu.com/pks/lookup?op=get&search=0xE1DD270288B4E6030699E45FA1715D88E1DF1F24' | sudo gpg --dearmor > /etc/apt/trusted.gpg.d/git-core_ubuntu_ppa.gpg
                for i in {1..${NET_RETRY_COUNT:-3}}; do sudo -E add-apt-repository -y ppa:git-core/ppa && break || sleep 10; done
                apt-get -o Acquire::Retries=$NET_RETRY_COUNT update
                apt-get -o Acquire::Retries=$NET_RETRY_COUNT install -y g++ python libpython-dev git locales
            fi
            # For jobs not compatible with ccache, use "ccache: no" in the matrix
            if [[ "${{ matrix.ccache }}" == "no" ]]; then
                echo "B2_USE_CCACHE=0" >> $GITHUB_ENV
            fi
            git config --global pack.threads 0

      - uses: actions/checkout@v3
        with:
          # For coverage builds fetch the whole history, else only 1 commit using a 'fake ternary'
          fetch-depth: ${{ matrix.coverage && '0' || '1' }}

      - name: Cache ccache
        uses: actions/cache@v3
        if: env.B2_USE_CCACHE
        with:
          path: ~/.ccache
          key: ${{matrix.os}}-${{matrix.container}}-${{matrix.compiler}}-${{github.sha}}
          restore-keys: ${{matrix.os}}-${{matrix.container}}-${{matrix.compiler}}-

      - name: Fetch Boost.CI
        uses: actions/checkout@v3
        with:
          repository: boostorg/boost-ci
          ref: master
          path: boost-ci-cloned

      - name: Get CI scripts folder
        run: |
            # Copy ci folder if not testing Boost.CI
            [[ "$GITHUB_REPOSITORY" =~ "boost-ci" ]] || cp -r boost-ci-cloned/ci .
            rm -rf boost-ci-cloned

      - name: Install packages
        if: startsWith(matrix.os, 'ubuntu')
        run: |
            SOURCE_KEYS=(${{join(matrix.source_keys, ' ')}})
            SOURCES=(${{join(matrix.sources, ' ')}})
            # Add this by default
            SOURCES+=(ppa:ubuntu-toolchain-r/test)
            for key in "${SOURCE_KEYS[@]}"; do
                for i in {1..$NET_RETRY_COUNT}; do
                    keyfilename=$(basename -s .key $key)
                    curl -sSL --retry ${NET_RETRY_COUNT:-5} "$key" | sudo gpg --dearmor > /etc/apt/trusted.gpg.d/${keyfilename} && break || sleep 10
                done
            done
            # Initial update before adding sources required to get e.g. keys
            sudo apt-get -o Acquire::Retries=$NET_RETRY_COUNT update
            for source in "${SOURCES[@]}"; do
                for i in {1..$NET_RETRY_COUNT}; do
                    sudo add-apt-repository $source && break || sleep 10
                done
            done
            sudo apt-get -o Acquire::Retries=$NET_RETRY_COUNT update
            if [[ -z "${{matrix.install}}" ]]; then
                pkgs="${{matrix.compiler}}"
                pkgs="${pkgs/gcc-/g++-}"
            else
                pkgs="${{matrix.install}}"
            fi
            sudo apt-get -o Acquire::Retries=$NET_RETRY_COUNT install -y $pkgs libicu-dev

      - name: Setup GCC Toolchain
        if: matrix.gcc_toolchain
        run: |
            GCC_TOOLCHAIN_ROOT="$HOME/gcc-toolchain"
            echo "GCC_TOOLCHAIN_ROOT=$GCC_TOOLCHAIN_ROOT" >> $GITHUB_ENV
            if ! command -v dpkg-architecture; then
                apt-get install -y dpkg-dev
            fi
            MULTIARCH_TRIPLET="$(dpkg-architecture -qDEB_HOST_MULTIARCH)"
            mkdir -p "$GCC_TOOLCHAIN_ROOT"
            ln -s /usr/include "$GCC_TOOLCHAIN_ROOT/include"
            ln -s /usr/bin "$GCC_TOOLCHAIN_ROOT/bin"
            mkdir -p "$GCC_TOOLCHAIN_ROOT/lib/gcc/$MULTIARCH_TRIPLET"
            ln -s "/usr/lib/gcc/$MULTIARCH_TRIPLET/${{matrix.gcc_toolchain}}" "$GCC_TOOLCHAIN_ROOT/lib/gcc/$MULTIARCH_TRIPLET/${{matrix.gcc_toolchain}}"

      - name: Setup multiarch
        if: matrix.multiarch
        run: |
          sudo apt-get install --no-install-recommends -y binfmt-support qemu-user-static
          sudo docker run --rm --privileged multiarch/qemu-user-static --reset -p yes
          git clone https://github.com/jeking3/bdde.git
          echo "$(pwd)/bdde/bin/linux" >> ${GITHUB_PATH}
          echo "BDDE_DISTRO=${{ matrix.distro }}" >> ${GITHUB_ENV}
          echo "BDDE_EDITION=${{ matrix.edition }}" >> ${GITHUB_ENV}
          echo "BDDE_ARCH=${{ matrix.arch }}" >> ${GITHUB_ENV}
          echo "B2_WRAPPER=bdde" >> ${GITHUB_ENV}

      - name: Install locales
        if: startsWith(matrix.os, 'ubuntu')
        run: |
          locales=$(locale -a)
          echo "Installed locales: $locales"
          function gen_locale() {
            if ! { echo "$locales" | grep -q "$1" || echo "$locales" | grep -q "${1/UTF-8/utf8}"; }; then
              echo "Adding locale $1"
              sudo locale-gen "${1%.iso*}";
            fi
          }

          gen_locale en_US.UTF-8 # Assumed to be there by tests
          # Used by various tests
          gen_locale he_IL.UTF-8
          gen_locale ja_JP.UTF-8
          gen_locale ru_RU.UTF-8
          gen_locale sv_SE.UTF-8
          gen_locale tr_TR.UTF-8
          # ISO locales used by some tests
          gen_locale en_US.iso88591
          gen_locale he_IL.iso88598
          # Used by test_boundary
          if ! echo "$locales" | grep -q "ja_JP.sjis"; then
            echo "Creating ja_JP.sjis"
            if localedef --help | grep -q -- "--no-warnings="; then
              no_warnings="--no-warnings=ascii"
            else
              no_warnings=""
            fi
            sudo localedef $no_warnings -f SHIFT_JIS -i ja_JP ja_JP.sjis
          fi
          echo "Installed locales: $(locale -a)"

      - name: Setup Boost
        env:
          B2_ADDRESS_MODEL: ${{matrix.address-model}}
          B2_COMPILER: ${{matrix.compiler}}
          B2_CXXSTD: ${{matrix.cxxstd}}
          B2_SANITIZE: ${{matrix.sanitize}}
          B2_STDLIB: ${{matrix.stdlib}}
          # More entries can be added in the same way, see the B2_ARGS assignment in ci/enforce.sh for the possible keys.
          # B2_DEFINES: ${{matrix.defines}}
          # Variables set here (to non-empty) will override the top-level environment variables, e.g.
          # B2_VARIANT: ${{matrix.variant}}
          B2_UBSAN: ${{matrix.ubsan}}
        run: source ci/github/install.sh

      - name: Setup coverage collection
        if: matrix.coverage
        run: ci/github/codecov.sh "setup"

      - name: Run tests
        if: '!matrix.coverity'
        run: |
          B2_TARGETS="libs/$SELF/test//show_config --verbose-test" ci/build.sh
          ci/build.sh

      - name: Run tests with iconv only
        if: '!matrix.coverity'
        run: ci/build.sh
        env: {B2_FLAGS: -a boost.locale.icu=off boost.locale.iconv=on}
      - name: Run tests with ICU only
        if: '!matrix.coverity'
        run: ci/build.sh
        env: {B2_FLAGS: -a boost.locale.icu=on  boost.locale.iconv=off}

      - name: Collect coverage
        if: matrix.coverage
        run: ci/codecov.sh "upload"
        env:
          BOOST_CI_CODECOV_IO_UPLOAD: skip

      - name: Upload coverage
        if: matrix.coverage
        uses: codecov/codecov-action@v4
        with:
          disable_search: true
          file: coverage.info
          name: Github Actions
          token: ${{secrets.CODECOV_TOKEN}}
          verbose: true

      - name: Run coverity
        if: matrix.coverity && github.event_name == 'push' && (github.ref_name == 'develop' || github.ref_name == 'master')
        run: ci/github/coverity.sh
        env:
          COVERITY_SCAN_NOTIFICATION_EMAIL: ${{ secrets.COVERITY_SCAN_NOTIFICATION_EMAIL }}
          COVERITY_SCAN_TOKEN: ${{ secrets.COVERITY_SCAN_TOKEN }}

  windows:
    defaults:
      run:
        shell: cmd
    strategy:
      fail-fast: false
      matrix:
        include:
          - { toolset: msvc-14.2, cxxstd: '14,17,20',       addrmd: '32,64', os: windows-2019 }
          - { name: Collect coverage, coverage: yes,
              toolset: msvc-14.3, cxxstd: '14,17,20',       addrmd: '32,64', os: windows-2022 }
          - { toolset: gcc,       cxxstd: '03,11,14,17,2a', addrmd: '64',    os: windows-2019 }

    runs-on: ${{matrix.os}}
    env: {ICU_VERSION: '71.1'}

    steps:
      - uses: actions/checkout@v4

      - name: Fetch Boost.CI
        uses: actions/checkout@v4
        with:
          repository: boostorg/boost-ci
          ref: master
          path: boost-ci-cloned
      - name: Get CI scripts folder
        run: |
            REM Copy ci folder if not testing Boost.CI
            if "%GITHUB_REPOSITORY%" == "%GITHUB_REPOSITORY:boost-ci=%" xcopy /s /e /q /i /y boost-ci-cloned\ci .\ci
            rmdir /s /q boost-ci-cloned

      - name: Setup Boost
        run: ci\github\install.bat

      - name: Get cached ICU
        uses: actions/cache@v4
        id: cache-icu
        with:
          path: ICU
          key: ICU-${{env.ICU_VERSION}}

      - name: Download ICU
        if: steps.cache-icu.outputs.cache-hit != 'true'
        shell: pwsh
        run: cmake -DICU_ROOT="$($pwd.Path)\ICU" -DICU_VERSION="$Env:ICU_VERSION" -P tools/download_icu.cmake

      - name: Setup ICU
        shell: pwsh
        run: 'Add-Content $Env:BOOST_ROOT/project-config.jam "path-constant ICU_PATH : `"$($pwd.Path)\ICU`" ;"'

      - name: Run tests (WinAPI, without coverage)
        if: '!matrix.coverage'
        run: |
          set B2_FLAGS=boost.locale.icu=off boost.locale.iconv=off boost.locale.std=off boost.locale.winapi=on
          ci\build.bat
          rmdir /s /q bin.v2
        env:
          B2_TOOLSET: ${{matrix.toolset}}
          B2_CXXSTD: ${{matrix.cxxstd}}
          B2_ADDRESS_MODEL: ${{matrix.addrmd}}

      - name: Run tests (ICU, without coverage)
        if: '!matrix.coverage'
        run: |
          set B2_FLAGS=boost.locale.icu=on boost.locale.iconv=off boost.locale.std=off boost.locale.winapi=off
          ci\build.bat
          rmdir /s /q bin.v2
        env:
          B2_TOOLSET: ${{matrix.toolset}}
          B2_CXXSTD: ${{matrix.cxxstd}}
          B2_ADDRESS_MODEL: ${{matrix.addrmd}}

      - name: Run tests (without coverage)
        if: '!matrix.coverage'
        run: |
          set B2_TARGETS=libs/%SELF%/test//show_config --verbose-test
          ci\build.bat
          set B2_TARGETS=
          ci\build.bat
        env:
          B2_TOOLSET: ${{matrix.toolset}}
          B2_CXXSTD: ${{matrix.cxxstd}}
          B2_ADDRESS_MODEL: ${{matrix.addrmd}}

      - name: Show config before collecting coverage
        if: matrix.coverage
        run: |
          set B2_TARGETS=libs/%SELF%/test//show_config --verbose-test
          ci\build.bat
        env:
          B2_TOOLSET: ${{matrix.toolset}}
          B2_CXXSTD: ${{matrix.cxxstd}}
          B2_ADDRESS_MODEL: ${{matrix.addrmd}}

      - name: Collect coverage
        shell: powershell
        if: matrix.coverage
        run: ci\opencppcoverage.ps1
        env:
          B2_TOOLSET: ${{matrix.toolset}}
          B2_CXXSTD: ${{matrix.cxxstd}}
          B2_ADDRESS_MODEL: ${{matrix.addrmd}}

      - name: Upload coverage
        if: matrix.coverage
        uses: codecov/codecov-action@v4
        with:
          disable_search: true
          file: __out/cobertura.xml
          name: Github Actions
          token: ${{secrets.CODECOV_TOKEN}}
          verbose: true

  MSYS2:
    defaults:
      run:
        shell: msys2 {0}
    strategy:
      fail-fast: false
      matrix:
        include:
          - { sys: MINGW32, compiler: gcc, cxxstd: '03,11,17,20' }
          - { sys: MINGW64, compiler: gcc, cxxstd: '03,11,17,20' }

    runs-on: windows-latest

    steps:
      - uses: actions/checkout@v4

      - name: Setup MSYS2 environment
        uses: msys2/setup-msys2@v2
        with:
          msystem: ${{matrix.sys}}
          update: true
          install: git python
          pacboy: gcc:p cmake:p ninja:p

      - name: Fetch Boost.CI
        uses: actions/checkout@v4
        with:
          repository: boostorg/boost-ci
          ref: master
          path: boost-ci-cloned
      - name: Get CI scripts folder
        run: |
            # Copy ci folder if not testing Boost.CI
            [[ "$GITHUB_REPOSITORY" =~ "boost-ci" ]] || cp -r boost-ci-cloned/ci .
            rm -rf boost-ci-cloned

      - name: Setup Boost
        env:
          B2_COMPILER: ${{matrix.compiler}}
          B2_CXXSTD: ${{matrix.cxxstd}}
          B2_SANITIZE: ${{matrix.sanitize}}
          B2_STDLIB: ${{matrix.stdlib}}
          B2_FLAGS: runtime-link=static,shared
        run: ci/github/install.sh

      - name: Run tests
        run: ci/build.sh

      # Run also the CMake tests to avoid having to setup another matrix for CMake on MSYS
      - name: Run CMake tests
        run: |
            cd "$BOOST_ROOT"
            mkdir __build_cmake_test__ && cd __build_cmake_test__
            cmake -G Ninja -DCMAKE_BUILD_TYPE=Debug -DBOOST_INCLUDE_LIBRARIES=$SELF -DBUILD_SHARED_LIBS=ON -DBUILD_TESTING=ON -DBoost_VERBOSE=ON ..
            cmake --build . --target tests --config Debug -j$B2_JOBS
            ctest --output-on-failure --build-config Debug

  CMake:
    defaults:
      run:
        shell: bash

    strategy:
      fail-fast: false
      matrix:
        include:
          - { os: ubuntu-20.04, build_shared: ON,  build_type: Debug, generator: 'Unix Makefiles' }
          - { os: ubuntu-20.04, build_shared: OFF, build_type: Debug, generator: 'Unix Makefiles' }
          - { os: ubuntu-20.04, build_shared: OFF, build_type: Debug, generator: 'Unix Makefiles', icu: '71.1' }
          - { os: ubuntu-22.04, build_shared: OFF, build_type: Debug, generator: 'Unix Makefiles', icu: '72.1' }
          - { os: ubuntu-22.04, build_shared: OFF, build_type: Debug, generator: 'Unix Makefiles', icu: '73.1' }
          - { os: windows-2019, build_shared: ON,  build_type: Debug, generator: 'Visual Studio 16 2019', icu: '71.1' }
          - { os: windows-2019, build_shared: OFF, build_type: Debug, generator: 'Visual Studio 16 2019', icu: '73.1' }

    timeout-minutes: 120
    runs-on: ${{matrix.os}}

    steps:
      - uses: actions/checkout@v4
      - name: Fetch Boost.CI
        uses: actions/checkout@v4
        with:
          repository: boostorg/boost-ci
          ref: master
          path: boost-ci-cloned
      - name: Get CI scripts folder
        run: |
            # Copy ci folder if not testing Boost.CI
            [[ "$GITHUB_REPOSITORY" =~ "boost-ci" ]] || cp -r boost-ci-cloned/ci .
            rm -rf boost-ci-cloned
      - name: Setup Boost
        env: {B2_DONT_BOOTSTRAP: 1}
        run: source ci/github/install.sh

      - name: Setup ICU (Windows)
        if: runner.os == 'Windows' && matrix.icu
        shell: pwsh
        run: |
            $ICU_ROOT="$($pwd.Path)\ICU"
            Add-Content $Env:GITHUB_ENV "ICU_ROOT=$ICU_ROOT"
            # Make sure shared libs can be found at runtime
            Add-Content $Env:GITHUB_PATH "$ICU_ROOT\bin64"
      - name: Setup ICU (Non-Windows)
        if: runner.os != 'Windows' && matrix.icu
        run: |
            ICU_ROOT="$PWD/ICU"
            echo "ICU_ROOT=$ICU_ROOT" >> $GITHUB_ENV
            # Make sure shared libs can be found at runtime
            echo "LD_LIBRARY_PATH=$ICU_ROOT/lib:$LD_LIBRARY_PATH" >> $GITHUB_ENV
      - name: Get cached ICU
        if: matrix.icu
        uses: actions/cache@v4
        id: cache-icu
        with:
          path: ICU
          key: ICU-${{matrix.os}}-${{matrix.icu}}
      - name: Download ICU
        if: matrix.icu && steps.cache-icu.outputs.cache-hit != 'true'
        run: cmake -DICU_VERSION="${{matrix.icu}}" -P tools/download_icu.cmake

      - name: Run CMake tests
        run: |
            cd "$BOOST_ROOT"
            mkdir __build_cmake_test__ && cd __build_cmake_test__
            cmake -G "${{matrix.generator}}" -DCMAKE_BUILD_TYPE=${{matrix.build_type}} -DBOOST_INCLUDE_LIBRARIES=$SELF -DBUILD_SHARED_LIBS=${{matrix.build_shared}} -DBUILD_TESTING=ON -DBoost_VERBOSE=ON -DBOOST_LOCALE_WERROR=ON ..
            cmake --build . --target tests --config ${{matrix.build_type}} -j$B2_JOBS
            ctest --output-on-failure --build-config ${{matrix.build_type}}

      - name: Run CMake subdir tests
        run: |
            cmake_test_folder="$BOOST_ROOT/libs/$SELF/test/cmake_test" # New unified folder
            [ -d "$cmake_test_folder" ] || cmake_test_folder="$BOOST_ROOT/libs/$SELF/test/cmake_subdir_test"
            cd "$cmake_test_folder"
            mkdir __build_cmake_subdir_test__ && cd __build_cmake_subdir_test__
            extra_args=""
            # On Windows DLLs need to be either in PATH or in the same folder as the executable, so put all binaries into the same folder
            if [[ "$RUNNER_OS" == "Windows" ]] && [[ "${{matrix.build_shared}}" == "ON" ]]; then
                extra_args="-DCMAKE_RUNTIME_OUTPUT_DIRECTORY='$(pwd)/bin'"
            fi
            cmake -G "${{matrix.generator}}" -DBOOST_CI_INSTALL_TEST=OFF -DCMAKE_BUILD_TYPE=${{matrix.build_type}} -DBUILD_SHARED_LIBS=${{matrix.build_shared}} $extra_args ..
            cmake --build . --config ${{matrix.build_type}} -j$B2_JOBS
            ctest --output-on-failure --build-config ${{matrix.build_type}}

      - name: Install Library
        run: |
            BCM_INSTALL_PATH=/tmp/boost_install
            echo "BCM_INSTALL_PATH=$BCM_INSTALL_PATH" >> $GITHUB_ENV
            cd "$BOOST_ROOT"
            mkdir __build_cmake_install_test__ && cd __build_cmake_install_test__
            cmake -G "${{matrix.generator}}" -DCMAKE_BUILD_TYPE=${{matrix.build_type}} -DBOOST_INCLUDE_LIBRARIES=$SELF -DBUILD_SHARED_LIBS=${{matrix.build_shared}} -DCMAKE_INSTALL_PREFIX="$BCM_INSTALL_PATH" -DBoost_VERBOSE=ON -DBoost_DEBUG=ON ..
            cmake --build . --target install --config ${{matrix.build_type}} -j$B2_JOBS
      - name: Run CMake install tests
        run: |
            cmake_test_folder="$BOOST_ROOT/libs/$SELF/test/cmake_test" # New unified folder
            [ -d "$cmake_test_folder" ] || cmake_test_folder="$BOOST_ROOT/libs/$SELF/test/cmake_install_test"
            cd "$cmake_test_folder"
            mkdir __build_cmake_install_test__ && cd __build_cmake_install_test__
            cmake -G "${{matrix.generator}}" -DBOOST_CI_INSTALL_TEST=ON -DCMAKE_BUILD_TYPE=${{matrix.build_type}} -DBUILD_SHARED_LIBS=${{matrix.build_shared}} -DCMAKE_PREFIX_PATH="$BCM_INSTALL_PATH" ..
            cmake --build . --config ${{matrix.build_type}} -j$B2_JOBS
            if [[ "${{matrix.build_shared}}" == "ON" ]]; then
                # Make sure shared libs can be found at runtime
                if [ "$RUNNER_OS" == "Windows" ]; then
                    export PATH="$BCM_INSTALL_PATH/bin:$PATH"
                else
                    export LD_LIBRARY_PATH="$BCM_INSTALL_PATH/lib:$LD_LIBRARY_PATH"
                fi
            fi
            ctest --output-on-failure --build-config ${{matrix.build_type}}
