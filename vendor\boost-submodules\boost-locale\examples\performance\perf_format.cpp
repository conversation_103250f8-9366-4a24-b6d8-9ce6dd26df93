//
// Copyright (c) 2009-2011 <PERSON><PERSON><PERSON> (Tonkikh)
//
// Distributed under the Boost Software License, Version 1.0.
// https://www.boost.org/LICENSE_1_0.txt

#include <boost/locale.hpp>
#include <ctime>
#include <iostream>
#include <set>
#include <string>

using namespace boost::locale;

int main(int argc, char** argv)
{
    if(argc != 3) {
        std::cerr << "Usage backend locale\n";
        return 1;
    }
    boost::locale::localization_backend_manager mgr = boost::locale::localization_backend_manager::global();
    mgr.select(argv[1]);
    generator gen(mgr);
    // Set global locale to requested
    std::locale::global(gen(argv[2]));

    for(int i = 0; i < 100000; i++) {
        std::ostringstream ss;
        for(int j = 0; j < 5; j++) {
            ss << boost::locale::as::datetime << std::time(nullptr) << " " << boost::locale::as::number << 13456.345
               << "\n";
        }
        if(i == 0)
            std::cout << ss.str() << std::endl;
    }
}
