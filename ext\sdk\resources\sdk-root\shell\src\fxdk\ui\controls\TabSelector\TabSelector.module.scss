@import "variables";

.root {
  display: flex;
  align-items: flex-end;

  width: 100%;

  .extender {
    flex-grow: 1;
    border-bottom: solid 2px rgba($fgColor, .2);
  }

  .item {
    padding: $q*2 $q*4;

    border-bottom: solid 2px rgba($fgColor, .2);

    user-select: none;
    cursor: pointer;

    &:hover {
      background-color: rgba($fgColor, .1);
    }

    &.active {
      padding-bottom: $q*2 + 2px;

      // expanded as something is messing up order of these rules in prod build :\
      // like, `border: solid 2px` ALL OF A SUDDEN appears after `border-image-*` rules, which, of course, breaks border-image
      border-width: 2px;
      border-style: solid;
      border-image-slice: 1;
      border-image-source: linear-gradient(to bottom, $acColor, rgba($fgColor, .2));
      border-bottom: none;
    }
  }

  &.vertical {
    flex-direction: column;

    width: auto;
    min-width: 250px;

    .item {
      display: flex;
      align-items: center;

      gap: $q;

      width: 100%;
      height: $q*8;

      padding: 0 $q*4;
      border: none;

      svg {
        height: $fs08;

        opacity: .75;
      }

      &:hover {
        background-color: rgba($fgColor, .05);
      }

      &.active {
        background-color: rgba($fgColor, .2);
      }
    }
  }
}
