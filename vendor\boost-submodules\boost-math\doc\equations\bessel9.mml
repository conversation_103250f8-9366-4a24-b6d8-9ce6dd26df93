<math  xmlns="http://www.w3.org/1998/Math/MathML" display="block" ><mrow >                        <msub><mrow ><mi >J</mi></mrow><mrow ><mo class="MathClass-bin">&#x2212;</mo><mi >&#x03BD;</mi></mrow></msub ><mrow ><mo class="MathClass-open">(</mo><mrow><mi >z</mi></mrow><mo class="MathClass-close">)</mo></mrow> <mo class="MathClass-rel">=</mo><mo class="MathClass-op"> cos</mo><!--nolimits--><mrow ><mo class="MathClass-open">(</mo><mrow><mi >&#x03BD;</mi><mi >&#x03C0;</mi></mrow><mo class="MathClass-close">)</mo></mrow><msub><mrow ><mi >J</mi></mrow><mrow ><mi >&#x03BD;</mi></mrow></msub ><mrow ><mo class="MathClass-open">(</mo><mrow><mi >z</mi></mrow><mo class="MathClass-close">)</mo></mrow> <mo class="MathClass-bin">&#x2212;</mo><mo class="MathClass-op"> sin</mo><!--nolimits--><mrow ><mo class="MathClass-open">(</mo><mrow><mi >&#x03BD;</mi><mi >&#x03C0;</mi></mrow><mo class="MathClass-close">)</mo></mrow><msub><mrow ><mi >Y</mi> </mrow><mrow ><mi >&#x03BD;</mi></mrow></msub ><mrow ><mo class="MathClass-open">(</mo><mrow><mi >z</mi></mrow><mo class="MathClass-close">)</mo></mrow></mrow></math>