.root {
  @include ui.border-radius('normal');

  @include ui.def('backdrop-color', ui.color-token('backdrop-300'));
  @include ui.fake-backdrop-blur();

  cursor: pointer;

  overflow: hidden;

  .showOnHover {
    display: none;
  }
  .visibleOnHover {
    opacity: 0;
  }
  .hideOnHover {
    display: block;
  }
  &:hover {
    .showOnHover {
      display: block;
    }
    .visibleOnHover {
      opacity: 1;
    }
    .hideOnHover {
      display: none;
    }
  }

  @include ui.animated('box-shadow');

  box-shadow: 0 0 0 2px transparent inset;
  &:hover {
    box-shadow: 0 0 0 2px ui.color-token('outlined-hover-border') inset;
  }

  &.placeControlsBelow {
    padding: 0 ui.offset('small');
    height: 100%;

    .content {
      height: 100%;
    }
  }

  &.withBanner {
    @include ui.animated('opacity', '.banner');
    &:hover {
      .banner {
        opacity: 1;
      }
    }

    .banner {
      display: block;

      width: 100%;
      aspect-ratio: 19.2;

      background-color: ui.color-token('text-a25');
      background-image: ui.use('banner');
      background-size: cover;
      background-position: center center;

      opacity: .75;

      will-change: filter, opacity;
    }

    .content {
      padding-top: ui.offset();
    }
  }

  .banner {
    display: none;
  }

  .content {
    padding: ui.offset();

    .players {
      color: ui.color-token('text-a50');
    }
  }
}

.serverpowerbutton {
  border-radius: 0 ui.q(1) ui.q(1) 0;
  border-left: 1px solid #9EA4BD;
  background-color: ui.color-token('button-background');

  &:disabled {
    border-left: 2px solid #9EA4BD;
    background-color: ui.color-token('button-background');
    cursor: default;
    border-top: none;
    border-right: none;
    border-bottom: none;
  }
}

.serverboostbutton {
  border: none;

  &:hover {
    background-color: ui.color-token('backdrop');
    cursor: pointer;
  }

  &:disabled {
    background-color: ui.color-token('button-background');
    cursor: default;
  }
}

.serverboostbutton-active {
  border-radius: ui.q(1) 0 0 ui.q(1);
}