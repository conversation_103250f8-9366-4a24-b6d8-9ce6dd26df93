// Dynamic Island Style Navigation
.topNavigation {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;

  // Dynamic Island shape
  background: rgba(0, 0, 0, 0.85);
  backdrop-filter: blur(20px) saturate(1.5);
  border-radius: 50px;
  padding: 12px 24px;

  // Smooth transitions
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);

  // Subtle shadow for depth
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.4),
    0 2px 8px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);

  // Subtle border
  border: 1px solid rgba(255, 255, 255, 0.1);

  // Subtle hover effect - giữ nguyên vị trí, chỉ scale nhẹ
  &:hover {
    transform: translateX(-50%) scale(1.05);
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(25px) saturate(1.8);
    box-shadow:
      0 12px 40px rgba(0, 0, 0, 0.5),
      0 4px 12px rgba(0, 0, 0, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.15);
  }

  // Dynamic Island glow effect
  &::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg,
        rgba(255, 140, 66, 0.3) 0%,
        rgba(124, 179, 66, 0.2) 50%,
        rgba(245, 230, 168, 0.3) 100%);
    border-radius: 52px;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover::before {
    opacity: 1;
  }

  // Inner highlight
  &::after {
    content: '';
    position: absolute;
    top: 1px;
    left: 1px;
    right: 1px;
    height: 1px;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(255, 255, 255, 0.3) 50%,
        transparent 100%);
    border-radius: 50px 50px 0 0;
    pointer-events: none;
  }
}

.container {
  display: flex;
  align-items: center;
  gap: 24px;
  max-width: none;
  margin: 0;
  padding: 0;
}

.logo {
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 20px;

  &:hover {
    transform: scale(1.1);
    background: rgba(255, 255, 255, 0.1);
  }
}

.logoImage {
  height: 32px;
  width: auto;
  max-width: 80px;
  object-fit: contain;
  filter: brightness(1.1) drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
  transition: all 0.3s ease;

  &:hover {
    filter: brightness(1.2) drop-shadow(0 2px 4px rgba(0, 0, 0, 0.4));
  }
}

.logoText {
  font-size: 1.6rem;
  font-weight: 700;
  background: linear-gradient(135deg, var(--gaming-text) 0%, var(--gaming-primary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  letter-spacing: -0.01em;
}

.navigationLinks {
  display: flex;
  align-items: center;
  gap: 8px;
}

.navLink {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  padding: 8px 16px;
  border-radius: 20px;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  letter-spacing: 0.01em;
  white-space: nowrap;

  &:hover {
    background: rgba(255, 255, 255, 0.15);
    color: rgba(255, 255, 255, 1);
    transform: scale(1.1);
  }

  &:active {
    transform: scale(0.95);
  }

  // Active state with subtle indicator
  &.active {
    background: rgba(255, 140, 66, 0.2);
    color: #FF8C42;

    &::after {
      content: '';
      position: absolute;
      bottom: 2px;
      left: 50%;
      transform: translateX(-50%);
      width: 4px;
      height: 4px;
      background: #FF8C42;
      border-radius: 50%;
      box-shadow: 0 0 8px rgba(255, 140, 66, 0.6);
    }
  }
}

.userSection {
  display: flex;
  align-items: center;
  gap: 12px;
}

.userInfo {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 6px 12px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: scale(1.05);
  }
}

.userMenu {
  display: flex;
  align-items: center;
  gap: 15px;
}

.userName {
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.8rem;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  white-space: nowrap;
  max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.loginButton,
.logoutButton {
  background: rgba(255, 255, 255, 0.1);
  border: none !important;
  color: rgba(255, 255, 255, 0.9);
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  backdrop-filter: blur(10px);
  letter-spacing: 0.01em;
  white-space: nowrap;

  &:hover {
    background: rgba(255, 255, 255, 0.2);
    border: none !important;
    transform: scale(1.1);
    box-shadow: none !important;
  }

  &:active {
    transform: scale(0.95);
  }
}

.logoutButton {
  background: rgba(255, 59, 48, 0.2);
  color: rgba(255, 255, 255, 0.95);
  padding: 6px 12px;
  font-size: 0.75rem;

  &:hover {
    background: rgba(255, 59, 48, 0.3);
    transform: scale(1.1);
  }
}

// Dynamic Island responsive design
@media (max-width: 768px) {
  .topNavigation {
    top: 15px;
    padding: 10px 20px;
    border-radius: 40px;
  }

  .container {
    gap: 16px;
  }

  .navigationLinks {
    gap: 4px;
  }

  .navLink {
    padding: 6px 12px;
    font-size: 0.8rem;
  }

  .logoImage {
    height: 28px;
    max-width: 70px;
  }

  .userName {
    display: none;
  }

  .userInfo {
    padding: 4px 8px;
    gap: 8px;
  }

  .loginButton,
  .logoutButton {
    padding: 6px 12px;
    font-size: 0.75rem;
  }
}

@media (max-width: 480px) {
  .topNavigation {
    top: 10px;
    padding: 8px 16px;
    border-radius: 35px;
  }

  .container {
    gap: 12px;
  }

  .navigationLinks {
    gap: 2px;
  }

  .navLink {
    padding: 4px 8px;
    font-size: 0.75rem;
  }

  .logoImage {
    height: 24px;
    max-width: 60px;
  }

  .userInfo {
    padding: 3px 6px;
  }

  .loginButton,
  .logoutButton {
    padding: 4px 8px;
    font-size: 0.7rem;
  }
}

// Dynamic Island entrance animation - mượt mà và giữ nguyên vị trí
@keyframes dynamicIslandEntrance {
  0% {
    transform: translateX(-50%) scale(0.9);
    opacity: 0;
  }

  100% {
    transform: translateX(-50%) scale(1);
    opacity: 1;
  }
}

.topNavigation {
  animation: dynamicIslandEntrance 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);

  // Enhanced webkit support
  -webkit-backdrop-filter: blur(20px) saturate(1.5);
}