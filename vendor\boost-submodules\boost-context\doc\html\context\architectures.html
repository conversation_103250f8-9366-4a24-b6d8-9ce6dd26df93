<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=US-ASCII">
<title>Architectures</title>
<link rel="stylesheet" href="../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../index.html" title="Chapter&#160;1.&#160;Context">
<link rel="up" href="../index.html" title="Chapter&#160;1.&#160;Context">
<link rel="prev" href="performance.html" title="Performance">
<link rel="next" href="architectures/crosscompiling.html" title="Cross compiling">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../boost.png"></td>
<td align="center"><a href="../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="performance.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../index.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="architectures/crosscompiling.html"><img src="../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="context.architectures"></a><a class="link" href="architectures.html" title="Architectures">Architectures</a>
</h2></div></div></div>
<div class="toc"><dl><dt><span class="section"><a href="architectures/crosscompiling.html">Cross compiling</a></span></dt></dl></div>
<p>
      <span class="bold"><strong>Boost.Context</strong></span>, using <a class="link" href="ff/implementations__fcontext_t__ucontext_t_and_winfiber.html#implementation"><span class="emphasis"><em>fcontext_t</em></span></a>,
      supports following architectures:
    </p>
<div class="table">
<a name="context.architectures.supported_architectures___abi_binary_format__"></a><p class="title"><b>Table&#160;1.2.&#160;Supported architectures (&lt;ABI|binary format&gt;)</b></p>
<div class="table-contents"><table class="table" summary="Supported architectures (&lt;ABI|binary format&gt;)">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              <p>
                Architecture
              </p>
            </th>
<th>
              <p>
                LINUX (UNIX)
              </p>
            </th>
<th>
              <p>
                Windows
              </p>
            </th>
<th>
              <p>
                MacOS X
              </p>
            </th>
<th>
              <p>
                iOS
              </p>
            </th>
</tr></thead>
<tbody>
<tr>
<td>
              <p>
                arm (aarch32)
              </p>
            </td>
<td>
              <p>
                AAPCS|ELF
              </p>
            </td>
<td>
              <p>
                AAPCS|PE
              </p>
            </td>
<td>
              <p>
                -
              </p>
            </td>
<td>
              <p>
                AAPCS|MACH-O
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                arm (aarch64)
              </p>
            </td>
<td>
              <p>
                AAPCS|ELF
              </p>
            </td>
<td>
              <p>
                -
              </p>
            </td>
<td>
              <p>
                -
              </p>
            </td>
<td>
              <p>
                AAPCS|MACH-O
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                i386
              </p>
            </td>
<td>
              <p>
                SYSV|ELF
              </p>
            </td>
<td>
              <p>
                MS|PE
              </p>
            </td>
<td>
              <p>
                SYSV|MACH-O
              </p>
            </td>
<td>
              <p>
                -
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                loongarch64
              </p>
            </td>
<td>
              <p>
                SYSV|ELF
              </p>
            </td>
<td>
              <p>
                -
              </p>
            </td>
<td>
              <p>
                -
              </p>
            </td>
<td>
              <p>
                -
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                mips
              </p>
            </td>
<td>
              <p>
                O32,N64|ELF
              </p>
            </td>
<td>
              <p>
                -
              </p>
            </td>
<td>
              <p>
                -
              </p>
            </td>
<td>
              <p>
                -
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                ppc32
              </p>
            </td>
<td>
              <p>
                SYSV|ELF,XCOFF
              </p>
            </td>
<td>
              <p>
                -
              </p>
            </td>
<td>
              <p>
                SYSV|MACH-O
              </p>
            </td>
<td>
              <p>
                -
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                ppc64
              </p>
            </td>
<td>
              <p>
                SYSV|ELF,XCOFF
              </p>
            </td>
<td>
              <p>
                -
              </p>
            </td>
<td>
              <p>
                SYSV|MACH-O
              </p>
            </td>
<td>
              <p>
                -
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                riscv64
              </p>
            </td>
<td>
              <p>
                SYSV|ELF
              </p>
            </td>
<td>
              <p>
                -
              </p>
            </td>
<td>
              <p>
                SYSV
              </p>
            </td>
<td>
              <p>
                -
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                s390x
              </p>
            </td>
<td>
              <p>
                SYSV|ELF
              </p>
            </td>
<td>
              <p>
                -
              </p>
            </td>
<td>
              <p>
                -
              </p>
            </td>
<td>
              <p>
                -
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                sparc
              </p>
            </td>
<td>
              <p>
                -
              </p>
            </td>
<td>
              <p>
                -
              </p>
            </td>
<td>
              <p>
                -
              </p>
            </td>
<td>
              <p>
                -
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                x86_64
              </p>
            </td>
<td>
              <p>
                SYSV,X32|ELF
              </p>
            </td>
<td>
              <p>
                MS|PE
              </p>
            </td>
<td>
              <p>
                SYSV|MACH-O
              </p>
            </td>
<td>
              <p>
                -
              </p>
            </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="note"><table border="0" summary="Note">
<tr>
<td rowspan="2" align="center" valign="top" width="25"><img alt="[Note]" src="../../../../../doc/src/images/note.png"></td>
<th align="left">Note</th>
</tr>
<tr><td align="left" valign="top"><p>
        If the architecture is not supported but the platform provides <a class="link" href="ff/implementations__fcontext_t__ucontext_t_and_winfiber.html#implementation"><span class="emphasis"><em>ucontext_t</em></span></a>,
        <span class="bold"><strong>Boost.Context</strong></span> should be compiled with <code class="computeroutput"><span class="identifier">BOOST_USE_UCONTEXT</span></code> and b2 property <code class="computeroutput"><span class="identifier">context</span><span class="special">-</span><span class="identifier">impl</span><span class="special">=</span><span class="identifier">ucontext</span></code>.
      </p></td></tr>
</table></div>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright &#169; 2014 Oliver Kowalke<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="performance.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../index.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="architectures/crosscompiling.html"><img src="../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
