# Copyright 2018 The Chromium Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

typemaps = [
  "//mojo/public/cpp/base/application_state.typemap",
  "//mojo/public/cpp/base/big_buffer.typemap",
  "//mojo/public/cpp/base/big_string.typemap",
  "//mojo/public/cpp/base/file_error.typemap",
  "//mojo/public/cpp/base/file_info.typemap",
  "//mojo/public/cpp/base/file_path.typemap",
  "//mojo/public/cpp/base/file.typemap",
  "//mojo/public/cpp/base/generic_pending_receiver.typemap",
  "//mojo/public/cpp/base/read_only_buffer.typemap",
  "//mojo/public/cpp/base/memory_allocator_dump_cross_process_uid.typemap",
  "//mojo/public/cpp/base/memory_pressure_level.typemap",
  "//mojo/public/cpp/base/message_pump_type.typemap",
  "//mojo/public/cpp/base/process_id.typemap",
  "//mojo/public/cpp/base/ref_counted_memory.typemap",
  "//mojo/public/cpp/base/shared_memory.typemap",
  "//mojo/public/cpp/base/string16.typemap",
  "//mojo/public/cpp/base/logfont_win.typemap",
  "//mojo/public/cpp/base/text_direction.typemap",
  "//mojo/public/cpp/base/thread_priority.typemap",
  "//mojo/public/cpp/base/time.typemap",
  "//mojo/public/cpp/base/token.typemap",
  "//mojo/public/cpp/base/unguessable_token.typemap",
  "//mojo/public/cpp/base/values.typemap",
]
