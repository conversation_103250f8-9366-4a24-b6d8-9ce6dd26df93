@import "variables";

.root {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;

  display: flex;
  align-items: center;
  justify-content: center;

  z-index: 9500;

  &.full-width {
    .modal {
      width: 60vw;
    }
  }

  &.full-height {
    .modal {
      height: 60vh;
    }
  }

  .backdrop {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;

    @keyframes appearance {
      0% {
        opacity: 0;
      }
      100% {
        opacity: 1;
      }
    }
    animation: appearance .2s ease;

    background-color: rgba($bgColor, .8);

    z-index: 1;
  }

  .modal {
    display: flex;
    flex-direction: column;

    max-width: 60vw;
    min-width: 30vw;
    max-height: calc(#{$modalContentHeight} + 4px);

    overflow: hidden;

    border: solid 2px rgba($fgColor, .1);
    border-radius: $q*2;

    background-color: $bgColor;
    box-shadow: 0 $q*5 $q*20 $shadowColor;

    z-index: 2;

    @keyframes appearance2 {
      0% {
        opacity: 0;
        transform: scale(.9);
      }
      100% {
        opacity: 1;
        transform: scale(1);
      }
    }
    animation: appearance2 .2s ease;

    .header {
      width: 100%;
      flex-shrink: 0;
      flex-grow: 0;

      @include fontSecondary;
      font-weight: 400;

      padding: $q*3 $q*4;
      text-transform: uppercase;
      margin-bottom: 0;

      user-select: none;
    }

    .content {
      flex-shrink: 0;
      flex-grow: 1;
      height: 1px;
      overflow-y: auto;
    }

    .actions {
      flex-shrink: 0;
      flex-grow: 0;

      display: flex;
      align-items: center;
      justify-content: space-between;

      padding: $q*4;

      justify-self: flex-end;

      border-top: solid 2px rgba($fgColor, .05);
    }

    .split-horizonal-layout {
      display: flex;

      width: 100%;
      height: 100%;

      overflow: hidden;

      .left {
        display: flex;
        flex-direction: column;
        align-items: stretch;
        justify-content: stretch;

        height: 100%;

        border-right: solid 2px rgba($fgColor, .05);
      }

      .right {
        flex-grow: 1;

        display: flex;
        flex-direction: column;
        align-items: stretch;
        justify-content: stretch;

        padding-top: $q*3;

        overflow-y: auto;
      }
    }
  }
}
