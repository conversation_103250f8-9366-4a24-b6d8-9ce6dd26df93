@import "../../../vars.scss";

.root {
  display: flex;

  width: auto;

  padding: 1px;

  @keyframes appearance {
    0% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }
  animation: appearance .2s ease;

  .icon {
    display: flex;
    align-items: center;
    justify-content: center;

    width: $weToolbarHeight;

    font-size: $fs1;
    color: $scColor;
  }

  .label {
    display: block;

    color: $fgColor;

    font-size: $fs08;

    max-width: 100%;

    @include ellipsis;
  }

  .clear {
    display: flex;
    align-items: center;
    justify-content: center;

    width: calc(#{$weToolbarHeight} - 2px);
    height: calc(#{$weToolbarHeight} - 2px);

    border-radius: $weToolbarHeight;
    border: none;

    font-size: $fs1;

    color: rgba($fgColor, .75);
    background-color: transparent;

    cursor: pointer;

    @include interactiveTransition;

    &:hover {
      color: rgba($fgColor, 1);
      background-color: $acColor;
    }
  }
}

.panel-wrapper {
  position: fixed;

  top: $weToolbarOffset + $weToolbarHeight + $wePanelOffset;
  left: 0;
  right: 0;

  display: flex;
  align-items: flex-start;
  justify-content: center;

  height: 0;

  .panel {
    position: unset;

    padding: $q*2;
  }
}
