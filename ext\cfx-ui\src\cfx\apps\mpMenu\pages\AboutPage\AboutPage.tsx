import { Page } from '@cfx-dev/ui-components';
import { observer } from 'mobx-react-lite';

export const AboutPage = observer(function AboutPage() {
  return (
    <Page>
      <div style={{
        padding: '3rem',
        textAlign: 'center',
        color: 'white',
        background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%)',
        backdropFilter: 'blur(25px)',
        borderRadius: '24px',
        margin: '2rem',
        boxShadow: '0 32px 64px -12px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.2) inset',
        border: '1px solid rgba(255, 255, 255, 0.2)'
      }}>
        <h1 style={{
          fontSize: '3rem',
          marginBottom: '1.5rem',
          background: 'linear-gradient(135deg, #ffffff 0%, #f7fafc 100%)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          backgroundClip: 'text',
          fontWeight: '800',
          letterSpacing: '-0.02em'
        }}>About Us</h1>
        <p style={{
          fontSize: '1.3rem',
          lineHeight: '1.7',
          maxWidth: '900px',
          margin: '0 auto 3rem auto',
          opacity: '0.9',
          fontWeight: '500'
        }}>
          Welcome to our CFX platform. We provide cutting-edge gaming experiences
          with modern interfaces and seamless gameplay. Our mission is to create
          the most immersive and user-friendly gaming environment possible.
        </p>
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',
          gap: '2rem'
        }}>
          {[
            { title: 'Innovation', desc: 'Cutting-edge technology and modern design' },
            { title: 'Community', desc: 'Built by gamers, for gamers worldwide' },
            { title: 'Performance', desc: 'Optimized for the best gaming experience' }
          ].map((item, index) => (
            <div key={index} style={{
              background: 'rgba(255, 255, 255, 0.1)',
              padding: '2rem',
              borderRadius: '16px',
              backdropFilter: 'blur(15px)',
              border: '1px solid rgba(255, 255, 255, 0.15)',
              transition: 'all 0.3s ease',
              cursor: 'pointer'
            }}>
              <h3 style={{ fontSize: '1.5rem', marginBottom: '1rem', fontWeight: '700' }}>{item.title}</h3>
              <p style={{ opacity: '0.8', lineHeight: '1.6' }}>{item.desc}</p>
            </div>
          ))}
        </div>
      </div>
    </Page>
  );
});
