/* eslint-disable react/no-unescaped-entities */
import {
  Icons,
  Flex,
  Text,
  Title,
} from '@cfx-dev/ui-components';
import * as pdfjsLib from 'pdfjs-dist';
import * as pdfjsViewer from 'pdfjs-dist/web/pdf_viewer';
import React from 'react';

import { LinkButton } from 'cfx/ui/Button/LinkButton';
import { useTimeoutFlag } from 'cfx/utils/hooks';

import 'pdfjs-dist/web/pdf_viewer.css';

import s from './PDFRenderer.module.scss';

// Check if PDF.js is properly loaded
const isPDFJSSupported = () => {
  try {
    return typeof pdfjsLib !== 'undefined' &&
      typeof pdfjsViewer !== 'undefined' &&
      pdfjsLib.getDocument;
  } catch {
    return false;
  }
};

// Configure PDF.js worker with improved fallback options
function configurePDFWorker() {
  if (typeof window === 'undefined') {
    return;
  }

  const workerOptions = [
    // Option 1: Use CDN worker (most reliable)
    () => {
      pdfjsLib.GlobalWorkerOptions.workerSrc = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js`;
      console.log('PDFRenderer: Using CDN worker');
    },
    // Option 2: Use local worker from webpack build
    () => {
      pdfjsLib.GlobalWorkerOptions.workerSrc = new URL('pdfjs-dist/build/pdf.worker.min.js', import.meta.url).toString();
      console.log('PDFRenderer: Using local worker from build directory');
    },
    // Option 3: Use worker from static assets
    () => {
      pdfjsLib.GlobalWorkerOptions.workerSrc = '/static/js/pdf.worker.min.js';
      console.log('PDFRenderer: Using worker from static assets');
    },
    // Option 4: Disable worker (fallback to main thread)
    () => {
      pdfjsLib.GlobalWorkerOptions.workerSrc = '';
      console.log('PDFRenderer: Disabled worker, using main thread');
    },
  ];

  let workerConfigured = false;

  for (let i = 0; i < workerOptions.length; i++) {
    try {
      workerOptions[i]();
      workerConfigured = true;
      break;
    } catch (error) {
      console.warn(`PDFRenderer: Worker option ${i + 1} failed:`, error);
    }
  }

  if (!workerConfigured) {
    console.error('PDFRenderer: All worker options failed, PDF.js may not work properly');
  }
}

// Initialize worker configuration only once
let workerInitialized = false;
if (!workerInitialized) {
  configurePDFWorker();
  workerInitialized = true;
}

async function loadAndRender(src: string, container: HTMLDivElement, abortSignal: AbortSignal) {
  try {
    console.log('PDFRenderer: Starting to load PDF from:', src);

    // Validate inputs
    if (!src || typeof src !== 'string') {
      throw new Error('Invalid PDF source URL');
    }

    if (!container) {
      throw new Error('Container element not found');
    }

    // Check if PDF.js is available
    if (!isPDFJSSupported()) {
      throw new Error('PDF.js library not properly loaded');
    }

    // Setting up the container
    container.innerHTML = '';

    const pageViewerNode = document.createElement('div');
    pageViewerNode.classList.add('pdfViewer');
    container.appendChild(pageViewerNode);

    // Create timeout promise
    const createTimeoutPromise = (ms: number) => {
      return new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error(`PDF loading timeout after ${ms}ms`)), ms);
      });
    };

    // Configure loading task with comprehensive options
    const loadingTask = pdfjsLib.getDocument({
      url: src,
      httpHeaders: {},
      withCredentials: false,
      // Disable range requests and streaming for better compatibility
      disableRange: true,
      disableStream: true,
      // Add error handling options
      stopAtErrors: false,
      maxImageSize: 1024 * 1024, // 1MB max image size
      cMapUrl: 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/cmaps/',
      cMapPacked: true,
    });

    // Add progress tracking
    loadingTask.onProgress = (progress: any) => {
      if (progress.loaded && progress.total) {
        const percent = Math.round((progress.loaded / progress.total) * 100);
        console.log(`PDFRenderer: Loading progress: ${percent}%`);
      }
    };

    // Add password handling
    loadingTask.onPassword = (callback: (password: string) => void, reason: string) => {
      console.log('PDFRenderer: PDF requires password:', reason);
      // For now, just reject password-protected PDFs
      callback('');
    };

    console.log('PDFRenderer: Waiting for document to load...');

    // Race between loading and timeout
    const doc = await Promise.race([
      loadingTask.promise,
      createTimeoutPromise(30000) // 30 seconds timeout
    ]);

    console.log('PDFRenderer: Document loaded successfully');

    // Check if operation was aborted
    if (abortSignal.aborted) {
      console.log('PDFRenderer: Load aborted, destroying document');
      doc.destroy();
      return;
    }

    // Validate document
    if (!doc || typeof doc.numPages !== 'number' || doc.numPages <= 0) {
      throw new Error('Invalid PDF document - no pages found');
    }

    console.log(`PDFRenderer: PDF has ${doc.numPages} pages`);

    // Set up PDF viewer components
    const eventBus = new pdfjsViewer.EventBus();
    const l10n = pdfjsViewer.NullL10n;
    const linkService = new pdfjsViewer.SimpleLinkService();

    // Create viewer with error handling
    const viewer = new pdfjsViewer.PDFViewer({
      container,
      eventBus,
      l10n,
      linkService,
      // Add rendering options
      removePageBorders: true,
      textLayerMode: 1, // Enable text layer
      annotationMode: 1, // Enable annotations
    });

    // linkService.setViewer(viewer); // Not available in this version

    // Scale adjustment function
    const adjustDocumentScale = () => {
      try {
        if (viewer && viewer.currentScaleValue !== 'page-width') {
          viewer.currentScaleValue = 'page-width';
        }
      } catch (error) {
        console.warn('PDFRenderer: Failed to adjust scale:', error);
      }
    };

    // Event listeners
    const handleResize = () => {
      requestAnimationFrame(adjustDocumentScale);
    };

    const handlePagesInit = () => {
      console.log('PDFRenderer: Pages initialized');
      adjustDocumentScale();
    };

    window.addEventListener('resize', handleResize);
    eventBus.on('pagesinit', handlePagesInit);

    // Cleanup function
    const cleanup = () => {
      console.log('PDFRenderer: Cleaning up viewer');
      try {
        window.removeEventListener('resize', handleResize);
        eventBus.off('pagesinit', handlePagesInit);

        if (viewer) {
          viewer.cleanup();
        }

        if (doc) {
          doc.destroy();
        }
      } catch (error) {
        console.warn('PDFRenderer: Error during cleanup:', error);
      }
    };

    // Set up abort handling
    abortSignal.addEventListener('abort', () => {
      requestIdleCallback(() => {
        cleanup();
      });
    });

    console.log('PDFRenderer: Setting document to viewer');
    viewer.setDocument(doc);

    console.log('PDFRenderer: PDF rendering completed successfully');

  } catch (error) {
    console.error('PDFRenderer: Error loading or rendering PDF:', error);

    // Determine error type and create appropriate message
    let errorMessage = 'Unknown error occurred';
    let suggestions: string[] = [];

    if (error instanceof Error) {
      errorMessage = error.message;

      if (error.message.includes('timeout')) {
        suggestions = [
          'Check your internet connection',
          'Try again later - the server might be slow',
          'The PDF file might be very large'
        ];
      } else if (error.message.includes('fetch')) {
        suggestions = [
          'Check your internet connection',
          'Verify the PDF file exists and is accessible',
          'The PDF URL might be incorrect or expired'
        ];
      } else if (error.message.includes('password')) {
        suggestions = [
          'This PDF is password-protected',
          'Contact the document owner for access',
          'Try opening the document directly in your browser'
        ];
      } else {
        suggestions = [
          'Try refreshing the page',
          'Check if the PDF file is corrupted',
          'Try opening the document in your browser using the link below'
        ];
      }
    }

    // Show detailed error message in container
    container.innerHTML = `
      <div style="
        padding: 20px; 
        text-align: center; 
        color: #ff6b6b; 
        font-family: Arial, sans-serif;
        max-width: 500px;
        margin: 0 auto;
      ">
        <div style="font-size: 48px; margin-bottom: 16px;">📄</div>
        <h3 style="margin: 0 0 16px 0; color: #333;">Cannot load PDF document</h3>
        <p style="margin: 0 0 16px 0; color: #666;">
          <strong>Error:</strong> ${errorMessage}
        </p>
        <div style="text-align: left; margin: 16px 0;">
          <p style="margin: 0 0 8px 0; font-weight: bold; color: #333;">Possible solutions:</p>
          <ul style="margin: 0; padding-left: 20px; color: #666;">
            ${suggestions.map(suggestion => `<li style="margin-bottom: 4px;">${suggestion}</li>`).join('')}
          </ul>
        </div>
      </div>
    `;

    // Re-throw the error so the component can handle it
    throw error;
  }
}

export interface PDFRendererProps {
  src: string;
}

export function PDFRenderer(props: PDFRendererProps) {
  const { src } = props;

  const ref = React.useRef<HTMLDivElement>(null);
  const [error, setError] = React.useState<Error | null>(null);
  const [isLoading, setIsLoading] = React.useState(true);
  const [useIframeFallback, setUseIframeFallback] = React.useState(false);

  // Validate props
  React.useEffect(() => {
    if (!src || typeof src !== 'string') {
      setError(new Error('Invalid PDF source URL provided'));
      setIsLoading(false);
      return;
    }
  }, [src]);

  // Main PDF loading effect
  React.useEffect(() => {
    if (!ref.current || !src || error) {
      return;
    }

    // Check PDF.js support first
    if (!isPDFJSSupported()) {
      console.warn('PDFRenderer: PDF.js not supported, falling back to iframe');
      setUseIframeFallback(true);
      setIsLoading(false);
      return;
    }

    const abortController = new AbortController();
    setError(null);
    setIsLoading(true);
    setUseIframeFallback(false);

    loadAndRender(src, ref.current, abortController.signal)
      .then(() => {
        console.log('PDFRenderer: Successfully loaded PDF');
        setIsLoading(false);
      })
      .catch((err) => {
        if (!abortController.signal.aborted) {
          console.error('PDFRenderer: Failed to load PDF with PDF.js:', err);
          setError(err);
          setIsLoading(false);

          // Try iframe fallback after a short delay
          setTimeout(() => {
            console.log('PDFRenderer: Attempting iframe fallback');
            setUseIframeFallback(true);
          }, 1000);
        }
      });

    return () => {
      abortController.abort();
    };
  }, [src, error]);

  // If PDF.js failed, try iframe fallback
  if (useIframeFallback) {
    return (
      <div className={s.root}>
        <iframe
          src={src}
          style={{
            width: '100%',
            height: '100%',
            border: 'none',
          }}
          title="PDF Document"
          onLoad={() => {
            console.log('PDFRenderer: Iframe fallback loaded successfully');
          }}
          onError={(e) => {
            console.error('PDFRenderer: Iframe fallback also failed:', e);
          }}
        />
      </div>
    );
  }

  return (
    <div className={s.root}>
      {(isLoading || error) && <DelayedPlaceholder {...props} error={error} />}

      <div ref={ref} className={s.wrapper} />
    </div>
  );
}

interface DelayedPlaceholderProps extends PDFRendererProps {
  error?: Error | null;
}

function DelayedPlaceholder(props: DelayedPlaceholderProps) {
  const { src, error } = props;
  const shouldShow = useTimeoutFlag(500);

  if (!shouldShow) {
    return null;
  }

  return (
    <Flex fullWidth fullHeight centered vertical gap="xlarge">
      {error ? (
        <>
          <Text size="large" color="error">
            Failed to load the PDF document
          </Text>
          <Text size="small">
            Error: {error.message}
          </Text>
          <Text size="small">
            This might be due to network issues, PDF.js configuration problems, or the PDF file being corrupted.
          </Text>
        </>
      ) : (
        <>
          <Text size="large">Loading the document...</Text>
          <Text size="small">
            Please wait while we prepare your PDF
          </Text>
        </>
      )}

      <Title title={src}>
        <LinkButton
          to={src}
          icon={Icons.externalLink}
          text="Open the document in your browser"
        />
      </Title>
    </Flex>
  );
}