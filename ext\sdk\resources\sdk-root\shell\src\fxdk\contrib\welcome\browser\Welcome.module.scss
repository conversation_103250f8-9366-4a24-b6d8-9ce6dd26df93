@import "variables";

.root {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  width: 100%;
  height: 100%;

  &>* {
    width: 40vw;
  }

  .loader {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .header {
    @include fontPrimary;
    text-align: center;

    margin-bottom: $q*20;

    span {
      opacity: .75;
    }
  }

  .controls {
    display: flex;
    align-items: center;
    justify-content: space-between;

    margin-top: $q*20;

    button {
      margin: 0 $q*4;
    }
  }
}
