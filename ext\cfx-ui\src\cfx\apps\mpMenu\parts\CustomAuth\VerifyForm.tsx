import {
  Button,
  Flex,
  Input,
  Text,
  TextBlock,
  Indicator,
} from '@cfx-dev/ui-components';
import { observer } from 'mobx-react-lite';
import React, { useState } from 'react';

import { AuthStep, useCustomAuthService } from 'cfx/apps/mpMenu/services/customAuth/customAuth.service';
import { getVietnameseErrorMessage } from 'cfx/apps/mpMenu/services/customAuth/errorMessages';

import s from './CustomAuth.module.scss';

// Modern icons for visual enhancement
const VerifyIcon = () => (
  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
    <path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4z" />
  </svg>
);

const ErrorIcon = () => (
  <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
  </svg>
);

const EmailIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
    <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z" />
  </svg>
);

export const VerifyForm = observer(function VerifyForm() {
  const authService = useCustomAuthService();
  const [verificationCode, setVerificationCode] = useState('');
  const [validationError, setValidationError] = useState('');

  const validateForm = () => {
    if (!verificationCode.trim()) {
      setValidationError(getVietnameseErrorMessage('Verification code is required'));
      return false;
    }

    if (!/^\d{6}$/.test(verificationCode)) {
      setValidationError(getVietnameseErrorMessage('Verification code must be 6 digits'));
      return false;
    }

    setValidationError('');
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    authService.clearError();

    try {
      await authService.verify(authService.pendingEmail || '', verificationCode);
    } catch (error) {
      // Error is handled by the service
    }
  };

  const handleInputChange = (value: string) => {
    // Only allow digits and limit to 6 characters
    const cleanValue = value.replace(/\D/g, '').slice(0, 6);
    setVerificationCode(cleanValue);

    if (validationError) {
      setValidationError('');
    }
  };

  return (
    <>
      {/* Modern Header */}
      <div className={s.formHeader}>
        <h1 className={s.title}>Xác Thực Email</h1>
        <p className={s.subtitle}>
          Chúng tôi đã gửi mã xác thực đến địa chỉ email của bạn
        </p>
      </div>

      {/* Email Display */}
      <div style={{
        textAlign: 'center',
        marginBottom: '2rem',
        padding: '1rem',
        background: 'rgba(255, 255, 255, 0.1)',
        borderRadius: '12px',
        border: '1px solid rgba(255, 255, 255, 0.2)',
        backdropFilter: 'blur(10px)'
      }}>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          gap: '0.5rem',
          marginBottom: '0.5rem'
        }}>
          <EmailIcon />
          <span style={{ color: 'rgba(255, 255, 255, 0.7)' }}>Đã gửi đến:</span>
        </div>
        <div style={{
          color: '#667eea',
          fontWeight: '600',
          fontSize: '1.1rem'
        }}>
          {authService.pendingEmail}
        </div>
      </div>

      <form onSubmit={handleSubmit}>
        {/* Verification Code Field */}
        <div className={s.inputGroup}>
          <div className={s.inputWrapper}>
            <input
              className={`${s.modernInput} ${validationError ? s.error : ''}`}
              type="text"
              placeholder="Nhập mã 6 chữ số"
              value={verificationCode}
              onChange={(e) => handleInputChange(e.target.value)}
              disabled={authService.isLoading}
              maxLength={6}
              style={{
                textAlign: 'center',
                fontSize: '1.5rem',
                letterSpacing: '0.5rem',
                fontWeight: '600'
              }}
            />
            <div className={s.inputIcon}>
              <VerifyIcon />
            </div>
          </div>
          {validationError && (
            <div className={s.errorMessage}>
              <ErrorIcon />
              {validationError}
            </div>
          )}
        </div>

        {/* Service Error Display */}
        {authService.error && (
          <div className={s.inputGroup}>
            <div className={s.errorMessage}>
              <ErrorIcon />
              {authService.error}
            </div>
          </div>
        )}

        {/* Submit Button */}
        <button
          type="submit"
          className={s.modernButton}
          disabled={authService.isLoading || verificationCode.length !== 6}
        >
          {authService.isLoading ? (
            <div className={s.loadingIndicator}>
              <div className={s.spinner}></div>
              Đang xác thực...
            </div>
          ) : (
            'Xác Thực Email'
          )}
        </button>

        {/* Footer Link */}
        <div className={s.formFooter}>
          <p className={s.linkText}>Không nhận được mã?</p>
          <button
            type="button"
            className={`${s.modernButton} ${s.secondaryButton}`}
            onClick={() => authService.setStep(AuthStep.REGISTER)}
            disabled={authService.isLoading}
          >
            Quay Lại Đăng Ký
          </button>
        </div>
      </form>
    </>
  );
});
