[/
Copyright 2019 <PERSON>
(glen<PERSON><PERSON>@gmail.com)

Distributed under the Boost Software License, Version 1.0.
(http://www.boost.org/LICENSE_1_0.txt)
]

[section:noinit_adaptor noinit_adaptor]

[simplesect Authors]

* <PERSON>

[endsimplesect]

[section Overview]

The header <boost/core/noinit_adaptor.hpp> provides the class
template `boost::noinit_adaptor` that converts any allocator into
one whose `construct(ptr)` performs default initialization via placement new,
and whose `destroy(ptr)` invokes `value_type` destructor directly.

[endsect]

[section Examples]

The following example shows use of this allocator adaptor to achieve default
initialization of elements of a trivial type, which are later assigned values.

```
#include <boost/core/noinit_adaptor.hpp>
#include <numeric>
#include <vector>

int main()
{
    std::vector<int, boost::noinit_adaptor<std::allocator<int> > > v(5);
    std::iota(v.begin(), v.end(), 1);
}
```

The `allocate_shared_noinit` function templates are now implemented simply
using `allocate_shared` with `noinit_adaptor`.

```
template<class T, class A>
enable_if_t<is_unbounded_array_v<T>, shared_ptr<T> >
allocate_shared_noinit(const A& a, size_t n)
{
    return allocate_shared<T>(boost::noinit_adapt(a), n);
}

template<class T, class A>
enable_if_t<!is_unbounded_array_v<T>, shared_ptr<T> >
allocate_shared_noinit(const A& a)
{
    return allocate_shared<T>(boost::noinit_adapt(a));
}
```

[endsect]

[section Reference]

```
namespace boost {

template<class A>
struct noinit_adaptor
    : A {
    template<class U>
    struct rebind {
        typedef noinit_adaptor<allocator_rebind_t<A, U> > other;
    };

    noinit_adaptor() noexcept;

    template<class U>
    noinit_adaptor(U&& u) noexcept;

    template<class U>
    noinit_adaptor(const noinit_adaptor<U>& u) noexcept;

    template<class U>
    void construct(U* p);

    template<class U>
    void destroy(U* p);
};

template<class T, class U>
bool operator==(const noinit_adaptor<T>& lhs,
    const noinit_adaptor<U>& rhs) noexcept;

template<class T, class U>
bool operator!=(const noinit_adaptor<T>& lhs,
    const noinit_adaptor<U>& rhs) noexcept;

template<class A>
noinit_adaptor<A> noinit_adapt(const A& a) noexcept;

} /* boost */
```

[section Constructors]

[variablelist
[[`noinit_adaptor() noexcept;`]
[[variablelist
[[Effects][Value initializes the A base class.]]]]]
[[`template<class U> noinit_adaptor(U&& u) noexcept;`]
[[variablelist
[[Requires][`A` shall be constructible from `U`.]]
[[Effects][Initializes the `A` base class with `std::forward<U>(u)`.]]]]]
[[`template<class U> noinit_adaptor(const noinit_adaptor<U>& u) noexcept;`]
[[variablelist
[[Requires][`A` shall be constructible from `U`.]]
[[Effects][Initializes the `A` base class with
`static_cast<const A&>(u)`.]]]]]]

[endsect]

[section Member functions]

[variablelist
[[`template<class U> void construct(U* p);`]
[[variablelist
[[Effects][`::new((void*)p) U`.]]]]]
[[`template<class U> void destroy(U* p);`]
[[variablelist
[[Effects][`p->~U()`.]]]]]]

[endsect]

[section Operators]

[variablelist
[[`template<class T, class U> constexpr bool
operator==(const noinit_adaptor<T>& lhs,
const noinit_adaptor<U>& rhs) noexcept;`]
[[variablelist
[[Returns][`static_cast<const T&>(lhs) == static_cast<const U&>(rhs)`.]]]]]
[[`template<class T, class U> constexpr bool
operator!=(const noinit_adaptor<T>& lhs,
const noinit_adaptor<U>& rhs) noexcept;`]
[[variablelist
[[Returns][`!(lhs == rhs)`.]]]]]]

[endsect]

[section Free functions]

[variablelist
[[`template<class A> noinit_adaptor<A> noinit_adapt(const A& a) noexcept;`]
[[variablelist
[[Returns][`noinit_adaptor<A>(a)`.]]]]]]

[endsect]

[endsect]

[endsect]
