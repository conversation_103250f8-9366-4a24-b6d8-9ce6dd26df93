@import "~@cfx-dev/ui-components/dist/styles-scss/global.scss";
@import "themes.scss";

// Calculating mpMenu viewport size
// We want to keep it of 16:9 aspect ratio

@media (max-aspect-ratio: 16/9) {
  html {
    --width: 100vw;
    --height: calc(100vw / 1.777778);
  }
}

@media (min-aspect-ratio: 16/9) {
  html {
    --width: calc(100vh * 1.777778);
    --height: 100vh;
  }
}

html {
  // On 720p quant will be 4px
  --quant: calc(var(--height) * 0.0055);
  // Base font size, needs to be here as we then use rems to define various font sizes
  font-size: ui.q(2.5);
}

body {
  --backdrop-image: url(assets/images/05b1de299c79fd87f705632ed6386708.jpg);

  // Custom color scheme based on user preferences
  --gaming-primary: #FF8C42; // Cam chính
  --gaming-secondary: #7CB342; // Xanh lá
  --gaming-accent: #F5E6A8; // Vàng kem
  --gaming-warning: #FF8C42; // Cam cho warning
  --gaming-danger: #d9534f; // Giữ nguyên đỏ cho danger
  --gaming-dark: #2C2C2C; // Đen chính
  --gaming-darker: #1a1a1a; // Đen đậm hơn
  --gaming-surface: #2C2C2C; // Surface đen
  --gaming-surface-light: #3a3a3a; // Surface sáng hơn
  --gaming-surface-elevated: #404040; // Surface nổi
  --gaming-text: #F5E6A8; // Text vàng kem
  --gaming-text-muted: #d4c896; // Text muted vàng kem nhạt
  --gaming-text-secondary: #c4b886; // Text secondary
  --gaming-border: #4a4a4a; // Border xám
  --gaming-border-light: #5a5a5a; // Border sáng
  --gaming-glow: 0 0 15px; // Subtle Glow

  background-image: var(--backdrop-image);
  background-size: cover;
  background-position: center center;
  background-color: var(--gaming-darker);
  background-attachment: fixed;
  background-repeat: no-repeat;

  // Clean dark overlay without colored gradients
  &::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(15, 15, 35, 0.85) 0%, rgba(26, 26, 46, 0.9) 100%);
    pointer-events: none;
    z-index: -1;
  }

  @include ui.def('viewport-top', calc((100vh - ui.use('height')) * 0.5));
  @include ui.def('viewport-bottom', calc(100vh - ui.use('viewport-top')));

  @include ui.def('viewport-left', calc((100vw - ui.use('width')) * 0.5));
  @include ui.def('viewport-right', calc(100vw - ui.use('viewport-left')));
}

#backdrop-outlet {
  width: 100vw;
  height: 100vh;

  background: ui.color-token('backdrop');
}

.cfxui-blurred-backdrop #backdrop-outlet {
  backdrop-filter: blur(20px) contrast(50%);
}

// Clean animations without colored glows
@keyframes subtleGlow {

  0%,
  100% {
    text-shadow:
      0 0 8px rgba(255, 255, 255, 0.3),
      0 0 16px rgba(255, 255, 255, 0.2);
  }

  50% {
    text-shadow:
      0 0 12px rgba(255, 255, 255, 0.4),
      0 0 24px rgba(255, 255, 255, 0.3);
  }
}

@keyframes softBorderGlow {

  0%,
  100% {
    box-shadow:
      0 0 8px rgba(255, 255, 255, 0.2),
      inset 0 0 8px rgba(255, 255, 255, 0.05);
  }

  50% {
    box-shadow:
      0 0 16px rgba(255, 255, 255, 0.3),
      inset 0 0 12px rgba(255, 255, 255, 0.1);
  }
}

@keyframes gentlePulse {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.85;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInSmooth {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }

  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

#cfxui-root {
  position: fixed;

  top: ui.use('viewport-top');
  left: ui.use('viewport-left');

  display: flex;
  align-items: center;
  justify-content: center;

  width: var(--width);
  height: var(--height);

  transition: transform .2s ease, filter .2s ease;
  transform-origin: left center;

  // Remove any potential border or outline
  border: none !important;
  outline: none !important;
  box-shadow: none !important;

  &.shrink {
    transform: translateX(-10px);
  }
}

.__inline_avatar {
  width: 1.5rem;
  height: 1.5rem;

  vertical-align: middle;

  margin-right: ui.offset('small');

  &::after {
    display: inline;
    content: ' ';
  }
}

::-webkit-scrollbar {
  width: ui.offset('normal');
}

::-webkit-scrollbar-thumb {
  border-top-left-radius: ui.border-radius('xsmall');
  border-bottom-left-radius: ui.border-radius('xsmall');

  background-color: ui.color-token('scrollbar');

  &:hover {
    background-color: ui.color-token('scrollbar-hover');
  }

  &:active {
    background-color: ui.color-token('scrollbar-active');
  }
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
  user-select: none;
  outline: none !important;
  line-height: 1;
  border: none !important;
  box-shadow: none !important;
}

// Additional reset for common elements that might have borders
*,
*::before,
*::after {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

// Override any colored borders or box-shadows globally
* {
  border-image: none !important;
  border-color: transparent !important;
}

// Override specific selectors that might have colored borders
.root,
.outlet,
.selector,
.item,
.card,
.tile,
.container,
.wrapper,
.content,
.form,
.input,
.button {
  border: none !important;
  border-image: none !important;
  box-shadow: none !important;
  outline: none !important;
}

// Override hover states
*:hover,
*:focus,
*:active {
  border: none !important;
  border-image: none !important;
  box-shadow: none !important;
  outline: none !important;
}

// Force remove any potential colored borders from specific components
[class*="module"],
[class*="component"],
[class*="page"],
[class*="part"],
[class*="auth"],
[class*="modal"],
[class*="server"],
[class*="top"],
[class*="continuity"],
[class*="home"],
[class*="cfxui"],
[class*="ui-"],
div,
section,
article,
main,
aside,
header,
footer,
nav {
  border: none !important;
  border-image: none !important;
  box-shadow: none !important;
  outline: none !important;

  &::before,
  &::after {
    border: none !important;
    border-image: none !important;
    box-shadow: none !important;
    outline: none !important;
  }
}

// Override any potential ui-components borders
[class^="cfxui-"],
[class*="-cfxui-"],
[class$="-cfxui"] {
  border: none !important;
  border-image: none !important;
  box-shadow: none !important;
  outline: none !important;
}

// Nuclear option - remove ALL borders from EVERYTHING
html *,
html *::before,
html *::after,
body *,
body *::before,
body *::after,
#cfxui-root *,
#cfxui-root *::before,
#cfxui-root *::after {
  border: none !important;
  border-image: none !important;
  border-color: transparent !important;
  border-width: 0 !important;
  border-style: none !important;
  box-shadow: none !important;
  outline: none !important;
  text-shadow: none !important;
}

// Specifically target auth components
[class*="CustomAuth"],
[class*="formContent"],
[class*="auth"],
[class*="Auth"] {
  border: none !important;
  border-image: none !important;
  box-shadow: none !important;
  outline: none !important;

  * {
    border: none !important;
    border-image: none !important;
    box-shadow: none !important;
    outline: none !important;
  }
}

// ULTIMATE BORDER KILLER - This will remove ALL borders from EVERYTHING
* {
  border: 0 !important;
  border-width: 0 !important;
  border-style: none !important;
  border-color: transparent !important;
  border-image: none !important;
  border-image-source: none !important;
  border-image-slice: 0 !important;
  border-image-width: 0 !important;
  border-image-outset: 0 !important;
  border-image-repeat: stretch !important;
  box-shadow: none !important;
  outline: none !important;
  outline-width: 0 !important;
  outline-style: none !important;
  outline-color: transparent !important;
  outline-offset: 0 !important;
}

*::before,
*::after {
  border: 0 !important;
  border-width: 0 !important;
  border-style: none !important;
  border-color: transparent !important;
  border-image: none !important;
  box-shadow: none !important;
  outline: none !important;
}

a {
  color: inherit;
  text-decoration: underline;
  text-decoration-color: currentColor;

  cursor: pointer;

  @include ui.animated();

  &.unstyle {
    text-decoration: none;
  }

  &:hover {
    text-decoration-color: ui.color-token('link-hover-decoration');
  }
}

button {
  font-size: var(--font-size);
}

html,
body {
  width: 100vw;
  height: 100vh;

  overflow: hidden;

  // Ensure no borders or outlines
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  color: ui.color-token('text');
  @include ui.font-family('secondary');
  @include ui.font-weight('bold');
}

h1 {
  font-size: 2rem;
}

h2 {
  font-size: 1.5rem;
}

h3 {
  font-size: 1.25rem;
}

h4 {
  font-size: 1rem;
}

h5 {
  font-size: 0.8rem;
}

h6 {
  font-size: 0.7rem;
}

kbd {
  font-weight: 300;
  padding: 2px ui.q(.5);
  background-color: ui.color-token('kbd-background');
  border-radius: 2px;
  border: none !important;
}

p {
  padding: ui.offset('small') 0;

  line-height: 1.4;
}

li::marker {
  color: ui.color-token('li-marker');
}

.brand-icon {
  width: 1em;
  height: 1em;

  fill: currentColor;
  stroke: currentColor;

  &.mirror {
    transform: rotateY(180deg);
  }
}