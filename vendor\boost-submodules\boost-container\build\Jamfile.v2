# (C) Copyright <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>,
# <PERSON> 2007-2008
# Use, modification and distribution are subject to the
# Boost Software License, Version 1.0. (See accompanying file
# LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

import sequence ;

project boost/container
    : source-location ../src
    : usage-requirements  # pass these requirement to dependents (i.e. users)
      <link>shared:<define>BOOST_CONTAINER_DYN_LINK=1
      <link>static:<define>BOOST_CONTAINER_STATIC_LINK=1
    ;

lib boost_container
   : alloc_lib.c [ sequence.insertion-sort [ glob *.cpp ] ]
   : <link>shared:<define>BOOST_CONTAINER_DYN_LINK=1
     <link>static:<define>BOOST_CONTAINER_STATIC_LINK=1
   ;

boost-install boost_container ;