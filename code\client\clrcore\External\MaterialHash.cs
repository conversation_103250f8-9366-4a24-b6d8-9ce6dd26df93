#if MONO_V2
namespace CitizenFX.FiveM
#else
namespace CitizenFX.Core
#endif
{
	public enum MaterialHash : uint
	{
		None = 0x0,
		Unk = 0x962C3F7B,
		Concrete = 0x46CA81E8,
		ConcretePothole = 0x1567BF52,
		ConcreteDusty = 0xBF59B491,
		Tarmac = 0x10DD5498,
		TarmacPainted = 0xB26EEFB0,
		TarmacPothole = 0x70726A55,
		RumbleStrip = 0xF116BC2D,
		BreezeBlock = 0xC72165D6,
		Rock = 0xCDEB5023,
		RockMossy = 0xF8902AC8,
		Stone = 0x2D9C1E0D,
		Cobblestone = 0x2257A573,
		Brick = 0x61B1F936,
		Marble = 0x73EF7697,
		PavingSlab = 0x71AB3FEE,
		SandstoneSolid = 0x23500534,
		SandstoneBrittle = 0x7209440E,
		SandLoose = 0xA0EBF7E4,
		SandCompact = 0x1E6D775E,
		SandWet = 0x363CBCD5,
		SandTrack = 0x8E4D8AFF,
		SandUnderwater = 0xBC4922A4,
		SandDryDeep = 0x1E5E7A48,
		SandWetDeep = 0x4CCC2AFF,
		Ice = 0xD125AA55,
		IceTarmac = 0x8CE6E7D9,
		SnowLoose = 0x8C8308CA,
		SnowCompact = 0xCBA23987,
		SnowDeep = 0x608ABC80,
		SnowTarmac = 0x5C67C62A,
		GravelSmall = 0x38BBD00C,
		GravelLarge = 0x7EDC5571,
		GravelDeep = 0xEABD174E,
		GravelTrainTrack = 0x72C668B6,
		DirtTrack = 0x8F9CD58F,
		MudHard = 0x8C31B7EA,
		MudPothole = 0x129ECA2A,
		MudSoft = 0x61826E7A,
		MudUnderwater = 0xEFB2DF09,
		MudDeep = 0x42251DC0,
		Marsh = 0xD4C07E2,
		MarshDeep = 0x5E73A22E,
		Soil = 0xD63CCDDB,
		ClayHard = 0x4434DFE7,
		ClaySoft = 0x216FF3F0,
		GrassLong = 0xE47A3E41,
		Grass = 0x4F747B87,
		GrassShort = 0xB34E900D,
		Hay = 0x92B69883,
		Bushes = 0x22AD7B72,
		Twigs = 0xC98F5B61,
		Leaves = 0x8653C6CD,
		Woodchips = 0xED932E53,
		TreeBark = 0x8DD4EBB9,
		MetalSolidSmall = 0xA9BC4217,
		MetalSolidMedium = 0xEA34E8F8,
		MetalSolidLarge = 0x2CD49BD1,
		MetalHollowSmall = 0xF3B93B,
		MetalHollowMedium = 0x6E3DBFB8,
		MetalHollowLarge = 0xDD3CDCF9,
		MetalChainlinkSmall = 0x2D6E26CD,
		MetalChainlinkLarge = 0x781FA34,
		MetalCorrugatedIron = 0x31B80AD6,
		MetalGrille = 0xE699F485,
		MetalRailing = 0x7D368D93,
		MetalDuct = 0x68FEB9FD,
		MetalGarageDoor = 0xF2373DE9,
		MetalManhole = 0xD2FFA63D,
		WoodSolidSmall = 0xE82A6F1C,
		WoodSolidMedium = 0x2114B37D,
		WoodSolidLarge = 0x309F8BB7,
		WoodSolidPolished = 0x789C7AB,
		WoodFloorDusty = 0xD35443DE,
		WoodHollowSmall = 0x76D9AC2F,
		WoodHollowMedium = 0xEA3746BD,
		WoodHollowLarge = 0xC8D738E7,
		WoodChipboard = 0x461D0E9B,
		WoodOldCreaky = 0x2B13503D,
		WoodHighDensity = 0x981E5200,
		WoodLattice = 0x77E08A22,
		Ceramic = 0xB94A2EB5,
		RoofTile = 0x689E0E75,
		RoofFelt = 0xAB87C845,
		Fibreglass = 0x50B728DB,
		Tarpaulin = 0xD9B1CDE0,
		Plastic = 0x846BC4FF,
		PlasticHollow = 0x25612338,
		PlasticHighDensity = 0x9F154729,
		PlasticClear = 0x9126E8CB,
		PlasticHollowClear = 0x2E0ECF63,
		PlasticHighDensityClear = 0xB038852E,
		FibreglassHollow = 0xD256ED46,
		Rubber = 0xF7503F13,
		RubberHollow = 0xD1461B30,
		Linoleum = 0x11436942,
		Laminate = 0x6E02C9AA,
		CarpetSolid = 0x27E49616,
		CarpetSolidDusty = 0x973AE44,
		CarpetFloorboard = 0xACC354B1,
		Cloth = 0x7519E5D,
		PlasterSolid = 0xDDC7963F,
		PlasterBrittle = 0xF0FC7AFE,
		CardboardSheet = 0xE18DFF5,
		CardboardBox = 0xAC038918,
		Paper = 0x1C42F3BC,
		Foam = 0x30341454,
		FeatherPillow = 0x4FFB413F,
		Polystyrene = 0x97476A9D,
		Leather = 0xDDFF4E0C,
		Tvscreen = 0x553BE97C,
		SlattedBlinds = 0x2827CBD9,
		GlassShootThrough = 0x37E12A0B,
		GlassBulletproof = 0xE931A0E,
		GlassOpaque = 0x596C55D1,
		Perspex = 0x9F73E76C,
		CarMetal = 0xFA73FCA1,
		CarPlastic = 0x7F630AE2,
		CarSofttop = 0xC59BC28A,
		CarSofttopClear = 0x7EFDF110,
		CarGlassWeak = 0x4A57FFCA,
		CarGlassMedium = 0x23EF48BC,
		CarGlassStrong = 0x3FD6150A,
		CarGlassBulletproof = 0x995DA5E6,
		CarGlassOpaque = 0x1E94B2B7,
		Water = 0x19F81600,
		Blood = 0x4FE54A,
		Oil = 0xDA2E9567,
		Petrol = 0x9E98536C,
		FreshMeat = 0x33C7D38F,
		DriedMeat = 0xA9DC9A13,
		EmissiveGlass = 0x5978A2ED,
		EmissivePlastic = 0x3F28ABAC,
		VfxMetalElectrified = 0xED92FC47,
		VfxMetalWaterTower = 0x2473B1BF,
		VfxMetalSteam = 0xD6CBF212,
		VfxMetalFlame = 0x13D5CB0D,
		PhysNoFriction = 0x63545F03,
		PhysGolfBall = 0x9B0A74CA,
		PhysTennisBall = 0xF0B2FF05,
		PhysCaster = 0xF1F990E5,
		PhysCasterRusty = 0x7830C8F1,
		PhysCarVoid = 0x50384F9D,
		PhysPedCapsule = 0xEE9E1045,
		PhysElectricFence = 0xBA428CAB,
		PhysElectricMetal = 0x87F87187,
		PhysBarbedWire = 0xA402C0C0,
		PhysPooltableSurface = 0x241B6C19,
		PhysPooltableCushion = 0x39FDE2BB,
		PhysPooltableBall = 0xD36536C6,
		Buttocks = 0x1CD01A28,
		ThighLeft = 0xE48CC7C1,
		ShinLeft = 0x26E885F4,
		FootLeft = 0x72D0C8E7,
		ThighRight = 0xF1DFF3F9,
		ShinRight = 0xE56A0745,
		FootRight = 0xAE64A1D4,
		Spine0 = 0x8D6C3ADC,
		Spine1 = 0xBC0B421B,
		Spine2 = 0x56E0CA1D,
		Spine3 = 0x1F3C404,
		ClavicleLeft = 0xA8676EAF,
		UpperArmLeft = 0xE194CB2A,
		LowerArmLeft = 0x3E4A6464,
		HandLeft = 0x6BDCCA1,
		ClavicleRight = 0xA32DA7DA,
		UpperArmRight = 0x5979C903,
		LowerArmRight = 0x69F8EE36,
		HandRight = 0x774441B4,
		Neck = 0x666B1694,
		Head = 0xD42ACC0F,
		AnimalDefault = 0x110F7216,
		CarEngine = 0x8DBDD298,
		Puddle = 0x3B982E13,
		ConcretePavement = 0x78239B1A,
		BrickPavement = 0xBB9CA6D8,
		PhysDynamicCoverBound = 0x85F61AC9,
		VfxWoodBeerBarrel = 0x3B7F59CE,
		WoodHighFriction = 0x8070DCF9,
		RockNoinst = 0x79E4953,
		BushesNoinst = 0x55E5AAEE,
		MetalSolidRoadSurface = 0xD48AA0F2,
		StuntRampSurface = 0x8388FA6C,
		Temp01 = 0x2C848051,
		Temp02 = 0x8A1A9241,
		Temp03 = 0x71E96559,
		Temp04 = 0x72ADD5E0,
		Temp05 = 0xACEE6610,
		Temp06 = 0x3F4163F1,
		Temp07 = 0x96C43F1E,
		Temp08 = 0x5016ECD6,
		Temp09 = 0x3D285B19,
		Temp10 = 0x3C5F90A,
		Temp11 = 0x2D45692,
		Temp12 = 0x29E0C642,
		Temp13 = 0x9E65F2A7,
		Temp14 = 0xD97F800A,
		Temp15 = 0xA1961C15,
		Temp16 = 0xA5D57DD7,
		Temp17 = 0x3C514932,
		Temp18 = 0x50C38DF2,
		Temp19 = 0xD0356F62,
		Temp20 = 0x85A387EB,
		Temp21 = 0xC2251964,
		Temp22 = 0xDB059FFF,
		Temp23 = 0x1BB7608F,
		Temp24 = 0x750D8481,
		Temp25 = 0x745D8E31,
		Temp26 = 0xBD775456,
		Temp27 = 0x3500F64A,
		Temp28 = 0xB9AF9A0E,
		Temp29 = 0x40475AB5,
		Temp30 = 0xCFEBB4
	}
}
