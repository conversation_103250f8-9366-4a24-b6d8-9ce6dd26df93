// Copyright Epic Games, Inc. All Rights Reserved.

// This file is not intended to be included directly. Include eos_ui_types.h instead.

/** Number of bits to shift the modifiers into the integer. */
EOS_UI_KEY_CONSTANT(EOS_UIK_, ModifierShift, 16)
/** A mask to isolate the single key. */
EOS_UI_KEY_CONSTANT(EOS_UIK_, KeyTypeMask, (1 << EOS_UIK_ModifierShift) - 1)
/** A mask to isolate the modifier keys. */
EOS_UI_KEY_CONSTANT(EOS_UIK_, ModifierMask, ~EOS_UIK_KeyTypeMask)

/** The Shift key */
EOS_UI_KEY_MODIFIER(EOS_UIK_, Shift, (1 << EOS_UIK_ModifierShift))
/** The Control key */
EOS_UI_KEY_MODIFIER(EOS_UIK_, Control, (2 << EOS_UIK_ModifierShift))
/** The Alt key */
EOS_UI_KEY_MODIFIER(EOS_UIK_, Alt, (4 << EOS_UIK_ModifierShift))
/** The Windows key on a Windows keyboard or the Command key on a Mac keyboard */
EOS_UI_KEY_MODIFIER(EOS_UIK_, Meta, (8 << EOS_UIK_ModifierShift))
EOS_UI_KEY_CONSTANT(EOS_UIK_, ValidModifierMask, (EOS_UIK_Shift | EOS_UIK_Control | EOS_UIK_Alt | EOS_UIK_Meta))

EOS_UI_KEY_ENTRY_FIRST(EOS_UIK_, None, 0)
EOS_UI_KEY_ENTRY(EOS_UIK_, Space)
EOS_UI_KEY_ENTRY(EOS_UIK_, Backspace)
EOS_UI_KEY_ENTRY(EOS_UIK_, Tab)
EOS_UI_KEY_ENTRY(EOS_UIK_, Escape)

EOS_UI_KEY_ENTRY(EOS_UIK_, PageUp)
EOS_UI_KEY_ENTRY(EOS_UIK_, PageDown)
EOS_UI_KEY_ENTRY(EOS_UIK_, End)
EOS_UI_KEY_ENTRY(EOS_UIK_, Home)
EOS_UI_KEY_ENTRY(EOS_UIK_, Insert)
EOS_UI_KEY_ENTRY(EOS_UIK_, Delete)

EOS_UI_KEY_ENTRY(EOS_UIK_, Left)
EOS_UI_KEY_ENTRY(EOS_UIK_, Up)
EOS_UI_KEY_ENTRY(EOS_UIK_, Right)
EOS_UI_KEY_ENTRY(EOS_UIK_, Down)

EOS_UI_KEY_ENTRY(EOS_UIK_, Key0)
EOS_UI_KEY_ENTRY(EOS_UIK_, Key1)
EOS_UI_KEY_ENTRY(EOS_UIK_, Key2)
EOS_UI_KEY_ENTRY(EOS_UIK_, Key3)
EOS_UI_KEY_ENTRY(EOS_UIK_, Key4)
EOS_UI_KEY_ENTRY(EOS_UIK_, Key5)
EOS_UI_KEY_ENTRY(EOS_UIK_, Key6)
EOS_UI_KEY_ENTRY(EOS_UIK_, Key7)
EOS_UI_KEY_ENTRY(EOS_UIK_, Key8)
EOS_UI_KEY_ENTRY(EOS_UIK_, Key9)

EOS_UI_KEY_ENTRY(EOS_UIK_, KeyA)
EOS_UI_KEY_ENTRY(EOS_UIK_, KeyB)
EOS_UI_KEY_ENTRY(EOS_UIK_, KeyC)
EOS_UI_KEY_ENTRY(EOS_UIK_, KeyD)
EOS_UI_KEY_ENTRY(EOS_UIK_, KeyE)
EOS_UI_KEY_ENTRY(EOS_UIK_, KeyF)
EOS_UI_KEY_ENTRY(EOS_UIK_, KeyG)
EOS_UI_KEY_ENTRY(EOS_UIK_, KeyH)
EOS_UI_KEY_ENTRY(EOS_UIK_, KeyI)
EOS_UI_KEY_ENTRY(EOS_UIK_, KeyJ)
EOS_UI_KEY_ENTRY(EOS_UIK_, KeyK)
EOS_UI_KEY_ENTRY(EOS_UIK_, KeyL)
EOS_UI_KEY_ENTRY(EOS_UIK_, KeyM)
EOS_UI_KEY_ENTRY(EOS_UIK_, KeyN)
EOS_UI_KEY_ENTRY(EOS_UIK_, KeyO)
EOS_UI_KEY_ENTRY(EOS_UIK_, KeyP)
EOS_UI_KEY_ENTRY(EOS_UIK_, KeyQ)
EOS_UI_KEY_ENTRY(EOS_UIK_, KeyR)
EOS_UI_KEY_ENTRY(EOS_UIK_, KeyS)
EOS_UI_KEY_ENTRY(EOS_UIK_, KeyT)
EOS_UI_KEY_ENTRY(EOS_UIK_, KeyU)
EOS_UI_KEY_ENTRY(EOS_UIK_, KeyV)
EOS_UI_KEY_ENTRY(EOS_UIK_, KeyW)
EOS_UI_KEY_ENTRY(EOS_UIK_, KeyX)
EOS_UI_KEY_ENTRY(EOS_UIK_, KeyY)
EOS_UI_KEY_ENTRY(EOS_UIK_, KeyZ)

EOS_UI_KEY_ENTRY(EOS_UIK_, Numpad0)
EOS_UI_KEY_ENTRY(EOS_UIK_, Numpad1)
EOS_UI_KEY_ENTRY(EOS_UIK_, Numpad2)
EOS_UI_KEY_ENTRY(EOS_UIK_, Numpad3)
EOS_UI_KEY_ENTRY(EOS_UIK_, Numpad4)
EOS_UI_KEY_ENTRY(EOS_UIK_, Numpad5)
EOS_UI_KEY_ENTRY(EOS_UIK_, Numpad6)
EOS_UI_KEY_ENTRY(EOS_UIK_, Numpad7)
EOS_UI_KEY_ENTRY(EOS_UIK_, Numpad8)
EOS_UI_KEY_ENTRY(EOS_UIK_, Numpad9)
EOS_UI_KEY_ENTRY(EOS_UIK_, NumpadAsterisk)
EOS_UI_KEY_ENTRY(EOS_UIK_, NumpadPlus)
EOS_UI_KEY_ENTRY(EOS_UIK_, NumpadMinus)
EOS_UI_KEY_ENTRY(EOS_UIK_, NumpadPeriod)
EOS_UI_KEY_ENTRY(EOS_UIK_, NumpadDivide)

EOS_UI_KEY_ENTRY(EOS_UIK_, F1)
EOS_UI_KEY_ENTRY(EOS_UIK_, F2)
EOS_UI_KEY_ENTRY(EOS_UIK_, F3)
EOS_UI_KEY_ENTRY(EOS_UIK_, F4)
EOS_UI_KEY_ENTRY(EOS_UIK_, F5)
EOS_UI_KEY_ENTRY(EOS_UIK_, F6)
EOS_UI_KEY_ENTRY(EOS_UIK_, F7)
EOS_UI_KEY_ENTRY(EOS_UIK_, F8)
EOS_UI_KEY_ENTRY(EOS_UIK_, F9)
EOS_UI_KEY_ENTRY(EOS_UIK_, F10)
EOS_UI_KEY_ENTRY(EOS_UIK_, F11)
EOS_UI_KEY_ENTRY(EOS_UIK_, F12)
EOS_UI_KEY_ENTRY(EOS_UIK_, F13)
EOS_UI_KEY_ENTRY(EOS_UIK_, F14)
EOS_UI_KEY_ENTRY(EOS_UIK_, F15)
EOS_UI_KEY_ENTRY(EOS_UIK_, F16)
EOS_UI_KEY_ENTRY(EOS_UIK_, F17)
EOS_UI_KEY_ENTRY(EOS_UIK_, F18)
EOS_UI_KEY_ENTRY(EOS_UIK_, F19)
EOS_UI_KEY_ENTRY(EOS_UIK_, F20)
EOS_UI_KEY_ENTRY(EOS_UIK_, F21)
EOS_UI_KEY_ENTRY(EOS_UIK_, F22)
EOS_UI_KEY_ENTRY(EOS_UIK_, F23)
EOS_UI_KEY_ENTRY(EOS_UIK_, F24)

EOS_UI_KEY_ENTRY(EOS_UIK_, OemPlus)
EOS_UI_KEY_ENTRY(EOS_UIK_, OemComma)
EOS_UI_KEY_ENTRY(EOS_UIK_, OemMinus)
EOS_UI_KEY_ENTRY(EOS_UIK_, OemPeriod)
/** ';' for US layout, others vary */
EOS_UI_KEY_ENTRY(EOS_UIK_, Oem1)
/** '/' for US layout, others vary */
EOS_UI_KEY_ENTRY(EOS_UIK_, Oem2)
/** '~' for US layout, others vary */
EOS_UI_KEY_ENTRY(EOS_UIK_, Oem3)
/** '[' for US layout, others vary */
EOS_UI_KEY_ENTRY(EOS_UIK_, Oem4)
/** '\' for US layout, others vary */
EOS_UI_KEY_ENTRY(EOS_UIK_, Oem5)
/** ']' for US layout, others vary */
EOS_UI_KEY_ENTRY(EOS_UIK_, Oem6)
/** '"' for US layout, others vary */
EOS_UI_KEY_ENTRY(EOS_UIK_, Oem7)
/** varies on all layouts */
EOS_UI_KEY_ENTRY(EOS_UIK_, Oem8)

/** Maximum key enumeration value. */
EOS_UI_KEY_CONSTANT_LAST(EOS_UIK_, MaxKeyType)
