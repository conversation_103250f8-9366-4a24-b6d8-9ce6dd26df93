["devcon", "rage:allocator:five", "rage:device:five", "rage:graphics:five", "font-renderer", "legitimacy", "rage:scripting:five", "debug:net", "citizen:game:main", "net", "profiles", "steam", "gta:net:five", "http-client", "pool-sizes-state", "rage:input:five", "asi:five", "scripthookv", "gta:streaming:five", "nui:core", "lovely-script", "glue", "loading-screens:five", "gta:core:five", "rage:nutsnbolts:five", "gta:mission-cleanup:five", "nui:gsclient", "conhost:v2", "citizen:level-loader:five", "tool:formats", "rage:formats:x", "vfs:core", "vfs:impl:rage", "citizen:resources:core", "citizen:resources:metadata:lua", "citizen:resources:gta", "net:base", "net:packet", "net:http-server", "net:tcp-server", "citizen:playernames:five", "nui:resources", "nui:profiles", "citizen:scripting:core", "citizen:scripting:lua", "citizen:scripting:v8client", "citizen:scripting:v8node", "citizen:scripting:v8-v12.4", "scripting:gta", "citizen:resources:client", "citizen:legacy-net:resources", "gta:game:five", "handling-loader:five", "citizen:scripting:mono", "citizen:scripting:mono-v2", "devtools:five", "tool:vehrec", "extra-natives:five", "discord", "voip:mumble", "scrbind:base", "citizen:mod-loader:five", "adhesive", "citizen:game:ipc", "fxdk:main", "citizen:dev<PERSON><PERSON>"]