# Copyright 2018 The Chromium Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

mojom = "//ipc/ipc.mojom"
public_headers = [ "//ipc/message_view.h" ]
traits_headers = [ "//ipc/message_mojom_traits.h" ]
sources = [
  "//ipc/message_mojom_traits.cc",
  "//ipc/message_mojom_traits.h",
  "//ipc/message_view.cc",
  "//ipc/message_view.h",
]
public_deps = [
  "//ipc:message_support",
  "//mojo/public/cpp/base:shared_typemap_traits",
]
type_mappings = [ "IPC.mojom.Message=::IPC::MessageView[move_only]" ]
