
/* This is a generated file. Do not edit. See tools/locale-builder. */
#ifndef MONO_METADATA_CULTURE_INFO_TABLES
#define MONO_METADATA_CULTURE_INFO_TABLES 1


#define NUM_CULTURE_ENTRIES 281
#define NUM_REGION_ENTRIES 117


static const DateTimeFormatEntry datetime_format_entries [] = {
	{1, 0, 0, {0, 0, 0, 0, 0, 0, 0}, {0, 0, 0, 0, 0, 0, 0}, {0, 0, 0, 0, 0, 0, 0}, {9, 18, 25, 45, 65, 89, 113, 120, 131, 142, 151, 169, 0}, {9, 18, 25, 45, 65, 89, 113, 120, 131, 142, 151, 169, 0}, {9, 18, 25, 45, 65, 89, 113, 120, 131, 142, 151, 169, 0}, {9, 18, 25, 45, 65, 89, 113, 120, 131, 142, 151, 169, 0}, 0, 6, 185, 187, {189,198,0,0,0,0,0,0,0,0,0,0,0,0},{209,222,0,0,0,0,0,0,0,0},{242,251,0,0,0,0,0,0,0,0,0,0},{257,269,0,0,0,0,0,0,0},{278,0,0,0,0,0,0,0}},
	{289, 296, 307, {318, 331, 352, 367, 378, 397, 408}, {421, 426, 431, 436, 441, 446, 451}, {456, 459, 462, 465, 468, 459, 465}, {471, 484, 501, 510, 521, 528, 535, 542, 555, 574, 591, 606, 0}, {471, 484, 501, 510, 521, 528, 535, 542, 555, 574, 591, 606, 0}, {623, 629, 501, 639, 521, 528, 535, 647, 655, 665, 673, 683, 0}, {623, 629, 501, 639, 521, 528, 535, 647, 655, 665, 673, 683, 0}, 2, 1, 691, 187, {693,708,724,740,0,0,0,0,0,0,0,0,0,0},{757,776,794,819,0,0,0,0,0,0},{843,251,0,0,0,0,0,0,0,0,0,0},{848,269,0,0,0,0,0,0,0},{856,0,0,0,0,0,0,0}},
	{289, 872, 878, {884, 893, 901, 909, 918, 925, 935}, {944, 948, 952, 956, 960, 964, 968}, {972, 975, 978, 981, 984, 987, 990}, {993, 999, 1006, 1012, 1018, 1023, 1028, 1035, 1041, 1050, 1058, 1067, 0}, {993, 999, 1006, 1012, 1018, 1023, 1028, 1035, 1041, 1050, 1058, 1067, 0}, {1076, 1081, 1006, 1086, 1018, 1023, 1091, 1096, 1100, 1105, 1110, 1115, 0}, {1076, 1081, 1006, 1086, 1018, 1023, 1091, 1096, 1100, 1105, 1110, 1115, 0}, 2, 1, 185, 187, {1120,1129,0,0,0,0,0,0,0,0,0,0,0,0},{1140,1163,0,0,0,0,0,0,0,0},{843,251,0,0,0,0,0,0,0,0,0,0},{848,269,0,0,0,0,0,0,0},{1180,0,0,0,0,0,0,0}},
	{1195, 1204, 1211, {1218, 1228, 1238, 1248, 1258, 1268, 1278}, {1288, 1295, 1302, 1309, 1316, 1323, 1330}, {1337, 1341, 1345, 1349, 1353, 1357, 1361}, {1365, 1372, 1379, 1386, 1393, 1400, 1407, 1414, 1421, 1428, 1435, 1445, 0}, {1365, 1372, 1379, 1386, 1393, 1400, 1407, 1414, 1421, 1428, 1435, 1445, 0}, {1455, 1460, 1465, 1470, 1475, 1480, 1485, 1490, 1495, 1500, 1506, 1512, 0}, {1455, 1460, 1465, 1470, 1475, 1480, 1485, 1490, 1495, 1500, 1506, 1512, 0}, 0, 0, 185, 187, {1518,1527,1536,1545,1556,1567,1578,1585,1592,1599,0,0,0,0},{1608,1630,1658,1686,1701,0,0,0,0,0},{843,251,1722,1730,0,0,0,0,0,0,0,0},{848,269,1739,1750,0,0,0,0,0},{1762,1778,1791,1805,0,0,0,0}},
	{1195, 1204, 1211, {1218, 1228, 1238, 1248, 1258, 1268, 1278}, {1288, 1295, 1302, 1309, 1316, 1323, 1330}, {1337, 1341, 1345, 1349, 1353, 1357, 1361}, {1365, 1372, 1379, 1386, 1393, 1400, 1407, 1414, 1421, 1428, 1435, 1445, 0}, {1365, 1372, 1379, 1386, 1393, 1400, 1407, 1414, 1421, 1428, 1435, 1445, 0}, {1455, 1460, 1465, 1470, 1475, 1480, 1485, 1490, 1495, 1500, 1506, 1512, 0}, {1455, 1460, 1465, 1470, 1475, 1480, 1485, 1490, 1495, 1500, 1506, 1512, 0}, 0, 0, 185, 187, {1518,1527,1536,1545,1556,1567,1578,1585,1592,1599,0,0,0,0},{1608,1630,1658,1686,1701,0,0,0,0,0},{843,251,1722,1730,0,0,0,0,0,0,0,0},{848,269,1739,1750,0,0,0,0,0},{1762,1778,1791,1805,0,0,0,0}},
	{1812, 1820, 1823, {1826, 1834, 1844, 1852, 1860, 1869, 1876}, {1883, 1886, 1889, 1893, 1896, 1900, 1904}, {1907, 1909, 1911, 1914, 1916, 1909, 1914}, {1919, 1925, 1931, 1939, 1945, 1953, 1961, 1971, 1977, 1985, 1993, 2002, 0}, {2011, 2017, 2024, 2032, 2038, 2046, 2054, 2064, 1977, 2070, 2078, 2088, 0}, {2097, 2101, 2106, 2111, 2115, 2120, 2125, 2130, 2134, 2140, 2146, 2150, 0}, {2097, 2101, 2106, 2111, 2115, 2120, 2125, 2130, 2134, 2140, 2146, 2150, 0}, 2, 1, 691, 187, {2154,2165,0,0,0,0,0,0,0,0,0,0,0,0},{2176,2194,0,0,0,0,0,0,0,0},{843,0,0,0,0,0,0,0,0,0,0,0},{848,0,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{1812, 1820, 1823, {2217, 2225, 2232, 2240, 2247, 2255, 2262}, {2270, 2275, 2279, 2283, 2287, 2291, 2295}, {1914, 2300, 2302, 2304, 2302, 2306, 2308}, {2310, 2317, 2325, 2331, 2337, 2341, 2346, 2351, 2358, 2368, 2376, 2385, 0}, {2310, 2317, 2325, 2331, 2337, 2341, 2346, 2351, 2358, 2368, 2376, 2385, 0}, {2394, 2398, 2402, 2406, 2337, 2410, 2414, 2418, 2422, 2426, 2430, 2434, 0}, {2394, 2398, 2402, 2406, 2337, 2410, 2414, 2418, 2422, 2426, 2430, 2434, 0}, 2, 1, 2438, 187, {2440,2451,1556,2460,0,0,0,0,0,0,0,0,0,0},{2194,0,0,0,0,0,0,0,0,0},{251,843,0,0,0,0,0,0,0,0,0,0},{269,848,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{1812, 2471, 2477, {2484, 2492, 2499, 2508, 2517, 2528, 2536}, {2544, 2547, 2550, 2553, 2556, 2559, 2562}, {1914, 2300, 2565, 2300, 2565, 2306, 1914}, {2567, 2574, 2582, 2588, 2594, 2598, 2603, 2608, 2615, 2625, 2633, 2642, 0}, {2567, 2574, 2582, 2588, 2594, 2598, 2603, 2608, 2615, 2625, 2633, 2642, 0}, {2651, 2655, 2659, 2664, 2594, 2668, 2672, 2676, 2680, 2684, 2688, 2692, 0}, {2651, 2655, 2659, 2664, 2594, 2668, 2672, 2676, 2680, 2684, 2688, 2692, 0}, 2, 1, 691, 187, {2154,2696,1556,2705,0,0,0,0,0,0,0,0,0,0},{2719,2194,2738,0,0,0,0,0,0,0},{251,2751,0,0,0,0,0,0,0,0,0,0},{269,2763,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{289, 2778, 2785, {2792, 2807, 2822, 2833, 2848, 2861, 2880}, {2895, 2902, 2909, 2916, 2923, 2930, 2937}, {2944, 2947, 2950, 2950, 2953, 2953, 2956}, {2959, 2980, 3003, 3018, 3035, 3046, 3061, 3076, 3095, 3118, 3137, 3156, 0}, {3177, 3198, 3221, 3236, 3253, 3264, 3279, 3294, 3313, 3336, 3355, 3374, 0}, {3395, 3402, 3409, 3416, 3423, 3430, 3439, 3448, 3455, 3462, 3469, 3476, 0}, {3395, 3402, 3409, 3416, 3423, 3430, 3439, 3448, 3455, 3462, 3469, 3476, 0}, 2, 1, 185, 187, {1120,198,3483,189,3490,1556,0,0,0,0,0,0,0,0},{3502,3520,0,0,0,0,0,0,0,0},{3532,242,843,251,0,0,0,0,0,0,0,0},{3540,257,848,269,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{3551, 1820, 1823, {3558, 3565, 3572, 3580, 3590, 3599, 3606}, {3615, 3619, 3623, 3627, 3631, 3635, 3639}, {1914, 2300, 2302, 3643, 2302, 2306, 1914}, {3645, 3653, 3662, 2588, 3668, 3672, 3677, 2608, 2615, 3682, 2633, 3690, 0}, {3645, 3653, 3662, 2588, 3668, 3672, 3677, 2608, 2615, 3682, 2633, 3690, 0}, {2651, 2655, 3699, 2664, 3668, 2668, 2672, 2676, 2680, 3703, 2688, 3707, 0}, {2651, 2655, 3699, 2664, 3668, 2668, 2672, 2676, 2680, 3703, 2688, 3707, 0}, 0, 0, 185, 187, {3711,3720,3727,3736,1599,1556,3747,0,0,0,0,0,0,0},{3757,3776,3789,3808,0,0,0,0,0,0},{3532,242,843,251,0,0,0,0,0,0,0,0},{3540,257,848,269,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{3821, 872, 878, {3833, 3841, 3847, 3854, 3865, 3872, 3880}, {3888, 3893, 3898, 3903, 3909, 3914, 3919}, {2565, 2308, 2300, 2300, 3925, 3927, 1914}, {3929, 3935, 3943, 3949, 3955, 3960, 3966, 3972, 3979, 3990, 3998, 4008, 0}, {4018, 4024, 4032, 1012, 4038, 4043, 4049, 4055, 4062, 1050, 4073, 4083, 0}, {4093, 4098, 3898, 4103, 4108, 4113, 4118, 4123, 4128, 4134, 4139, 4144, 0}, {4093, 4098, 3898, 4103, 4108, 4113, 4118, 4123, 4128, 4134, 4139, 4144, 0}, 0, 1, 185, 187, {198,189,4149,3483,4157,2451,2696,1556,0,0,0,0,0,0},{4164,4192,4219,0,0,0,0,0,0,0},{843,251,4241,4246,4252,0,0,0,0,0,0,0},{848,269,4260,4268,4277,0,0,0,0},{4288,0,0,0,0,0,0,0}},
	{1812, 4303, 4307, {4311, 4323, 4335, 4345, 4359, 4369, 4381}, {4392, 4395, 4398, 4401, 4404, 4407, 4410}, {1914, 2300, 2302, 4413, 2302, 1909, 2308}, {4415, 4424, 4433, 4443, 4452, 4461, 4470, 4480, 4487, 4495, 4503, 4513, 0}, {4522, 4533, 4544, 4556, 4567, 4578, 4589, 4601, 4610, 4620, 4630, 4642, 0}, {4653, 4659, 4665, 4672, 4678, 4684, 4690, 4697, 4701, 4706, 4711, 4718, 0}, {4653, 4659, 4665, 4672, 4678, 4684, 4690, 4697, 4701, 4706, 4711, 4718, 0}, 2, 1, 691, 691, {4724,0,0,0,0,0,0,0,0,0,0,0,0,0},{2176,2194,0,0,0,0,0,0,0,0},{4241,0,0,0,0,0,0,0,0,0,0,0},{4260,0,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{289, 1820, 1823, {4733, 4742, 4748, 4754, 4763, 4769, 4778}, {4785, 4790, 4795, 4800, 4805, 4810, 4815}, {2565, 2308, 2300, 2300, 3925, 3927, 1914}, {4820, 4828, 4837, 4842, 4848, 4852, 4857, 4865, 4871, 4881, 1058, 4889, 0}, {4820, 4828, 4837, 4842, 4848, 4852, 4857, 4865, 4871, 4881, 1058, 4889, 0}, {4899, 4905, 4837, 4912, 4848, 4852, 4917, 4865, 4923, 1105, 1110, 4929, 0}, {4899, 4905, 4837, 4912, 4848, 4852, 4917, 4865, 4923, 1105, 1110, 4929, 0}, 0, 1, 185, 187, {198,189,2696,2451,1556,0,0,0,0,0,0,0,0,0},{4935,3520,4952,0,0,0,0,0,0,0},{251,843,4246,4961,4971,0,0,0,0,0,0,0},{269,848,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{1, 4979, 4992, {5003, 5021, 5035, 5053, 5071, 5089, 5105}, {5119, 5131, 5143, 5155, 5167, 5179, 5191}, {5198, 5203, 5208, 5213, 5218, 5223, 5228}, {5233, 5244, 5257, 5264, 5275, 5282, 5291, 5300, 5313, 5326, 5341, 5354, 0}, {5233, 5244, 5257, 5264, 5275, 5282, 5291, 5300, 5313, 5326, 5341, 5354, 0}, {5365, 5374, 5257, 5383, 5275, 5392, 5401, 5410, 5419, 5428, 5437, 5446, 0}, {5365, 5374, 5257, 5383, 5275, 5392, 5401, 5410, 5419, 5428, 5437, 5446, 0}, 0, 0, 185, 187, {198,5455,189,209,2451,2440,5468,1556,5481,5498,0,0,0,0},{5508,5455,5526,5548,5481,0,0,0,0,0},{251,242,0,0,0,0,0,0,0,0,0,0},{269,257,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{5569, 5577, 5581, {5585, 5595, 5603, 5608, 5615, 5628, 5636}, {3927, 5644, 4413, 5646, 5650, 1909, 5653}, {3927, 5644, 4413, 5657, 5650, 1909, 5657}, {5660, 5668, 5677, 5686, 5695, 5702, 5710, 5718, 5728, 5739, 2376, 2385, 0}, {5660, 5668, 5677, 5686, 5695, 5702, 5710, 5718, 5728, 5739, 2376, 2385, 0}, {5748, 5753, 5759, 5766, 5772, 5778, 5784, 5790, 5795, 5802, 1110, 5807, 0}, {5748, 5753, 5759, 5766, 5772, 5778, 5784, 5790, 5795, 5802, 1110, 5807, 0}, 2, 1, 5812, 187, {5815,5829,0,0,0,0,0,0,0,0,0,0,0,0},{5842,5862,0,0,0,0,0,0,0,0},{843,0,0,0,0,0,0,0,0,0,0,0},{848,0,0,0,0,0,0,0,0},{5876,0,0,0,0,0,0,0}},
	{1812, 5887, 5892, {5897, 5908, 5919, 5933, 5947, 5959, 5971}, {5983, 5988, 5994, 6000, 6006, 6011, 6017}, {1914, 2300, 6022, 2300, 2306, 2306, 2308}, {6025, 6033, 4837, 6042, 6049, 6054, 6061, 6068, 2358, 5739, 6076, 6086, 0}, {6025, 6033, 4837, 6042, 6049, 6054, 6061, 6068, 2358, 5739, 6076, 6086, 0}, {5748, 1081, 4795, 6095, 6049, 5778, 5784, 6100, 6107, 5802, 6112, 1115, 0}, {5748, 1081, 4795, 6095, 6049, 5778, 5784, 6100, 6107, 5802, 6112, 1115, 0}, 2, 1, 691, 187, {4724,6118,0,0,0,0,0,0,0,0,0,0,0,0},{2719,2194,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{289, 1820, 1823, {6130, 6139, 6147, 6156, 6167, 6176, 6185}, {6192, 6196, 2402, 6200, 6204, 6208, 6212}, {2565, 2308, 2300, 2300, 6216, 3927, 1914}, {6218, 6226, 3943, 6235, 6242, 6249, 6256, 3972, 6263, 6273, 6281, 6290, 0}, {6299, 6307, 4032, 6316, 6323, 6330, 6337, 4055, 6344, 6354, 1058, 6362, 0}, {6371, 2398, 2402, 2406, 6375, 6379, 6383, 6387, 6391, 6395, 2430, 6399, 0}, {6371, 2398, 2402, 2406, 6375, 6379, 6383, 6387, 6391, 6395, 2430, 6399, 0}, 2, 1, 185, 187, {198,3490,189,6403,3483,0,0,0,0,0,0,0,0,0},{4935,6411,3520,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{1195, 6420, 6427, {6434, 6444, 6454, 6464, 6474, 6484, 6494}, {1337, 6504, 6508, 6512, 6516, 6520, 6524}, {1337, 6504, 6508, 6512, 6516, 6520, 6524}, {1455, 1460, 1465, 1470, 1475, 1480, 1485, 1490, 1495, 1500, 1506, 1512, 0}, {1455, 1460, 1465, 1470, 1475, 1480, 1485, 1490, 1495, 1500, 1506, 1512, 0}, {6528, 6530, 6532, 6534, 6536, 6538, 6540, 6542, 6544, 6546, 6549, 6552, 0}, {6528, 6530, 6532, 6534, 6536, 6538, 6540, 6542, 6544, 6546, 6549, 6552, 0}, 0, 0, 185, 187, {1545,1599,1578,1518,1556,0,0,0,0,0,0,0,0,0},{1608,6555,6579,6606,6635,6659,6688,6708,0,0},{843,251,1722,1730,0,0,0,0,0,0,0,0},{848,269,1739,1750,0,0,0,0,0},{1762,6733,1791,0,0,0,0,0}},
	{6751, 6761, 6768, {6775, 6785, 6795, 6805, 6815, 6825, 6835}, {6845, 6849, 6853, 6857, 6861, 6865, 6869}, {6845, 6849, 6853, 6857, 6861, 6865, 6869}, {6873, 6878, 6883, 6888, 6893, 6898, 6903, 6908, 6913, 6918, 6924, 6930, 0}, {6873, 6878, 6883, 6888, 6893, 6898, 6903, 6908, 6913, 6918, 6924, 6930, 0}, {6873, 6878, 6883, 6888, 6893, 6898, 6903, 6908, 6913, 6918, 6924, 6930, 0}, {6873, 6878, 6883, 6888, 6893, 6898, 6903, 6908, 6913, 6918, 6924, 6930, 0}, 0, 0, 2438, 187, {1556,6936,1585,1527,0,0,0,0,0,0,0,0,0,0},{6945,6974,6998,7025,7047,7078,7104,7135,7161,7188},{1722,1730,843,251,0,0,0,0,0,0,0,0},{1739,1750,848,269,0,0,0,0,0},{7210,7227,7246,0,0,0,0,0}},
	{289, 7261, 7266, {7271, 7278, 7286, 7294, 7303, 7313, 7321}, {7330, 4395, 7333, 7336, 7339, 7342, 7345}, {7348, 2300, 2565, 3643, 2565, 3927, 7348}, {7350, 7358, 7367, 2331, 7373, 2341, 2346, 7377, 2358, 2368, 2376, 2385, 0}, {7350, 7358, 7367, 2331, 7373, 2341, 2346, 7377, 2358, 2368, 2376, 2385, 0}, {2394, 2398, 7386, 2406, 7373, 2410, 2414, 2418, 2422, 2426, 2430, 2434, 0}, {2394, 2398, 7386, 2406, 7373, 2410, 2414, 2418, 2422, 2426, 2430, 2434, 0}, 2, 1, 2438, 187, {7390,4157,2451,189,2696,7399,1556,0,0,0,0,0,0,0},{4935,6411,3520,4952,0,0,0,0,0,0},{251,843,4241,7411,7423,0,0,0,0,0,0,0},{269,848,7435,7450,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{7465, 7261, 7266, {2217, 2225, 2232, 2240, 2247, 2255, 2262}, {7473, 7478, 7482, 7486, 7490, 7494, 7498}, {1914, 2300, 2302, 2304, 2302, 2306, 2308}, {2310, 2317, 4837, 2331, 4848, 2341, 2346, 2351, 2358, 2368, 2376, 6086, 0}, {2310, 2317, 4837, 2331, 4848, 2341, 2346, 2351, 2358, 2368, 2376, 6086, 0}, {2394, 2398, 2402, 2406, 4848, 2410, 2414, 2418, 2422, 2426, 2430, 7503, 0}, {2394, 2398, 2402, 2406, 4848, 2410, 2414, 2418, 2422, 2426, 2430, 7503, 0}, 0, 0, 691, 691, {2154,6118,0,0,0,0,0,0,0,0,0,0,0,0},{2176,2194,0,0,0,0,0,0,0,0},{4246,0,0,0,0,0,0,0,0,0,0,0},{4268,0,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{289, 1820, 1823, {7507, 7517, 7531, 7538, 7545, 7554, 1876}, {7562, 7569, 7574, 7578, 7583, 7588, 7592}, {1907, 1909, 3643, 7597, 7600, 1909, 1914}, {7602, 7611, 7616, 7623, 2337, 7633, 7642, 7649, 7659, 7669, 1993, 7682, 0}, {7692, 7701, 7708, 7714, 7723, 7728, 7736, 7742, 7751, 7761, 7775, 7785, 0}, {7793, 7797, 2402, 7801, 2337, 7805, 7809, 7813, 7817, 7821, 2146, 7826, 0}, {7793, 7797, 2402, 7801, 2337, 7805, 7809, 7813, 7817, 7821, 2146, 7826, 0}, 2, 1, 691, 187, {2154,0,0,0,0,0,0,0,0,0,0,0,0,0},{3502,3520,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{3821, 1820, 1823, {3833, 7830, 7844, 7857, 7870, 7883, 3880}, {6192, 7895, 7899, 7903, 7907, 7911, 7915}, {2565, 1914, 2302, 7920, 7920, 1914, 1914}, {7922, 7930, 7940, 1012, 7947, 7952, 7958, 4055, 7964, 7973, 7981, 7990, 0}, {7922, 7930, 7940, 1012, 7947, 7952, 7958, 4055, 7964, 7973, 7981, 7990, 0}, {2394, 7999, 2402, 8003, 4848, 2410, 2414, 6387, 6391, 8007, 2430, 8011, 0}, {2394, 7999, 2402, 8003, 4848, 2410, 2414, 6387, 6391, 8007, 2430, 8011, 0}, 0, 0, 185, 187, {198,189,1120,3483,2451,2440,4157,7390,2696,2154,7399,8015,4724,1556},{4164,4219,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{4288,0,0,0,0,0,0,0}},
	{1812, 8022, 8025, {8028, 8037, 4748, 8047, 8055, 8063, 8072}, {8078, 8081, 4395, 8085, 8088, 8092, 1904}, {2565, 6216, 2300, 2300, 6216, 3927, 1914}, {8095, 8103, 4837, 8110, 8117, 8122, 8132, 8140, 8146, 8156, 2376, 2385, 0}, {8095, 8103, 4837, 8110, 8117, 8122, 8132, 8140, 8146, 8156, 2376, 2385, 0}, {8164, 8171, 4837, 4912, 8117, 8177, 8184, 8140, 8189, 1105, 1110, 5807, 0}, {8164, 8171, 4837, 4912, 8117, 8177, 8184, 8140, 8189, 1105, 1110, 5807, 0}, 2, 1, 2438, 187, {2440,0,0,0,0,0,0,0,0,0,0,0,0,0},{8195,8224,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{289, 7261, 7266, {8241, 8251, 8256, 8263, 8272, 8276, 8283}, {8294, 8298, 3699, 8302, 8306, 8310, 8314}, {2565, 2308, 2300, 2300, 3925, 3927, 1914}, {8319, 8328, 8338, 8345, 4848, 8353, 8359, 2351, 8365, 8376, 8386, 8396, 0}, {8319, 8328, 8338, 8345, 4848, 8353, 8359, 2351, 8365, 8376, 8386, 8396, 0}, {8406, 1081, 4795, 6095, 4848, 8411, 8416, 5790, 4923, 1105, 1110, 5807, 0}, {8406, 1081, 4795, 6095, 4848, 8411, 8416, 5790, 4923, 1105, 1110, 5807, 0}, 0, 1, 691, 187, {2154,1129,0,0,0,0,0,0,0,0,0,0,0,0},{3502,3520,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{289, 8421, 8441, {8467, 8490, 352, 8513, 8524, 8539, 8554}, {8569, 8574, 8579, 8584, 8589, 8594, 8599}, {8604, 8607, 8604, 8610, 8613, 8607, 8610}, {8616, 8629, 8644, 8653, 8666, 8673, 8682, 8691, 8704, 8721, 8736, 8749, 0}, {8764, 8777, 8792, 8803, 8816, 8823, 8832, 8841, 8856, 8873, 8888, 8901, 0}, {8916, 8924, 8644, 8934, 8666, 8673, 8682, 8942, 8950, 8960, 8968, 8978, 0}, {8916, 8924, 8644, 8934, 8666, 8673, 8682, 8942, 8950, 8960, 8968, 8978, 0}, 0, 1, 691, 187, {2154,2696,8015,2440,189,0,0,0,0,0,0,0,0,0},{776,757,0,0,0,0,0,0,0,0},{843,251,0,0,0,0,0,0,0,0,0,0},{848,269,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{1812, 1820, 1823, {8986, 8995, 9007, 9014, 9022, 9032, 9038}, {9045, 9049, 9053, 9057, 9061, 9066, 9070}, {9074, 9076, 9078, 9080, 9082, 9076, 9080}, {9085, 9095, 9104, 9112, 9120, 9128, 9135, 9142, 9150, 1993, 9156, 9164, 0}, {9173, 9183, 9192, 9200, 9208, 9216, 9223, 9230, 9239, 7775, 9245, 9255, 0}, {9264, 9268, 9273, 9278, 9282, 7809, 2130, 9286, 9290, 2146, 9294, 2150, 0}, {9264, 9268, 9273, 9278, 9282, 7809, 2130, 9286, 9290, 2146, 9294, 2150, 0}, 0, 1, 691, 187, {9298,9308,9316,9328,9340,9350,9360,1556,0,0,0,0,0,0},{9372,9386,9401,0,0,0,0,0,0,0},{843,251,0,0,0,0,0,0,0,0,0,0},{848,269,0,0,0,0,0,0,0},{278,0,0,0,0,0,0,0}},
	{1812, 1820, 1823, {9421, 9429, 9438, 9445, 9452, 9461, 1876}, {1883, 1886, 9468, 1893, 9471, 9475, 1904}, {1907, 1909, 9478, 1914, 9480, 1909, 1914}, {5660, 5668, 9483, 6042, 9489, 9494, 9499, 2351, 2358, 5739, 2376, 2385, 0}, {9504, 9513, 7708, 9523, 9531, 9537, 9543, 9549, 9557, 9567, 9576, 9585, 0}, {2394, 2398, 2402, 2406, 9489, 9494, 9499, 2418, 2422, 2426, 2430, 2434, 0}, {2394, 2398, 2402, 2406, 9489, 9494, 9499, 2418, 2422, 2426, 2430, 2434, 0}, 2, 1, 691, 187, {2154,2165,0,0,0,0,0,0,0,0,0,0,0,0},{2719,2194,0,0,0,0,0,0,0,0},{843,0,0,0,0,0,0,0,0,0,0,0},{848,0,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{289, 9594, 9603, {9611, 9618, 9627, 9636, 9648, 9656, 9665}, {9675, 9679, 3699, 9684, 9689, 9693, 9697}, {2565, 5644, 2300, 2300, 9701, 1909, 1914}, {9703, 9709, 4837, 9716, 2337, 9722, 9730, 9737, 9743, 9751, 9757, 9765, 0}, {9703, 9709, 4837, 9716, 2337, 9722, 9730, 9737, 9743, 9751, 9757, 9765, 0}, {2651, 9773, 3699, 9777, 9781, 9785, 9789, 9793, 9697, 9797, 9801, 9806, 0}, {2651, 9773, 3699, 9777, 9781, 9785, 9789, 9793, 9697, 9797, 9801, 9806, 0}, 0, 1, 691, 187, {4724,1129,0,0,0,0,0,0,0,0,0,0,0,0},{3502,3520,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{9810, 9823, 9826, {9829, 9837, 9845, 2240, 2247, 2255, 9852}, {9860, 9865, 9870, 2283, 2287, 2291, 9874}, {1914, 2300, 2302, 2304, 2302, 2306, 2308}, {7350, 7358, 4837, 2331, 2337, 2341, 2346, 9879, 2358, 2368, 2376, 2385, 0}, {7350, 7358, 4837, 2331, 2337, 2341, 2346, 9879, 2358, 2368, 2376, 2385, 0}, {2394, 2398, 2402, 2406, 2337, 2410, 2414, 2418, 2422, 2426, 2430, 2434, 0}, {2394, 2398, 2402, 2406, 2337, 2410, 2414, 2418, 2422, 2426, 2430, 2434, 0}, 2, 1, 2438, 187, {1556,6936,0,0,0,0,0,0,0,0,0,0,0,0},{9887,9905,0,0,0,0,0,0,0,0},{251,843,9928,0,0,0,0,0,0,0,0,0},{269,848,9938,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{289, 0, 0, {0, 0, 0, 0, 0, 0, 0}, {0, 0, 0, 0, 0, 0, 0}, {0, 0, 0, 0, 0, 0, 0}, {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0}, {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0}, {6528, 6530, 6532, 6534, 6536, 6538, 6540, 6542, 6544, 6546, 6549, 6552, 0}, {6528, 6530, 6532, 6534, 6536, 6538, 6540, 6542, 6544, 6546, 6549, 6552, 0}, 0, 0, 185, 187, {1120,3483,189,198,1129,9951,1556,0,0,0,0,0,0,0},{3520,9963,9979,0,0,0,0,0,0,0},{843,251,3532,242,0,0,0,0,0,0,0,0},{848,269,3540,257,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{1, 10021, 10026, {10030, 10036, 10046, 10052, 10063, 10073, 10078}, {10088, 10092, 10096, 10100, 10105, 10109, 10113}, {1909, 1909, 1914, 10117, 1909, 7600, 7600}, {10120, 10125, 10132, 10137, 10143, 10150, 10158, 10165, 10174, 10181, 10186, 10193, 0}, {10120, 10125, 10132, 10137, 10143, 10150, 10158, 10165, 10174, 10181, 10186, 10193, 0}, {10201, 10205, 3699, 10210, 3668, 10214, 10218, 10222, 10227, 10231, 10235, 10239, 0}, {10201, 10205, 3699, 10210, 3668, 10214, 10218, 10222, 10227, 10231, 10235, 10239, 0}, 0, 1, 691, 187, {10243,1129,0,0,0,0,0,0,0,0,0,0,0,0},{10253,3520,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{1, 10270, 10288, {10306, 10317, 10330, 10339, 10346, 10359, 10368}, {10306, 10317, 10330, 10339, 10346, 10359, 10368}, {1914, 2300, 2302, 3643, 2302, 2306, 1914}, {10377, 10388, 10399, 10408, 10419, 10426, 10433, 10446, 10455, 10466, 10479, 10490, 0}, {10377, 10388, 10399, 10408, 10419, 10426, 10433, 10446, 10455, 10466, 10479, 10490, 0}, {10377, 10388, 10399, 10408, 10419, 10426, 10433, 10446, 10455, 10466, 10479, 10490, 0}, {10377, 10388, 10399, 10408, 10419, 10426, 10433, 10446, 10455, 10466, 10479, 10490, 0}, 0, 0, 185, 187, {198,189,1556,0,0,0,0,0,0,0,0,0,0,0},{10501,222,0,0,0,0,0,0,0,0},{3532,242,843,251,0,0,0,0,0,0,0,0},{3540,257,848,269,0,0,0,0,0},{278,0,0,0,0,0,0,0}},
	{289, 1820, 1823, {10515, 10522, 10528, 10535, 10540, 10546, 10552}, {10558, 10562, 10566, 10570, 10574, 10578, 10582}, {2300, 1914, 1914, 10586, 4413, 3925, 1914}, {10588, 10596, 10605, 2588, 10611, 2598, 2603, 10615, 2615, 2625, 2633, 10623, 0}, {10588, 10596, 10605, 2588, 10611, 2598, 2603, 10615, 2615, 2625, 2633, 10623, 0}, {2651, 2655, 3699, 2664, 10611, 2668, 2672, 10632, 2680, 2684, 2688, 10636, 0}, {2651, 2655, 3699, 2664, 10611, 2668, 2672, 10632, 2680, 2684, 2688, 10636, 0}, 0, 0, 185, 691, {198,1129,0,0,0,0,0,0,0,0,0,0,0,0},{10640,3520,0,0,0,0,0,0,0,0},{4246,0,0,0,0,0,0,0,0,0,0,0},{4268,0,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{289, 10659, 10664, {10669, 10682, 10701, 10718, 10731, 10744, 10761}, {10774, 8574, 8579, 8584, 8589, 8594, 8599}, {10779, 8607, 8604, 8610, 8613, 8607, 8610}, {10782, 10795, 10806, 10823, 10838, 10853, 10868, 10881, 10896, 10913, 10928, 10945, 0}, {10960, 10971, 10984, 10999, 11012, 11025, 11038, 11049, 11062, 11077, 11090, 11109, 0}, {11122, 11129, 11136, 11143, 11150, 11157, 11164, 11171, 11178, 11185, 11192, 11199, 0}, {11122, 11129, 11136, 11143, 11150, 11157, 11164, 11171, 11178, 11185, 11192, 11199, 0}, 0, 1, 691, 187, {2154,2696,1556,0,0,0,0,0,0,0,0,0,0,0},{11206,0,0,0,0,0,0,0,0,0},{843,251,0,0,0,0,0,0,0,0,0,0},{848,269,0,0,0,0,0,0,0},{11224,0,0,0,0,0,0,0}},
	{289, 11240, 11253, {11266, 11281, 11302, 11317, 11330, 11343, 10761}, {421, 426, 11358, 436, 11363, 446, 451}, {456, 459, 11368, 465, 468, 459, 465}, {11371, 11388, 11397, 11412, 521, 11429, 11444, 11457, 11472, 11489, 11510, 11527, 0}, {11542, 11559, 11572, 11589, 8816, 11608, 11623, 11636, 11649, 11664, 11687, 11706, 0}, {11719, 11726, 11733, 11740, 521, 11747, 11754, 11761, 11768, 11775, 11782, 11789, 0}, {11719, 11726, 11733, 11740, 521, 11747, 11754, 11761, 11768, 11775, 11782, 11789, 0}, 0, 0, 691, 187, {2696,0,0,0,0,0,0,0,0,0,0,0,0,0},{3520,0,0,0,0,0,0,0,0,0},{251,843,0,0,0,0,0,0,0,0,0,0},{269,848,0,0,0,0,0,0,0},{11796,0,0,0,0,0,0,0}},
	{1812, 11810, 11815, {11820, 11828, 11839, 11845, 11851, 11860, 1876}, {9045, 9049, 2287, 11866, 9061, 9066, 11870}, {9074, 9076, 11874, 9080, 9082, 9076, 9080}, {2310, 2317, 9483, 2331, 2337, 11876, 11882, 11888, 2358, 2368, 2376, 2385, 0}, {2310, 2317, 9483, 2331, 2337, 11876, 11882, 11888, 2358, 2368, 2376, 2385, 0}, {2394, 2398, 2402, 2406, 2337, 2410, 2414, 11895, 2422, 2426, 2430, 2434, 0}, {2394, 2398, 2402, 2406, 2337, 2410, 2414, 11895, 2422, 2426, 2430, 2434, 0}, 0, 1, 5812, 187, {11899,6118,0,0,0,0,0,0,0,0,0,0,0,0},{11911,11931,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{1812, 11945, 11950, {11955, 11966, 11977, 11988, 11999, 12010, 12016}, {1909, 9701, 2302, 4413, 1907, 10586, 2308}, {1909, 9701, 2302, 4413, 1907, 10586, 2308}, {12025, 12033, 12042, 12049, 4848, 12056, 12062, 2351, 2358, 12068, 2376, 12077, 0}, {12025, 12033, 12042, 12049, 4848, 12056, 12062, 2351, 2358, 12068, 2376, 12077, 0}, {12087, 12092, 12042, 2406, 4848, 12056, 12062, 2418, 12098, 2426, 2430, 12103, 0}, {12087, 12092, 12042, 2406, 4848, 12056, 12062, 2418, 12098, 2426, 2430, 12103, 0}, 2, 1, 691, 187, {2154,6118,0,0,0,0,0,0,0,0,0,0,0,0},{2719,2194,0,0,0,0,0,0,0,0},{843,251,0,0,0,0,0,0,0,0,0,0},{12108,0,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{1812, 12116, 12133, {12147, 12158, 12168, 12177, 12188, 12200, 12211}, {12221, 12224, 12227, 12230, 12233, 12236, 12239}, {1914, 1909, 2304, 2302, 7600, 1909, 1914}, {12242, 12252, 12263, 12269, 12278, 12284, 12292, 12300, 12308, 12319, 12328, 12338, 0}, {12348, 12358, 2325, 12369, 12378, 12384, 12392, 12400, 12408, 12419, 12428, 12438, 0}, {12448, 12454, 12263, 12460, 12278, 12465, 12471, 12477, 4128, 12482, 4139, 12487, 0}, {12448, 12454, 12263, 12460, 12278, 12465, 12471, 12477, 4128, 12482, 4139, 12487, 0}, 0, 1, 691, 187, {2154,12492,0,0,0,0,0,0,0,0,0,0,0,0},{12512,12539,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{12560,0,0,0,0,0,0,0}},
	{3551, 12576, 11815, {12582, 12594, 12606, 12618, 12632, 12647, 12660}, {12674, 12677, 12680, 12683, 12686, 12689, 9471}, {1914, 1909, 12692, 2302, 4413, 1909, 9480}, {12694, 12701, 12709, 12715, 12724, 12733, 12743, 12749, 12760, 12770, 12777, 12787, 0}, {12694, 12701, 12709, 12715, 12724, 12733, 12743, 12749, 12760, 12770, 12777, 12787, 0}, {12795, 12801, 12806, 12811, 12816, 12821, 12828, 12834, 12840, 12846, 12852, 12859, 0}, {12795, 12801, 12806, 12811, 12816, 12821, 12828, 12834, 12840, 12846, 12852, 12859, 0}, 2, 1, 2438, 187, {1556,0,0,0,0,0,0,0,0,0,0,0,0,0},{12866,12894,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{12916,0,0,0,0,0,0,0}},
	{1, 12926, 12938, {12950, 12965, 12980, 12995, 13012, 13031, 13042}, {13053, 13060, 13067, 13074, 13081, 13088, 13095}, {0, 0, 0, 0, 0, 0, 0}, {13102, 13113, 8644, 13126, 8666, 13137, 13144, 8691, 13151, 13166, 13179, 13190, 0}, {13102, 13113, 8644, 13126, 8666, 13137, 13144, 8691, 13151, 13166, 13179, 13190, 0}, {13203, 13210, 13217, 13224, 8666, 13137, 13144, 13231, 13238, 13245, 13252, 13259, 0}, {13203, 13210, 13217, 13224, 8666, 13137, 13144, 13231, 13238, 13245, 13252, 13259, 0}, 0, 0, 691, 187, {2154,2696,8015,2440,189,0,0,0,0,0,0,0,0,0},{13266,13284,0,0,0,0,0,0,0,0},{251,843,0,0,0,0,0,0,0,0,0,0},{269,848,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{289, 13303, 13323, {13340, 13353, 13366, 13382, 13399, 13414, 13423}, {13340, 13353, 13366, 13382, 13399, 13414, 13423}, {13432, 13435, 13438, 13441, 13444, 13447, 13450}, {13453, 13466, 13477, 13486, 13497, 13502, 13511, 13522, 13529, 13544, 13555, 13568, 0}, {13581, 13596, 13477, 13486, 13609, 13502, 13616, 13522, 13529, 13544, 13555, 13568, 0}, {13453, 13466, 13477, 13486, 13497, 13502, 13511, 13522, 13529, 13544, 13555, 13568, 0}, {13453, 13466, 13477, 13486, 13497, 13502, 13511, 13522, 13529, 13544, 13555, 13568, 0}, 0, 6, 185, 187, {198,189,0,0,0,0,0,0,0,0,0,0,0,0},{3502,3520,0,0,0,0,0,0,0,0},{242,251,0,0,0,0,0,0,0,0,0,0},{257,269,0,0,0,0,0,0,0},{278,0,0,0,0,0,0,0}},
	{1, 13629, 13632, {13635, 13648, 13658, 13667, 13677, 13688, 13699}, {13711, 13714, 13719, 13724, 13729, 13734, 13739}, {13711, 13744, 13747, 13750, 13753, 13756, 13759}, {13762, 13771, 13780, 13789, 13798, 13807, 13816, 13825, 13834, 13843, 13853, 13863, 0}, {13873, 13882, 13891, 13900, 13909, 13918, 13927, 13936, 13945, 13954, 13964, 13974, 0}, {13984, 13990, 13996, 14002, 14008, 14014, 14020, 14026, 14032, 14038, 14045, 14052, 0}, {13984, 13990, 13996, 14002, 14008, 14014, 14020, 14026, 14032, 14038, 14045, 14052, 0}, 0, 1, 185, 187, {198,189,2451,2440,1556,0,0,0,0,0,0,0,0,0},{5455,0,0,0,0,0,0,0,0,0},{3532,242,843,251,0,0,0,0,0,0,0,0},{3540,257,848,269,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{289, 14059, 14083, {14107, 14120, 14141, 14160, 14181, 14200, 14213}, {14224, 14231, 14238, 14245, 14252, 14259, 14266}, {14273, 14276, 14276, 14279, 14282, 14285, 14290}, {14293, 14308, 14323, 14332, 14343, 14354, 14367, 14380, 14395, 14414, 14433, 14450, 0}, {14469, 14486, 14503, 14514, 14527, 14540, 14555, 14570, 14587, 14608, 14629, 14648, 0}, {14669, 14676, 14683, 14690, 14697, 14704, 14711, 14718, 14725, 14732, 14739, 14746, 0}, {14669, 14676, 14683, 14690, 14697, 14704, 14711, 14718, 14725, 14732, 14739, 14746, 0}, 0, 1, 691, 187, {2154,2696,14753,198,14763,3490,1556,0,0,0,0,0,0,0},{3808,3502,10640,5455,14774,14785,14797,14814,0,0},{251,843,0,0,0,0,0,0,0,0,0,0},{269,848,0,0,0,0,0,0,0},{278,0,0,0,0,0,0,0}},
	{289, 1820, 1823, {14832, 14838, 14852, 14875, 14889, 14905, 14912}, {14921, 14924, 14929, 14935, 14939, 7600, 14944}, {6540, 6528, 6530, 6532, 6534, 6536, 6538}, {14948, 14955, 10132, 14962, 3668, 14968, 14974, 14980, 14987, 14996, 15004, 15011, 0}, {15018, 15025, 15032, 15037, 15043, 15047, 15052, 15057, 15064, 15073, 15081, 15088, 0}, {15095, 7999, 2402, 2406, 15043, 15099, 15103, 15107, 15111, 2426, 15115, 15119, 0}, {15095, 7999, 2402, 2406, 15043, 15099, 15103, 15107, 15111, 2426, 15115, 15119, 0}, 0, 0, 691, 187, {2154,1129,0,0,0,0,0,0,0,0,0,0,0,0},{15123,3520,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{3551, 1820, 1823, {15141, 15149, 15160, 15170, 15181, 15190, 15199}, {15209, 15213, 15217, 15221, 15225, 15229, 15233}, {15237, 12692, 12692, 12692, 2304, 2304, 2308}, {15239, 15249, 15257, 15265, 15273, 15281, 15288, 15296, 15304, 15311, 15317, 15324, 0}, {15332, 15343, 15352, 15361, 15370, 15379, 15387, 15396, 15405, 15413, 15420, 15428, 0}, {15437, 15442, 4795, 15447, 15452, 15457, 15462, 15467, 15472, 15477, 15482, 15487, 0}, {15437, 15442, 4795, 15447, 15452, 15457, 15462, 15467, 15472, 15477, 15482, 15487, 0}, 2, 1, 185, 187, {1545,15492,0,0,0,0,0,0,0,0,0,0,0,0},{15503,15530,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{15551,0,0,0,0,0,0,0}},
	{289, 15570, 15591, {15608, 331, 352, 8513, 15621, 15638, 15649}, {15662, 15670, 15678, 15684, 15692, 15700, 15708}, {456, 459, 462, 465, 468, 459, 465}, {15716, 484, 501, 510, 15731, 15738, 15747, 542, 555, 574, 591, 606, 0}, {15716, 484, 501, 510, 15731, 15738, 15747, 542, 555, 574, 591, 606, 0}, {15756, 15764, 15772, 639, 15731, 15780, 15788, 647, 655, 665, 673, 683, 0}, {15756, 15764, 15772, 639, 15731, 15780, 15788, 647, 655, 665, 673, 683, 0}, 0, 1, 691, 187, {15796,0,0,0,0,0,0,0,0,0,0,0,0,0},{10640,5455,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{15806,0,0,0,0,0,0,0}},
	{3551, 0, 0, {15822, 15829, 15840, 15849, 15858, 15865, 15876}, {15886, 15890, 15894, 15898, 15902, 15905, 15909}, {0, 0, 0, 0, 0, 0, 0}, {15913, 15923, 15932, 15940, 15949, 15962, 15974, 15981, 15988, 15995, 16005, 16017, 0}, {15913, 15923, 15932, 15940, 15949, 15962, 15974, 15981, 15988, 15995, 16005, 16017, 0}, {16030, 16034, 16038, 16042, 16046, 16050, 16054, 16058, 16062, 16066, 16070, 16074, 0}, {16030, 16034, 16038, 16042, 16046, 16050, 16054, 16058, 16062, 16066, 16070, 16074, 0}, 0, 0, 2438, 187, {1556,15492,0,0,0,0,0,0,0,0,0,0,0,0},{16078,16096,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{12916,0,0,0,0,0,0,0}},
	{3551, 0, 0, {16108, 16113, 16119, 16129, 16141, 16149, 16160}, {16169, 16173, 16177, 16181, 16185, 16189, 16193}, {0, 0, 0, 0, 0, 0, 0}, {16197, 16207, 16217, 16224, 16231, 2598, 16236, 16243, 16250, 16259, 16267, 16275, 0}, {16197, 16207, 16217, 16224, 16231, 2598, 16236, 16243, 16250, 16259, 16267, 16275, 0}, {2651, 2655, 15909, 16283, 16287, 2668, 2672, 16291, 2680, 2684, 2688, 16295, 0}, {2651, 2655, 15909, 16283, 16287, 2668, 2672, 16291, 2680, 2684, 2688, 16295, 0}, 0, 0, 2438, 187, {1556,15492,0,0,0,0,0,0,0,0,0,0,0,0},{16078,16096,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{12916,0,0,0,0,0,0,0}},
	{3551, 1820, 1823, {16299, 16305, 16316, 16129, 16141, 16149, 16160}, {16326, 16330, 16334, 16181, 16185, 16189, 16193}, {1914, 2300, 16338, 2302, 1914, 5644, 2300}, {16340, 16350, 16361, 16368, 16377, 16383, 16389, 16397, 16405, 16416, 16426, 16435, 0}, {16444, 16207, 16453, 16459, 16231, 2598, 16236, 16243, 16466, 16259, 16267, 16275, 0}, {2651, 2655, 16476, 2664, 16287, 2668, 2672, 16291, 2680, 2684, 2688, 16295, 0}, {2651, 2655, 16476, 2664, 16287, 2668, 2672, 16291, 2680, 2684, 2688, 16295, 0}, 0, 0, 185, 187, {3711,16480,0,0,0,0,0,0,0,0,0,0,0,0},{3757,3776,0,0,0,0,0,0,0,0},{3532,251,0,0,0,0,0,0,0,0,0,0},{3540,269,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{289, 16492, 16496, {16500, 16507, 16515, 16523, 16532, 16542, 16549}, {2544, 16558, 2550, 16561, 2556, 16564, 2562}, {1914, 2300, 2565, 3643, 2565, 3927, 1914}, {16567, 16576, 16586, 2588, 10611, 16592, 16598, 16604, 2615, 2625, 2633, 10623, 0}, {16567, 16576, 16586, 2588, 10611, 16592, 16598, 16604, 2615, 2625, 2633, 10623, 0}, {2651, 2655, 3699, 2664, 10611, 2668, 2672, 2676, 2680, 2684, 2688, 10636, 0}, {2651, 2655, 3699, 2664, 10611, 2668, 2672, 2676, 2680, 2684, 2688, 10636, 0}, 0, 0, 2438, 187, {1556,9951,0,0,0,0,0,0,0,0,0,0,0,0},{10640,5455,0,0,0,0,0,0,0,0},{3532,251,0,0,0,0,0,0,0,0,0,0},{3540,269,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{289, 1820, 1823, {16613, 16629, 16654, 16682, 16710, 16738, 16766}, {16785, 16795, 16805, 16815, 16825, 16835, 16845}, {16855, 16859, 16863, 16859, 16867, 16871, 16875}, {16879, 16901, 16929, 16945, 16964, 16980, 16999, 17018, 17040, 17071, 17099, 17124, 0}, {16879, 16901, 16929, 16945, 16964, 16980, 16999, 17018, 17040, 17071, 17099, 17124, 0}, {17152, 17162, 17172, 17182, 17192, 17202, 17212, 17222, 17232, 17242, 17252, 17262, 0}, {17152, 17162, 17172, 17182, 17192, 17202, 17212, 17222, 17232, 17242, 17252, 17262, 0}, 0, 1, 691, 187, {2154,17272,0,0,0,0,0,0,0,0,0,0,0,0},{222,3808,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{278,0,0,0,0,0,0,0}},
	{3551, 17284, 17289, {5897, 17294, 17305, 17315, 17325, 17335, 17349}, {17361, 17365, 17370, 17375, 17379, 17384, 17389}, {1914, 2300, 2302, 2300, 5644, 2306, 2308}, {2310, 2317, 4837, 6042, 4848, 2341, 2346, 2351, 2358, 2368, 2376, 6086, 0}, {2310, 2317, 4837, 6042, 4848, 2341, 2346, 2351, 2358, 2368, 2376, 6086, 0}, {2394, 2398, 2402, 2406, 4848, 2410, 2414, 2418, 2422, 2426, 2430, 7503, 0}, {2394, 2398, 2402, 2406, 4848, 2410, 2414, 2418, 2422, 2426, 2430, 7503, 0}, 2, 1, 2438, 187, {2440,0,0,0,0,0,0,0,0,0,0,0,0,0},{5508,6118,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{12916,0,0,0,0,0,0,0}},
	{1, 17393, 17409, {17419, 17438, 17457, 17479, 17498, 17520, 17545}, {17564, 17574, 17584, 17597, 17607, 17620, 17636}, {17646, 17650, 17657, 17664, 17671, 17678, 17685}, {17689, 17705, 17724, 17740, 17759, 17766, 17776, 17792, 17808, 17827, 17849, 17865, 0}, {17689, 17705, 17724, 17740, 17759, 17766, 17776, 17792, 17808, 17827, 17849, 17865, 0}, {17884, 17891, 17724, 17901, 17759, 17766, 17917, 17930, 17937, 17950, 17966, 17976, 0}, {17884, 17891, 17724, 17901, 17759, 17766, 17917, 17930, 17937, 17950, 17966, 17976, 0}, 0, 0, 2438, 187, {2440,2451,4157,8015,1556,0,0,0,0,0,0,0,0,0},{5455,3520,0,0,0,0,0,0,0,0},{251,843,1730,1722,0,0,0,0,0,0,0,0},{269,848,1750,1739,0,0,0,0,0},{278,0,0,0,0,0,0,0}},
	{17989, 18004, 18007, {18010, 18019, 18028, 18038, 18048, 18058, 18070}, {18078, 18083, 18087, 18091, 18095, 18100, 18105}, {18109, 2302, 2302, 9701, 18109, 18112, 1914}, {18115, 18122, 18127, 2588, 18133, 18139, 18146, 18152, 18160, 18170, 18178, 18187, 0}, {18115, 18122, 18127, 2588, 18133, 18139, 18146, 18152, 18160, 18170, 18178, 18187, 0}, {2651, 18197, 3699, 2664, 18201, 18205, 18210, 18214, 18218, 18222, 2688, 18226, 0}, {2651, 18197, 3699, 2664, 18201, 18205, 18210, 18214, 18218, 18222, 2688, 18226, 0}, 0, 0, 185, 187, {198,9951,0,0,0,0,0,0,0,0,0,0,0,0},{18231,18257,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{18277,0,0,0,0,0,0,0}},
	{3551, 18295, 18300, {18305, 18317, 18328, 18342, 18354, 18364, 18374}, {18385, 18390, 18395, 18400, 18405, 18410, 18415}, {1914, 3927, 2300, 6216, 2565, 16338, 2308}, {18420, 18437, 18450, 18464, 18477, 18490, 18503, 18517, 18529, 18543, 18557, 18571, 0}, {18420, 18437, 18450, 18464, 18477, 18490, 18503, 18517, 18529, 18543, 18557, 18571, 0}, {18584, 18591, 18596, 18601, 18605, 18610, 18615, 18620, 18625, 18632, 18637, 18643, 0}, {18584, 18591, 18596, 18601, 18605, 18610, 18615, 18620, 18625, 18632, 18637, 18643, 0}, 2, 1, 2438, 187, {1556,15492,0,0,0,0,0,0,0,0,0,0,0,0},{16078,16096,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{12916,0,0,0,0,0,0,0}},
	{289, 7261, 7266, {18648, 18662, 18672, 18683, 18697, 18708, 18719}, {18732, 18737, 18742, 18749, 18755, 18761, 18767}, {2565, 2308, 2300, 7600, 2565, 12692, 1914}, {18772, 18780, 18788, 18795, 18804, 18814, 18824, 18830, 18838, 18853, 18871, 18879, 0}, {18772, 18780, 18788, 18795, 18804, 18814, 18824, 18830, 18838, 18853, 18871, 18879, 0}, {18887, 18891, 18788, 18897, 18901, 18906, 18824, 18912, 18917, 18924, 18931, 18936, 0}, {18887, 18891, 18788, 18897, 18901, 18906, 18824, 18912, 18917, 18924, 18931, 18936, 0}, 2, 0, 185, 187, {198,1129,0,0,0,0,0,0,0,0,0,0,0,0},{4935,3520,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{289, 18941, 18944, {18948, 18953, 10528, 10535, 18959, 18966, 10552}, {18973, 18977, 10566, 10570, 18981, 10578, 10582}, {12692, 15237, 1914, 10586, 4413, 3925, 1914}, {10588, 10596, 18985, 2588, 10611, 2668, 18989, 18995, 2615, 2625, 2633, 19000, 0}, {10588, 10596, 18985, 2588, 10611, 2668, 18989, 18995, 2615, 2625, 2633, 19000, 0}, {2651, 2655, 18985, 2664, 10611, 2668, 2672, 19009, 2680, 2684, 2688, 16295, 0}, {2651, 2655, 18985, 2664, 10611, 2668, 2672, 19009, 2680, 2684, 2688, 16295, 0}, 0, 0, 185, 187, {14753,1129,0,0,0,0,0,0,0,0,0,0,0,0},{3502,3520,0,0,0,0,0,0,0,0},{3532,251,0,0,0,0,0,0,0,0,0,0},{3540,269,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{289, 19013, 19035, {19059, 19074, 19091, 19108, 19125, 19142, 19151}, {19162, 19168, 19174, 19180, 19186, 19192, 19198}, {19204, 19207, 8610, 8610, 19210, 19204, 8610}, {19213, 19226, 19237, 19250, 19261, 19272, 19285, 19296, 19307, 19324, 19335, 19348, 0}, {19213, 19226, 19237, 19250, 19261, 19272, 19285, 19296, 19307, 19324, 19335, 19348, 0}, {19367, 19375, 19383, 19391, 19399, 19407, 19415, 19423, 19431, 19439, 19447, 19455, 0}, {19367, 19375, 19383, 19391, 19399, 19407, 19415, 19423, 19431, 19439, 19447, 19455, 0}, 0, 0, 185, 187, {198,19465,0,0,0,0,0,0,0,0,0,0,0,0},{3502,3520,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{19478, 19485, 19511, {19539, 19546, 19553, 19560, 19567, 19574, 19581}, {19539, 19546, 19553, 19560, 19567, 19574, 19581}, {19204, 19207, 19588, 19588, 19210, 19204, 19591}, {19594, 19607, 501, 19622, 521, 19635, 19644, 542, 19653, 19670, 19685, 19698, 0}, {19594, 19607, 501, 19622, 521, 19635, 19644, 542, 19653, 19670, 19685, 19698, 0}, {19713, 15764, 15772, 639, 521, 19721, 19729, 647, 19737, 665, 19745, 683, 0}, {19713, 15764, 15772, 639, 521, 19721, 19729, 647, 19737, 665, 19745, 683, 0}, 0, 0, 2438, 187, {19753,0,0,0,0,0,0,0,0,0,0,0,0,0},{19762,0,0,0,0,0,0,0,0,0},{251,843,0,0,0,0,0,0,0,0,0,0},{269,848,0,0,0,0,0,0,0},{19781,0,0,0,0,0,0,0}},
	{289, 1820, 1823, {19797, 19806, 19815, 19823, 19832, 19841, 19848}, {19857, 19860, 19863, 19866, 19869, 19873, 19876}, {6530, 6532, 6534, 6536, 12692, 15237, 6528}, {10588, 10596, 19879, 19885, 10611, 2598, 18989, 19892, 16250, 19899, 16267, 19906, 0}, {10588, 10596, 19879, 19885, 10611, 2598, 18989, 19892, 16250, 19899, 16267, 19906, 0}, {2651, 2655, 18985, 2664, 10611, 2668, 2672, 19914, 2680, 2684, 2688, 10636, 0}, {2651, 2655, 18985, 2664, 10611, 2668, 2672, 19914, 2680, 2684, 2688, 10636, 0}, 0, 0, 185, 187, {198,1129,0,0,0,0,0,0,0,0,0,0,0,0},{3502,3520,0,0,0,0,0,0,0,0},{3532,251,0,0,0,0,0,0,0,0,0,0},{3540,269,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{3551, 1820, 1823, {19918, 19928, 19937, 19946, 19957, 19967, 19972}, {19979, 19985, 19990, 19995, 20000, 10578, 20004}, {20009, 2565, 1914, 7600, 1909, 3925, 1914}, {14948, 14955, 10132, 14962, 3668, 20011, 20016, 20021, 14987, 14996, 15004, 15011, 0}, {14948, 14955, 10132, 14962, 3668, 20011, 20016, 20021, 14987, 14996, 15004, 15011, 0}, {20028, 20033, 3699, 2664, 3668, 20011, 20016, 20037, 10562, 2684, 20041, 20046, 0}, {20028, 20033, 3699, 2664, 3668, 20011, 20016, 20037, 10562, 2684, 20041, 20046, 0}, 0, 0, 185, 187, {1545,15492,0,0,0,0,0,0,0,0,0,0,0,0},{20050,16096,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{12916,0,0,0,0,0,0,0}},
	{289, 20069, 20097, {20119, 20138, 20157, 20182, 20201, 20235, 20260}, {20279, 20289, 20299, 20315, 20325, 20350, 20366}, {20376, 20380, 20387, 20391, 20398, 20405, 20412}, {20416, 20444, 20478, 20494, 20513, 20520, 20530, 20546, 20562, 20593, 20615, 20637, 0}, {20416, 20444, 20478, 20494, 20513, 20520, 20530, 20546, 20562, 20593, 20615, 20637, 0}, {20416, 20444, 20478, 20494, 20513, 20520, 20530, 20546, 20562, 20593, 20615, 20637, 0}, {20416, 20444, 20478, 20494, 20513, 20520, 20530, 20546, 20562, 20593, 20615, 20637, 0}, 0, 1, 2438, 691, {2451,4157,2440,2451,2440,0,0,0,0,0,0,0,0,0},{5455,3520,0,0,0,0,0,0,0,0},{4246,4241,20662,20671,0,0,0,0,0,0,0,0},{4268,4260,20679,20691,0,0,0,0,0},{278,0,0,0,0,0,0,0}},
	{1, 1820, 1823, {20702, 20718, 20737, 20759, 20778, 20797, 20825}, {20847, 20855, 20866, 20880, 20891, 20902, 20922}, {20936, 20940, 20947, 20954, 20964, 20971, 20984}, {20991, 21007, 21026, 21039, 21058, 21065, 21075, 21091, 21104, 21120, 21139, 21155, 0}, {20991, 21007, 21026, 21039, 21058, 21065, 21075, 21091, 21104, 21120, 21139, 21155, 0}, {20991, 21007, 21026, 21039, 21058, 21065, 21075, 21091, 21104, 21120, 21139, 21155, 0}, {20991, 21007, 21026, 21039, 21058, 21065, 21075, 21091, 21104, 21120, 21139, 21155, 0}, 0, 0, 2438, 187, {2451,4157,8015,2440,1556,0,0,0,0,0,0,0,0,0},{21171,3520,0,0,0,0,0,0,0,0},{1730,1722,843,251,0,0,0,0,0,0,0,0},{1750,1739,848,269,0,0,0,0,0},{278,0,0,0,0,0,0,0}},
	{1, 1820, 1823, {21189, 21208, 21227, 21249, 21268, 21290, 21315}, {21334, 21344, 21354, 21367, 21377, 21390, 21406}, {21416, 21420, 21427, 21434, 21441, 21448, 21455}, {21459, 21487, 21515, 21531, 21550, 21557, 21567, 21583, 21599, 21627, 21649, 21671, 0}, {21459, 21487, 21515, 21531, 21550, 21557, 21567, 21583, 21599, 21627, 21649, 21671, 0}, {21696, 21715, 21515, 21531, 21550, 21557, 21567, 21734, 21741, 21757, 21773, 21783, 0}, {21696, 21715, 21515, 21531, 21550, 21557, 21567, 21734, 21741, 21757, 21773, 21783, 0}, 0, 0, 2438, 187, {2451,4157,8015,2440,1556,0,0,0,0,0,0,0,0,0},{5455,3520,0,0,0,0,0,0,0,0},{251,843,1730,1722,0,0,0,0,0,0,0,0},{269,848,1750,1739,0,0,0,0,0},{278,0,0,0,0,0,0,0}},
	{1, 8022, 21796, {21799, 21818, 21837, 21862, 21881, 21903, 21928}, {21947, 21957, 21967, 21983, 21993, 22006, 22022}, {22032, 22036, 22043, 22047, 22054, 22061, 22068}, {22072, 22094, 22125, 22147, 22166, 22173, 22183, 22199, 22215, 22246, 22268, 22290, 0}, {22072, 22094, 22125, 22147, 22166, 22173, 22183, 22199, 22215, 22246, 22268, 22290, 0}, {6528, 6530, 6532, 6534, 6536, 6538, 6540, 6542, 6544, 6546, 6549, 6552, 0}, {6528, 6530, 6532, 6534, 6536, 6538, 6540, 6542, 6544, 6546, 6549, 6552, 0}, 0, 0, 2438, 187, {2451,4157,8015,2440,1556,0,0,0,0,0,0,0,0,0},{5455,3520,0,0,0,0,0,0,0,0},{251,843,1730,1722,0,0,0,0,0,0,0,0},{269,848,1750,1739,0,0,0,0,0},{278,0,0,0,0,0,0,0}},
	{289, 22315, 22340, {22365, 22384, 22406, 22431, 22447, 22469, 22488}, {22498, 22505, 22512, 22519, 22526, 22533, 22540}, {22498, 22505, 22512, 22519, 22526, 22533, 22540}, {22544, 22560, 22585, 22604, 22623, 22630, 22643, 22656, 22675, 22706, 22731, 22753, 0}, {22544, 22560, 22585, 22604, 22623, 22630, 22643, 22778, 22675, 22706, 22731, 22753, 0}, {22797, 22805, 22819, 22833, 22623, 22630, 22643, 22844, 22852, 22866, 22877, 22885, 0}, {22797, 22805, 22819, 22833, 22623, 22630, 22643, 22844, 22852, 22866, 22877, 22885, 0}, 0, 0, 2438, 187, {2440,2451,4157,8015,1556,0,0,0,0,0,0,0,0,0},{5455,3520,0,0,0,0,0,0,0,0},{251,843,1730,1722,0,0,0,0,0,0,0,0},{269,848,1750,1739,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{3551, 1820, 1823, {22896, 22918, 22940, 22965, 22987, 23012, 23040}, {23062, 23072, 23082, 23095, 23105, 23118, 23134}, {23144, 23148, 23155, 23159, 23166, 23173, 23180}, {23184, 23200, 23225, 23244, 23266, 23273, 23286, 23299, 23318, 23349, 23374, 23393, 0}, {23184, 23200, 23225, 23244, 23266, 23273, 23418, 23299, 23318, 23349, 23374, 23393, 0}, {23431, 23438, 23225, 23454, 23266, 23273, 23418, 23299, 23470, 23492, 23508, 23518, 0}, {23431, 23438, 23225, 23454, 23266, 23273, 23418, 23299, 23470, 23492, 23508, 23518, 0}, 0, 0, 2438, 187, {2451,4157,8015,2440,1556,0,0,0,0,0,0,0,0,0},{5455,3520,0,0,0,0,0,0,0,0},{251,843,1730,1722,0,0,0,0,0,0,0,0},{269,848,1750,1739,0,0,0,0,0},{278,0,0,0,0,0,0,0}},
	{3551, 1820, 1823, {23534, 23553, 23572, 23594, 23613, 23635, 23660}, {23679, 23689, 23699, 23712, 23722, 23735, 23751}, {23761, 23765, 23772, 23779, 23786, 23793, 23800}, {23804, 23820, 23845, 23864, 23886, 23893, 23906, 23919, 23938, 23966, 23991, 24013, 0}, {23804, 23820, 23845, 23864, 23886, 23893, 23906, 23919, 23938, 23966, 23991, 24013, 0}, {24038, 24046, 24066, 24073, 23886, 24090, 24097, 24105, 24113, 24136, 24153, 24167, 0}, {24038, 24046, 24066, 24073, 23886, 24090, 24097, 24105, 24113, 24136, 24153, 24167, 0}, 0, 0, 2438, 187, {2451,4157,8015,2440,1556,0,0,0,0,0,0,0,0,0},{5455,3520,0,0,0,0,0,0,0,0},{251,843,1730,1722,0,0,0,0,0,0,0,0},{269,848,1750,1739,0,0,0,0,0},{278,0,0,0,0,0,0,0}},
	{24184, 1820, 1823, {24192, 24220, 24254, 24282, 24310, 24341, 24378}, {24406, 24419, 24438, 24454, 24467, 24486, 24505}, {24515, 24522, 24529, 24536, 24543, 24556, 24563}, {24567, 24586, 24614, 24636, 24655, 24668, 24678, 24691, 24716, 24747, 24772, 24788, 0}, {24567, 24586, 24614, 24636, 24655, 24668, 24678, 24691, 24716, 24747, 24772, 24788, 0}, {24807, 24817, 24836, 24846, 24655, 24668, 24678, 24862, 24869, 24894, 24910, 24920, 0}, {24807, 24817, 24836, 24846, 24655, 24668, 24678, 24862, 24869, 24894, 24910, 24920, 0}, 0, 0, 2438, 691, {2451,4157,2696,8015,0,0,0,0,0,0,0,0,0,0},{5455,3520,0,0,0,0,0,0,0,0},{4246,4241,20662,20671,0,0,0,0,0,0,0,0},{4268,4260,20679,20691,0,0,0,0,0},{278,0,0,0,0,0,0,0}},
	{289, 24933, 24961, {24983, 25002, 25021, 25046, 25065, 25099, 25124}, {25143, 20289, 20299, 20315, 25153, 25178, 20366}, {0, 0, 0, 0, 0, 0, 0}, {25194, 25219, 25250, 25266, 20513, 20520, 20530, 25285, 25301, 25332, 25354, 25376, 0}, {25194, 25219, 25250, 25266, 20513, 20520, 20530, 25285, 25301, 25332, 25354, 25376, 0}, {25401, 25414, 25250, 25266, 20513, 20520, 20530, 25433, 25440, 25456, 25472, 25482, 0}, {25401, 25414, 25250, 25266, 20513, 20520, 20530, 25433, 25440, 25456, 25472, 25482, 0}, 0, 0, 2438, 187, {2440,0,0,0,0,0,0,0,0,0,0,0,0,0},{25495,0,0,0,0,0,0,0,0,0},{1722,1730,843,0,0,0,0,0,0,0,0,0},{1739,1750,848,0,0,0,0,0,0},{25514,25522,0,0,0,0,0,0}},
	{1, 1820, 1823, {17419, 17438, 25532, 17479, 17498, 17520, 17545}, {17564, 17574, 25554, 17597, 17607, 17620, 17636}, {17646, 17650, 17657, 17664, 17671, 17678, 17685}, {25567, 25592, 17724, 25623, 25642, 17766, 25649, 25662, 25678, 25703, 25725, 25753, 0}, {25567, 25592, 17724, 25623, 25642, 17766, 25649, 25662, 25678, 25703, 25725, 25753, 0}, {25775, 25788, 17724, 25807, 25642, 17766, 25649, 25823, 25830, 25849, 25865, 25887, 0}, {25775, 25788, 17724, 25807, 25642, 17766, 25649, 25823, 25830, 25849, 25865, 25887, 0}, 0, 0, 2438, 187, {2440,2451,4157,8015,1556,0,0,0,0,0,0,0,0,0},{5455,3520,0,0,0,0,0,0,0,0},{251,843,1730,1722,0,0,0,0,0,0,0,0},{269,848,1750,1739,0,0,0,0,0},{278,0,0,0,0,0,0,0}},
	{3551, 25903, 25908, {25913, 25920, 25931, 25944, 25957, 25968, 25981}, {25992, 25997, 26002, 26007, 26012, 26017, 26022}, {6528, 6530, 6532, 6534, 6536, 6538, 6540}, {26027, 26053, 26081, 26111, 26141, 26167, 26197, 26223, 26251, 26275, 26303, 26340, 0}, {26027, 26053, 26081, 26111, 26141, 26167, 26197, 26223, 26251, 26275, 26303, 26340, 0}, {26379, 26391, 26403, 26415, 26427, 26439, 26451, 26463, 26475, 26487, 26500, 26513, 0}, {26379, 26391, 26403, 26415, 26427, 26439, 26451, 26463, 26475, 26487, 26500, 26513, 0}, 0, 0, 2438, 187, {1556,15492,0,0,0,0,0,0,0,0,0,0,0,0},{26526,26564,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{12916,0,0,0,0,0,0,0}},
	{26596, 26620, 26642, {26667, 26695, 26723, 26760, 26791, 26825, 26850}, {26884, 26900, 26916, 26941, 26960, 26982, 26995}, {27017, 27024, 27031, 27038, 27045, 27052, 27056}, {27066, 27100, 27137, 27171, 27205, 27236, 27273, 27310, 27350, 27384, 27418, 27467, 0}, {27066, 27100, 27137, 27171, 27205, 27236, 27273, 27310, 27350, 27384, 27418, 27467, 0}, {27516, 27529, 27542, 27555, 27568, 27581, 27594, 27607, 27620, 27633, 27649, 27665, 0}, {27516, 27529, 27542, 27555, 27568, 27581, 27594, 27607, 27620, 27633, 27649, 27665, 0}, 0, 0, 185, 187, {1518,1527,1536,1567,1556,1545,1585,1578,1592,0,0,0,0,0},{27681,27724,27772,27804,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{27841,1805,0,0,0,0,0,0}},
	{289, 1820, 1823, {27874, 27883, 27893, 27905, 27918, 27927, 27939}, {27951, 27955, 27960, 27964, 27968, 27972, 27976}, {1914, 27980, 2300, 2300, 15237, 6216, 1914}, {27983, 27990, 27999, 28006, 2594, 28013, 28021, 28032, 28037, 28042, 28049, 28058, 0}, {27983, 27990, 27999, 28006, 2594, 28013, 28021, 28032, 28037, 28042, 28049, 28058, 0}, {28066, 28070, 27960, 28074, 2594, 28078, 28082, 28032, 28037, 28086, 28090, 28095, 0}, {28066, 28070, 27960, 28074, 2594, 28078, 28082, 28032, 28037, 28086, 28090, 28095, 0}, 2, 1, 185, 187, {198,1129,0,0,0,0,0,0,0,0,0,0,0,0},{3502,3520,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{289, 28100, 28116, {28132, 28154, 28167, 28186, 28196, 28227, 28243}, {28132, 28154, 28167, 28186, 28196, 28227, 28243}, {6528, 6530, 6532, 6534, 6536, 6538, 6540}, {28256, 28269, 28288, 28301, 28314, 28327, 28346, 28365, 28378, 28394, 28407, 28432, 0}, {28256, 28269, 28288, 28301, 28314, 28327, 28346, 28365, 28378, 28394, 28407, 28432, 0}, {28256, 28269, 28288, 28301, 28314, 28327, 28346, 28365, 28378, 28394, 28407, 28432, 0}, {28256, 28269, 28288, 28301, 28314, 28327, 28346, 28365, 28378, 28394, 28407, 28432, 0}, 0, 0, 185, 187, {189,1556,0,0,0,0,0,0,0,0,0,0,0,0},{3520,9963,0,0,0,0,0,0,0,0},{843,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{28445,0,0,0,0,0,0,0}},
	{289, 28480, 28505, {28530, 28555, 28574, 28602, 28621, 28646, 28665}, {28530, 28555, 28574, 28602, 28621, 28646, 28665}, {28687, 28691, 28695, 28699, 28709, 28713, 28723}, {28727, 28746, 28762, 28775, 28788, 28810, 28829, 28851, 28867, 28883, 28896, 28912, 0}, {28727, 28746, 28762, 28775, 28788, 28810, 28829, 28851, 28867, 28883, 28896, 28912, 0}, {28928, 28937, 28946, 28955, 28964, 28973, 28985, 28994, 29003, 29012, 29021, 29030, 0}, {28928, 28937, 28946, 28955, 28964, 28973, 28985, 28994, 29003, 29012, 29021, 29030, 0}, 0, 0, 185, 187, {1120,1129,0,0,0,0,0,0,0,0,0,0,0,0},{29039,3520,0,0,0,0,0,0,0,0},{843,251,0,0,0,0,0,0,0,0,0,0},{848,269,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{289, 7261, 7266, {3833, 29066, 3847, 29071, 29081, 29087, 3880}, {29094, 8298, 3699, 29098, 29103, 29107, 29111}, {2565, 2308, 2300, 2300, 29116, 3927, 1914}, {29118, 29126, 3943, 3949, 29135, 29140, 29146, 3972, 29152, 29161, 29169, 29178, 0}, {29187, 29195, 4032, 1012, 7947, 29204, 29210, 4055, 7964, 7973, 7981, 29216, 0}, {29225, 2655, 3699, 29229, 2594, 29233, 29238, 19914, 18218, 29242, 2688, 3707, 0}, {29225, 2655, 3699, 29229, 2594, 29233, 29238, 19914, 18218, 29242, 2688, 3707, 0}, 2, 1, 185, 187, {198,17272,0,0,0,0,0,0,0,0,0,0,0,0},{5508,5455,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{1, 29246, 29258, {29270, 17438, 29298, 17479, 17498, 17520, 17545}, {17564, 17574, 25554, 17597, 17607, 17620, 17636}, {0, 0, 0, 0, 0, 0, 0}, {25567, 25592, 17724, 25623, 25642, 17766, 25649, 29317, 29333, 29361, 25725, 25753, 0}, {25567, 25592, 17724, 25623, 25642, 17766, 25649, 29317, 29333, 29361, 25725, 25753, 0}, {6528, 6530, 6532, 6534, 6536, 6538, 6540, 6542, 6544, 6546, 6549, 6552, 0}, {6528, 6530, 6532, 6534, 6536, 6538, 6540, 6542, 6544, 6546, 6549, 6552, 0}, 0, 0, 2438, 187, {2440,2451,4157,8015,1556,0,0,0,0,0,0,0,0,0},{5455,3520,0,0,0,0,0,0,0,0},{251,843,1730,1722,0,0,0,0,0,0,0,0},{269,848,1750,1739,0,0,0,0,0},{278,0,0,0,0,0,0,0}},
	{3551, 29383, 29395, {29404, 29420, 29436, 29464, 29480, 29523, 29548}, {29404, 29420, 29576, 29464, 29586, 29608, 29621}, {29631, 29635, 29639, 29643, 29647, 29660, 29667}, {29674, 29693, 29718, 29737, 29762, 29775, 29788, 29801, 29823, 29857, 29882, 29910, 0}, {29674, 29693, 29718, 29737, 29762, 29775, 29788, 29801, 29823, 29857, 29882, 29910, 0}, {29938, 29945, 29955, 29737, 29762, 29775, 29788, 29968, 29978, 29991, 30001, 30014, 0}, {29938, 29945, 29955, 29737, 29762, 29775, 29788, 29968, 29978, 29991, 30001, 30014, 0}, 0, 1, 2438, 691, {1556,15492,0,0,0,0,0,0,0,0,0,0,0,0},{16078,16096,0,0,0,0,0,0,0,0},{20671,251,0,0,0,0,0,0,0,0,0,0},{20691,269,0,0,0,0,0,0,0},{12916,0,0,0,0,0,0,0}},
	{3551, 30027, 30037, {30050, 30060, 30067, 30080, 30090, 30100, 30110}, {30050, 30060, 30120, 30080, 30090, 30100, 30110}, {30130, 30134, 30138, 30142, 30146, 30150, 30154}, {30158, 30174, 30190, 30200, 30213, 30220, 30227, 30237, 30250, 30269, 30285, 30301, 0}, {30158, 30174, 30190, 30200, 30213, 30220, 30227, 30237, 30250, 30317, 30285, 30301, 0}, {30336, 30346, 30190, 30356, 30213, 30220, 30227, 30366, 30376, 30386, 30396, 30406, 0}, {30336, 30346, 30190, 30356, 30213, 30220, 30227, 30366, 30376, 30386, 30396, 30406, 0}, 0, 0, 185, 187, {198,1129,0,0,0,0,0,0,0,0,0,0,0,0},{3502,3520,0,0,0,0,0,0,0,0},{3532,251,0,0,0,0,0,0,0,0,0,0},{3540,269,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{289, 30416, 30426, {30439, 30446, 30452, 30459, 30465, 30471, 30479}, {30489, 30493, 30497, 30501, 30505, 30509, 30513}, {12692, 12692, 12692, 12692, 12692, 12692, 12692}, {30519, 30528, 30537, 30542, 30548, 30554, 30560, 30567, 30573, 30582, 30591, 30599, 0}, {30519, 30528, 30537, 30542, 30548, 30554, 30560, 30567, 30573, 30582, 30591, 30599, 0}, {30608, 30612, 3699, 30616, 3668, 30620, 30624, 30628, 30633, 30637, 30643, 30647, 0}, {30608, 30612, 3699, 30616, 3668, 30620, 30624, 30628, 30633, 30637, 30643, 30647, 0}, 0, 0, 2438, 187, {2440,2451,1556,0,0,0,0,0,0,0,0,0,0,0},{10501,222,0,0,0,0,0,0,0,0},{843,251,0,0,0,0,0,0,0,0,0,0},{848,269,0,0,0,0,0,0,0},{278,0,0,0,0,0,0,0}},
	{1, 30651, 30692, {30733, 30752, 30771, 30796, 30815, 30837, 30862}, {30881, 17574, 30891, 17597, 30907, 17620, 17636}, {30920, 17650, 30924, 17664, 30928, 17678, 17685}, {17689, 30935, 17724, 30963, 25642, 30982, 17776, 30992, 31008, 31039, 31061, 31086, 0}, {17689, 30935, 17724, 30963, 25642, 30982, 17776, 30992, 31008, 31039, 31061, 31086, 0}, {17689, 30935, 17724, 30963, 25642, 30982, 17776, 30992, 31008, 31039, 31061, 31086, 0}, {17689, 30935, 17724, 30963, 25642, 30982, 17776, 30992, 31008, 31039, 31061, 31086, 0}, 0, 0, 185, 187, {3711,3720,3727,3736,1599,1556,3747,0,0,0,0,0,0,0},{31111,31131,222,10501,0,0,0,0,0,0},{3532,242,843,251,0,0,0,0,0,0,0,0},{3540,257,848,269,0,0,0,0,0},{25522,0,0,0,0,0,0,0}},
	{289, 0, 0, {0, 0, 0, 0, 0, 0, 0}, {0, 0, 0, 0, 0, 0, 0}, {0, 0, 0, 0, 0, 0, 0}, {31145, 31152, 31161, 31176, 31187, 31196, 31203, 31210, 31217, 31228, 31241, 31254, 0}, {31145, 31152, 31161, 31176, 31187, 31196, 31203, 31210, 31217, 31228, 31241, 31254, 0}, {6528, 6530, 6532, 6534, 6536, 6538, 6540, 6542, 6544, 6546, 6549, 6552, 0}, {6528, 6530, 6532, 6534, 6536, 6538, 6540, 6542, 6544, 6546, 6549, 6552, 0}, 0, 6, 185, 187, {1518,1556,0,0,0,0,0,0,0,0,0,0,0,0},{3520,4935,0,0,0,0,0,0,0,0},{843,0,0,0,0,0,0,0,0,0,0,0},{848,0,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{3551, 1820, 1823, {31259, 31266, 31272, 31279, 31290, 31298, 31307}, {31314, 8298, 3699, 31318, 31322, 31326, 10582}, {2308, 2308, 2300, 2300, 5644, 16338, 1914}, {3929, 31330, 31338, 3949, 3955, 31344, 31350, 3972, 31356, 31366, 31374, 31384, 0}, {3929, 31330, 31338, 3949, 3955, 31344, 31350, 3972, 31356, 31366, 31374, 31384, 0}, {31394, 31398, 3699, 29229, 3668, 31402, 31406, 19914, 18218, 2684, 31410, 16295, 0}, {31394, 31398, 3699, 29229, 3668, 31402, 31406, 19914, 18218, 2684, 31410, 16295, 0}, 0, 0, 185, 187, {3711,16480,0,0,0,0,0,0,0,0,0,0,0,0},{3757,3776,0,0,0,0,0,0,0,0},{3532,251,0,0,0,0,0,0,0,0,0,0},{3540,269,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{3551, 0, 0, {31414, 31421, 31429, 31436, 31443, 31451, 31459}, {31466, 31469, 31472, 31475, 31478, 31481, 31484}, {2308, 2308, 2302, 2308, 12692, 3925, 12692}, {31487, 31495, 31505, 31511, 31519, 31524, 31529, 31534, 31541, 19899, 31549, 31557, 0}, {31487, 31495, 31505, 31511, 31519, 31524, 31529, 31534, 31541, 19899, 31549, 31557, 0}, {2651, 31565, 3699, 31569, 3668, 30620, 30624, 31573, 3639, 2684, 31577, 16295, 0}, {2651, 31565, 3699, 31569, 3668, 30620, 30624, 31573, 3639, 2684, 31577, 16295, 0}, 0, 0, 185, 187, {1120,17272,0,0,0,0,0,0,0,0,0,0,0,0},{3789,3808,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{3551, 31581, 31592, {31602, 31620, 31635, 31657, 31670, 31684, 31701}, {31725, 31733, 31738, 31657, 31670, 31750, 31757}, {0, 0, 0, 0, 0, 0, 0}, {31771, 31793, 31809, 31829, 31843, 31860, 31875, 31892, 31906, 31919, 31938, 31952, 0}, {31771, 31793, 31809, 31829, 31843, 31860, 31875, 31892, 31906, 31919, 31938, 31952, 0}, {31971, 31986, 31995, 32008, 32015, 32025, 32033, 32043, 32050, 32056, 32068, 32075, 0}, {31971, 31986, 31995, 32008, 32015, 32025, 32033, 32043, 32050, 32056, 32068, 32075, 0}, 0, 0, 185, 187, {198,1129,0,0,0,0,0,0,0,0,0,0,0,0},{3502,3520,0,0,0,0,0,0,0,0},{3532,251,0,0,0,0,0,0,0,0,0,0},{3540,269,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{3551, 0, 0, {32087, 32095, 15840, 15849, 15858, 32106, 32116}, {16326, 15890, 15894, 15898, 15902, 16189, 32125}, {0, 0, 0, 0, 0, 0, 0}, {32129, 32138, 32148, 32156, 10611, 3672, 32164, 32170, 32179, 32188, 32197, 32206, 0}, {32129, 32138, 32148, 32156, 10611, 3672, 32164, 32170, 32179, 32188, 32197, 32206, 0}, {2651, 2655, 15909, 32215, 10611, 2668, 2672, 19914, 18218, 2684, 32219, 16295, 0}, {2651, 2655, 15909, 32215, 10611, 2668, 2672, 19914, 18218, 2684, 32219, 16295, 0}, 0, 0, 2438, 187, {1556,15492,0,0,0,0,0,0,0,0,0,0,0,0},{16078,16096,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{12916,0,0,0,0,0,0,0}},
	{32223, 32236, 32241, {32246, 32253, 32268, 32282, 32298, 32313, 32329}, {6212, 32344, 2402, 32348, 32352, 32356, 32360}, {1914, 12692, 2300, 1909, 1914, 2302, 12692}, {7350, 7358, 32364, 32371, 32378, 2341, 2346, 32383, 32393, 32404, 32413, 32423, 0}, {7350, 7358, 32364, 32371, 32378, 2341, 2346, 32383, 32393, 32404, 32413, 32423, 0}, {2394, 2398, 2402, 2406, 2337, 2410, 2414, 2418, 2422, 2426, 2430, 2434, 0}, {2394, 2398, 2402, 2406, 2337, 2410, 2414, 2418, 2422, 2426, 2430, 2434, 0}, 0, 0, 2438, 187, {2440,2451,1556,2460,0,0,0,0,0,0,0,0,0,0},{32433,2194,11931,0,0,0,0,0,0,0},{251,843,0,0,0,0,0,0,0,0,0,0},{269,848,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{3551, 32452, 32457, {32462, 32478, 32486, 32494, 32503, 32515, 32525}, {32535, 32541, 32547, 32551, 32555, 32563, 3639}, {0, 0, 0, 0, 0, 0, 0}, {32570, 32583, 32597, 32606, 32612, 32616, 32621, 32629, 16250, 32642, 16267, 16275, 0}, {32570, 32583, 32597, 32606, 32612, 32616, 32621, 32629, 16250, 32642, 16267, 16275, 0}, {32651, 2655, 32655, 16283, 32612, 32659, 2672, 32663, 2680, 32671, 2688, 16295, 0}, {32651, 2655, 32655, 16283, 32612, 32659, 2672, 32663, 2680, 32671, 2688, 16295, 0}, 0, 0, 185, 187, {198,1129,0,0,0,0,0,0,0,0,0,0,0,0},{3502,3520,0,0,0,0,0,0,0,0},{3532,251,0,0,0,0,0,0,0,0,0,0},{3540,269,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{32677, 32700, 32707, {32714, 32724, 32734, 32744, 32754, 32764, 32774}, {32784, 32791, 32798, 32805, 32812, 32819, 32826}, {32833, 32837, 32841, 32845, 32849, 32853, 32857}, {32861, 32868, 32875, 32882, 32889, 32896, 32903, 32910, 32917, 32924, 32931, 32941, 0}, {32861, 32868, 32875, 32882, 32889, 32896, 32903, 32910, 32917, 32924, 32931, 32941, 0}, {6528, 6530, 6532, 6534, 6536, 6538, 6540, 6542, 6544, 6546, 6549, 6552, 0}, {6528, 6530, 6532, 6534, 6536, 6538, 6540, 6542, 6544, 6546, 6549, 6552, 0}, 0, 0, 185, 187, {1518,1527,1536,1567,1556,1545,0,0,0,0,0,0,0,0},{32951,32975,33005,33035,33052,0,0,0,0,0},{1722,843,251,0,0,0,0,0,0,0,0,0},{1739,848,269,0,0,0,0,0,0},{33075,1805,0,0,0,0,0,0}},
	{289, 0, 0, {27951, 8298, 33092, 33099, 33109, 33114, 33121}, {33128, 6196, 33132, 4800, 33137, 33142, 33147}, {4392, 33152, 33155, 33158, 33161, 33164, 33167}, {33170, 33177, 33092, 33188, 33194, 33198, 33207, 33214, 33219, 33228, 33233, 33236, 0}, {33170, 33177, 33092, 33188, 33194, 33198, 33207, 33214, 33219, 33228, 33233, 33236, 0}, {33242, 33246, 33253, 28074, 33194, 33258, 33263, 33214, 33268, 33228, 33233, 33273, 0}, {33242, 33246, 33253, 28074, 33194, 33258, 33263, 33214, 33268, 33228, 33233, 33273, 0}, 2, 1, 2438, 187, {1556,15492,0,0,0,0,0,0,0,0,0,0,0,0},{16078,16096,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{1812, 33277, 33282, {33287, 33295, 33305, 33315, 33324, 33334, 33342}, {33352, 33356, 33361, 33365, 33369, 33373, 33377}, {1914, 2300, 2565, 2300, 2565, 2306, 1914}, {2567, 2574, 2582, 2588, 2594, 2598, 2603, 33381, 33390, 33401, 33410, 33420, 0}, {2567, 2574, 2582, 2588, 2594, 2598, 2603, 33381, 33390, 33401, 33410, 33420, 0}, {2651, 2655, 2659, 2664, 2594, 2668, 2672, 2676, 2680, 2684, 2688, 2692, 0}, {2651, 2655, 2659, 2664, 2594, 2668, 2672, 2676, 2680, 2684, 2688, 2692, 0}, 0, 0, 185, 187, {198,189,2696,2451,1556,0,0,0,0,0,0,0,0,0},{4935,4952,3520,0,0,0,0,0,0,0},{251,843,4246,4961,4971,0,0,0,0,0,0,0},{269,848,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{33430, 33446, 33451, {33456, 33481, 33510, 33535, 33548, 33563, 33580}, {33595, 33600, 33605, 33610, 33615, 33620, 8599}, {19210, 19210, 33625, 8610, 8613, 19210, 8610}, {33628, 33647, 33662, 33684, 33704, 33720, 33736, 33750, 33776, 33800, 33817, 33834, 0}, {33628, 33647, 33662, 33684, 33704, 33720, 33736, 33750, 33776, 33800, 33817, 33834, 0}, {33851, 33860, 33869, 33883, 33897, 33909, 33921, 33931, 33947, 33961, 33968, 33975, 0}, {33851, 33860, 33869, 33883, 33897, 33909, 33921, 33931, 33947, 33961, 33968, 33975, 0}, 0, 1, 691, 187, {2154,4724,1556,33982,33992,0,0,0,0,0,0,0,0,0},{34005,34040,34069,0,0,0,0,0,0,0},{843,251,0,0,0,0,0,0,0,0,0,0},{848,269,0,0,0,0,0,0,0},{34104,0,0,0,0,0,0,0}},
	{3551, 0, 0, {34120, 34132, 34143, 34155, 34167, 34177, 34189}, {34204, 34209, 34214, 34219, 34224, 34229, 34234}, {0, 0, 0, 0, 0, 0, 0}, {34239, 34248, 34260, 34268, 34273, 34283, 34290, 34299, 34306, 34312, 34321, 34332, 0}, {34239, 34248, 34260, 34268, 34273, 34283, 34290, 34299, 34306, 34312, 34321, 34332, 0}, {34340, 34345, 34350, 34355, 34360, 34365, 34370, 34224, 34375, 34380, 34385, 34390, 0}, {34340, 34345, 34350, 34355, 34360, 34365, 34370, 34224, 34375, 34380, 34385, 34390, 0}, 0, 0, 185, 187, {1545,15492,0,0,0,0,0,0,0,0,0,0,0,0},{20050,16096,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{12916,0,0,0,0,0,0,0}},
	{34395, 34406, 34408, {34410, 34423, 34431, 34440, 34450, 34460, 34469}, {34481, 34485, 34489, 34493, 34497, 34501, 34505}, {2565, 2308, 2300, 7600, 12692, 5644, 1914}, {34509, 34523, 34534, 34543, 34554, 34566, 34580, 34592, 34605, 34618, 34630, 34643, 0}, {34509, 34523, 34534, 34543, 34554, 34566, 34580, 34592, 34605, 34618, 34630, 34643, 0}, {34657, 34662, 34668, 34674, 34679, 34685, 34691, 34696, 34702, 34707, 18931, 34713, 0}, {34657, 34662, 34668, 34674, 34679, 34685, 34691, 34696, 34702, 34707, 18931, 34713, 0}, 2, 1, 185, 187, {198,1129,0,0,0,0,0,0,0,0,0,0,0,0},{34719,34741,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{1, 0, 0, {0, 0, 0, 0, 0, 0, 0}, {0, 0, 0, 0, 0, 0, 0}, {0, 0, 0, 0, 0, 0, 0}, {9, 18, 25, 45, 65, 89, 113, 120, 131, 142, 151, 169, 0}, {9, 18, 25, 45, 65, 89, 113, 120, 131, 142, 151, 169, 0}, {9, 18, 25, 45, 65, 89, 113, 120, 131, 142, 151, 169, 0}, {9, 18, 25, 45, 65, 89, 113, 120, 131, 142, 151, 169, 0}, 0, 0, 185, 187, {189,198,0,0,0,0,0,0,0,0,0,0,0,0},{209,222,0,0,0,0,0,0,0,0},{242,251,0,0,0,0,0,0,0,0,0,0},{257,269,0,0,0,0,0,0,0},{278,0,0,0,0,0,0,0}},
	{289, 296, 307, {318, 331, 352, 367, 378, 397, 408}, {421, 426, 431, 436, 441, 446, 451}, {456, 459, 462, 465, 468, 459, 465}, {471, 484, 501, 510, 521, 528, 535, 542, 555, 574, 591, 606, 0}, {471, 484, 501, 510, 521, 528, 535, 542, 555, 574, 591, 606, 0}, {623, 629, 501, 639, 521, 528, 535, 647, 655, 665, 673, 683, 0}, {623, 629, 501, 639, 521, 528, 535, 647, 655, 665, 673, 683, 0}, 2, 1, 691, 187, {693,708,724,740,0,0,0,0,0,0,0,0,0,0},{757,776,794,819,0,0,0,0,0,0},{843,251,0,0,0,0,0,0,0,0,0,0},{848,269,0,0,0,0,0,0,0},{856,0,0,0,0,0,0,0}},
	{289, 872, 878, {884, 893, 901, 909, 918, 925, 935}, {944, 948, 952, 956, 960, 964, 968}, {972, 975, 978, 981, 984, 987, 990}, {993, 999, 1006, 1012, 1018, 1023, 1028, 1035, 1041, 1050, 1058, 1067, 0}, {993, 999, 1006, 1012, 1018, 1023, 1028, 1035, 1041, 1050, 1058, 1067, 0}, {1076, 1081, 1006, 1086, 1018, 1023, 1091, 1096, 1100, 1105, 1110, 1115, 0}, {1076, 1081, 1006, 1086, 1018, 1023, 1091, 1096, 1100, 1105, 1110, 1115, 0}, 2, 1, 185, 187, {1120,1129,0,0,0,0,0,0,0,0,0,0,0,0},{1140,1163,0,0,0,0,0,0,0,0},{843,251,0,0,0,0,0,0,0,0,0,0},{848,269,0,0,0,0,0,0,0},{1180,0,0,0,0,0,0,0}},
	{1195, 1204, 1211, {1218, 1228, 1238, 1248, 1258, 1268, 1278}, {34757, 34764, 34771, 34778, 34785, 34792, 34799}, {1337, 1341, 1345, 1349, 1353, 1357, 1361}, {1455, 1460, 1465, 1470, 1475, 1480, 1485, 1490, 1495, 1500, 1506, 1512, 0}, {1455, 1460, 1465, 1470, 1475, 1480, 1485, 1490, 1495, 1500, 1506, 1512, 0}, {1455, 1460, 1465, 1470, 1475, 1480, 1485, 1490, 1495, 1500, 1506, 1512, 0}, {1455, 1460, 1465, 1470, 1475, 1480, 1485, 1490, 1495, 1500, 1506, 1512, 0}, 0, 0, 185, 187, {1518,1545,1556,0,0,0,0,0,0,0,0,0,0,0},{1608,6555,1686,34806,0,0,0,0,0,0},{1730,1722,251,843,0,0,0,0,0,0,0,0},{1750,1739,269,848,0,0,0,0,0},{1762,1778,1791,0,0,0,0,0}},
	{1812, 1820, 1823, {1826, 1834, 1844, 1852, 1860, 1869, 1876}, {1883, 1886, 1889, 1893, 1896, 1900, 1904}, {1907, 1909, 1911, 1914, 1916, 1909, 1914}, {1919, 1925, 1931, 1939, 1945, 1953, 1961, 1971, 1977, 1985, 1993, 2002, 0}, {2011, 2017, 2024, 2032, 2038, 2046, 2054, 2064, 1977, 2070, 2078, 2088, 0}, {2097, 2101, 2106, 2111, 2115, 2120, 2125, 2130, 2134, 2140, 2146, 2150, 0}, {2097, 2101, 2106, 2111, 2115, 2120, 2125, 2130, 2134, 2140, 2146, 2150, 0}, 2, 1, 691, 187, {2154,2165,0,0,0,0,0,0,0,0,0,0,0,0},{2176,2194,0,0,0,0,0,0,0,0},{843,0,0,0,0,0,0,0,0,0,0,0},{848,0,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{1812, 1820, 1823, {2217, 2225, 2232, 2240, 2247, 2255, 2262}, {2270, 2275, 2279, 2283, 2287, 2291, 2295}, {1914, 2300, 2302, 2304, 2302, 2306, 2308}, {2310, 2317, 2325, 2331, 2337, 2341, 2346, 2351, 2358, 2368, 2376, 2385, 0}, {2310, 2317, 2325, 2331, 2337, 2341, 2346, 2351, 2358, 2368, 2376, 2385, 0}, {2394, 2398, 2402, 2406, 2337, 2410, 2414, 2418, 2422, 2426, 2430, 2434, 0}, {2394, 2398, 2402, 2406, 2337, 2410, 2414, 2418, 2422, 2426, 2430, 2434, 0}, 2, 1, 2438, 187, {2440,2451,1556,2460,0,0,0,0,0,0,0,0,0,0},{2194,0,0,0,0,0,0,0,0,0},{251,843,0,0,0,0,0,0,0,0,0,0},{269,848,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{1812, 2471, 2477, {2484, 2492, 2499, 2508, 2517, 2528, 2536}, {2544, 2547, 2550, 2553, 2556, 2559, 2562}, {1914, 2300, 2565, 2300, 2565, 2306, 1914}, {2567, 2574, 2582, 2588, 2594, 2598, 2603, 2608, 2615, 2625, 2633, 2642, 0}, {2567, 2574, 2582, 2588, 2594, 2598, 2603, 2608, 2615, 2625, 2633, 2642, 0}, {2651, 2655, 2659, 2664, 2594, 2668, 2672, 2676, 2680, 2684, 2688, 2692, 0}, {2651, 2655, 2659, 2664, 2594, 2668, 2672, 2676, 2680, 2684, 2688, 2692, 0}, 2, 1, 691, 187, {2154,2696,1556,2705,0,0,0,0,0,0,0,0,0,0},{2719,2194,2738,0,0,0,0,0,0,0},{251,2751,0,0,0,0,0,0,0,0,0,0},{269,2763,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{289, 2778, 2785, {2792, 2807, 2822, 2833, 2848, 2861, 2880}, {2895, 2902, 2909, 2916, 2923, 2930, 2937}, {2944, 2947, 2950, 2950, 2953, 2953, 2956}, {2959, 2980, 3003, 3018, 3035, 3046, 3061, 3076, 3095, 3118, 3137, 3156, 0}, {3177, 3198, 3221, 3236, 3253, 3264, 3279, 3294, 3313, 3336, 3355, 3374, 0}, {3395, 3402, 3409, 3416, 3423, 3430, 3439, 3448, 3455, 3462, 3469, 3476, 0}, {3395, 3402, 3409, 3416, 3423, 3430, 3439, 3448, 3455, 3462, 3469, 3476, 0}, 2, 1, 185, 187, {1120,198,3483,189,3490,1556,0,0,0,0,0,0,0,0},{3502,3520,0,0,0,0,0,0,0,0},{3532,242,843,251,0,0,0,0,0,0,0,0},{3540,257,848,269,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{3551, 1820, 1823, {3558, 3565, 3572, 3580, 3590, 3599, 3606}, {3615, 3619, 3623, 3627, 3631, 3635, 3639}, {1914, 2300, 2302, 3643, 2302, 2306, 1914}, {3645, 3653, 3662, 2588, 3668, 3672, 3677, 2608, 2615, 3682, 2633, 3690, 0}, {3645, 3653, 3662, 2588, 3668, 3672, 3677, 2608, 2615, 3682, 2633, 3690, 0}, {2651, 2655, 3699, 2664, 3668, 2668, 2672, 2676, 2680, 3703, 2688, 3707, 0}, {2651, 2655, 3699, 2664, 3668, 2668, 2672, 2676, 2680, 3703, 2688, 3707, 0}, 0, 0, 185, 187, {3711,3720,3727,3736,1599,1556,3747,0,0,0,0,0,0,0},{3757,3776,3789,3808,0,0,0,0,0,0},{3532,242,843,251,0,0,0,0,0,0,0,0},{3540,257,848,269,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{1812, 4303, 4307, {4311, 4323, 4335, 4345, 4359, 4369, 4381}, {4392, 4395, 4398, 4401, 4404, 4407, 4410}, {1914, 2300, 2302, 4413, 2302, 1909, 2308}, {4415, 4424, 4433, 4443, 4452, 4461, 4470, 4480, 4487, 4495, 4503, 4513, 0}, {4522, 4533, 4544, 4556, 4567, 4578, 4589, 4601, 4610, 4620, 4630, 4642, 0}, {4653, 4659, 4665, 4672, 4678, 4684, 4690, 4697, 4701, 4706, 4711, 4718, 0}, {4653, 4659, 4665, 4672, 4678, 4684, 4690, 4697, 4701, 4706, 4711, 4718, 0}, 2, 1, 691, 691, {4724,0,0,0,0,0,0,0,0,0,0,0,0,0},{2176,2194,0,0,0,0,0,0,0,0},{4241,0,0,0,0,0,0,0,0,0,0,0},{4260,0,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{289, 1820, 1823, {4733, 4742, 4748, 4754, 4763, 4769, 4778}, {4785, 4790, 4795, 4800, 4805, 4810, 4815}, {2565, 2308, 2300, 2300, 3925, 3927, 1914}, {4820, 4828, 4837, 4842, 4848, 4852, 4857, 4865, 4871, 4881, 1058, 4889, 0}, {4820, 4828, 4837, 4842, 4848, 4852, 4857, 4865, 4871, 4881, 1058, 4889, 0}, {4899, 4905, 4837, 4912, 4848, 4852, 4917, 4865, 4923, 1105, 1110, 4929, 0}, {4899, 4905, 4837, 4912, 4848, 4852, 4917, 4865, 4923, 1105, 1110, 4929, 0}, 2, 1, 185, 187, {198,189,2696,2451,1556,0,0,0,0,0,0,0,0,0},{4935,3520,4952,0,0,0,0,0,0,0},{251,843,4246,4961,4971,0,0,0,0,0,0,0},{269,848,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{1, 4979, 4992, {5003, 5021, 5035, 5053, 5071, 5089, 5105}, {5119, 5131, 5143, 5155, 5167, 5179, 5191}, {5198, 5203, 5208, 5213, 5218, 5223, 5228}, {5233, 5244, 5257, 5264, 5275, 5282, 5291, 5300, 5313, 5326, 5341, 5354, 0}, {5233, 5244, 5257, 5264, 5275, 5282, 5291, 5300, 5313, 5326, 5341, 5354, 0}, {5365, 5374, 5257, 5383, 5275, 5392, 5401, 5410, 5419, 5428, 5437, 5446, 0}, {5365, 5374, 5257, 5383, 5275, 5392, 5401, 5410, 5419, 5428, 5437, 5446, 0}, 0, 0, 185, 187, {198,5455,189,209,2451,2440,5468,1556,5481,5498,0,0,0,0},{5508,5455,5526,5548,5481,0,0,0,0,0},{251,242,0,0,0,0,0,0,0,0,0,0},{269,257,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{5569, 5577, 5581, {5585, 5595, 5603, 5608, 5615, 5628, 5636}, {3927, 5644, 4413, 5646, 5650, 1909, 5653}, {3927, 5644, 4413, 5657, 5650, 1909, 5657}, {5660, 5668, 5677, 5686, 5695, 5702, 5710, 5718, 5728, 5739, 2376, 2385, 0}, {5660, 5668, 5677, 5686, 5695, 5702, 5710, 5718, 5728, 5739, 2376, 2385, 0}, {5748, 5753, 5759, 5766, 5772, 5778, 5784, 5790, 5795, 5802, 1110, 5807, 0}, {5748, 5753, 5759, 5766, 5772, 5778, 5784, 5790, 5795, 5802, 1110, 5807, 0}, 2, 1, 5812, 187, {5815,5829,0,0,0,0,0,0,0,0,0,0,0,0},{5842,5862,0,0,0,0,0,0,0,0},{843,0,0,0,0,0,0,0,0,0,0,0},{848,0,0,0,0,0,0,0,0},{5876,0,0,0,0,0,0,0}},
	{1812, 5887, 5892, {5897, 5908, 5919, 5933, 5947, 5959, 5971}, {5983, 5988, 5994, 6000, 6006, 6011, 6017}, {1914, 2300, 6022, 2300, 2306, 2306, 2308}, {6025, 6033, 4837, 6042, 6049, 6054, 6061, 6068, 2358, 5739, 6076, 6086, 0}, {6025, 6033, 4837, 6042, 6049, 6054, 6061, 6068, 2358, 5739, 6076, 6086, 0}, {5748, 1081, 4795, 6095, 6049, 5778, 5784, 6100, 6107, 5802, 6112, 1115, 0}, {5748, 1081, 4795, 6095, 6049, 5778, 5784, 6100, 6107, 5802, 6112, 1115, 0}, 2, 1, 691, 187, {4724,6118,0,0,0,0,0,0,0,0,0,0,0,0},{2719,2194,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{289, 1820, 1823, {6130, 6139, 6147, 6156, 6167, 6176, 6185}, {6192, 6196, 2402, 6200, 6204, 6208, 6212}, {2565, 2308, 2300, 2300, 6216, 3927, 1914}, {6218, 6226, 3943, 6235, 6242, 6249, 6256, 3972, 6263, 6273, 6281, 6290, 0}, {6299, 6307, 4032, 6316, 6323, 6330, 6337, 4055, 6344, 6354, 1058, 6362, 0}, {6371, 2398, 2402, 2406, 6375, 6379, 6383, 6387, 6391, 6395, 2430, 6399, 0}, {6371, 2398, 2402, 2406, 6375, 6379, 6383, 6387, 6391, 6395, 2430, 6399, 0}, 2, 1, 185, 187, {198,3490,189,6403,3483,0,0,0,0,0,0,0,0,0},{4935,6411,3520,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{1195, 6420, 6427, {6434, 6444, 6454, 6464, 6474, 6484, 6494}, {1337, 6504, 6508, 6512, 6516, 6520, 6524}, {1337, 6504, 6508, 6512, 6516, 6520, 6524}, {1455, 1460, 1465, 1470, 1475, 1480, 1485, 1490, 1495, 1500, 1506, 1512, 0}, {1455, 1460, 1465, 1470, 1475, 1480, 1485, 1490, 1495, 1500, 1506, 1512, 0}, {6528, 6530, 6532, 6534, 6536, 6538, 6540, 6542, 6544, 6546, 6549, 6552, 0}, {6528, 6530, 6532, 6534, 6536, 6538, 6540, 6542, 6544, 6546, 6549, 6552, 0}, 0, 0, 185, 187, {1545,1599,1578,1518,1556,0,0,0,0,0,0,0,0,0},{1608,6555,6579,6606,6635,6659,6688,6708,0,0},{843,251,1722,1730,0,0,0,0,0,0,0,0},{848,269,1739,1750,0,0,0,0,0},{1762,6733,1791,0,0,0,0,0}},
	{6751, 6761, 6768, {6775, 6785, 6795, 6805, 6815, 6825, 6835}, {6845, 6849, 6853, 6857, 6861, 6865, 6869}, {6845, 6849, 6853, 6857, 6861, 6865, 6869}, {6873, 6878, 6883, 6888, 6893, 6898, 6903, 6908, 6913, 6918, 6924, 6930, 0}, {6873, 6878, 6883, 6888, 6893, 6898, 6903, 6908, 6913, 6918, 6924, 6930, 0}, {6873, 6878, 6883, 6888, 6893, 6898, 6903, 6908, 6913, 6918, 6924, 6930, 0}, {6873, 6878, 6883, 6888, 6893, 6898, 6903, 6908, 6913, 6918, 6924, 6930, 0}, 0, 0, 2438, 187, {1556,6936,1585,1527,0,0,0,0,0,0,0,0,0,0},{6945,6974,6998,7025,7047,7078,7104,7135,7161,7188},{1722,1730,843,251,0,0,0,0,0,0,0,0},{1739,1750,848,269,0,0,0,0,0},{7210,7227,7246,0,0,0,0,0}},
	{289, 7261, 7266, {7271, 7278, 7286, 7294, 7303, 7313, 7321}, {7330, 4395, 7333, 7336, 7339, 7342, 7345}, {7348, 2300, 2565, 3643, 2565, 3927, 7348}, {7350, 7358, 7367, 2331, 7373, 2341, 2346, 7377, 2358, 2368, 2376, 2385, 0}, {7350, 7358, 7367, 2331, 7373, 2341, 2346, 7377, 2358, 2368, 2376, 2385, 0}, {2394, 2398, 7386, 2406, 7373, 2410, 2414, 2418, 2422, 2426, 2430, 2434, 0}, {2394, 2398, 7386, 2406, 7373, 2410, 2414, 2418, 2422, 2426, 2430, 2434, 0}, 2, 1, 2438, 187, {7390,4157,2451,189,2696,7399,1556,0,0,0,0,0,0,0},{4935,6411,3520,4952,0,0,0,0,0,0},{251,843,4241,7411,7423,0,0,0,0,0,0,0},{269,848,7435,7450,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{7465, 7261, 7266, {2217, 2225, 2232, 2240, 2247, 2255, 2262}, {7473, 7478, 7482, 7486, 7490, 7494, 7498}, {1914, 2300, 2302, 2304, 2302, 2306, 2308}, {2310, 2317, 4837, 2331, 4848, 2341, 2346, 2351, 2358, 2368, 2376, 6086, 0}, {2310, 2317, 4837, 2331, 4848, 2341, 2346, 2351, 2358, 2368, 2376, 6086, 0}, {2394, 2398, 2402, 2406, 4848, 2410, 2414, 2418, 2422, 2426, 2430, 7503, 0}, {2394, 2398, 2402, 2406, 4848, 2410, 2414, 2418, 2422, 2426, 2430, 7503, 0}, 2, 1, 691, 691, {2154,6118,0,0,0,0,0,0,0,0,0,0,0,0},{2176,2194,0,0,0,0,0,0,0,0},{4246,0,0,0,0,0,0,0,0,0,0,0},{4268,0,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{289, 1820, 1823, {7507, 7517, 7531, 7538, 7545, 7554, 1876}, {7562, 7569, 7574, 7578, 7583, 7588, 7592}, {1907, 1909, 3643, 7597, 7600, 1909, 1914}, {7602, 7611, 7616, 7623, 2337, 7633, 7642, 7649, 7659, 7669, 1993, 7682, 0}, {7692, 7701, 7708, 7714, 7723, 7728, 7736, 7742, 7751, 7761, 7775, 7785, 0}, {7793, 7797, 2402, 7801, 2337, 7805, 7809, 7813, 7817, 7821, 2146, 7826, 0}, {7793, 7797, 2402, 7801, 2337, 7805, 7809, 7813, 7817, 7821, 2146, 7826, 0}, 2, 1, 691, 187, {2154,0,0,0,0,0,0,0,0,0,0,0,0,0},{3502,3520,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{3821, 1820, 1823, {3833, 7830, 7844, 7857, 7870, 7883, 3880}, {6192, 7895, 7899, 7903, 7907, 7911, 7915}, {2565, 1914, 2302, 7920, 7920, 1914, 1914}, {7922, 7930, 7940, 1012, 7947, 7952, 7958, 4055, 7964, 7973, 7981, 7990, 0}, {7922, 7930, 7940, 1012, 7947, 7952, 7958, 4055, 7964, 7973, 7981, 7990, 0}, {2394, 7999, 2402, 8003, 4848, 2410, 2414, 6387, 6391, 8007, 2430, 8011, 0}, {2394, 7999, 2402, 8003, 4848, 2410, 2414, 6387, 6391, 8007, 2430, 8011, 0}, 0, 0, 185, 187, {198,189,1120,3483,2451,2440,4157,7390,2696,2154,7399,8015,4724,1556},{4164,4219,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{4288,0,0,0,0,0,0,0}},
	{1812, 8022, 8025, {8028, 8037, 4748, 8047, 8055, 8063, 8072}, {8078, 8081, 4395, 8085, 8088, 8092, 1904}, {2565, 6216, 2300, 2300, 6216, 3927, 1914}, {8095, 8103, 4837, 8110, 8117, 8122, 8132, 8140, 8146, 8156, 2376, 2385, 0}, {8095, 8103, 4837, 8110, 8117, 8122, 8132, 8140, 8146, 8156, 2376, 2385, 0}, {8164, 8171, 4837, 4912, 8117, 8177, 8184, 8140, 8189, 1105, 1110, 5807, 0}, {8164, 8171, 4837, 4912, 8117, 8177, 8184, 8140, 8189, 1105, 1110, 5807, 0}, 2, 1, 2438, 187, {2440,0,0,0,0,0,0,0,0,0,0,0,0,0},{8195,8224,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{289, 7261, 7266, {8241, 8251, 8256, 8263, 8272, 8276, 8283}, {8294, 8298, 3699, 8302, 8306, 8310, 8314}, {2565, 2308, 2300, 2300, 3925, 3927, 1914}, {8319, 8328, 8338, 8345, 4848, 8353, 8359, 2351, 8365, 8376, 8386, 8396, 0}, {8319, 8328, 8338, 8345, 4848, 8353, 8359, 2351, 8365, 8376, 8386, 8396, 0}, {8406, 1081, 4795, 6095, 4848, 8411, 8416, 5790, 4923, 1105, 1110, 5807, 0}, {8406, 1081, 4795, 6095, 4848, 8411, 8416, 5790, 4923, 1105, 1110, 5807, 0}, 0, 1, 691, 187, {2154,1129,0,0,0,0,0,0,0,0,0,0,0,0},{3502,3520,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{289, 8421, 8441, {8467, 8490, 352, 8513, 8524, 8539, 8554}, {8569, 8574, 8579, 8584, 8589, 8594, 8599}, {8604, 8607, 8604, 8610, 8613, 8607, 8610}, {8616, 8629, 8644, 8653, 8666, 8673, 8682, 8691, 8704, 8721, 8736, 8749, 0}, {8764, 8777, 8792, 8803, 8816, 8823, 8832, 8841, 8856, 8873, 8888, 8901, 0}, {8916, 8924, 8644, 8934, 8666, 8673, 8682, 8942, 8950, 8960, 8968, 8978, 0}, {8916, 8924, 8644, 8934, 8666, 8673, 8682, 8942, 8950, 8960, 8968, 8978, 0}, 0, 1, 691, 187, {2154,2696,8015,2440,189,0,0,0,0,0,0,0,0,0},{776,757,0,0,0,0,0,0,0,0},{843,251,0,0,0,0,0,0,0,0,0,0},{848,269,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{1812, 1820, 1823, {8986, 8995, 9007, 9014, 9022, 9032, 9038}, {9045, 9049, 9053, 9057, 9061, 9066, 9070}, {9074, 9076, 9078, 9080, 9082, 9076, 9080}, {9085, 9095, 9104, 9112, 9120, 9128, 9135, 9142, 9150, 1993, 9156, 9164, 0}, {9173, 9183, 9192, 9200, 9208, 9216, 9223, 9230, 9239, 7775, 9245, 9255, 0}, {9264, 9268, 9273, 9278, 9282, 7809, 2130, 9286, 9290, 2146, 9294, 2150, 0}, {9264, 9268, 9273, 9278, 9282, 7809, 2130, 9286, 9290, 2146, 9294, 2150, 0}, 0, 1, 691, 187, {9298,9308,9316,9328,9340,9350,9360,1556,0,0,0,0,0,0},{9372,9386,9401,0,0,0,0,0,0,0},{843,251,0,0,0,0,0,0,0,0,0,0},{848,269,0,0,0,0,0,0,0},{278,0,0,0,0,0,0,0}},
	{1812, 1820, 1823, {9421, 9429, 9438, 9445, 9452, 9461, 1876}, {1883, 1886, 9468, 1893, 9471, 9475, 1904}, {1907, 1909, 9478, 1914, 9480, 1909, 1914}, {5660, 5668, 9483, 6042, 9489, 9494, 9499, 2351, 2358, 5739, 2376, 2385, 0}, {9504, 9513, 7708, 9523, 9531, 9537, 9543, 9549, 9557, 9567, 9576, 9585, 0}, {2394, 2398, 2402, 2406, 9489, 9494, 9499, 2418, 2422, 2426, 2430, 2434, 0}, {2394, 2398, 2402, 2406, 9489, 9494, 9499, 2418, 2422, 2426, 2430, 2434, 0}, 2, 1, 691, 187, {2154,2165,0,0,0,0,0,0,0,0,0,0,0,0},{2719,2194,0,0,0,0,0,0,0,0},{843,0,0,0,0,0,0,0,0,0,0,0},{848,0,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{289, 9594, 9603, {9611, 9618, 9627, 9636, 9648, 9656, 9665}, {9675, 9679, 3699, 9684, 9689, 9693, 9697}, {2565, 5644, 2300, 2300, 9701, 1909, 1914}, {9703, 9709, 4837, 9716, 2337, 9722, 9730, 9737, 9743, 9751, 9757, 9765, 0}, {9703, 9709, 4837, 9716, 2337, 9722, 9730, 9737, 9743, 9751, 9757, 9765, 0}, {2651, 9773, 3699, 9777, 9781, 9785, 9789, 9793, 9697, 9797, 9801, 9806, 0}, {2651, 9773, 3699, 9777, 9781, 9785, 9789, 9793, 9697, 9797, 9801, 9806, 0}, 0, 1, 691, 187, {4724,1129,0,0,0,0,0,0,0,0,0,0,0,0},{3502,3520,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{9810, 9823, 9826, {9829, 9837, 9845, 2240, 2247, 2255, 9852}, {9860, 9865, 9870, 2283, 2287, 2291, 9874}, {1914, 2300, 2302, 2304, 2302, 2306, 2308}, {7350, 7358, 4837, 2331, 2337, 2341, 2346, 9879, 2358, 2368, 2376, 2385, 0}, {7350, 7358, 4837, 2331, 2337, 2341, 2346, 9879, 2358, 2368, 2376, 2385, 0}, {2394, 2398, 2402, 2406, 2337, 2410, 2414, 2418, 2422, 2426, 2430, 2434, 0}, {2394, 2398, 2402, 2406, 2337, 2410, 2414, 2418, 2422, 2426, 2430, 2434, 0}, 2, 1, 2438, 187, {1556,6936,0,0,0,0,0,0,0,0,0,0,0,0},{9887,9905,0,0,0,0,0,0,0,0},{251,843,9928,0,0,0,0,0,0,0,0,0},{269,848,9938,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{289, 0, 0, {0, 0, 0, 0, 0, 0, 0}, {0, 0, 0, 0, 0, 0, 0}, {0, 0, 0, 0, 0, 0, 0}, {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0}, {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0}, {6528, 6530, 6532, 6534, 6536, 6538, 6540, 6542, 6544, 6546, 6549, 6552, 0}, {6528, 6530, 6532, 6534, 6536, 6538, 6540, 6542, 6544, 6546, 6549, 6552, 0}, 0, 0, 185, 187, {1120,3483,189,198,1129,9951,1556,0,0,0,0,0,0,0},{3520,9963,9979,0,0,0,0,0,0,0},{843,251,3532,242,0,0,0,0,0,0,0,0},{848,269,3540,257,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{1, 10021, 10026, {10030, 10036, 10046, 10052, 10063, 10073, 10078}, {10088, 10092, 10096, 10100, 10105, 10109, 10113}, {1909, 1909, 1914, 10117, 1909, 7600, 7600}, {10120, 10125, 10132, 10137, 10143, 10150, 10158, 10165, 10174, 10181, 10186, 10193, 0}, {10120, 10125, 10132, 10137, 10143, 10150, 10158, 10165, 10174, 10181, 10186, 10193, 0}, {10201, 10205, 3699, 10210, 3668, 10214, 10218, 10222, 10227, 10231, 10235, 10239, 0}, {10201, 10205, 3699, 10210, 3668, 10214, 10218, 10222, 10227, 10231, 10235, 10239, 0}, 0, 1, 691, 187, {10243,1129,0,0,0,0,0,0,0,0,0,0,0,0},{10253,3520,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{1, 10270, 10288, {10306, 10317, 10330, 10339, 10346, 10359, 10368}, {10306, 10317, 10330, 10339, 10346, 10359, 10368}, {1914, 2300, 2302, 3643, 2302, 2306, 1914}, {10377, 10388, 10399, 10408, 10419, 10426, 10433, 10446, 10455, 10466, 10479, 10490, 0}, {10377, 10388, 10399, 10408, 10419, 10426, 10433, 10446, 10455, 10466, 10479, 10490, 0}, {10377, 10388, 10399, 10408, 10419, 10426, 10433, 10446, 10455, 10466, 10479, 10490, 0}, {10377, 10388, 10399, 10408, 10419, 10426, 10433, 10446, 10455, 10466, 10479, 10490, 0}, 0, 0, 185, 187, {198,189,1556,0,0,0,0,0,0,0,0,0,0,0},{10501,222,0,0,0,0,0,0,0,0},{3532,242,843,251,0,0,0,0,0,0,0,0},{3540,257,848,269,0,0,0,0,0},{278,0,0,0,0,0,0,0}},
	{289, 1820, 1823, {10515, 10522, 10528, 10535, 10540, 10546, 10552}, {10558, 10562, 10566, 10570, 10574, 10578, 10582}, {2300, 1914, 1914, 10586, 4413, 3925, 1914}, {10588, 10596, 10605, 2588, 10611, 2598, 2603, 10615, 2615, 2625, 2633, 10623, 0}, {10588, 10596, 10605, 2588, 10611, 2598, 2603, 10615, 2615, 2625, 2633, 10623, 0}, {2651, 2655, 3699, 2664, 10611, 2668, 2672, 10632, 2680, 2684, 2688, 10636, 0}, {2651, 2655, 3699, 2664, 10611, 2668, 2672, 10632, 2680, 2684, 2688, 10636, 0}, 0, 0, 185, 691, {198,1129,0,0,0,0,0,0,0,0,0,0,0,0},{10640,3520,0,0,0,0,0,0,0,0},{4246,0,0,0,0,0,0,0,0,0,0,0},{4268,0,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{289, 10659, 10664, {10669, 10682, 10701, 10718, 10731, 10744, 10761}, {10774, 8574, 8579, 8584, 8589, 8594, 8599}, {10779, 8607, 8604, 8610, 8613, 8607, 8610}, {10782, 10795, 10806, 10823, 10838, 10853, 10868, 10881, 10896, 10913, 10928, 10945, 0}, {10960, 10971, 10984, 10999, 11012, 11025, 11038, 11049, 11062, 11077, 11090, 11109, 0}, {11122, 11129, 11136, 11143, 11150, 11157, 11164, 11171, 11178, 11185, 11192, 11199, 0}, {11122, 11129, 11136, 11143, 11150, 11157, 11164, 11171, 11178, 11185, 11192, 11199, 0}, 0, 1, 691, 187, {2154,2696,1556,0,0,0,0,0,0,0,0,0,0,0},{11206,0,0,0,0,0,0,0,0,0},{843,251,0,0,0,0,0,0,0,0,0,0},{848,269,0,0,0,0,0,0,0},{11224,0,0,0,0,0,0,0}},
	{289, 11240, 11253, {11266, 11281, 11302, 11317, 11330, 11343, 10761}, {421, 426, 11358, 436, 11363, 446, 451}, {456, 459, 11368, 465, 468, 459, 465}, {11371, 11388, 11397, 11412, 521, 11429, 11444, 11457, 11472, 11489, 11510, 11527, 0}, {11542, 11559, 11572, 11589, 8816, 11608, 11623, 11636, 11649, 11664, 11687, 11706, 0}, {11719, 11726, 11733, 11740, 521, 11747, 11754, 11761, 11768, 11775, 11782, 11789, 0}, {11719, 11726, 11733, 11740, 521, 11747, 11754, 11761, 11768, 11775, 11782, 11789, 0}, 0, 0, 691, 187, {2696,0,0,0,0,0,0,0,0,0,0,0,0,0},{3520,0,0,0,0,0,0,0,0,0},{251,843,0,0,0,0,0,0,0,0,0,0},{269,848,0,0,0,0,0,0,0},{11796,0,0,0,0,0,0,0}},
	{1812, 11810, 11815, {11820, 11828, 11839, 11845, 11851, 11860, 1876}, {9045, 9049, 2287, 11866, 9061, 9066, 11870}, {9074, 9076, 11874, 9080, 9082, 9076, 9080}, {2310, 2317, 9483, 2331, 2337, 11876, 11882, 11888, 2358, 2368, 2376, 2385, 0}, {2310, 2317, 9483, 2331, 2337, 11876, 11882, 11888, 2358, 2368, 2376, 2385, 0}, {2394, 2398, 2402, 2406, 2337, 2410, 2414, 11895, 2422, 2426, 2430, 2434, 0}, {2394, 2398, 2402, 2406, 2337, 2410, 2414, 11895, 2422, 2426, 2430, 2434, 0}, 0, 1, 5812, 187, {11899,6118,0,0,0,0,0,0,0,0,0,0,0,0},{11911,11931,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{1812, 11945, 11950, {11955, 11966, 11977, 11988, 11999, 12010, 12016}, {1909, 9701, 2302, 4413, 1907, 10586, 2308}, {1909, 9701, 2302, 4413, 1907, 10586, 2308}, {12025, 12033, 12042, 12049, 4848, 12056, 12062, 2351, 2358, 12068, 2376, 12077, 0}, {12025, 12033, 12042, 12049, 4848, 12056, 12062, 2351, 2358, 12068, 2376, 12077, 0}, {12087, 12092, 12042, 2406, 4848, 12056, 12062, 2418, 12098, 2426, 2430, 12103, 0}, {12087, 12092, 12042, 2406, 4848, 12056, 12062, 2418, 12098, 2426, 2430, 12103, 0}, 2, 1, 691, 187, {2154,6118,0,0,0,0,0,0,0,0,0,0,0,0},{2719,2194,0,0,0,0,0,0,0,0},{843,251,0,0,0,0,0,0,0,0,0,0},{12108,0,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{1812, 12116, 12133, {12147, 12158, 12168, 12177, 12188, 12200, 12211}, {12221, 12224, 12227, 12230, 12233, 12236, 12239}, {1914, 1909, 2304, 2302, 7600, 1909, 1914}, {12242, 12252, 12263, 12269, 12278, 12284, 12292, 12300, 12308, 12319, 12328, 12338, 0}, {12348, 12358, 2325, 12369, 12378, 12384, 12392, 12400, 12408, 12419, 12428, 12438, 0}, {12448, 12454, 12263, 12460, 12278, 12465, 12471, 12477, 4128, 12482, 4139, 12487, 0}, {12448, 12454, 12263, 12460, 12278, 12465, 12471, 12477, 4128, 12482, 4139, 12487, 0}, 0, 1, 691, 187, {2154,12492,0,0,0,0,0,0,0,0,0,0,0,0},{12512,12539,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{12560,0,0,0,0,0,0,0}},
	{3551, 12576, 11815, {12582, 12594, 12606, 12618, 12632, 12647, 12660}, {12674, 12677, 12680, 12683, 12686, 12689, 9471}, {1914, 1909, 12692, 2302, 4413, 1909, 9480}, {12694, 12701, 12709, 12715, 12724, 12733, 12743, 12749, 12760, 12770, 12777, 12787, 0}, {12694, 12701, 12709, 12715, 12724, 12733, 12743, 12749, 12760, 12770, 12777, 12787, 0}, {12795, 12801, 12806, 12811, 12816, 12821, 12828, 12834, 12840, 12846, 12852, 12859, 0}, {12795, 12801, 12806, 12811, 12816, 12821, 12828, 12834, 12840, 12846, 12852, 12859, 0}, 2, 1, 2438, 187, {1556,0,0,0,0,0,0,0,0,0,0,0,0,0},{12866,12894,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{12916,0,0,0,0,0,0,0}},
	{1, 12926, 12938, {12950, 12965, 12980, 12995, 13012, 13031, 13042}, {13053, 13060, 13067, 13074, 13081, 13088, 13095}, {0, 0, 0, 0, 0, 0, 0}, {13102, 13113, 8644, 13126, 8666, 13137, 13144, 8691, 13151, 13166, 13179, 13190, 0}, {13102, 13113, 8644, 13126, 8666, 13137, 13144, 8691, 13151, 13166, 13179, 13190, 0}, {13203, 13210, 13217, 13224, 8666, 13137, 13144, 13231, 13238, 13245, 13252, 13259, 0}, {13203, 13210, 13217, 13224, 8666, 13137, 13144, 13231, 13238, 13245, 13252, 13259, 0}, 0, 1, 691, 187, {2154,2696,8015,2440,189,0,0,0,0,0,0,0,0,0},{13266,13284,0,0,0,0,0,0,0,0},{251,843,0,0,0,0,0,0,0,0,0,0},{269,848,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{289, 13303, 13323, {13340, 13353, 13366, 13382, 13399, 13414, 13423}, {13340, 13353, 13366, 13382, 13399, 13414, 13423}, {13432, 13435, 13438, 13441, 13444, 13447, 13450}, {13453, 13466, 13477, 13486, 13497, 13502, 13511, 13522, 13529, 13544, 13555, 13568, 0}, {13581, 13596, 13477, 13486, 13609, 13502, 13616, 13522, 13529, 13544, 13555, 13568, 0}, {13453, 13466, 13477, 13486, 13497, 13502, 13511, 13522, 13529, 13544, 13555, 13568, 0}, {13453, 13466, 13477, 13486, 13497, 13502, 13511, 13522, 13529, 13544, 13555, 13568, 0}, 0, 6, 185, 187, {198,189,0,0,0,0,0,0,0,0,0,0,0,0},{3502,3520,0,0,0,0,0,0,0,0},{242,251,0,0,0,0,0,0,0,0,0,0},{257,269,0,0,0,0,0,0,0},{278,0,0,0,0,0,0,0}},
	{1, 13629, 13632, {13635, 13648, 13658, 13667, 13677, 13688, 13699}, {13711, 13714, 13719, 13724, 13729, 13734, 13739}, {13711, 13744, 13747, 13750, 13753, 13756, 13759}, {13762, 13771, 13780, 13789, 13798, 13807, 13816, 13825, 13834, 13843, 13853, 13863, 0}, {13873, 13882, 13891, 13900, 13909, 13918, 13927, 13936, 13945, 13954, 13964, 13974, 0}, {13984, 13990, 13996, 14002, 14008, 14014, 14020, 14026, 14032, 14038, 14045, 14052, 0}, {13984, 13990, 13996, 14002, 14008, 14014, 14020, 14026, 14032, 14038, 14045, 14052, 0}, 0, 1, 185, 187, {198,189,2451,2440,1556,0,0,0,0,0,0,0,0,0},{5455,0,0,0,0,0,0,0,0,0},{3532,242,843,251,0,0,0,0,0,0,0,0},{3540,257,848,269,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{289, 14059, 14083, {14107, 14120, 14141, 14160, 14181, 14200, 14213}, {14224, 14231, 14238, 14245, 14252, 14259, 14266}, {14273, 14276, 14276, 14279, 14282, 14285, 14290}, {14293, 14308, 14323, 14332, 14343, 14354, 14367, 14380, 14395, 14414, 14433, 14450, 0}, {14469, 14486, 14503, 14514, 14527, 14540, 14555, 14570, 14587, 14608, 14629, 14648, 0}, {14669, 14676, 14683, 14690, 14697, 14704, 14711, 14718, 14725, 14732, 14739, 14746, 0}, {14669, 14676, 14683, 14690, 14697, 14704, 14711, 14718, 14725, 14732, 14739, 14746, 0}, 0, 1, 691, 187, {2154,2696,14753,198,14763,3490,1556,0,0,0,0,0,0,0},{3808,3502,10640,5455,14774,14785,14797,14814,0,0},{251,843,0,0,0,0,0,0,0,0,0,0},{269,848,0,0,0,0,0,0,0},{278,0,0,0,0,0,0,0}},
	{289, 1820, 1823, {14832, 14838, 14852, 14875, 14889, 14905, 14912}, {14921, 14924, 14929, 14935, 14939, 7600, 14944}, {6540, 6528, 6530, 6532, 6534, 6536, 6538}, {14948, 14955, 10132, 14962, 3668, 14968, 14974, 14980, 14987, 14996, 15004, 15011, 0}, {15018, 15025, 15032, 15037, 15043, 15047, 15052, 15057, 15064, 15073, 15081, 15088, 0}, {15095, 7999, 2402, 2406, 15043, 15099, 15103, 15107, 15111, 2426, 15115, 15119, 0}, {15095, 7999, 2402, 2406, 15043, 15099, 15103, 15107, 15111, 2426, 15115, 15119, 0}, 0, 1, 691, 187, {2154,1129,0,0,0,0,0,0,0,0,0,0,0,0},{15123,3520,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{3551, 1820, 1823, {15141, 15149, 15160, 15170, 15181, 15190, 15199}, {15209, 15213, 15217, 15221, 15225, 15229, 15233}, {15237, 12692, 12692, 12692, 2304, 2304, 2308}, {15239, 15249, 15257, 15265, 15273, 15281, 15288, 15296, 15304, 15311, 15317, 15324, 0}, {15332, 15343, 15352, 15361, 15370, 15379, 15387, 15396, 15405, 15413, 15420, 15428, 0}, {15437, 15442, 4795, 15447, 15452, 15457, 15462, 15467, 15472, 15477, 15482, 15487, 0}, {15437, 15442, 4795, 15447, 15452, 15457, 15462, 15467, 15472, 15477, 15482, 15487, 0}, 2, 1, 185, 187, {1545,15492,0,0,0,0,0,0,0,0,0,0,0,0},{15503,15530,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{15551,0,0,0,0,0,0,0}},
	{289, 15570, 15591, {15608, 331, 352, 8513, 15621, 15638, 15649}, {15662, 15670, 15678, 15684, 15692, 15700, 15708}, {456, 459, 462, 465, 468, 459, 465}, {15716, 484, 501, 510, 15731, 15738, 15747, 542, 555, 574, 591, 606, 0}, {15716, 484, 501, 510, 15731, 15738, 15747, 542, 555, 574, 591, 606, 0}, {15756, 15764, 15772, 639, 15731, 15780, 15788, 647, 655, 665, 673, 683, 0}, {15756, 15764, 15772, 639, 15731, 15780, 15788, 647, 655, 665, 673, 683, 0}, 0, 1, 691, 187, {15796,0,0,0,0,0,0,0,0,0,0,0,0,0},{10640,5455,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{15806,0,0,0,0,0,0,0}},
	{3551, 0, 0, {15822, 15829, 15840, 15849, 15858, 15865, 15876}, {15886, 15890, 15894, 15898, 15902, 15905, 15909}, {0, 0, 0, 0, 0, 0, 0}, {15913, 15923, 15932, 15940, 15949, 15962, 15974, 15981, 15988, 15995, 16005, 16017, 0}, {15913, 15923, 15932, 15940, 15949, 15962, 15974, 15981, 15988, 15995, 16005, 16017, 0}, {16030, 16034, 16038, 16042, 16046, 16050, 16054, 16058, 16062, 16066, 16070, 16074, 0}, {16030, 16034, 16038, 16042, 16046, 16050, 16054, 16058, 16062, 16066, 16070, 16074, 0}, 0, 0, 2438, 187, {1556,15492,0,0,0,0,0,0,0,0,0,0,0,0},{16078,16096,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{12916,0,0,0,0,0,0,0}},
	{3551, 0, 0, {16108, 16113, 16119, 16129, 16141, 16149, 16160}, {16169, 16173, 16177, 16181, 16185, 16189, 16193}, {0, 0, 0, 0, 0, 0, 0}, {16197, 16207, 16217, 16224, 16231, 2598, 16236, 16243, 16250, 16259, 16267, 16275, 0}, {16197, 16207, 16217, 16224, 16231, 2598, 16236, 16243, 16250, 16259, 16267, 16275, 0}, {2651, 2655, 15909, 16283, 16287, 2668, 2672, 16291, 2680, 2684, 2688, 16295, 0}, {2651, 2655, 15909, 16283, 16287, 2668, 2672, 16291, 2680, 2684, 2688, 16295, 0}, 0, 0, 2438, 187, {1556,15492,0,0,0,0,0,0,0,0,0,0,0,0},{16078,16096,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{12916,0,0,0,0,0,0,0}},
	{3551, 1820, 1823, {16299, 16305, 16316, 16129, 16141, 16149, 16160}, {16326, 16330, 16334, 16181, 16185, 16189, 16193}, {1914, 2300, 16338, 2302, 1914, 5644, 2300}, {16340, 16350, 16361, 16368, 16377, 16383, 16389, 16397, 16405, 16416, 16426, 16435, 0}, {16444, 16207, 16453, 16459, 16231, 2598, 16236, 16243, 16466, 16259, 16267, 16275, 0}, {2651, 2655, 16476, 2664, 16287, 2668, 2672, 16291, 2680, 2684, 2688, 16295, 0}, {2651, 2655, 16476, 2664, 16287, 2668, 2672, 16291, 2680, 2684, 2688, 16295, 0}, 0, 0, 185, 187, {3711,16480,0,0,0,0,0,0,0,0,0,0,0,0},{3757,3776,0,0,0,0,0,0,0,0},{3532,251,0,0,0,0,0,0,0,0,0,0},{3540,269,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{289, 16492, 16496, {16500, 16507, 16515, 16523, 16532, 16542, 16549}, {2544, 16558, 2550, 16561, 2556, 16564, 2562}, {1914, 2300, 2565, 3643, 2565, 3927, 1914}, {16567, 16576, 16586, 2588, 10611, 16592, 16598, 16604, 2615, 2625, 2633, 10623, 0}, {16567, 16576, 16586, 2588, 10611, 16592, 16598, 16604, 2615, 2625, 2633, 10623, 0}, {2651, 2655, 3699, 2664, 10611, 2668, 2672, 2676, 2680, 2684, 2688, 10636, 0}, {2651, 2655, 3699, 2664, 10611, 2668, 2672, 2676, 2680, 2684, 2688, 10636, 0}, 0, 0, 2438, 187, {1556,9951,0,0,0,0,0,0,0,0,0,0,0,0},{10640,5455,0,0,0,0,0,0,0,0},{3532,251,0,0,0,0,0,0,0,0,0,0},{3540,269,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{289, 1820, 1823, {16613, 16629, 16654, 16682, 16710, 16738, 16766}, {16785, 16795, 16805, 16815, 16825, 16835, 16845}, {16855, 16859, 16863, 16859, 16867, 16871, 16875}, {16879, 16901, 16929, 16945, 16964, 16980, 16999, 17018, 17040, 17071, 17099, 17124, 0}, {16879, 16901, 16929, 16945, 16964, 16980, 16999, 17018, 17040, 17071, 17099, 17124, 0}, {17152, 17162, 17172, 17182, 17192, 17202, 17212, 17222, 17232, 17242, 17252, 17262, 0}, {17152, 17162, 17172, 17182, 17192, 17202, 17212, 17222, 17232, 17242, 17252, 17262, 0}, 0, 1, 691, 187, {2154,17272,0,0,0,0,0,0,0,0,0,0,0,0},{222,3808,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{278,0,0,0,0,0,0,0}},
	{3551, 17284, 17289, {5897, 17294, 17305, 17315, 17325, 17335, 17349}, {17361, 17365, 17370, 17375, 17379, 17384, 17389}, {1914, 2300, 2302, 2300, 5644, 2306, 2308}, {2310, 2317, 4837, 6042, 4848, 2341, 2346, 2351, 2358, 2368, 2376, 6086, 0}, {2310, 2317, 4837, 6042, 4848, 2341, 2346, 2351, 2358, 2368, 2376, 6086, 0}, {2394, 2398, 2402, 2406, 4848, 2410, 2414, 2418, 2422, 2426, 2430, 7503, 0}, {2394, 2398, 2402, 2406, 4848, 2410, 2414, 2418, 2422, 2426, 2430, 7503, 0}, 2, 1, 2438, 187, {2440,0,0,0,0,0,0,0,0,0,0,0,0,0},{5508,6118,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{12916,0,0,0,0,0,0,0}},
	{1, 17393, 17409, {17419, 17438, 17457, 17479, 17498, 17520, 17545}, {17564, 17574, 17584, 17597, 17607, 17620, 17636}, {17646, 17650, 17657, 17664, 17671, 17678, 17685}, {17689, 17705, 17724, 17740, 17759, 17766, 17776, 17792, 17808, 17827, 17849, 17865, 0}, {17689, 17705, 17724, 17740, 17759, 17766, 17776, 17792, 17808, 17827, 17849, 17865, 0}, {17884, 17891, 17724, 17901, 17759, 17766, 17917, 17930, 17937, 17950, 17966, 17976, 0}, {17884, 17891, 17724, 17901, 17759, 17766, 17917, 17930, 17937, 17950, 17966, 17976, 0}, 0, 0, 2438, 187, {2440,2451,4157,8015,1556,0,0,0,0,0,0,0,0,0},{5455,3520,0,0,0,0,0,0,0,0},{251,843,1730,1722,0,0,0,0,0,0,0,0},{269,848,1750,1739,0,0,0,0,0},{278,0,0,0,0,0,0,0}},
	{17989, 18004, 18007, {18010, 18019, 18028, 18038, 18048, 18058, 18070}, {18078, 18083, 18087, 18091, 18095, 18100, 18105}, {18109, 2302, 2302, 9701, 18109, 18112, 1914}, {18115, 18122, 18127, 2588, 18133, 18139, 18146, 18152, 18160, 18170, 18178, 18187, 0}, {18115, 18122, 18127, 2588, 18133, 18139, 18146, 18152, 18160, 18170, 18178, 18187, 0}, {2651, 18197, 3699, 2664, 18201, 18205, 18210, 18214, 18218, 18222, 2688, 18226, 0}, {2651, 18197, 3699, 2664, 18201, 18205, 18210, 18214, 18218, 18222, 2688, 18226, 0}, 0, 0, 185, 187, {198,9951,0,0,0,0,0,0,0,0,0,0,0,0},{18231,18257,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{18277,0,0,0,0,0,0,0}},
	{3551, 18295, 18300, {18305, 18317, 18328, 18342, 18354, 18364, 18374}, {18385, 18390, 18395, 18400, 18405, 18410, 18415}, {1914, 3927, 2300, 6216, 2565, 16338, 2308}, {18420, 18437, 18450, 18464, 18477, 18490, 18503, 18517, 18529, 18543, 18557, 18571, 0}, {18420, 18437, 18450, 18464, 18477, 18490, 18503, 18517, 18529, 18543, 18557, 18571, 0}, {18584, 18591, 18596, 18601, 18605, 18610, 18615, 18620, 18625, 18632, 18637, 18643, 0}, {18584, 18591, 18596, 18601, 18605, 18610, 18615, 18620, 18625, 18632, 18637, 18643, 0}, 2, 1, 2438, 187, {1556,15492,0,0,0,0,0,0,0,0,0,0,0,0},{16078,16096,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{12916,0,0,0,0,0,0,0}},
	{289, 1820, 1823, {19797, 19806, 19815, 19823, 19832, 19841, 19848}, {19857, 19860, 19863, 19866, 19869, 19873, 19876}, {6530, 6532, 6534, 6536, 12692, 15237, 6528}, {10588, 10596, 19879, 19885, 10611, 2598, 18989, 19892, 16250, 19899, 16267, 19906, 0}, {10588, 10596, 19879, 19885, 10611, 2598, 18989, 19892, 16250, 19899, 16267, 19906, 0}, {2651, 2655, 18985, 2664, 10611, 2668, 2672, 19914, 2680, 2684, 2688, 10636, 0}, {2651, 2655, 18985, 2664, 10611, 2668, 2672, 19914, 2680, 2684, 2688, 10636, 0}, 0, 0, 185, 187, {198,1129,0,0,0,0,0,0,0,0,0,0,0,0},{3502,3520,0,0,0,0,0,0,0,0},{3532,251,0,0,0,0,0,0,0,0,0,0},{3540,269,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{3551, 1820, 1823, {19918, 19928, 19937, 19946, 19957, 19967, 19972}, {19979, 19985, 19990, 19995, 20000, 10578, 20004}, {20009, 2565, 1914, 7600, 1909, 3925, 1914}, {14948, 14955, 10132, 14962, 3668, 20011, 20016, 20021, 14987, 14996, 15004, 15011, 0}, {14948, 14955, 10132, 14962, 3668, 20011, 20016, 20021, 14987, 14996, 15004, 15011, 0}, {20028, 20033, 3699, 2664, 3668, 20011, 20016, 20037, 10562, 2684, 20041, 20046, 0}, {20028, 20033, 3699, 2664, 3668, 20011, 20016, 20037, 10562, 2684, 20041, 20046, 0}, 0, 1, 185, 187, {1545,15492,0,0,0,0,0,0,0,0,0,0,0,0},{20050,16096,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{12916,0,0,0,0,0,0,0}},
	{289, 20069, 20097, {20119, 20138, 20157, 20182, 20201, 20235, 20260}, {20279, 20289, 20299, 20315, 20325, 20350, 20366}, {20376, 20380, 20387, 20391, 20398, 20405, 20412}, {20416, 20444, 20478, 20494, 20513, 20520, 20530, 20546, 20562, 20593, 20615, 20637, 0}, {20416, 20444, 20478, 20494, 20513, 20520, 20530, 20546, 20562, 20593, 20615, 20637, 0}, {20416, 20444, 20478, 20494, 20513, 20520, 20530, 20546, 20562, 20593, 20615, 20637, 0}, {20416, 20444, 20478, 20494, 20513, 20520, 20530, 20546, 20562, 20593, 20615, 20637, 0}, 0, 0, 2438, 691, {2451,4157,2440,2451,2440,0,0,0,0,0,0,0,0,0},{5455,3520,0,0,0,0,0,0,0,0},{4246,4241,20662,20671,0,0,0,0,0,0,0,0},{4268,4260,20679,20691,0,0,0,0,0},{278,0,0,0,0,0,0,0}},
	{1, 1820, 1823, {21189, 21208, 21227, 21249, 21268, 21290, 21315}, {21334, 21344, 21354, 21367, 21377, 21390, 21406}, {21416, 21420, 21427, 21434, 21441, 21448, 21455}, {21459, 21487, 21515, 21531, 21550, 21557, 21567, 21583, 21599, 21627, 21649, 21671, 0}, {21459, 21487, 21515, 21531, 21550, 21557, 21567, 21583, 21599, 21627, 21649, 21671, 0}, {21696, 21715, 21515, 21531, 21550, 21557, 21567, 21734, 21741, 21757, 21773, 21783, 0}, {21696, 21715, 21515, 21531, 21550, 21557, 21567, 21734, 21741, 21757, 21773, 21783, 0}, 0, 0, 2438, 187, {2451,4157,8015,2440,1556,0,0,0,0,0,0,0,0,0},{5455,3520,0,0,0,0,0,0,0,0},{251,843,1730,1722,0,0,0,0,0,0,0,0},{269,848,1750,1739,0,0,0,0,0},{278,0,0,0,0,0,0,0}},
	{1, 8022, 21796, {21799, 21818, 21837, 21862, 21881, 21903, 21928}, {21947, 21957, 21967, 21983, 21993, 22006, 22022}, {22032, 22036, 22043, 22047, 22054, 22061, 22068}, {22072, 22094, 22125, 22147, 22166, 22173, 22183, 22199, 22215, 22246, 22268, 22290, 0}, {22072, 22094, 22125, 22147, 22166, 22173, 22183, 22199, 22215, 22246, 22268, 22290, 0}, {6528, 6530, 6532, 6534, 6536, 6538, 6540, 6542, 6544, 6546, 6549, 6552, 0}, {6528, 6530, 6532, 6534, 6536, 6538, 6540, 6542, 6544, 6546, 6549, 6552, 0}, 0, 0, 2438, 187, {2451,4157,8015,2440,1556,0,0,0,0,0,0,0,0,0},{5455,3520,0,0,0,0,0,0,0,0},{251,843,1730,1722,0,0,0,0,0,0,0,0},{269,848,1750,1739,0,0,0,0,0},{278,0,0,0,0,0,0,0}},
	{289, 22315, 22340, {22365, 22384, 22406, 22431, 22447, 22469, 22488}, {22498, 22505, 22512, 22519, 22526, 22533, 22540}, {22498, 22505, 22512, 22519, 22526, 22533, 22540}, {22544, 22560, 22585, 22604, 22623, 22630, 22643, 22656, 22675, 22706, 22731, 22753, 0}, {22544, 22560, 22585, 22604, 22623, 22630, 22643, 22778, 22675, 22706, 22731, 22753, 0}, {22797, 22805, 22819, 22833, 22623, 22630, 22643, 22844, 22852, 22866, 22877, 22885, 0}, {22797, 22805, 22819, 22833, 22623, 22630, 22643, 22844, 22852, 22866, 22877, 22885, 0}, 0, 0, 2438, 187, {2440,2451,4157,8015,1556,0,0,0,0,0,0,0,0,0},{5455,3520,0,0,0,0,0,0,0,0},{251,843,1730,1722,0,0,0,0,0,0,0,0},{269,848,1750,1739,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{3551, 1820, 1823, {22896, 22918, 22940, 22965, 22987, 23012, 23040}, {23062, 23072, 23082, 23095, 23105, 23118, 23134}, {23144, 23148, 23155, 23159, 23166, 23173, 23180}, {23184, 23200, 23225, 23244, 23266, 23273, 23286, 23299, 23318, 23349, 23374, 23393, 0}, {23184, 23200, 23225, 23244, 23266, 23273, 23418, 23299, 23318, 23349, 23374, 23393, 0}, {23431, 23438, 23225, 23454, 23266, 23273, 23418, 23299, 23470, 23492, 23508, 23518, 0}, {23431, 23438, 23225, 23454, 23266, 23273, 23418, 23299, 23470, 23492, 23508, 23518, 0}, 0, 0, 2438, 187, {2451,4157,8015,2440,1556,0,0,0,0,0,0,0,0,0},{5455,3520,0,0,0,0,0,0,0,0},{251,843,1730,1722,0,0,0,0,0,0,0,0},{269,848,1750,1739,0,0,0,0,0},{278,0,0,0,0,0,0,0}},
	{3551, 1820, 1823, {23534, 23553, 23572, 23594, 23613, 23635, 23660}, {23679, 23689, 23699, 23712, 23722, 23735, 23751}, {23761, 23765, 23772, 23779, 23786, 23793, 23800}, {23804, 23820, 23845, 23864, 23886, 23893, 23906, 23919, 23938, 23966, 23991, 24013, 0}, {23804, 23820, 23845, 23864, 23886, 23893, 23906, 23919, 23938, 23966, 23991, 24013, 0}, {24038, 24046, 24066, 24073, 23886, 24090, 24097, 24105, 24113, 24136, 24153, 24167, 0}, {24038, 24046, 24066, 24073, 23886, 24090, 24097, 24105, 24113, 24136, 24153, 24167, 0}, 0, 0, 2438, 187, {2451,4157,8015,2440,1556,0,0,0,0,0,0,0,0,0},{5455,3520,0,0,0,0,0,0,0,0},{251,843,1730,1722,0,0,0,0,0,0,0,0},{269,848,1750,1739,0,0,0,0,0},{278,0,0,0,0,0,0,0}},
	{24184, 1820, 1823, {24192, 24220, 24254, 24282, 24310, 24341, 24378}, {24406, 24419, 24438, 24454, 24467, 24486, 24505}, {24515, 24522, 24529, 24536, 24543, 24556, 24563}, {24567, 24586, 24614, 24636, 24655, 24668, 24678, 24691, 24716, 24747, 24772, 24788, 0}, {24567, 24586, 24614, 24636, 24655, 24668, 24678, 24691, 24716, 24747, 24772, 24788, 0}, {24807, 24817, 24836, 24846, 24655, 24668, 24678, 24862, 24869, 24894, 24910, 24920, 0}, {24807, 24817, 24836, 24846, 24655, 24668, 24678, 24862, 24869, 24894, 24910, 24920, 0}, 0, 0, 2438, 691, {2451,4157,2696,8015,0,0,0,0,0,0,0,0,0,0},{5455,3520,0,0,0,0,0,0,0,0},{4246,4241,20662,20671,0,0,0,0,0,0,0,0},{4268,4260,20679,20691,0,0,0,0,0},{278,0,0,0,0,0,0,0}},
	{289, 24933, 24961, {24983, 25002, 25021, 25046, 25065, 25099, 25124}, {25143, 20289, 20299, 20315, 25153, 25178, 20366}, {0, 0, 0, 0, 0, 0, 0}, {25194, 25219, 25250, 25266, 20513, 20520, 20530, 25285, 25301, 25332, 25354, 25376, 0}, {25194, 25219, 25250, 25266, 20513, 20520, 20530, 25285, 25301, 25332, 25354, 25376, 0}, {25401, 25414, 25250, 25266, 20513, 20520, 20530, 25433, 25440, 25456, 25472, 25482, 0}, {25401, 25414, 25250, 25266, 20513, 20520, 20530, 25433, 25440, 25456, 25472, 25482, 0}, 0, 0, 2438, 187, {2440,0,0,0,0,0,0,0,0,0,0,0,0,0},{25495,0,0,0,0,0,0,0,0,0},{1722,1730,843,0,0,0,0,0,0,0,0,0},{1739,1750,848,0,0,0,0,0,0},{25514,25522,0,0,0,0,0,0}},
	{1, 1820, 1823, {17419, 17438, 25532, 17479, 17498, 17520, 17545}, {17564, 17574, 25554, 17597, 17607, 17620, 17636}, {17646, 17650, 17657, 17664, 17671, 17678, 17685}, {25567, 25592, 17724, 25623, 25642, 17766, 25649, 25662, 25678, 25703, 25725, 25753, 0}, {25567, 25592, 17724, 25623, 25642, 17766, 25649, 25662, 25678, 25703, 25725, 25753, 0}, {25775, 25788, 17724, 25807, 25642, 17766, 25649, 25823, 25830, 25849, 25865, 25887, 0}, {25775, 25788, 17724, 25807, 25642, 17766, 25649, 25823, 25830, 25849, 25865, 25887, 0}, 0, 0, 2438, 187, {2440,2451,4157,8015,1556,0,0,0,0,0,0,0,0,0},{5455,3520,0,0,0,0,0,0,0,0},{251,843,1730,1722,0,0,0,0,0,0,0,0},{269,848,1750,1739,0,0,0,0,0},{278,0,0,0,0,0,0,0}},
	{26596, 26620, 26642, {26667, 26695, 26723, 26760, 26791, 26825, 26850}, {26884, 26900, 26916, 26941, 26960, 26982, 26995}, {27017, 27024, 27031, 27038, 27045, 27052, 27056}, {27066, 27100, 27137, 27171, 27205, 27236, 27273, 27310, 27350, 27384, 27418, 27467, 0}, {27066, 27100, 27137, 27171, 27205, 27236, 27273, 27310, 27350, 27384, 27418, 27467, 0}, {27516, 27529, 27542, 27555, 27568, 27581, 27594, 27607, 27620, 27633, 27649, 27665, 0}, {27516, 27529, 27542, 27555, 27568, 27581, 27594, 27607, 27620, 27633, 27649, 27665, 0}, 0, 0, 185, 187, {1518,1527,1536,1567,1556,1545,1585,1578,1592,0,0,0,0,0},{27681,27724,27772,27804,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{27841,1805,0,0,0,0,0,0}},
	{289, 1820, 1823, {27874, 27883, 27893, 27905, 27918, 27927, 27939}, {27951, 27955, 27960, 27964, 27968, 27972, 27976}, {1914, 27980, 2300, 2300, 15237, 6216, 1914}, {27983, 27990, 27999, 28006, 2594, 28013, 28021, 28032, 28037, 28042, 28049, 28058, 0}, {27983, 27990, 27999, 28006, 2594, 28013, 28021, 28032, 28037, 28042, 28049, 28058, 0}, {28066, 28070, 27960, 28074, 2594, 28078, 28082, 28032, 28037, 28086, 28090, 28095, 0}, {28066, 28070, 27960, 28074, 2594, 28078, 28082, 28032, 28037, 28086, 28090, 28095, 0}, 2, 1, 185, 187, {198,1129,0,0,0,0,0,0,0,0,0,0,0,0},{3502,3520,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{289, 28100, 28116, {28132, 28154, 28167, 28186, 28196, 28227, 28243}, {28132, 28154, 28167, 28186, 28196, 28227, 28243}, {6528, 6530, 6532, 6534, 6536, 6538, 6540}, {28256, 28269, 28288, 28301, 28314, 28327, 28346, 28365, 28378, 28394, 28407, 28432, 0}, {28256, 28269, 28288, 28301, 28314, 28327, 28346, 28365, 28378, 28394, 28407, 28432, 0}, {28256, 28269, 28288, 28301, 28314, 28327, 28346, 28365, 28378, 28394, 28407, 28432, 0}, {28256, 28269, 28288, 28301, 28314, 28327, 28346, 28365, 28378, 28394, 28407, 28432, 0}, 0, 0, 185, 187, {189,1556,0,0,0,0,0,0,0,0,0,0,0,0},{3520,9963,0,0,0,0,0,0,0,0},{843,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{28445,0,0,0,0,0,0,0}},
	{289, 28480, 28505, {28530, 28555, 28574, 28602, 28621, 28646, 28665}, {28530, 28555, 28574, 28602, 28621, 28646, 28665}, {28687, 28691, 28695, 28699, 28709, 28713, 28723}, {28727, 28746, 28762, 28775, 28788, 28810, 28829, 28851, 28867, 28883, 28896, 28912, 0}, {28727, 28746, 28762, 28775, 28788, 28810, 28829, 28851, 28867, 28883, 28896, 28912, 0}, {28928, 28937, 28946, 28955, 28964, 28973, 28985, 28994, 29003, 29012, 29021, 29030, 0}, {28928, 28937, 28946, 28955, 28964, 28973, 28985, 28994, 29003, 29012, 29021, 29030, 0}, 0, 0, 185, 187, {1120,1129,0,0,0,0,0,0,0,0,0,0,0,0},{29039,3520,0,0,0,0,0,0,0,0},{843,251,0,0,0,0,0,0,0,0,0,0},{848,269,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{289, 7261, 7266, {3833, 29066, 3847, 29071, 29081, 29087, 3880}, {29094, 8298, 3699, 29098, 29103, 29107, 29111}, {2565, 2308, 2300, 2300, 29116, 3927, 1914}, {29118, 29126, 3943, 3949, 29135, 29140, 29146, 3972, 29152, 29161, 29169, 29178, 0}, {29187, 29195, 4032, 1012, 7947, 29204, 29210, 4055, 7964, 7973, 7981, 29216, 0}, {29225, 2655, 3699, 29229, 2594, 29233, 29238, 19914, 18218, 29242, 2688, 3707, 0}, {29225, 2655, 3699, 29229, 2594, 29233, 29238, 19914, 18218, 29242, 2688, 3707, 0}, 2, 1, 185, 187, {198,17272,0,0,0,0,0,0,0,0,0,0,0,0},{5508,5455,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{1, 29246, 29258, {29270, 17438, 29298, 17479, 17498, 17520, 17545}, {17564, 17574, 25554, 17597, 17607, 17620, 17636}, {0, 0, 0, 0, 0, 0, 0}, {25567, 25592, 17724, 25623, 25642, 17766, 25649, 29317, 29333, 29361, 25725, 25753, 0}, {25567, 25592, 17724, 25623, 25642, 17766, 25649, 29317, 29333, 29361, 25725, 25753, 0}, {6528, 6530, 6532, 6534, 6536, 6538, 6540, 6542, 6544, 6546, 6549, 6552, 0}, {6528, 6530, 6532, 6534, 6536, 6538, 6540, 6542, 6544, 6546, 6549, 6552, 0}, 0, 0, 2438, 187, {2440,2451,4157,8015,1556,0,0,0,0,0,0,0,0,0},{5455,3520,0,0,0,0,0,0,0,0},{251,843,1730,1722,0,0,0,0,0,0,0,0},{269,848,1750,1739,0,0,0,0,0},{278,0,0,0,0,0,0,0}},
	{3551, 29383, 29395, {29404, 29420, 29436, 29464, 29480, 29523, 29548}, {29404, 29420, 29576, 29464, 29586, 29608, 29621}, {29631, 29635, 29639, 29643, 29647, 29660, 29667}, {29674, 29693, 29718, 29737, 29762, 29775, 29788, 29801, 29823, 29857, 29882, 29910, 0}, {29674, 29693, 29718, 29737, 29762, 29775, 29788, 29801, 29823, 29857, 29882, 29910, 0}, {29938, 29945, 29955, 29737, 29762, 29775, 29788, 29968, 29978, 29991, 30001, 30014, 0}, {29938, 29945, 29955, 29737, 29762, 29775, 29788, 29968, 29978, 29991, 30001, 30014, 0}, 0, 1, 2438, 691, {1556,15492,0,0,0,0,0,0,0,0,0,0,0,0},{16078,16096,0,0,0,0,0,0,0,0},{20671,251,0,0,0,0,0,0,0,0,0,0},{20691,269,0,0,0,0,0,0,0},{12916,0,0,0,0,0,0,0}},
	{3551, 30027, 30037, {30050, 30060, 30067, 30080, 30090, 30100, 30110}, {30050, 30060, 30120, 30080, 30090, 30100, 30110}, {30130, 30134, 30138, 30142, 30146, 30150, 30154}, {30158, 30174, 30190, 30200, 30213, 30220, 30227, 30237, 30250, 30269, 30285, 30301, 0}, {30158, 30174, 30190, 30200, 30213, 30220, 30227, 30237, 30250, 30317, 30285, 30301, 0}, {30336, 30346, 30190, 30356, 30213, 30220, 30227, 30366, 30376, 30386, 30396, 30406, 0}, {30336, 30346, 30190, 30356, 30213, 30220, 30227, 30366, 30376, 30386, 30396, 30406, 0}, 0, 0, 185, 187, {198,1129,0,0,0,0,0,0,0,0,0,0,0,0},{3502,3520,0,0,0,0,0,0,0,0},{3532,251,0,0,0,0,0,0,0,0,0,0},{3540,269,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{1, 30651, 30692, {30733, 30752, 30771, 30796, 30815, 30837, 30862}, {30881, 17574, 30891, 17597, 30907, 17620, 17636}, {30920, 17650, 30924, 17664, 30928, 17678, 17685}, {17689, 30935, 17724, 30963, 25642, 30982, 17776, 30992, 31008, 31039, 31061, 31086, 0}, {17689, 30935, 17724, 30963, 25642, 30982, 17776, 30992, 31008, 31039, 31061, 31086, 0}, {17689, 30935, 17724, 30963, 25642, 30982, 17776, 30992, 31008, 31039, 31061, 31086, 0}, {17689, 30935, 17724, 30963, 25642, 30982, 17776, 30992, 31008, 31039, 31061, 31086, 0}, 0, 0, 185, 187, {3711,3720,3727,3736,1599,1556,3747,0,0,0,0,0,0,0},{31111,31131,222,10501,0,0,0,0,0,0},{3532,242,843,251,0,0,0,0,0,0,0,0},{3540,257,848,269,0,0,0,0,0},{25522,0,0,0,0,0,0,0}},
	{289, 0, 0, {0, 0, 0, 0, 0, 0, 0}, {0, 0, 0, 0, 0, 0, 0}, {0, 0, 0, 0, 0, 0, 0}, {31145, 31152, 31161, 31176, 31187, 31196, 31203, 31210, 31217, 31228, 31241, 31254, 0}, {31145, 31152, 31161, 31176, 31187, 31196, 31203, 31210, 31217, 31228, 31241, 31254, 0}, {6528, 6530, 6532, 6534, 6536, 6538, 6540, 6542, 6544, 6546, 6549, 6552, 0}, {6528, 6530, 6532, 6534, 6536, 6538, 6540, 6542, 6544, 6546, 6549, 6552, 0}, 0, 6, 185, 187, {1518,1556,0,0,0,0,0,0,0,0,0,0,0,0},{3520,4935,0,0,0,0,0,0,0,0},{843,0,0,0,0,0,0,0,0,0,0,0},{848,0,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{3551, 1820, 1823, {31259, 31266, 31272, 31279, 31290, 31298, 31307}, {31314, 8298, 3699, 31318, 31322, 31326, 10582}, {2308, 2308, 2300, 2300, 5644, 16338, 1914}, {3929, 31330, 31338, 3949, 3955, 31344, 31350, 3972, 31356, 31366, 31374, 31384, 0}, {3929, 31330, 31338, 3949, 3955, 31344, 31350, 3972, 31356, 31366, 31374, 31384, 0}, {31394, 31398, 3699, 29229, 3668, 31402, 31406, 19914, 18218, 2684, 31410, 16295, 0}, {31394, 31398, 3699, 29229, 3668, 31402, 31406, 19914, 18218, 2684, 31410, 16295, 0}, 0, 0, 185, 187, {3711,16480,0,0,0,0,0,0,0,0,0,0,0,0},{3757,3776,0,0,0,0,0,0,0,0},{3532,251,0,0,0,0,0,0,0,0,0,0},{3540,269,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{3551, 0, 0, {31414, 31421, 31429, 31436, 31443, 31451, 31459}, {31466, 31469, 31472, 31475, 31478, 31481, 31484}, {2308, 2308, 2302, 2308, 12692, 3925, 12692}, {31487, 31495, 31505, 31511, 31519, 31524, 31529, 31534, 31541, 19899, 31549, 31557, 0}, {31487, 31495, 31505, 31511, 31519, 31524, 31529, 31534, 31541, 19899, 31549, 31557, 0}, {2651, 31565, 3699, 31569, 3668, 30620, 30624, 31573, 3639, 2684, 31577, 16295, 0}, {2651, 31565, 3699, 31569, 3668, 30620, 30624, 31573, 3639, 2684, 31577, 16295, 0}, 0, 0, 185, 187, {1120,17272,0,0,0,0,0,0,0,0,0,0,0,0},{3789,3808,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{3551, 31581, 31592, {31602, 31620, 31635, 31657, 31670, 31684, 31701}, {31725, 31733, 31738, 31657, 31670, 31750, 31757}, {0, 0, 0, 0, 0, 0, 0}, {31771, 31793, 31809, 31829, 31843, 31860, 31875, 31892, 31906, 31919, 31938, 31952, 0}, {31771, 31793, 31809, 31829, 31843, 31860, 31875, 31892, 31906, 31919, 31938, 31952, 0}, {31971, 31986, 31995, 32008, 32015, 32025, 32033, 32043, 32050, 32056, 32068, 32075, 0}, {31971, 31986, 31995, 32008, 32015, 32025, 32033, 32043, 32050, 32056, 32068, 32075, 0}, 0, 0, 185, 187, {198,1129,0,0,0,0,0,0,0,0,0,0,0,0},{3502,3520,0,0,0,0,0,0,0,0},{3532,251,0,0,0,0,0,0,0,0,0,0},{3540,269,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{3551, 0, 0, {32087, 32095, 15840, 15849, 15858, 32106, 32116}, {16326, 15890, 15894, 15898, 15902, 16189, 32125}, {0, 0, 0, 0, 0, 0, 0}, {32129, 32138, 32148, 32156, 10611, 3672, 32164, 32170, 32179, 32188, 32197, 32206, 0}, {32129, 32138, 32148, 32156, 10611, 3672, 32164, 32170, 32179, 32188, 32197, 32206, 0}, {2651, 2655, 15909, 32215, 10611, 2668, 2672, 19914, 18218, 2684, 32219, 16295, 0}, {2651, 2655, 15909, 32215, 10611, 2668, 2672, 19914, 18218, 2684, 32219, 16295, 0}, 0, 0, 2438, 187, {1556,15492,0,0,0,0,0,0,0,0,0,0,0,0},{16078,16096,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{12916,0,0,0,0,0,0,0}},
	{32223, 32236, 32241, {32246, 32253, 32268, 32282, 32298, 32313, 32329}, {6212, 32344, 2402, 32348, 32352, 32356, 32360}, {1914, 12692, 2300, 1909, 1914, 2302, 12692}, {7350, 7358, 32364, 32371, 32378, 2341, 2346, 32383, 32393, 32404, 32413, 32423, 0}, {7350, 7358, 32364, 32371, 32378, 2341, 2346, 32383, 32393, 32404, 32413, 32423, 0}, {2394, 2398, 2402, 2406, 2337, 2410, 2414, 2418, 2422, 2426, 2430, 2434, 0}, {2394, 2398, 2402, 2406, 2337, 2410, 2414, 2418, 2422, 2426, 2430, 2434, 0}, 0, 0, 2438, 187, {2440,2451,1556,2460,0,0,0,0,0,0,0,0,0,0},{32433,2194,11931,0,0,0,0,0,0,0},{251,843,0,0,0,0,0,0,0,0,0,0},{269,848,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{3551, 32452, 32457, {32462, 32478, 32486, 32494, 32503, 32515, 32525}, {32535, 32541, 32547, 32551, 32555, 32563, 3639}, {0, 0, 0, 0, 0, 0, 0}, {32570, 32583, 32597, 32606, 32612, 32616, 32621, 32629, 16250, 32642, 16267, 16275, 0}, {32570, 32583, 32597, 32606, 32612, 32616, 32621, 32629, 16250, 32642, 16267, 16275, 0}, {32651, 2655, 32655, 16283, 32612, 32659, 2672, 32663, 2680, 32671, 2688, 16295, 0}, {32651, 2655, 32655, 16283, 32612, 32659, 2672, 32663, 2680, 32671, 2688, 16295, 0}, 0, 0, 185, 187, {198,1129,0,0,0,0,0,0,0,0,0,0,0,0},{3502,3520,0,0,0,0,0,0,0,0},{3532,251,0,0,0,0,0,0,0,0,0,0},{3540,269,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{32677, 32700, 32707, {32714, 32724, 32734, 32744, 32754, 32764, 32774}, {32784, 32791, 32798, 32805, 32812, 32819, 32826}, {32833, 32837, 32841, 32845, 32849, 32853, 32857}, {32861, 32868, 32875, 32882, 32889, 32896, 32903, 32910, 32917, 32924, 32931, 32941, 0}, {32861, 32868, 32875, 32882, 32889, 32896, 32903, 32910, 32917, 32924, 32931, 32941, 0}, {6528, 6530, 6532, 6534, 6536, 6538, 6540, 6542, 6544, 6546, 6549, 6552, 0}, {6528, 6530, 6532, 6534, 6536, 6538, 6540, 6542, 6544, 6546, 6549, 6552, 0}, 0, 0, 185, 187, {1518,1527,1536,1567,1556,1545,0,0,0,0,0,0,0,0},{32951,32975,33005,33035,33052,0,0,0,0,0},{1722,843,251,0,0,0,0,0,0,0,0,0},{1739,848,269,0,0,0,0,0,0},{33075,1805,0,0,0,0,0,0}},
	{289, 0, 0, {27951, 8298, 33092, 33099, 33109, 33114, 33121}, {33128, 6196, 33132, 4800, 33137, 33142, 33147}, {4392, 33152, 33155, 33158, 33161, 33164, 33167}, {33170, 33177, 33092, 33188, 33194, 33198, 33207, 33214, 33219, 33228, 33233, 33236, 0}, {33170, 33177, 33092, 33188, 33194, 33198, 33207, 33214, 33219, 33228, 33233, 33236, 0}, {33242, 33246, 33253, 28074, 33194, 33258, 33263, 33214, 33268, 33228, 33233, 33273, 0}, {33242, 33246, 33253, 28074, 33194, 33258, 33263, 33214, 33268, 33228, 33233, 33273, 0}, 2, 1, 2438, 187, {1556,15492,0,0,0,0,0,0,0,0,0,0,0,0},{16078,16096,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{33430, 33446, 33451, {33456, 33481, 33510, 33535, 33548, 33563, 33580}, {33595, 33600, 33605, 33610, 33615, 33620, 8599}, {19210, 19210, 33625, 8610, 8613, 19210, 8610}, {33628, 33647, 33662, 33684, 33704, 33720, 33736, 33750, 33776, 33800, 33817, 33834, 0}, {33628, 33647, 33662, 33684, 33704, 33720, 33736, 33750, 33776, 33800, 33817, 33834, 0}, {33851, 33860, 33869, 33883, 33897, 33909, 33921, 33931, 33947, 33961, 33968, 33975, 0}, {33851, 33860, 33869, 33883, 33897, 33909, 33921, 33931, 33947, 33961, 33968, 33975, 0}, 0, 1, 691, 187, {2154,4724,1556,33982,33992,0,0,0,0,0,0,0,0,0},{34005,34040,34069,0,0,0,0,0,0,0},{843,251,0,0,0,0,0,0,0,0,0,0},{848,269,0,0,0,0,0,0,0},{34104,0,0,0,0,0,0,0}},
	{3551, 0, 0, {34120, 34132, 34143, 34155, 34167, 34177, 34189}, {34204, 34209, 34214, 34219, 34224, 34229, 34234}, {0, 0, 0, 0, 0, 0, 0}, {34239, 34248, 34260, 34268, 34273, 34283, 34290, 34299, 34306, 34312, 34321, 34332, 0}, {34239, 34248, 34260, 34268, 34273, 34283, 34290, 34299, 34306, 34312, 34321, 34332, 0}, {34340, 34345, 34350, 34355, 34360, 34365, 34370, 34224, 34375, 34380, 34385, 34390, 0}, {34340, 34345, 34350, 34355, 34360, 34365, 34370, 34224, 34375, 34380, 34385, 34390, 0}, 0, 0, 185, 187, {1545,15492,0,0,0,0,0,0,0,0,0,0,0,0},{20050,16096,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{12916,0,0,0,0,0,0,0}},
	{34395, 34406, 34408, {34410, 34423, 34431, 34440, 34450, 34460, 34469}, {34481, 34485, 34489, 34493, 34497, 34501, 34505}, {2565, 2308, 2300, 7600, 12692, 5644, 1914}, {34509, 34523, 34534, 34543, 34554, 34566, 34580, 34592, 34605, 34618, 34630, 34643, 0}, {34509, 34523, 34534, 34543, 34554, 34566, 34580, 34592, 34605, 34618, 34630, 34643, 0}, {34657, 34662, 34668, 34674, 34679, 34685, 34691, 34696, 34702, 34707, 18931, 34713, 0}, {34657, 34662, 34668, 34674, 34679, 34685, 34691, 34696, 34702, 34707, 18931, 34713, 0}, 2, 1, 185, 187, {198,1129,0,0,0,0,0,0,0,0,0,0,0,0},{34719,34741,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{1, 34826, 34829, {34832, 34843, 34858, 34875, 34892, 34905, 34918}, {34832, 34843, 34858, 34875, 34892, 34905, 34918}, {34929, 34932, 34935, 34938, 34941, 13447, 13438}, {34944, 34968, 34977, 34986, 34997, 35006, 35019, 35028, 35033, 35044, 35066, 35090, 0}, {34944, 34968, 34977, 34986, 34997, 35006, 35019, 35028, 35033, 35044, 35066, 35090, 0}, {34944, 34968, 34977, 34986, 34997, 35006, 35019, 35028, 35033, 35044, 35066, 35090, 0}, {34944, 34968, 34977, 34986, 34997, 35006, 35019, 35028, 35033, 35044, 35066, 35090, 0}, 0, 6, 185, 187, {198,189,1556,0,0,0,0,0,0,0,0,0,0,0},{10501,222,0,0,0,0,0,0,0,0},{242,251,0,0,0,0,0,0,0,0,0,0},{257,269,0,0,0,0,0,0,0},{278,0,0,0,0,0,0,0}},
	{1195, 1204, 1211, {1218, 1228, 1238, 1248, 1258, 1268, 1278}, {1288, 1295, 1302, 1309, 1316, 1323, 1330}, {1337, 1341, 1345, 1349, 1353, 1357, 1361}, {1365, 1372, 1379, 1386, 1393, 1400, 1407, 1414, 1421, 1428, 1435, 1445, 0}, {1365, 1372, 1379, 1386, 1393, 1400, 1407, 1414, 1421, 1428, 1435, 1445, 0}, {1455, 1460, 1465, 1470, 1475, 1480, 1485, 1490, 1495, 1500, 1506, 1512, 0}, {1455, 1460, 1465, 1470, 1475, 1480, 1485, 1490, 1495, 1500, 1506, 1512, 0}, 0, 0, 185, 187, {1518,1527,1536,1545,1556,1567,1578,1585,1592,1599,0,0,0,0},{1608,1630,1658,1686,1701,0,0,0,0,0},{843,251,1722,1730,0,0,0,0,0,0,0,0},{848,269,1739,1750,0,0,0,0,0},{1762,1778,1791,1805,0,0,0,0}},
	{35112, 2471, 2477, {2484, 2492, 2499, 2508, 2517, 2528, 2536}, {2544, 2547, 2550, 2553, 2556, 2559, 2562}, {1914, 2300, 2565, 2300, 2565, 2306, 1914}, {2567, 2574, 2582, 2588, 2594, 2598, 2603, 2608, 2615, 2625, 2633, 2642, 0}, {2567, 2574, 2582, 2588, 2594, 2598, 2603, 2608, 2615, 2625, 2633, 2642, 0}, {2651, 2655, 2659, 2664, 2594, 2668, 2672, 2676, 2680, 2684, 2688, 2692, 0}, {2651, 2655, 2659, 2664, 2594, 2668, 2672, 2676, 2680, 2684, 2688, 2692, 0}, 2, 1, 691, 187, {2154,0,0,0,0,0,0,0,0,0,0,0,0,0},{2719,2194,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{289, 8022, 21796, {3558, 3565, 3572, 3580, 3590, 3599, 3606}, {3615, 3619, 3623, 3627, 3631, 3635, 3639}, {1914, 2300, 2302, 3643, 2302, 2306, 1914}, {3645, 3653, 3662, 2588, 3668, 3672, 3677, 2608, 2615, 3682, 2633, 3690, 0}, {3645, 3653, 3662, 2588, 3668, 3672, 3677, 2608, 2615, 3682, 2633, 3690, 0}, {2651, 2655, 3699, 2664, 3668, 2668, 2672, 2676, 2680, 3703, 2688, 3707, 0}, {2651, 2655, 3699, 2664, 3668, 2668, 2672, 2676, 2680, 3703, 2688, 3707, 0}, 2, 1, 185, 187, {198,189,3483,8015,1556,0,0,0,0,0,0,0,0,0},{5455,3520,3502,10640,0,0,0,0,0,0},{251,843,242,3532,0,0,0,0,0,0,0,0},{269,848,257,3540,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{3821, 7261, 7266, {3833, 3841, 3847, 3854, 3865, 3872, 3880}, {35121, 6196, 4795, 35126, 35132, 35137, 35141}, {2565, 2308, 2300, 29116, 3925, 3927, 1914}, {4018, 4024, 4032, 1012, 4038, 4043, 4049, 4055, 4062, 1050, 4073, 4083, 0}, {4018, 4024, 4032, 1012, 4038, 4043, 4049, 4055, 4062, 1050, 4073, 4083, 0}, {35147, 5753, 35151, 1086, 35156, 2410, 2414, 1096, 1100, 1105, 1110, 35160, 0}, {35147, 5753, 35151, 1086, 35156, 2410, 2414, 1096, 1100, 1105, 1110, 35160, 0}, 0, 0, 185, 187, {198,3490,189,4149,3483,2451,1556,0,0,0,0,0,0,0},{4164,4219,0,0,0,0,0,0,0,0},{242,3532,843,251,0,0,0,0,0,0,0,0},{257,3540,848,269,0,0,0,0,0},{4288,0,0,0,0,0,0,0}},
	{289, 1820, 1823, {4733, 4742, 4748, 4754, 4763, 4769, 4778}, {4785, 4790, 4795, 4800, 4805, 4810, 4815}, {2565, 2308, 2300, 2300, 3925, 3927, 1914}, {4820, 4828, 4837, 4842, 4848, 4852, 4857, 4865, 4871, 4881, 1058, 4889, 0}, {4820, 4828, 4837, 4842, 4848, 4852, 4857, 4865, 4871, 4881, 1058, 4889, 0}, {4899, 4905, 4837, 4912, 4848, 4852, 4917, 4865, 4923, 1105, 1110, 4929, 0}, {4899, 4905, 4837, 4912, 4848, 4852, 4917, 4865, 4923, 1105, 1110, 4929, 0}, 2, 1, 2438, 187, {2451,198,189,2440,0,0,0,0,0,0,0,0,0,0},{4935,3520,0,0,0,0,0,0,0,0},{251,843,4246,4961,4971,0,0,0,0,0,0,0},{269,848,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{35165, 1820, 1823, {6130, 6139, 6147, 6156, 6167, 6176, 6185}, {6192, 6196, 2402, 6200, 6204, 6208, 6212}, {2565, 2308, 2300, 2300, 6216, 3927, 1914}, {6218, 6226, 3943, 6235, 6242, 6249, 6256, 3972, 6263, 6273, 6281, 6290, 0}, {6299, 6307, 4032, 6316, 6323, 6330, 6337, 4055, 6344, 6354, 1058, 6362, 0}, {6371, 2398, 2402, 2406, 6375, 6379, 6383, 6387, 6391, 6395, 2430, 6399, 0}, {6371, 2398, 2402, 2406, 6375, 6379, 6383, 6387, 6391, 6395, 2430, 6399, 0}, 2, 1, 691, 187, {2154,14774,0,0,0,0,0,0,0,0,0,0,0,0},{3502,3520,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{35173,0,0,0,0,0,0,0}},
	{289, 7261, 7266, {7271, 7278, 7286, 7294, 7303, 7313, 7321}, {7330, 4395, 7333, 7336, 7339, 7342, 7345}, {7348, 2300, 2565, 3643, 2565, 3927, 7348}, {7350, 7358, 7367, 2331, 7373, 2341, 2346, 7377, 2358, 2368, 2376, 2385, 0}, {7350, 7358, 7367, 2331, 7373, 2341, 2346, 7377, 2358, 2368, 2376, 2385, 0}, {2394, 2398, 7386, 2406, 7373, 2410, 2414, 2418, 2422, 2426, 2430, 2434, 0}, {2394, 2398, 7386, 2406, 7373, 2410, 2414, 2418, 2422, 2426, 2430, 2434, 0}, 2, 1, 185, 187, {14753,4149,2451,2696,7399,1556,0,0,0,0,0,0,0,0},{4935,3747,3520,5498,0,0,0,0,0,0},{843,251,35183,0,0,0,0,0,0,0,0,0},{848,269,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{1812, 35193, 35198, {2217, 9837, 35203, 2240, 2247, 2255, 35210}, {2270, 9865, 35218, 2283, 2287, 2291, 35222}, {1914, 2300, 2302, 2304, 2302, 2306, 2308}, {2310, 2317, 4837, 2331, 4848, 2341, 2346, 2351, 2358, 2368, 2376, 6086, 0}, {2310, 2317, 4837, 2331, 4848, 2341, 2346, 2351, 2358, 2368, 2376, 6086, 0}, {2394, 2398, 2402, 2406, 4848, 2410, 2414, 2418, 2422, 2426, 2430, 7503, 0}, {2394, 2398, 2402, 2406, 4848, 2410, 2414, 2418, 2422, 2426, 2430, 7503, 0}, 2, 1, 691, 187, {2154,6118,0,0,0,0,0,0,0,0,0,0,0,0},{2176,2194,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{3821, 7261, 7266, {3833, 7830, 7844, 7857, 7870, 7883, 3880}, {6192, 7895, 7899, 7903, 7907, 7911, 7915}, {2565, 1914, 2302, 7920, 7920, 1914, 1914}, {35226, 35234, 35244, 3949, 29135, 35251, 35257, 3972, 29152, 29161, 29169, 35263, 0}, {35226, 35234, 35244, 3949, 29135, 35251, 35257, 3972, 29152, 29161, 29169, 35263, 0}, {2651, 20033, 3699, 29229, 2594, 2668, 2672, 19914, 18218, 29242, 2688, 2692, 0}, {2651, 20033, 3699, 29229, 2594, 2668, 2672, 19914, 18218, 29242, 2688, 2692, 0}, 2, 1, 185, 187, {198,2154,2440,189,2696,2451,1545,1567,1556,1599,35272,6936,0,0},{4219,4164,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{4288,0,0,0,0,0,0,0}},
	{289, 9823, 9826, {9829, 9837, 9845, 2240, 2247, 2255, 9852}, {9860, 9865, 9870, 2283, 2287, 2291, 9874}, {1914, 2300, 2302, 2304, 2302, 2306, 2308}, {7350, 7358, 4837, 2331, 2337, 2341, 2346, 9879, 2358, 2368, 2376, 2385, 0}, {7350, 7358, 4837, 2331, 2337, 2341, 2346, 9879, 2358, 2368, 2376, 2385, 0}, {2394, 2398, 2402, 2406, 2337, 2410, 2414, 2418, 2422, 2426, 2430, 2434, 0}, {2394, 2398, 2402, 2406, 2337, 2410, 2414, 2418, 2422, 2426, 2430, 2434, 0}, 2, 1, 2438, 187, {2440,1129,0,0,0,0,0,0,0,0,0,0,0,0},{4935,3520,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{289, 1820, 1823, {35281, 35292, 35316, 35346, 35363, 35385, 35394}, {14921, 14924, 14929, 14935, 14939, 7600, 14944}, {6540, 6528, 6530, 6532, 6534, 6536, 6538}, {35405, 35418, 501, 35431, 521, 35442, 35451, 542, 35460, 35477, 35492, 35505, 0}, {35405, 35418, 501, 35431, 521, 35442, 35451, 542, 35460, 35477, 35492, 35505, 0}, {15095, 7999, 2402, 2406, 15043, 15099, 15103, 15107, 15111, 2426, 15115, 15119, 0}, {15095, 7999, 2402, 2406, 15043, 15099, 15103, 15107, 15111, 2426, 15115, 15119, 0}, 0, 1, 691, 187, {2154,2696,8015,189,1556,0,0,0,0,0,0,0,0,0},{3520,5455,0,0,0,0,0,0,0,0},{843,251,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{289, 7261, 7266, {18648, 18662, 18672, 18683, 18697, 18708, 18719}, {18732, 18737, 18742, 18749, 18755, 18761, 18767}, {2565, 2308, 2300, 7600, 2565, 12692, 1914}, {18772, 18780, 18788, 18795, 18804, 18814, 18824, 18830, 18838, 18853, 18871, 18879, 0}, {18772, 18780, 18788, 18795, 18804, 18814, 18824, 18830, 18838, 18853, 18871, 18879, 0}, {18887, 18891, 18788, 18897, 18901, 18906, 18824, 18912, 18917, 18924, 18931, 18936, 0}, {18887, 18891, 18788, 18897, 18901, 18906, 18824, 18912, 18917, 18924, 18931, 18936, 0}, 2, 0, 185, 187, {198,1129,0,0,0,0,0,0,0,0,0,0,0,0},{4935,3520,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{3551, 1820, 1823, {35518, 35533, 35548, 35563, 35580, 35597, 35606}, {35617, 35624, 35631, 35638, 35645, 19574, 35652}, {35659, 19207, 8610, 8613, 8607, 19204, 19588}, {13102, 13113, 8644, 13126, 8666, 13137, 13144, 8691, 13151, 13166, 13179, 13190, 0}, {13102, 13113, 8644, 13126, 8666, 13137, 13144, 8691, 13151, 13166, 13179, 13190, 0}, {13203, 13210, 13217, 13224, 8666, 13137, 13144, 13231, 13238, 13245, 13252, 13259, 0}, {13203, 13210, 13217, 13224, 8666, 13137, 13144, 13231, 13238, 13245, 13252, 13259, 0}, 0, 1, 185, 187, {1545,15492,0,0,0,0,0,0,0,0,0,0,0,0},{20050,16096,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{12916,0,0,0,0,0,0,0}},
	{1, 20069, 20097, {20119, 20138, 20157, 20182, 20201, 20235, 20260}, {20279, 20289, 20299, 20315, 20325, 20350, 20366}, {20376, 20380, 20387, 20391, 20398, 20405, 20412}, {20416, 20444, 20478, 20494, 20513, 20520, 20530, 20546, 20562, 20593, 20615, 20637, 0}, {20416, 20444, 20478, 20494, 20513, 20520, 20530, 20546, 20562, 20593, 20615, 20637, 0}, {20416, 20444, 20478, 20494, 20513, 20520, 20530, 20546, 20562, 20593, 20615, 20637, 0}, {20416, 20444, 20478, 20494, 20513, 20520, 20530, 20546, 20562, 20593, 20615, 20637, 0}, 0, 5, 2438, 691, {2451,4157,8015,2440,1556,0,0,0,0,0,0,0,0,0},{5455,3520,0,0,0,0,0,0,0,0},{4246,4241,20662,20671,0,0,0,0,0,0,0,0},{4268,4260,20679,20691,0,0,0,0,0},{278,0,0,0,0,0,0,0}},
	{1, 34826, 34829, {34832, 34843, 34858, 34875, 34892, 34905, 34918}, {34832, 34843, 34858, 34875, 34892, 34905, 34918}, {34929, 34932, 34935, 34938, 34941, 13447, 13438}, {35662, 35673, 13477, 35686, 35697, 35706, 35717, 35728, 35739, 35752, 35765, 35778, 0}, {35662, 35673, 13477, 35686, 35697, 35706, 35717, 35728, 35739, 35752, 35765, 35778, 0}, {35662, 35673, 13477, 35686, 35697, 35706, 35717, 35728, 35739, 35752, 35765, 35778, 0}, {35662, 35673, 13477, 35686, 35697, 35706, 35717, 35728, 35739, 35752, 35765, 35778, 0}, 0, 6, 185, 187, {198,189,1556,0,0,0,0,0,0,0,0,0,0,0},{10501,222,0,0,0,0,0,0,0,0},{242,251,0,0,0,0,0,0,0,0,0,0},{257,269,0,0,0,0,0,0,0},{278,0,0,0,0,0,0,0}},
	{1195, 1204, 1211, {1218, 1228, 1238, 1248, 1258, 1268, 1278}, {34757, 34764, 34771, 34778, 34785, 34792, 34799}, {1337, 1341, 1345, 1349, 1353, 1357, 1361}, {1455, 1460, 1465, 1470, 1475, 1480, 1485, 1490, 1495, 1500, 1506, 1512, 0}, {1455, 1460, 1465, 1470, 1475, 1480, 1485, 1490, 1495, 1500, 1506, 1512, 0}, {1455, 1460, 1465, 1470, 1475, 1480, 1485, 1490, 1495, 1500, 1506, 1512, 0}, {1455, 1460, 1465, 1470, 1475, 1480, 1485, 1490, 1495, 1500, 1506, 1512, 0}, 0, 0, 185, 187, {1120,3483,189,1578,1599,1518,1545,1556,0,0,0,0,0,0},{1608,6555,1686,1701,0,0,0,0,0,0},{843,251,0,0,0,0,0,0,0,0,0,0},{848,269,0,0,0,0,0,0,0},{1762,1778,1791,1805,0,0,0,0}},
	{35112, 2471, 2477, {2484, 2492, 2499, 2508, 2517, 2528, 2536}, {2544, 2547, 2550, 2553, 2556, 2559, 2562}, {1914, 2300, 2565, 2300, 2565, 2306, 1914}, {35791, 2574, 2582, 2588, 2594, 2598, 2603, 2608, 2615, 2625, 2633, 2642, 0}, {35791, 2574, 2582, 2588, 2594, 2598, 2603, 2608, 2615, 2625, 2633, 2642, 0}, {35799, 2655, 2659, 2664, 2594, 2668, 2672, 2676, 2680, 2684, 2688, 2692, 0}, {35799, 2655, 2659, 2664, 2594, 2668, 2672, 2676, 2680, 2684, 2688, 2692, 0}, 2, 1, 691, 187, {2154,0,0,0,0,0,0,0,0,0,0,0,0,0},{2719,2194,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{289, 1820, 1823, {3558, 3565, 3572, 3580, 3590, 3599, 3606}, {3615, 3619, 3623, 3627, 3631, 3635, 3639}, {1914, 2300, 2302, 3643, 2302, 2306, 1914}, {3645, 3653, 3662, 2588, 3668, 3672, 3677, 2608, 2615, 3682, 2633, 3690, 0}, {3645, 3653, 3662, 2588, 3668, 3672, 3677, 2608, 2615, 3682, 2633, 3690, 0}, {2651, 2655, 3699, 2664, 3668, 2668, 2672, 2676, 2680, 3703, 2688, 3707, 0}, {2651, 2655, 3699, 2664, 3668, 2668, 2672, 2676, 2680, 3703, 2688, 3707, 0}, 0, 0, 185, 187, {14753,4149,3483,1120,189,198,3747,5468,1556,1599,1545,0,0,0},{3502,3520,0,0,0,0,0,0,0,0},{3532,843,251,0,0,0,0,0,0,0,0,0},{3540,848,269,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{3821, 872, 878, {3833, 3841, 3847, 3854, 3865, 3872, 3880}, {3888, 3893, 3898, 3903, 3909, 3914, 3919}, {2565, 2308, 2300, 2300, 3925, 3927, 1914}, {3929, 3935, 3943, 3949, 3955, 3960, 3966, 3972, 3979, 3990, 3998, 4008, 0}, {4018, 4024, 4032, 1012, 4038, 4043, 4049, 4055, 4062, 1050, 4073, 4083, 0}, {4093, 4098, 3898, 4103, 4108, 4113, 4118, 4123, 4128, 4134, 4139, 4144, 0}, {4093, 4098, 3898, 4103, 4108, 4113, 4118, 4123, 4128, 4134, 4139, 4144, 0}, 2, 1, 185, 187, {198,189,4149,3483,4157,2451,2696,1556,0,0,0,0,0,0},{4164,4192,4219,0,0,0,0,0,0,0},{843,251,4241,4246,4252,0,0,0,0,0,0,0},{848,269,4260,4268,4277,0,0,0,0},{4288,0,0,0,0,0,0,0}},
	{289, 1820, 1823, {4733, 4742, 4748, 4754, 4763, 4769, 4778}, {4785, 4790, 4795, 4800, 4805, 4810, 4815}, {2565, 2308, 2300, 2300, 3925, 3927, 1914}, {4820, 4828, 4837, 4842, 4848, 4852, 4857, 4865, 4871, 4881, 1058, 4889, 0}, {4820, 4828, 4837, 4842, 4848, 4852, 4857, 4865, 4871, 4881, 1058, 4889, 0}, {4899, 4905, 4837, 4912, 4848, 4852, 4917, 4865, 4923, 1105, 1110, 4929, 0}, {4899, 4905, 4837, 4912, 4848, 4852, 4917, 4865, 4923, 1105, 1110, 4929, 0}, 0, 0, 2438, 187, {1556,6936,2451,35804,189,3490,0,0,0,0,0,0,0,0},{3520,1129,0,0,0,0,0,0,0,0},{251,843,4246,4961,4971,0,0,0,0,0,0,0},{269,848,0,0,0,0,0,0,0},{278,0,0,0,0,0,0,0}},
	{35813, 18295, 18300, {35826, 35834, 35842, 35850, 35862, 35871, 35883}, {18385, 18390, 18395, 18400, 18405, 18410, 18415}, {1914, 2300, 2565, 6216, 2565, 16338, 2308}, {18420, 18437, 18450, 18464, 18477, 18490, 18503, 18517, 18529, 18543, 18557, 18571, 0}, {18420, 18437, 18450, 18464, 18477, 18490, 18503, 18517, 18529, 18543, 18557, 18571, 0}, {35894, 35905, 35912, 35920, 35927, 35934, 35941, 35949, 35955, 35963, 35971, 35979, 0}, {35894, 35905, 35912, 35920, 35927, 35934, 35941, 35949, 35955, 35963, 35971, 35979, 0}, 2, 1, 691, 187, {4724,2154,8015,1556,0,0,0,0,0,0,0,0,0,0},{35986,36012,0,0,0,0,0,0,0,0},{843,251,0,0,0,0,0,0,0,0,0,0},{848,269,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{1, 34826, 34829, {34832, 34843, 34858, 34875, 34892, 34905, 34918}, {34832, 34843, 34858, 34875, 34892, 34905, 34918}, {34929, 34932, 34935, 34938, 34941, 13447, 13438}, {35662, 35673, 13477, 35686, 35697, 35706, 35717, 35728, 35739, 35752, 35765, 35778, 0}, {35662, 35673, 13477, 35686, 35697, 35706, 35717, 35728, 35739, 35752, 35765, 35778, 0}, {35662, 35673, 13477, 35686, 35697, 35706, 35717, 35728, 35739, 35752, 35765, 35778, 0}, {35662, 35673, 13477, 35686, 35697, 35706, 35717, 35728, 35739, 35752, 35765, 35778, 0}, 0, 6, 185, 187, {198,189,1556,0,0,0,0,0,0,0,0,0,0,0},{10501,222,0,0,0,0,0,0,0,0},{242,251,0,0,0,0,0,0,0,0,0,0},{257,269,0,0,0,0,0,0,0},{278,0,0,0,0,0,0,0}},
	{1195, 1204, 1211, {1218, 1228, 1238, 1248, 1258, 1268, 1278}, {1288, 1295, 1302, 1309, 1316, 1323, 1330}, {1337, 1341, 1345, 1349, 1353, 1357, 1361}, {1365, 1372, 1379, 1386, 1393, 1400, 1407, 1414, 1421, 1428, 1435, 1445, 0}, {1365, 1372, 1379, 1386, 1393, 1400, 1407, 1414, 1421, 1428, 1435, 1445, 0}, {1455, 1460, 1465, 1470, 1475, 1480, 1485, 1490, 1495, 1500, 1506, 1512, 0}, {1455, 1460, 1465, 1470, 1475, 1480, 1485, 1490, 1495, 1500, 1506, 1512, 0}, 0, 0, 185, 187, {1120,3483,189,1578,1599,1518,1545,1556,0,0,0,0,0,0},{1608,6555,1686,34806,0,0,0,0,0,0},{1722,1730,843,251,0,0,0,0,0,0,0,0},{1739,1750,848,269,0,0,0,0,0},{1762,1778,1791,0,0,0,0,0}},
	{35112, 2471, 2477, {2484, 2492, 2499, 2508, 2517, 2528, 2536}, {2544, 2547, 2550, 2553, 2556, 2559, 2562}, {1914, 2300, 2565, 2300, 2565, 2306, 1914}, {2567, 2574, 2582, 2588, 2594, 2598, 2603, 2608, 2615, 2625, 2633, 2642, 0}, {2567, 2574, 2582, 2588, 2594, 2598, 2603, 2608, 2615, 2625, 2633, 2642, 0}, {2651, 2655, 2659, 2664, 2594, 2668, 2672, 2676, 2680, 2684, 2688, 2692, 0}, {2651, 2655, 2659, 2664, 2594, 2668, 2672, 2676, 2680, 2684, 2688, 2692, 0}, 2, 1, 691, 187, {2154,0,0,0,0,0,0,0,0,0,0,0,0,0},{2719,2194,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{289, 1820, 1823, {3558, 3565, 3572, 3580, 3590, 3599, 3606}, {3615, 3619, 3623, 3627, 3631, 3635, 3639}, {1914, 2300, 2302, 3643, 2302, 2306, 1914}, {3645, 3653, 3662, 2588, 3668, 3672, 3677, 2608, 2615, 3682, 2633, 3690, 0}, {3645, 3653, 3662, 2588, 3668, 3672, 3677, 2608, 2615, 3682, 2633, 3690, 0}, {2651, 2655, 3699, 2664, 3668, 2668, 2672, 2676, 2680, 3703, 2688, 3707, 0}, {2651, 2655, 3699, 2664, 3668, 2668, 2672, 2676, 2680, 3703, 2688, 3707, 0}, 0, 0, 2438, 187, {1556,198,189,3483,6936,36030,3747,14785,1556,0,0,0,0,0},{3776,31111,36038,6411,0,0,0,0,0,0},{3532,242,251,843,0,0,0,0,0,0,0,0},{3540,257,269,848,0,0,0,0,0},{278,0,0,0,0,0,0,0}},
	{36049, 872, 878, {3833, 3841, 3847, 3854, 3865, 3872, 3880}, {3888, 3893, 3898, 3903, 3909, 3914, 3919}, {2565, 2308, 2300, 2300, 3925, 3927, 1914}, {3929, 3935, 3943, 3949, 3955, 3960, 3966, 3972, 3979, 3990, 3998, 4008, 0}, {4018, 4024, 4032, 1012, 4038, 4043, 4049, 4055, 4062, 1050, 4073, 4083, 0}, {4093, 4098, 3898, 4103, 4108, 4113, 4118, 4123, 4128, 4134, 4139, 4144, 0}, {4093, 4098, 3898, 4103, 4108, 4113, 4118, 4123, 4128, 4134, 4139, 4144, 0}, 0, 0, 185, 187, {14753,0,0,0,0,0,0,0,0,0,0,0,0,0},{36057,36085,0,0,0,0,0,0,0,0},{3532,251,0,0,0,0,0,0,0,0,0,0},{3540,269,0,0,0,0,0,0,0},{1180,0,0,0,0,0,0,0}},
	{35165, 1820, 1823, {4733, 4742, 4748, 4754, 4763, 4769, 4778}, {4785, 4790, 4795, 4800, 4805, 4810, 4815}, {2565, 2308, 2300, 2300, 3925, 3927, 1914}, {4820, 4828, 4837, 4842, 4848, 4852, 4857, 4865, 4871, 4881, 1058, 4889, 0}, {4820, 4828, 4837, 4842, 4848, 4852, 4857, 4865, 4871, 4881, 1058, 4889, 0}, {4899, 4905, 4837, 4912, 4848, 4852, 4917, 4865, 4923, 1105, 1110, 4929, 0}, {4899, 4905, 4837, 4912, 4848, 4852, 4917, 4865, 4923, 1105, 1110, 4929, 0}, 2, 1, 691, 187, {2154,1129,0,0,0,0,0,0,0,0,0,0,0,0},{3502,3520,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{35173,0,0,0,0,0,0,0}},
	{1812, 1820, 1823, {8986, 8995, 9007, 9014, 9022, 9032, 9038}, {9045, 9049, 9053, 9057, 9061, 9066, 9070}, {9074, 9076, 9078, 9080, 9082, 9076, 9080}, {9085, 9095, 9104, 9112, 9120, 9128, 9135, 9142, 9150, 1993, 9156, 9164, 0}, {9173, 9183, 9192, 9200, 9208, 9216, 9223, 9230, 9239, 7775, 9245, 9255, 0}, {9264, 9268, 9273, 9278, 9282, 7809, 2130, 9286, 9290, 2146, 9294, 2150, 0}, {9264, 9268, 9273, 9278, 9282, 7809, 2130, 9286, 9290, 2146, 9294, 2150, 0}, 0, 1, 691, 187, {9328,36107,0,0,0,0,0,0,0,0,0,0,0,0},{9401,9372,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{36120,0,0,0,0,0,0,0}},
	{1, 34826, 34829, {34832, 34843, 34858, 34875, 34892, 34905, 34918}, {34832, 34843, 34858, 34875, 34892, 34905, 34918}, {34929, 34932, 34935, 34938, 34941, 13447, 13438}, {36131, 36142, 13477, 36153, 36164, 36171, 36180, 36193, 35739, 35752, 35765, 35778, 0}, {36131, 36142, 13477, 36153, 36164, 36171, 36180, 36193, 35739, 35752, 35765, 35778, 0}, {36131, 36142, 13477, 36153, 36164, 36171, 36180, 36193, 35739, 35752, 35765, 35778, 0}, {36131, 36142, 13477, 36153, 36164, 36171, 36180, 36193, 35739, 35752, 35765, 35778, 0}, 0, 6, 2438, 187, {2440,2451,1556,0,0,0,0,0,0,0,0,0,0,0},{10501,222,0,0,0,0,0,0,0,0},{843,251,242,0,0,0,0,0,0,0,0,0},{848,269,257,0,0,0,0,0,0},{278,0,0,0,0,0,0,0}},
	{1195, 1204, 1211, {1218, 1228, 1238, 1248, 1258, 1268, 1278}, {34757, 34764, 34771, 34778, 34785, 34792, 34799}, {1337, 1341, 1345, 1349, 1353, 1357, 1361}, {1455, 1460, 1465, 1470, 1475, 1480, 1485, 1490, 1495, 1500, 1506, 1512, 0}, {1455, 1460, 1465, 1470, 1475, 1480, 1485, 1490, 1495, 1500, 1506, 1512, 0}, {1455, 1460, 1465, 1470, 1475, 1480, 1485, 1490, 1495, 1500, 1506, 1512, 0}, {1455, 1460, 1465, 1470, 1475, 1480, 1485, 1490, 1495, 1500, 1506, 1512, 0}, 0, 0, 185, 187, {1120,3483,189,1578,1599,1518,1545,1556,0,0,0,0,0,0},{1608,36200,36227,2460,1686,34806,0,0,0,0},{843,251,0,0,0,0,0,0,0,0,0,0},{848,269,0,0,0,0,0,0,0},{1762,1778,1791,1805,0,0,0,0}},
	{35112, 2471, 2477, {2484, 2492, 2499, 2508, 2517, 2528, 2536}, {2544, 2547, 2550, 2553, 2556, 2559, 2562}, {1914, 2300, 2565, 2300, 2565, 2306, 1914}, {2567, 2574, 2582, 2588, 2594, 2598, 2603, 2608, 2615, 2625, 2633, 2642, 0}, {2567, 2574, 2582, 2588, 2594, 2598, 2603, 2608, 2615, 2625, 2633, 2642, 0}, {2651, 2655, 2659, 2664, 2594, 2668, 2672, 2676, 2680, 2684, 2688, 2692, 0}, {2651, 2655, 2659, 2664, 2594, 2668, 2672, 2676, 2680, 2684, 2688, 2692, 0}, 2, 1, 691, 187, {2154,0,0,0,0,0,0,0,0,0,0,0,0,0},{2719,2194,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{36243, 7261, 7266, {3558, 3565, 3572, 3580, 3590, 3599, 3606}, {3615, 3619, 3623, 3627, 3631, 3635, 3639}, {1914, 2300, 2302, 3643, 2302, 2306, 1914}, {3645, 3653, 3662, 2588, 3668, 3672, 3677, 2608, 2615, 3682, 2633, 3690, 0}, {3645, 3653, 3662, 2588, 3668, 3672, 3677, 2608, 2615, 3682, 2633, 3690, 0}, {2651, 2655, 3699, 2664, 3668, 2668, 2672, 2676, 2680, 3703, 2688, 3707, 0}, {2651, 2655, 3699, 2664, 3668, 2668, 2672, 2676, 2680, 3703, 2688, 3707, 0}, 0, 0, 185, 187, {14753,0,0,0,0,0,0,0,0,0,0,0,0,0},{3502,3520,0,0,0,0,0,0,0,0},{3532,251,0,0,0,0,0,0,0,0,0,0},{3540,269,0,0,0,0,0,0,0},{36250,0,0,0,0,0,0,0}},
	{36049, 872, 878, {3833, 3841, 3847, 3854, 3865, 3872, 3880}, {3888, 3893, 3898, 3903, 3909, 3914, 3919}, {2565, 2308, 2300, 2300, 3925, 3927, 1914}, {3929, 3935, 3943, 3949, 3955, 3960, 3966, 3972, 3979, 3990, 3998, 4008, 0}, {4018, 4024, 4032, 1012, 4038, 4043, 4049, 4055, 4062, 1050, 4073, 4083, 0}, {4093, 4098, 3898, 4103, 4108, 4113, 4118, 4123, 4128, 4134, 4139, 4144, 0}, {4093, 4098, 3898, 4103, 4108, 4113, 4118, 4123, 4128, 4134, 4139, 4144, 0}, 0, 1, 185, 187, {1120,1129,0,0,0,0,0,0,0,0,0,0,0,0},{36057,36085,0,0,0,0,0,0,0,0},{3532,251,0,0,0,0,0,0,0,0,0,0},{3540,269,0,0,0,0,0,0,0},{1180,0,0,0,0,0,0,0}},
	{289, 1820, 1823, {4733, 4742, 4748, 4754, 4763, 4769, 4778}, {4785, 4790, 4795, 4800, 4805, 4810, 4815}, {2565, 2308, 2300, 2300, 3925, 3927, 1914}, {4820, 4828, 4837, 4842, 4848, 4852, 4857, 4865, 4871, 4881, 1058, 4889, 0}, {4820, 4828, 4837, 4842, 4848, 4852, 4857, 4865, 4871, 4881, 1058, 4889, 0}, {4899, 4905, 4837, 4912, 4848, 4852, 4917, 4865, 4923, 1105, 1110, 4929, 0}, {4899, 4905, 4837, 4912, 4848, 4852, 4917, 4865, 4923, 1105, 1110, 4929, 0}, 2, 1, 185, 187, {198,1129,0,0,0,0,0,0,0,0,0,0,0,0},{4935,3520,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{1812, 36260, 36270, {8986, 8995, 9007, 9014, 9022, 9032, 9038}, {9045, 9049, 9053, 9057, 9061, 9066, 9070}, {0, 0, 0, 0, 0, 0, 0}, {2310, 2317, 15032, 2331, 2337, 2341, 2346, 11888, 36278, 36288, 36296, 36305, 0}, {2310, 2317, 15032, 2331, 2337, 2341, 2346, 11888, 36278, 36288, 36296, 36305, 0}, {2394, 2398, 2402, 2406, 2337, 2410, 2414, 11895, 2422, 2426, 2430, 2434, 0}, {2394, 2398, 2402, 2406, 2337, 2410, 2414, 11895, 2422, 2426, 2430, 2434, 0}, 0, 1, 691, 187, {9328,36314,0,0,0,0,0,0,0,0,0,0,0,0},{36329,9386,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{36120,0,0,0,0,0,0,0}},
	{1, 34826, 34829, {34832, 34843, 34858, 34875, 34892, 34905, 34918}, {34832, 34843, 34858, 34875, 34892, 34905, 34918}, {34929, 34932, 34935, 34938, 34941, 13447, 13438}, {35662, 35673, 13477, 35686, 36164, 35706, 36350, 36363, 36370, 35752, 36381, 36392, 0}, {35662, 35673, 13477, 35686, 36164, 35706, 36350, 36363, 36370, 35752, 36381, 36392, 0}, {35662, 35673, 13477, 35686, 36164, 35706, 36350, 36363, 36370, 35752, 36381, 36392, 0}, {35662, 35673, 13477, 35686, 36164, 35706, 36350, 36363, 36370, 35752, 36381, 36392, 0}, 0, 6, 2438, 187, {2440,2451,1556,0,0,0,0,0,0,0,0,0,0,0},{10501,222,0,0,0,0,0,0,0,0},{843,251,242,0,0,0,0,0,0,0,0,0},{848,269,257,0,0,0,0,0,0},{278,0,0,0,0,0,0,0}},
	{36243, 7261, 7266, {3558, 3565, 3572, 3580, 3590, 3599, 3606}, {3615, 3619, 3623, 3627, 3631, 3635, 3639}, {1914, 2300, 2302, 3643, 2302, 2306, 1914}, {3645, 3653, 3662, 2588, 3668, 3672, 3677, 2608, 2615, 3682, 2633, 3690, 0}, {3645, 3653, 3662, 2588, 3668, 3672, 3677, 2608, 2615, 3682, 2633, 3690, 0}, {2651, 2655, 3699, 2664, 3668, 2668, 2672, 2676, 2680, 3703, 2688, 3707, 0}, {2651, 2655, 3699, 2664, 3668, 2668, 2672, 2676, 2680, 3703, 2688, 3707, 0}, 2, 0, 185, 187, {198,1129,0,0,0,0,0,0,0,0,0,0,0,0},{4935,3520,0,0,0,0,0,0,0,0},{251,3532,0,0,0,0,0,0,0,0,0,0},{269,3540,0,0,0,0,0,0,0},{36250,0,0,0,0,0,0,0}},
	{36403, 872, 878, {3833, 3841, 3847, 3854, 3865, 3872, 3880}, {3888, 3893, 3898, 3903, 3909, 3914, 3919}, {2565, 2308, 2300, 2300, 3925, 3927, 1914}, {3929, 3935, 3943, 3949, 3955, 3960, 3966, 3972, 3979, 3990, 3998, 4008, 0}, {4018, 4024, 4032, 1012, 4038, 4043, 4049, 4055, 4062, 1050, 4073, 4083, 0}, {4093, 4098, 3898, 4103, 4108, 4113, 4118, 4123, 4128, 4134, 4139, 4144, 0}, {4093, 4098, 3898, 4103, 4108, 4113, 4118, 4123, 4128, 4134, 4139, 4144, 0}, 0, 0, 185, 187, {3736,0,0,0,0,0,0,0,0,0,0,0,0,0},{36057,36085,0,0,0,0,0,0,0,0},{3532,251,0,0,0,0,0,0,0,0,0,0},{3540,269,0,0,0,0,0,0,0},{36250,0,0,0,0,0,0,0}},
	{289, 1820, 1823, {4733, 4742, 4748, 4754, 4763, 4769, 4778}, {4785, 4790, 4795, 4800, 4805, 4810, 4815}, {2565, 2308, 2300, 2300, 3925, 3927, 1914}, {4820, 4828, 4837, 4842, 4848, 4852, 4857, 4865, 4871, 4881, 1058, 4889, 0}, {4820, 4828, 4837, 4842, 4848, 4852, 4857, 4865, 4871, 4881, 1058, 4889, 0}, {4899, 4905, 4837, 4912, 4848, 4852, 4917, 4865, 4923, 1105, 1110, 4929, 0}, {4899, 4905, 4837, 4912, 4848, 4852, 4917, 4865, 4923, 1105, 1110, 4929, 0}, 2, 1, 185, 187, {198,1129,0,0,0,0,0,0,0,0,0,0,0,0},{4935,3520,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{1812, 36260, 36270, {11820, 36411, 9007, 11845, 9022, 9032, 9038}, {9045, 9049, 9053, 11866, 9061, 9066, 9070}, {9074, 9076, 9078, 9080, 9082, 9076, 9080}, {2310, 2317, 15032, 2331, 2337, 2410, 2414, 11888, 36278, 36288, 36296, 36305, 0}, {2310, 2317, 15032, 2331, 2337, 2410, 2414, 11888, 36278, 36288, 36296, 36305, 0}, {2394, 2398, 2402, 2406, 2337, 2410, 2414, 11895, 2422, 2426, 2430, 2434, 0}, {2394, 2398, 2402, 2406, 2337, 2410, 2414, 11895, 2422, 2426, 2430, 2434, 0}, 0, 1, 691, 187, {9298,9328,0,0,0,0,0,0,0,0,0,0,0,0},{36329,9386,0,0,0,0,0,0,0,0},{251,4246,0,0,0,0,0,0,0,0,0,0},{269,4268,0,0,0,0,0,0,0},{36120,0,0,0,0,0,0,0}},
	{1, 34826, 34829, {34832, 34843, 34858, 34875, 34892, 34905, 34918}, {34832, 34843, 34858, 34875, 34892, 34905, 34918}, {34929, 34932, 34935, 34938, 34941, 13447, 13438}, {36131, 36142, 13477, 36153, 36164, 36171, 36180, 36193, 35739, 35752, 35765, 35778, 0}, {36131, 36142, 13477, 36153, 36164, 36171, 36180, 36193, 35739, 35752, 35765, 35778, 0}, {36131, 36142, 13477, 36153, 36164, 36171, 36180, 36193, 35739, 35752, 35765, 35778, 0}, {36131, 36142, 13477, 36153, 36164, 36171, 36180, 36193, 35739, 35752, 35765, 35778, 0}, 0, 0, 2438, 187, {2440,2451,1556,0,0,0,0,0,0,0,0,0,0,0},{10501,222,0,0,0,0,0,0,0,0},{843,251,242,0,0,0,0,0,0,0,0,0},{848,269,257,0,0,0,0,0,0},{278,0,0,0,0,0,0,0}},
	{1, 1820, 1823, {3558, 3565, 3572, 3580, 3590, 3599, 3606}, {3615, 3619, 3623, 3627, 3631, 3635, 3639}, {1914, 2300, 2302, 3643, 2302, 2306, 1914}, {3645, 3653, 3662, 2588, 3668, 3672, 3677, 2608, 2615, 3682, 2633, 3690, 0}, {3645, 3653, 3662, 2588, 3668, 3672, 3677, 2608, 2615, 3682, 2633, 3690, 0}, {2651, 2655, 3699, 2664, 3668, 2668, 2672, 2676, 2680, 3703, 2688, 3707, 0}, {2651, 2655, 3699, 2664, 3668, 2668, 2672, 2676, 2680, 3703, 2688, 3707, 0}, 0, 0, 185, 187, {1545,9951,0,0,0,0,0,0,0,0,0,0,0,0},{10640,5455,0,0,0,0,0,0,0,0},{3532,251,0,0,0,0,0,0,0,0,0,0},{3540,269,0,0,0,0,0,0,0},{36250,0,0,0,0,0,0,0}},
	{36049, 872, 878, {3833, 3841, 3847, 3854, 3865, 3872, 3880}, {3888, 3893, 3898, 3903, 3909, 3914, 3919}, {2565, 2308, 2300, 2300, 3925, 3927, 1914}, {3929, 3935, 3943, 3949, 3955, 3960, 3966, 3972, 3979, 3990, 3998, 4008, 0}, {4018, 4024, 4032, 1012, 4038, 4043, 4049, 4055, 4062, 1050, 4073, 4083, 0}, {4093, 4098, 3898, 4103, 4108, 4113, 4118, 4123, 4128, 4134, 4139, 4144, 0}, {4093, 4098, 3898, 4103, 4108, 4113, 4118, 4123, 4128, 4134, 4139, 4144, 0}, 0, 0, 185, 187, {1120,1129,0,0,0,0,0,0,0,0,0,0,0,0},{36057,36085,0,0,0,0,0,0,0,0},{3532,251,0,0,0,0,0,0,0,0,0,0},{3540,269,0,0,0,0,0,0,0},{1180,0,0,0,0,0,0,0}},
	{1812, 36422, 36440, {36455, 36468, 36487, 36500, 36515, 36532, 10761}, {36543, 36550, 36557, 36564, 36571, 36578, 36585}, {456, 459, 36592, 465, 468, 459, 465}, {36595, 36608, 501, 510, 15731, 15738, 15747, 542, 36623, 36642, 36657, 36674, 0}, {36595, 36608, 501, 510, 15731, 15738, 15747, 542, 36623, 36642, 36657, 36674, 0}, {36691, 36698, 36705, 36712, 15731, 36719, 36726, 36733, 36740, 36747, 36754, 36761, 0}, {36691, 36698, 36705, 36712, 15731, 36719, 36726, 36733, 36740, 36747, 36754, 36761, 0}, 0, 1, 691, 187, {9298,9328,0,0,0,0,0,0,0,0,0,0,0,0},{2194,11931,2719,0,0,0,0,0,0,0},{843,251,0,0,0,0,0,0,0,0,0,0},{848,269,0,0,0,0,0,0,0},{278,0,0,0,0,0,0,0}},
	{1, 34826, 34829, {34832, 34843, 34858, 34875, 34892, 34905, 34918}, {34832, 34843, 34858, 34875, 34892, 34905, 34918}, {34929, 34932, 34935, 34938, 34941, 13447, 13438}, {35662, 35673, 13477, 35686, 35697, 35706, 35717, 35728, 35739, 35752, 35765, 35778, 0}, {35662, 35673, 13477, 35686, 35697, 35706, 35717, 35728, 35739, 35752, 35765, 35778, 0}, {35662, 35673, 13477, 35686, 35697, 35706, 35717, 35728, 35739, 35752, 35765, 35778, 0}, {35662, 35673, 13477, 35686, 35697, 35706, 35717, 35728, 35739, 35752, 35765, 35778, 0}, 0, 6, 185, 187, {198,189,1556,0,0,0,0,0,0,0,0,0,0,0},{10501,222,0,0,0,0,0,0,0,0},{242,251,843,0,0,0,0,0,0,0,0,0},{257,269,848,0,0,0,0,0,0},{278,0,0,0,0,0,0,0}},
	{36243, 1820, 1823, {3558, 3565, 3572, 3580, 3590, 3599, 3606}, {3615, 3619, 3623, 3627, 3631, 3635, 3639}, {1914, 2300, 2302, 3643, 2302, 2306, 1914}, {3645, 3653, 3662, 2588, 3668, 3672, 3677, 2608, 2615, 3682, 2633, 3690, 0}, {3645, 3653, 3662, 2588, 3668, 3672, 3677, 2608, 2615, 3682, 2633, 3690, 0}, {2651, 2655, 3699, 2664, 3668, 2668, 2672, 2676, 2680, 3703, 2688, 3707, 0}, {2651, 2655, 3699, 2664, 3668, 2668, 2672, 2676, 2680, 3703, 2688, 3707, 0}, 0, 0, 185, 187, {1120,1129,0,0,0,0,0,0,0,0,0,0,0,0},{3502,3520,0,0,0,0,0,0,0,0},{3532,251,0,0,0,0,0,0,0,0,0,0},{3540,269,0,0,0,0,0,0,0},{36250,0,0,0,0,0,0,0}},
	{36049, 872, 878, {3833, 3841, 3847, 3854, 3865, 3872, 3880}, {3888, 3893, 3898, 3903, 3909, 3914, 3919}, {2565, 2308, 2300, 2300, 3925, 3927, 1914}, {3929, 3935, 3943, 3949, 3955, 3960, 3966, 3972, 3979, 3990, 3998, 4008, 0}, {4018, 4024, 4032, 1012, 4038, 4043, 4049, 4055, 4062, 1050, 4073, 4083, 0}, {4093, 4098, 3898, 4103, 4108, 4113, 4118, 4123, 4128, 4134, 4139, 4144, 0}, {4093, 4098, 3898, 4103, 4108, 4113, 4118, 4123, 4128, 4134, 4139, 4144, 0}, 0, 0, 185, 187, {1120,1129,0,0,0,0,0,0,0,0,0,0,0,0},{36057,36085,0,0,0,0,0,0,0,0},{3532,251,0,0,0,0,0,0,0,0,0,0},{3540,269,0,0,0,0,0,0,0},{1180,0,0,0,0,0,0,0}},
	{24184, 36422, 36440, {36455, 36468, 36487, 36500, 36515, 36532, 10761}, {36543, 36550, 36557, 36564, 36571, 36578, 36585}, {456, 459, 36592, 465, 468, 459, 465}, {36595, 36608, 501, 510, 15731, 15738, 15747, 542, 36623, 36642, 36657, 36674, 0}, {36595, 36608, 501, 510, 15731, 15738, 15747, 542, 36623, 36642, 36657, 36674, 0}, {36691, 36698, 36705, 36712, 15731, 36719, 36726, 36733, 36740, 36747, 36754, 36761, 0}, {36691, 36698, 36705, 36712, 15731, 36719, 36726, 36733, 36740, 36747, 36754, 36761, 0}, 0, 1, 691, 187, {4724,8015,2165,2154,36768,2696,36777,1556,0,0,0,0,0,0},{2194,11931,2719,0,0,0,0,0,0,0},{843,251,0,0,0,0,0,0,0,0,0,0},{848,269,0,0,0,0,0,0,0},{278,0,0,0,0,0,0,0}},
	{1, 34826, 34829, {34832, 34843, 34858, 34875, 34892, 34905, 34918}, {34832, 34843, 34858, 34875, 34892, 34905, 34918}, {34929, 34932, 34935, 34938, 34941, 13447, 13438}, {35662, 35673, 13477, 35686, 35697, 35706, 35717, 35728, 35739, 35752, 35765, 35778, 0}, {35662, 35673, 13477, 35686, 35697, 35706, 35717, 35728, 35739, 35752, 35765, 35778, 0}, {35662, 35673, 13477, 35686, 35697, 35706, 35717, 35728, 35739, 35752, 35765, 35778, 0}, {35662, 35673, 13477, 35686, 35697, 35706, 35717, 35728, 35739, 35752, 35765, 35778, 0}, 0, 0, 185, 187, {198,189,1556,0,0,0,0,0,0,0,0,0,0,0},{10501,222,0,0,0,0,0,0,0,0},{242,251,843,0,0,0,0,0,0,0,0,0},{257,269,848,0,0,0,0,0,0},{278,0,0,0,0,0,0,0}},
	{36049, 872, 878, {3833, 3841, 3847, 3854, 3865, 3872, 3880}, {3888, 3893, 3898, 3903, 3909, 3914, 3919}, {2565, 2308, 2300, 2300, 3925, 3927, 1914}, {3929, 3935, 3943, 3949, 3955, 3960, 3966, 3972, 3979, 3990, 3998, 4008, 0}, {4018, 4024, 4032, 1012, 4038, 4043, 4049, 4055, 4062, 1050, 4073, 4083, 0}, {4093, 4098, 3898, 4103, 4108, 4113, 4118, 4123, 4128, 4134, 4139, 4144, 0}, {4093, 4098, 3898, 4103, 4108, 4113, 4118, 4123, 4128, 4134, 4139, 4144, 0}, 0, 0, 185, 187, {14753,0,0,0,0,0,0,0,0,0,0,0,0,0},{36057,36085,0,0,0,0,0,0,0,0},{3532,251,0,0,0,0,0,0,0,0,0,0},{3540,269,0,0,0,0,0,0,0},{1180,0,0,0,0,0,0,0}},
	{1812, 36260, 36270, {11820, 36411, 9007, 11845, 9022, 9032, 9038}, {9045, 9049, 9053, 11866, 9061, 9066, 9070}, {9074, 9076, 9078, 9080, 9082, 9076, 9080}, {2310, 2317, 15032, 2331, 2337, 2410, 2414, 11888, 36278, 36288, 36296, 36305, 0}, {2310, 2317, 15032, 2331, 2337, 2410, 2414, 11888, 36278, 36288, 36296, 36305, 0}, {2394, 2398, 2402, 2406, 2337, 2410, 2414, 11895, 2422, 2426, 2430, 2434, 0}, {2394, 2398, 2402, 2406, 2337, 2410, 2414, 11895, 2422, 2426, 2430, 2434, 0}, 0, 1, 691, 691, {9298,9328,0,0,0,0,0,0,0,0,0,0,0,0},{36329,9386,0,0,0,0,0,0,0,0},{4246,0,0,0,0,0,0,0,0,0,0,0},{4268,0,0,0,0,0,0,0,0},{36120,0,0,0,0,0,0,0}},
	{1, 34826, 34829, {34832, 34843, 34858, 34875, 34892, 34905, 34918}, {34832, 34843, 34858, 34875, 34892, 34905, 34918}, {34929, 34932, 34935, 34938, 34941, 13447, 13438}, {34944, 34968, 34977, 34986, 34997, 35006, 35019, 35028, 35033, 35044, 35066, 35090, 0}, {34944, 34968, 34977, 34986, 34997, 35006, 35019, 35028, 35033, 35044, 35066, 35090, 0}, {34944, 34968, 34977, 34986, 34997, 35006, 35019, 35028, 35033, 35044, 35066, 35090, 0}, {34944, 34968, 34977, 34986, 34997, 35006, 35019, 35028, 35033, 35044, 35066, 35090, 0}, 0, 6, 185, 187, {198,189,1556,0,0,0,0,0,0,0,0,0,0,0},{10501,222,0,0,0,0,0,0,0,0},{242,251,843,0,0,0,0,0,0,0,0,0},{257,269,848,0,0,0,0,0,0},{278,0,0,0,0,0,0,0}},
	{1, 1820, 1823, {3558, 3565, 3572, 3580, 3590, 3599, 3606}, {3615, 3619, 3623, 3627, 3631, 3635, 3639}, {1914, 2300, 2302, 3643, 2302, 2306, 1914}, {3645, 3653, 3662, 2588, 3668, 3672, 3677, 2608, 2615, 3682, 2633, 3690, 0}, {3645, 3653, 3662, 2588, 3668, 3672, 3677, 2608, 2615, 3682, 2633, 3690, 0}, {2651, 2655, 3699, 2664, 3668, 2668, 2672, 2676, 2680, 3703, 2688, 3707, 0}, {2651, 2655, 3699, 2664, 3668, 2668, 2672, 2676, 2680, 3703, 2688, 3707, 0}, 0, 0, 185, 187, {198,14785,0,0,0,0,0,0,0,0,0,0,0,0},{10640,5455,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{36250,0,0,0,0,0,0,0}},
	{36049, 872, 878, {3833, 3841, 3847, 3854, 3865, 3872, 3880}, {3888, 3893, 3898, 3903, 3909, 3914, 3919}, {2565, 2308, 2300, 2300, 3925, 3927, 1914}, {3929, 3935, 3943, 3949, 3955, 3960, 3966, 3972, 3979, 3990, 3998, 4008, 0}, {4018, 4024, 4032, 1012, 4038, 4043, 4049, 4055, 4062, 1050, 4073, 4083, 0}, {4093, 4098, 3898, 4103, 4108, 4113, 4118, 4123, 4128, 4134, 4139, 4144, 0}, {4093, 4098, 3898, 4103, 4108, 4113, 4118, 4123, 4128, 4134, 4139, 4144, 0}, 0, 0, 185, 187, {14753,1129,0,0,0,0,0,0,0,0,0,0,0,0},{36057,36085,0,0,0,0,0,0,0,0},{3532,251,0,0,0,0,0,0,0,0,0,0},{3540,269,0,0,0,0,0,0,0},{1180,0,0,0,0,0,0,0}},
	{1812, 36422, 36440, {36455, 36468, 36487, 8513, 36515, 36532, 10761}, {36543, 36550, 36557, 36788, 36571, 36578, 36585}, {456, 459, 36592, 465, 468, 459, 465}, {36595, 36608, 501, 510, 15731, 36719, 36726, 542, 36623, 36642, 36657, 36674, 0}, {36595, 36608, 501, 510, 15731, 36719, 36726, 542, 36623, 36642, 36657, 36674, 0}, {36691, 36698, 36705, 36712, 15731, 36719, 36726, 36733, 36740, 36747, 36754, 36761, 0}, {36691, 36698, 36705, 36712, 15731, 36719, 36726, 36733, 36740, 36747, 36754, 36761, 0}, 0, 1, 691, 187, {9328,0,0,0,0,0,0,0,0,0,0,0,0,0},{9372,9386,9401,0,0,0,0,0,0,0},{843,251,0,0,0,0,0,0,0,0,0,0},{848,269,0,0,0,0,0,0,0},{36120,0,0,0,0,0,0,0}},
	{1, 34826, 34829, {34832, 34843, 34858, 34875, 34892, 34905, 34918}, {34832, 34843, 34858, 34875, 34892, 34905, 34918}, {34929, 34932, 34935, 34938, 34941, 13447, 13438}, {34944, 34968, 34977, 34986, 34997, 35006, 35019, 35028, 35033, 35044, 35066, 35090, 0}, {34944, 34968, 34977, 34986, 34997, 35006, 35019, 35028, 35033, 35044, 35066, 35090, 0}, {34944, 34968, 34977, 34986, 34997, 35006, 35019, 35028, 35033, 35044, 35066, 35090, 0}, {34944, 34968, 34977, 34986, 34997, 35006, 35019, 35028, 35033, 35044, 35066, 35090, 0}, 0, 6, 185, 187, {198,189,1556,0,0,0,0,0,0,0,0,0,0,0},{10501,222,0,0,0,0,0,0,0,0},{242,251,843,0,0,0,0,0,0,0,0,0},{257,269,848,0,0,0,0,0,0},{278,0,0,0,0,0,0,0}},
	{289, 1820, 1823, {3558, 3565, 3572, 3580, 3590, 3599, 3606}, {3615, 3619, 3623, 3627, 3631, 3635, 3639}, {1914, 2300, 2302, 3643, 2302, 2306, 1914}, {3645, 3653, 3662, 2588, 3668, 3672, 3677, 2608, 2615, 3682, 2633, 3690, 0}, {3645, 3653, 3662, 2588, 3668, 3672, 3677, 2608, 2615, 3682, 2633, 3690, 0}, {2651, 2655, 3699, 2664, 3668, 2668, 2672, 2676, 2680, 3703, 2688, 3707, 0}, {2651, 2655, 3699, 2664, 3668, 2668, 2672, 2676, 2680, 3703, 2688, 3707, 0}, 0, 0, 185, 187, {198,1129,0,0,0,0,0,0,0,0,0,0,0,0},{3502,3520,0,0,0,0,0,0,0,0},{3532,251,0,0,0,0,0,0,0,0,0,0},{3540,269,0,0,0,0,0,0,0},{36250,0,0,0,0,0,0,0}},
	{36049, 872, 878, {3833, 3841, 3847, 3854, 3865, 3872, 3880}, {3888, 3893, 3898, 3903, 3909, 3914, 3919}, {2565, 2308, 2300, 2300, 3925, 3927, 1914}, {3929, 3935, 3943, 3949, 3955, 3960, 3966, 3972, 3979, 3990, 3998, 4008, 0}, {4018, 4024, 4032, 1012, 4038, 4043, 4049, 4055, 4062, 1050, 4073, 4083, 0}, {4093, 4098, 3898, 4103, 4108, 4113, 4118, 4123, 4128, 4134, 4139, 4144, 0}, {4093, 4098, 3898, 4103, 4108, 4113, 4118, 4123, 4128, 4134, 4139, 4144, 0}, 0, 0, 185, 187, {1120,1129,0,0,0,0,0,0,0,0,0,0,0,0},{36057,36085,0,0,0,0,0,0,0,0},{3532,251,0,0,0,0,0,0,0,0,0,0},{3540,269,0,0,0,0,0,0,0},{36795,0,0,0,0,0,0,0}},
	{1812, 36260, 36270, {11820, 36411, 9007, 11845, 9022, 9032, 9038}, {9045, 9049, 9053, 11866, 9061, 9066, 9070}, {9074, 9076, 9078, 9080, 9082, 9076, 9080}, {2310, 2317, 15032, 2331, 2337, 2410, 2414, 11888, 36278, 36288, 36296, 36305, 0}, {2310, 2317, 15032, 2331, 2337, 2410, 2414, 11888, 36278, 36288, 36296, 36305, 0}, {2394, 2398, 2402, 2406, 2337, 2410, 2414, 11895, 2422, 2426, 2430, 2434, 0}, {2394, 2398, 2402, 2406, 2337, 2410, 2414, 11895, 2422, 2426, 2430, 2434, 0}, 0, 1, 691, 691, {9298,9328,0,0,0,0,0,0,0,0,0,0,0,0},{36329,9386,0,0,0,0,0,0,0,0},{4246,0,0,0,0,0,0,0,0,0,0,0},{4268,0,0,0,0,0,0,0,0},{36120,0,0,0,0,0,0,0}},
	{1, 34826, 34829, {34832, 34843, 34858, 34875, 34892, 34905, 34918}, {34832, 34843, 34858, 34875, 34892, 34905, 34918}, {34929, 34932, 34935, 34938, 34941, 13447, 13438}, {34944, 34968, 34977, 34986, 34997, 35006, 35019, 35028, 35033, 35044, 35066, 35090, 0}, {34944, 34968, 34977, 34986, 34997, 35006, 35019, 35028, 35033, 35044, 35066, 35090, 0}, {34944, 34968, 34977, 34986, 34997, 35006, 35019, 35028, 35033, 35044, 35066, 35090, 0}, {34944, 34968, 34977, 34986, 34997, 35006, 35019, 35028, 35033, 35044, 35066, 35090, 0}, 0, 1, 185, 187, {198,189,1556,0,0,0,0,0,0,0,0,0,0,0},{10501,222,0,0,0,0,0,0,0,0},{242,251,843,0,0,0,0,0,0,0,0,0},{257,269,848,0,0,0,0,0,0},{278,0,0,0,0,0,0,0}},
	{1, 1820, 1823, {3558, 3565, 3572, 3580, 3590, 3599, 3606}, {3615, 3619, 3623, 3627, 3631, 3635, 3639}, {1914, 2300, 2302, 3643, 2302, 2306, 1914}, {3645, 3653, 3662, 2588, 3668, 3672, 3677, 2608, 2615, 3682, 2633, 3690, 0}, {3645, 3653, 3662, 2588, 3668, 3672, 3677, 2608, 2615, 3682, 2633, 3690, 0}, {2651, 2655, 3699, 2664, 3668, 2668, 2672, 2676, 2680, 3703, 2688, 3707, 0}, {2651, 2655, 3699, 2664, 3668, 2668, 2672, 2676, 2680, 3703, 2688, 3707, 0}, 0, 0, 185, 187, {1120,36805,0,0,0,0,0,0,0,0,0,0,0,0},{10640,5455,0,0,0,0,0,0,0,0},{3532,251,0,0,0,0,0,0,0,0,0,0},{3540,269,0,0,0,0,0,0,0},{36250,0,0,0,0,0,0,0}},
	{36049, 872, 878, {3833, 3841, 3847, 3854, 3865, 3872, 3880}, {3888, 3893, 3898, 3903, 3909, 3914, 3919}, {2565, 2308, 2300, 2300, 3925, 3927, 1914}, {3929, 3935, 3943, 3949, 3955, 3960, 3966, 3972, 3979, 3990, 3998, 4008, 0}, {4018, 4024, 4032, 1012, 4038, 4043, 4049, 4055, 4062, 1050, 4073, 4083, 0}, {4093, 4098, 3898, 4103, 4108, 4113, 4118, 4123, 4128, 4134, 4139, 4144, 0}, {4093, 4098, 3898, 4103, 4108, 4113, 4118, 4123, 4128, 4134, 4139, 4144, 0}, 0, 1, 185, 187, {1120,1129,0,0,0,0,0,0,0,0,0,0,0,0},{36057,36085,0,0,0,0,0,0,0,0},{843,251,0,0,0,0,0,0,0,0,0,0},{848,269,0,0,0,0,0,0,0},{1180,0,0,0,0,0,0,0}},
	{1812, 36422, 36440, {36455, 36468, 36487, 8513, 36515, 36532, 10761}, {36543, 36550, 36557, 36788, 36571, 36578, 36585}, {456, 459, 36592, 465, 468, 459, 465}, {36595, 36608, 501, 510, 15731, 36719, 36726, 542, 36623, 36642, 36657, 36674, 0}, {36595, 36608, 501, 510, 15731, 36719, 36726, 542, 36623, 36642, 36657, 36674, 0}, {36691, 36698, 36705, 36712, 15731, 36719, 36726, 36733, 36740, 36747, 36754, 36761, 0}, {36691, 36698, 36705, 36712, 15731, 36719, 36726, 36733, 36740, 36747, 36754, 36761, 0}, 0, 1, 691, 187, {9298,9308,9316,9328,9340,9350,9360,36817,0,0,0,0,0,0},{9372,9386,9401,0,0,0,0,0,0,0},{843,251,0,0,0,0,0,0,0,0,0,0},{848,269,0,0,0,0,0,0,0},{36120,0,0,0,0,0,0,0}},
	{1, 34826, 34829, {34832, 34843, 34858, 34875, 34892, 34905, 34918}, {34832, 34843, 34858, 34875, 34892, 34905, 34918}, {34929, 34932, 34935, 34938, 34941, 13447, 13438}, {35662, 35673, 13477, 35686, 35697, 35706, 35717, 35728, 35739, 35752, 35765, 35778, 0}, {35662, 35673, 13477, 35686, 35697, 35706, 35717, 35728, 35739, 35752, 35765, 35778, 0}, {35662, 35673, 13477, 35686, 35697, 35706, 35717, 35728, 35739, 35752, 35765, 35778, 0}, {35662, 35673, 13477, 35686, 35697, 35706, 35717, 35728, 35739, 35752, 35765, 35778, 0}, 0, 6, 185, 187, {198,189,1556,0,0,0,0,0,0,0,0,0,0,0},{10501,222,0,0,0,0,0,0,0,0},{242,251,843,0,0,0,0,0,0,0,0,0},{257,269,848,0,0,0,0,0,0},{278,0,0,0,0,0,0,0}},
	{289, 1820, 1823, {3558, 3565, 3572, 3580, 3590, 3599, 3606}, {3615, 3619, 3623, 3627, 3631, 3635, 3639}, {1914, 2300, 2302, 3643, 2302, 2306, 1914}, {3645, 3653, 3662, 2588, 3668, 3672, 3677, 2608, 2615, 3682, 2633, 3690, 0}, {3645, 3653, 3662, 2588, 3668, 3672, 3677, 2608, 2615, 3682, 2633, 3690, 0}, {2651, 2655, 3699, 2664, 3668, 2668, 2672, 2676, 2680, 3703, 2688, 3707, 0}, {2651, 2655, 3699, 2664, 3668, 2668, 2672, 2676, 2680, 3703, 2688, 3707, 0}, 0, 0, 185, 187, {198,1129,0,0,0,0,0,0,0,0,0,0,0,0},{3502,3520,0,0,0,0,0,0,0,0},{3532,251,0,0,0,0,0,0,0,0,0,0},{3540,269,0,0,0,0,0,0,0},{36250,0,0,0,0,0,0,0}},
	{36049, 872, 878, {3833, 3841, 3847, 3854, 3865, 3872, 3880}, {3888, 3893, 3898, 3903, 3909, 3914, 3919}, {2565, 2308, 2300, 2300, 3925, 3927, 1914}, {3929, 3935, 3943, 3949, 3955, 3960, 3966, 3972, 3979, 3990, 3998, 4008, 0}, {4018, 4024, 4032, 1012, 4038, 4043, 4049, 4055, 4062, 1050, 4073, 4083, 0}, {4093, 4098, 3898, 4103, 4108, 4113, 4118, 4123, 4128, 4134, 4139, 4144, 0}, {4093, 4098, 3898, 4103, 4108, 4113, 4118, 4123, 4128, 4134, 4139, 4144, 0}, 0, 1, 2438, 187, {2440,0,0,0,0,0,0,0,0,0,0,0,0,0},{36057,36085,0,0,0,0,0,0,0,0},{843,251,0,0,0,0,0,0,0,0,0,0},{848,269,0,0,0,0,0,0,0},{36795,0,0,0,0,0,0,0}},
	{1, 34826, 34829, {34832, 34843, 34858, 34875, 34892, 34905, 34918}, {34832, 34843, 34858, 34875, 34892, 34905, 34918}, {34929, 34932, 34935, 34938, 34941, 13447, 13438}, {35662, 35673, 13477, 35686, 35697, 35706, 35717, 35728, 35739, 35752, 35765, 35778, 0}, {35662, 35673, 13477, 35686, 35697, 35706, 35717, 35728, 35739, 35752, 35765, 35778, 0}, {35662, 35673, 13477, 35686, 35697, 35706, 35717, 35728, 35739, 35752, 35765, 35778, 0}, {35662, 35673, 13477, 35686, 35697, 35706, 35717, 35728, 35739, 35752, 35765, 35778, 0}, 0, 6, 185, 187, {198,189,1556,0,0,0,0,0,0,0,0,0,0,0},{10501,222,0,0,0,0,0,0,0,0},{242,251,843,0,0,0,0,0,0,0,0,0},{257,269,848,0,0,0,0,0,0},{278,0,0,0,0,0,0,0}},
	{36049, 872, 878, {3833, 3841, 3847, 3854, 3865, 3872, 3880}, {3888, 3893, 3898, 3903, 3909, 3914, 3919}, {2565, 2308, 2300, 2300, 3925, 3927, 1914}, {3929, 3935, 3943, 3949, 3955, 3960, 3966, 3972, 3979, 3990, 3998, 4008, 0}, {4018, 4024, 4032, 1012, 4038, 4043, 4049, 4055, 4062, 1050, 4073, 4083, 0}, {4093, 4098, 3898, 4103, 4108, 4113, 4118, 4123, 4128, 4134, 4139, 4144, 0}, {4093, 4098, 3898, 4103, 4108, 4113, 4118, 4123, 4128, 4134, 4139, 4144, 0}, 0, 1, 185, 187, {1120,1129,0,0,0,0,0,0,0,0,0,0,0,0},{36057,36085,0,0,0,0,0,0,0,0},{843,251,242,3532,0,0,0,0,0,0,0,0},{848,269,257,3540,0,0,0,0,0},{1180,0,0,0,0,0,0,0}},
	{1, 34826, 34829, {34832, 34843, 34858, 34875, 34892, 34905, 34918}, {34832, 34843, 34858, 34875, 34892, 34905, 34918}, {34929, 34932, 34935, 34938, 34941, 13447, 13438}, {35662, 35673, 13477, 35686, 35697, 35706, 35717, 35728, 35739, 35752, 35765, 35778, 0}, {35662, 35673, 13477, 35686, 35697, 35706, 35717, 35728, 35739, 35752, 35765, 35778, 0}, {35662, 35673, 13477, 35686, 35697, 35706, 35717, 35728, 35739, 35752, 35765, 35778, 0}, {35662, 35673, 13477, 35686, 35697, 35706, 35717, 35728, 35739, 35752, 35765, 35778, 0}, 0, 6, 185, 187, {198,189,1556,0,0,0,0,0,0,0,0,0,0,0},{10501,222,0,0,0,0,0,0,0,0},{242,251,843,0,0,0,0,0,0,0,0,0},{257,269,848,0,0,0,0,0,0},{278,0,0,0,0,0,0,0}},
	{36049, 872, 878, {3833, 3841, 3847, 3854, 3865, 3872, 3880}, {3888, 3893, 3898, 3903, 3909, 3914, 3919}, {2565, 2308, 2300, 2300, 3925, 3927, 1914}, {3929, 3935, 3943, 3949, 3955, 3960, 3966, 3972, 3979, 3990, 3998, 4008, 0}, {4018, 4024, 4032, 1012, 4038, 4043, 4049, 4055, 4062, 1050, 4073, 4083, 0}, {4093, 4098, 3898, 4103, 4108, 4113, 4118, 4123, 4128, 4134, 4139, 4144, 0}, {4093, 4098, 3898, 4103, 4108, 4113, 4118, 4123, 4128, 4134, 4139, 4144, 0}, 0, 0, 185, 187, {1120,1129,0,0,0,0,0,0,0,0,0,0,0,0},{36057,36085,0,0,0,0,0,0,0,0},{3532,251,0,0,0,0,0,0,0,0,0,0},{3540,269,0,0,0,0,0,0,0},{1180,0,0,0,0,0,0,0}},
	{1, 34826, 34829, {34832, 34843, 34858, 34875, 34892, 34905, 34918}, {34832, 34843, 34858, 34875, 34892, 34905, 34918}, {34929, 34932, 34935, 34938, 34941, 13447, 13438}, {35662, 35673, 13477, 35686, 35697, 35706, 35717, 35728, 35739, 35752, 35765, 35778, 0}, {35662, 35673, 13477, 35686, 35697, 35706, 35717, 35728, 35739, 35752, 35765, 35778, 0}, {35662, 35673, 13477, 35686, 35697, 35706, 35717, 35728, 35739, 35752, 35765, 35778, 0}, {35662, 35673, 13477, 35686, 35697, 35706, 35717, 35728, 35739, 35752, 35765, 35778, 0}, 0, 6, 185, 187, {198,189,1556,0,0,0,0,0,0,0,0,0,0,0},{10501,222,0,0,0,0,0,0,0,0},{242,251,843,0,0,0,0,0,0,0,0,0},{257,269,848,0,0,0,0,0,0},{278,0,0,0,0,0,0,0}},
	{289, 8022, 21796, {3558, 3565, 3572, 3580, 3590, 3599, 3606}, {3615, 3619, 3623, 3627, 3631, 3635, 3639}, {1914, 2300, 2302, 3643, 2302, 2306, 1914}, {3645, 3653, 3662, 2588, 3668, 3672, 3677, 2608, 2615, 3682, 2633, 3690, 0}, {3645, 3653, 3662, 2588, 3668, 3672, 3677, 2608, 2615, 3682, 2633, 3690, 0}, {2651, 2655, 3699, 2664, 3668, 2668, 2672, 2676, 2680, 3703, 2688, 3707, 0}, {2651, 2655, 3699, 2664, 3668, 2668, 2672, 2676, 2680, 3703, 2688, 3707, 0}, 0, 0, 2438, 187, {2440,2451,4157,8015,1556,0,0,0,0,0,0,0,0,0},{5455,3520,0,0,0,0,0,0,0,0},{251,843,242,0,0,0,0,0,0,0,0,0},{269,848,36829,257,0,0,0,0,0},{278,0,0,0,0,0,0,0}},
	{36049, 872, 878, {3833, 3841, 3847, 3854, 3865, 3872, 3880}, {3888, 3893, 3898, 3903, 3909, 3914, 3919}, {2565, 2308, 2300, 2300, 3925, 3927, 1914}, {3929, 3935, 3943, 3949, 3955, 3960, 3966, 3972, 3979, 3990, 3998, 4008, 0}, {4018, 4024, 4032, 1012, 4038, 4043, 4049, 4055, 4062, 1050, 4073, 4083, 0}, {4093, 4098, 3898, 4103, 4108, 4113, 4118, 4123, 4128, 4134, 4139, 4144, 0}, {4093, 4098, 3898, 4103, 4108, 4113, 4118, 4123, 4128, 4134, 4139, 4144, 0}, 0, 0, 185, 187, {1120,1129,0,0,0,0,0,0,0,0,0,0,0,0},{36057,36085,0,0,0,0,0,0,0,0},{3532,251,0,0,0,0,0,0,0,0,0,0},{3540,269,0,0,0,0,0,0,0},{1180,0,0,0,0,0,0,0}},
	{36049, 872, 878, {3833, 3841, 3847, 3854, 3865, 3872, 3880}, {3888, 3893, 3898, 3903, 3909, 3914, 3919}, {2565, 2308, 2300, 2300, 3925, 3927, 1914}, {3929, 3935, 3943, 3949, 3955, 3960, 3966, 3972, 3979, 3990, 3998, 4008, 0}, {4018, 4024, 4032, 1012, 4038, 4043, 4049, 4055, 4062, 1050, 4073, 4083, 0}, {4093, 4098, 3898, 4103, 4108, 4113, 4118, 4123, 4128, 4134, 4139, 4144, 0}, {4093, 4098, 3898, 4103, 4108, 4113, 4118, 4123, 4128, 4134, 4139, 4144, 0}, 0, 0, 185, 187, {1120,1129,0,0,0,0,0,0,0,0,0,0,0,0},{36057,36085,0,0,0,0,0,0,0,0},{3532,251,0,0,0,0,0,0,0,0,0,0},{3540,269,0,0,0,0,0,0,0},{1180,0,0,0,0,0,0,0}},
	{289, 8022, 21796, {3558, 3565, 3572, 3580, 3590, 3599, 3606}, {3615, 3619, 3623, 3627, 3631, 3635, 3639}, {1914, 2300, 2302, 3643, 2302, 2306, 1914}, {3645, 3653, 3662, 2588, 3668, 3672, 3677, 2608, 2615, 3682, 2633, 3690, 0}, {3645, 3653, 3662, 2588, 3668, 3672, 3677, 2608, 2615, 3682, 2633, 3690, 0}, {2651, 2655, 3699, 2664, 3668, 2668, 2672, 2676, 2680, 3703, 2688, 3707, 0}, {2651, 2655, 3699, 2664, 3668, 2668, 2672, 2676, 2680, 3703, 2688, 3707, 0}, 0, 0, 185, 187, {1120,1129,0,0,0,0,0,0,0,0,0,0,0,0},{3502,3520,0,0,0,0,0,0,0,0},{3532,251,0,0,0,0,0,0,0,0,0,0},{3540,269,0,0,0,0,0,0,0},{36250,0,0,0,0,0,0,0}},
	{36049, 872, 878, {3833, 3841, 3847, 3854, 3865, 3872, 3880}, {3888, 3893, 3898, 3903, 3909, 3914, 3919}, {2565, 2308, 2300, 2300, 3925, 3927, 1914}, {3929, 3935, 3943, 3949, 3955, 3960, 3966, 3972, 3979, 3990, 3998, 4008, 0}, {4018, 4024, 4032, 1012, 4038, 4043, 4049, 4055, 4062, 1050, 4073, 4083, 0}, {4093, 4098, 3898, 4103, 4108, 4113, 4118, 4123, 4128, 4134, 4139, 4144, 0}, {4093, 4098, 3898, 4103, 4108, 4113, 4118, 4123, 4128, 4134, 4139, 4144, 0}, 0, 0, 185, 187, {1120,1129,0,0,0,0,0,0,0,0,0,0,0,0},{36840,36868,0,0,0,0,0,0,0,0},{3532,251,0,0,0,0,0,0,0,0,0,0},{3540,269,0,0,0,0,0,0,0},{1180,0,0,0,0,0,0,0}},
	{36049, 872, 878, {3833, 3841, 3847, 3854, 3865, 3872, 3880}, {3888, 3893, 3898, 3903, 3909, 3914, 3919}, {2565, 2308, 2300, 2300, 3925, 3927, 1914}, {3929, 3935, 3943, 3949, 3955, 3960, 3966, 3972, 3979, 3990, 3998, 4008, 0}, {4018, 4024, 4032, 1012, 4038, 4043, 4049, 4055, 4062, 1050, 4073, 4083, 0}, {4093, 4098, 3898, 4103, 4108, 4113, 4118, 4123, 4128, 4134, 4139, 4144, 0}, {4093, 4098, 3898, 4103, 4108, 4113, 4118, 4123, 4128, 4134, 4139, 4144, 0}, 0, 0, 185, 187, {1120,1129,0,0,0,0,0,0,0,0,0,0,0,0},{36057,36085,0,0,0,0,0,0,0,0},{3532,251,0,0,0,0,0,0,0,0,0,0},{3540,269,0,0,0,0,0,0,0},{1180,0,0,0,0,0,0,0}},
	{36403, 872, 878, {3833, 3841, 3847, 3854, 3865, 3872, 3880}, {3888, 3893, 3898, 3903, 3909, 3914, 3919}, {2565, 2308, 2300, 2300, 3925, 3927, 1914}, {3929, 3935, 3943, 3949, 3955, 3960, 3966, 3972, 3979, 3990, 3998, 4008, 0}, {4018, 4024, 4032, 1012, 4038, 4043, 4049, 4055, 4062, 1050, 4073, 4083, 0}, {4093, 4098, 3898, 4103, 4108, 4113, 4118, 4123, 4128, 4134, 4139, 4144, 0}, {4093, 4098, 3898, 4103, 4108, 4113, 4118, 4123, 4128, 4134, 4139, 4144, 0}, 0, 0, 185, 187, {3736,0,0,0,0,0,0,0,0,0,0,0,0,0},{36057,36085,0,0,0,0,0,0,0,0},{3532,251,0,0,0,0,0,0,0,0,0,0},{3540,269,0,0,0,0,0,0,0},{36250,0,0,0,0,0,0,0}},
	{3551, 1820, 1823, {3833, 3841, 3847, 3854, 3865, 3872, 3880}, {3888, 3893, 3898, 3903, 3909, 3914, 3919}, {2565, 2308, 2300, 2300, 3925, 3927, 1914}, {3929, 3935, 3943, 3949, 3955, 3960, 3966, 3972, 3979, 3990, 3998, 4008, 0}, {4018, 4024, 4032, 1012, 4038, 4043, 4049, 4055, 4062, 1050, 4073, 4083, 0}, {4093, 4098, 3898, 4103, 4108, 4113, 4118, 4123, 4128, 4134, 4139, 4144, 0}, {4093, 4098, 3898, 4103, 4108, 4113, 4118, 4123, 4128, 4134, 4139, 4144, 0}, 0, 0, 185, 187, {3711,3720,3727,3736,1599,1556,3747,0,0,0,0,0,0,0},{31111,31131,222,10501,0,0,0,0,0,0},{3532,242,843,251,0,0,0,0,0,0,0,0},{3540,257,848,269,0,0,0,0,0},{4288,0,0,0,0,0,0,0}},
	{24184, 36422, 36440, {36455, 36468, 36487, 36500, 36515, 36532, 10761}, {36543, 36550, 36557, 36564, 36571, 36578, 36585}, {456, 459, 36592, 465, 468, 459, 465}, {36595, 36608, 501, 510, 15731, 15738, 15747, 542, 36623, 36642, 36657, 36674, 0}, {36595, 36608, 501, 510, 15731, 15738, 15747, 542, 36623, 36642, 36657, 36674, 0}, {36691, 36698, 36705, 36712, 15731, 36719, 36726, 36733, 36740, 36747, 36754, 36761, 0}, {36691, 36698, 36705, 36712, 15731, 36719, 36726, 36733, 36740, 36747, 36754, 36761, 0}, 0, 1, 691, 187, {4724,8015,2165,2154,36768,2696,36777,1556,0,0,0,0,0,0},{2194,11931,2719,0,0,0,0,0,0,0},{843,251,0,0,0,0,0,0,0,0,0,0},{848,269,0,0,0,0,0,0,0},{278,0,0,0,0,0,0,0}},
	{1812, 36260, 36270, {8986, 8995, 9007, 9014, 9022, 9032, 9038}, {9045, 9049, 9053, 9057, 9061, 9066, 9070}, {0, 0, 0, 0, 0, 0, 0}, {2310, 2317, 15032, 2331, 2337, 2341, 2346, 11888, 36278, 36288, 36296, 36305, 0}, {2310, 2317, 15032, 2331, 2337, 2341, 2346, 11888, 36278, 36288, 36296, 36305, 0}, {2394, 2398, 2402, 2406, 2337, 2410, 2414, 11895, 2422, 2426, 2430, 2434, 0}, {2394, 2398, 2402, 2406, 2337, 2410, 2414, 11895, 2422, 2426, 2430, 2434, 0}, 0, 1, 691, 187, {9328,36314,0,0,0,0,0,0,0,0,0,0,0,0},{36329,9386,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{36120,0,0,0,0,0,0,0}},
	{1812, 36422, 36440, {36455, 36468, 36487, 8513, 36515, 36532, 10761}, {36543, 36550, 36557, 36788, 36571, 36578, 36585}, {456, 459, 36592, 465, 468, 459, 465}, {36595, 36608, 501, 510, 15731, 36719, 36726, 542, 36623, 36642, 36657, 36674, 0}, {36595, 36608, 501, 510, 15731, 36719, 36726, 542, 36623, 36642, 36657, 36674, 0}, {36691, 36698, 36705, 36712, 15731, 36719, 36726, 36733, 36740, 36747, 36754, 36761, 0}, {36691, 36698, 36705, 36712, 15731, 36719, 36726, 36733, 36740, 36747, 36754, 36761, 0}, 0, 1, 691, 187, {9328,0,0,0,0,0,0,0,0,0,0,0,0,0},{9372,9386,9401,0,0,0,0,0,0,0},{843,251,0,0,0,0,0,0,0,0,0,0},{848,269,0,0,0,0,0,0,0},{36120,0,0,0,0,0,0,0}},
	{1812, 36260, 36270, {11820, 36411, 9007, 11845, 9022, 9032, 9038}, {9045, 9049, 9053, 11866, 9061, 9066, 9070}, {9074, 9076, 9078, 9080, 9082, 9076, 9080}, {2310, 2317, 15032, 2331, 2337, 2410, 2414, 11888, 36278, 36288, 36296, 36305, 0}, {2310, 2317, 15032, 2331, 2337, 2410, 2414, 11888, 36278, 36288, 36296, 36305, 0}, {2394, 2398, 2402, 2406, 2337, 2410, 2414, 11895, 2422, 2426, 2430, 2434, 0}, {2394, 2398, 2402, 2406, 2337, 2410, 2414, 11895, 2422, 2426, 2430, 2434, 0}, 0, 1, 691, 691, {9298,9328,0,0,0,0,0,0,0,0,0,0,0,0},{36329,9386,0,0,0,0,0,0,0,0},{4246,0,0,0,0,0,0,0,0,0,0,0},{4268,0,0,0,0,0,0,0,0},{36120,0,0,0,0,0,0,0}},
	{289, 1820, 1823, {35281, 35292, 35316, 35346, 35363, 35385, 35394}, {14921, 14924, 14929, 14935, 14939, 7600, 14944}, {6540, 6528, 6530, 6532, 6534, 6536, 6538}, {35405, 35418, 501, 35431, 521, 35442, 35451, 542, 35460, 35477, 35492, 35505, 0}, {35405, 35418, 501, 35431, 521, 35442, 35451, 542, 35460, 35477, 35492, 35505, 0}, {15095, 7999, 2402, 2406, 15043, 15099, 15103, 15107, 15111, 2426, 15115, 15119, 0}, {15095, 7999, 2402, 2406, 15043, 15099, 15103, 15107, 15111, 2426, 15115, 15119, 0}, 0, 1, 691, 187, {2154,2696,8015,189,1556,0,0,0,0,0,0,0,0,0},{3520,5455,0,0,0,0,0,0,0,0},{843,251,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{1195, 1204, 1211, {1218, 1228, 1238, 1248, 1258, 1268, 1278}, {1288, 1295, 1302, 1309, 1316, 1323, 1330}, {1337, 1341, 1345, 1349, 1353, 1357, 1361}, {1365, 1372, 1379, 1386, 1393, 1400, 1407, 1414, 1421, 1428, 1435, 1445, 0}, {1365, 1372, 1379, 1386, 1393, 1400, 1407, 1414, 1421, 1428, 1435, 1445, 0}, {1455, 1460, 1465, 1470, 1475, 1480, 1485, 1490, 1495, 1500, 1506, 1512, 0}, {1455, 1460, 1465, 1470, 1475, 1480, 1485, 1490, 1495, 1500, 1506, 1512, 0}, 0, 0, 185, 187, {1518,1527,1536,1545,1556,1567,1578,1585,1592,1599,0,0,0,0},{1608,1630,1658,1686,1701,0,0,0,0,0},{843,251,1722,1730,0,0,0,0,0,0,0,0},{848,269,1739,1750,0,0,0,0,0},{1762,1778,1791,1805,0,0,0,0}},
	{1812, 35193, 35198, {2217, 9837, 35203, 2240, 2247, 2255, 35210}, {2270, 9865, 35218, 2283, 2287, 2291, 35222}, {1914, 2300, 2302, 2304, 2302, 2306, 2308}, {2310, 2317, 4837, 2331, 4848, 2341, 2346, 2351, 2358, 2368, 2376, 6086, 0}, {2310, 2317, 4837, 2331, 4848, 2341, 2346, 2351, 2358, 2368, 2376, 6086, 0}, {2394, 2398, 2402, 2406, 4848, 2410, 2414, 2418, 2422, 2426, 2430, 7503, 0}, {2394, 2398, 2402, 2406, 4848, 2410, 2414, 2418, 2422, 2426, 2430, 7503, 0}, 2, 1, 691, 187, {2154,6118,0,0,0,0,0,0,0,0,0,0,0,0},{2176,2194,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{1812, 36260, 36270, {8986, 8995, 9007, 9014, 9022, 9032, 9038}, {9045, 9049, 9053, 9057, 9061, 9066, 9070}, {0, 0, 0, 0, 0, 0, 0}, {2310, 2317, 15032, 2331, 2337, 2341, 2346, 11888, 36278, 36288, 36296, 36305, 0}, {2310, 2317, 15032, 2331, 2337, 2341, 2346, 11888, 36278, 36288, 36296, 36305, 0}, {2394, 2398, 2402, 2406, 2337, 2410, 2414, 11895, 2422, 2426, 2430, 2434, 0}, {2394, 2398, 2402, 2406, 2337, 2410, 2414, 11895, 2422, 2426, 2430, 2434, 0}, 0, 1, 691, 187, {9328,36314,0,0,0,0,0,0,0,0,0,0,0,0},{36329,9386,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{36120,0,0,0,0,0,0,0}},
	{289, 1820, 1823, {14832, 14838, 14852, 14875, 14889, 14905, 14912}, {14921, 14924, 14929, 14935, 14939, 7600, 14944}, {6540, 6528, 6530, 6532, 6534, 6536, 6538}, {14948, 14955, 10132, 14962, 3668, 14968, 14974, 14980, 14987, 14996, 15004, 15011, 0}, {15018, 15025, 15032, 15037, 15043, 15047, 15052, 15057, 15064, 15073, 15081, 15088, 0}, {15095, 7999, 2402, 2406, 15043, 15099, 15103, 15107, 15111, 2426, 15115, 15119, 0}, {15095, 7999, 2402, 2406, 15043, 15099, 15103, 15107, 15111, 2426, 15115, 15119, 0}, 0, 1, 691, 187, {2154,1129,0,0,0,0,0,0,0,0,0,0,0,0},{15123,3520,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{3551, 1820, 1823, {35518, 35533, 35548, 35563, 35580, 35597, 35606}, {35617, 35624, 35631, 35638, 35645, 19574, 35652}, {35659, 19207, 8610, 8613, 8607, 19204, 19588}, {13102, 13113, 8644, 13126, 8666, 13137, 13144, 8691, 13151, 13166, 13179, 13190, 0}, {13102, 13113, 8644, 13126, 8666, 13137, 13144, 8691, 13151, 13166, 13179, 13190, 0}, {13203, 13210, 13217, 13224, 8666, 13137, 13144, 13231, 13238, 13245, 13252, 13259, 0}, {13203, 13210, 13217, 13224, 8666, 13137, 13144, 13231, 13238, 13245, 13252, 13259, 0}, 0, 1, 185, 187, {1545,15492,0,0,0,0,0,0,0,0,0,0,0,0},{20050,16096,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{12916,0,0,0,0,0,0,0}},
	{3551, 25903, 25908, {25913, 25920, 25931, 25944, 25957, 25968, 25981}, {25992, 25997, 26002, 26007, 26012, 26017, 26022}, {6528, 6530, 6532, 6534, 6536, 6538, 6540}, {26027, 26053, 26081, 26111, 26141, 26167, 26197, 26223, 26251, 26275, 26303, 26340, 0}, {26027, 26053, 26081, 26111, 26141, 26167, 26197, 26223, 26251, 26275, 26303, 26340, 0}, {26379, 26391, 26403, 26415, 26427, 26439, 26451, 26463, 26475, 26487, 26500, 26513, 0}, {26379, 26391, 26403, 26415, 26427, 26439, 26451, 26463, 26475, 26487, 26500, 26513, 0}, 0, 0, 2438, 187, {1556,15492,0,0,0,0,0,0,0,0,0,0,0,0},{26526,26564,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{12916,0,0,0,0,0,0,0}},
	{1195, 1204, 1211, {1218, 1228, 1238, 1248, 1258, 1268, 1278}, {34757, 34764, 34771, 34778, 34785, 34792, 34799}, {1337, 1341, 1345, 1349, 1353, 1357, 1361}, {1455, 1460, 1465, 1470, 1475, 1480, 1485, 1490, 1495, 1500, 1506, 1512, 0}, {1455, 1460, 1465, 1470, 1475, 1480, 1485, 1490, 1495, 1500, 1506, 1512, 0}, {1455, 1460, 1465, 1470, 1475, 1480, 1485, 1490, 1495, 1500, 1506, 1512, 0}, {1455, 1460, 1465, 1470, 1475, 1480, 1485, 1490, 1495, 1500, 1506, 1512, 0}, 0, 0, 185, 187, {1120,3483,189,1578,1599,1518,1545,1556,0,0,0,0,0,0},{1608,6555,1686,1701,0,0,0,0,0,0},{843,251,0,0,0,0,0,0,0,0,0,0},{848,269,0,0,0,0,0,0,0},{1762,1778,1791,1805,0,0,0,0}},
	{1195, 1204, 1211, {1218, 1228, 1238, 1248, 1258, 1268, 1278}, {34757, 34764, 34771, 34778, 34785, 34792, 34799}, {1337, 1341, 1345, 1349, 1353, 1357, 1361}, {1455, 1460, 1465, 1470, 1475, 1480, 1485, 1490, 1495, 1500, 1506, 1512, 0}, {1455, 1460, 1465, 1470, 1475, 1480, 1485, 1490, 1495, 1500, 1506, 1512, 0}, {1455, 1460, 1465, 1470, 1475, 1480, 1485, 1490, 1495, 1500, 1506, 1512, 0}, {1455, 1460, 1465, 1470, 1475, 1480, 1485, 1490, 1495, 1500, 1506, 1512, 0}, 0, 0, 185, 187, {1120,3483,189,1578,1599,1518,1545,1556,0,0,0,0,0,0},{1608,6555,1686,1701,0,0,0,0,0,0},{843,251,0,0,0,0,0,0,0,0,0,0},{848,269,0,0,0,0,0,0,0},{1762,1778,1791,1805,0,0,0,0}},
	{7465, 7261, 7266, {2217, 2225, 2232, 2240, 2247, 2255, 2262}, {7473, 7478, 7482, 7486, 7490, 7494, 7498}, {1914, 2300, 2302, 2304, 2302, 2306, 2308}, {2310, 2317, 4837, 2331, 4848, 2341, 2346, 2351, 2358, 2368, 2376, 6086, 0}, {2310, 2317, 4837, 2331, 4848, 2341, 2346, 2351, 2358, 2368, 2376, 6086, 0}, {2394, 2398, 2402, 2406, 4848, 2410, 2414, 2418, 2422, 2426, 2430, 7503, 0}, {2394, 2398, 2402, 2406, 4848, 2410, 2414, 2418, 2422, 2426, 2430, 7503, 0}, 2, 1, 691, 691, {2154,6118,0,0,0,0,0,0,0,0,0,0,0,0},{2176,2194,0,0,0,0,0,0,0,0},{4246,0,0,0,0,0,0,0,0,0,0,0},{4268,0,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{1812, 36422, 36440, {36455, 36468, 36487, 8513, 36515, 36532, 10761}, {36543, 36550, 36557, 36788, 36571, 36578, 36585}, {456, 459, 36592, 465, 468, 459, 465}, {36595, 36608, 501, 510, 15731, 36719, 36726, 542, 36623, 36642, 36657, 36674, 0}, {36595, 36608, 501, 510, 15731, 36719, 36726, 542, 36623, 36642, 36657, 36674, 0}, {36691, 36698, 36705, 36712, 15731, 36719, 36726, 36733, 36740, 36747, 36754, 36761, 0}, {36691, 36698, 36705, 36712, 15731, 36719, 36726, 36733, 36740, 36747, 36754, 36761, 0}, 0, 1, 691, 691, {9298,9328,0,0,0,0,0,0,0,0,0,0,0,0},{36329,9386,0,0,0,0,0,0,0,0},{4246,0,0,0,0,0,0,0,0,0,0,0},{4268,0,0,0,0,0,0,0,0},{36120,0,0,0,0,0,0,0}},
	{1, 12926, 12938, {12950, 12965, 12980, 12995, 13012, 13031, 13042}, {13053, 13060, 13067, 13074, 13081, 13088, 13095}, {0, 0, 0, 0, 0, 0, 0}, {13102, 13113, 8644, 13126, 8666, 13137, 13144, 8691, 13151, 13166, 13179, 13190, 0}, {13102, 13113, 8644, 13126, 8666, 13137, 13144, 8691, 13151, 13166, 13179, 13190, 0}, {13203, 13210, 13217, 13224, 8666, 13137, 13144, 13231, 13238, 13245, 13252, 13259, 0}, {13203, 13210, 13217, 13224, 8666, 13137, 13144, 13231, 13238, 13245, 13252, 13259, 0}, 0, 1, 691, 187, {2154,2696,8015,2440,189,0,0,0,0,0,0,0,0,0},{13266,13284,0,0,0,0,0,0,0,0},{251,843,0,0,0,0,0,0,0,0,0,0},{269,848,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}},
	{3551, 1820, 1823, {19918, 19928, 19937, 19946, 19957, 19967, 19972}, {19979, 19985, 19990, 19995, 20000, 10578, 20004}, {20009, 2565, 1914, 7600, 1909, 3925, 1914}, {14948, 14955, 10132, 14962, 3668, 20011, 20016, 20021, 14987, 14996, 15004, 15011, 0}, {14948, 14955, 10132, 14962, 3668, 20011, 20016, 20021, 14987, 14996, 15004, 15011, 0}, {20028, 20033, 3699, 2664, 3668, 20011, 20016, 20037, 10562, 2684, 20041, 20046, 0}, {20028, 20033, 3699, 2664, 3668, 20011, 20016, 20037, 10562, 2684, 20041, 20046, 0}, 0, 1, 185, 187, {1545,15492,0,0,0,0,0,0,0,0,0,0,0,0},{20050,16096,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{12916,0,0,0,0,0,0,0}},
	{289, 30416, 30426, {30439, 30446, 30452, 30459, 30465, 30471, 30479}, {30489, 30493, 30497, 30501, 30505, 30509, 30513}, {12692, 12692, 12692, 12692, 12692, 12692, 12692}, {30519, 30528, 30537, 30542, 30548, 30554, 30560, 30567, 30573, 30582, 30591, 30599, 0}, {30519, 30528, 30537, 30542, 30548, 30554, 30560, 30567, 30573, 30582, 30591, 30599, 0}, {30608, 30612, 3699, 30616, 3668, 30620, 30624, 30628, 30633, 30637, 30643, 30647, 0}, {30608, 30612, 3699, 30616, 3668, 30620, 30624, 30628, 30633, 30637, 30643, 30647, 0}, 0, 0, 2438, 187, {2440,2451,1556,0,0,0,0,0,0,0,0,0,0,0},{10501,222,0,0,0,0,0,0,0,0},{843,251,0,0,0,0,0,0,0,0,0,0},{848,269,0,0,0,0,0,0,0},{278,0,0,0,0,0,0,0}},
	{3551, 0, 0, {31414, 31421, 31429, 31436, 31443, 31451, 31459}, {31466, 31469, 31472, 31475, 31478, 31481, 31484}, {2308, 2308, 2302, 2308, 12692, 3925, 12692}, {31487, 31495, 31505, 31511, 31519, 31524, 31529, 31534, 31541, 19899, 31549, 31557, 0}, {31487, 31495, 31505, 31511, 31519, 31524, 31529, 31534, 31541, 19899, 31549, 31557, 0}, {2651, 31565, 3699, 31569, 3668, 30620, 30624, 31573, 3639, 2684, 31577, 16295, 0}, {2651, 31565, 3699, 31569, 3668, 30620, 30624, 31573, 3639, 2684, 31577, 16295, 0}, 0, 0, 185, 187, {1120,17272,0,0,0,0,0,0,0,0,0,0,0,0},{3789,3808,0,0,0,0,0,0,0,0},{251,0,0,0,0,0,0,0,0,0,0,0},{269,0,0,0,0,0,0,0,0},{2207,0,0,0,0,0,0,0}}
};


static const NumberFormatEntry number_format_entries [] = {
	{691, 36891, 691, 36891, 36893, 36903, 36906, 36921, 36924, 36934, 2438, 36943, 3, 2, 0, 0, 3, 2, 2, {3, -1}, {3, -1}},
	{36891, 36948, 36891, 36948, 36951, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 8, 3, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 36969, 36957, 36959, 36963, 36973, 36982, 2438, 36967, 8, 3, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 36990, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 2, 0, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 36990, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 2, 0, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 36948, 36891, 36948, 36993, 36957, 36959, 36963, 36997, 37009, 2438, 36967, 8, 3, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 37021, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 12, 2, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 36969, 36957, 36959, 36963, 37024, 37035, 2438, 36967, 8, 3, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 36969, 36957, 36959, 36963, 37046, 37060, 2438, 36967, 8, 3, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37073, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 0, 0, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 36969, 36957, 36959, 36963, 37075, 37085, 2438, 36967, 8, 3, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 36948, 36891, 36948, 36969, 36957, 37094, 36963, 36924, 36934, 2438, 36967, 8, 3, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 36948, 36891, 36948, 36969, 36957, 36959, 36963, 37103, 37111, 2438, 36967, 8, 3, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37119, 36957, 36959, 36963, 36924, 36934, 2438, 37123, 2, 2, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 36948, 36891, 36948, 37128, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 8, 3, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 37021, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 8, 3, 1, 1, 1, 0, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 36969, 36957, 36959, 36963, 37075, 37131, 2438, 36967, 9, 2, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37141, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 1, 0, 1, 1, 1, 0, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37145, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 1, 0, 0, 0, 1, 0, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 36969, 36957, 36959, 36963, 37149, 37159, 2438, 36967, 12, 2, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 36948, 36891, 36948, 0, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 9, 2, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 36948, 36891, 36948, 37168, 36957, 36959, 36963, 37172, 37191, 2438, 36967, 8, 3, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 37210, 36957, 36959, 36963, 37075, 37131, 2438, 36967, 9, 2, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 37213, 691, 37213, 37217, 36957, 36959, 36963, 37221, 37230, 2438, 36967, 8, 3, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 0, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 8, 3, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 36948, 36891, 36948, 37239, 36957, 37247, 36963, 37264, 37292, 2438, 36967, 8, 3, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 37319, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 8, 3, 1, 1, 2, 2, 2, {3, -1}, {3, -1}},
	{36891, 36948, 36891, 36948, 36969, 36957, 36959, 36963, 36997, 37009, 2438, 36967, 8, 3, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 36948, 36891, 36948, 37322, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 8, 3, 1, 1, 1, 0, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 36948, 37021, 36957, 37328, 36963, 36924, 36934, 2438, 36967, 8, 3, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37335, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 1, 0, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 37339, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 8, 3, 2, 2, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37343, 36957, 37346, 36963, 37369, 36934, 37385, 37393, 3, 0, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 37401, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 1, 0, 1, 1, 1, 0, 2, {3, -1}, {3, -1}},
	{36891, 36948, 36891, 36948, 37404, 36957, 37408, 36963, 36924, 36934, 2438, 36967, 5, 1, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 36948, 36891, 36948, 37425, 36957, 0, 36963, 36924, 36934, 2438, 36967, 8, 3, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 36969, 36957, 36959, 36963, 37429, 37443, 2438, 36967, 8, 3, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 36948, 36891, 36948, 36969, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 8, 3, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 36948, 36891, 36948, 36969, 36957, 37456, 36963, 37470, 37482, 2438, 36967, 1, 0, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 36948, 36891, 36948, 37493, 36957, 36959, 36963, 37496, 37507, 2438, 36967, 8, 3, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 36948, 36891, 36948, 0, 36957, 0, 36963, 36924, 36934, 2438, 36967, 8, 3, 1, 1, 1, 2, 2, {3, 0}, {3, 0}},
	{185, 36891, 185, 36891, 37517, 36903, 37526, 36921, 37537, 36934, 37552, 37393, 3, 0, 0, 0, 3, 2, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 37559, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 8, 3, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37563, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 8, 3, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 0, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 9, 2, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 36969, 36957, 36959, 36963, 37569, 37579, 2438, 36967, 8, 3, 7, 3, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 37588, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 9, 2, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36948, 691, 36948, 10586, 36957, 0, 36963, 36924, 36934, 2438, 36967, 1, 0, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 36948, 36891, 36948, 10586, 36957, 0, 36963, 36924, 36934, 2438, 36967, 1, 0, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 10586, 36957, 37595, 36963, 36924, 36934, 2438, 36967, 1, 0, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 36948, 36891, 36948, 10586, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 1, 0, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 36948, 36891, 36948, 0, 36957, 37601, 36963, 36924, 36934, 2438, 36967, 8, 3, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 37021, 36957, 37328, 36963, 36924, 36934, 2438, 36967, 2, 0, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37642, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 12, 2, 0, 0, 1, 2, 2, {3, 2}, {3, 2}},
	{691, 36891, 691, 36891, 36969, 36957, 0, 36963, 36924, 36934, 2438, 36967, 1, 0, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 36948, 36891, 36948, 36969, 36957, 37328, 36963, 36924, 36934, 2438, 36967, 8, 3, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 36969, 36957, 0, 36963, 36924, 36934, 2438, 36967, 1, 0, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37646, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 1, 0, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 36948, 36891, 36948, 0, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 8, 3, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 36948, 36891, 36948, 0, 36957, 37649, 36963, 36924, 36934, 2438, 36967, 8, 3, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37666, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 1, 0, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 36948, 36891, 36948, 0, 36903, 36959, 36921, 36924, 36934, 2438, 36967, 9, 2, 1, 1, 1, 0, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37670, 36957, 37677, 36963, 36924, 36934, 2438, 36967, 12, 2, 1, 0, 1, 2, 2, {3, 2}, {3, 2}},
	{691, 36891, 691, 36891, 0, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 12, 2, 0, 0, 1, 2, 2, {3, 2}, {3, 2}},
	{691, 36891, 691, 36891, 37642, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 12, 2, 0, 0, 1, 2, 2, {3, 2}, {3, 2}},
	{691, 36891, 691, 36891, 37642, 36957, 0, 36963, 36924, 36934, 2438, 36967, 12, 2, 0, 0, 1, 2, 2, {3, 2}, {3, 2}},
	{691, 36891, 691, 36891, 37642, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 12, 2, 0, 0, 1, 2, 2, {3, 2}, {3, 2}},
	{691, 36891, 691, 36891, 37642, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 12, 2, 0, 0, 1, 2, 2, {3, 2}, {3, 2}},
	{691, 36891, 691, 36891, 37642, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 12, 2, 0, 0, 1, 2, 2, {3, 2}, {3, 2}},
	{691, 36891, 691, 36891, 37642, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 12, 2, 2, 2, 1, 2, 2, {3, 2}, {3, 2}},
	{691, 36891, 691, 36891, 37642, 36957, 0, 36963, 36924, 36934, 2438, 36967, 12, 2, 1, 1, 1, 2, 2, {3, 2}, {3, 2}},
	{37704, 36891, 691, 36891, 37642, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 12, 2, 0, 0, 1, 2, 2, {3, 2}, {3, 2}},
	{691, 36891, 691, 36891, 0, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 9, 2, 1, 1, 1, 0, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 36990, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 2, 0, 1, 1, 1, 2, 2, {3, 0}, {3, 0}},
	{691, 36891, 691, 36891, 37706, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 1, 0, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37709, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 5, 1, 1, 1, 2, 2, 2, {3, -1}, {3, 0}},
	{36891, 691, 36891, 691, 37713, 36957, 37717, 36963, 36924, 36934, 2438, 36967, 2, 0, 1, 1, 1, 0, 2, {3, 0}, {3, 0}},
	{36891, 691, 36891, 691, 36969, 36957, 36959, 36963, 37075, 37085, 2438, 36967, 1, 0, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37642, 36957, 0, 36963, 36924, 36934, 2438, 36967, 12, 2, 0, 0, 1, 2, 2, {3, 2}, {3, 2}},
	{691, 36891, 691, 36891, 37754, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 1, 0, 1, 1, 1, 2, 2, {3, -1}, {3, 2}},
	{691, 36891, 691, 36891, 37762, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 1, 0, 1, 1, 1, 2, 1, {3, 0}, {3, 0}},
	{36891, 36948, 36891, 36948, 0, 36957, 0, 36963, 37103, 37111, 2438, 36967, 8, 3, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37769, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 1, 0, 1, 1, 1, 2, 2, {3, -1}, {3, 2}},
	{36891, 691, 36891, 691, 37782, 36903, 0, 36963, 36924, 36934, 2438, 36967, 8, 3, 1, 1, 1, 0, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37785, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 1, 0, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 0, 36957, 0, 36963, 36924, 36934, 2438, 36967, 9, 2, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37789, 36957, 0, 36963, 36924, 36934, 2438, 36967, 1, 0, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36948, 691, 36948, 10586, 36957, 0, 36963, 36924, 36934, 2438, 36967, 1, 0, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 37021, 36957, 37328, 36963, 36924, 36934, 2438, 36967, 12, 2, 0, 0, 1, 2, 2, {3, 0}, {3, 0}},
	{691, 36891, 691, 36891, 37789, 36957, 0, 36963, 36924, 36934, 2438, 36967, 1, 0, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 36990, 36957, 0, 36963, 36924, 36934, 2438, 36967, 2, 0, 1, 1, 1, 2, 2, {3, -1}, {3, 0}},
	{36891, 36948, 36891, 36948, 36969, 36957, 0, 36963, 36924, 36934, 2438, 36967, 8, 3, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 36948, 36891, 36948, 0, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 8, 3, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 36948, 36891, 36948, 37239, 36957, 0, 36963, 36924, 36934, 2438, 36967, 8, 3, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 37793, 36957, 0, 36963, 36924, 36934, 2438, 36967, 9, 2, 1, 1, 1, 0, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37706, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 1, 0, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 36893, 36903, 36906, 36921, 36924, 36934, 2438, 36943, 3, 2, 0, 0, 3, 2, 2, {3, -1}, {3, -1}},
	{36891, 36948, 36891, 36948, 36951, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 8, 3, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 36969, 36957, 36959, 36963, 36973, 36982, 2438, 36967, 8, 3, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37796, 36957, 37800, 36963, 36924, 36934, 2438, 36967, 1, 0, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 36948, 36891, 36948, 36993, 36957, 36959, 36963, 36997, 37009, 2438, 36967, 8, 3, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 37021, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 12, 2, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 36969, 36957, 36959, 36963, 37024, 37035, 2438, 36967, 8, 3, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 36969, 36957, 36959, 36963, 37046, 37060, 2438, 36967, 8, 3, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37073, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 0, 0, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 36948, 36891, 36948, 36969, 36957, 37094, 36963, 36924, 36934, 2438, 36967, 8, 3, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 36948, 36891, 36948, 36969, 36957, 36959, 36963, 37103, 37111, 2438, 36967, 8, 3, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37119, 36957, 36959, 36963, 36924, 36934, 2438, 37123, 2, 2, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 36948, 36891, 36948, 37128, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 8, 3, 1, 1, 1, 0, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 37021, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 8, 3, 1, 1, 1, 0, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 36969, 36957, 36959, 36963, 37075, 37131, 2438, 36967, 9, 2, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37141, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 1, 0, 1, 1, 1, 0, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37145, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 1, 0, 0, 0, 1, 0, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 36969, 36957, 36959, 36963, 37149, 37159, 2438, 36967, 12, 2, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 36948, 36891, 36948, 37021, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 9, 2, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 36948, 36891, 36948, 37168, 36957, 36959, 36963, 37172, 37191, 2438, 36967, 8, 3, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 37210, 36957, 36959, 36963, 37075, 37131, 2438, 36967, 9, 2, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 37213, 691, 37213, 37217, 36957, 36959, 36963, 37221, 37230, 2438, 36967, 8, 3, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 0, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 8, 3, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 36948, 36891, 36948, 37239, 36957, 37247, 36963, 37264, 37292, 2438, 36967, 8, 3, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 37319, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 8, 3, 1, 1, 2, 2, 2, {3, -1}, {3, -1}},
	{36891, 36948, 36891, 36948, 36969, 36957, 36959, 36963, 36997, 37009, 2438, 36967, 8, 3, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 36948, 36891, 36948, 37322, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 8, 3, 1, 1, 1, 0, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 36948, 37021, 36957, 37328, 36963, 36924, 36934, 2438, 36967, 8, 3, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37335, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 1, 0, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 37339, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 8, 3, 2, 2, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37343, 36957, 37346, 36963, 37369, 36934, 37385, 37393, 3, 0, 0, 0, 1, 0, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 37401, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 1, 0, 1, 1, 1, 0, 2, {3, -1}, {3, -1}},
	{36891, 36948, 36891, 36948, 37404, 36957, 37408, 36963, 36924, 36934, 2438, 36967, 5, 1, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 36948, 36891, 36948, 37425, 36957, 0, 36963, 36924, 36934, 2438, 36967, 8, 3, 0, 0, 1, 0, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 36969, 36957, 36959, 36963, 37429, 37443, 2438, 36967, 8, 3, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 36948, 36891, 36948, 36969, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 8, 3, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 36948, 36891, 36948, 36969, 36957, 37456, 36963, 37470, 37482, 2438, 36967, 1, 0, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 36948, 36891, 36948, 37493, 36957, 36959, 36963, 37496, 37507, 2438, 36967, 8, 3, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 36948, 36891, 36948, 37810, 36957, 0, 36963, 36924, 36934, 2438, 36967, 8, 3, 1, 1, 1, 2, 2, {3, 0}, {3, 0}},
	{185, 36891, 185, 36891, 37517, 36903, 37526, 36921, 37537, 36934, 37552, 37393, 3, 0, 0, 0, 3, 0, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 37559, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 8, 3, 0, 0, 1, 0, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37563, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 8, 3, 0, 0, 1, 0, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 37817, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 9, 2, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 36969, 36957, 36959, 36963, 37569, 37579, 2438, 36967, 8, 3, 7, 3, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 37588, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 9, 2, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36948, 691, 36948, 10586, 36957, 0, 36963, 36924, 36934, 2438, 36967, 1, 0, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 36948, 36891, 36948, 10586, 36957, 0, 36963, 36924, 36934, 2438, 36967, 1, 0, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 10586, 36957, 37595, 36963, 36924, 36934, 2438, 36967, 1, 0, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 36948, 36891, 36948, 10586, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 1, 0, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 36948, 36891, 36948, 0, 36957, 37601, 36963, 36924, 36934, 2438, 36967, 8, 3, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 37021, 36957, 37328, 36963, 36924, 36934, 2438, 36967, 2, 0, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37642, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 12, 2, 0, 0, 1, 2, 2, {3, 2}, {3, 2}},
	{691, 36891, 691, 36891, 36969, 36957, 0, 36963, 36924, 36934, 2438, 36967, 1, 0, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 36948, 36891, 36948, 37021, 36957, 37328, 36963, 36924, 36934, 2438, 36967, 8, 3, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37666, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 1, 0, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 36948, 36891, 36948, 37822, 36903, 36959, 36921, 36924, 36934, 2438, 36967, 9, 2, 1, 1, 1, 0, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37642, 36957, 37677, 36963, 36924, 36934, 2438, 36967, 12, 2, 1, 0, 1, 2, 2, {3, 2}, {3, 2}},
	{691, 36891, 691, 36891, 37642, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 12, 2, 0, 0, 1, 2, 2, {3, 2}, {3, 2}},
	{691, 36891, 691, 36891, 37642, 36957, 0, 36963, 36924, 36934, 2438, 36967, 12, 2, 0, 0, 1, 2, 2, {3, 2}, {3, 2}},
	{691, 36891, 691, 36891, 37642, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 12, 2, 0, 0, 1, 2, 2, {3, 2}, {3, 2}},
	{691, 36891, 691, 36891, 37642, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 12, 2, 0, 0, 1, 2, 2, {3, 2}, {3, 2}},
	{691, 36891, 691, 36891, 37642, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 12, 2, 0, 0, 1, 2, 2, {3, 2}, {3, 2}},
	{691, 36891, 691, 36891, 37642, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 12, 2, 2, 2, 1, 2, 2, {3, 2}, {3, 2}},
	{691, 36891, 691, 36891, 37642, 36957, 0, 36963, 36924, 36934, 2438, 36967, 12, 2, 1, 1, 1, 2, 2, {3, 2}, {3, 2}},
	{37704, 36891, 691, 36891, 37642, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 12, 2, 0, 0, 1, 2, 2, {3, 2}, {3, 2}},
	{691, 36891, 691, 36891, 36990, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 2, 0, 1, 1, 1, 2, 2, {3, 0}, {3, 0}},
	{691, 36891, 691, 36891, 37706, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 1, 0, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37709, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 5, 1, 1, 1, 2, 2, 2, {3, -1}, {3, 0}},
	{36891, 691, 36891, 691, 37713, 36957, 37717, 36963, 36924, 36934, 2438, 36967, 2, 0, 1, 1, 1, 0, 2, {3, 0}, {3, 0}},
	{36891, 691, 36891, 691, 36969, 36957, 36959, 36963, 37075, 37085, 2438, 36967, 1, 0, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37642, 36957, 0, 36963, 36924, 36934, 2438, 36967, 12, 2, 0, 0, 1, 2, 2, {3, 2}, {3, 2}},
	{691, 36891, 691, 36891, 37754, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 1, 0, 1, 1, 1, 2, 2, {3, -1}, {3, 2}},
	{691, 36891, 691, 36891, 37762, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 1, 0, 1, 1, 1, 2, 1, {3, 0}, {3, 0}},
	{691, 36891, 691, 36891, 37769, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 1, 0, 1, 1, 1, 2, 2, {3, -1}, {3, 2}},
	{36891, 691, 36891, 691, 37782, 36903, 0, 36963, 36924, 36934, 2438, 36967, 8, 3, 1, 1, 1, 0, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37785, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 1, 0, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37789, 36957, 0, 36963, 36924, 36934, 2438, 36967, 9, 2, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37789, 36957, 0, 36963, 36924, 36934, 2438, 36967, 1, 0, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36948, 691, 36948, 10586, 36957, 0, 36963, 36924, 36934, 2438, 36967, 1, 0, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 37021, 36957, 37328, 36963, 36924, 36934, 2438, 36967, 12, 2, 0, 0, 1, 2, 2, {3, 0}, {3, 0}},
	{691, 36891, 691, 36891, 37789, 36957, 0, 36963, 36924, 36934, 2438, 36967, 1, 0, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 36990, 36957, 0, 36963, 36924, 36934, 2438, 36967, 2, 0, 1, 1, 1, 2, 2, {3, -1}, {3, 0}},
	{36891, 36948, 36891, 36948, 36969, 36957, 0, 36963, 36924, 36934, 2438, 36967, 8, 3, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 36948, 36891, 36948, 37239, 36957, 0, 36963, 36924, 36934, 2438, 36967, 8, 3, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 37793, 36957, 0, 36963, 36924, 36934, 2438, 36967, 9, 2, 1, 1, 1, 0, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37706, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 1, 0, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37828, 36903, 36906, 36921, 36924, 36934, 2438, 36943, 3, 2, 0, 0, 3, 0, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37141, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 2, 0, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 37838, 691, 37838, 37217, 36957, 36959, 36963, 37024, 37035, 2438, 36967, 2, 2, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37706, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 1, 0, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37073, 36957, 36959, 36963, 37075, 37085, 2438, 36967, 1, 0, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 36969, 36957, 36959, 36963, 37103, 37111, 2438, 36967, 8, 3, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 37838, 691, 37838, 37217, 36957, 36959, 36963, 37075, 37131, 2438, 36967, 2, 2, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 36948, 36969, 36957, 36959, 36963, 37149, 37159, 2438, 36967, 12, 2, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 36948, 36891, 36948, 37021, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 8, 3, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 36948, 36891, 36948, 36969, 36957, 36959, 36963, 37075, 37131, 2438, 36967, 8, 3, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 36948, 36891, 36948, 36969, 36957, 37328, 36963, 36924, 36934, 2438, 36967, 8, 3, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 36948, 36891, 36948, 37840, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 8, 3, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 36969, 36957, 0, 36963, 36924, 36934, 2438, 36967, 1, 0, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 36948, 36891, 36948, 37848, 36903, 36959, 36921, 36924, 36934, 2438, 36967, 9, 2, 1, 1, 1, 0, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37855, 36957, 37677, 36963, 36924, 36934, 2438, 36967, 12, 2, 1, 0, 1, 2, 2, {3, 2}, {3, 2}},
	{691, 36891, 691, 36891, 37859, 36903, 36959, 36921, 36924, 36934, 2438, 36943, 3, 2, 0, 0, 3, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37073, 36957, 37800, 36963, 36924, 36934, 2438, 36967, 0, 0, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 36969, 36957, 36959, 36963, 37024, 37035, 2438, 36967, 9, 2, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37073, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 1, 0, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 36969, 36957, 36959, 36963, 37075, 37085, 2438, 36967, 8, 3, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 36948, 36891, 36948, 37073, 36957, 36959, 36963, 37103, 37111, 2438, 36967, 15, 3, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 36948, 36891, 36948, 36969, 36957, 37328, 36963, 36924, 36934, 2438, 36967, 8, 3, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37869, 36903, 36906, 36921, 36924, 36934, 2438, 36943, 3, 0, 0, 0, 3, 3, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37073, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 0, 0, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 36969, 36957, 36959, 36963, 37024, 37035, 2438, 36967, 8, 3, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37073, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 1, 0, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 7920, 36957, 36959, 36963, 37075, 37085, 2438, 36967, 1, 0, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36948, 691, 36948, 37217, 36957, 36959, 36963, 37103, 37111, 2438, 36967, 2, 2, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 37879, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 8, 3, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37882, 36903, 36906, 36921, 36924, 36934, 2438, 36943, 3, 2, 0, 0, 3, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37892, 36957, 37800, 36963, 36924, 36934, 2438, 36967, 0, 0, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 37838, 691, 37838, 0, 36957, 36959, 36963, 37024, 37035, 2438, 36967, 9, 2, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37073, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 1, 0, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 37897, 36957, 36959, 36963, 37075, 37085, 2438, 36967, 1, 0, 0, 0, 1, 0, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 36969, 36957, 36959, 36963, 37103, 37111, 2438, 36967, 8, 3, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 37879, 36957, 0, 36963, 36924, 36934, 2438, 36967, 8, 3, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37901, 36903, 36906, 36921, 36924, 36934, 2438, 36943, 3, 2, 0, 0, 3, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 36969, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 1, 0, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37911, 36957, 36959, 36963, 37075, 37085, 2438, 36967, 1, 0, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 36948, 36891, 36948, 36969, 36957, 36959, 36963, 37103, 37111, 2438, 36967, 8, 3, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 37879, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 8, 3, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37915, 36903, 36906, 36921, 36924, 36934, 2438, 36943, 3, 2, 0, 0, 3, 3, 2, {3, -1}, {3, -1}},
	{36891, 36948, 36891, 36948, 10586, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 1, 0, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37073, 36957, 36959, 36963, 37075, 37085, 2438, 36967, 1, 0, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 37925, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 8, 3, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37930, 36903, 36906, 36921, 36924, 36934, 2438, 36943, 3, 2, 0, 0, 3, 3, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37073, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 1, 0, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 37940, 36957, 36959, 36963, 37075, 37085, 2438, 36967, 2, 0, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 37925, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 8, 3, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37944, 36903, 36906, 36921, 36924, 36934, 2438, 36943, 3, 2, 0, 0, 3, 0, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 37073, 36957, 36959, 36963, 37075, 37085, 2438, 36967, 1, 0, 0, 0, 1, 0, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 37954, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 8, 3, 1, 1, 1, 0, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37959, 36903, 36906, 36921, 36924, 36934, 2438, 36943, 3, 2, 0, 0, 3, 0, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37073, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 1, 0, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37969, 36957, 36959, 36963, 37075, 37085, 2438, 36967, 1, 0, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 37973, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 8, 3, 1, 1, 1, 0, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37981, 36903, 36906, 36921, 36924, 36934, 2438, 36943, 3, 2, 0, 0, 3, 3, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37073, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 1, 0, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 37073, 36957, 36959, 36963, 37075, 37085, 2438, 36967, 1, 0, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 36969, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 8, 3, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37991, 36903, 36906, 36921, 36924, 36934, 2438, 36943, 3, 2, 0, 0, 3, 0, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37073, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 1, 0, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 37073, 36957, 36959, 36963, 37075, 37085, 2438, 36967, 2, 0, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 36969, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 8, 3, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 38001, 36903, 36906, 36921, 36924, 36934, 2438, 36943, 3, 2, 0, 0, 3, 3, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37785, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 1, 0, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 37073, 36957, 36959, 36963, 37075, 37085, 2438, 36967, 2, 0, 0, 0, 1, 0, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 38011, 36903, 36906, 36921, 36924, 36934, 2438, 36943, 3, 2, 0, 0, 3, 2, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 37073, 36957, 36959, 36963, 37075, 37085, 2438, 36967, 9, 2, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 38021, 36903, 36906, 36921, 36924, 36934, 2438, 36943, 3, 2, 0, 0, 3, 3, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 38031, 36957, 36959, 36963, 37075, 37085, 2438, 36967, 12, 2, 0, 0, 1, 0, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 38035, 36903, 36906, 36921, 36924, 36934, 2438, 36943, 3, 2, 0, 0, 3, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37642, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 12, 2, 1, 1, 1, 2, 2, {3, 2}, {3, 2}},
	{36891, 691, 36891, 691, 38045, 36957, 36959, 36963, 37075, 37085, 2438, 36967, 1, 0, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37073, 36957, 36959, 36963, 37075, 37085, 2438, 36967, 1, 0, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37073, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 1, 0, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 2308, 36957, 36959, 36963, 37075, 37085, 2438, 36967, 1, 0, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 38048, 36957, 36959, 36963, 37075, 37085, 2438, 36967, 1, 0, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37073, 36957, 36959, 36963, 37075, 37085, 2438, 36967, 1, 0, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37073, 36957, 36959, 36963, 37075, 37085, 2438, 36967, 0, 0, 1, 1, 1, 2, 2, {3, -1}, {3, 0}},
	{36891, 691, 36891, 691, 37925, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 8, 3, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 37879, 36957, 0, 36963, 36924, 36934, 2438, 36967, 8, 3, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 38051, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 8, 3, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 38059, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 8, 3, 1, 1, 1, 0, 2, {3, -1}, {3, -1}},
	{36891, 36948, 36891, 36948, 37840, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 8, 3, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 36990, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 2, 0, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 36948, 36891, 36948, 37021, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 8, 3, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 37879, 36957, 0, 36963, 36924, 36934, 2438, 36967, 8, 3, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 37817, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 9, 2, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 36948, 36891, 36948, 37848, 36903, 36959, 36921, 36924, 36934, 2438, 36967, 9, 2, 1, 1, 1, 0, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 0, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 9, 2, 1, 1, 1, 0, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 38064, 36957, 37800, 36963, 36924, 36934, 2438, 36967, 0, 0, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 38064, 36957, 37800, 36963, 36924, 36934, 2438, 36967, 0, 0, 1, 1, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 36948, 36891, 36948, 37021, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 9, 2, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{36891, 691, 36891, 691, 38059, 36957, 36959, 36963, 36924, 36934, 2438, 36967, 8, 3, 1, 1, 1, 0, 2, {3, -1}, {3, -1}},
	{36891, 36948, 36891, 36948, 37810, 36957, 0, 36963, 36924, 36934, 2438, 36967, 8, 3, 1, 1, 1, 2, 2, {3, 0}, {3, 0}},
	{36891, 36948, 36891, 36948, 37822, 36903, 36959, 36921, 36924, 36934, 2438, 36967, 9, 2, 1, 1, 1, 0, 2, {3, -1}, {3, -1}},
	{36891, 36948, 36891, 36948, 0, 36957, 0, 36963, 37103, 37111, 2438, 36967, 8, 3, 0, 0, 1, 2, 2, {3, -1}, {3, -1}},
	{691, 36891, 691, 36891, 37789, 36957, 0, 36963, 36924, 36934, 2438, 36967, 9, 2, 1, 1, 1, 2, 2, {3, -1}, {3, -1}}
};


static const CultureInfoEntry culture_entries [] = {
	{0x0001, 0x007F, 768, -1, 38068, 38071, 38078, 38093, 38097, 38068, 0, {0, 0, 38101, 0}, 0, 0, { 1256, 20420, 10004, 720, 1, ';' }},
	{0x0002, 0x007F, 257, -1, 38129, 38132, 38142, 38161, 38165, 38129, 0, {38169, 0, 0, 0}, 1, 1, { 1251, 21025, 10007, 866, 0, ';' }},
	{0x0003, 0x007F, 257, -1, 38211, 38214, 38222, 38230, 38234, 38211, 0, {38238, 0, 0, 0}, 2, 2, { 1252, 500, 10000, 850, 0, ';' }},
	{0x0004, 0x0004, 257, -1, 38258, 38265, 38293, 38300, 38304, 38308, 0, {38311, 0, 0, 0}, 3, 3, { 936, 500, 10008, 936, 0, ',' }},
	{0x0004, 0x7804, 257, -1, 38318, 38326, 38293, 38300, 38304, 38308, 0, {38311, 0, 0, 0}, 4, 4, { 936, 500, 10008, 936, 0, ',' }},
	{0x0005, 0x007F, 257, -1, 38347, 38350, 38356, 38366, 38370, 38347, 0, {38374, 0, 0, 0}, 5, 5, { 1250, 500, 10029, 852, 0, ';' }},
	{0x0006, 0x007F, 257, -1, 38400, 38403, 38410, 38416, 38420, 38400, 0, {38424, 0, 0, 0}, 6, 6, { 1252, 20277, 10000, 850, 0, ';' }},
	{0x0007, 0x007F, 257, -1, 38445, 38448, 38455, 38463, 38467, 38445, 0, {38471, 0, 0, 0}, 7, 7, { 1252, 20273, 10000, 850, 0, ';' }},
	{0x0008, 0x007F, 257, -1, 38496, 38499, 38505, 38522, 38526, 38496, 0, {38530, 0, 0, 0}, 8, 8, { 1253, 20273, 10006, 737, 0, ';' }},
	{0x0009, 0x007F, 257, -1, 38572, 38575, 38575, 38583, 38587, 38572, 0, {38591, 0, 0, 0}, 9, 9, { 1252, 37, 10000, 437, 0, ',' }},
	{0x000A, 0x007F, 257, -1, 38610, 38613, 38621, 38630, 38634, 38610, 0, {38638, 0, 0, 0}, 10, 10, { 1252, 20284, 10000, 850, 0, ';' }},
	{0x000B, 0x007F, 257, -1, 38660, 38663, 38671, 38677, 38681, 38660, 0, {38685, 0, 0, 0}, 11, 11, { 1252, 20278, 10000, 850, 0, ';' }},
	{0x000C, 0x007F, 257, -1, 38710, 38713, 38720, 38730, 38734, 38710, 0, {38738, 0, 0, 0}, 12, 12, { 1252, 20297, 10000, 850, 0, ';' }},
	{0x000D, 0x007F, 257, -1, 38760, 38763, 38770, 38781, 38785, 38760, 0, {38789, 0, 0, 0}, 13, 13, { 1255, 500, 10005, 862, 1, ',' }},
	{0x000E, 0x007F, 257, -1, 38822, 38825, 38835, 38842, 38846, 38822, 0, {38850, 0, 0, 0}, 14, 14, { 1250, 500, 10029, 852, 0, ';' }},
	{0x000F, 0x007F, 257, -1, 38866, 38869, 38879, 38889, 38893, 38866, 0, {38897, 0, 0, 0}, 15, 15, { 1252, 20871, 10079, 850, 0, ';' }},
	{0x0010, 0x007F, 257, -1, 38917, 38920, 38928, 38937, 38941, 38917, 0, {38945, 0, 0, 0}, 16, 16, { 1252, 20280, 10000, 850, 0, ';' }},
	{0x0011, 0x007F, 257, -1, 38967, 38970, 38979, 38989, 38993, 38967, 0, {38997, 0, 0, 0}, 17, 17, { 932, 20290, 10001, 932, 0, ',' }},
	{0x0012, 0x007F, 257, -1, 39024, 39027, 39034, 39044, 39048, 39024, 0, {39052, 0, 0, 0}, 18, 18, { 949, 20833, 10003, 949, 0, ',' }},
	{0x0013, 0x007F, 257, -1, 39062, 39065, 39071, 39082, 39086, 39062, 0, {39090, 0, 0, 0}, 19, 19, { 1252, 500, 10000, 850, 0, ';' }},
	{0x0014, 0x007F, 257, -1, 39112, 39115, 39125, 39131, 39135, 39139, 0, {38424, 0, 0, 0}, 20, 20, { 1252, 20277, 10000, 850, 0, ';' }},
	{0x0015, 0x007F, 257, -1, 39142, 39145, 39152, 39159, 39163, 39142, 0, {39167, 0, 0, 0}, 21, 21, { 1250, 20880, 10029, 852, 0, ';' }},
	{0x0016, 0x007F, 257, -1, 39191, 39194, 39205, 39216, 39220, 39191, 0, {39224, 0, 0, 0}, 22, 22, { 1252, 500, 10000, 850, 0, ';' }},
	{0x0017, 0x007F, 257, -1, 39247, 39250, 39258, 39268, 39272, 39247, 0, {39276, 0, 0, 0}, 23, 23, { 1252, 20273, 10000, 850, 0, ';' }},
	{0x0018, 0x007F, 257, -1, 39296, 39299, 39308, 39317, 39321, 39296, 0, {39325, 0, 0, 0}, 24, 24, { 1250, 20880, 10029, 852, 0, ';' }},
	{0x0019, 0x007F, 257, -1, 39344, 39347, 39355, 39370, 39374, 39344, 0, {39378, 0, 0, 0}, 25, 25, { 1251, 20880, 10007, 866, 0, ';' }},
	{0x001A, 0x007F, 257, -1, 39424, 39427, 39436, 39445, 39449, 39424, 0, {39453, 0, 0, 0}, 26, 26, { 1250, 500, 10082, 852, 0, ';' }},
	{0x001B, 0x007F, 257, -1, 12674, 39476, 39483, 39495, 39499, 12674, 0, {39503, 0, 0, 0}, 27, 27, { 1250, 20880, 10029, 852, 0, ';' }},
	{0x001C, 0x007F, 257, -1, 39527, 39530, 39539, 39545, 39549, 39527, 0, {39553, 0, 0, 0}, 28, 28, { 1250, 20880, 10029, 852, 0, ';' }},
	{0x001D, 0x007F, 257, -1, 39573, 39576, 39584, 39592, 39596, 39573, 0, {38424, 0, 0, 0}, 29, 29, { 1252, 20278, 10000, 850, 0, ';' }},
	{0x001E, 0x007F, 512, -1, 39600, 39603, 39608, 39618, 39622, 39600, 0, {0, 39626, 0, 0}, 30, 30, { 874, 20838, 10021, 874, 0, ',' }},
	{0x001F, 0x007F, 257, -1, 12683, 39657, 39665, 39674, 39678, 12683, 0, {39682, 0, 0, 0}, 31, 31, { 1254, 20905, 10081, 857, 0, ';' }},
	{0x0020, 0x007F, 257, -1, 39696, 39699, 39704, 39713, 39717, 39696, 0, {39721, 0, 0, 0}, 32, 32, { 1256, 20420, 10004, 720, 1, ';' }},
	{0x0021, 0x007F, 257, -1, 39751, 39754, 39765, 39782, 39786, 39751, 0, {39790, 0, 0, 0}, 33, 33, { 1252, 500, 10000, 850, 0, ';' }},
	{0x0022, 0x007F, 257, -1, 39809, 39812, 39822, 39843, 39847, 39809, 0, {39851, 0, 0, 0}, 34, 34, { 1251, 500, 10017, 866, 0, ';' }},
	{0x0023, 0x007F, 257, -1, 39897, 39900, 39911, 39932, 39936, 39897, 0, {39940, 0, 0, 0}, 35, 35, { 1251, 500, 10007, 866, 0, ';' }},
	{0x0024, 0x007F, 257, -1, 39982, 39985, 39995, 40009, 40013, 39982, 0, {40017, 0, 0, 0}, 36, 36, { 1250, 20880, 10029, 852, 0, ';' }},
	{0x0025, 0x007F, 257, -1, 40039, 40042, 40051, 40057, 40061, 40039, 0, {40065, 0, 0, 0}, 37, 37, { 1257, 500, 10029, 775, 0, ';' }},
	{0x0026, 0x007F, 257, -1, 40085, 40088, 40096, 40106, 40110, 40085, 0, {40114, 0, 0, 0}, 38, 38, { 1257, 500, 10029, 775, 0, ';' }},
	{0x0027, 0x007F, 257, -1, 40133, 40136, 40147, 40157, 40161, 40133, 0, {40165, 0, 0, 0}, 39, 39, { 1257, 500, 10029, 775, 0, ';' }},
	{0x0028, 0x007F, 257, -1, 40188, 40191, 40197, 40210, 40214, 40188, 0, {0, 0, 0, 0}, 40, 40, { 1251, 20880, 10007, 866, 0, ';' }},
	{0x0029, 0x007F, 257, -1, 40218, 40221, 40229, 40240, 40244, 40218, 0, {40248, 0, 0, 0}, 41, 41, { 1256, 20420, 10004, 720, 1, ';' }},
	{0x002A, 0x007F, 257, -1, 40272, 40275, 40286, 40301, 35137, 40272, 0, {40305, 0, 0, 0}, 42, 42, { 1258, 500, 10000, 1258, 0, ',' }},
	{0x002B, 0x007F, 257, -1, 40320, 40323, 40332, 40347, 40351, 40320, 0, {40355, 0, 0, 0}, 43, 43, { 0, 500, 2, 1, 0, ',' }},
	{0x002C, 0x007F, 257, -1, 40385, 40388, 40400, 40412, 40416, 40385, 0, {40420, 0, 0, 0}, 44, 44, { 1254, 20905, 10081, 857, 0, ';' }},
	{0x002D, 0x007F, 257, -1, 40439, 40442, 40449, 40457, 40461, 40439, 0, {40465, 0, 0, 0}, 45, 45, { 1252, 500, 10000, 850, 0, ';' }},
	{0x002F, 0x007F, 257, -1, 40485, 40488, 40499, 40520, 40524, 40485, 0, {40528, 0, 0, 0}, 46, 46, { 1251, 500, 10007, 866, 0, ';' }},
	{0x0032, 0x007F, 257, -1, 40572, 40575, 40582, 40591, 40595, 40572, 0, {0, 0, 0, 0}, 47, 47, { 1252, 500, 10000, 850, 0, ',' }},
	{0x0034, 0x007F, 257, -1, 40599, 40602, 40608, 40617, 40621, 40599, 0, {0, 0, 0, 0}, 48, 48, { 1252, 500, 10000, 850, 0, ',' }},
	{0x0035, 0x007F, 257, -1, 40625, 40628, 40633, 40641, 40645, 40625, 0, {40649, 0, 0, 0}, 49, 49, { 1252, 500, 10000, 850, 0, ',' }},
	{0x0036, 0x007F, 257, -1, 40670, 40673, 40673, 40683, 40687, 40670, 0, {40691, 0, 0, 0}, 50, 50, { 1252, 500, 10000, 850, 0, ',' }},
	{0x0037, 0x007F, 257, -1, 40711, 40714, 40723, 40745, 40749, 40711, 0, {40753, 0, 0, 0}, 51, 51, { 0, 500, 2, 1, 0, ';' }},
	{0x0038, 0x007F, 257, -1, 40818, 40821, 40829, 40839, 40843, 40818, 0, {0, 0, 0, 0}, 52, 52, { 1252, 20277, 10079, 850, 0, ';' }},
	{0x0039, 0x007F, 257, -1, 40847, 40850, 40856, 40872, 40876, 40847, 0, {40880, 0, 0, 0}, 53, 53, { 0, 500, 2, 1, 0, ',' }},
	{0x003A, 0x007F, 257, -1, 40933, 40936, 40944, 40950, 40954, 40933, 0, {40958, 0, 0, 0}, 54, 54, { 0, 500, 2, 1, 0, ',' }},
	{0x003B, 0x007F, 257, -1, 40979, 40982, 40996, 41013, 41017, 40979, 0, {41021, 0, 0, 0}, 55, 55, { 1252, 20277, 10000, 850, 0, ';' }},
	{0x003C, 0x007F, 257, -1, 41040, 41043, 41049, 41057, 41061, 41040, 0, {41065, 0, 0, 0}, 56, 56, { 1252, 500, 10000, 850, 0, ',' }},
	{0x003E, 0x007F, 257, -1, 41086, 41089, 41095, 41109, 41113, 41086, 0, {41117, 0, 0, 0}, 57, 57, { 1252, 500, 10000, 850, 0, ';' }},
	{0x003F, 0x007F, 257, -1, 41134, 41137, 41144, 41164, 41168, 41134, 0, {41172, 0, 0, 0}, 58, 58, { 0, 500, 2, 1, 0, ';' }},
	{0x0040, 0x007F, 257, -1, 41214, 41217, 41224, 41241, 41245, 41214, 0, {41249, 0, 0, 0}, 59, 59, { 1251, 20880, 10007, 866, 0, ';' }},
	{0x0041, 0x007F, 257, -1, 41287, 41290, 41298, 41308, 41312, 41287, 0, {41316, 0, 0, 0}, 60, 60, { 1252, 500, 10000, 437, 0, ',' }},
	{0x0043, 0x007F, 257, -1, 41337, 41340, 41346, 41357, 41361, 41337, 0, {41365, 0, 0, 0}, 61, 61, { 1254, 500, 10029, 857, 0, ';' }},
	{0x0045, 0x007F, 257, -1, 41385, 41388, 41396, 41412, 41416, 41385, 0, {41420, 0, 0, 0}, 62, 62, { 0, 500, 2, 1, 0, ',' }},
	{0x0046, 0x007F, 257, -1, 41485, 41488, 41496, 41515, 41519, 41485, 0, {41523, 0, 0, 0}, 63, 63, { 0, 500, 2, 1, 0, ',' }},
	{0x0047, 0x007F, 257, -1, 41570, 41573, 41582, 41604, 41608, 41570, 0, {41612, 0, 0, 0}, 64, 64, { 0, 500, 2, 1, 0, ',' }},
	{0x0048, 0x007F, 257, -1, 41668, 41671, 41677, 41693, 41697, 41668, 0, {0, 0, 0, 0}, 65, 65, { 0, 500, 2, 1, 0, ',' }},
	{0x0049, 0x007F, 257, -1, 41701, 41704, 41710, 41726, 41730, 41701, 0, {41734, 0, 0, 0}, 66, 66, { 0, 500, 2, 1, 0, ',' }},
	{0x004A, 0x007F, 257, -1, 41799, 41802, 41809, 41828, 41832, 41799, 0, {41836, 0, 0, 0}, 67, 67, { 0, 500, 2, 1, 0, ',' }},
	{0x004B, 0x007F, 257, -1, 37319, 41901, 41909, 41925, 41929, 37319, 0, {41933, 0, 0, 0}, 68, 68, { 0, 500, 2, 1, 0, ',' }},
	{0x004C, 0x007F, 257, -1, 41998, 42001, 42011, 42030, 42034, 41998, 0, {42038, 0, 0, 0}, 69, 69, { 0, 500, 2, 1, 0, ',' }},
	{0x004D, 0x007F, 257, -1, 42082, 42085, 42094, 42116, 42120, 42082, 0, {42124, 0, 0, 0}, 70, 70, { 0, 500, 2, 1, 0, ',' }},
	{0x004E, 0x007F, 257, -1, 42177, 42180, 42188, 42204, 2402, 42177, 0, {42208, 0, 0, 0}, 71, 71, { 0, 500, 2, 1, 0, ',' }},
	{0x0050, 0x007F, 257, -1, 42270, 42273, 42283, 42296, 42300, 42270, 0, {42304, 0, 0, 0}, 72, 72, { 1251, 20880, 10007, 866, 0, ';' }},
	{0x0051, 0x007F, 257, -1, 42336, 42339, 42347, 42372, 42376, 42336, 0, {0, 0, 0, 0}, 73, 73, { 0, 500, 2, 1, 0, ',' }},
	{0x0052, 0x007F, 257, -1, 42380, 42383, 42389, 42397, 42401, 42380, 0, {42405, 0, 0, 0}, 74, 74, { 1252, 20285, 10000, 850, 0, ',' }},
	{0x0053, 0x007F, 257, -1, 42421, 42424, 42430, 42446, 42450, 42421, 0, {42454, 0, 0, 0}, 75, 75, { 0, 500, 2, 1, 0, ',' }},
	{0x0054, 0x007F, 257, -1, 42509, 42512, 42516, 42526, 42530, 42509, 0, {42534, 0, 0, 0}, 76, 76, { 0, 500, 2, 1, 0, ',' }},
	{0x0056, 0x007F, 257, -1, 42580, 42583, 42592, 42599, 42603, 42580, 0, {38945, 0, 0, 0}, 77, 77, { 1252, 500, 10000, 850, 0, ';' }},
	{0x0057, 0x007F, 257, -1, 42607, 42611, 42619, 42638, 42607, 42607, 0, {0, 0, 0, 0}, 78, 78, { 0, 500, 2, 1, 0, ',' }},
	{0x005B, 0x007F, 257, -1, 42642, 42645, 42653, 42669, 42673, 42642, 0, {42677, 0, 0, 0}, 79, 79, { 0, 500, 2, 1, 0, ',' }},
	{0x005E, 0x007F, 257, -1, 8022, 42736, 42744, 42757, 42761, 8022, 0, {42765, 0, 0, 0}, 80, 80, { 0, 500, 2, 1, 0, ';' }},
	{0x005F, 0x007F, 257, -1, 42810, 42814, 42838, 42848, 42810, 42810, 0, {0, 0, 0, 0}, 81, 81, { 1252, 20297, 10000, 850, 0, ';' }},
	{0x0061, 0x007F, 257, -1, 1883, 42852, 42859, 42878, 42882, 1883, 0, {42886, 0, 0, 0}, 82, 82, { 0, 500, 2, 1, 0, ',' }},
	{0x0063, 0x007F, 1024, -1, 42936, 42939, 42946, 42955, 42959, 42936, 0, {0, 0, 0, 0}, 83, 83, { 0, 500, 2, 1, 1, ';' }},
	{0x0064, 0x007F, 257, -1, 42963, 42967, 42967, 42976, 42963, 42963, 0, {42980, 0, 0, 0}, 84, 84, { 1252, 500, 10000, 437, 0, ',' }},
	{0x0068, 0x007F, 257, -1, 43003, 43006, 43006, 43012, 43016, 43003, 0, {0, 0, 0, 0}, 85, 85, { 1252, 37, 10000, 437, 0, ',' }},
	{0x006A, 0x007F, 257, -1, 43020, 43023, 43030, 43045, 43049, 43020, 0, {0, 0, 0, 0}, 86, 86, { 1252, 37, 10000, 437, 0, ',' }},
	{0x006C, 0x007F, 257, -1, 43053, 43057, 43072, 43089, 43053, 43053, 0, {0, 0, 0, 0}, 87, 87, { 1252, 500, 10000, 850, 0, ',' }},
	{0x006F, 0x007F, 257, -1, 43093, 43096, 43108, 43120, 43124, 43093, 0, {43128, 0, 0, 0}, 88, 88, { 1252, 20277, 10000, 850, 0, ';' }},
	{0x0070, 0x007F, 257, -1, 43155, 43158, 43158, 43163, 43167, 43155, 0, {0, 0, 0, 0}, 89, 89, { 1252, 37, 10000, 437, 0, ',' }},
	{0x0078, 0x007F, 257, -1, 43171, 43174, 43185, 43195, 43199, 43171, 0, {43203, 0, 0, 0}, 90, 90, { 0, 500, 2, 1, 0, ',' }},
	{0x007E, 0x007F, 257, -1, 43216, 43219, 43226, 43236, 43240, 43216, 0, {43244, 0, 0, 0}, 91, 91, { 1252, 20297, 10000, 850, 0, ';' }},
	{0x0084, 0x007F, 257, -1, 43264, 43268, 43281, 43300, 43264, 43264, 0, {43304, 0, 0, 0}, 92, 92, { 1252, 20297, 10000, 850, 0, ';' }},
	{0x0085, 0x007F, 257, -1, 43329, 43333, 43339, 43357, 43329, 43329, 0, {0, 0, 0, 0}, 93, 93, { 1251, 20880, 10007, 866, 0, ';' }},
	{0x0087, 0x007F, 257, -1, 43361, 43364, 43364, 43376, 43380, 43361, 0, {0, 0, 0, 0}, 94, 94, { 1252, 37, 10000, 437, 0, ';' }},
	{0x0091, 0x007F, 257, -1, 43384, 43387, 43403, 43413, 43417, 43384, 0, {43421, 0, 0, 0}, 95, 95, { 1252, 20285, 10000, 850, 0, ',' }},
	{0x0401, 0x0001, 768, 95, 43446, 43452, 43474, 38093, 38097, 38068, 13629, {0, 0, 38101, 0}, 96, 96, { 1256, 20420, 10004, 720, 1, ';' }},
	{0x0402, 0x0002, 257, 11, 43538, 43544, 43565, 38161, 38165, 38129, 43603, {38169, 0, 0, 0}, 97, 97, { 1251, 21025, 10007, 866, 0, ';' }},
	{0x0403, 0x0003, 257, 31, 43606, 43612, 43628, 38230, 38234, 38211, 43646, {38238, 0, 0, 0}, 98, 98, { 1252, 500, 10000, 850, 0, ';' }},
	{0x0404, 0x7C04, 257, 107, 43649, 43655, 43677, 43693, 38304, 38308, 43697, {43700, 0, 0, 0}, 99, 99, { 950, 500, 10002, 950, 0, ',' }},
	{0x0405, 0x0005, 257, 23, 43707, 43713, 43736, 38366, 38370, 38347, 43766, {38374, 0, 0, 0}, 100, 100, { 1250, 500, 10029, 852, 0, ';' }},
	{0x0406, 0x0006, 257, 25, 43769, 43775, 43792, 38416, 38420, 38400, 43808, {38424, 0, 0, 0}, 101, 101, { 1252, 20277, 10000, 850, 0, ';' }},
	{0x0407, 0x0007, 257, 24, 43811, 43817, 43834, 38463, 38467, 38445, 43856, {38471, 0, 0, 0}, 102, 102, { 1252, 20273, 10000, 850, 0, ';' }},
	{0x0408, 0x0008, 257, 39, 43859, 43865, 43880, 38522, 38526, 38496, 43912, {38530, 0, 0, 0}, 103, 103, { 1253, 20273, 10006, 737, 0, ';' }},
	{0x0409, 0x0009, 257, 109, 43915, 43921, 43921, 38583, 38587, 38572, 43945, {38591, 0, 0, 0}, 104, 104, { 1252, 37, 10000, 437, 0, ',' }},
	{0x040B, 0x000B, 257, 33, 43948, 43954, 43972, 38677, 38681, 38660, 43986, {38685, 0, 0, 0}, 105, 105, { 1252, 20278, 10000, 850, 0, ';' }},
	{0x040C, 0x000C, 257, 35, 43989, 43995, 44011, 38730, 38734, 38710, 44030, {38738, 0, 0, 0}, 106, 106, { 1252, 20297, 10000, 850, 0, ';' }},
	{0x040D, 0x000D, 257, 47, 44033, 44039, 44055, 38781, 38785, 38760, 44079, {38789, 0, 0, 0}, 107, 107, { 1255, 500, 10005, 862, 1, ',' }},
	{0x040E, 0x000E, 257, 44, 44082, 44088, 44108, 38842, 38846, 38822, 44131, {38850, 0, 0, 0}, 108, 108, { 1250, 500, 10029, 852, 0, ';' }},
	{0x040F, 0x000F, 257, 51, 44134, 44140, 44160, 38889, 38893, 38866, 44180, {38897, 0, 0, 0}, 109, 109, { 1252, 20871, 10079, 850, 0, ';' }},
	{0x0410, 0x0010, 257, 52, 44183, 44189, 44205, 38937, 38941, 38917, 44223, {38945, 0, 0, 0}, 110, 110, { 1252, 20280, 10000, 850, 0, ';' }},
	{0x0411, 0x0011, 257, 55, 44226, 44232, 44249, 38989, 38993, 38967, 44268, {38997, 0, 0, 0}, 111, 111, { 932, 20290, 10001, 932, 0, ',' }},
	{0x0412, 0x0012, 257, 58, 44271, 44277, 44298, 39044, 39048, 39024, 44323, {39052, 0, 0, 0}, 112, 112, { 949, 20833, 10003, 949, 0, ',' }},
	{0x0413, 0x0013, 257, 77, 44326, 44332, 44352, 39082, 39086, 39062, 44375, {39090, 0, 0, 0}, 113, 113, { 1252, 500, 10000, 850, 0, ';' }},
	{0x0414, 0x7C14, 257, 78, 44378, 44384, 44411, 39131, 39135, 39139, 44433, {38424, 0, 0, 0}, 114, 114, { 1252, 20277, 10000, 850, 0, ';' }},
	{0x0415, 0x0015, 257, 86, 44436, 44442, 44458, 39159, 39163, 39142, 44474, {39167, 0, 0, 0}, 115, 115, { 1250, 20880, 10029, 852, 0, ';' }},
	{0x0416, 0x0016, 257, 14, 44477, 44483, 44503, 39216, 39220, 39191, 44523, {39224, 0, 0, 0}, 116, 116, { 1252, 500, 10000, 850, 0, ';' }},
	{0x0417, 0x0017, 257, 18, 44526, 44532, 44554, 39268, 39272, 39247, 13632, {39276, 0, 0, 0}, 117, 117, { 1252, 20273, 10000, 850, 0, ';' }},
	{0x0418, 0x0018, 257, 91, 44573, 44579, 44598, 39317, 39321, 39296, 44618, {39325, 0, 0, 0}, 118, 118, { 1250, 20880, 10029, 852, 0, ';' }},
	{0x0419, 0x0019, 257, 93, 44621, 44627, 44644, 39370, 39374, 39344, 44674, {39378, 0, 0, 0}, 119, 119, { 1251, 20880, 10007, 866, 0, ';' }},
	{0x041A, 0x001A, 257, 43, 44677, 44683, 44702, 39445, 39449, 39424, 44722, {39453, 0, 0, 0}, 120, 120, { 1250, 500, 10082, 852, 0, ';' }},
	{0x041B, 0x001B, 257, 99, 44725, 44731, 44749, 39495, 39499, 12674, 44773, {39503, 0, 0, 0}, 121, 121, { 1250, 20880, 10029, 852, 0, ';' }},
	{0x041C, 0x001C, 257, 2, 44776, 44782, 44801, 39545, 39549, 39527, 44819, {39553, 0, 0, 0}, 122, 122, { 1250, 20880, 10029, 852, 0, ';' }},
	{0x041D, 0x001D, 257, 96, 44822, 44828, 44845, 39592, 39596, 39573, 44863, {38424, 0, 0, 0}, 123, 123, { 1252, 20278, 10000, 850, 0, ';' }},
	{0x041E, 0x001E, 512, 102, 44866, 44872, 44888, 39618, 39622, 39600, 44910, {0, 39626, 0, 0}, 124, 124, { 874, 20838, 10021, 874, 0, ',' }},
	{0x041F, 0x001F, 257, 105, 44913, 44919, 44936, 39674, 39678, 12683, 44956, {39682, 0, 0, 0}, 125, 125, { 1254, 20905, 10081, 857, 0, ';' }},
	{0x0420, 0x0020, 257, 85, 44959, 44965, 44981, 39713, 39717, 39696, 45007, {39721, 0, 0, 0}, 126, 126, { 1256, 20420, 10004, 720, 1, ';' }},
	{0x0421, 0x0021, 257, 45, 45010, 45016, 45039, 39782, 39786, 39751, 45068, {39790, 0, 0, 0}, 127, 127, { 1252, 500, 10000, 850, 0, ';' }},
	{0x0422, 0x0022, 257, 108, 45071, 45077, 45097, 39843, 39847, 39809, 45135, {39851, 0, 0, 0}, 128, 128, { 1251, 500, 10017, 866, 0, ';' }},
	{0x0423, 0x0023, 257, 15, 45138, 45144, 45165, 39932, 39936, 39897, 45205, {39940, 0, 0, 0}, 129, 129, { 1251, 500, 10007, 866, 0, ';' }},
	{0x0424, 0x0024, 257, 98, 45208, 45214, 45235, 40009, 40013, 39982, 45261, {40017, 0, 0, 0}, 130, 130, { 1250, 20880, 10029, 852, 0, ';' }},
	{0x0425, 0x0025, 257, 29, 45264, 45270, 45289, 40057, 40061, 40039, 45303, {40065, 0, 0, 0}, 131, 131, { 1257, 500, 10029, 775, 0, ';' }},
	{0x0426, 0x0026, 257, 66, 45306, 45312, 45329, 40106, 40110, 40085, 45349, {40114, 0, 0, 0}, 132, 132, { 1257, 500, 10029, 775, 0, ';' }},
	{0x0427, 0x0027, 257, 64, 45352, 45358, 45381, 40157, 40161, 40133, 45401, {40165, 0, 0, 0}, 133, 133, { 1257, 500, 10029, 775, 0, ';' }},
	{0x0428, 0x7C28, 257, 103, 45404, 45415, 45444, 40210, 40214, 40188, 45480, {0, 0, 0, 0}, 134, 134, { 1251, 20880, 10007, 866, 0, ';' }},
	{0x0429, 0x0029, 257, 50, 45483, 45489, 45504, 40240, 40244, 40218, 45528, {40248, 0, 0, 0}, 135, 135, { 1256, 20420, 10004, 720, 1, ';' }},
	{0x042A, 0x002A, 257, 113, 45531, 45537, 45558, 40301, 35137, 40272, 45586, {40305, 0, 0, 0}, 136, 136, { 1258, 500, 10000, 1258, 0, ',' }},
	{0x042B, 0x002B, 257, 3, 45589, 45595, 45614, 40347, 40351, 40320, 1820, {40355, 0, 0, 0}, 137, 137, { 0, 500, 2, 1, 0, ',' }},
	{0x042C, 0x782C, 257, 7, 45648, 45659, 45691, 40412, 40416, 40385, 45717, {40420, 0, 0, 0}, 138, 138, { 1254, 20905, 10081, 857, 0, ';' }},
	{0x042D, 0x002D, 257, 31, 45720, 45726, 45741, 40457, 40461, 40439, 43646, {40465, 0, 0, 0}, 139, 139, { 1252, 500, 10000, 850, 0, ';' }},
	{0x042F, 0x002F, 257, 71, 45760, 45766, 45789, 40520, 40524, 40485, 45833, {40528, 0, 0, 0}, 140, 140, { 1251, 500, 10007, 866, 0, ';' }},
	{0x0432, 0x0032, 257, 115, 45836, 45842, 40582, 40591, 40595, 40572, 45864, {0, 0, 0, 0}, 141, 141, { 1252, 500, 10000, 850, 0, ',' }},
	{0x0434, 0x0034, 257, 115, 45867, 45873, 40608, 40617, 40621, 40599, 45864, {0, 0, 0, 0}, 142, 142, { 1252, 500, 10000, 850, 0, ',' }},
	{0x0435, 0x0035, 257, 115, 45894, 45900, 45920, 40641, 40645, 40625, 45864, {40649, 0, 0, 0}, 143, 143, { 1252, 500, 10000, 850, 0, ',' }},
	{0x0436, 0x0036, 257, 115, 45945, 45951, 45976, 40683, 40687, 40670, 45864, {40691, 0, 0, 0}, 144, 144, { 1252, 500, 10000, 850, 0, ',' }},
	{0x0437, 0x0037, 257, 37, 46000, 46006, 46025, 40745, 40749, 40711, 46080, {40753, 0, 0, 0}, 145, 145, { 0, 500, 2, 1, 0, ';' }},
	{0x0438, 0x0038, 257, 34, 46083, 46089, 46113, 40839, 40843, 40818, 46134, {0, 0, 0, 0}, 146, 146, { 1252, 20277, 10079, 850, 0, ';' }},
	{0x0439, 0x0039, 257, 48, 46137, 46143, 46157, 40872, 40876, 40847, 46188, {40880, 0, 0, 0}, 147, 147, { 0, 500, 2, 1, 0, ',' }},
	{0x043A, 0x003A, 257, 73, 46191, 46197, 46213, 40950, 40954, 40933, 46227, {40958, 0, 0, 0}, 148, 148, { 0, 500, 2, 1, 0, ',' }},
	{0x043B, 0x003B, 257, 78, 46230, 46236, 46259, 41013, 41017, 40979, 44433, {41021, 0, 0, 0}, 149, 149, { 1252, 20277, 10000, 850, 0, ';' }},
	{0x0441, 0x0041, 257, 56, 46284, 46290, 46306, 41308, 41312, 41287, 46324, {41316, 0, 0, 0}, 150, 150, { 1252, 500, 10000, 437, 0, ',' }},
	{0x0443, 0x7C43, 257, 111, 46327, 46338, 46364, 41357, 41361, 41337, 46390, {41365, 0, 0, 0}, 151, 151, { 1254, 500, 10029, 857, 0, ';' }},
	{0x0445, 0x0045, 257, 48, 46393, 46399, 46415, 41412, 41416, 41385, 46188, {41420, 0, 0, 0}, 152, 152, { 0, 500, 2, 1, 0, ',' }},
	{0x0447, 0x0047, 257, 48, 46446, 46452, 46469, 41604, 41608, 41570, 46188, {41612, 0, 0, 0}, 153, 153, { 0, 500, 2, 1, 0, ',' }},
	{0x0448, 0x0048, 257, 48, 46506, 46512, 46526, 41693, 41697, 41668, 46188, {0, 0, 0, 0}, 154, 154, { 0, 500, 2, 1, 0, ',' }},
	{0x0449, 0x0049, 257, 48, 46557, 46563, 46577, 41726, 41730, 41701, 46188, {41734, 0, 0, 0}, 155, 155, { 0, 500, 2, 1, 0, ',' }},
	{0x044A, 0x004A, 257, 48, 46617, 46623, 46638, 41828, 41832, 41799, 46188, {41836, 0, 0, 0}, 156, 156, { 0, 500, 2, 1, 0, ',' }},
	{0x044B, 0x004B, 257, 48, 46685, 46691, 46707, 41925, 41929, 37319, 46188, {41933, 0, 0, 0}, 157, 157, { 0, 500, 2, 1, 0, ',' }},
	{0x044C, 0x004C, 257, 48, 46738, 46744, 46762, 42030, 42034, 41998, 46188, {42038, 0, 0, 0}, 158, 158, { 0, 500, 2, 1, 0, ',' }},
	{0x044D, 0x004D, 257, 48, 46802, 46808, 46825, 42116, 42120, 42082, 46188, {42124, 0, 0, 0}, 159, 159, { 0, 500, 2, 1, 0, ',' }},
	{0x044E, 0x004E, 257, 48, 46862, 46868, 46884, 42204, 2402, 42177, 46188, {42208, 0, 0, 0}, 160, 160, { 0, 500, 2, 1, 0, ',' }},
	{0x0451, 0x0051, 257, 20, 46915, 46921, 46937, 42372, 42376, 42336, 13711, {0, 0, 0, 0}, 161, 161, { 0, 500, 2, 1, 0, ',' }},
	{0x0452, 0x0052, 257, 36, 46983, 46989, 47012, 42397, 42401, 42380, 47039, {42405, 0, 0, 0}, 162, 162, { 1252, 20285, 10000, 850, 0, ',' }},
	{0x0453, 0x0053, 257, 57, 47042, 47048, 47065, 42446, 42450, 42421, 47105, {42454, 0, 0, 0}, 163, 163, { 0, 500, 2, 1, 0, ',' }},
	{0x0454, 0x0054, 257, 60, 47108, 47114, 47125, 42526, 42530, 42509, 47147, {42534, 0, 0, 0}, 164, 164, { 0, 500, 2, 1, 0, ',' }},
	{0x0456, 0x0056, 257, 31, 47150, 47156, 47173, 42599, 42603, 42580, 43646, {38945, 0, 0, 0}, 165, 165, { 1252, 500, 10000, 850, 0, ';' }},
	{0x0457, 0x0057, 257, 48, 47190, 47197, 47213, 42638, 42607, 42607, 46188, {0, 0, 0, 0}, 166, 166, { 0, 500, 2, 1, 0, ',' }},
	{0x045B, 0x005B, 257, 63, 47247, 47253, 47273, 42669, 42673, 42642, 47323, {42677, 0, 0, 0}, 167, 167, { 0, 500, 2, 1, 0, ',' }},
	{0x045E, 0x005E, 257, 32, 47326, 47332, 47351, 42757, 42761, 8022, 47382, {42765, 0, 0, 0}, 168, 168, { 0, 500, 2, 1, 0, ';' }},
	{0x0461, 0x0061, 257, 79, 47385, 47391, 47406, 42878, 42882, 1883, 47443, {42886, 0, 0, 0}, 169, 169, { 0, 500, 2, 1, 0, ',' }},
	{0x0463, 0x0063, 1024, 1, 47446, 47452, 47473, 42955, 42959, 42936, 47503, {0, 0, 0, 0}, 170, 170, { 0, 500, 2, 1, 1, ';' }},
	{0x0464, 0x0064, 257, 84, 47506, 47513, 47536, 42976, 42963, 42963, 47557, {42980, 0, 0, 0}, 171, 171, { 1252, 500, 10000, 437, 0, ',' }},
	{0x0468, 0x7C68, 257, 75, 47560, 47571, 47594, 43012, 43016, 43003, 47611, {0, 0, 0, 0}, 172, 172, { 1252, 37, 10000, 437, 0, ',' }},
	{0x046A, 0x006A, 257, 75, 47614, 47620, 47637, 43045, 43049, 43020, 47611, {0, 0, 0, 0}, 173, 173, { 1252, 37, 10000, 437, 0, ',' }},
	{0x046C, 0x006C, 257, 115, 47683, 47690, 43072, 43089, 43053, 43053, 45864, {0, 0, 0, 0}, 174, 174, { 1252, 500, 10000, 850, 0, ',' }},
	{0x046F, 0x006F, 257, 38, 47720, 47726, 47750, 43120, 43124, 43093, 47781, {43128, 0, 0, 0}, 175, 175, { 1252, 20277, 10000, 850, 0, ';' }},
	{0x0470, 0x0070, 257, 75, 47784, 47790, 47790, 43163, 43167, 43155, 47611, {0, 0, 0, 0}, 176, 176, { 1252, 37, 10000, 437, 0, ',' }},
	{0x0478, 0x0078, 257, 20, 47805, 47811, 47830, 43195, 43199, 43171, 13711, {43203, 0, 0, 0}, 177, 177, { 0, 500, 2, 1, 0, ',' }},
	{0x047E, 0x007E, 257, 35, 47849, 47855, 47871, 43236, 43240, 43216, 44030, {43244, 0, 0, 0}, 178, 178, { 1252, 20297, 10000, 850, 0, ';' }},
	{0x0485, 0x0085, 257, 93, 47890, 47897, 43339, 43357, 43329, 43329, 44674, {0, 0, 0, 0}, 179, 179, { 1251, 20880, 10007, 866, 0, ';' }},
	{0x0487, 0x0087, 257, 94, 47912, 47918, 47918, 43376, 43380, 43361, 47939, {0, 0, 0, 0}, 180, 180, { 1252, 37, 10000, 437, 0, ';' }},
	{0x0491, 0x0091, 257, 36, 47942, 47948, 47981, 43413, 43417, 43384, 47039, {43421, 0, 0, 0}, 181, 181, { 1252, 20285, 10000, 850, 0, ',' }},
	{0x0801, 0x0001, 257, 49, 48017, 48023, 48037, 48067, 38097, 38068, 48071, {48074, 0, 0, 0}, 182, 182, { 1256, 20420, 10004, 720, 1, ';' }},
	{0x0804, 0x0004, 257, 20, 48106, 38326, 48112, 38300, 38304, 38308, 13711, {38311, 0, 0, 0}, 183, 183, { 936, 500, 10008, 936, 0, ',' }},
	{0x0807, 0x0007, 257, 18, 48128, 48134, 48155, 48173, 38467, 38445, 13632, {38471, 0, 0, 0}, 184, 184, { 1252, 20273, 10000, 850, 0, ';' }},
	{0x0809, 0x0009, 257, 36, 48177, 48183, 48183, 48208, 38587, 38572, 47039, {38591, 0, 0, 0}, 185, 185, { 1252, 20285, 10000, 850, 0, ',' }},
	{0x080A, 0x000A, 257, 74, 48212, 48218, 48235, 48254, 38634, 38610, 48258, {38945, 0, 0, 0}, 186, 186, { 1252, 20284, 10000, 850, 0, ',' }},
	{0x080C, 0x000C, 257, 10, 48261, 48267, 48284, 48305, 38734, 38710, 48309, {38738, 0, 0, 0}, 187, 187, { 1252, 20297, 10000, 850, 0, ';' }},
	{0x0810, 0x0010, 257, 18, 48312, 48318, 48340, 48360, 38941, 38917, 13632, {38945, 0, 0, 0}, 188, 188, { 1252, 500, 10000, 850, 0, ';' }},
	{0x0813, 0x0013, 257, 10, 48364, 48370, 48386, 48407, 39086, 39062, 48309, {39090, 0, 0, 0}, 189, 189, { 1252, 500, 10000, 850, 0, ';' }},
	{0x0814, 0x7814, 257, 78, 48411, 48417, 48444, 48460, 48464, 48468, 44433, {38424, 0, 0, 0}, 190, 190, { 1252, 20277, 10000, 850, 0, ';' }},
	{0x0816, 0x0016, 257, 88, 48471, 48477, 48499, 48521, 39220, 39191, 48525, {39224, 0, 0, 0}, 191, 191, { 1252, 500, 10000, 850, 0, ';' }},
	{0x081D, 0x001D, 257, 33, 48528, 48534, 48552, 48570, 39596, 39573, 43986, {38424, 0, 0, 0}, 192, 192, { 1252, 20278, 10000, 850, 0, ';' }},
	{0x082C, 0x742C, 257, 7, 48574, 48585, 45691, 48620, 40416, 40385, 45717, {40420, 0, 0, 0}, 193, 193, { 1251, 20880, 10007, 866, 0, ';' }},
	{0x083C, 0x003C, 257, 46, 48624, 48630, 48646, 41057, 41061, 41040, 48662, {41065, 0, 0, 0}, 194, 194, { 1252, 500, 10000, 850, 0, ',' }},
	{0x0843, 0x7843, 257, 111, 48665, 48676, 46364, 41357, 41361, 41337, 46390, {48705, 0, 0, 0}, 195, 195, { 1251, 20880, 10007, 866, 0, ';' }},
	{0x0845, 0x0045, 257, 9, 48743, 48749, 48770, 48813, 41416, 41385, 48817, {41420, 0, 0, 0}, 196, 196, { 0, 500, 2, 1, 0, ',' }},
	{0x0C01, 0x0001, 257, 30, 48820, 48826, 48841, 48865, 38097, 38068, 48869, {48074, 0, 0, 0}, 197, 197, { 1256, 20420, 10004, 720, 1, ';' }},
	{0x0C04, 0x7C04, 257, 41, 48872, 48878, 48921, 48958, 38304, 38308, 48962, {43700, 0, 0, 0}, 198, 198, { 950, 500, 10002, 950, 0, ',' }},
	{0x0C07, 0x0007, 257, 5, 48965, 48971, 48988, 49010, 38467, 38445, 49014, {38471, 0, 0, 0}, 199, 199, { 1252, 20273, 10000, 850, 0, ';' }},
	{0x0C09, 0x0009, 257, 6, 49017, 49023, 49023, 49043, 38587, 38572, 49047, {38591, 0, 0, 0}, 200, 200, { 1252, 500, 10000, 850, 0, ',' }},
	{0x0C0A, 0x000A, 257, 31, 49050, 49056, 49072, 49091, 38634, 38610, 43646, {38638, 0, 0, 0}, 201, 201, { 1252, 20284, 10000, 850, 0, ';' }},
	{0x0C0C, 0x000C, 257, 17, 49095, 49101, 49117, 49136, 38734, 38710, 49140, {49143, 0, 0, 0}, 202, 202, { 1252, 20297, 10000, 850, 0, ';' }},
	{0x0C3B, 0x003B, 257, 33, 49165, 49171, 49195, 49221, 49225, 40979, 43986, {41021, 0, 0, 0}, 203, 203, { 1252, 20278, 10000, 850, 0, ';' }},
	{0x1001, 0x0001, 257, 67, 49229, 49235, 49250, 49278, 38097, 38068, 49282, {48074, 0, 0, 0}, 204, 204, { 1256, 20420, 10004, 720, 1, ';' }},
	{0x1004, 0x0004, 257, 97, 49285, 49291, 49323, 49342, 38304, 38308, 49346, {38311, 0, 0, 0}, 205, 205, { 936, 500, 10008, 936, 0, ',' }},
	{0x1007, 0x0007, 257, 65, 49349, 49355, 49375, 49395, 38467, 38445, 49399, {38471, 0, 0, 0}, 206, 206, { 1252, 20273, 10000, 850, 0, ';' }},
	{0x1009, 0x0009, 257, 17, 49402, 49408, 49408, 49425, 38587, 38572, 49140, {38591, 0, 0, 0}, 207, 207, { 1252, 37, 10000, 850, 0, ',' }},
	{0x100A, 0x000A, 257, 40, 49429, 49435, 49455, 49476, 38634, 38610, 49480, {38638, 0, 0, 0}, 208, 208, { 1252, 20284, 10000, 850, 0, ',' }},
	{0x100C, 0x000C, 257, 18, 49483, 49489, 49510, 49529, 38734, 38710, 13632, {38738, 0, 0, 0}, 209, 209, { 1252, 20297, 10000, 850, 0, ';' }},
	{0x101A, 0x001A, 257, 8, 49533, 49539, 49573, 49604, 49608, 39424, 49612, {39453, 0, 0, 0}, 210, 210, { 1250, 870, 10082, 852, 0, ';' }},
	{0x1401, 0x0001, 257, 27, 49615, 49621, 49638, 49670, 38097, 38068, 49674, {48074, 0, 0, 0}, 211, 211, { 1256, 20420, 10004, 720, 1, ';' }},
	{0x1404, 0x7C04, 257, 72, 49677, 49683, 49722, 49759, 38304, 38308, 49763, {43700, 0, 0, 0}, 212, 212, { 950, 500, 10002, 950, 0, ',' }},
	{0x1407, 0x0007, 257, 62, 49766, 49772, 49795, 49819, 38467, 38445, 49823, {38471, 0, 0, 0}, 213, 213, { 1252, 20273, 10000, 850, 0, ';' }},
	{0x1409, 0x0009, 257, 80, 49826, 49832, 49832, 49854, 38587, 38572, 49858, {38591, 0, 0, 0}, 214, 214, { 1252, 500, 10000, 850, 0, ',' }},
	{0x140A, 0x000A, 257, 22, 49861, 49867, 49888, 49910, 38634, 38610, 49914, {38638, 0, 0, 0}, 215, 215, { 1252, 20284, 10000, 850, 0, ',' }},
	{0x140C, 0x000C, 257, 65, 49917, 49923, 49943, 49966, 38734, 38710, 49399, {38738, 0, 0, 0}, 216, 216, { 1252, 20297, 10000, 850, 0, ';' }},
	{0x141A, 0x681A, 257, 8, 49970, 49981, 50021, 50052, 50056, 50060, 49612, {50063, 0, 0, 0}, 217, 217, { 1250, 870, 10082, 852, 0, ';' }},
	{0x1801, 0x0001, 257, 68, 50086, 50092, 50109, 50139, 38097, 38068, 50143, {48074, 0, 0, 0}, 218, 218, { 1256, 20420, 10004, 720, 1, ';' }},
	{0x1809, 0x0009, 257, 46, 50146, 50152, 50152, 50170, 38587, 38572, 48662, {38591, 0, 0, 0}, 219, 219, { 1252, 500, 10000, 850, 0, ',' }},
	{0x180A, 0x000A, 257, 82, 50174, 50180, 50197, 50216, 38634, 38610, 50220, {38638, 0, 0, 0}, 220, 220, { 1252, 20284, 10000, 850, 0, ',' }},
	{0x180C, 0x000C, 257, 69, 50223, 50229, 50245, 50264, 38734, 38710, 50268, {38738, 0, 0, 0}, 221, 221, { 1252, 20297, 10000, 850, 0, ';' }},
	{0x181A, 0x701A, 257, 8, 50271, 50282, 50322, 50374, 50378, 50382, 49612, {50063, 0, 0, 0}, 222, 222, { 1250, 870, 10082, 852, 0, ';' }},
	{0x1C01, 0x0001, 257, 104, 50385, 50391, 50408, 50434, 38097, 38068, 50438, {48074, 0, 0, 0}, 223, 223, { 1256, 20420, 10004, 720, 1, ';' }},
	{0x1C09, 0x0009, 257, 115, 50441, 50447, 50447, 50470, 38587, 38572, 45864, {38591, 0, 0, 0}, 224, 224, { 1252, 500, 10000, 437, 0, ',' }},
	{0x1C0A, 0x000A, 257, 26, 50474, 50480, 50509, 50542, 38634, 38610, 50546, {38638, 0, 0, 0}, 225, 225, { 1252, 20284, 10000, 850, 0, ',' }},
	{0x1C1A, 0x6C1A, 257, 8, 50549, 50560, 50322, 50603, 50607, 50382, 49612, {40528, 0, 0, 0}, 226, 226, { 1251, 21025, 10007, 855, 0, ';' }},
	{0x2001, 0x0001, 257, 81, 50611, 50617, 50631, 50659, 38097, 38068, 50663, {48074, 0, 0, 0}, 227, 227, { 1256, 20420, 10004, 720, 1, ';' }},
	{0x2009, 0x0009, 257, 53, 50666, 50672, 50672, 50690, 38587, 38572, 50694, {38591, 0, 0, 0}, 228, 228, { 1252, 500, 10000, 850, 0, ',' }},
	{0x200A, 0x000A, 257, 112, 50697, 50703, 50723, 50744, 38634, 38610, 50748, {38638, 0, 0, 0}, 229, 229, { 1252, 20284, 10000, 850, 0, ',' }},
	{0x201A, 0x641A, 257, 8, 50751, 50762, 50021, 50805, 50809, 50060, 49612, {40528, 0, 0, 0}, 230, 230, { 1251, 870, 10082, 855, 0, ';' }},
	{0x2401, 0x0001, 257, 114, 50813, 50819, 50834, 50862, 38097, 38068, 50866, {48074, 0, 0, 0}, 231, 231, { 1256, 20420, 10004, 720, 1, ';' }},
	{0x240A, 0x000A, 257, 21, 50869, 50875, 50894, 50914, 38634, 38610, 50918, {38638, 0, 0, 0}, 232, 232, { 1252, 20284, 10000, 850, 0, ',' }},
	{0x241A, 0x701A, 257, 92, 50921, 50932, 50956, 50984, 2130, 50382, 50988, {50063, 0, 0, 0}, 233, 233, { 1250, 500, 10029, 852, 0, ';' }},
	{0x2801, 0x0001, 257, 101, 50991, 50997, 51012, 51040, 38097, 38068, 51044, {48074, 0, 0, 0}, 234, 234, { 1256, 20420, 10004, 720, 1, ';' }},
	{0x2809, 0x0009, 257, 16, 51047, 51053, 51053, 51070, 38587, 38572, 51074, {38591, 0, 0, 0}, 235, 235, { 1252, 500, 10000, 850, 0, ';' }},
	{0x280A, 0x000A, 257, 83, 51077, 51083, 51098, 51115, 38634, 38610, 51119, {38638, 0, 0, 0}, 236, 236, { 1252, 20284, 10000, 850, 0, ',' }},
	{0x281A, 0x6C1A, 257, 92, 51122, 51133, 50956, 51160, 2130, 50382, 50988, {40528, 0, 0, 0}, 237, 237, { 1251, 21025, 10007, 855, 0, ';' }},
	{0x2C01, 0x0001, 257, 54, 51164, 51170, 51186, 51216, 38097, 38068, 51220, {48074, 0, 0, 0}, 238, 238, { 1256, 20420, 10004, 720, 1, ';' }},
	{0x2C09, 0x0009, 257, 106, 51223, 51229, 51229, 51259, 38587, 38572, 51263, {38591, 0, 0, 0}, 239, 239, { 1252, 500, 10000, 850, 0, ';' }},
	{0x2C0A, 0x000A, 257, 4, 51266, 51272, 51292, 51313, 38634, 38610, 51317, {38638, 0, 0, 0}, 240, 240, { 1252, 20284, 10000, 850, 0, ',' }},
	{0x2C1A, 0x701A, 257, 70, 51320, 51331, 51359, 51392, 2130, 50382, 51396, {50063, 0, 0, 0}, 241, 241, { 1250, 500, 10029, 852, 0, ';' }},
	{0x3001, 0x0001, 257, 61, 51399, 51405, 51422, 51450, 38097, 38068, 51454, {48074, 0, 0, 0}, 242, 242, { 1256, 20420, 10004, 720, 1, ';' }},
	{0x3009, 0x0009, 257, 116, 51457, 51463, 51463, 51482, 38587, 38572, 51486, {38591, 0, 0, 0}, 243, 243, { 1252, 500, 10000, 437, 0, ',' }},
	{0x300A, 0x000A, 257, 28, 51489, 51495, 51513, 51532, 38634, 38610, 51536, {38638, 0, 0, 0}, 244, 244, { 1252, 20284, 10000, 850, 0, ',' }},
	{0x301A, 0x6C1A, 257, 70, 51539, 51550, 51359, 51581, 2130, 50382, 51396, {40528, 0, 0, 0}, 245, 245, { 1251, 21025, 10007, 855, 0, ';' }},
	{0x3401, 0x0001, 257, 59, 51585, 51591, 51607, 51637, 38097, 38068, 51641, {48074, 0, 0, 0}, 246, 246, { 1256, 20420, 10004, 720, 1, ';' }},
	{0x3409, 0x0009, 257, 84, 51644, 51650, 51650, 51672, 38587, 38572, 47557, {38591, 0, 0, 0}, 247, 247, { 1252, 500, 10000, 437, 0, ',' }},
	{0x340A, 0x000A, 257, 19, 51676, 51682, 51698, 51715, 38634, 38610, 51719, {38638, 0, 0, 0}, 248, 248, { 1252, 20284, 10000, 850, 0, ',' }},
	{0x3801, 0x0001, 257, 0, 51722, 51728, 51758, 51822, 38097, 38068, 51826, {48074, 0, 0, 0}, 249, 249, { 1256, 20420, 10004, 720, 1, ';' }},
	{0x380A, 0x000A, 257, 110, 51829, 51835, 51853, 51872, 38634, 38610, 51876, {38638, 0, 0, 0}, 250, 250, { 1252, 20284, 10000, 850, 0, ',' }},
	{0x3C01, 0x0001, 257, 12, 51879, 51885, 51902, 51934, 38097, 38068, 51938, {48074, 0, 0, 0}, 251, 251, { 1256, 20420, 10004, 720, 1, ';' }},
	{0x3C0A, 0x000A, 257, 89, 51941, 51947, 51966, 51986, 38634, 38610, 51990, {38638, 0, 0, 0}, 252, 252, { 1252, 20284, 10000, 850, 0, ',' }},
	{0x4001, 0x0001, 257, 90, 51993, 51999, 52014, 52038, 38097, 38068, 52042, {48074, 0, 0, 0}, 253, 253, { 1256, 20420, 10004, 720, 1, ';' }},
	{0x4009, 0x0009, 257, 48, 52045, 52051, 52051, 52067, 38587, 38572, 46188, {38591, 0, 0, 0}, 254, 254, { 1252, 37, 10000, 437, 0, ',' }},
	{0x400A, 0x000A, 257, 13, 52071, 52077, 52095, 52114, 38634, 38610, 52118, {38638, 0, 0, 0}, 255, 255, { 1252, 20284, 10000, 850, 0, ',' }},
	{0x440A, 0x000A, 257, 100, 52121, 52127, 52149, 52172, 38634, 38610, 52176, {38638, 0, 0, 0}, 256, 256, { 1252, 20284, 10000, 850, 0, ',' }},
	{0x4809, 0x0009, 257, 97, 52179, 52185, 52185, 52205, 38587, 38572, 49346, {38591, 0, 0, 0}, 257, 257, { 1252, 37, 10000, 437, 0, ',' }},
	{0x480A, 0x000A, 257, 42, 52209, 52215, 52234, 52254, 38634, 38610, 52258, {38638, 0, 0, 0}, 258, 258, { 1252, 20284, 10000, 850, 0, ',' }},
	{0x4C0A, 0x000A, 257, 76, 52261, 52267, 52287, 52308, 38634, 38610, 52312, {38638, 0, 0, 0}, 259, 259, { 1252, 20284, 10000, 850, 0, ',' }},
	{0x500A, 0x000A, 257, 87, 52315, 52321, 52343, 52366, 38634, 38610, 52370, {38638, 0, 0, 0}, 260, 260, { 1252, 20284, 10000, 850, 0, ',' }},
	{0x540A, 0x000A, 257, 109, 52373, 52379, 52403, 52429, 38634, 38610, 43945, {38638, 0, 0, 0}, 261, 261, { 1252, 20284, 10000, 850, 0, ',' }},
	{0x641A, 0x781A, 257, -1, 52433, 52441, 52460, 50805, 50809, 50060, 0, {40528, 0, 0, 0}, 262, 262, { 1251, 870, 10082, 855, 0, ';' }},
	{0x681A, 0x781A, 257, -1, 52469, 52477, 52460, 50052, 50056, 50060, 0, {50063, 0, 0, 0}, 263, 263, { 1250, 870, 10082, 852, 0, ';' }},
	{0x6C1A, 0x7C1A, 257, -1, 52493, 52501, 52520, 51160, 2130, 50382, 0, {40528, 0, 0, 0}, 264, 264, { 1251, 21025, 10007, 855, 0, ';' }},
	{0x701A, 0x7C1A, 257, -1, 52533, 52541, 52520, 50984, 2130, 50382, 0, {50063, 0, 0, 0}, 265, 265, { 1250, 500, 10029, 852, 0, ';' }},
	{0x742C, 0x002C, 257, -1, 52557, 52565, 40400, 48620, 40416, 40385, 0, {40420, 0, 0, 0}, 266, 266, { 1251, 20880, 10007, 866, 0, ';' }},
	{0x7804, 0x007F, 257, -1, 38308, 38326, 38293, 38300, 38304, 38308, 0, {38311, 0, 0, 0}, 267, 267, { 936, 500, 10008, 936, 0, ',' }},
	{0x7814, 0x0014, 257, -1, 48468, 52588, 52606, 48460, 48464, 48468, 0, {38424, 0, 0, 0}, 268, 268, { 1252, 20277, 10000, 850, 0, ';' }},
	{0x781A, 0x007F, 257, -1, 50060, 52614, 52460, 50052, 50056, 50060, 0, {50063, 0, 0, 0}, 269, 269, { 1250, 870, 10082, 852, 0, ';' }},
	{0x782C, 0x002C, 257, -1, 52622, 52630, 40400, 40412, 40416, 40385, 0, {40420, 0, 0, 0}, 270, 270, { 1254, 20905, 10081, 857, 0, ';' }},
	{0x7843, 0x0043, 257, -1, 52650, 52658, 41346, 41357, 41361, 41337, 0, {48705, 0, 0, 0}, 271, 271, { 1251, 20880, 10007, 866, 0, ';' }},
	{0x7850, 0x0050, 257, -1, 52675, 52683, 42283, 52704, 42300, 42270, 0, {42304, 0, 0, 0}, 272, 272, { 1251, 20880, 10007, 866, 0, ';' }},
	{0x7C04, 0x7804, 257, -1, 52708, 43655, 38293, 43693, 38304, 38308, 0, {43700, 0, 0, 0}, 273, 273, { 950, 500, 10002, 950, 0, ',' }},
	{0x7C04, 0x7C04, 257, -1, 52716, 52723, 38293, 43693, 38304, 38308, 0, {43700, 0, 0, 0}, 274, 274, { 950, 500, 10002, 950, 0, ',' }},
	{0x7C14, 0x0014, 257, -1, 39139, 52752, 52770, 39131, 39135, 39139, 0, {38424, 0, 0, 0}, 275, 275, { 1252, 20277, 10000, 850, 0, ';' }},
	{0x7C1A, 0x007F, 257, -1, 50382, 52784, 52520, 52792, 2130, 50382, 0, {40528, 0, 0, 0}, 276, 276, { 1250, 500, 10029, 852, 0, ';' }},
	{0x7C28, 0x0028, 257, -1, 52796, 52804, 40197, 40210, 40214, 40188, 0, {0, 0, 0, 0}, 277, 277, { 1251, 20880, 10007, 866, 0, ';' }},
	{0x7C43, 0x0043, 257, -1, 52821, 52829, 41346, 41357, 41361, 41337, 0, {41365, 0, 0, 0}, 278, 278, { 1254, 500, 10029, 857, 0, ';' }},
	{0x7C5F, 0x005F, 257, -1, 52843, 52852, 42838, 42848, 42810, 42810, 0, {0, 0, 0, 0}, 279, 279, { 1252, 20297, 10000, 850, 0, ';' }},
	{0x7C68, 0x0068, 257, -1, 52884, 52892, 43006, 43012, 43016, 43003, 0, {0, 0, 0, 0}, 280, 280, { 1252, 37, 10000, 437, 0, ',' }}
};


static const CultureInfoNameEntry culture_name_entries [] = {
	{40670, 50},	 /* af */
	{52906, 144},	 /* af-za */
	{8022, 80},	 /* am */
	{52912, 168},	 /* am-et */
	{38068, 0},	 /* ar */
	{52918, 249},	 /* ar-ae */
	{52924, 251},	 /* ar-bh */
	{52930, 211},	 /* ar-dz */
	{52936, 197},	 /* ar-eg */
	{52942, 182},	 /* ar-iq */
	{52948, 238},	 /* ar-jo */
	{52954, 246},	 /* ar-kw */
	{52960, 242},	 /* ar-lb */
	{52966, 204},	 /* ar-ly */
	{52972, 218},	 /* ar-ma */
	{52978, 227},	 /* ar-om */
	{52984, 253},	 /* ar-qa */
	{52990, 96},	 /* ar-sa */
	{52996, 234},	 /* ar-sy */
	{53002, 223},	 /* ar-tn */
	{53008, 231},	 /* ar-ye */
	{42082, 70},	 /* as */
	{53014, 159},	 /* as-in */
	{40385, 44},	 /* az */
	{53020, 266},	 /* az-cyrl */
	{53028, 193},	 /* az-cyrl-az */
	{53039, 270},	 /* az-latn */
	{53047, 138},	 /* az-latn-az */
	{39897, 35},	 /* be */
	{53058, 129},	 /* be-by */
	{38129, 1},	 /* bg */
	{53064, 97},	 /* bg-bg */
	{41385, 62},	 /* bn */
	{53070, 196},	 /* bn-bd */
	{53076, 152},	 /* bn-in */
	{42336, 73},	 /* bo */
	{53082, 161},	 /* bo-cn */
	{43216, 91},	 /* br */
	{53088, 178},	 /* br-fr */
	{50060, 269},	 /* bs */
	{53094, 262},	 /* bs-cyrl */
	{53102, 230},	 /* bs-cyrl-ba */
	{53113, 263},	 /* bs-latn */
	{53121, 217},	 /* bs-latn-ba */
	{38211, 2},	 /* ca */
	{53132, 98},	 /* ca-es */
	{38347, 5},	 /* cs */
	{53138, 100},	 /* cs-cz */
	{42380, 74},	 /* cy */
	{53144, 162},	 /* cy-gb */
	{38400, 6},	 /* da */
	{53150, 101},	 /* da-dk */
	{38445, 7},	 /* de */
	{53156, 199},	 /* de-at */
	{53162, 184},	 /* de-ch */
	{53168, 102},	 /* de-de */
	{53174, 213},	 /* de-li */
	{53180, 206},	 /* de-lu */
	{38496, 8},	 /* el */
	{53186, 103},	 /* el-gr */
	{38572, 9},	 /* en */
	{53192, 200},	 /* en-au */
	{53198, 235},	 /* en-bz */
	{53204, 207},	 /* en-ca */
	{53210, 185},	 /* en-gb */
	{53216, 219},	 /* en-ie */
	{53222, 254},	 /* en-in */
	{53228, 228},	 /* en-jm */
	{53234, 214},	 /* en-nz */
	{53240, 247},	 /* en-ph */
	{53246, 257},	 /* en-sg */
	{53252, 239},	 /* en-tt */
	{53258, 104},	 /* en-us */
	{53264, 224},	 /* en-za */
	{53270, 243},	 /* en-zw */
	{38610, 10},	 /* es */
	{53276, 240},	 /* es-ar */
	{53282, 255},	 /* es-bo */
	{53288, 248},	 /* es-cl */
	{53294, 232},	 /* es-co */
	{53300, 215},	 /* es-cr */
	{53306, 225},	 /* es-do */
	{53312, 244},	 /* es-ec */
	{53318, 201},	 /* es-es */
	{53324, 208},	 /* es-gt */
	{53330, 258},	 /* es-hn */
	{53336, 186},	 /* es-mx */
	{53342, 259},	 /* es-ni */
	{53348, 220},	 /* es-pa */
	{53354, 236},	 /* es-pe */
	{53360, 260},	 /* es-pr */
	{53366, 252},	 /* es-py */
	{53372, 256},	 /* es-sv */
	{53378, 261},	 /* es-us */
	{53384, 250},	 /* es-uy */
	{53390, 229},	 /* es-ve */
	{40039, 37},	 /* et */
	{53396, 131},	 /* et-ee */
	{40439, 45},	 /* eu */
	{53402, 139},	 /* eu-es */
	{40218, 41},	 /* fa */
	{53408, 135},	 /* fa-ir */
	{38660, 11},	 /* fi */
	{53414, 105},	 /* fi-fi */
	{42963, 84},	 /* fil */
	{53420, 171},	 /* fil-ph */
	{40818, 52},	 /* fo */
	{53427, 146},	 /* fo-fo */
	{38710, 12},	 /* fr */
	{53433, 187},	 /* fr-be */
	{53439, 202},	 /* fr-ca */
	{53445, 209},	 /* fr-ch */
	{53451, 106},	 /* fr-fr */
	{53457, 216},	 /* fr-lu */
	{53463, 221},	 /* fr-mc */
	{41040, 56},	 /* ga */
	{53469, 194},	 /* ga-ie */
	{43384, 95},	 /* gd */
	{53475, 181},	 /* gd-gb */
	{42580, 77},	 /* gl */
	{53481, 165},	 /* gl-es */
	{43264, 92},	 /* gsw */
	{41570, 64},	 /* gu */
	{53487, 153},	 /* gu-in */
	{43003, 85},	 /* ha */
	{53493, 280},	 /* ha-latn */
	{53501, 172},	 /* ha-latn-ng */
	{38760, 13},	 /* he */
	{53512, 107},	 /* he-il */
	{40847, 53},	 /* hi */
	{53518, 147},	 /* hi-in */
	{39424, 26},	 /* hr */
	{53524, 210},	 /* hr-ba */
	{53530, 120},	 /* hr-hr */
	{38822, 14},	 /* hu */
	{53536, 108},	 /* hu-hu */
	{40320, 43},	 /* hy */
	{53542, 137},	 /* hy-am */
	{39751, 33},	 /* id */
	{53548, 127},	 /* id-id */
	{43155, 89},	 /* ig */
	{53554, 176},	 /* ig-ng */
	{43171, 90},	 /* ii */
	{53560, 177},	 /* ii-cn */
	{38866, 15},	 /* is */
	{53566, 109},	 /* is-is */
	{38917, 16},	 /* it */
	{53572, 188},	 /* it-ch */
	{53578, 110},	 /* it-it */
	{38967, 17},	 /* ja */
	{53584, 111},	 /* ja-jp */
	{40711, 51},	 /* ka */
	{53590, 145},	 /* ka-ge */
	{41134, 58},	 /* kk */
	{43093, 88},	 /* kl */
	{53596, 175},	 /* kl-gl */
	{42421, 75},	 /* km */
	{53602, 163},	 /* km-kh */
	{37319, 68},	 /* kn */
	{53608, 157},	 /* kn-in */
	{39024, 18},	 /* ko */
	{53614, 112},	 /* ko-kr */
	{42607, 78},	 /* kok */
	{53620, 166},	 /* kok-in */
	{41214, 59},	 /* ky */
	{42509, 76},	 /* lo */
	{53627, 164},	 /* lo-la */
	{40133, 39},	 /* lt */
	{53633, 133},	 /* lt-lt */
	{40085, 38},	 /* lv */
	{53639, 132},	 /* lv-lv */
	{40485, 46},	 /* mk */
	{53645, 140},	 /* mk-mk */
	{41998, 69},	 /* ml */
	{53651, 158},	 /* ml-in */
	{42270, 72},	 /* mn */
	{53657, 272},	 /* mn-cyrl */
	{42177, 71},	 /* mr */
	{53665, 160},	 /* mr-in */
	{41086, 57},	 /* ms */
	{40933, 54},	 /* mt */
	{53671, 148},	 /* mt-mt */
	{39139, 275},	 /* nb */
	{53677, 114},	 /* nb-no */
	{1883, 82},	 /* ne */
	{53683, 169},	 /* ne-np */
	{39062, 19},	 /* nl */
	{53689, 189},	 /* nl-be */
	{53695, 113},	 /* nl-nl */
	{48468, 268},	 /* nn */
	{53701, 190},	 /* nn-no */
	{39112, 20},	 /* no */
	{43053, 87},	 /* nso */
	{53707, 174},	 /* nso-za */
	{41668, 65},	 /* or */
	{53714, 154},	 /* or-in */
	{41485, 63},	 /* pa */
	{39142, 21},	 /* pl */
	{53720, 115},	 /* pl-pl */
	{42936, 83},	 /* ps */
	{53726, 170},	 /* ps-af */
	{39191, 22},	 /* pt */
	{53732, 116},	 /* pt-br */
	{53738, 191},	 /* pt-pt */
	{39247, 23},	 /* rm */
	{53744, 117},	 /* rm-ch */
	{39296, 24},	 /* ro */
	{53750, 118},	 /* ro-ro */
	{39344, 25},	 /* ru */
	{53756, 119},	 /* ru-ru */
	{43361, 94},	 /* rw */
	{53762, 180},	 /* rw-rw */
	{43329, 93},	 /* sah */
	{53768, 179},	 /* sah-ru */
	{40979, 55},	 /* se */
	{53775, 203},	 /* se-fi */
	{53781, 149},	 /* se-no */
	{42642, 79},	 /* si */
	{53787, 167},	 /* si-lk */
	{12674, 27},	 /* sk */
	{53793, 121},	 /* sk-sk */
	{39982, 36},	 /* sl */
	{53799, 130},	 /* sl-si */
	{39527, 28},	 /* sq */
	{53805, 122},	 /* sq-al */
	{50382, 276},	 /* sr */
	{53811, 264},	 /* sr-cyrl */
	{53819, 226},	 /* sr-cyrl-ba */
	{53830, 245},	 /* sr-cyrl-me */
	{53841, 237},	 /* sr-cyrl-rs */
	{53852, 265},	 /* sr-latn */
	{53860, 222},	 /* sr-latn-ba */
	{53871, 241},	 /* sr-latn-me */
	{53882, 233},	 /* sr-latn-rs */
	{39573, 29},	 /* sv */
	{53893, 192},	 /* sv-fi */
	{53899, 123},	 /* sv-se */
	{41287, 60},	 /* sw */
	{53905, 150},	 /* sw-ke */
	{41701, 66},	 /* ta */
	{53911, 155},	 /* ta-in */
	{41799, 67},	 /* te */
	{53917, 156},	 /* te-in */
	{40188, 40},	 /* tg */
	{53923, 277},	 /* tg-cyrl */
	{53931, 134},	 /* tg-cyrl-tj */
	{39600, 30},	 /* th */
	{53942, 124},	 /* th-th */
	{40572, 47},	 /* tn */
	{53948, 141},	 /* tn-za */
	{12683, 31},	 /* tr */
	{53954, 125},	 /* tr-tr */
	{42810, 81},	 /* tzm */
	{53960, 279},	 /* tzm-latn */
	{39809, 34},	 /* uk */
	{53969, 128},	 /* uk-ua */
	{39696, 32},	 /* ur */
	{53975, 126},	 /* ur-pk */
	{41337, 61},	 /* uz */
	{53981, 271},	 /* uz-cyrl */
	{53989, 195},	 /* uz-cyrl-uz */
	{54000, 278},	 /* uz-latn */
	{54008, 151},	 /* uz-latn-uz */
	{40272, 42},	 /* vi */
	{54019, 136},	 /* vi-vn */
	{40599, 48},	 /* xh */
	{54025, 142},	 /* xh-za */
	{43020, 86},	 /* yo */
	{54031, 173},	 /* yo-ng */
	{38308, 267},	 /* zh */
	{54037, 3},	 /* zh-chs */
	{54044, 274},	 /* zh-cht */
	{54051, 183},	 /* zh-cn */
	{54057, 4},	 /* zh-hans */
	{54065, 273},	 /* zh-hant */
	{54073, 198},	 /* zh-hk */
	{54079, 212},	 /* zh-mo */
	{54085, 205},	 /* zh-sg */
	{54091, 99},	 /* zh-tw */
	{40625, 49},	 /* zu */
	{54097, 143}	 /* zu-za */
};


static const RegionInfoEntry region_entries [] = {
	{ 224,51826,48865,48865,54103,54124,38011,54171,54175,54203},
	{ 3,47503,54227,54227,54231,54243,37782,54262,54266,54281},
	{ 6,44819,54294,54294,54298,54306,37322,54316,54320,54333},
	{ 7,1820,50139,50139,54347,54355,37563,54372,54376,54390},
	{ 11,51317,49670,49670,54416,54416,37073,51040,54426,54441},
	{ 14,49014,54456,54456,54460,54468,36969,54480,54484,54484},
	{ 12,49047,54489,54489,54493,54493,37073,54503,54507,54507},
	{ 5,45717,40412,40412,54525,54536,37817,54548,54552,54570},
	{ 25,49612,54590,54590,54594,54617,37879,54637,54641,54677},
	{ 23,48817,54697,54697,54701,54712,37855,54737,54741,54758},
	{ 21,48309,39932,39932,54799,54807,36969,54480,54484,54816},
	{ 35,43603,38161,38161,54821,54830,36951,54847,54851,54865},
	{ 17,51938,54891,54891,54895,54903,38021,54918,54922,54937},
	{ 26,52118,54961,54961,54965,54965,38045,42372,54973,54992},
	{ 32,44523,55002,55002,55006,55013,37210,55020,55024,55039},
	{ 29,45205,55055,55055,55059,55067,37425,55084,55088,55105},
	{ 24,51074,55137,55137,55141,55141,37073,55148,55152,55152},
	{ 39,49140,55166,55166,55170,55170,37073,55177,55181,55197},
	{ 223,13632,55213,55213,55217,55229,37217,37217,55236,55248},
	{ 46,51719,55262,55262,55266,55266,37073,55272,55276,55289},
	{ 45,13711,55302,55302,55306,55312,36990,55331,55335,55348},
	{ 51,50918,55367,55367,55371,55371,37073,55380,55384,55399},
	{ 54,49914,55415,55415,55419,55419,37897,55430,55434,55453},
	{ 75,43766,55474,55474,55478,55493,36993,55511,55515,55537},
	{ 94,43856,38463,38463,55552,55560,36969,54480,54484,54484},
	{ 61,43808,55572,55572,55576,55584,37021,55592,55596,55609},
	{ 65,50546,55621,55621,55625,55644,37073,55666,55670,55685},
	{ 4,49674,55701,55701,55705,55713,37882,55728,55732,55747},
	{ 66,51536,55771,55771,55775,55775,37073,55783,55787,55797},
	{ 70,45303,52429,52429,55819,55827,36969,54480,54484,54816},
	{ 67,48869,55833,55833,55837,55843,37859,55850,55854,55869},
	{ 217,43646,38630,38630,55887,55893,36969,54480,54484,54816},
	{ 73,47382,55901,55901,55905,55914,37762,55930,55934,55949},
	{ 77,43986,38677,38677,55975,55983,36969,54480,54484,54816},
	{ 81,46134,55989,55989,55993,56007,37021,55592,55596,56016},
	{ 84,44030,38730,38730,56029,56029,36969,54480,54484,54816},
	{ 242,47039,56036,56036,56040,56055,37706,56072,56076,56099},
	{ 88,46080,56121,56121,56125,56133,0,56164,56168,56182},
	{ 93,47781,56217,56217,56221,56231,37021,55592,55596,56248},
	{ 98,43912,56268,56268,56272,56279,36969,54480,54484,56292},
	{ 99,49480,56301,56301,56305,56305,7920,56315,56319,56338},
	{ 104,48962,56359,56359,56363,56383,37073,56411,56415,56432},
	{ 106,52258,56439,56439,56443,56443,2308,56452,56456,56473},
	{ 108,44722,39445,39445,56492,56500,37319,56509,56513,56527},
	{ 109,44131,38842,38842,56541,56549,37128,56563,56567,56584},
	{ 111,45068,56598,56598,56602,56602,37401,56612,56616,56634},
	{ 68,48662,56651,56651,56655,56663,36969,54480,54484,54484},
	{ 117,44079,56669,56669,56673,56680,37119,56691,56695,56714},
	{ 113,46188,39782,39782,56721,56727,37642,56740,56744,56757},
	{ 121,48071,56792,56792,56796,56801,37828,56814,56818,56830},
	{ 116,45528,56852,56852,56856,56861,37517,56872,56876,56889},
	{ 110,44180,38889,38889,56909,56917,37021,56925,56929,56946},
	{ 118,44223,38937,38937,56962,56968,36969,54480,54484,54484},
	{ 124,50694,56975,56975,56979,56979,37073,56987,56991,56991},
	{ 126,51220,57007,57007,57011,57018,37981,57031,57035,57051},
	{ 122,44268,38989,38989,57073,57079,37141,57086,57090,57103},
	{ 129,46324,57113,57113,57117,57117,37666,57123,57127,57143},
	{ 40,47105,42446,42446,57161,57170,37709,57192,57196,57211},
	{ 134,44323,39044,39044,57243,57255,37145,57268,57272,57289},
	{ 136,51641,57306,57306,57310,57317,38001,57330,57334,57348},
	{ 138,47147,42526,42526,57370,42516,37713,57375,57379,57391},
	{ 139,51454,57411,57411,57415,57423,37991,57434,57438,57453},
	{ 145,49823,57475,57475,57479,57479,0,37217,55236,57493},
	{ 42,47323,57511,57511,57515,57525,37754,57557,57561,57578},
	{ 141,45401,57626,57626,57630,57640,37493,57648,57652,57669},
	{ 147,49399,57684,57684,57688,57699,36969,54480,54484,54484},
	{ 140,45349,57709,57709,57713,57720,36969,54480,54484,57728},
	{ 148,49282,57733,57733,57737,57743,37869,57754,57758,57771},
	{ 159,50143,42204,42204,57791,57799,37901,57812,57816,57832},
	{ 158,50268,57852,57852,57856,57856,36969,54480,54484,54816},
	{ 270,51396,57863,57863,57867,57878,36969,54480,54484,57896},
	{ 19618,45833,57901,57901,57905,57915,37588,57901,57936,57953},
	{ 151,49763,57985,57985,57989,58005,37892,58033,58037,58053},
	{ 163,46227,40950,40950,58063,58063,36969,54480,54484,58069},
	{ 166,48258,58074,58074,58078,58085,37073,58093,58097,58110},
	{ 175,47611,58124,58124,58128,58136,37789,58145,58149,58164},
	{ 182,52312,58170,58170,58174,58174,38048,58184,58188,58208},
	{ 176,44375,39082,39082,58231,58243,36969,54480,54484,54484},
	{ 177,44433,39131,39131,58253,58260,37021,58266,58270,58286},
	{ 178,47443,58300,58300,58304,58310,37769,58326,58330,58345},
	{ 183,49858,58386,58386,58390,58390,37073,58402,58406,58406},
	{ 164,50663,58425,58425,58429,58434,37930,58445,58449,58460},
	{ 192,50220,41515,41515,58480,58487,37911,58495,58499,58517},
	{ 187,51119,58534,58534,58538,58543,37969,58549,58553,58572},
	{ 201,47557,58590,58590,58594,58606,37785,58616,58620,58620},
	{ 190,45007,58636,58636,58640,58649,37343,58664,58668,58684},
	{ 191,44474,58712,58712,58716,58723,37168,58730,58734,58747},
	{ 202,52370,58761,58761,58765,58765,37073,55783,55787,55797},
	{ 193,48525,58777,58777,58781,58781,36969,54480,54484,54484},
	{ 185,51990,58790,58790,58794,58794,38031,58803,58807,58826},
	{ 197,52042,58845,58845,58849,58855,38035,58862,58866,58878},
	{ 200,44618,58896,58896,58900,58908,0,58917,58921,58934},
	{ 271,50988,52792,52792,58948,58955,37954,58968,58972,58986},
	{ 203,44674,39370,39370,58999,59006,37239,59019,59023,59037},
	{ 204,47939,59069,59069,59073,59073,37793,59080,59084,0},
	{ 205,13629,59098,59098,59102,59115,36893,59162,59166,59178},
	{ 221,44863,59198,59198,59202,59209,37021,59217,59221,59235},
	{ 215,49346,59248,59248,59252,59262,37073,59272,59276,59293},
	{ 212,45261,59306,59306,59310,59319,36969,54480,54484,59329},
	{ 143,44773,59334,59334,59338,59347,36969,54480,54484,54484},
	{ 72,52176,40009,40009,59357,59357,37073,55783,55787,55797},
	{ 222,51044,59369,59369,59373,59379,37959,59390,59394,59407},
	{ 227,44910,39618,39618,59427,39608,37335,59436,59440,59450},
	{ 228,45480,59469,59469,59473,59484,37810,59505,59509,59528},
	{ 234,50438,59541,59541,59545,59553,37915,59562,59566,59581},
	{ 235,44956,59602,59602,59606,59613,37339,59622,59626,59639},
	{ 225,51263,59653,59653,59657,59657,37073,59677,59681,59681},
	{ 237,43697,59708,59708,59712,59719,37796,59726,59730,59748},
	{ 241,45135,39843,39843,59758,59766,37404,59781,59785,59803},
	{ 244,43945,59837,59837,59841,59841,37073,55783,55787,55787},
	{ 246,51876,59855,59855,59859,59859,37073,59867,59871,59886},
	{ 247,46390,41357,41357,59900,59911,37822,59924,59928,59943},
	{ 249,50748,59962,59962,59966,59966,37940,59976,59980,60000},
	{ 251,45586,60020,60020,60024,60032,37559,60043,60047,60063},
	{ 261,50866,60082,60082,60086,60092,37944,60103,60107,60119},
	{ 209,45864,60137,60137,60141,0,10586,60154,60158,0},
	{ 264,51486,60177,60177,60181,60181,37073,55783,55787,55787}
};


static const RegionInfoNameEntry region_name_entries [] = {
	{51826, 0},	 /* AE */
	{47503, 1},	 /* AF */
	{44819, 2},	 /* AL */
	{1820, 3},	 /* AM */
	{51317, 4},	 /* AR */
	{49014, 5},	 /* AT */
	{49047, 6},	 /* AU */
	{45717, 7},	 /* AZ */
	{49612, 8},	 /* BA */
	{48817, 9},	 /* BD */
	{48309, 10},	 /* BE */
	{43603, 11},	 /* BG */
	{51938, 12},	 /* BH */
	{52118, 13},	 /* BO */
	{44523, 14},	 /* BR */
	{45205, 15},	 /* BY */
	{51074, 16},	 /* BZ */
	{49140, 17},	 /* CA */
	{13632, 18},	 /* CH */
	{51719, 19},	 /* CL */
	{13711, 20},	 /* CN */
	{50918, 21},	 /* CO */
	{49914, 22},	 /* CR */
	{43766, 23},	 /* CZ */
	{43856, 24},	 /* DE */
	{43808, 25},	 /* DK */
	{50546, 26},	 /* DO */
	{49674, 27},	 /* DZ */
	{51536, 28},	 /* EC */
	{45303, 29},	 /* EE */
	{48869, 30},	 /* EG */
	{43646, 31},	 /* ES */
	{47382, 32},	 /* ET */
	{43986, 33},	 /* FI */
	{46134, 34},	 /* FO */
	{44030, 35},	 /* FR */
	{47039, 36},	 /* GB */
	{46080, 37},	 /* GE */
	{47781, 38},	 /* GL */
	{43912, 39},	 /* GR */
	{49480, 40},	 /* GT */
	{48962, 41},	 /* HK */
	{52258, 42},	 /* HN */
	{44722, 43},	 /* HR */
	{44131, 44},	 /* HU */
	{45068, 45},	 /* ID */
	{48662, 46},	 /* IE */
	{44079, 47},	 /* IL */
	{46188, 48},	 /* IN */
	{48071, 49},	 /* IQ */
	{45528, 50},	 /* IR */
	{44180, 51},	 /* IS */
	{44223, 52},	 /* IT */
	{50694, 53},	 /* JM */
	{51220, 54},	 /* JO */
	{44268, 55},	 /* JP */
	{46324, 56},	 /* KE */
	{47105, 57},	 /* KH */
	{44323, 58},	 /* KR */
	{51641, 59},	 /* KW */
	{47147, 60},	 /* LA */
	{51454, 61},	 /* LB */
	{49823, 62},	 /* LI */
	{47323, 63},	 /* LK */
	{45401, 64},	 /* LT */
	{49399, 65},	 /* LU */
	{45349, 66},	 /* LV */
	{49282, 67},	 /* LY */
	{50143, 68},	 /* MA */
	{50268, 69},	 /* MC */
	{51396, 70},	 /* ME */
	{45833, 71},	 /* MK */
	{49763, 72},	 /* MO */
	{46227, 73},	 /* MT */
	{48258, 74},	 /* MX */
	{47611, 75},	 /* NG */
	{52312, 76},	 /* NI */
	{44375, 77},	 /* NL */
	{44433, 78},	 /* NO */
	{47443, 79},	 /* NP */
	{49858, 80},	 /* NZ */
	{50663, 81},	 /* OM */
	{50220, 82},	 /* PA */
	{51119, 83},	 /* PE */
	{47557, 84},	 /* PH */
	{45007, 85},	 /* PK */
	{44474, 86},	 /* PL */
	{52370, 87},	 /* PR */
	{48525, 88},	 /* PT */
	{51990, 89},	 /* PY */
	{52042, 90},	 /* QA */
	{44618, 91},	 /* RO */
	{50988, 92},	 /* RS */
	{44674, 93},	 /* RU */
	{47939, 94},	 /* RW */
	{13629, 95},	 /* SA */
	{44863, 96},	 /* SE */
	{49346, 97},	 /* SG */
	{45261, 98},	 /* SI */
	{44773, 99},	 /* SK */
	{52176, 100},	 /* SV */
	{51044, 101},	 /* SY */
	{44910, 102},	 /* TH */
	{45480, 103},	 /* TJ */
	{50438, 104},	 /* TN */
	{44956, 105},	 /* TR */
	{51263, 106},	 /* TT */
	{43697, 107},	 /* TW */
	{45135, 108},	 /* UA */
	{43945, 109},	 /* US */
	{51876, 110},	 /* UY */
	{46390, 111},	 /* UZ */
	{50748, 112},	 /* VE */
	{45586, 113},	 /* VN */
	{50866, 114},	 /* YE */
	{45864, 115},	 /* ZA */
	{51486, 116}	 /* ZW */
};


static const char locale_strings [] = {
"\0"
	"dd MMMM\0"
	"\xd9\x85\xd8\xad\xd8\xb1\xd9\x85\0"
	"\xd8\xb5\xd9\x81\xd8\xb1\0"
	"\xd8\xb1\xd8\xa8\xd9\x8a\xd8\xb9 \xd8\xa7\xd9\x84\xd8\xa3\xd9\x88\xd9\x84\0"
	"\xd8\xb1\xd8\xa8\xd9\x8a\xd8\xb9 \xd8\xa7\xd9\x84\xd8\xa2\xd8\xae\xd8\xb1\0"
	"\xd8\xac\xd9\x85\xd8\xa7\xd8\xaf\xd9\x89 \xd8\xa7\xd9\x84\xd8\xa3\xd9\x88\xd9\x84\xd9\x89\0"
	"\xd8\xac\xd9\x85\xd8\xa7\xd8\xaf\xd9\x89 \xd8\xa7\xd9\x84\xd8\xa2\xd8\xae\xd8\xb1\xd8\xa9\0"
	"\xd8\xb1\xd8\xac\xd8\xa8\0"
	"\xd8\xb4\xd8\xb9\xd8\xa8\xd8\xa7\xd9\x86\0"
	"\xd8\xb1\xd9\x85\xd8\xb6\xd8\xa7\xd9\x86\0"
	"\xd8\xb4\xd9\x88\xd8\xa7\xd9\x84\0"
	"\xd8\xb0\xd9\x88 \xd8\xa7\xd9\x84\xd9\x82\xd8\xb9\xd8\xaf\xd8\xa9\0"
	"\xd8\xb0\xd9\x88 \xd8\xa7\xd9\x84\xd8\xad\xd8\xac\xd8\xa9\0"
	"/\0"
	":\0"
	"dd/MM/yy\0"
	"dd/MM/yyyy\0"
	"dd/MMMM/yyyy\0"
	"dddd, dd MMMM, yyyy\0"
	"hh:mm tt\0"
	"HH:mm\0"
	"hh:mm:ss tt\0"
	"HH:mm:ss\0"
	"MMMM, yyyy\0"
	"d MMMM\0"
	"\xd0\xbf\xd1\x80.\xd0\xbe\xd0\xb1.\0"
	"\xd1\x81\xd0\xbb.\xd0\xbe\xd0\xb1.\0"
	"\xd0\xbd\xd0\xb5\xd0\xb4\xd0\xb5\xd0\xbb\xd1\x8f\0"
	"\xd0\xbf\xd0\xbe\xd0\xbd\xd0\xb5\xd0\xb4\xd0\xb5\xd0\xbb\xd0\xbd\xd0\xb8\xd0\xba\0"
	"\xd0\xb2\xd1\x82\xd0\xbe\xd1\x80\xd0\xbd\xd0\xb8\xd0\xba\0"
	"\xd1\x81\xd1\x80\xd1\x8f\xd0\xb4\xd0\xb0\0"
	"\xd1\x87\xd0\xb5\xd1\x82\xd0\xb2\xd1\x8a\xd1\x80\xd1\x82\xd1\x8a\xd0\xba\0"
	"\xd0\xbf\xd0\xb5\xd1\x82\xd1\x8a\xd0\xba\0"
	"\xd1\x81\xd1\x8a\xd0\xb1\xd0\xbe\xd1\x82\xd0\xb0\0"
	"\xd0\xbd\xd0\xb4\0"
	"\xd0\xbf\xd0\xbd\0"
	"\xd0\xb2\xd1\x82\0"
	"\xd1\x81\xd1\x80\0"
	"\xd1\x87\xd1\x82\0"
	"\xd0\xbf\xd1\x82\0"
	"\xd1\x81\xd0\xb1\0"
	"\xd0\xbd\0"
	"\xd0\xbf\0"
	"\xd0\xb2\0"
	"\xd1\x81\0"
	"\xd1\x87\0"
	"\xd1\x8f\xd0\xbd\xd1\x83\xd0\xb0\xd1\x80\xd0\xb8\0"
	"\xd1\x84\xd0\xb5\xd0\xb2\xd1\x80\xd1\x83\xd0\xb0\xd1\x80\xd0\xb8\0"
	"\xd0\xbc\xd0\xb0\xd1\x80\xd1\x82\0"
	"\xd0\xb0\xd0\xbf\xd1\x80\xd0\xb8\xd0\xbb\0"
	"\xd0\xbc\xd0\xb0\xd0\xb9\0"
	"\xd1\x8e\xd0\xbd\xd0\xb8\0"
	"\xd1\x8e\xd0\xbb\xd0\xb8\0"
	"\xd0\xb0\xd0\xb2\xd0\xb3\xd1\x83\xd1\x81\xd1\x82\0"
	"\xd1\x81\xd0\xb5\xd0\xbf\xd1\x82\xd0\xb5\xd0\xbc\xd0\xb2\xd1\x80\xd0\xb8\0"
	"\xd0\xbe\xd0\xba\xd1\x82\xd0\xbe\xd0\xbc\xd0\xb2\xd1\x80\xd0\xb8\0"
	"\xd0\xbd\xd0\xbe\xd0\xb5\xd0\xbc\xd0\xb2\xd1\x80\xd0\xb8\0"
	"\xd0\xb4\xd0\xb5\xd0\xba\xd0\xb5\xd0\xbc\xd0\xb2\xd1\x80\xd0\xb8\0"
	"\xd1\x8f\xd0\xbd.\0"
	"\xd1\x84\xd0\xb5\xd0\xb2\xd1\x80.\0"
	"\xd0\xb0\xd0\xbf\xd1\x80.\0"
	"\xd0\xb0\xd0\xb2\xd0\xb3.\0"
	"\xd1\x81\xd0\xb5\xd0\xbf\xd1\x82.\0"
	"\xd0\xbe\xd0\xba\xd1\x82.\0"
	"\xd0\xbd\xd0\xbe\xd0\xb5\xd0\xbc.\0"
	"\xd0\xb4\xd0\xb5\xd0\xba.\0"
	".\0"
	"d.M.yyyy '\xd0\xb3.'\0"
	"dd.M.yyyy '\xd0\xb3.'\0"
	"d.MM.yyyy '\xd0\xb3.'\0"
	"dd.MM.yyyy '\xd0\xb3.'\0"
	"dd MMMM yyyy '\xd0\xb3.'\0"
	"d MMMM yyyy '\xd0\xb3.'\0"
	"dddd, dd MMMM yyyy '\xd0\xb3.'\0"
	"dddd, d MMMM yyyy '\xd0\xb3.'\0"
	"H:mm\0"
	"H:mm:ss\0"
	"MMMM yyyy '\xd0\xb3.'\0"
	"a. m.\0"
	"p. m.\0"
	"diumenge\0"
	"dilluns\0"
	"dimarts\0"
	"dimecres\0"
	"dijous\0"
	"divendres\0"
	"dissabte\0"
	"dg.\0"
	"dl.\0"
	"dt.\0"
	"dc.\0"
	"dj.\0"
	"dv.\0"
	"ds.\0"
	"dg\0"
	"dl\0"
	"dt\0"
	"dc\0"
	"dj\0"
	"dv\0"
	"ds\0"
	"gener\0"
	"febrer\0"
	"mar\xc3\xa7\0"
	"abril\0"
	"maig\0"
	"juny\0"
	"juliol\0"
	"agost\0"
	"setembre\0"
	"octubre\0"
	"novembre\0"
	"desembre\0"
	"gen.\0"
	"feb.\0"
	"abr.\0"
	"jul.\0"
	"ag.\0"
	"set.\0"
	"oct.\0"
	"nov.\0"
	"des.\0"
	"d/M/yyyy\0"
	"d MMM yyyy\0"
	"dddd, d MMMM 'de' yyyy\0"
	"d MMMM 'de' yyyy\0"
	"MMMM 'de' yyyy\0"
	"M\xe6\x9c\x88\x64\xe6\x97\xa5\0"
	"\xe4\xb8\x8a\xe5\x8d\x88\0"
	"\xe4\xb8\x8b\xe5\x8d\x88\0"
	"\xe6\x98\x9f\xe6\x9c\x9f\xe6\x97\xa5\0"
	"\xe6\x98\x9f\xe6\x9c\x9f\xe4\xb8\x80\0"
	"\xe6\x98\x9f\xe6\x9c\x9f\xe4\xba\x8c\0"
	"\xe6\x98\x9f\xe6\x9c\x9f\xe4\xb8\x89\0"
	"\xe6\x98\x9f\xe6\x9c\x9f\xe5\x9b\x9b\0"
	"\xe6\x98\x9f\xe6\x9c\x9f\xe4\xba\x94\0"
	"\xe6\x98\x9f\xe6\x9c\x9f\xe5\x85\xad\0"
	"\xe5\x91\xa8\xe6\x97\xa5\0"
	"\xe5\x91\xa8\xe4\xb8\x80\0"
	"\xe5\x91\xa8\xe4\xba\x8c\0"
	"\xe5\x91\xa8\xe4\xb8\x89\0"
	"\xe5\x91\xa8\xe5\x9b\x9b\0"
	"\xe5\x91\xa8\xe4\xba\x94\0"
	"\xe5\x91\xa8\xe5\x85\xad\0"
	"\xe6\x97\xa5\0"
	"\xe4\xb8\x80\0"
	"\xe4\xba\x8c\0"
	"\xe4\xb8\x89\0"
	"\xe5\x9b\x9b\0"
	"\xe4\xba\x94\0"
	"\xe5\x85\xad\0"
	"\xe4\xb8\x80\xe6\x9c\x88\0"
	"\xe4\xba\x8c\xe6\x9c\x88\0"
	"\xe4\xb8\x89\xe6\x9c\x88\0"
	"\xe5\x9b\x9b\xe6\x9c\x88\0"
	"\xe4\xba\x94\xe6\x9c\x88\0"
	"\xe5\x85\xad\xe6\x9c\x88\0"
	"\xe4\xb8\x83\xe6\x9c\x88\0"
	"\xe5\x85\xab\xe6\x9c\x88\0"
	"\xe4\xb9\x9d\xe6\x9c\x88\0"
	"\xe5\x8d\x81\xe6\x9c\x88\0"
	"\xe5\x8d\x81\xe4\xb8\x80\xe6\x9c\x88\0"
	"\xe5\x8d\x81\xe4\xba\x8c\xe6\x9c\x88\0"
	"1\xe6\x9c\x88\0"
	"2\xe6\x9c\x88\0"
	"3\xe6\x9c\x88\0"
	"4\xe6\x9c\x88\0"
	"5\xe6\x9c\x88\0"
	"6\xe6\x9c\x88\0"
	"7\xe6\x9c\x88\0"
	"8\xe6\x9c\x88\0"
	"9\xe6\x9c\x88\0"
	"10\xe6\x9c\x88\0"
	"11\xe6\x9c\x88\0"
	"12\xe6\x9c\x88\0"
	"yyyy/M/d\0"
	"yyyy-M-d\0"
	"yyyy.M.d\0"
	"yyyy/MM/dd\0"
	"yyyy-MM-dd\0"
	"yyyy.MM.dd\0"
	"yy/M/d\0"
	"yy-M-d\0"
	"yy.M.d\0"
	"yy/MM/dd\0"
	"yyyy'\xe5\xb9\xb4'M'\xe6\x9c\x88'd'\xe6\x97\xa5'\0"
	"yyyy'\xe5\xb9\xb4'M'\xe6\x9c\x88'd'\xe6\x97\xa5', dddd\0"
	"dddd, yyyy'\xe5\xb9\xb4'M'\xe6\x9c\x88'd'\xe6\x97\xa5'\0"
	"yyyy\xe5\xb9\xb4MMMd\xe6\x97\xa5\0"
	"yyyy\xe5\xb9\xb4MMMd\xe6\x97\xa5, dddd\0"
	"tt h:mm\0"
	"tt hh:mm\0"
	"tt h:mm:ss\0"
	"tt hh:mm:ss\0"
	"yyyy'\xe5\xb9\xb4'M'\xe6\x9c\x88'\0"
	"yyyy'\xe5\xb9\xb4'MMM\0"
	"yyyy'\xe5\xb9\xb4'MMMM\0"
	"yyyy.M\0"
	"d. MMMM\0"
	"AM\0"
	"PM\0"
	"ned\xc4\x9ble\0"
	"pond\xc4\x9bl\xc3\xad\0"
	"\xc3\xbater\xc3\xbd\0"
	"st\xc5\x99\x65\x64\x61\0"
	"\xc4\x8dtvrtek\0"
	"p\xc3\xa1tek\0"
	"sobota\0"
	"ne\0"
	"po\0"
	"\xc3\xbat\0"
	"st\0"
	"\xc4\x8dt\0"
	"p\xc3\xa1\0"
	"so\0"
	"N\0"
	"P\0"
	"\xc3\x9a\0"
	"S\0"
	"\xc4\x8c\0"
	"leden\0"
	"\xc3\xbanor\0"
	"b\xc5\x99\x65zen\0"
	"duben\0"
	"kv\xc4\x9bten\0"
	"\xc4\x8d\x65rven\0"
	"\xc4\x8d\x65rvenec\0"
	"srpen\0"
	"z\xc3\xa1\xc5\x99\xc3\xad\0"
	"\xc5\x99\xc3\xadjen\0"
	"listopad\0"
	"prosinec\0"
	"ledna\0"
	"\xc3\xbanora\0"
	"b\xc5\x99\x65zna\0"
	"dubna\0"
	"kv\xc4\x9btna\0"
	"\xc4\x8d\x65rvna\0"
	"\xc4\x8d\x65rvence\0"
	"srpna\0"
	"\xc5\x99\xc3\xadjna\0"
	"listopadu\0"
	"prosince\0"
	"led\0"
	"\xc3\xbano\0"
	"b\xc5\x99\x65\0"
	"dub\0"
	"kv\xc4\x9b\0"
	"\xc4\x8dvn\0"
	"\xc4\x8dvc\0"
	"srp\0"
	"z\xc3\xa1\xc5\x99\0"
	"\xc5\x99\xc3\xadj\0"
	"lis\0"
	"pro\0"
	"dd.MM.yyyy\0"
	"d. M. yyyy\0"
	"dddd d. MMMM yyyy\0"
	"d. MMMM yyyy\0"
	"MMMM yyyy\0"
	"s\xc3\xb8ndag\0"
	"mandag\0"
	"tirsdag\0"
	"onsdag\0"
	"torsdag\0"
	"fredag\0"
	"l\xc3\xb8rdag\0"
	"s\xc3\xb8n\0"
	"man\0"
	"tir\0"
	"ons\0"
	"tor\0"
	"fre\0"
	"l\xc3\xb8r\0"
	"M\0"
	"T\0"
	"O\0"
	"F\0"
	"L\0"
	"januar\0"
	"februar\0"
	"marts\0"
	"april\0"
	"maj\0"
	"juni\0"
	"juli\0"
	"august\0"
	"september\0"
	"oktober\0"
	"november\0"
	"december\0"
	"jan\0"
	"feb\0"
	"mar\0"
	"apr\0"
	"jun\0"
	"jul\0"
	"aug\0"
	"sep\0"
	"okt\0"
	"nov\0"
	"dec\0"
	"-\0"
	"dd-MM-yyyy\0"
	"dd-MM-yy\0"
	"yyyy MM dd\0"
	"vorm.\0"
	"nachm.\0"
	"Sonntag\0"
	"Montag\0"
	"Dienstag\0"
	"Mittwoch\0"
	"Donnerstag\0"
	"Freitag\0"
	"Samstag\0"
	"So\0"
	"Mo\0"
	"Di\0"
	"Mi\0"
	"Do\0"
	"Fr\0"
	"Sa\0"
	"D\0"
	"Januar\0"
	"Februar\0"
	"M\xc3\xa4rz\0"
	"April\0"
	"Mai\0"
	"Juni\0"
	"Juli\0"
	"August\0"
	"September\0"
	"Oktober\0"
	"November\0"
	"Dezember\0"
	"Jan\0"
	"Feb\0"
	"M\xc3\xa4r\0"
	"Apr\0"
	"Jun\0"
	"Jul\0"
	"Aug\0"
	"Sep\0"
	"Okt\0"
	"Nov\0"
	"Dez\0"
	"dd.MM.yy\0"
	"dd. MMM. yyyy\0"
	"dddd, d. MMMM yyyy\0"
	"d. MMM. yyyy\0"
	"HH:mm' Uhr'\0"
	"HH:mm:ss' Uhr'\0"
	"\xcf\x80.\xce\xbc.\0"
	"\xce\xbc.\xce\xbc.\0"
	"\xce\x9a\xcf\x85\xcf\x81\xce\xb9\xce\xb1\xce\xba\xce\xae\0"
	"\xce\x94\xce\xb5\xcf\x85\xcf\x84\xce\xad\xcf\x81\xce\xb1\0"
	"\xce\xa4\xcf\x81\xce\xaf\xcf\x84\xce\xb7\0"
	"\xce\xa4\xce\xb5\xcf\x84\xce\xac\xcf\x81\xcf\x84\xce\xb7\0"
	"\xce\xa0\xce\xad\xce\xbc\xcf\x80\xcf\x84\xce\xb7\0"
	"\xce\xa0\xce\xb1\xcf\x81\xce\xb1\xcf\x83\xce\xba\xce\xb5\xcf\x85\xce\xae\0"
	"\xce\xa3\xce\xac\xce\xb2\xce\xb2\xce\xb1\xcf\x84\xce\xbf\0"
	"\xce\x9a\xcf\x85\xcf\x81\0"
	"\xce\x94\xce\xb5\xcf\x85\0"
	"\xce\xa4\xcf\x81\xce\xaf\0"
	"\xce\xa4\xce\xb5\xcf\x84\0"
	"\xce\xa0\xce\xad\xce\xbc\0"
	"\xce\xa0\xce\xb1\xcf\x81\0"
	"\xce\xa3\xce\xac\xce\xb2\0"
	"\xce\x9a\0"
	"\xce\x94\0"
	"\xce\xa4\0"
	"\xce\xa0\0"
	"\xce\xa3\0"
	"\xce\x99\xce\xb1\xce\xbd\xce\xbf\xcf\x85\xce\xac\xcf\x81\xce\xb9\xce\xbf\xcf\x82\0"
	"\xce\xa6\xce\xb5\xce\xb2\xcf\x81\xce\xbf\xcf\x85\xce\xac\xcf\x81\xce\xb9\xce\xbf\xcf\x82\0"
	"\xce\x9c\xce\xac\xcf\x81\xcf\x84\xce\xb9\xce\xbf\xcf\x82\0"
	"\xce\x91\xcf\x80\xcf\x81\xce\xaf\xce\xbb\xce\xb9\xce\xbf\xcf\x82\0"
	"\xce\x9c\xce\xac\xce\xb9\xce\xbf\xcf\x82\0"
	"\xce\x99\xce\xbf\xcf\x8d\xce\xbd\xce\xb9\xce\xbf\xcf\x82\0"
	"\xce\x99\xce\xbf\xcf\x8d\xce\xbb\xce\xb9\xce\xbf\xcf\x82\0"
	"\xce\x91\xcf\x8d\xce\xb3\xce\xbf\xcf\x85\xcf\x83\xcf\x84\xce\xbf\xcf\x82\0"
	"\xce\xa3\xce\xb5\xcf\x80\xcf\x84\xce\xad\xce\xbc\xce\xb2\xcf\x81\xce\xb9\xce\xbf\xcf\x82\0"
	"\xce\x9f\xce\xba\xcf\x84\xcf\x8e\xce\xb2\xcf\x81\xce\xb9\xce\xbf\xcf\x82\0"
	"\xce\x9d\xce\xbf\xce\xad\xce\xbc\xce\xb2\xcf\x81\xce\xb9\xce\xbf\xcf\x82\0"
	"\xce\x94\xce\xb5\xce\xba\xce\xad\xce\xbc\xce\xb2\xcf\x81\xce\xb9\xce\xbf\xcf\x82\0"
	"\xce\x99\xce\xb1\xce\xbd\xce\xbf\xcf\x85\xce\xb1\xcf\x81\xce\xaf\xce\xbf\xcf\x85\0"
	"\xce\xa6\xce\xb5\xce\xb2\xcf\x81\xce\xbf\xcf\x85\xce\xb1\xcf\x81\xce\xaf\xce\xbf\xcf\x85\0"
	"\xce\x9c\xce\xb1\xcf\x81\xcf\x84\xce\xaf\xce\xbf\xcf\x85\0"
	"\xce\x91\xcf\x80\xcf\x81\xce\xb9\xce\xbb\xce\xaf\xce\xbf\xcf\x85\0"
	"\xce\x9c\xce\xb1\xce\x90\xce\xbf\xcf\x85\0"
	"\xce\x99\xce\xbf\xcf\x85\xce\xbd\xce\xaf\xce\xbf\xcf\x85\0"
	"\xce\x99\xce\xbf\xcf\x85\xce\xbb\xce\xaf\xce\xbf\xcf\x85\0"
	"\xce\x91\xcf\x85\xce\xb3\xce\xbf\xcf\x8d\xcf\x83\xcf\x84\xce\xbf\xcf\x85\0"
	"\xce\xa3\xce\xb5\xcf\x80\xcf\x84\xce\xb5\xce\xbc\xce\xb2\xcf\x81\xce\xaf\xce\xbf\xcf\x85\0"
	"\xce\x9f\xce\xba\xcf\x84\xcf\x89\xce\xb2\xcf\x81\xce\xaf\xce\xbf\xcf\x85\0"
	"\xce\x9d\xce\xbf\xce\xb5\xce\xbc\xce\xb2\xcf\x81\xce\xaf\xce\xbf\xcf\x85\0"
	"\xce\x94\xce\xb5\xce\xba\xce\xb5\xce\xbc\xce\xb2\xcf\x81\xce\xaf\xce\xbf\xcf\x85\0"
	"\xce\x99\xce\xb1\xce\xbd\0"
	"\xce\xa6\xce\xb5\xce\xb2\0"
	"\xce\x9c\xce\xac\xcf\x81\0"
	"\xce\x91\xcf\x80\xcf\x81\0"
	"\xce\x9c\xce\xac\xce\xb9\0"
	"\xce\x99\xce\xbf\xcf\x8d\xce\xbd\0"
	"\xce\x99\xce\xbf\xcf\x8d\xce\xbb\0"
	"\xce\x91\xcf\x8d\xce\xb3\0"
	"\xce\xa3\xce\xb5\xcf\x80\0"
	"\xce\x9f\xce\xba\xcf\x84\0"
	"\xce\x9d\xce\xbf\xce\xad\0"
	"\xce\x94\xce\xb5\xce\xba\0"
	"d/M/yy\0"
	"dd/MMM/yyyy\0"
	"dddd, d MMMM yyyy\0"
	"d MMMM yyyy\0"
	"h:mm tt\0"
	"h:mm:ss tt\0"
	"MMMM d\0"
	"Sunday\0"
	"Monday\0"
	"Tuesday\0"
	"Wednesday\0"
	"Thursday\0"
	"Friday\0"
	"Saturday\0"
	"Sun\0"
	"Mon\0"
	"Tue\0"
	"Wed\0"
	"Thu\0"
	"Fri\0"
	"Sat\0"
	"W\0"
	"January\0"
	"February\0"
	"March\0"
	"May\0"
	"June\0"
	"July\0"
	"October\0"
	"December\0"
	"Mar\0"
	"Oct\0"
	"Dec\0"
	"M/d/yyyy\0"
	"M/d/yy\0"
	"MM/dd/yy\0"
	"MM/dd/yyyy\0"
	"dd-MMM-yy\0"
	"dddd, MMMM d, yyyy\0"
	"MMMM d, yyyy\0"
	"dddd, d MMMM, yyyy\0"
	"d MMMM, yyyy\0"
	"d 'de' MMMM\0"
	"domingo\0"
	"lunes\0"
	"martes\0"
	"mi\xc3\xa9rcoles\0"
	"jueves\0"
	"viernes\0"
	"s\xc3\xa1\x62\x61\x64o\0"
	"Dom.\0"
	"Lun.\0"
	"Mar.\0"
	"Mi\xc3\xa9.\0"
	"Jue.\0"
	"Vie.\0"
	"S\xc3\xa1\x62.\0"
	"J\0"
	"V\0"
	"Enero\0"
	"Febrero\0"
	"Marzo\0"
	"Abril\0"
	"Mayo\0"
	"Junio\0"
	"Julio\0"
	"Agosto\0"
	"Septiembre\0"
	"Octubre\0"
	"Noviembre\0"
	"Diciembre\0"
	"enero\0"
	"febrero\0"
	"marzo\0"
	"mayo\0"
	"junio\0"
	"julio\0"
	"agosto\0"
	"septiembre\0"
	"noviembre\0"
	"diciembre\0"
	"Ene.\0"
	"Feb.\0"
	"Abr.\0"
	"May.\0"
	"Jun.\0"
	"Jul.\0"
	"Ago.\0"
	"Sept.\0"
	"Oct.\0"
	"Nov.\0"
	"Dic.\0"
	"d/MM/yy\0"
	"d-M-yy\0"
	"dddd, d' de 'MMMM' de 'yyyy\0"
	"dddd d' de 'MMMM' de 'yyyy\0"
	"d' de 'MMMM' de 'yyyy\0"
	"H.mm\0"
	"HH.mm\0"
	"HH'H'mm\0"
	"H.mm.ss\0"
	"HH.mm.ss\0"
	"HH'H'mm.ss\0"
	"MMMM' de 'yyyy\0"
	"ap.\0"
	"ip.\0"
	"sunnuntaina\0"
	"maanantaina\0"
	"tiistaina\0"
	"keskiviikkona\0"
	"torstaina\0"
	"perjantaina\0"
	"lauantaina\0"
	"su\0"
	"ma\0"
	"ti\0"
	"ke\0"
	"to\0"
	"pe\0"
	"la\0"
	"K\0"
	"tammikuu\0"
	"helmikuu\0"
	"maaliskuu\0"
	"huhtikuu\0"
	"toukokuu\0"
	"kes\xc3\xa4kuu\0"
	"hein\xc3\xa4kuu\0"
	"elokuu\0"
	"syyskuu\0"
	"lokakuu\0"
	"marraskuu\0"
	"joulukuu\0"
	"tammikuuta\0"
	"helmikuuta\0"
	"maaliskuuta\0"
	"huhtikuuta\0"
	"toukokuuta\0"
	"kes\xc3\xa4kuuta\0"
	"hein\xc3\xa4kuuta\0"
	"elokuuta\0"
	"syyskuuta\0"
	"lokakuuta\0"
	"marraskuuta\0"
	"joulukuuta\0"
	"tammi\0"
	"helmi\0"
	"maalis\0"
	"huhti\0"
	"touko\0"
	"kes\xc3\xa4\0"
	"hein\xc3\xa4\0"
	"elo\0"
	"syys\0"
	"loka\0"
	"marras\0"
	"joulu\0"
	"d.M.yyyy\0"
	"dimanche\0"
	"lundi\0"
	"mardi\0"
	"mercredi\0"
	"jeudi\0"
	"vendredi\0"
	"samedi\0"
	"dim.\0"
	"lun.\0"
	"mar.\0"
	"mer.\0"
	"jeu.\0"
	"ven.\0"
	"sam.\0"
	"janvier\0"
	"f\xc3\xa9vrier\0"
	"mars\0"
	"avril\0"
	"mai\0"
	"juin\0"
	"juillet\0"
	"ao\xc3\xbbt\0"
	"septembre\0"
	"octobre\0"
	"d\xc3\xa9\x63\x65mbre\0"
	"janv.\0"
	"f\xc3\xa9vr.\0"
	"avr.\0"
	"juil.\0"
	"sept.\0"
	"d\xc3\xa9\x63.\0"
	"dddd d MMMM yyyy\0"
	"d MMM yy\0"
	"HH' h 'mm\0"
	"HH'h'mm\0"
	"\xd7\x9c\xd7\xa4\xd7\xa0\xd7\x94\xd7\xb4\xd7\xa6\0"
	"\xd7\x90\xd7\x97\xd7\x94\xd7\xb4\xd7\xa6\0"
	"\xd7\x99\xd7\x95\xd7\x9d \xd7\xa8\xd7\x90\xd7\xa9\xd7\x95\xd7\x9f\0"
	"\xd7\x99\xd7\x95\xd7\x9d \xd7\xa9\xd7\xa0\xd7\x99\0"
	"\xd7\x99\xd7\x95\xd7\x9d \xd7\xa9\xd7\x9c\xd7\x99\xd7\xa9\xd7\x99\0"
	"\xd7\x99\xd7\x95\xd7\x9d \xd7\xa8\xd7\x91\xd7\x99\xd7\xa2\xd7\x99\0"
	"\xd7\x99\xd7\x95\xd7\x9d \xd7\x97\xd7\x9e\xd7\x99\xd7\xa9\xd7\x99\0"
	"\xd7\x99\xd7\x95\xd7\x9d \xd7\xa9\xd7\x99\xd7\xa9\xd7\x99\0"
	"\xd7\x99\xd7\x95\xd7\x9d \xd7\xa9\xd7\x91\xd7\xaa\0"
	"\xd7\x99\xd7\x95\xd7\x9d \xd7\x90\xd7\xb3\0"
	"\xd7\x99\xd7\x95\xd7\x9d \xd7\x91\xd7\xb3\0"
	"\xd7\x99\xd7\x95\xd7\x9d \xd7\x92\xd7\xb3\0"
	"\xd7\x99\xd7\x95\xd7\x9d \xd7\x93\xd7\xb3\0"
	"\xd7\x99\xd7\x95\xd7\x9d \xd7\x94\xd7\xb3\0"
	"\xd7\x99\xd7\x95\xd7\x9d \xd7\x95\xd7\xb3\0"
	"\xd7\xa9\xd7\x91\xd7\xaa\0"
	"\xd7\x90\xd7\xb3\0"
	"\xd7\x91\xd7\xb3\0"
	"\xd7\x92\xd7\xb3\0"
	"\xd7\x93\xd7\xb3\0"
	"\xd7\x94\xd7\xb3\0"
	"\xd7\x95\xd7\xb3\0"
	"\xd7\xa9\xd7\xb3\0"
	"\xd7\x99\xd7\xa0\xd7\x95\xd7\x90\xd7\xa8\0"
	"\xd7\xa4\xd7\x91\xd7\xa8\xd7\x95\xd7\x90\xd7\xa8\0"
	"\xd7\x9e\xd7\xa8\xd7\xa5\0"
	"\xd7\x90\xd7\xa4\xd7\xa8\xd7\x99\xd7\x9c\0"
	"\xd7\x9e\xd7\x90\xd7\x99\0"
	"\xd7\x99\xd7\x95\xd7\xa0\xd7\x99\0"
	"\xd7\x99\xd7\x95\xd7\x9c\xd7\x99\0"
	"\xd7\x90\xd7\x95\xd7\x92\xd7\x95\xd7\xa1\xd7\x98\0"
	"\xd7\xa1\xd7\xa4\xd7\x98\xd7\x9e\xd7\x91\xd7\xa8\0"
	"\xd7\x90\xd7\x95\xd7\xa7\xd7\x98\xd7\x95\xd7\x91\xd7\xa8\0"
	"\xd7\xa0\xd7\x95\xd7\x91\xd7\x9e\xd7\x91\xd7\xa8\0"
	"\xd7\x93\xd7\xa6\xd7\x9e\xd7\x91\xd7\xa8\0"
	"\xd7\x99\xd7\xa0\xd7\x95\xd7\xb3\0"
	"\xd7\xa4\xd7\x91\xd7\xa8\xd7\xb3\0"
	"\xd7\x90\xd7\xa4\xd7\xa8\xd7\xb3\0"
	"\xd7\x99\xd7\x95\xd7\xa0\xd7\xb3\0"
	"\xd7\x99\xd7\x95\xd7\x9c\xd7\xb3\0"
	"\xd7\x90\xd7\x95\xd7\x92\xd7\xb3\0"
	"\xd7\xa1\xd7\xa4\xd7\x98\xd7\xb3\0"
	"\xd7\x90\xd7\x95\xd7\xa7\xd7\xb3\0"
	"\xd7\xa0\xd7\x95\xd7\x91\xd7\xb3\0"
	"\xd7\x93\xd7\xa6\xd7\x9e\xd7\xb3\0"
	"dd MMMM yyyy\0"
	"dd-MMMM-yyyy\0"
	"dd '\xd7\x91'MMMM yyyy\0"
	"dd MMM yy\0"
	"dddd dd MMMM yyyy\0"
	"dddd dd '\xd7\x91'MMMM yyyy\0"
	"ddd dd '\xd7\x91'MMMM yyyy\0"
	"MMMM d.\0"
	"de.\0"
	"du.\0"
	"vas\xc3\xa1rnap\0"
	"h\xc3\xa9tf\xc5\x91\0"
	"kedd\0"
	"szerda\0"
	"cs\xc3\xbct\xc3\xb6rt\xc3\xb6k\0"
	"p\xc3\xa9ntek\0"
	"szombat\0"
	"H\0"
	"Sze\0"
	"Cs\0"
	"Szo\0"
	"Sz\0"
	"janu\xc3\xa1r\0"
	"febru\xc3\xa1r\0"
	"m\xc3\xa1rcius\0"
	"\xc3\xa1prilis\0"
	"m\xc3\xa1jus\0"
	"j\xc3\xbanius\0"
	"j\xc3\xbalius\0"
	"augusztus\0"
	"szeptember\0"
	"okt\xc3\xb3\x62\x65r\0"
	"jan.\0"
	"febr.\0"
	"m\xc3\xa1rc.\0"
	"\xc3\xa1pr.\0"
	"m\xc3\xa1j.\0"
	"j\xc3\xban.\0"
	"j\xc3\xbal.\0"
	"aug.\0"
	"szept.\0"
	"okt.\0"
	"dec.\0"
	". \0"
	"yyyy. MM. dd.\0"
	"yyyy. MMM d.\0"
	"yyyy. MMMM d., dddd\0"
	"yyyy. MMMM d.\0"
	"yyyy. MMMM\0"
	"f.h.\0"
	"e.h.\0"
	"sunnudagur\0"
	"m\xc3\xa1nudagur\0"
	"\xc3\xberi\xc3\xb0judagur\0"
	"mi\xc3\xb0vikudagur\0"
	"fimmtudagur\0"
	"f\xc3\xb6studagur\0"
	"laugardagur\0"
	"sun.\0"
	"m\xc3\xa1n.\0"
	"\xc3\xberi.\0"
	"mi\xc3\xb0.\0"
	"fim.\0"
	"f\xc3\xb6s.\0"
	"lau.\0"
	"\xc3\x9e\0"
	"jan\xc3\xba\x61r\0"
	"febr\xc3\xba\x61r\0"
	"apr\xc3\xadl\0"
	"ma\xc3\xad\0"
	"j\xc3\xban\xc3\xad\0"
	"j\xc3\xbal\xc3\xad\0"
	"\xc3\xa1g\xc3\xbast\0"
	"n\xc3\xb3vember\0"
	"desember\0"
	"apr.\0"
	"\xc3\xa1g\xc3\xba.\0"
	"sep.\0"
	"n\xc3\xb3v.\0"
	"d. MMM yyyy\0"
	"domenica\0"
	"luned\xc3\xac\0"
	"marted\xc3\xac\0"
	"mercoled\xc3\xac\0"
	"gioved\xc3\xac\0"
	"venerd\xc3\xac\0"
	"sabato\0"
	"dom\0"
	"lun\0"
	"mer\0"
	"gio\0"
	"ven\0"
	"sab\0"
	"G\0"
	"Gennaio\0"
	"Febbraio\0"
	"Aprile\0"
	"Maggio\0"
	"Giugno\0"
	"Luglio\0"
	"Settembre\0"
	"Ottobre\0"
	"Novembre\0"
	"Dicembre\0"
	"gennaio\0"
	"febbraio\0"
	"aprile\0"
	"maggio\0"
	"giugno\0"
	"luglio\0"
	"settembre\0"
	"ottobre\0"
	"dicembre\0"
	"gen\0"
	"mag\0"
	"giu\0"
	"lug\0"
	"ago\0"
	"set\0"
	"ott\0"
	"dic\0"
	"dd.M.yy\0"
	"d-MMM-yy\0"
	"\xe5\x8d\x88\xe5\x89\x8d\0"
	"\xe5\x8d\x88\xe5\xbe\x8c\0"
	"\xe6\x97\xa5\xe6\x9b\x9c\xe6\x97\xa5\0"
	"\xe6\x9c\x88\xe6\x9b\x9c\xe6\x97\xa5\0"
	"\xe7\x81\xab\xe6\x9b\x9c\xe6\x97\xa5\0"
	"\xe6\xb0\xb4\xe6\x9b\x9c\xe6\x97\xa5\0"
	"\xe6\x9c\xa8\xe6\x9b\x9c\xe6\x97\xa5\0"
	"\xe9\x87\x91\xe6\x9b\x9c\xe6\x97\xa5\0"
	"\xe5\x9c\x9f\xe6\x9b\x9c\xe6\x97\xa5\0"
	"\xe6\x9c\x88\0"
	"\xe7\x81\xab\0"
	"\xe6\xb0\xb4\0"
	"\xe6\x9c\xa8\0"
	"\xe9\x87\x91\0"
	"\xe5\x9c\x9f\0"
	"1\0"
	"2\0"
	"3\0"
	"4\0"
	"5\0"
	"6\0"
	"7\0"
	"8\0"
	"9\0"
	"10\0"
	"11\0"
	"12\0"
	"yyyy'\xe5\xb9\xb4'MM'\xe6\x9c\x88'dd'\xe6\x97\xa5'\0"
	"yyyy'\xe5\xb9\xb4'M'\xe6\x9c\x88'd'\xe6\x97\xa5 'dddd\0"
	"yyyy'\xe5\xb9\xb4'MM'\xe6\x9c\x88'dd'\xe6\x97\xa5 'dddd\0"
	"yyyy'\xe5\xb9\xb4'MMM'\xe6\x9c\x88'd'\xe6\x97\xa5'\0"
	"yyyy'\xe5\xb9\xb4'MMM'\xe6\x9c\x88'd'\xe6\x97\xa5 'dddd\0"
	"yyyy'\xe5\xb9\xb4'MMMMd'\xe6\x97\xa5'\0"
	"yyyy'\xe5\xb9\xb4'MMMMd'\xe6\x97\xa5 'dddd\0"
	"yyyy'\xe5\xb9\xb4'MMM'\xe6\x9c\x88'\0"
	"M\xec\x9b\x94 d\xec\x9d\xbc\0"
	"\xec\x98\xa4\xec\xa0\x84\0"
	"\xec\x98\xa4\xed\x9b\x84\0"
	"\xec\x9d\xbc\xec\x9a\x94\xec\x9d\xbc\0"
	"\xec\x9b\x94\xec\x9a\x94\xec\x9d\xbc\0"
	"\xed\x99\x94\xec\x9a\x94\xec\x9d\xbc\0"
	"\xec\x88\x98\xec\x9a\x94\xec\x9d\xbc\0"
	"\xeb\xaa\xa9\xec\x9a\x94\xec\x9d\xbc\0"
	"\xea\xb8\x88\xec\x9a\x94\xec\x9d\xbc\0"
	"\xed\x86\xa0\xec\x9a\x94\xec\x9d\xbc\0"
	"\xec\x9d\xbc\0"
	"\xec\x9b\x94\0"
	"\xed\x99\x94\0"
	"\xec\x88\x98\0"
	"\xeb\xaa\xa9\0"
	"\xea\xb8\x88\0"
	"\xed\x86\xa0\0"
	"1\xec\x9b\x94\0"
	"2\xec\x9b\x94\0"
	"3\xec\x9b\x94\0"
	"4\xec\x9b\x94\0"
	"5\xec\x9b\x94\0"
	"6\xec\x9b\x94\0"
	"7\xec\x9b\x94\0"
	"8\xec\x9b\x94\0"
	"9\xec\x9b\x94\0"
	"10\xec\x9b\x94\0"
	"11\xec\x9b\x94\0"
	"12\xec\x9b\x94\0"
	"yy-MM-dd\0"
	"yyyy'\xeb\x85\x84' M'\xec\x9b\x94' d'\xec\x9d\xbc' dddd\0"
	"yyyy'\xeb\x85\x84' M'\xec\x9b\x94' d'\xec\x9d\xbc'\0"
	"yy'\xeb\x85\x84' M'\xec\x9b\x94' d'\xec\x9d\xbc' dddd\0"
	"yy'\xeb\x85\x84' M'\xec\x9b\x94' d'\xec\x9d\xbc'\0"
	"yyyy'\xeb\x85\x84' MM'\xec\x9b\x94' dd'\xec\x9d\xbc' dddd\0"
	"yyyy'\xeb\x85\x84' MM'\xec\x9b\x94' dd'\xec\x9d\xbc'\0"
	"yyyy'\xeb\x85\x84 'MMM'\xec\x9b\x94 'd'\xec\x9d\xbc 'dddd\0"
	"yyyy'\xeb\x85\x84 'MMM'\xec\x9b\x94 'd'\xec\x9d\xbc'\0"
	"yyyy'\xeb\x85\x84 'MMMM d'\xec\x9d\xbc 'dddd\0"
	"yyyy'\xeb\x85\x84 'MMMM d'\xec\x9d\xbc'\0"
	"yyyy'\xeb\x85\x84' M'\xec\x9b\x94'\0"
	"yyyy'\xeb\x85\x84' MMM'\xec\x9b\x94'\0"
	"yyyy'\xeb\x85\x84' MMMM\0"
	"a.m.\0"
	"p.m.\0"
	"zondag\0"
	"maandag\0"
	"dinsdag\0"
	"woensdag\0"
	"donderdag\0"
	"vrijdag\0"
	"zaterdag\0"
	"zo\0"
	"di\0"
	"wo\0"
	"do\0"
	"vr\0"
	"za\0"
	"Z\0"
	"januari\0"
	"februari\0"
	"maart\0"
	"mei\0"
	"augustus\0"
	"mrt\0"
	"d-M-yyyy\0"
	"dd.MMM.yyyy\0"
	"HH.mm' uur'\0"
	"HH:mm' uur'\0"
	"HH.mm.ss' uur'\0"
	"HH:mm:ss' uur'\0"
	"d.MMMM.\0"
	"s\xc3\xb8.\0"
	"ma.\0"
	"ti.\0"
	"on.\0"
	"to.\0"
	"fr.\0"
	"l\xc3\xb8.\0"
	"des\0"
	"niedziela\0"
	"poniedzia\xc5\x82\x65k\0"
	"wtorek\0"
	"\xc5\x9broda\0"
	"czwartek\0"
	"pi\xc4\x85tek\0"
	"niedz.\0"
	"pon.\0"
	"wt.\0"
	"\xc5\x9br.\0"
	"czw.\0"
	"pt.\0"
	"sob.\0"
	"\xc5\x9a\0"
	"C\0"
	"stycze\xc5\x84\0"
	"luty\0"
	"marzec\0"
	"kwiecie\xc5\x84\0"
	"czerwiec\0"
	"lipiec\0"
	"sierpie\xc5\x84\0"
	"wrzesie\xc5\x84\0"
	"pa\xc5\xba\x64ziernik\0"
	"grudzie\xc5\x84\0"
	"stycznia\0"
	"lutego\0"
	"marca\0"
	"kwietnia\0"
	"maja\0"
	"czerwca\0"
	"lipca\0"
	"sierpnia\0"
	"wrze\xc5\x9bnia\0"
	"pa\xc5\xba\x64ziernika\0"
	"listopada\0"
	"grudnia\0"
	"sty\0"
	"lut\0"
	"kwi\0"
	"cze\0"
	"lip\0"
	"sie\0"
	"wrz\0"
	"pa\xc5\xba\0"
	"gru\0"
	"segunda-feira\0"
	"ter\xc3\xa7\x61-feira\0"
	"quarta-feira\0"
	"quinta-feira\0"
	"sexta-feira\0"
	"seg\0"
	"ter\0"
	"qua\0"
	"qui\0"
	"sex\0"
	"s\xc3\xa1\x62\0"
	"Q\0"
	"janeiro\0"
	"fevereiro\0"
	"mar\xc3\xa7o\0"
	"maio\0"
	"junho\0"
	"julho\0"
	"setembro\0"
	"outubro\0"
	"novembro\0"
	"dezembro\0"
	"fev\0"
	"abr\0"
	"out\0"
	"dez\0"
	"d.M.yy\0"
	"am\0"
	"sm\0"
	"dumengia\0"
	"glindesdi\0"
	"mesemna\0"
	"gievgia\0"
	"venderdi\0"
	"sonda\0"
	"du\0"
	"gli\0"
	"me\0"
	"gie\0"
	"ve\0"
	"schaner\0"
	"favrer\0"
	"avrigl\0"
	"matg\0"
	"zercladur\0"
	"fanadur\0"
	"avust\0"
	"settember\0"
	"october\0"
	"schan.\0"
	"favr.\0"
	"zercl.\0"
	"fan.\0"
	"sett.\0"
	"dddd, 'ils' d 'da' MMMM yyyy\0"
	"d 'da' MMMM yyyy\0"
	"duminic\xc4\x83\0"
	"luni\0"
	"mar\xc8\x9bi\0"
	"miercuri\0"
	"joi\0"
	"vineri\0"
	"s\xc3\xa2mb\xc4\x83t\xc4\x83\0"
	"Dum\0"
	"Lun\0"
	"Mie\0"
	"Joi\0"
	"Vin\0"
	"S\xc3\xa2m\0"
	"ianuarie\0"
	"februarie\0"
	"martie\0"
	"aprilie\0"
	"iunie\0"
	"iulie\0"
	"septembrie\0"
	"octombrie\0"
	"noiembrie\0"
	"decembrie\0"
	"ian.\0"
	"iun.\0"
	"iul.\0"
	"\xd0\xb4\xd0\xbe \xd0\xbf\xd0\xbe\xd0\xbb\xd1\x83\xd0\xb4\xd0\xbd\xd1\x8f\0"
	"\xd0\xbf\xd0\xbe\xd1\x81\xd0\xbb\xd0\xb5 \xd0\xbf\xd0\xbe\xd0\xbb\xd1\x83\xd0\xb4\xd0\xbd\xd1\x8f\0"
	"\xd0\xb2\xd0\xbe\xd1\x81\xd0\xba\xd1\x80\xd0\xb5\xd1\x81\xd0\xb5\xd0\xbd\xd1\x8c\xd0\xb5\0"
	"\xd0\xbf\xd0\xbe\xd0\xbd\xd0\xb5\xd0\xb4\xd0\xb5\xd0\xbb\xd1\x8c\xd0\xbd\xd0\xb8\xd0\xba\0"
	"\xd1\x81\xd1\x80\xd0\xb5\xd0\xb4\xd0\xb0\0"
	"\xd1\x87\xd0\xb5\xd1\x82\xd0\xb2\xd0\xb5\xd1\x80\xd0\xb3\0"
	"\xd0\xbf\xd1\x8f\xd1\x82\xd0\xbd\xd0\xb8\xd1\x86\xd0\xb0\0"
	"\xd1\x81\xd1\x83\xd0\xb1\xd0\xb1\xd0\xbe\xd1\x82\xd0\xb0\0"
	"\xd0\x92\xd1\x81\0"
	"\xd0\x9f\xd0\xbd\0"
	"\xd0\x92\xd1\x82\0"
	"\xd0\xa1\xd1\x80\0"
	"\xd0\xa7\xd1\x82\0"
	"\xd0\x9f\xd1\x82\0"
	"\xd0\xa1\xd0\xb1\0"
	"\xd0\x92\0"
	"\xd0\x9f\0"
	"\xd0\xa1\0"
	"\xd0\xa7\0"
	"\xd0\xaf\xd0\xbd\xd0\xb2\xd0\xb0\xd1\x80\xd1\x8c\0"
	"\xd0\xa4\xd0\xb5\xd0\xb2\xd1\x80\xd0\xb0\xd0\xbb\xd1\x8c\0"
	"\xd0\x9c\xd0\xb0\xd1\x80\xd1\x82\0"
	"\xd0\x90\xd0\xbf\xd1\x80\xd0\xb5\xd0\xbb\xd1\x8c\0"
	"\xd0\x9c\xd0\xb0\xd0\xb9\0"
	"\xd0\x98\xd1\x8e\xd0\xbd\xd1\x8c\0"
	"\xd0\x98\xd1\x8e\xd0\xbb\xd1\x8c\0"
	"\xd0\x90\xd0\xb2\xd0\xb3\xd1\x83\xd1\x81\xd1\x82\0"
	"\xd0\xa1\xd0\xb5\xd0\xbd\xd1\x82\xd1\x8f\xd0\xb1\xd1\x80\xd1\x8c\0"
	"\xd0\x9e\xd0\xba\xd1\x82\xd1\x8f\xd0\xb1\xd1\x80\xd1\x8c\0"
	"\xd0\x9d\xd0\xbe\xd1\x8f\xd0\xb1\xd1\x80\xd1\x8c\0"
	"\xd0\x94\xd0\xb5\xd0\xba\xd0\xb0\xd0\xb1\xd1\x80\xd1\x8c\0"
	"\xd1\x8f\xd0\xbd\xd0\xb2\xd0\xb0\xd1\x80\xd1\x8f\0"
	"\xd1\x84\xd0\xb5\xd0\xb2\xd1\x80\xd0\xb0\xd0\xbb\xd1\x8f\0"
	"\xd0\xbc\xd0\xb0\xd1\x80\xd1\x82\xd0\xb0\0"
	"\xd0\xb0\xd0\xbf\xd1\x80\xd0\xb5\xd0\xbb\xd1\x8f\0"
	"\xd0\xbc\xd0\xb0\xd1\x8f\0"
	"\xd0\xb8\xd1\x8e\xd0\xbd\xd1\x8f\0"
	"\xd0\xb8\xd1\x8e\xd0\xbb\xd1\x8f\0"
	"\xd0\xb0\xd0\xb2\xd0\xb3\xd1\x83\xd1\x81\xd1\x82\xd0\xb0\0"
	"\xd1\x81\xd0\xb5\xd0\xbd\xd1\x82\xd1\x8f\xd0\xb1\xd1\x80\xd1\x8f\0"
	"\xd0\xbe\xd0\xba\xd1\x82\xd1\x8f\xd0\xb1\xd1\x80\xd1\x8f\0"
	"\xd0\xbd\xd0\xbe\xd1\x8f\xd0\xb1\xd1\x80\xd1\x8f\0"
	"\xd0\xb4\xd0\xb5\xd0\xba\xd0\xb0\xd0\xb1\xd1\x80\xd1\x8f\0"
	"\xd0\xaf\xd0\xbd\xd0\xb2.\0"
	"\xd0\xa4\xd0\xb5\xd0\xb2\xd1\x80.\0"
	"\xd0\x90\xd0\xbf\xd1\x80.\0"
	"\xd0\x90\xd0\xb2\xd0\xb3.\0"
	"\xd0\xa1\xd0\xb5\xd0\xbd\xd1\x82.\0"
	"\xd0\x9e\xd0\xba\xd1\x82.\0"
	"\xd0\x9d\xd0\xbe\xd1\x8f\xd0\xb1.\0"
	"\xd0\x94\xd0\xb5\xd0\xba.\0"
	"nedjelja\0"
	"ponedjeljak\0"
	"utorak\0"
	"srijeda\0"
	"\xc4\x8d\x65tvrtak\0"
	"petak\0"
	"subota\0"
	"ned\0"
	"pon\0"
	"uto\0"
	"sri\0"
	"\xc4\x8d\x65t\0"
	"pet\0"
	"sub\0"
	"n\0"
	"p\0"
	"u\0"
	"s\0"
	"\xc4\x8d\0"
	"sije\xc4\x8d\x61nj\0"
	"velja\xc4\x8d\x61\0"
	"o\xc5\xbeujak\0"
	"travanj\0"
	"svibanj\0"
	"lipanj\0"
	"srpanj\0"
	"kolovoz\0"
	"rujan\0"
	"studeni\0"
	"prosinac\0"
	"sije\xc4\x8dnja\0"
	"velja\xc4\x8d\x65\0"
	"o\xc5\xbeujka\0"
	"travnja\0"
	"svibnja\0"
	"lipnja\0"
	"srpnja\0"
	"kolovoza\0"
	"rujna\0"
	"studenoga\0"
	"prosinca\0"
	"sij\0"
	"velj\0"
	"o\xc5\xbeu\0"
	"tra\0"
	"svi\0"
	"kol\0"
	"ruj\0"
	"stu\0"
	"d.M.yyyy.\0"
	"d.M.yy.\0"
	"d. M. yyyy.\0"
	"dd.MM.yyyy.\0"
	"d. M. yy.\0"
	"dd.MM.yy.\0"
	"dd. MM. yy.\0"
	"d. MMMM yyyy.\0"
	"dd. MMMM yyyy.\0"
	"dddd, d. MMMM yyyy.\0"
	"nede\xc4\xbe\x61\0"
	"pondelok\0"
	"utorok\0"
	"streda\0"
	"\xc5\xa1tvrtok\0"
	"piatok\0"
	"ut\0"
	"\xc5\xa1t\0"
	"pi\0"
	"U\0"
	"\xc5\xa0\0"
	"marec\0"
	"m\xc3\xa1j\0"
	"j\xc3\xban\0"
	"j\xc3\xbal\0"
	"janu\xc3\xa1ra\0"
	"febru\xc3\xa1ra\0"
	"apr\xc3\xadla\0"
	"m\xc3\xa1ja\0"
	"j\xc3\xbana\0"
	"j\xc3\xbala\0"
	"augusta\0"
	"septembra\0"
	"okt\xc3\xb3\x62ra\0"
	"novembra\0"
	"decembra\0"
	"paradite\0"
	"pasdite\0"
	"e diel\0"
	"e h\xc3\xabn\xc3\xab\0"
	"e mart\xc3\xab\0"
	"e m\xc3\xabrkur\xc3\xab\0"
	"e enjte\0"
	"e premte\0"
	"e shtun\xc3\xab\0"
	"Die\0"
	"H\xc3\xabn\0"
	"M\xc3\xabr\0"
	"Enj\0"
	"Pre\0"
	"Sht\0"
	"E\0"
	"janar\0"
	"shkurt\0"
	"prill\0"
	"qershor\0"
	"korrik\0"
	"gusht\0"
	"shtator\0"
	"tetor\0"
	"n\xc3\xabntor\0"
	"dhjetor\0"
	"Shk\0"
	"Pri\0"
	"Maj\0"
	"Qer\0"
	"Kor\0"
	"Gsh\0"
	"Tet\0"
	"N\xc3\xabn\0"
	"Dhj\0"
	"'den 'd MMMM\0"
	"FM\0"
	"EM\0"
	"s\xc3\xb6ndag\0"
	"m\xc3\xa5ndag\0"
	"tisdag\0"
	"l\xc3\xb6rdag\0"
	"s\xc3\xb6n\0"
	"m\xc3\xa5n\0"
	"tis\0"
	"l\xc3\xb6r\0"
	"augusti\0"
	"'den 'd MMMM yyyy\0"
	"dddd' den 'd MMMM yyyy\0"
	"'kl 'H:mm\0"
	"'kl 'H:mm:ss\0"
	"dd MMM yyyy\0"
	"ddd d MMMM yyyy\0"
	"'\xe0\xb8\xa7\xe0\xb8\xb1\xe0\xb8\x99'dddd'\xe0\xb8\x97\xe0\xb8\xb5\xe0\xb9\x88' d MMMM gg yyyy\0"
	"\xc3\x96\xc3\x96\0"
	"\xc3\x96S\0"
	"Pazar\0"
	"Pazartesi\0"
	"Sal\xc4\xb1\0"
	"\xc3\x87\x61r\xc5\x9f\x61mba\0"
	"Per\xc5\x9f\x65mbe\0"
	"Cuma\0"
	"Cumartesi\0"
	"Paz\0"
	"Pzt\0"
	"Sal\0"
	"\xc3\x87\x61r\0"
	"Per\0"
	"Cum\0"
	"Cmt\0"
	"\xc3\x87\0"
	"Ocak\0"
	"\xc5\x9eubat\0"
	"Mart\0"
	"Nisan\0"
	"May\xc4\xb1s\0"
	"Haziran\0"
	"Temmuz\0"
	"A\xc4\x9fustos\0"
	"Eyl\xc3\xbcl\0"
	"Ekim\0"
	"Kas\xc4\xb1m\0"
	"Aral\xc4\xb1k\0"
	"Oca\0"
	"\xc5\x9eub\0"
	"Nis\0"
	"Haz\0"
	"Tem\0"
	"A\xc4\x9fu\0"
	"Eyl\0"
	"Eki\0"
	"Kas\0"
	"Ara\0"
	"d.MM.yyyy\0"
	"d MMMM yyyy dddd\0"
	"\xd9\x82\xd8\xa8\xd9\x84 \xd8\xaf\xd9\x88\xd9\xbe\xdb\x81\xd8\xb1\0"
	"\xd8\xa8\xd8\xb9\xd8\xaf \xd8\xaf\xd9\x88\xd9\xbe\xdb\x81\xd8\xb1\0"
	"\xd8\xa7\xd8\xaa\xd9\x88\xd8\xa7\xd8\xb1\0"
	"\xd8\xb3\xd9\x88\xd9\x85\xd9\x88\xd8\xa7\xd8\xb1\0"
	"\xd9\x85\xd9\x86\xda\xaf\xd9\x84\0"
	"\xd8\xa8\xd8\xaf\xda\xbe\0"
	"\xd8\xac\xd9\x85\xd8\xb9\xd8\xb1\xd8\xa7\xd8\xaa\0"
	"\xd8\xac\xd9\x85\xd8\xb9\xdb\x81\0"
	"\xdb\x81\xd9\x81\xd8\xaa\xdb\x81\0"
	"\xd8\xac\xd9\x86\xd9\x88\xd8\xb1\xdb\x8c\0"
	"\xd9\x81\xd8\xb1\xd9\x88\xd8\xb1\xdb\x8c\0"
	"\xd9\x85\xd8\xa7\xd8\xb1\xda\x86\0"
	"\xd8\xa7\xd9\xbe\xd8\xb1\xdb\x8c\xd9\x84\0"
	"\xd9\x85\xd8\xa6\xdb\x8c\0"
	"\xd8\xac\xd9\x88\xd9\x86\0"
	"\xd8\xac\xd9\x88\xd9\x84\xd8\xa7\xd8\xa6\xdb\x8c\0"
	"\xd8\xa7\xda\xaf\xd8\xb3\xd8\xaa\0"
	"\xd8\xb3\xd8\xaa\xd9\x85\xd8\xa8\xd8\xb1\0"
	"\xd8\xa7\xda\xa9\xd8\xaa\xd9\x88\xd8\xa8\xd8\xb1\0"
	"\xd9\x86\xd9\x88\xd9\x85\xd8\xa8\xd8\xb1\0"
	"\xd8\xaf\xd8\xb3\xd9\x85\xd8\xa8\xd8\xb1\0"
	"dd MMMM, yyyy\0"
	"Minggu\0"
	"Senin\0"
	"Selasa\0"
	"Rabu\0"
	"Kamis\0"
	"Jumat\0"
	"Sabtu\0"
	"Min\0"
	"Sen\0"
	"Sel\0"
	"Rab\0"
	"Kam\0"
	"Jum\0"
	"Sab\0"
	"R\0"
	"Januari\0"
	"Februari\0"
	"Maret\0"
	"Mei\0"
	"Agustus\0"
	"Desember\0"
	"Agt\0"
	"Des\0"
	"dddd, dd MMMM yyyy\0"
	"\xd0\xb4\xd0\xbf\0"
	"\xd0\xbf\xd0\xbf\0"
	"\xd0\xbd\xd0\xb5\xd0\xb4\xd1\x96\xd0\xbb\xd1\x8f\0"
	"\xd0\xbf\xd0\xbe\xd0\xbd\xd0\xb5\xd0\xb4\xd1\x96\xd0\xbb\xd0\xbe\xd0\xba\0"
	"\xd0\xb2\xd1\x96\xd0\xb2\xd1\x82\xd0\xbe\xd1\x80\xd0\xbe\xd0\xba\0"
	"\xd1\x81\xd0\xb5\xd1\x80\xd0\xb5\xd0\xb4\xd0\xb0\0"
	"\xd1\x87\xd0\xb5\xd1\x82\xd0\xb2\xd0\xb5\xd1\x80\0"
	"\xd0\xbf\xca\xbc\xd1\x8f\xd1\x82\xd0\xbd\xd0\xb8\xd1\x86\xd1\x8f\0"
	"\xd1\x81\xd1\x83\xd0\xb1\xd0\xbe\xd1\x82\xd0\xb0\0"
	"\xd0\x9d\xd0\xb4\0"
	"\xd0\x9d\0"
	"\xd0\xa1\xd1\x96\xd1\x87\xd0\xb5\xd0\xbd\xd1\x8c\0"
	"\xd0\x9b\xd1\x8e\xd1\x82\xd0\xb8\xd0\xb9\0"
	"\xd0\x91\xd0\xb5\xd1\x80\xd0\xb5\xd0\xb7\xd0\xb5\xd0\xbd\xd1\x8c\0"
	"\xd0\x9a\xd0\xb2\xd1\x96\xd1\x82\xd0\xb5\xd0\xbd\xd1\x8c\0"
	"\xd0\xa2\xd1\x80\xd0\xb0\xd0\xb2\xd0\xb5\xd0\xbd\xd1\x8c\0"
	"\xd0\xa7\xd0\xb5\xd1\x80\xd0\xb2\xd0\xb5\xd0\xbd\xd1\x8c\0"
	"\xd0\x9b\xd0\xb8\xd0\xbf\xd0\xb5\xd0\xbd\xd1\x8c\0"
	"\xd0\xa1\xd0\xb5\xd1\x80\xd0\xbf\xd0\xb5\xd0\xbd\xd1\x8c\0"
	"\xd0\x92\xd0\xb5\xd1\x80\xd0\xb5\xd1\x81\xd0\xb5\xd0\xbd\xd1\x8c\0"
	"\xd0\x96\xd0\xbe\xd0\xb2\xd1\x82\xd0\xb5\xd0\xbd\xd1\x8c\0"
	"\xd0\x9b\xd0\xb8\xd1\x81\xd1\x82\xd0\xbe\xd0\xbf\xd0\xb0\xd0\xb4\0"
	"\xd0\x93\xd1\x80\xd1\x83\xd0\xb4\xd0\xb5\xd0\xbd\xd1\x8c\0"
	"\xd1\x81\xd1\x96\xd1\x87\xd0\xbd\xd1\x8f\0"
	"\xd0\xbb\xd1\x8e\xd1\x82\xd0\xbe\xd0\xb3\xd0\xbe\0"
	"\xd0\xb1\xd0\xb5\xd1\x80\xd0\xb5\xd0\xb7\xd0\xbd\xd1\x8f\0"
	"\xd0\xba\xd0\xb2\xd1\x96\xd1\x82\xd0\xbd\xd1\x8f\0"
	"\xd1\x82\xd1\x80\xd0\xb0\xd0\xb2\xd0\xbd\xd1\x8f\0"
	"\xd1\x87\xd0\xb5\xd1\x80\xd0\xb2\xd0\xbd\xd1\x8f\0"
	"\xd0\xbb\xd0\xb8\xd0\xbf\xd0\xbd\xd1\x8f\0"
	"\xd1\x81\xd0\xb5\xd1\x80\xd0\xbf\xd0\xbd\xd1\x8f\0"
	"\xd0\xb2\xd0\xb5\xd1\x80\xd0\xb5\xd1\x81\xd0\xbd\xd1\x8f\0"
	"\xd0\xb6\xd0\xbe\xd0\xb2\xd1\x82\xd0\xbd\xd1\x8f\0"
	"\xd0\xbb\xd0\xb8\xd1\x81\xd1\x82\xd0\xbe\xd0\xbf\xd0\xb0\xd0\xb4\xd0\xb0\0"
	"\xd0\xb3\xd1\x80\xd1\x83\xd0\xb4\xd0\xbd\xd1\x8f\0"
	"\xd0\xa1\xd1\x96\xd1\x87\0"
	"\xd0\x9b\xd1\x8e\xd1\x82\0"
	"\xd0\x91\xd0\xb5\xd1\x80\0"
	"\xd0\x9a\xd0\xb2\xd1\x96\0"
	"\xd0\xa2\xd1\x80\xd0\xb0\0"
	"\xd0\xa7\xd0\xb5\xd1\x80\0"
	"\xd0\x9b\xd0\xb8\xd0\xbf\0"
	"\xd0\xa1\xd0\xb5\xd1\x80\0"
	"\xd0\x92\xd0\xb5\xd1\x80\0"
	"\xd0\x96\xd0\xbe\xd0\xb2\0"
	"\xd0\x9b\xd0\xb8\xd1\x81\0"
	"\xd0\x93\xd1\x80\xd1\x83\0"
	"d MMMM yyyy' \xd1\x80.'\0"
	"MMMM yyyy' \xd1\x80.'\0"
	"\xd1\x80\xd0\xb0\xd0\xbd\xd1\x96\xd1\x86\xd1\x8b\0"
	"\xd0\xb2\xd0\xb5\xd1\x87\xd0\xb0\xd1\x80\xd0\xb0\0"
	"\xd0\xbd\xd1\x8f\xd0\xb4\xd0\xb7\xd0\xb5\xd0\xbb\xd1\x8f\0"
	"\xd0\xbf\xd0\xb0\xd0\xbd\xd1\x8f\xd0\xb4\xd0\xb7\xd0\xb5\xd0\xbb\xd0\xb0\xd0\xba\0"
	"\xd0\xb0\xd1\x9e\xd1\x82\xd0\xbe\xd1\x80\xd0\xb0\xd0\xba\0"
	"\xd1\x81\xd0\xb5\xd1\x80\xd0\xb0\xd0\xb4\xd0\xb0\0"
	"\xd1\x87\xd0\xb0\xd1\x86\xd0\xb2\xd0\xb5\xd1\x80\0"
	"\xd0\xbf\xd1\x8f\xd1\x82\xd0\xbd\xd1\x96\xd1\x86\xd0\xb0\0"
	"\xd0\xb0\xd1\x9e\0"
	"\xd1\x87\xd1\x86\0"
	"\xd0\xb0\0"
	"\xd1\x81\xd1\x82\xd1\x83\xd0\xb4\xd0\xb7\xd0\xb5\xd0\xbd\xd1\x8c\0"
	"\xd0\xbb\xd1\x8e\xd1\x82\xd1\x8b\0"
	"\xd1\x81\xd0\xb0\xd0\xba\xd0\xb0\xd0\xb2\xd1\x96\xd0\xba\0"
	"\xd0\xba\xd1\x80\xd0\xb0\xd1\x81\xd0\xb0\xd0\xb2\xd1\x96\xd0\xba\0"
	"\xd1\x87\xd1\x8d\xd1\x80\xd0\xb2\xd0\xb5\xd0\xbd\xd1\x8c\0"
	"\xd0\xbb\xd1\x96\xd0\xbf\xd0\xb5\xd0\xbd\xd1\x8c\0"
	"\xd0\xb6\xd0\xbd\xd1\x96\xd0\xb2\xd0\xb5\xd0\xbd\xd1\x8c\0"
	"\xd0\xb2\xd0\xb5\xd1\x80\xd0\xb0\xd1\x81\xd0\xb5\xd0\xbd\xd1\x8c\0"
	"\xd0\xba\xd0\xb0\xd1\x81\xd1\x82\xd1\x80\xd1\x8b\xd1\x87\xd0\xbd\xd1\x96\xd0\xba\0"
	"\xd0\xbb\xd1\x96\xd1\x81\xd1\x82\xd0\xb0\xd0\xbf\xd0\xb0\xd0\xb4\0"
	"\xd1\x81\xd0\xbd\xd0\xb5\xd0\xb6\xd0\xb0\xd0\xbd\xd1\x8c\0"
	"\xd1\x81\xd1\x82\xd1\x83\xd0\xb4\xd0\xb7\xd0\xb5\xd0\xbd\xd1\x8f\0"
	"\xd0\xbb\xd1\x8e\xd1\x82\xd0\xb0\xd0\xb3\xd0\xb0\0"
	"\xd1\x81\xd0\xb0\xd0\xba\xd0\xb0\xd0\xb2\xd1\x96\xd0\xba\xd0\xb0\0"
	"\xd0\xba\xd1\x80\xd0\xb0\xd1\x81\xd0\xb0\xd0\xb2\xd1\x96\xd0\xba\xd0\xb0\0"
	"\xd1\x87\xd1\x8d\xd1\x80\xd0\xb2\xd0\xb5\xd0\xbd\xd1\x8f\0"
	"\xd0\xbb\xd1\x96\xd0\xbf\xd0\xb5\xd0\xbd\xd1\x8f\0"
	"\xd0\xb6\xd0\xbd\xd1\x96\xd1\x9e\xd0\xbd\xd1\x8f\0"
	"\xd0\xb2\xd0\xb5\xd1\x80\xd0\xb0\xd1\x81\xd0\xbd\xd1\x8f\0"
	"\xd0\xba\xd0\xb0\xd1\x81\xd1\x82\xd1\x80\xd1\x8b\xd1\x87\xd0\xbd\xd1\x96\xd0\xba\xd0\xb0\0"
	"\xd0\xbb\xd1\x96\xd1\x81\xd1\x82\xd0\xb0\xd0\xbf\xd0\xb0\xd0\xb4\xd0\xb0\0"
	"\xd1\x81\xd0\xbd\xd0\xb5\xd0\xb6\xd0\xbd\xd1\x8f\0"
	"\xd1\x81\xd1\x82\xd1\x83\0"
	"\xd0\xbb\xd1\x8e\xd1\x82\0"
	"\xd1\x81\xd0\xb0\xd0\xba\0"
	"\xd0\xba\xd1\x80\xd0\xb0\0"
	"\xd1\x87\xd1\x8d\xd1\x80\0"
	"\xd0\xbb\xd1\x96\xd0\xbf\0"
	"\xd0\xb6\xd0\xbd\xd1\x96\0"
	"\xd0\xb2\xd0\xb5\xd1\x80\0"
	"\xd0\xba\xd0\xb0\xd1\x81\0"
	"\xd0\xbb\xd1\x96\xd1\x81\0"
	"\xd1\x81\xd0\xbd\xd0\xb5\0"
	"MMMM yyyy \xd0\xb3.\0"
	"dop.\0"
	"pop.\0"
	"nedelja\0"
	"ponedeljek\0"
	"torek\0"
	"sreda\0"
	"\xc4\x8d\x65trtek\0"
	"petek\0"
	"sre\0"
	"sob\0"
	"t\0"
	"junij\0"
	"julij\0"
	"avgust\0"
	"avg\0"
	"d. MM. yyyy\0"
	"dddd, dd. MMMM yyyy\0"
	"dd. MMMM yyyy\0"
	"e.k.\0"
	"p.k.\0"
	"p\xc3\xbchap\xc3\xa4\x65v\0"
	"esmasp\xc3\xa4\x65v\0"
	"teisip\xc3\xa4\x65v\0"
	"kolmap\xc3\xa4\x65v\0"
	"neljap\xc3\xa4\x65v\0"
	"reede\0"
	"laup\xc3\xa4\x65v\0"
	"jaanuar\0"
	"veebruar\0"
	"m\xc3\xa4rts\0"
	"aprill\0"
	"juuni\0"
	"juuli\0"
	"oktoober\0"
	"detsember\0"
	"jaan\0"
	"veebr\0"
	"sept\0"
	"dets\0"
	"H:mm.ss\0"
	"priek\xc5\xa1pusdien\xc4\x81\0"
	"p\xc4\x93\x63pusdien\xc4\x81\0"
	"sv\xc4\x93tdiena\0"
	"pirmdiena\0"
	"otrdiena\0"
	"tre\xc5\xa1\x64iena\0"
	"ceturtdiena\0"
	"piektdiena\0"
	"sestdiena\0"
	"Sv\0"
	"Pr\0"
	"Ot\0"
	"Tr\0"
	"Ce\0"
	"Pk\0"
	"Se\0"
	"Janv\xc4\x81ris\0"
	"Febru\xc4\x81ris\0"
	"Marts\0"
	"Apr\xc4\xablis\0"
	"Maijs\0"
	"J\xc5\xabnijs\0"
	"J\xc5\xablijs\0"
	"Augusts\0"
	"Septembris\0"
	"Oktobris\0"
	"Novembris\0"
	"Decembris\0"
	"janv\xc4\x81ris\0"
	"febru\xc4\x81ris\0"
	"apr\xc4\xablis\0"
	"maijs\0"
	"j\xc5\xabnijs\0"
	"j\xc5\xablijs\0"
	"augusts\0"
	"septembris\0"
	"oktobris\0"
	"novembris\0"
	"decembris\0"
	"Janv.\0"
	"Febr.\0"
	"Apr.\0"
	"J\xc5\xabn.\0"
	"J\xc5\xabl.\0"
	"Aug.\0"
	"Okt.\0"
	"Dec.\0"
	"yyyy. 'gada' d. MMM\0"
	"dddd, yyyy. 'gada' d. MMMM\0"
	"yyyy. 'gada' d. MMMM\0"
	"yyyy. 'g'. MMMM\0"
	"pr.p.\0"
	"sekmadienis\0"
	"pirmadienis\0"
	"antradienis\0"
	"tre\xc4\x8diadienis\0"
	"ketvirtadienis\0"
	"penktadienis\0"
	"\xc5\xa1\x65\xc5\xa1tadienis\0"
	"sk\0"
	"pr\0"
	"an\0"
	"tr\0"
	"kt\0"
	"pn\0"
	"A\0"
	"sausis\0"
	"vasaris\0"
	"kovas\0"
	"balandis\0"
	"gegu\xc5\xbe\xc4\x97\0"
	"bir\xc5\xbe\x65lis\0"
	"liepa\0"
	"rugpj\xc5\xabtis\0"
	"rugs\xc4\x97jis\0"
	"spalis\0"
	"lapkritis\0"
	"gruodis\0"
	"saus.\0"
	"vas.\0"
	"kov.\0"
	"bal.\0"
	"geg.\0"
	"bir\xc5\xbe.\0"
	"liep.\0"
	"rugp.\0"
	"rugs.\0"
	"spal.\0"
	"lapkr.\0"
	"gruod.\0"
	"yyyy 'm'. MMMM d 'd'., dddd\0"
	"yyyy 'm'. MMMM d 'd'.\0"
	"yyyy MMMM\0"
	"\xd0\xbf\xd0\xb5. \xd1\x87\xd0\xbe.\0"
	"\xd0\xbf\xd0\xb0. \xd1\x87\xd0\xbe.\0"
	"\xd0\xaf\xd0\xba\xd1\x88\xd0\xb0\xd0\xbd\xd0\xb1\xd0\xb5\0"
	"\xd0\x94\xd1\x83\xd1\x88\xd0\xb0\xd0\xbd\xd0\xb1\xd0\xb5\0"
	"\xd0\xa1\xd0\xb5\xd1\x88\xd0\xb0\xd0\xbd\xd0\xb1\xd0\xb5\0"
	"\xd0\xa7\xd0\xbe\xd1\x80\xd1\x88\xd0\xb0\xd0\xbd\xd0\xb1\xd0\xb5\0"
	"\xd0\x9f\xd0\xb0\xd0\xbd\xd2\xb7\xd1\x88\xd0\xb0\xd0\xbd\xd0\xb1\xd0\xb5\0"
	"\xd2\xb6\xd1\x83\xd0\xbc\xd1\x8a\xd0\xb0\0"
	"\xd0\xa8\xd0\xb0\xd0\xbd\xd0\xb1\xd0\xb5\0"
	"\xd0\xaf\xd1\x88\xd0\xb1\0"
	"\xd0\x94\xd1\x88\xd0\xb1\0"
	"\xd0\xa1\xd1\x88\xd0\xb1\0"
	"\xd0\xa7\xd1\x88\xd0\xb1\0"
	"\xd0\x9f\xd1\x88\xd0\xb1\0"
	"\xd2\xb6\xd0\xbc\xd1\x8a\0"
	"\xd0\xa8\xd0\xbd\xd0\xb1\0"
	"\xd0\xaf\xd0\xbd\xd0\xb2\xd0\xb0\xd1\x80\0"
	"\xd0\xa4\xd0\xb5\xd0\xb2\xd1\x80\xd0\xb0\xd0\xbb\0"
	"\xd0\x90\xd0\xbf\xd1\x80\xd0\xb5\xd0\xbb\0"
	"\xd0\x98\xd1\x8e\xd0\xbd\0"
	"\xd0\x98\xd1\x8e\xd0\xbb\0"
	"\xd0\xa1\xd0\xb5\xd0\xbd\xd1\x82\xd1\x8f\xd0\xb1\xd1\x80\0"
	"\xd0\x9e\xd0\xba\xd1\x82\xd1\x8f\xd0\xb1\xd1\x80\0"
	"\xd0\x9d\xd0\xbe\xd1\x8f\xd0\xb1\xd1\x80\0"
	"\xd0\x94\xd0\xb5\xd0\xba\xd0\xb0\xd0\xb1\xd1\x80\0"
	"\xd0\xaf\xd0\xbd\xd0\xb2\0"
	"\xd0\xa4\xd0\xb5\xd0\xb2\0"
	"\xd0\x9c\xd0\xb0\xd1\x80\0"
	"\xd0\x90\xd0\xbf\xd1\x80\0"
	"\xd0\x90\xd0\xb2\xd0\xb3\0"
	"\xd0\xa1\xd0\xb5\xd0\xbd\0"
	"\xd0\x9e\xd0\xba\xd1\x82\0"
	"\xd0\x9d\xd0\xbe\xd1\x8f\0"
	"\xd0\x94\xd0\xb5\xd0\xba\0"
	"d MMMM yyyy' \xd1\x81.'\0"
	"dd MMMM yyyy' \xd1\x81.'\0"
	"\xd9\x82\xd8\xa8\xd9\x84\xe2\x80\x8c\xd8\xa7\xd8\xb2\xd8\xb8\xd9\x87\xd8\xb1\0"
	"\xd8\xa8\xd8\xb9\xd8\xaf\xd8\xa7\xd8\xb2\xd8\xb8\xd9\x87\xd8\xb1\0"
	"\xdb\x8c\xda\xa9\xd8\xb4\xd9\x86\xd8\xa8\xd9\x87\0"
	"\xd8\xaf\xd9\x88\xd8\xb4\xd9\x86\xd8\xa8\xd9\x87\0"
	"\xd8\xb3\xd9\x87\xe2\x80\x8c\xd8\xb4\xd9\x86\xd8\xa8\xd9\x87\0"
	"\xda\x86\xd9\x87\xd8\xa7\xd8\xb1\xd8\xb4\xd9\x86\xd8\xa8\xd9\x87\0"
	"\xd9\xbe\xd9\x86\xd8\xac\xd8\xb4\xd9\x86\xd8\xa8\xd9\x87\0"
	"\xd8\xac\xd9\x85\xd8\xb9\xd9\x87\0"
	"\xd8\xb4\xd9\x86\xd8\xa8\xd9\x87\0"
	"\xdb\x8c\0"
	"\xd8\xaf\0"
	"\xd8\xb3\0"
	"\xda\x86\0"
	"\xd9\xbe\0"
	"\xd8\xac\0"
	"\xd8\xb4\0"
	"\xda\x98\xd8\xa7\xd9\x86\xd9\x88\xdb\x8c\xd9\x87\0"
	"\xd9\x81\xd9\x88\xd8\xb1\xdb\x8c\xd9\x87\0"
	"\xd9\x85\xd8\xa7\xd8\xb1\xd8\xb3\0"
	"\xd8\xa2\xd9\x88\xd8\xb1\xdb\x8c\xd9\x84\0"
	"\xd9\x85\xd9\x87\0"
	"\xda\x98\xd9\x88\xd8\xa6\xd9\x86\0"
	"\xda\x98\xd9\x88\xd8\xa6\xdb\x8c\xd9\x87\0"
	"\xd8\xa7\xd9\x88\xd8\xaa\0"
	"\xd8\xb3\xd9\xbe\xd8\xaa\xd8\xa7\xd9\x85\xd8\xa8\xd8\xb1\0"
	"\xd8\xa7\xda\xa9\xd8\xaa\xd8\xa8\xd8\xb1\0"
	"\xd9\x86\xd9\x88\xd8\xa7\xd9\x85\xd8\xa8\xd8\xb1\0"
	"\xd8\xaf\xd8\xb3\xd8\xa7\xd9\x85\xd8\xa8\xd8\xb1\0"
	"\xda\x98\xd8\xa7\xd9\x86\xd9\x88\xdb\x8c\xd9\x87\xd9\x94\0"
	"\xd9\x81\xd9\x88\xd8\xb1\xdb\x8c\xd9\x87\xd9\x94\0"
	"\xd9\x85\xd9\x87\xd9\x94\0"
	"\xda\x98\xd9\x88\xd8\xa6\xdb\x8c\xd9\x87\xd9\x94\0"
	"SA\0"
	"CH\0"
	"Ch\xe1\xbb\xa7 Nh\xe1\xba\xadt\0"
	"Th\xe1\xbb\xa9 Hai\0"
	"Th\xe1\xbb\xa9 Ba\0"
	"Th\xe1\xbb\xa9 T\xc6\xb0\0"
	"Th\xe1\xbb\xa9 N\xc4\x83m\0"
	"Th\xe1\xbb\xa9 S\xc3\xa1u\0"
	"Th\xe1\xbb\xa9 B\xe1\xba\xa3y\0"
	"CN\0"
	"Th 2\0"
	"Th 3\0"
	"Th 4\0"
	"Th 5\0"
	"Th 6\0"
	"Th 7\0"
	"T2\0"
	"T3\0"
	"T4\0"
	"T5\0"
	"T6\0"
	"T7\0"
	"Th\xc3\xa1ng 1\0"
	"Th\xc3\xa1ng 2\0"
	"Th\xc3\xa1ng 3\0"
	"Th\xc3\xa1ng 4\0"
	"Th\xc3\xa1ng 5\0"
	"Th\xc3\xa1ng 6\0"
	"Th\xc3\xa1ng 7\0"
	"Th\xc3\xa1ng 8\0"
	"Th\xc3\xa1ng 9\0"
	"Th\xc3\xa1ng 10\0"
	"Th\xc3\xa1ng 11\0"
	"Th\xc3\xa1ng 12\0"
	"th\xc3\xa1ng 1\0"
	"th\xc3\xa1ng 2\0"
	"th\xc3\xa1ng 3\0"
	"th\xc3\xa1ng 4\0"
	"th\xc3\xa1ng 5\0"
	"th\xc3\xa1ng 6\0"
	"th\xc3\xa1ng 7\0"
	"th\xc3\xa1ng 8\0"
	"th\xc3\xa1ng 9\0"
	"th\xc3\xa1ng 10\0"
	"th\xc3\xa1ng 11\0"
	"th\xc3\xa1ng 12\0"
	"Thg 1\0"
	"Thg 2\0"
	"Thg 3\0"
	"Thg 4\0"
	"Thg 5\0"
	"Thg 6\0"
	"Thg 7\0"
	"Thg 8\0"
	"Thg 9\0"
	"Thg 10\0"
	"Thg 11\0"
	"Thg 12\0"
	"\xd5\xaf\xd5\xa5\xd5\xbd\xd6\x85\xd6\x80\xd5\xab\xd6\x81 \xd5\xa1\xd5\xbc\xd5\xa1\xd5\xbb\0"
	"\xd5\xaf\xd5\xa5\xd5\xbd\xd6\x85\xd6\x80\xd5\xab\xd6\x81 \xd5\xb0\xd5\xa5\xd5\xbf\xd5\xb8\0"
	"\xd5\xaf\xd5\xab\xd6\x80\xd5\xa1\xd5\xaf\xd5\xab\0"
	"\xd5\xa5\xd6\x80\xd5\xaf\xd5\xb8\xd6\x82\xd5\xb7\xd5\xa1\xd5\xa2\xd5\xa9\xd5\xab\0"
	"\xd5\xa5\xd6\x80\xd5\xa5\xd6\x84\xd5\xb7\xd5\xa1\xd5\xa2\xd5\xa9\xd5\xab\0"
	"\xd5\xb9\xd5\xb8\xd6\x80\xd5\xa5\xd6\x84\xd5\xb7\xd5\xa1\xd5\xa2\xd5\xa9\xd5\xab\0"
	"\xd5\xb0\xd5\xab\xd5\xb6\xd5\xa3\xd5\xb7\xd5\xa1\xd5\xa2\xd5\xa9\xd5\xab\0"
	"\xd5\xb8\xd6\x82\xd6\x80\xd5\xa2\xd5\xa1\xd5\xa9\0"
	"\xd5\xb7\xd5\xa1\xd5\xa2\xd5\xa1\xd5\xa9\0"
	"\xd5\xaf\xd5\xab\xd6\x80\0"
	"\xd5\xa5\xd6\x80\xd5\xaf\0"
	"\xd5\xa5\xd6\x80\xd6\x84\0"
	"\xd5\xb9\xd6\x80\xd6\x84\0"
	"\xd5\xb0\xd5\xb6\xd5\xa3\0"
	"\xd5\xb8\xd6\x82\xd6\x80\0"
	"\xd5\xb7\xd5\xa2\xd5\xa9\0"
	"\xd4\xbf\0"
	"\xd4\xb5\0"
	"\xd5\x89\0"
	"\xd5\x80\0"
	"\xd5\x88\xd6\x82\0"
	"\xd5\x87\0"
	"\xd5\xb0\xd5\xb8\xd6\x82\xd5\xb6\xd5\xbe\xd5\xa1\xd6\x80\0"
	"\xd6\x83\xd5\xa5\xd5\xbf\xd6\x80\xd5\xbe\xd5\xa1\xd6\x80\0"
	"\xd5\xb4\xd5\xa1\xd6\x80\xd5\xbf\0"
	"\xd5\xa1\xd5\xba\xd6\x80\xd5\xab\xd5\xac\0"
	"\xd5\xb4\xd5\xa1\xd5\xb5\xd5\xab\xd5\xbd\0"
	"\xd5\xb0\xd5\xb8\xd6\x82\xd5\xb6\xd5\xab\xd5\xbd\0"
	"\xd5\xb0\xd5\xb8\xd6\x82\xd5\xac\xd5\xab\xd5\xbd\0"
	"\xd6\x85\xd5\xa3\xd5\xb8\xd5\xbd\xd5\xbf\xd5\xb8\xd5\xbd\0"
	"\xd5\xbd\xd5\xa5\xd5\xba\xd5\xbf\xd5\xa5\xd5\xb4\xd5\xa2\xd5\xa5\xd6\x80\0"
	"\xd5\xb0\xd5\xb8\xd5\xaf\xd5\xbf\xd5\xa5\xd5\xb4\xd5\xa2\xd5\xa5\xd6\x80\0"
	"\xd5\xb6\xd5\xb8\xd5\xb5\xd5\xa5\xd5\xb4\xd5\xa2\xd5\xa5\xd6\x80\0"
	"\xd5\xa4\xd5\xa5\xd5\xaf\xd5\xbf\xd5\xa5\xd5\xb4\xd5\xa2\xd5\xa5\xd6\x80\0"
	"\xd5\xb0\xd5\xb8\xd6\x82\xd5\xb6\xd5\xbe\xd5\xa1\xd6\x80\xd5\xab\0"
	"\xd6\x83\xd5\xa5\xd5\xbf\xd6\x80\xd5\xbe\xd5\xa1\xd6\x80\xd5\xab\0"
	"\xd5\xb4\xd5\xa1\xd6\x80\xd5\xbf\xd5\xab\0"
	"\xd5\xa1\xd5\xba\xd6\x80\xd5\xab\xd5\xac\xd5\xab\0"
	"\xd5\xb4\xd5\xa1\xd5\xb5\xd5\xab\xd5\xbd\xd5\xab\0"
	"\xd5\xb0\xd5\xb8\xd6\x82\xd5\xb6\xd5\xab\xd5\xbd\xd5\xab\0"
	"\xd5\xb0\xd5\xb8\xd6\x82\xd5\xac\xd5\xab\xd5\xbd\xd5\xab\0"
	"\xd6\x85\xd5\xa3\xd5\xb8\xd5\xbd\xd5\xbf\xd5\xb8\xd5\xbd\xd5\xab\0"
	"\xd5\xbd\xd5\xa5\xd5\xba\xd5\xbf\xd5\xa5\xd5\xb4\xd5\xa2\xd5\xa5\xd6\x80\xd5\xab\0"
	"\xd5\xb0\xd5\xb8\xd5\xaf\xd5\xbf\xd5\xa5\xd5\xb4\xd5\xa2\xd5\xa5\xd6\x80\xd5\xab\0"
	"\xd5\xb6\xd5\xb8\xd5\xb5\xd5\xa5\xd5\xb4\xd5\xa2\xd5\xa5\xd6\x80\xd5\xab\0"
	"\xd5\xa4\xd5\xa5\xd5\xaf\xd5\xbf\xd5\xa5\xd5\xb4\xd5\xa2\xd5\xa5\xd6\x80\xd5\xab\0"
	"\xd5\xb0\xd5\xb6\xd5\xbe\0"
	"\xd6\x83\xd5\xbf\xd5\xbe\0"
	"\xd5\xb4\xd6\x80\xd5\xbf\0"
	"\xd5\xa1\xd5\xba\xd6\x80\0"
	"\xd5\xb4\xd5\xb5\xd5\xbd\0"
	"\xd5\xb0\xd5\xb6\xd5\xbd\0"
	"\xd5\xb0\xd5\xac\xd5\xbd\0"
	"\xd6\x85\xd5\xa3\xd5\xbd\0"
	"\xd5\xbd\xd5\xba\xd5\xbf\0"
	"\xd5\xb0\xd5\xaf\xd5\xbf\0"
	"\xd5\xb6\xd5\xb5\xd5\xb4\0"
	"\xd5\xa4\xd5\xaf\xd5\xbf\0"
	"d/MM/yyyy\0"
	"d/MMM/yyyy\0"
	"d-MMM-yyyy\0"
	"dd-MMM-yyyy\0"
	"ddd, d-MMMM-yyyy\0"
	"ddd, dd-MMMM-yyyy\0"
	"bazar\0"
	"bazar ert\xc9\x99si\0"
	"\xc3\xa7\xc9\x99r\xc5\x9f\xc9\x99nb\xc9\x99 ax\xc5\x9f\x61m\xc4\xb1\0"
	"\xc3\xa7\xc9\x99r\xc5\x9f\xc9\x99nb\xc9\x99\0"
	"c\xc3\xbcm\xc9\x99 ax\xc5\x9f\x61m\xc4\xb1\0"
	"c\xc3\xbcm\xc9\x99\0"
	"\xc5\x9f\xc9\x99nb\xc9\x99\0"
	"B.\0"
	"B.E.\0"
	"\xc3\x87.A.\0"
	"\xc3\x87.\0"
	"C.A.\0"
	"\xc5\x9e.\0"
	"Yanvar\0"
	"Fevral\0"
	"Aprel\0"
	"\xc4\xb0yun\0"
	"\xc4\xb0yul\0"
	"Avqust\0"
	"Sentyabr\0"
	"Oktyabr\0"
	"Noyabr\0"
	"Dekabr\0"
	"yanvar\0"
	"fevral\0"
	"mart\0"
	"aprel\0"
	"may\0"
	"iyun\0"
	"iyul\0"
	"avqust\0"
	"sentyabr\0"
	"oktyabr\0"
	"noyabr\0"
	"dekabr\0"
	"yan\0"
	"iyn\0"
	"iyl\0"
	"avq\0"
	"sen\0"
	"noy\0"
	"dek\0"
	"d MMMM yyyy, dddd\0"
	"igandea\0"
	"astelehena\0"
	"asteartea\0"
	"asteazkena\0"
	"osteguna\0"
	"ostirala\0"
	"larunbata\0"
	"ig.\0"
	"al.\0"
	"ar.\0"
	"az.\0"
	"og.\0"
	"or.\0"
	"lr.\0"
	"I\0"
	"urtarrila\0"
	"otsaila\0"
	"martxoa\0"
	"apirila\0"
	"maiatza\0"
	"ekaina\0"
	"uztaila\0"
	"abuztua\0"
	"iraila\0"
	"urria\0"
	"azaroa\0"
	"abendua\0"
	"urtarrilak\0"
	"otsailak\0"
	"martxoak\0"
	"apirilak\0"
	"maiatzak\0"
	"ekainak\0"
	"uztailak\0"
	"abuztuak\0"
	"irailak\0"
	"urriak\0"
	"azaroak\0"
	"abenduak\0"
	"urt.\0"
	"ots.\0"
	"api.\0"
	"mai.\0"
	"eka.\0"
	"uzt.\0"
	"abu.\0"
	"ira.\0"
	"urr.\0"
	"aza.\0"
	"abe.\0"
	"yyyy MMM d\0"
	"yyyy('e')'ko' MMMM d, dddd\0"
	"yyyy('e')'ko' MMMM d\0"
	"yyyy('e')'ko' MMMM\0"
	"\xd0\xbf\xd1\x80\xd0\xb5\xd1\x82\xd0\xbf\xd0\xbb\xd0\xb0\xd0\xb4\xd0\xbd\xd0\xb5\0"
	"\xd0\xbf\xd0\xbe\xd0\xbf\xd0\xbb\xd0\xb0\xd0\xb4\xd0\xbd\xd0\xb5\0"
	"\xd0\xbd\xd0\xb5\xd0\xb4\xd0\xb5\xd0\xbb\xd0\xb0\0"
	"\xd1\x87\xd0\xb5\xd1\x82\xd0\xb2\xd1\x80\xd1\x82\xd0\xbe\xd0\xba\0"
	"\xd0\xbf\xd0\xb5\xd1\x82\xd0\xbe\xd0\xba\0"
	"\xd1\x81\xd0\xb0\xd0\xb1\xd0\xbe\xd1\x82\xd0\xb0\0"
	"\xd0\xbd\xd0\xb5\xd0\xb4.\0"
	"\xd0\xbf\xd0\xbe\xd0\xbd.\0"
	"\xd0\xb2\xd1\x82.\0"
	"\xd1\x81\xd1\x80\xd0\xb5.\0"
	"\xd1\x87\xd0\xb5\xd1\x82.\0"
	"\xd0\xbf\xd0\xb5\xd1\x82.\0"
	"\xd1\x81\xd0\xb0\xd0\xb1.\0"
	"\xd1\x98\xd0\xb0\xd0\xbd\xd1\x83\xd0\xb0\xd1\x80\xd0\xb8\0"
	"\xd0\xbc\xd0\xb0\xd1\x98\0"
	"\xd1\x98\xd1\x83\xd0\xbd\xd0\xb8\0"
	"\xd1\x98\xd1\x83\xd0\xbb\xd0\xb8\0"
	"\xd1\x98\xd0\xb0\xd0\xbd.\0"
	"\xd1\x84\xd0\xb5\xd0\xb2.\0"
	"\xd0\xbc\xd0\xb0\xd1\x80.\0"
	"\xd1\x98\xd1\x83\xd0\xbd.\0"
	"\xd1\x98\xd1\x83\xd0\xbb.\0"
	"dd.M.yyyy\0"
	"MMMM yyyy '\xd0\xb3'.\0"
	"Tshipi\0"
	"Mosopulogo\0"
	"Labobedi\0"
	"Laboraro\0"
	"Labone\0"
	"Labotlhano\0"
	"Matlhatso\0"
	"Tsh\0"
	"Mos\0"
	"Bed\0"
	"Rar\0"
	"Ne\0"
	"Tla\0"
	"Mat\0"
	"Ferikgong\0"
	"Tlhakole\0"
	"Mopitlo\0"
	"Moranang\0"
	"Motsheganang\0"
	"Seetebosigo\0"
	"Phukwi\0"
	"Phatwe\0"
	"Lwetse\0"
	"Diphalane\0"
	"Ngwanatsele\0"
	"Sedimonthole\0"
	"Fer\0"
	"Tlh\0"
	"Mop\0"
	"Mor\0"
	"Mot\0"
	"See\0"
	"Phu\0"
	"Pha\0"
	"Lwe\0"
	"Dip\0"
	"Ngw\0"
	"Sed\0"
	"yyyy MMMM d, dddd\0"
	"yyyy MMMM d\0"
	"Cawe\0"
	"Mvulo\0"
	"Lwesibini\0"
	"Lwesithathu\0"
	"Lwesine\0"
	"Lwesihlanu\0"
	"Mgqibelo\0"
	"Caw\0"
	"Mvu\0"
	"Bin\0"
	"Tha\0"
	"Sin\0"
	"Hla\0"
	"Mgq\0"
	"Janyuwari\0"
	"Februwari\0"
	"Matshi\0"
	"Epreli\0"
	"Meyi\0"
	"Julayi\0"
	"Agasti\0"
	"Septemba\0"
	"Okthoba\0"
	"Novemba\0"
	"Disemba\0"
	"Epr\0"
	"Mey\0"
	"Aga\0"
	"Dis\0"
	"Sonto\0"
	"Msombuluko\0"
	"Lwesibili\0"
	"Son\0"
	"Mso\0"
	"Bil\0"
	"B\0"
	"uJanuwari\0"
	"uFebruwari\0"
	"uMashi\0"
	"u-Apreli\0"
	"uMeyi\0"
	"uJuni\0"
	"uJulayi\0"
	"uAgasti\0"
	"uSepthemba\0"
	"u-Okthoba\0"
	"uNovemba\0"
	"uDisemba\0"
	"Januwari\0"
	"Mashi\0"
	"Apreli\0"
	"Septhemba\0"
	"Mas\0"
	"MMM d, yyyy\0"
	"vm.\0"
	"nm.\0"
	"Sondag\0"
	"Maandag\0"
	"Dinsdag\0"
	"Woensdag\0"
	"Donderdag\0"
	"Vrydag\0"
	"Saterdag\0"
	"Ma\0"
	"Wo\0"
	"Vr\0"
	"Januarie\0"
	"Februarie\0"
	"Maart\0"
	"Junie\0"
	"Julie\0"
	"Augustus\0"
	"\xe1\x83\x99\xe1\x83\x95\xe1\x83\x98\xe1\x83\xa0\xe1\x83\x90\0"
	"\xe1\x83\x9d\xe1\x83\xa0\xe1\x83\xa8\xe1\x83\x90\xe1\x83\x91\xe1\x83\x90\xe1\x83\x97\xe1\x83\x98\0"
	"\xe1\x83\xa1\xe1\x83\x90\xe1\x83\x9b\xe1\x83\xa8\xe1\x83\x90\xe1\x83\x91\xe1\x83\x90\xe1\x83\x97\xe1\x83\x98\0"
	"\xe1\x83\x9d\xe1\x83\x97\xe1\x83\xae\xe1\x83\xa8\xe1\x83\x90\xe1\x83\x91\xe1\x83\x90\xe1\x83\x97\xe1\x83\x98\0"
	"\xe1\x83\xae\xe1\x83\xa3\xe1\x83\x97\xe1\x83\xa8\xe1\x83\x90\xe1\x83\x91\xe1\x83\x90\xe1\x83\x97\xe1\x83\x98\0"
	"\xe1\x83\x9e\xe1\x83\x90\xe1\x83\xa0\xe1\x83\x90\xe1\x83\xa1\xe1\x83\x99\xe1\x83\x94\xe1\x83\x95\xe1\x83\x98\0"
	"\xe1\x83\xa8\xe1\x83\x90\xe1\x83\x91\xe1\x83\x90\xe1\x83\x97\xe1\x83\x98\0"
	"\xe1\x83\x99\xe1\x83\x95\xe1\x83\x98\0"
	"\xe1\x83\x9d\xe1\x83\xa0\xe1\x83\xa8\0"
	"\xe1\x83\xa1\xe1\x83\x90\xe1\x83\x9b\0"
	"\xe1\x83\x9d\xe1\x83\x97\xe1\x83\xae\0"
	"\xe1\x83\xae\xe1\x83\xa3\xe1\x83\x97\0"
	"\xe1\x83\x9e\xe1\x83\x90\xe1\x83\xa0\0"
	"\xe1\x83\xa8\xe1\x83\x90\xe1\x83\x91\0"
	"\xe1\x83\x99\0"
	"\xe1\x83\x9d\0"
	"\xe1\x83\xa1\0"
	"\xe1\x83\xae\0"
	"\xe1\x83\x9e\0"
	"\xe1\x83\xa8\0"
	"\xe1\x83\x98\xe1\x83\x90\xe1\x83\x9c\xe1\x83\x95\xe1\x83\x90\xe1\x83\xa0\xe1\x83\x98\0"
	"\xe1\x83\x97\xe1\x83\x94\xe1\x83\x91\xe1\x83\x94\xe1\x83\xa0\xe1\x83\x95\xe1\x83\x90\xe1\x83\x9a\xe1\x83\x98\0"
	"\xe1\x83\x9b\xe1\x83\x90\xe1\x83\xa0\xe1\x83\xa2\xe1\x83\x98\0"
	"\xe1\x83\x90\xe1\x83\x9e\xe1\x83\xa0\xe1\x83\x98\xe1\x83\x9a\xe1\x83\x98\0"
	"\xe1\x83\x9b\xe1\x83\x90\xe1\x83\x98\xe1\x83\xa1\xe1\x83\x98\0"
	"\xe1\x83\x98\xe1\x83\x95\xe1\x83\x9c\xe1\x83\x98\xe1\x83\xa1\xe1\x83\x98\0"
	"\xe1\x83\x98\xe1\x83\x95\xe1\x83\x9a\xe1\x83\x98\xe1\x83\xa1\xe1\x83\x98\0"
	"\xe1\x83\x90\xe1\x83\x92\xe1\x83\x95\xe1\x83\x98\xe1\x83\xa1\xe1\x83\xa2\xe1\x83\x9d\0"
	"\xe1\x83\xa1\xe1\x83\x94\xe1\x83\xa5\xe1\x83\xa2\xe1\x83\x94\xe1\x83\x9b\xe1\x83\x91\xe1\x83\x94\xe1\x83\xa0\xe1\x83\x98\0"
	"\xe1\x83\x9d\xe1\x83\xa5\xe1\x83\xa2\xe1\x83\x9d\xe1\x83\x9b\xe1\x83\x91\xe1\x83\x94\xe1\x83\xa0\xe1\x83\x98\0"
	"\xe1\x83\x9c\xe1\x83\x9d\xe1\x83\x94\xe1\x83\x9b\xe1\x83\x91\xe1\x83\x94\xe1\x83\xa0\xe1\x83\x98\0"
	"\xe1\x83\x93\xe1\x83\x94\xe1\x83\x99\xe1\x83\x94\xe1\x83\x9b\xe1\x83\x91\xe1\x83\x94\xe1\x83\xa0\xe1\x83\x98\0"
	"\xe1\x83\x98\xe1\x83\x90\xe1\x83\x9c\0"
	"\xe1\x83\x97\xe1\x83\x94\xe1\x83\x91\0"
	"\xe1\x83\x9b\xe1\x83\x90\xe1\x83\xa0\0"
	"\xe1\x83\x90\xe1\x83\x9e\xe1\x83\xa0\0"
	"\xe1\x83\x9b\xe1\x83\x90\xe1\x83\x98\0"
	"\xe1\x83\x98\xe1\x83\x95\xe1\x83\x9c\0"
	"\xe1\x83\x98\xe1\x83\x95\xe1\x83\x9a\0"
	"\xe1\x83\x90\xe1\x83\x92\xe1\x83\x95\0"
	"\xe1\x83\xa1\xe1\x83\x94\xe1\x83\xa5\0"
	"\xe1\x83\x9d\xe1\x83\xa5\xe1\x83\xa2\0"
	"\xe1\x83\x9c\xe1\x83\x9d\xe1\x83\x94\0"
	"\xe1\x83\x93\xe1\x83\x94\xe1\x83\x99\0"
	"d MMM, yyyy\0"
	"f.p.\0"
	"s.p.\0"
	"m\xc3\xa1nadagur\0"
	"t\xc3\xbdsdagur\0"
	"mikudagur\0"
	"h\xc3\xb3sdagur\0"
	"fr\xc3\xadggjadagur\0"
	"leygardagur\0"
	"sun\0"
	"m\xc3\xa1n\0"
	"t\xc3\xbds\0"
	"mik\0"
	"h\xc3\xb3s\0"
	"fr\xc3\xad\0"
	"ley\0"
	"\xe0\xa4\xaa\xe0\xa5\x82\xe0\xa4\xb0\xe0\xa5\x8d\xe0\xa4\xb5\0"
	"\xe0\xa4\x85\xe0\xa4\xaa\xe0\xa4\xb0\0"
	"\xe0\xa4\xb0\xe0\xa4\xb5\xe0\xa4\xbf\xe0\xa4\xb5\xe0\xa4\xbe\xe0\xa4\xb0\0"
	"\xe0\xa4\xb8\xe0\xa5\x8b\xe0\xa4\xae\xe0\xa4\xb5\xe0\xa4\xbe\xe0\xa4\xb0\0"
	"\xe0\xa4\xae\xe0\xa4\x82\xe0\xa4\x97\xe0\xa4\xb2\xe0\xa4\xb5\xe0\xa4\xbe\xe0\xa4\xb0\0"
	"\xe0\xa4\xac\xe0\xa5\x81\xe0\xa4\xa7\xe0\xa4\xb5\xe0\xa4\xbe\xe0\xa4\xb0\0"
	"\xe0\xa4\x97\xe0\xa5\x81\xe0\xa4\xb0\xe0\xa5\x81\xe0\xa4\xb5\xe0\xa4\xbe\xe0\xa4\xb0\0"
	"\xe0\xa4\xb6\xe0\xa5\x81\xe0\xa4\x95\xe0\xa5\x8d\xe0\xa4\xb0\xe0\xa4\xb5\xe0\xa4\xbe\xe0\xa4\xb0\0"
	"\xe0\xa4\xb6\xe0\xa4\xa8\xe0\xa4\xbf\xe0\xa4\xb5\xe0\xa4\xbe\xe0\xa4\xb0\0"
	"\xe0\xa4\xb0\xe0\xa4\xb5\xe0\xa4\xbf\0"
	"\xe0\xa4\xb8\xe0\xa5\x8b\xe0\xa4\xae\0"
	"\xe0\xa4\xae\xe0\xa4\x82\xe0\xa4\x97\xe0\xa4\xb2\0"
	"\xe0\xa4\xac\xe0\xa5\x81\xe0\xa4\xa7\0"
	"\xe0\xa4\x97\xe0\xa5\x81\xe0\xa4\xb0\xe0\xa5\x81\0"
	"\xe0\xa4\xb6\xe0\xa5\x81\xe0\xa4\x95\xe0\xa5\x8d\xe0\xa4\xb0\0"
	"\xe0\xa4\xb6\xe0\xa4\xa8\xe0\xa4\xbf\0"
	"\xe0\xa4\xb0\0"
	"\xe0\xa4\xb8\xe0\xa5\x8b\0"
	"\xe0\xa4\xae\xe0\xa4\x82\0"
	"\xe0\xa4\xac\xe0\xa5\x81\0"
	"\xe0\xa4\x97\xe0\xa5\x81\0"
	"\xe0\xa4\xb6\xe0\xa5\x81\0"
	"\xe0\xa4\xb6\0"
	"\xe0\xa4\x9c\xe0\xa4\xa8\xe0\xa4\xb5\xe0\xa4\xb0\xe0\xa5\x80\0"
	"\xe0\xa4\xab\xe0\xa4\xbc\xe0\xa4\xb0\xe0\xa4\xb5\xe0\xa4\xb0\xe0\xa5\x80\0"
	"\xe0\xa4\xae\xe0\xa4\xbe\xe0\xa4\xb0\xe0\xa5\x8d\xe0\xa4\x9a\0"
	"\xe0\xa4\x85\xe0\xa4\xaa\xe0\xa5\x8d\xe0\xa4\xb0\xe0\xa5\x88\xe0\xa4\xb2\0"
	"\xe0\xa4\xae\xe0\xa4\x88\0"
	"\xe0\xa4\x9c\xe0\xa5\x82\xe0\xa4\xa8\0"
	"\xe0\xa4\x9c\xe0\xa5\x81\xe0\xa4\xb2\xe0\xa4\xbe\xe0\xa4\x88\0"
	"\xe0\xa4\x85\xe0\xa4\x97\xe0\xa4\xb8\xe0\xa5\x8d\xe0\xa4\xa4\0"
	"\xe0\xa4\xb8\xe0\xa4\xbf\xe0\xa4\xa4\xe0\xa4\x82\xe0\xa4\xac\xe0\xa4\xb0\0"
	"\xe0\xa4\x85\xe0\xa4\x95\xe0\xa5\x8d\xe0\xa4\x9f\xe0\xa5\x82\xe0\xa4\xac\xe0\xa4\xb0\0"
	"\xe0\xa4\xa8\xe0\xa4\xb5\xe0\xa4\x82\xe0\xa4\xac\xe0\xa4\xb0\0"
	"\xe0\xa4\xa6\xe0\xa4\xbf\xe0\xa4\xb8\xe0\xa4\x82\xe0\xa4\xac\xe0\xa4\xb0\0"
	"\xe0\xa4\x9c\xe0\xa4\xa8\0"
	"\xe0\xa4\xab\xe0\xa4\xbc\xe0\xa4\xb0\0"
	"\xe0\xa4\x85\xe0\xa4\xaa\xe0\xa5\x8d\xe0\xa4\xb0\xe0\xa5\x88\0"
	"\xe0\xa4\x9c\xe0\xa5\x81\xe0\xa4\xb2\xe0\xa4\xbe\0"
	"\xe0\xa4\x85\xe0\xa4\x97\0"
	"\xe0\xa4\xb8\xe0\xa4\xbf\xe0\xa4\xa4\xe0\xa4\x82\0"
	"\xe0\xa4\x85\xe0\xa4\x95\xe0\xa5\x8d\xe0\xa4\x9f\xe0\xa5\x82\0"
	"\xe0\xa4\xa8\xe0\xa4\xb5\xe0\xa4\x82\0"
	"\xe0\xa4\xa6\xe0\xa4\xbf\xe0\xa4\xb8\xe0\xa4\x82\0"
	"d 'ta'\xe2\x80\x99 MMMM\0"
	"QN\0"
	"WN\0"
	"Il-\xc4\xa6\x61\x64\x64\0"
	"It-Tnejn\0"
	"It-Tlieta\0"
	"L-Erbg\xc4\xa7\x61\0"
	"Il-\xc4\xa6\x61mis\0"
	"Il-\xc4\xa0img\xc4\xa7\x61\0"
	"Is-Sibt\0"
	"\xc4\xa6\x61\x64\0"
	"Tne\0"
	"Tli\0"
	"Erb\0"
	"\xc4\xa6\x61m\0"
	"\xc4\xa0im\0"
	"Sib\0"
	"\xc4\xa6\0"
	"\xc4\xa0\0"
	"Jannar\0"
	"Frar\0"
	"Marzu\0"
	"Mejju\0"
	"\xc4\xa0unju\0"
	"Lulju\0"
	"Awwissu\0"
	"Settembru\0"
	"Ottubru\0"
	"Novembru\0"
	"Di\xc4\x8b\x65mbru\0"
	"Fra\0"
	"Mej\0"
	"\xc4\xa0un\0"
	"Lul\0"
	"Aww\0"
	"Set\0"
	"Ott\0"
	"Di\xc4\x8b\0"
	"dddd, d 'ta'\xe2\x80\x99 MMMM yyyy\0"
	"d 'ta'\xe2\x80\x99 MMMM yyyy\0"
	"MMMM 'ta'\xe2\x80\x99 yyyy\0"
	"i.b.\0"
	"e.b.\0"
	"sotnabeaivi\0"
	"vuoss\xc3\xa1rga\0"
	"ma\xc5\x8b\xc5\x8b\x65\x62\xc3\xa1rga\0"
	"gaskavahkku\0"
	"duorasdat\0"
	"bearjadat\0"
	"l\xc3\xa1vvardat\0"
	"sotn\0"
	"vuos\0"
	"ma\xc5\x8b\0"
	"gask\0"
	"duor\0"
	"bear\0"
	"l\xc3\xa1v\0"
	"o\xc4\x91\xc4\x91\x61jagem\xc3\xa1nnu\0"
	"guovvam\xc3\xa1nnu\0"
	"njuk\xc4\x8d\x61m\xc3\xa1nnu\0"
	"cuo\xc5\x8bom\xc3\xa1nnu\0"
	"miessem\xc3\xa1nnu\0"
	"geassem\xc3\xa1nnu\0"
	"suoidnem\xc3\xa1nnu\0"
	"borgem\xc3\xa1nnu\0"
	"\xc4\x8d\x61k\xc4\x8d\x61m\xc3\xa1nnu\0"
	"golggotm\xc3\xa1nnu\0"
	"sk\xc3\xa1\x62mam\xc3\xa1nnu\0"
	"juovlam\xc3\xa1nnu\0"
	"o\xc4\x91\xc4\x91j\0"
	"guov\0"
	"njuk\0"
	"cuo\0"
	"mies\0"
	"geas\0"
	"suoi\0"
	"borg\0"
	"\xc4\x8d\x61k\xc4\x8d\0"
	"golg\0"
	"sk\xc3\xa1\x62\0"
	"juov\0"
	"D\xc3\xa9 Domhnaigh\0"
	"D\xc3\xa9 Luain\0"
	"D\xc3\xa9 M\xc3\xa1irt\0"
	"D\xc3\xa9 C\xc3\xa9\x61\x64\x61oin\0"
	"D\xc3\xa9\x61rdaoin\0"
	"D\xc3\xa9 hAoine\0"
	"D\xc3\xa9 Sathairn\0"
	"Domh\0"
	"Luan\0"
	"M\xc3\xa1irt\0"
	"C\xc3\xa9\x61\x64\0"
	"D\xc3\xa9\x61r\0"
	"Aoine\0"
	"Sath\0"
	"Ean\xc3\xa1ir\0"
	"Feabhra\0"
	"M\xc3\xa1rta\0"
	"Aibre\xc3\xa1n\0"
	"Bealtaine\0"
	"Meitheamh\0"
	"I\xc3\xbail\0"
	"L\xc3\xbanasa\0"
	"Me\xc3\xa1n F\xc3\xb3mhair\0"
	"Deireadh F\xc3\xb3mhair\0"
	"Samhain\0"
	"Nollaig\0"
	"Ean\0"
	"Feabh\0"
	"Aib\0"
	"Beal\0"
	"Meith\0"
	"L\xc3\xban\0"
	"MF\xc3\xb3mh\0"
	"DF\xc3\xb3mh\0"
	"Samh\0"
	"Noll\0"
	"pg\0"
	"ptg\0"
	"Ahad\0"
	"Isnin\0"
	"Khamis\0"
	"Jumaat\0"
	"Ahd\0"
	"Isn\0"
	"Kha\0"
	"Mac\0"
	"Julai\0"
	"Ogos\0"
	"Disember\0"
	"Ogo\0"
	"\xd1\x82\xd2\xaf\xd1\x81\xd0\xba\xd0\xb5 \xd0\xb4\xd0\xb5\xd0\xb9\xd1\x96\xd0\xbd\0"
	"\xd1\x82\xd2\xaf\xd1\x81\xd1\x82\xd0\xb5\xd0\xbd \xd0\xba\xd0\xb5\xd0\xb9\xd1\x96\xd0\xbd\0"
	"\xd0\xb6\xd0\xb5\xd0\xba\xd1\x81\xd0\xb5\xd0\xbd\xd1\x96\0"
	"\xd0\xb4\xd1\x83\xd0\xb9\xd1\x81\xd0\xb5\xd0\xbd\xd0\xb1\xd1\x96\0"
	"\xd1\x81\xd0\xb5\xd0\xb9\xd1\x81\xd0\xb5\xd0\xbd\xd0\xb1\xd1\x96\0"
	"\xd1\x81\xd3\x99\xd1\x80\xd1\x81\xd0\xb5\xd0\xbd\xd0\xb1\xd1\x96\0"
	"\xd0\xb1\xd0\xb5\xd0\xb9\xd1\x81\xd0\xb5\xd0\xbd\xd0\xb1\xd1\x96\0"
	"\xd0\xb6\xd2\xb1\xd0\xbc\xd0\xb0\0"
	"\xd1\x81\xd0\xb5\xd0\xbd\xd0\xb1\xd1\x96\0"
	"\xd0\xb6\xd1\x81.\0"
	"\xd0\xb4\xd1\x81.\0"
	"\xd1\x81\xd1\x81.\0"
	"\xd1\x81\xd1\x80.\0"
	"\xd0\xb1\xd1\x81.\0"
	"\xd0\xb6\xd0\xbc.\0"
	"\xd1\x81\xd0\xb1.\0"
	"\xd0\x96\0"
	"\xd0\x94\0"
	"\xd0\x91\0"
	"\xd2\x9b\xd0\xb0\xd2\xa3\xd1\x82\xd0\xb0\xd1\x80\0"
	"\xd0\xb0\xd2\x9b\xd0\xbf\xd0\xb0\xd0\xbd\0"
	"\xd0\xbd\xd0\xb0\xd1\x83\xd1\x80\xd1\x8b\xd0\xb7\0"
	"\xd1\x81\xd3\x99\xd1\x83\xd1\x96\xd1\x80\0"
	"\xd0\xbc\xd0\xb0\xd0\xbc\xd1\x8b\xd1\x80\0"
	"\xd0\xbc\xd0\xb0\xd1\x83\xd1\x81\xd1\x8b\xd0\xbc\0"
	"\xd1\x88\xd1\x96\xd0\xbb\xd0\xb4\xd0\xb5\0"
	"\xd1\x82\xd0\xb0\xd0\xbc\xd1\x8b\xd0\xb7\0"
	"\xd2\x9b\xd1\x8b\xd1\x80\xd0\xba\xd2\xaf\xd0\xb9\xd0\xb5\xd0\xba\0"
	"\xd2\x9b\xd0\xb0\xd0\xb7\xd0\xb0\xd0\xbd\0"
	"\xd2\x9b\xd0\xb0\xd1\x80\xd0\xb0\xd1\x88\xd0\xb0\0"
	"\xd0\xb6\xd0\xb5\xd0\xbb\xd1\x82\xd0\xbe\xd2\x9b\xd1\x81\xd0\xb0\xd0\xbd\0"
	"\xd2\x9b\xd0\xb0\xd2\xa3.\0"
	"\xd0\xb0\xd2\x9b\xd0\xbf.\0"
	"\xd0\xbd\xd0\xb0\xd1\x83.\0"
	"\xd1\x81\xd3\x99\xd1\x83.\0"
	"\xd0\xbc\xd0\xb0\xd0\xbc.\0"
	"\xd0\xbc\xd0\xb0\xd1\x83.\0"
	"\xd1\x88\xd1\x96\xd0\xbb.\0"
	"\xd1\x82\xd0\xb0\xd0\xbc.\0"
	"\xd2\x9b\xd1\x8b\xd1\x80.\0"
	"\xd2\x9b\xd0\xb0\xd0\xb7.\0"
	"\xd2\x9b\xd0\xb0\xd1\x80.\0"
	"\xd0\xb6\xd0\xb5\xd0\xbb\xd1\x82.\0"
	"yyyy, dd-MMM\0"
	"d-MMMM\0"
	"\xd1\x82\xd2\xaf\xd1\x88\xd0\xba\xd3\xa9 \xd1\x87\xd0\xb5\xd0\xb9\xd0\xb8\xd0\xbd\xd0\xba\xd0\xb8\0"
	"\xd1\x82\xd2\xaf\xd1\x88\xd1\x82\xd3\xa9\xd0\xbd \xd0\xba\xd0\xb8\xd0\xb9\xd0\xb8\xd0\xbd\xd0\xba\xd0\xb8\0"
	"\xd0\x96\xd0\xb5\xd0\xba\0"
	"\xd0\x94\xd2\xaf\xd0\xb9\0"
	"\xd0\xa8\xd0\xb5\xd0\xb9\0"
	"\xd0\xa8\xd0\xb0\xd1\x80\0"
	"\xd0\x91\xd0\xb5\xd0\xb9\0"
	"\xd0\x96\xd1\x83\xd0\xbc\0"
	"\xd0\x98\xd1\x88\xd0\xbc\0"
	"\xd0\xa8\0"
	"\xd0\x98\0"
	"\xd1\x8f\xd0\xbd\xd0\xb2\xd0\xb0\xd1\x80\xd1\x8c\0"
	"\xd1\x84\xd0\xb5\xd0\xb2\xd1\x80\xd0\xb0\xd0\xbb\xd1\x8c\0"
	"\xd0\xb0\xd0\xbf\xd1\x80\xd0\xb5\xd0\xbb\xd1\x8c\0"
	"\xd0\xb8\xd1\x8e\xd0\xbd\xd1\x8c\0"
	"\xd0\xb8\xd1\x8e\xd0\xbb\xd1\x8c\0"
	"\xd1\x81\xd0\xb5\xd0\xbd\xd1\x82\xd1\x8f\xd0\xb1\xd1\x80\xd1\x8c\0"
	"\xd0\xbe\xd0\xba\xd1\x82\xd1\x8f\xd0\xb1\xd1\x80\xd1\x8c\0"
	"\xd0\xbd\xd0\xbe\xd1\x8f\xd0\xb1\xd1\x80\xd1\x8c\0"
	"\xd0\xb4\xd0\xb5\xd0\xba\xd0\xb0\xd0\xb1\xd1\x80\xd1\x8c\0"
	"\xd1\x8f\xd0\xbd\xd0\xb2.\0"
	"\xd0\xb8\xd1\x8e\xd0\xbd.\0"
	"\xd0\xb8\xd1\x8e\xd0\xbb.\0"
	"\xd1\x81\xd0\xb5\xd0\xbd.\0"
	"\xd0\xbd\xd0\xbe\xd1\x8f.\0"
	"d-MMM yy\0"
	"dd-MMMM yyyy'-\xd0\xb6.'\0"
	"MMMM yyyy'-\xd0\xb6.'\0"
	"Jumapili\0"
	"Jumatatu\0"
	"Jumanne\0"
	"Jumatano\0"
	"Alhamisi\0"
	"Ijumaa\0"
	"Jumamosi\0"
	"J2\0"
	"J3\0"
	"J4\0"
	"J5\0"
	"Alh\0"
	"Ij\0"
	"J1\0"
	"Machi\0"
	"Aprili\0"
	"Agosti\0"
	"Oktoba\0"
	"Desemba\0"
	"Ago\0"
	"yakshanba\0"
	"dushanba\0"
	"seshanba\0"
	"chorshanba\0"
	"payshanba\0"
	"juma\0"
	"shanba\0"
	"Yaksh\0"
	"Dush\0"
	"Sesh\0"
	"Chor\0"
	"Pay\0"
	"Shan\0"
	"Y\0"
	"Iyun\0"
	"Iyul\0"
	"Avgust\0"
	"Yanv\0"
	"Fev\0"
	"Avg\0"
	"Noya\0"
	"Dek\0"
	"dddd, yyyy MMMM dd\0"
	"\xe0\xa6\xaa\xe0\xa7\x82\xe0\xa6\xb0\xe0\xa7\x8d\xe0\xa6\xac\xe0\xa6\xbe\xe0\xa6\xb9\xe0\xa7\x8d\xe0\xa6\xa3\0"
	"\xe0\xa6\x85\xe0\xa6\xaa\xe0\xa6\xb0\xe0\xa6\xbe\xe0\xa6\xb9\xe0\xa7\x8d\xe0\xa6\xa3\0"
	"\xe0\xa6\xb0\xe0\xa6\xac\xe0\xa6\xbf\xe0\xa6\xac\xe0\xa6\xbe\xe0\xa6\xb0\0"
	"\xe0\xa6\xb8\xe0\xa7\x8b\xe0\xa6\xae\xe0\xa6\xac\xe0\xa6\xbe\xe0\xa6\xb0\0"
	"\xe0\xa6\xae\xe0\xa6\x99\xe0\xa7\x8d\xe0\xa6\x97\xe0\xa6\xb2\xe0\xa6\xac\xe0\xa6\xbe\xe0\xa6\xb0\0"
	"\xe0\xa6\xac\xe0\xa7\x81\xe0\xa6\xa7\xe0\xa6\xac\xe0\xa6\xbe\xe0\xa6\xb0\0"
	"\xe0\xa6\xac\xe0\xa7\x83\xe0\xa6\xb9\xe0\xa6\xb7\xe0\xa7\x8d\xe0\xa6\xaa\xe0\xa6\xa4\xe0\xa6\xbf\xe0\xa6\xac\xe0\xa6\xbe\xe0\xa6\xb0\0"
	"\xe0\xa6\xb6\xe0\xa7\x81\xe0\xa6\x95\xe0\xa7\x8d\xe0\xa6\xb0\xe0\xa6\xac\xe0\xa6\xbe\xe0\xa6\xb0\0"
	"\xe0\xa6\xb6\xe0\xa6\xa8\xe0\xa6\xbf\xe0\xa6\xac\xe0\xa6\xbe\xe0\xa6\xb0\0"
	"\xe0\xa6\xb0\xe0\xa6\xac\xe0\xa6\xbf\0"
	"\xe0\xa6\xb8\xe0\xa7\x8b\xe0\xa6\xae\0"
	"\xe0\xa6\xae\xe0\xa6\x99\xe0\xa7\x8d\xe0\xa6\x97\xe0\xa6\xb2\0"
	"\xe0\xa6\xac\xe0\xa7\x81\xe0\xa6\xa7\0"
	"\xe0\xa6\xac\xe0\xa7\x83\xe0\xa6\xb9\xe0\xa6\xb8\xe0\xa7\x8d\xe0\xa6\xaa\xe0\xa6\xa4\xe0\xa6\xbf\0"
	"\xe0\xa6\xb6\xe0\xa7\x81\xe0\xa6\x95\xe0\xa7\x8d\xe0\xa6\xb0\0"
	"\xe0\xa6\xb6\xe0\xa6\xa8\xe0\xa6\xbf\0"
	"\xe0\xa6\xb0\0"
	"\xe0\xa6\xb8\xe0\xa7\x8b\0"
	"\xe0\xa6\xae\0"
	"\xe0\xa6\xac\xe0\xa7\x81\0"
	"\xe0\xa6\xac\xe0\xa7\x83\0"
	"\xe0\xa6\xb6\xe0\xa7\x81\0"
	"\xe0\xa6\xb6\0"
	"\xe0\xa6\x9c\xe0\xa6\xbe\xe0\xa6\xa8\xe0\xa7\x81\xe0\xa6\xaf\xe0\xa6\xbc\xe0\xa6\xbe\xe0\xa6\xb0\xe0\xa7\x80\0"
	"\xe0\xa6\xab\xe0\xa7\x87\xe0\xa6\xac\xe0\xa7\x8d\xe0\xa6\xb0\xe0\xa7\x81\xe0\xa6\xaf\xe0\xa6\xbc\xe0\xa6\xbe\xe0\xa6\xb0\xe0\xa7\x80\0"
	"\xe0\xa6\xae\xe0\xa6\xbe\xe0\xa6\xb0\xe0\xa7\x8d\xe0\xa6\x9a\0"
	"\xe0\xa6\x8f\xe0\xa6\xaa\xe0\xa7\x8d\xe0\xa6\xb0\xe0\xa6\xbf\xe0\xa6\xb2\0"
	"\xe0\xa6\xae\xe0\xa7\x87\0"
	"\xe0\xa6\x9c\xe0\xa7\x81\xe0\xa6\xa8\0"
	"\xe0\xa6\x9c\xe0\xa7\x81\xe0\xa6\xb2\xe0\xa6\xbe\xe0\xa6\x87\0"
	"\xe0\xa6\x86\xe0\xa6\x97\xe0\xa6\xb8\xe0\xa7\x8d\xe0\xa6\x9f\0"
	"\xe0\xa6\xb8\xe0\xa7\x87\xe0\xa6\xaa\xe0\xa7\x8d\xe0\xa6\x9f\xe0\xa7\x87\xe0\xa6\xae\xe0\xa7\x8d\xe0\xa6\xac\xe0\xa6\xb0\0"
	"\xe0\xa6\x85\xe0\xa6\x95\xe0\xa7\x8d\xe0\xa6\x9f\xe0\xa7\x8b\xe0\xa6\xac\xe0\xa6\xb0\0"
	"\xe0\xa6\xa8\xe0\xa6\xad\xe0\xa7\x87\xe0\xa6\xae\xe0\xa7\x8d\xe0\xa6\xac\xe0\xa6\xb0\0"
	"\xe0\xa6\xa1\xe0\xa6\xbf\xe0\xa6\xb8\xe0\xa7\x87\xe0\xa6\xae\xe0\xa7\x8d\xe0\xa6\xac\xe0\xa6\xb0\0"
	"tt hh.mm\0"
	"tt h.mm\0"
	"tt hh.mm.ss\0"
	"tt h.mm.ss\0"
	"\xe0\xa8\x90\xe0\xa8\xa4\xe0\xa8\xb5\xe0\xa8\xbe\xe0\xa8\xb0\0"
	"\xe0\xa8\xb8\xe0\xa9\x8b\xe0\xa8\xae\xe0\xa8\xb5\xe0\xa8\xbe\xe0\xa8\xb0\0"
	"\xe0\xa8\xae\xe0\xa9\xb0\xe0\xa8\x97\xe0\xa8\xb2\xe0\xa8\xb5\xe0\xa8\xbe\xe0\xa8\xb0\0"
	"\xe0\xa8\xac\xe0\xa9\x81\xe0\xa8\xa7\xe0\xa8\xb5\xe0\xa8\xbe\xe0\xa8\xb0\0"
	"\xe0\xa8\xb5\xe0\xa9\x80\xe0\xa8\xb0\xe0\xa8\xb5\xe0\xa8\xbe\xe0\xa8\xb0\0"
	"\xe0\xa8\xb8\xe0\xa8\xbc\xe0\xa9\x81\xe0\xa9\xb1\xe0\xa8\x95\xe0\xa8\xb0\xe0\xa8\xb5\xe0\xa8\xbe\xe0\xa8\xb0\0"
	"\xe0\xa8\xb8\xe0\xa8\xbc\xe0\xa8\xa8\xe0\xa9\x80\xe0\xa8\xb5\xe0\xa8\xbe\xe0\xa8\xb0\0"
	"\xe0\xa8\x90\xe0\xa8\xa4.\0"
	"\xe0\xa8\xb8\xe0\xa9\x8b\xe0\xa8\xae.\0"
	"\xe0\xa8\xae\xe0\xa9\xb0\xe0\xa8\x97\xe0\xa8\xb2.\0"
	"\xe0\xa8\xac\xe0\xa9\x81\xe0\xa8\xa7.\0"
	"\xe0\xa8\xb5\xe0\xa9\x80\xe0\xa8\xb0.\0"
	"\xe0\xa8\xb8\xe0\xa8\xbc\xe0\xa9\x81\xe0\xa9\xb1\xe0\xa8\x95\xe0\xa8\xb0.\0"
	"\xe0\xa8\xb8\xe0\xa8\xbc\xe0\xa8\xa8\xe0\xa9\x80.\0"
	"\xe0\xa8\x90\0"
	"\xe0\xa8\xb8\xe0\xa9\x8b\0"
	"\xe0\xa8\xae\xe0\xa9\xb0\0"
	"\xe0\xa8\xac\xe0\xa9\x81\xe0\xa9\xb1\0"
	"\xe0\xa8\xb5\xe0\xa9\x80\0"
	"\xe0\xa8\xb8\xe0\xa8\xbc\xe0\xa9\x81\xe0\xa9\xb1\0"
	"\xe0\xa8\xb8\xe0\xa8\xbc\0"
	"\xe0\xa8\x9c\xe0\xa8\xa8\xe0\xa8\xb5\xe0\xa8\xb0\xe0\xa9\x80\0"
	"\xe0\xa8\xab\xe0\xa8\xbc\xe0\xa8\xb0\xe0\xa8\xb5\xe0\xa8\xb0\xe0\xa9\x80\0"
	"\xe0\xa8\xae\xe0\xa8\xbe\xe0\xa8\xb0\xe0\xa8\x9a\0"
	"\xe0\xa8\x85\xe0\xa8\xaa\xe0\xa9\x8d\xe0\xa8\xb0\xe0\xa9\x88\xe0\xa8\xb2\0"
	"\xe0\xa8\xae\xe0\xa8\x88\0"
	"\xe0\xa8\x9c\xe0\xa9\x82\xe0\xa8\xa8\0"
	"\xe0\xa8\x9c\xe0\xa9\x81\xe0\xa8\xb2\xe0\xa8\xbe\xe0\xa8\x88\0"
	"\xe0\xa8\x85\xe0\xa8\x97\xe0\xa8\xb8\xe0\xa8\xa4\0"
	"\xe0\xa8\xb8\xe0\xa8\xa4\xe0\xa9\xb0\xe0\xa8\xac\xe0\xa8\xb0\0"
	"\xe0\xa8\x85\xe0\xa8\x95\xe0\xa8\xa4\xe0\xa9\x82\xe0\xa8\xac\xe0\xa8\xb0\0"
	"\xe0\xa8\xa8\xe0\xa8\xb5\xe0\xa9\xb0\xe0\xa8\xac\xe0\xa8\xb0\0"
	"\xe0\xa8\xa6\xe0\xa8\xb8\xe0\xa9\xb0\xe0\xa8\xac\xe0\xa8\xb0\0"
	"dd MMMM yyyy dddd\0"
	"\xe0\xaa\xb0\xe0\xaa\xb5\xe0\xaa\xbf\xe0\xaa\xb5\xe0\xaa\xbe\xe0\xaa\xb0\0"
	"\xe0\xaa\xb8\xe0\xab\x8b\xe0\xaa\xae\xe0\xaa\xb5\xe0\xaa\xbe\xe0\xaa\xb0\0"
	"\xe0\xaa\xae\xe0\xaa\x82\xe0\xaa\x97\xe0\xaa\xb3\xe0\xaa\xb5\xe0\xaa\xbe\xe0\xaa\xb0\0"
	"\xe0\xaa\xac\xe0\xab\x81\xe0\xaa\xa7\xe0\xaa\xb5\xe0\xaa\xbe\xe0\xaa\xb0\0"
	"\xe0\xaa\x97\xe0\xab\x81\xe0\xaa\xb0\xe0\xab\x81\xe0\xaa\xb5\xe0\xaa\xbe\xe0\xaa\xb0\0"
	"\xe0\xaa\xb6\xe0\xab\x81\xe0\xaa\x95\xe0\xab\x8d\xe0\xaa\xb0\xe0\xaa\xb5\xe0\xaa\xbe\xe0\xaa\xb0\0"
	"\xe0\xaa\xb6\xe0\xaa\xa8\xe0\xaa\xbf\xe0\xaa\xb5\xe0\xaa\xbe\xe0\xaa\xb0\0"
	"\xe0\xaa\xb0\xe0\xaa\xb5\xe0\xaa\xbf\0"
	"\xe0\xaa\xb8\xe0\xab\x8b\xe0\xaa\xae\0"
	"\xe0\xaa\xae\xe0\xaa\x82\xe0\xaa\x97\xe0\xaa\xb3\0"
	"\xe0\xaa\xac\xe0\xab\x81\xe0\xaa\xa7\0"
	"\xe0\xaa\x97\xe0\xab\x81\xe0\xaa\xb0\xe0\xab\x81\0"
	"\xe0\xaa\xb6\xe0\xab\x81\xe0\xaa\x95\xe0\xab\x8d\xe0\xaa\xb0\0"
	"\xe0\xaa\xb6\xe0\xaa\xa8\xe0\xaa\xbf\0"
	"\xe0\xaa\xb0\0"
	"\xe0\xaa\xb8\xe0\xab\x8b\0"
	"\xe0\xaa\xae\xe0\xaa\x82\0"
	"\xe0\xaa\xac\xe0\xab\x81\0"
	"\xe0\xaa\x97\xe0\xab\x81\0"
	"\xe0\xaa\xb6\xe0\xab\x81\0"
	"\xe0\xaa\xb6\0"
	"\xe0\xaa\x9c\xe0\xaa\xbe\xe0\xaa\xa8\xe0\xab\x8d\xe0\xaa\xaf\xe0\xab\x81\xe0\xaa\x86\xe0\xaa\xb0\xe0\xab\x80\0"
	"\xe0\xaa\xab\xe0\xab\x87\xe0\xaa\xac\xe0\xab\x8d\xe0\xaa\xb0\xe0\xab\x81\xe0\xaa\x86\xe0\xaa\xb0\xe0\xab\x80\0"
	"\xe0\xaa\xae\xe0\xaa\xbe\xe0\xaa\xb0\xe0\xab\x8d\xe0\xaa\x9a\0"
	"\xe0\xaa\x8f\xe0\xaa\xaa\xe0\xab\x8d\xe0\xaa\xb0\xe0\xaa\xbf\xe0\xaa\xb2\0"
	"\xe0\xaa\xae\xe0\xab\x87\0"
	"\xe0\xaa\x9c\xe0\xab\x82\xe0\xaa\xa8\0"
	"\xe0\xaa\x9c\xe0\xab\x81\xe0\xaa\xb2\xe0\xaa\xbe\xe0\xaa\x88\0"
	"\xe0\xaa\x91\xe0\xaa\x97\xe0\xaa\xb8\xe0\xab\x8d\xe0\xaa\x9f\0"
	"\xe0\xaa\xb8\xe0\xaa\xaa\xe0\xab\x8d\xe0\xaa\x9f\xe0\xab\x87\xe0\xaa\xae\xe0\xab\x8d\xe0\xaa\xac\xe0\xaa\xb0\0"
	"\xe0\xaa\x91\xe0\xaa\x95\xe0\xab\x8d\xe0\xaa\x9f\xe0\xab\x8b\xe0\xaa\xac\xe0\xaa\xb0\0"
	"\xe0\xaa\xa8\xe0\xaa\xb5\xe0\xab\x87\xe0\xaa\xae\xe0\xab\x8d\xe0\xaa\xac\xe0\xaa\xb0\0"
	"\xe0\xaa\xa1\xe0\xaa\xbf\xe0\xaa\xb8\xe0\xab\x87\xe0\xaa\xae\xe0\xab\x8d\xe0\xaa\xac\xe0\xaa\xb0\0"
	"\xe0\xaa\x9c\xe0\xaa\xbe\xe0\xaa\xa8\xe0\xab\x8d\xe0\xaa\xaf\xe0\xab\x81\0"
	"\xe0\xaa\xab\xe0\xab\x87\xe0\xaa\xac\xe0\xab\x8d\xe0\xaa\xb0\xe0\xab\x81\0"
	"\xe0\xaa\x91\xe0\xaa\x97\0"
	"\xe0\xaa\xb8\xe0\xaa\xaa\xe0\xab\x8d\xe0\xaa\x9f\xe0\xab\x87\0"
	"\xe0\xaa\x91\xe0\xaa\x95\xe0\xab\x8d\xe0\xaa\x9f\xe0\xab\x8b\0"
	"\xe0\xaa\xa8\xe0\xaa\xb5\xe0\xab\x87\0"
	"\xe0\xaa\xa1\xe0\xaa\xbf\xe0\xaa\xb8\xe0\xab\x87\0"
	"pm\0"
	"\xe0\xac\xb0\xe0\xac\xac\xe0\xac\xbf\xe0\xac\xac\xe0\xac\xbe\xe0\xac\xb0\0"
	"\xe0\xac\xb8\xe0\xad\x8b\xe0\xac\xae\xe0\xac\xac\xe0\xac\xbe\xe0\xac\xb0\0"
	"\xe0\xac\xae\xe0\xac\x99\xe0\xad\x8d\xe0\xac\x97\xe0\xac\xb3\xe0\xac\xac\xe0\xac\xbe\xe0\xac\xb0\0"
	"\xe0\xac\xac\xe0\xad\x81\xe0\xac\xa7\xe0\xac\xac\xe0\xac\xbe\xe0\xac\xb0\0"
	"\xe0\xac\x97\xe0\xad\x81\xe0\xac\xb0\xe0\xad\x81\xe0\xac\xac\xe0\xac\xbe\xe0\xac\xb0\0"
	"\xe0\xac\xb6\xe0\xad\x81\xe0\xac\x95\xe0\xad\x8d\xe0\xac\xb0\xe0\xac\xac\xe0\xac\xbe\xe0\xac\xb0\0"
	"\xe0\xac\xb6\xe0\xac\xa8\xe0\xac\xbf\xe0\xac\xac\xe0\xac\xbe\xe0\xac\xb0\0"
	"\xe0\xac\xb0\xe0\xac\xac\xe0\xac\xbf\0"
	"\xe0\xac\xb8\xe0\xad\x8b\xe0\xac\xae\0"
	"\xe0\xac\xae\xe0\xac\x99\xe0\xad\x8d\xe0\xac\x97\xe0\xac\xb3\0"
	"\xe0\xac\xac\xe0\xad\x81\xe0\xac\xa7\0"
	"\xe0\xac\x97\xe0\xad\x81\xe0\xac\xb0\xe0\xad\x81\0"
	"\xe0\xac\xb6\xe0\xad\x81\xe0\xac\x95\xe0\xad\x8d\xe0\xac\xb0\0"
	"\xe0\xac\xb6\xe0\xac\xa8\xe0\xac\xbf\0"
	"\xe0\xac\xb0\0"
	"\xe0\xac\xb8\xe0\xad\x8b\0"
	"\xe0\xac\xae\0"
	"\xe0\xac\xac\xe0\xad\x81\0"
	"\xe0\xac\x97\xe0\xad\x81\0"
	"\xe0\xac\xb6\xe0\xad\x81\0"
	"\xe0\xac\xb6\0"
	"\xe0\xac\x9c\xe0\xac\xbe\xe0\xac\xa8\xe0\xad\x81\xe0\xac\x86\xe0\xac\xb0\xe0\xad\x80\0"
	"\xe0\xac\xab\xe0\xad\x87\xe0\xac\xac\xe0\xad\x8d\xe0\xac\xb0\xe0\xad\x81\xe0\xad\x9f\xe0\xac\xbe\xe0\xac\xb0\xe0\xad\x80\0"
	"\xe0\xac\xae\xe0\xac\xbe\xe0\xac\xb0\xe0\xad\x8d\xe0\xac\x9a\xe0\xad\x8d\xe0\xac\x9a\0"
	"\xe0\xac\x85\xe0\xac\xaa\xe0\xad\x8d\xe0\xac\xb0\xe0\xad\x87\xe0\xac\xb2\0"
	"\xe0\xac\xae\xe0\xad\x87\0"
	"\xe0\xac\x9c\xe0\xad\x81\xe0\xac\xa8\0"
	"\xe0\xac\x9c\xe0\xad\x81\xe0\xac\xb2\xe0\xac\xbe\xe0\xac\x87\0"
	"\xe0\xac\x85\xe0\xac\x97\xe0\xac\xb7\xe0\xad\x8d\xe0\xac\x9f\0"
	"\xe0\xac\xb8\xe0\xad\x87\xe0\xac\xaa\xe0\xad\x8d\xe0\xac\x9f\xe0\xad\x87\xe0\xac\xae\xe0\xad\x8d\xe0\xac\xac\xe0\xac\xb0\0"
	"\xe0\xac\x85\xe0\xac\x95\xe0\xad\x8d\xe0\xac\x9f\xe0\xad\x8b\xe0\xac\xac\xe0\xac\xb0\0"
	"\xe0\xac\xa8\xe0\xac\xad\xe0\xad\x87\xe0\xac\xae\xe0\xad\x8d\xe0\xac\xac\xe0\xac\xb0\0"
	"\xe0\xac\xa1\xe0\xac\xbf\xe0\xac\xb8\xe0\xad\x87\xe0\xac\xae\xe0\xad\x8d\xe0\xac\xac\xe0\xac\xb0\0"
	"\xe0\xae\xae\xe0\xaf\x81\xe0\xae\xb1\xe0\xaf\x8d\xe0\xae\xaa\xe0\xae\x95\xe0\xae\xb2\xe0\xaf\x8d\0"
	"\xe0\xae\xaa\xe0\xae\xbf\xe0\xae\xb1\xe0\xaf\x8d\xe0\xae\xaa\xe0\xae\x95\xe0\xae\xb2\xe0\xaf\x8d\0"
	"\xe0\xae\x9e\xe0\xae\xbe\xe0\xae\xaf\xe0\xae\xbf\xe0\xae\xb1\xe0\xaf\x81\0"
	"\xe0\xae\xa4\xe0\xae\xbf\xe0\xae\x99\xe0\xaf\x8d\xe0\xae\x95\xe0\xae\xb3\xe0\xaf\x8d\0"
	"\xe0\xae\x9a\xe0\xaf\x86\xe0\xae\xb5\xe0\xaf\x8d\xe0\xae\xb5\xe0\xae\xbe\xe0\xae\xaf\xe0\xaf\x8d\0"
	"\xe0\xae\xaa\xe0\xaf\x81\xe0\xae\xa4\xe0\xae\xa9\xe0\xaf\x8d\0"
	"\xe0\xae\xb5\xe0\xae\xbf\xe0\xae\xaf\xe0\xae\xbe\xe0\xae\xb4\xe0\xae\xa9\xe0\xaf\x8d\0"
	"\xe0\xae\xb5\xe0\xaf\x86\xe0\xae\xb3\xe0\xaf\x8d\xe0\xae\xb3\xe0\xae\xbf\0"
	"\xe0\xae\x9a\xe0\xae\xa9\xe0\xae\xbf\0"
	"\xe0\xae\x9e\xe0\xae\xbe\0"
	"\xe0\xae\xa4\xe0\xae\xbf\0"
	"\xe0\xae\x9a\xe0\xaf\x86\0"
	"\xe0\xae\xaa\xe0\xaf\x81\0"
	"\xe0\xae\xb5\xe0\xae\xbf\0"
	"\xe0\xae\xb5\xe0\xaf\x86\0"
	"\xe0\xae\x9a\0"
	"\xe0\xae\x9c\xe0\xae\xa9\xe0\xae\xb5\xe0\xae\xb0\xe0\xae\xbf\0"
	"\xe0\xae\xaa\xe0\xae\xbf\xe0\xae\xaa\xe0\xaf\x8d\xe0\xae\xb0\xe0\xae\xb5\xe0\xae\xb0\xe0\xae\xbf\0"
	"\xe0\xae\xae\xe0\xae\xbe\xe0\xae\xb0\xe0\xaf\x8d\xe0\xae\x9a\xe0\xaf\x8d\0"
	"\xe0\xae\x8f\xe0\xae\xaa\xe0\xaf\x8d\xe0\xae\xb0\xe0\xae\xb2\xe0\xaf\x8d\0"
	"\xe0\xae\xae\xe0\xaf\x87\0"
	"\xe0\xae\x9c\xe0\xaf\x82\xe0\xae\xa9\xe0\xaf\x8d\0"
	"\xe0\xae\x9c\xe0\xaf\x82\xe0\xae\xb2\xe0\xaf\x88\0"
	"\xe0\xae\x86\xe0\xae\x95\xe0\xae\xb8\xe0\xaf\x8d\xe0\xae\x9f\xe0\xaf\x81\0"
	"\xe0\xae\x9a\xe0\xaf\x86\xe0\xae\xaa\xe0\xaf\x8d\xe0\xae\x9f\xe0\xae\xae\xe0\xaf\x8d\xe0\xae\xaa\xe0\xae\xb0\xe0\xaf\x8d\0"
	"\xe0\xae\x85\xe0\xae\x95\xe0\xaf\x8d\xe0\xae\x9f\xe0\xaf\x8b\xe0\xae\xaa\xe0\xae\xb0\xe0\xaf\x8d\0"
	"\xe0\xae\xa8\xe0\xae\xb5\xe0\xae\xae\xe0\xaf\x8d\xe0\xae\xaa\xe0\xae\xb0\xe0\xaf\x8d\0"
	"\xe0\xae\x9f\xe0\xae\xbf\xe0\xae\x9a\xe0\xae\xae\xe0\xaf\x8d\xe0\xae\xaa\xe0\xae\xb0\xe0\xaf\x8d\0"
	"\xe0\xae\x86\xe0\xae\x95\xe0\xae\xb8\xe0\xaf\x8d\xe0\xae\x9f\xe0\xaf\x8d\0"
	"\xe0\xae\x9c\xe0\xae\xa9.\0"
	"\xe0\xae\xaa\xe0\xae\xbf\xe0\xae\xaa\xe0\xaf\x8d.\0"
	"\xe0\xae\xae\xe0\xae\xbe\xe0\xae\xb0\xe0\xaf\x8d.\0"
	"\xe0\xae\x8f\xe0\xae\xaa\xe0\xaf\x8d.\0"
	"\xe0\xae\x86\xe0\xae\x95.\0"
	"\xe0\xae\x9a\xe0\xaf\x86\xe0\xae\xaa\xe0\xaf\x8d.\0"
	"\xe0\xae\x85\xe0\xae\x95\xe0\xaf\x8d.\0"
	"\xe0\xae\xa8\xe0\xae\xb5.\0"
	"\xe0\xae\x9f\xe0\xae\xbf\xe0\xae\x9a.\0"
	"\xe0\xb0\x86\xe0\xb0\xa6\xe0\xb0\xbf\xe0\xb0\xb5\xe0\xb0\xbe\xe0\xb0\xb0\xe0\xb0\x82\0"
	"\xe0\xb0\xb8\xe0\xb1\x8b\xe0\xb0\xae\xe0\xb0\xb5\xe0\xb0\xbe\xe0\xb0\xb0\xe0\xb0\x82\0"
	"\xe0\xb0\xae\xe0\xb0\x82\xe0\xb0\x97\xe0\xb0\xb3\xe0\xb0\xb5\xe0\xb0\xbe\xe0\xb0\xb0\xe0\xb0\x82\0"
	"\xe0\xb0\xac\xe0\xb1\x81\xe0\xb0\xa7\xe0\xb0\xb5\xe0\xb0\xbe\xe0\xb0\xb0\xe0\xb0\x82\0"
	"\xe0\xb0\x97\xe0\xb1\x81\xe0\xb0\xb0\xe0\xb1\x81\xe0\xb0\xb5\xe0\xb0\xbe\xe0\xb0\xb0\xe0\xb0\x82\0"
	"\xe0\xb0\xb6\xe0\xb1\x81\xe0\xb0\x95\xe0\xb1\x8d\xe0\xb0\xb0\xe0\xb0\xb5\xe0\xb0\xbe\xe0\xb0\xb0\xe0\xb0\x82\0"
	"\xe0\xb0\xb6\xe0\xb0\xa8\xe0\xb0\xbf\xe0\xb0\xb5\xe0\xb0\xbe\xe0\xb0\xb0\xe0\xb0\x82\0"
	"\xe0\xb0\x86\xe0\xb0\xa6\xe0\xb0\xbf\0"
	"\xe0\xb0\xb8\xe0\xb1\x8b\xe0\xb0\xae\0"
	"\xe0\xb0\xae\xe0\xb0\x82\xe0\xb0\x97\xe0\xb0\xb3\0"
	"\xe0\xb0\xac\xe0\xb1\x81\xe0\xb0\xa7\0"
	"\xe0\xb0\x97\xe0\xb1\x81\xe0\xb0\xb0\xe0\xb1\x81\0"
	"\xe0\xb0\xb6\xe0\xb1\x81\xe0\xb0\x95\xe0\xb1\x8d\xe0\xb0\xb0\0"
	"\xe0\xb0\xb6\xe0\xb0\xa8\xe0\xb0\xbf\0"
	"\xe0\xb0\x86\0"
	"\xe0\xb0\xb8\xe0\xb1\x8b\0"
	"\xe0\xb0\xae\0"
	"\xe0\xb0\xac\xe0\xb1\x81\0"
	"\xe0\xb0\x97\xe0\xb1\x81\0"
	"\xe0\xb0\xb6\xe0\xb1\x81\0"
	"\xe0\xb0\xb6\0"
	"\xe0\xb0\x9c\xe0\xb0\xa8\xe0\xb0\xb5\xe0\xb0\xb0\xe0\xb0\xbf\0"
	"\xe0\xb0\xab\xe0\xb0\xbf\xe0\xb0\xac\xe0\xb1\x8d\xe0\xb0\xb0\xe0\xb0\xb5\xe0\xb0\xb0\xe0\xb0\xbf\0"
	"\xe0\xb0\xae\xe0\xb0\xbe\xe0\xb0\xb0\xe0\xb1\x8d\xe0\xb0\x9a\xe0\xb0\xbf\0"
	"\xe0\xb0\x8e\xe0\xb0\xaa\xe0\xb1\x8d\xe0\xb0\xb0\xe0\xb0\xbf\xe0\xb0\xb2\xe0\xb1\x8d\0"
	"\xe0\xb0\xae\xe0\xb1\x87\0"
	"\xe0\xb0\x9c\xe0\xb1\x82\xe0\xb0\xa8\xe0\xb1\x8d\0"
	"\xe0\xb0\x9c\xe0\xb1\x82\xe0\xb0\xb2\xe0\xb1\x88\0"
	"\xe0\xb0\x86\xe0\xb0\x97\xe0\xb0\xb8\xe0\xb1\x8d\xe0\xb0\x9f\xe0\xb1\x81\0"
	"\xe0\xb0\xb8\xe0\xb1\x86\xe0\xb0\xaa\xe0\xb1\x8d\xe0\xb0\x9f\xe0\xb1\x86\xe0\xb0\x82\xe0\xb0\xac\xe0\xb0\xb0\xe0\xb1\x8d\0"
	"\xe0\xb0\x85\xe0\xb0\x95\xe0\xb1\x8d\xe0\xb0\x9f\xe0\xb1\x8b\xe0\xb0\xac\xe0\xb0\xb0\xe0\xb1\x8d\0"
	"\xe0\xb0\xa8\xe0\xb0\xb5\xe0\xb0\x82\xe0\xb0\xac\xe0\xb0\xb0\xe0\xb1\x8d\0"
	"\xe0\xb0\xa1\xe0\xb0\xbf\xe0\xb0\xb8\xe0\xb1\x86\xe0\xb0\x82\xe0\xb0\xac\xe0\xb0\xb0\xe0\xb1\x8d\0"
	"\xe0\xb0\x9c\xe0\xb1\x81\xe0\xb0\xb2\xe0\xb1\x88\0"
	"\xe0\xb0\x9c\xe0\xb0\xa8\0"
	"\xe0\xb0\xab\xe0\xb0\xbf\xe0\xb0\xac\xe0\xb1\x8d\xe0\xb0\xb0\0"
	"\xe0\xb0\x8f\xe0\xb0\xaa\xe0\xb1\x8d\xe0\xb0\xb0\xe0\xb0\xbf\0"
	"\xe0\xb0\xb8\xe0\xb1\x86\xe0\xb0\xaa\xe0\xb1\x8d\xe0\xb0\x9f\xe0\xb1\x86\xe0\xb0\x82\0"
	"\xe0\xb0\x85\xe0\xb0\x95\xe0\xb1\x8d\xe0\xb0\x9f\xe0\xb1\x8b\0"
	"\xe0\xb0\xa8\xe0\xb0\xb5\xe0\xb0\x82\0"
	"\xe0\xb0\xa1\xe0\xb0\xbf\xe0\xb0\xb8\xe0\xb1\x86\xe0\xb0\x82\0"
	"\xe0\xb2\xb0\xe0\xb2\xb5\xe0\xb2\xbf\xe0\xb2\xb5\xe0\xb2\xbe\xe0\xb2\xb0\0"
	"\xe0\xb2\xb8\xe0\xb3\x8b\xe0\xb2\xae\xe0\xb2\xb5\xe0\xb2\xbe\xe0\xb2\xb0\0"
	"\xe0\xb2\xae\xe0\xb2\x82\xe0\xb2\x97\xe0\xb2\xb3\xe0\xb2\xb5\xe0\xb2\xbe\xe0\xb2\xb0\0"
	"\xe0\xb2\xac\xe0\xb3\x81\xe0\xb2\xa7\xe0\xb2\xb5\xe0\xb2\xbe\xe0\xb2\xb0\0"
	"\xe0\xb2\x97\xe0\xb3\x81\xe0\xb2\xb0\xe0\xb3\x81\xe0\xb2\xb5\xe0\xb2\xbe\xe0\xb2\xb0\0"
	"\xe0\xb2\xb6\xe0\xb3\x81\xe0\xb2\x95\xe0\xb3\x8d\xe0\xb2\xb0\xe0\xb2\xb5\xe0\xb2\xbe\xe0\xb2\xb0\0"
	"\xe0\xb2\xb6\xe0\xb2\xa8\xe0\xb2\xbf\xe0\xb2\xb5\xe0\xb2\xbe\xe0\xb2\xb0\0"
	"\xe0\xb2\xb0\xe0\xb2\xb5\xe0\xb2\xbf\0"
	"\xe0\xb2\xb8\xe0\xb3\x8b\xe0\xb2\xae\0"
	"\xe0\xb2\xae\xe0\xb2\x82\xe0\xb2\x97\xe0\xb2\xb3\0"
	"\xe0\xb2\xac\xe0\xb3\x81\xe0\xb2\xa7\0"
	"\xe0\xb2\x97\xe0\xb3\x81\xe0\xb2\xb0\xe0\xb3\x81\0"
	"\xe0\xb2\xb6\xe0\xb3\x81\xe0\xb2\x95\xe0\xb3\x8d\xe0\xb2\xb0\0"
	"\xe0\xb2\xb6\xe0\xb2\xa8\xe0\xb2\xbf\0"
	"\xe0\xb2\xb0\0"
	"\xe0\xb2\xb8\xe0\xb3\x8b\0"
	"\xe0\xb2\xae\xe0\xb2\x82\0"
	"\xe0\xb2\xac\xe0\xb3\x81\0"
	"\xe0\xb2\x97\xe0\xb3\x81\0"
	"\xe0\xb2\xb6\xe0\xb3\x81\0"
	"\xe0\xb2\xb6\0"
	"\xe0\xb2\x9c\xe0\xb2\xa8\xe0\xb2\xb5\xe0\xb2\xb0\xe0\xb2\xbf\0"
	"\xe0\xb2\xab\xe0\xb3\x86\xe0\xb2\xac\xe0\xb3\x8d\xe0\xb2\xb0\xe0\xb2\xb5\xe0\xb2\xb0\xe0\xb2\xbf\0"
	"\xe0\xb2\xae\xe0\xb2\xbe\xe0\xb2\xb0\xe0\xb3\x8d\xe0\xb2\x9a\xe0\xb3\x8d\0"
	"\xe0\xb2\x8f\xe0\xb2\xaa\xe0\xb3\x8d\xe0\xb2\xb0\xe0\xb2\xbf\xe0\xb2\xb2\xe0\xb3\x8d\0"
	"\xe0\xb2\xae\xe0\xb3\x87\0"
	"\xe0\xb2\x9c\xe0\xb3\x82\xe0\xb2\xa8\xe0\xb3\x8d\0"
	"\xe0\xb2\x9c\xe0\xb3\x81\xe0\xb2\xb2\xe0\xb3\x88\0"
	"\xe0\xb2\x86\xe0\xb2\x97\xe0\xb2\xb8\xe0\xb3\x8d\xe0\xb2\x9f\xe0\xb3\x8d\0"
	"\xe0\xb2\xb8\xe0\xb2\xaa\xe0\xb3\x8d\xe0\xb2\x9f\xe0\xb3\x86\xe0\xb2\x82\xe0\xb2\xac\xe0\xb2\xb0\xe0\xb3\x8d\0"
	"\xe0\xb2\x85\xe0\xb2\x95\xe0\xb3\x8d\xe0\xb2\x9f\xe0\xb3\x8b\xe0\xb2\xac\xe0\xb2\xb0\xe0\xb3\x8d\0"
	"\xe0\xb2\xa8\xe0\xb2\xb5\xe0\xb3\x86\xe0\xb2\x82\xe0\xb2\xac\xe0\xb2\xb0\xe0\xb3\x8d\0"
	"\xe0\xb2\xa1\xe0\xb2\xbf\xe0\xb2\xb8\xe0\xb3\x86\xe0\xb2\x82\xe0\xb2\xac\xe0\xb2\xb0\xe0\xb3\x8d\0"
	"\xe0\xb2\x9c\xe0\xb2\xa8.\0"
	"\xe0\xb2\xab\xe0\xb3\x86\xe0\xb2\xac\xe0\xb3\x8d\xe0\xb2\xb0\xe0\xb3\x81.\0"
	"\xe0\xb2\xae\xe0\xb2\xbe\0"
	"\xe0\xb2\x8f\xe0\xb2\xaa\xe0\xb3\x8d\xe0\xb2\xb0\xe0\xb2\xbf.\0"
	"\xe0\xb2\x9c\xe0\xb3\x82\0"
	"\xe0\xb2\x9c\xe0\xb3\x81.\0"
	"\xe0\xb2\x86\xe0\xb2\x97.\0"
	"\xe0\xb2\xb8\xe0\xb3\x86\xe0\xb2\xaa\xe0\xb3\x8d\xe0\xb2\x9f\xe0\xb3\x86\xe0\xb2\x82.\0"
	"\xe0\xb2\x85\xe0\xb2\x95\xe0\xb3\x8d\xe0\xb2\x9f\xe0\xb3\x8b.\0"
	"\xe0\xb2\xa8\xe0\xb2\xb5\xe0\xb3\x86\xe0\xb2\x82.\0"
	"\xe0\xb2\xa1\xe0\xb2\xbf\xe0\xb2\xb8\xe0\xb3\x86\xe0\xb2\x82.\0"
	"MMMM dd\0"
	"\xe0\xb4\x9e\xe0\xb4\xbe\xe0\xb4\xaf\xe0\xb4\xb1\xe0\xb4\xbe\xe0\xb4\xb4\xe0\xb5\x8d\xe2\x80\x8c\xe0\xb4\x9a\0"
	"\xe0\xb4\xa4\xe0\xb4\xbf\xe0\xb4\x99\xe0\xb5\x8d\xe0\xb4\x95\xe0\xb4\xb3\xe0\xb4\xbe\xe0\xb4\xb4\xe0\xb5\x8d\xe2\x80\x8c\xe0\xb4\x9a\0"
	"\xe0\xb4\x9a\xe0\xb5\x8a\xe0\xb4\xb5\xe0\xb5\x8d\xe0\xb4\xb5\xe0\xb4\xbe\xe0\xb4\xb4\xe0\xb5\x8d\xe0\xb4\x9a\0"
	"\xe0\xb4\xac\xe0\xb5\x81\xe0\xb4\xa7\xe0\xb4\xa8\xe0\xb4\xbe\xe0\xb4\xb4\xe0\xb5\x8d\xe2\x80\x8c\xe0\xb4\x9a\0"
	"\xe0\xb4\xb5\xe0\xb5\x8d\xe0\xb4\xaf\xe0\xb4\xbe\xe0\xb4\xb4\xe0\xb4\xbe\xe0\xb4\xb4\xe0\xb5\x8d\xe2\x80\x8c\xe0\xb4\x9a\0"
	"\xe0\xb4\xb5\xe0\xb5\x86\xe0\xb4\xb3\xe0\xb5\x8d\xe0\xb4\xb3\xe0\xb4\xbf\xe0\xb4\xaf\xe0\xb4\xbe\xe0\xb4\xb4\xe0\xb5\x8d\xe2\x80\x8c\xe0\xb4\x9a\0"
	"\xe0\xb4\xb6\xe0\xb4\xa8\xe0\xb4\xbf\xe0\xb4\xaf\xe0\xb4\xbe\xe0\xb4\xb4\xe0\xb5\x8d\xe2\x80\x8c\xe0\xb4\x9a\0"
	"\xe0\xb4\x9e\xe0\xb4\xbe\xe0\xb4\xaf\xe0\xb5\xbc\0"
	"\xe0\xb4\xa4\xe0\xb4\xbf\xe0\xb4\x99\xe0\xb5\x8d\xe0\xb4\x95\xe0\xb5\xbe\0"
	"\xe0\xb4\x9a\xe0\xb5\x8a\xe0\xb4\xb5\xe0\xb5\x8d\xe0\xb4\xb5\0"
	"\xe0\xb4\xac\xe0\xb5\x81\xe0\xb4\xa7\xe0\xb5\xbb\0"
	"\xe0\xb4\xb5\xe0\xb5\x8d\xe0\xb4\xaf\xe0\xb4\xbe\xe0\xb4\xb4\xe0\xb4\x82\0"
	"\xe0\xb4\xb5\xe0\xb5\x86\xe0\xb4\xb3\xe0\xb5\x8d\xe0\xb4\xb3\xe0\xb4\xbf\0"
	"\xe0\xb4\xb6\xe0\xb4\xa8\xe0\xb4\xbf\0"
	"\xe0\xb4\x9e\xe0\xb4\xbe\0"
	"\xe0\xb4\xa4\xe0\xb4\xbf\0"
	"\xe0\xb4\x9a\xe0\xb5\x8a\0"
	"\xe0\xb4\xac\xe0\xb5\x81\0"
	"\xe0\xb4\xb5\xe0\xb5\x8d\xe0\xb4\xaf\xe0\xb4\xbe\0"
	"\xe0\xb4\xb5\xe0\xb5\x86\0"
	"\xe0\xb4\xb6\0"
	"\xe0\xb4\x9c\xe0\xb4\xa8\xe0\xb5\x81\xe0\xb4\xb5\xe0\xb4\xb0\xe0\xb4\xbf\0"
	"\xe0\xb4\xab\xe0\xb5\x86\xe0\xb4\xac\xe0\xb5\x8d\xe0\xb4\xb0\xe0\xb5\x81\xe0\xb4\xb5\xe0\xb4\xb0\xe0\xb4\xbf\0"
	"\xe0\xb4\xae\xe0\xb4\xbe\xe0\xb5\xbc\xe0\xb4\x9a\xe0\xb5\x8d\xe0\xb4\x9a\xe0\xb5\x8d\0"
	"\xe0\xb4\x8f\xe0\xb4\xaa\xe0\xb5\x8d\xe0\xb4\xb0\xe0\xb4\xbf\xe0\xb5\xbd\0"
	"\xe0\xb4\xae\xe0\xb5\x87\xe0\xb4\xaf\xe0\xb5\x8d\0"
	"\xe0\xb4\x9c\xe0\xb5\x82\xe0\xb5\xba\0"
	"\xe0\xb4\x9c\xe0\xb5\x82\xe0\xb4\xb2\xe0\xb5\x88\0"
	"\xe0\xb4\x86\xe0\xb4\x97\xe0\xb4\xb8\xe0\xb5\x8d\xe0\xb4\xb1\xe0\xb5\x8d\xe0\xb4\xb1\xe0\xb5\x8d\0"
	"\xe0\xb4\xb8\xe0\xb5\x86\xe0\xb4\xaa\xe0\xb5\x8d\xe0\xb4\xb1\xe0\xb5\x8d\xe0\xb4\xb1\xe0\xb4\x82\xe0\xb4\xac\xe0\xb5\xbc\0"
	"\xe0\xb4\x92\xe0\xb4\x95\xe0\xb5\x8d\xe2\x80\x8c\xe0\xb4\x9f\xe0\xb5\x8b\xe0\xb4\xac\xe0\xb5\xbc\0"
	"\xe0\xb4\xa8\xe0\xb4\xb5\xe0\xb4\x82\xe0\xb4\xac\xe0\xb5\xbc\0"
	"\xe0\xb4\xa1\xe0\xb4\xbf\xe0\xb4\xb8\xe0\xb4\x82\xe0\xb4\xac\xe0\xb5\xbc\0"
	"\xe0\xb4\x9c\xe0\xb4\xa8\xe0\xb5\x81\0"
	"\xe0\xb4\xab\xe0\xb5\x86\xe0\xb4\xac\xe0\xb5\x8d\xe0\xb4\xb0\xe0\xb5\x81\0"
	"\xe0\xb4\xae\xe0\xb4\xbe\xe0\xb5\xbc\0"
	"\xe0\xb4\x8f\xe0\xb4\xaa\xe0\xb5\x8d\xe0\xb4\xb0\xe0\xb4\xbf\0"
	"\xe0\xb4\x93\xe0\xb4\x97\0"
	"\xe0\xb4\xb8\xe0\xb5\x86\xe0\xb4\xaa\xe0\xb5\x8d\xe0\xb4\xb1\xe0\xb5\x8d\xe0\xb4\xb1\xe0\xb4\x82\0"
	"\xe0\xb4\x92\xe0\xb4\x95\xe0\xb5\x8d\xe0\xb4\x9f\xe0\xb5\x8b\0"
	"\xe0\xb4\xa8\xe0\xb4\xb5\xe0\xb4\x82\0"
	"\xe0\xb4\xa1\xe0\xb4\xbf\xe0\xb4\xb8\xe0\xb4\x82\0"
	"\xe0\xa6\xaa\xe0\xa7\x82\xe0\xa7\xb0\xe0\xa7\x8d\xe0\xa6\xac\xe0\xa6\xbe\xe0\xa6\xb9\xe0\xa7\x8d\xe0\xa6\xa3\0"
	"\xe0\xa6\x85\xe0\xa6\xaa\xe0\xa7\xb0\xe0\xa6\xbe\xe0\xa6\xb9\xe0\xa7\x8d\xe0\xa6\xa3\0"
	"\xe0\xa6\xa6\xe0\xa7\x87\xe0\xa6\x93\xe0\xa6\xac\xe0\xa6\xbe\xe0\xa7\xb0\0"
	"\xe0\xa6\xb8\xe0\xa7\x8b\xe0\xa6\xae\xe0\xa6\xac\xe0\xa6\xbe\xe0\xa7\xb0\0"
	"\xe0\xa6\xae\xe0\xa6\x99\xe0\xa7\x8d\xe0\xa6\x97\xe0\xa6\xb2\xe0\xa6\xac\xe0\xa6\xbe\xe0\xa7\xb0\0"
	"\xe0\xa6\xac\xe0\xa7\x81\xe0\xa6\xa7\xe0\xa6\xac\xe0\xa6\xbe\xe0\xa7\xb0\0"
	"\xe0\xa6\xac\xe0\xa7\x83\xe0\xa6\xb9\xe0\xa6\xb7\xe0\xa7\x8d\xe0\xa6\xaa\xe0\xa6\xa4\xe0\xa6\xbf\xe0\xa6\xac\xe0\xa6\xbe\xe0\xa7\xb0\0"
	"\xe0\xa6\xb6\xe0\xa7\x81\xe0\xa6\x95\xe0\xa7\x8d\xe0\xa7\xb0\xe0\xa6\xac\xe0\xa6\xbe\xe0\xa7\xb0\0"
	"\xe0\xa6\xb6\xe0\xa6\xa8\xe0\xa6\xbf\xe0\xa6\xac\xe0\xa6\xbe\xe0\xa7\xb0\0"
	"\xe0\xa7\xb0\xe0\xa6\xac\xe0\xa6\xbf\0"
	"\xe0\xa6\xac\xe0\xa7\x83\xe0\xa6\xb9\xe0\xa6\xb7\xe0\xa7\x8d\xe0\xa6\xaa\xe0\xa6\xa4\xe0\xa6\xbf\0"
	"\xe0\xa6\xb6\xe0\xa7\x81\xe0\xa6\x95\xe0\xa7\x8d\xe0\xa7\xb0\0"
	"\xe0\xa6\x9c\xe0\xa6\xbe\xe0\xa6\xa8\xe0\xa7\x81\xe0\xa7\xb1\xe0\xa6\xbe\xe0\xa7\xb0\xe0\xa7\x80\0"
	"\xe0\xa6\xab\xe0\xa7\x87\xe0\xa6\xac\xe0\xa7\x8d\xe0\xa7\xb0\xe0\xa7\x81\xe0\xa7\xb1\xe0\xa6\xbe\xe0\xa7\xb0\xe0\xa7\x80\0"
	"\xe0\xa6\xae\xe0\xa6\xbe\xe0\xa7\xb0\xe0\xa7\x8d\xe0\xa6\x9a\0"
	"\xe0\xa6\x8f\xe0\xa6\xaa\xe0\xa7\x8d\xe0\xa7\xb0\xe0\xa6\xbf\xe0\xa6\xb2\0"
	"\xe0\xa6\x86\xe0\xa6\x97\xe0\xa6\xb7\xe0\xa7\x8d\xe0\xa6\x9f\0"
	"\xe0\xa6\x9b\xe0\xa7\x87\xe0\xa6\xaa\xe0\xa7\x8d\xe0\xa6\xa4\xe0\xa7\x87\xe0\xa6\xae\xe0\xa7\x8d\xe0\xa6\xac\xe0\xa7\xb0\0"
	"\xe0\xa6\x85\xe0\xa6\x95\xe0\xa7\x8d\xe0\xa6\x9f\xe0\xa7\x8b\xe0\xa6\xac\xe0\xa7\xb0\0"
	"\xe0\xa6\xa8\xe0\xa7\xb1\xe0\xa7\x87\xe0\xa6\xae\xe0\xa7\x8d\xe0\xa6\xac\xe0\xa7\xb0\0"
	"\xe0\xa6\xa1\xe0\xa6\xbf\xe0\xa6\x9a\xe0\xa7\x87\xe0\xa6\xae\xe0\xa7\x8d\xe0\xa6\xac\xe0\xa7\xb0\0"
	"\xe0\xa6\x9c\xe0\xa6\xbe\xe0\xa6\xa8\xe0\xa7\x81\0"
	"\xe0\xa6\xab\xe0\xa7\x87\xe0\xa6\xac\xe0\xa7\x8d\xe0\xa7\xb0\xe0\xa7\x81\0"
	"\xe0\xa6\x86\xe0\xa6\x97\0"
	"\xe0\xa6\xb8\xe0\xa7\x87\xe0\xa6\xaa\xe0\xa7\x8d\xe0\xa6\x9f\0"
	"\xe0\xa6\x85\xe0\xa6\x95\xe0\xa7\x8d\xe0\xa6\x9f\xe0\xa7\x8b\0"
	"\xe0\xa6\xa8\xe0\xa6\xad\xe0\xa7\x87\0"
	"\xe0\xa6\xa1\xe0\xa6\xbf\xe0\xa6\xb8\xe0\xa7\x87\0"
	"yyyy,MMMM dd, dddd\0"
	"MMMM,yy\0"
	"MMMM,yyyy\0"
	"\xe0\xa4\xae\xe0\xa4\x82\xe0\xa4\x97\xe0\xa4\xb3\xe0\xa4\xb5\xe0\xa4\xbe\xe0\xa4\xb0\0"
	"\xe0\xa4\xae\xe0\xa4\x82\xe0\xa4\x97\xe0\xa4\xb3\0"
	"\xe0\xa4\x9c\xe0\xa4\xbe\xe0\xa4\xa8\xe0\xa5\x87\xe0\xa4\xb5\xe0\xa4\xbe\xe0\xa4\xb0\xe0\xa5\x80\0"
	"\xe0\xa4\xab\xe0\xa5\x87\xe0\xa4\xac\xe0\xa5\x8d\xe0\xa4\xb0\xe0\xa5\x81\xe0\xa4\xb5\xe0\xa4\xbe\xe0\xa4\xb0\xe0\xa5\x80\0"
	"\xe0\xa4\x8f\xe0\xa4\xaa\xe0\xa5\x8d\xe0\xa4\xb0\xe0\xa4\xbf\xe0\xa4\xb2\0"
	"\xe0\xa4\xae\xe0\xa5\x87\0"
	"\xe0\xa4\x9c\xe0\xa5\x81\xe0\xa4\xb2\xe0\xa5\x88\0"
	"\xe0\xa4\x91\xe0\xa4\x97\xe0\xa4\xb8\xe0\xa5\x8d\xe0\xa4\x9f\0"
	"\xe0\xa4\xb8\xe0\xa4\xaa\xe0\xa5\x8d\xe0\xa4\x9f\xe0\xa5\x87\xe0\xa4\x82\xe0\xa4\xac\xe0\xa4\xb0\0"
	"\xe0\xa4\x91\xe0\xa4\x95\xe0\xa5\x8d\xe0\xa4\x9f\xe0\xa5\x8b\xe0\xa4\xac\xe0\xa4\xb0\0"
	"\xe0\xa4\xa8\xe0\xa5\x8b\xe0\xa4\xb5\xe0\xa5\x8d\xe0\xa4\xb9\xe0\xa5\x87\xe0\xa4\x82\xe0\xa4\xac\xe0\xa4\xb0\0"
	"\xe0\xa4\xa1\xe0\xa4\xbf\xe0\xa4\xb8\xe0\xa5\x87\xe0\xa4\x82\xe0\xa4\xac\xe0\xa4\xb0\0"
	"\xe0\xa4\x9c\xe0\xa4\xbe\xe0\xa4\xa8\xe0\xa5\x87\0"
	"\xe0\xa4\xab\xe0\xa5\x87\xe0\xa4\xac\xe0\xa5\x8d\xe0\xa4\xb0\xe0\xa5\x81\0"
	"\xe0\xa4\x8f\xe0\xa4\xaa\xe0\xa5\x8d\xe0\xa4\xb0\xe0\xa4\xbf\0"
	"\xe0\xa4\x91\xe0\xa4\x97\0"
	"\xe0\xa4\xb8\xe0\xa4\xaa\xe0\xa5\x8d\xe0\xa4\x9f\xe0\xa5\x87\xe0\xa4\x82\0"
	"\xe0\xa4\x91\xe0\xa4\x95\xe0\xa5\x8d\xe0\xa4\x9f\xe0\xa5\x8b\0"
	"\xe0\xa4\xa8\xe0\xa5\x8b\xe0\xa4\xb5\xe0\xa5\x8d\xe0\xa4\xb9\xe0\xa5\x87\xe0\xa4\x82\0"
	"\xe0\xa4\xa1\xe0\xa4\xbf\xe0\xa4\xb8\xe0\xa5\x87\xe0\xa4\x82\0"
	"\xd2\xae\xd3\xa8\0"
	"\xd2\xae\xd0\xa5\0"
	"\xd0\xbd\xd1\x8f\xd0\xbc\0"
	"\xd0\xb4\xd0\xb0\xd0\xb2\xd0\xb0\xd0\xb0\0"
	"\xd0\xbc\xd1\x8f\xd0\xb3\xd0\xbc\xd0\xb0\xd1\x80\0"
	"\xd0\xbb\xd1\x85\xd0\xb0\xd0\xb3\xd0\xb2\xd0\xb0\0"
	"\xd0\xbf\xd2\xaf\xd1\x80\xd1\x8d\xd0\xb2\0"
	"\xd0\xb1\xd0\xb0\xd0\xb0\xd1\x81\xd0\xb0\xd0\xbd\0"
	"\xd0\xb1\xd1\x8f\xd0\xbc\xd0\xb1\xd0\xb0\0"
	"\xd0\x9d\xd1\x8f\0"
	"\xd0\x94\xd0\xb0\0"
	"\xd0\x9c\xd1\x8f\0"
	"\xd0\x9b\xd1\x85\0"
	"\xd0\x9f\xd2\xaf\0"
	"\xd0\x91\xd0\xb0\0"
	"\xd0\x91\xd1\x8f\0"
	"\xd0\x9d\xd1\x8d\xd0\xb3\xd0\xb4\xd2\xaf\xd0\xb3\xd1\x8d\xd1\x8d\xd1\x80 \xd1\x81\xd0\xb0\xd1\x80\0"
	"\xd0\xa5\xd0\xbe\xd1\x91\xd1\x80\xd0\xb4\xd1\x83\xd0\xb3\xd0\xb0\xd0\xb0\xd1\x80 \xd1\x81\xd0\xb0\xd1\x80\0"
	"\xd0\x93\xd1\x83\xd1\x80\xd0\xb0\xd0\xb2\xd0\xb4\xd1\x83\xd0\xb3\xd0\xb0\xd0\xb0\xd1\x80 \xd1\x81\xd0\xb0\xd1\x80\0"
	"\xd0\x94\xd3\xa9\xd1\x80\xd3\xa9\xd0\xb2\xd0\xb4\xd2\xaf\xd0\xb3\xd1\x8d\xd1\x8d\xd1\x80 \xd1\x81\xd0\xb0\xd1\x80\0"
	"\xd0\xa2\xd0\xb0\xd0\xb2\xd0\xb4\xd1\x83\xd0\xb3\xd0\xb0\xd0\xb0\xd1\x80 \xd1\x81\xd0\xb0\xd1\x80\0"
	"\xd0\x97\xd1\x83\xd1\x80\xd0\xb3\xd0\xb0\xd0\xb4\xd1\x83\xd0\xb3\xd0\xb0\xd0\xb0\xd1\x80 \xd1\x81\xd0\xb0\xd1\x80\0"
	"\xd0\x94\xd0\xbe\xd0\xbb\xd0\xb4\xd1\x83\xd0\xb3\xd0\xb0\xd0\xb0\xd1\x80 \xd1\x81\xd0\xb0\xd1\x80\0"
	"\xd0\x9d\xd0\xb0\xd0\xb9\xd0\xbc\xd0\xb4\xd1\x83\xd0\xb3\xd0\xb0\xd0\xb0\xd1\x80 \xd1\x81\xd0\xb0\xd1\x80\0"
	"\xd0\x95\xd1\x81\xd0\xb4\xd2\xaf\xd0\xb3\xd1\x8d\xd1\x8d\xd1\x80 \xd1\x81\xd0\xb0\xd1\x80\0"
	"\xd0\x90\xd1\x80\xd0\xb0\xd0\xb2\xd0\xb4\xd1\x83\xd0\xb3\xd0\xb0\xd0\xb0\xd1\x80 \xd1\x81\xd0\xb0\xd1\x80\0"
	"\xd0\x90\xd1\x80\xd0\xb2\xd0\xb0\xd0\xbd \xd0\xbd\xd1\x8d\xd0\xb3\xd0\xb4\xd2\xaf\xd0\xb3\xd1\x8d\xd1\x8d\xd1\x80 \xd1\x81\xd0\xb0\xd1\x80\0"
	"\xd0\x90\xd1\x80\xd0\xb2\xd0\xb0\xd0\xbd \xd1\x85\xd0\xbe\xd1\x91\xd1\x80\xd0\xb4\xd1\x83\xd0\xb3\xd0\xb0\xd0\xb0\xd1\x80 \xd1\x81\xd0\xb0\xd1\x80\0"
	"1-\xd1\x80 \xd1\x81\xd0\xb0\xd1\x80\0"
	"2-\xd1\x80 \xd1\x81\xd0\xb0\xd1\x80\0"
	"3-\xd1\x80 \xd1\x81\xd0\xb0\xd1\x80\0"
	"4-\xd1\x80 \xd1\x81\xd0\xb0\xd1\x80\0"
	"5-\xd1\x80 \xd1\x81\xd0\xb0\xd1\x80\0"
	"6-\xd1\x80 \xd1\x81\xd0\xb0\xd1\x80\0"
	"7-\xd1\x80 \xd1\x81\xd0\xb0\xd1\x80\0"
	"8-\xd1\x80 \xd1\x81\xd0\xb0\xd1\x80\0"
	"9-\xd1\x80 \xd1\x81\xd0\xb0\xd1\x80\0"
	"10-\xd1\x80 \xd1\x81\xd0\xb0\xd1\x80\0"
	"11-\xd1\x80 \xd1\x81\xd0\xb0\xd1\x80\0"
	"12-\xd1\x80 \xd1\x81\xd0\xb0\xd1\x80\0"
	"dddd, yyyy '\xd0\xbe\xd0\xbd\xd1\x8b' MM '\xd1\x81\xd0\xb0\xd1\x80\xd1\x8b\xd0\xbd' d\0"
	"yyyy '\xd0\xbe\xd0\xbd\xd1\x8b' MM '\xd1\x81\xd0\xb0\xd1\x80\xd1\x8b\xd0\xbd' d\0"
	"\xe0\xbd\x9f\xe0\xbe\xb3\xe0\xbc\x8bM\xe0\xbd\x9a\xe0\xbd\xba\xe0\xbd\xa6\xe0\xbc\x8b\x64\0"
	"\xe0\xbd\xa6\xe0\xbe\x94\xe0\xbc\x8b\xe0\xbd\x91\xe0\xbe\xb2\xe0\xbd\xbc\xe0\xbc\x8b\0"
	"\xe0\xbd\x95\xe0\xbe\xb1\xe0\xbd\xb2\xe0\xbc\x8b\xe0\xbd\x91\xe0\xbe\xb2\xe0\xbd\xbc\xe0\xbc\x8b\0"
	"\xe0\xbd\x82\xe0\xbd\x9f\xe0\xbd\xa0\xe0\xbc\x8b\xe0\xbd\x89\xe0\xbd\xb2\xe0\xbc\x8b\xe0\xbd\x98\xe0\xbc\x8b\0"
	"\xe0\xbd\x82\xe0\xbd\x9f\xe0\xbd\xa0\xe0\xbc\x8b\xe0\xbd\x9f\xe0\xbe\xb3\xe0\xbc\x8b\xe0\xbd\x96\xe0\xbc\x8b\0"
	"\xe0\xbd\x82\xe0\xbd\x9f\xe0\xbd\xa0\xe0\xbc\x8b\xe0\xbd\x98\xe0\xbd\xb2\xe0\xbd\x82\xe0\xbc\x8b\xe0\xbd\x91\xe0\xbd\x98\xe0\xbd\xa2\xe0\xbc\x8b\0"
	"\xe0\xbd\x82\xe0\xbd\x9f\xe0\xbd\xa0\xe0\xbc\x8b\xe0\xbd\xa7\xe0\xbe\xb3\xe0\xbd\x82\xe0\xbc\x8b\xe0\xbd\x94\xe0\xbc\x8b\0"
	"\xe0\xbd\x82\xe0\xbd\x9f\xe0\xbd\xa0\xe0\xbc\x8b\xe0\xbd\x95\xe0\xbd\xb4\xe0\xbd\xa2\xe0\xbc\x8b\xe0\xbd\x96\xe0\xbd\xb4\xe0\xbc\x8b\0"
	"\xe0\xbd\x82\xe0\xbd\x9f\xe0\xbd\xa0\xe0\xbc\x8b\xe0\xbd\xa6\xe0\xbd\x84\xe0\xbd\xa6\xe0\xbc\x8b\0"
	"\xe0\xbd\x82\xe0\xbd\x9f\xe0\xbd\xa0\xe0\xbc\x8b\xe0\xbd\xa6\xe0\xbe\xa4\xe0\xbd\xba\xe0\xbd\x93\xe0\xbc\x8b\xe0\xbd\x94\xe0\xbc\x8b\0"
	"\xe0\xbd\x89\xe0\xbd\xb2\xe0\xbc\x8b\xe0\xbd\x98\xe0\xbc\x8b\0"
	"\xe0\xbd\x9f\xe0\xbe\xb3\xe0\xbc\x8b\xe0\xbd\x96\xe0\xbc\x8b\0"
	"\xe0\xbd\x98\xe0\xbd\xb2\xe0\xbd\x82\xe0\xbc\x8b\xe0\xbd\x91\xe0\xbd\x98\xe0\xbd\xa2\xe0\xbc\x8b\0"
	"\xe0\xbd\xa7\xe0\xbe\xb3\xe0\xbd\x82\xe0\xbc\x8b\xe0\xbd\x94\xe0\xbc\x8b\0"
	"\xe0\xbd\x95\xe0\xbd\xb4\xe0\xbd\xa2\xe0\xbc\x8b\xe0\xbd\x96\xe0\xbd\xb4\xe0\xbc\x8b\0"
	"\xe0\xbd\xa6\xe0\xbd\x84\xe0\xbd\xa6\xe0\xbc\x8b\0"
	"\xe0\xbd\xa6\xe0\xbe\xa4\xe0\xbd\xba\xe0\xbd\x93\xe0\xbc\x8b\xe0\xbd\x94\xe0\xbc\x8b\0"
	"\xe0\xbd\x89\xe0\xbd\xb2\0"
	"\xe0\xbd\x9f\xe0\xbe\xb3\0"
	"\xe0\xbd\x98\xe0\xbd\xb2\0"
	"\xe0\xbd\xa7\xe0\xbe\xb3\0"
	"\xe0\xbd\x95\xe0\xbd\xb4\0"
	"\xe0\xbd\xa6\0"
	"\xe0\xbd\xa6\xe0\xbe\xa4\xe0\xbd\xba\0"
	"\xe0\xbd\x9f\xe0\xbe\xb3\xe0\xbc\x8b\xe0\xbd\x96\xe0\xbc\x8b\xe0\xbd\x91\xe0\xbd\x84\xe0\xbc\x8b\xe0\xbd\x94\xe0\xbd\xbc\xe0\xbc\x8b\0"
	"\xe0\xbd\x9f\xe0\xbe\xb3\xe0\xbc\x8b\xe0\xbd\x96\xe0\xbc\x8b\xe0\xbd\x82\xe0\xbd\x89\xe0\xbd\xb2\xe0\xbd\xa6\xe0\xbc\x8b\xe0\xbd\x94\xe0\xbc\x8b\0"
	"\xe0\xbd\x9f\xe0\xbe\xb3\xe0\xbc\x8b\xe0\xbd\x96\xe0\xbc\x8b\xe0\xbd\xa6\xe0\xbd\xb4\xe0\xbd\x98\xe0\xbc\x8b\xe0\xbd\x94\xe0\xbc\x8b\0"
	"\xe0\xbd\x9f\xe0\xbe\xb3\xe0\xbc\x8b\xe0\xbd\x96\xe0\xbc\x8b\xe0\xbd\x96\xe0\xbd\x9e\xe0\xbd\xb2\xe0\xbc\x8b\xe0\xbd\x94\xe0\xbc\x8b\0"
	"\xe0\xbd\x9f\xe0\xbe\xb3\xe0\xbc\x8b\xe0\xbd\x96\xe0\xbc\x8b\xe0\xbd\xa3\xe0\xbe\x94\xe0\xbc\x8b\xe0\xbd\x94\xe0\xbc\x8b\0"
	"\xe0\xbd\x9f\xe0\xbe\xb3\xe0\xbc\x8b\xe0\xbd\x96\xe0\xbc\x8b\xe0\xbd\x91\xe0\xbe\xb2\xe0\xbd\xb4\xe0\xbd\x82\xe0\xbc\x8b\xe0\xbd\x94\xe0\xbc\x8b\0"
	"\xe0\xbd\x9f\xe0\xbe\xb3\xe0\xbc\x8b\xe0\xbd\x96\xe0\xbc\x8b\xe0\xbd\x96\xe0\xbd\x91\xe0\xbd\xb4\xe0\xbd\x93\xe0\xbc\x8b\xe0\xbd\x94\xe0\xbc\x8b\0"
	"\xe0\xbd\x9f\xe0\xbe\xb3\xe0\xbc\x8b\xe0\xbd\x96\xe0\xbc\x8b\xe0\xbd\x96\xe0\xbd\xa2\xe0\xbe\x92\xe0\xbe\xb1\xe0\xbd\x91\xe0\xbc\x8b\xe0\xbd\x94\xe0\xbc\x8b\0"
	"\xe0\xbd\x9f\xe0\xbe\xb3\xe0\xbc\x8b\xe0\xbd\x96\xe0\xbc\x8b\xe0\xbd\x91\xe0\xbd\x82\xe0\xbd\xb4\xe0\xbc\x8b\xe0\xbd\x94\xe0\xbc\x8b\0"
	"\xe0\xbd\x9f\xe0\xbe\xb3\xe0\xbc\x8b\xe0\xbd\x96\xe0\xbc\x8b\xe0\xbd\x96\xe0\xbd\x85\xe0\xbd\xb4\xe0\xbc\x8b\xe0\xbd\x94\xe0\xbc\x8b\0"
	"\xe0\xbd\x9f\xe0\xbe\xb3\xe0\xbc\x8b\xe0\xbd\x96\xe0\xbc\x8b\xe0\xbd\x96\xe0\xbd\x85\xe0\xbd\xb4\xe0\xbc\x8b\xe0\xbd\x82\xe0\xbd\x85\xe0\xbd\xb2\xe0\xbd\x82\xe0\xbc\x8b\xe0\xbd\x94\xe0\xbc\x8b\0"
	"\xe0\xbd\x9f\xe0\xbe\xb3\xe0\xbc\x8b\xe0\xbd\x96\xe0\xbc\x8b\xe0\xbd\x96\xe0\xbd\x85\xe0\xbd\xb4\xe0\xbc\x8b\xe0\xbd\x82\xe0\xbd\x89\xe0\xbd\xb2\xe0\xbd\xa6\xe0\xbc\x8b\xe0\xbd\x94\xe0\xbc\x8b\0"
	"\xe0\xbd\x9f\xe0\xbe\xb3\xe0\xbc\x8b\xe0\xbc\xa1\0"
	"\xe0\xbd\x9f\xe0\xbe\xb3\xe0\xbc\x8b\xe0\xbc\xa2\0"
	"\xe0\xbd\x9f\xe0\xbe\xb3\xe0\xbc\x8b\xe0\xbc\xa3\0"
	"\xe0\xbd\x9f\xe0\xbe\xb3\xe0\xbc\x8b\xe0\xbc\xa4\0"
	"\xe0\xbd\x9f\xe0\xbe\xb3\xe0\xbc\x8b\xe0\xbc\xa5\0"
	"\xe0\xbd\x9f\xe0\xbe\xb3\xe0\xbc\x8b\xe0\xbc\xa6\0"
	"\xe0\xbd\x9f\xe0\xbe\xb3\xe0\xbc\x8b\xe0\xbc\xa7\0"
	"\xe0\xbd\x9f\xe0\xbe\xb3\xe0\xbc\x8b\xe0\xbc\xa8\0"
	"\xe0\xbd\x9f\xe0\xbe\xb3\xe0\xbc\x8b\xe0\xbc\xa9\0"
	"\xe0\xbd\x9f\xe0\xbe\xb3\xe0\xbc\x8b\xe0\xbc\xa1\xe0\xbc\xa0\0"
	"\xe0\xbd\x9f\xe0\xbe\xb3\xe0\xbc\x8b\xe0\xbc\xa1\xe0\xbc\xa1\0"
	"\xe0\xbd\x9f\xe0\xbe\xb3\xe0\xbc\x8b\xe0\xbc\xa1\xe0\xbc\xa2\0"
	"yyyy'\xe0\xbd\xa3\xe0\xbd\xbc\xe0\xbd\xa0\xe0\xbd\xb2\xe0\xbc\x8b\xe0\xbd\x9f\xe0\xbe\xb3' M'\xe0\xbd\x9a\xe0\xbd\xba\xe0\xbd\xa6' d\0"
	"yyyy'\xe0\xbd\xa3\xe0\xbd\xbc\xe0\xbd\xa0\xe0\xbd\xb2\xe0\xbc\x8b\xe0\xbd\x9f\xe0\xbe\xb3' M'\xe0\xbd\x9a\xe0\xbd\xba\xe0\xbd\xa6' d dddd\0"
	"yyyy\xe0\xbd\xa3\xe0\xbd\xbc\xe0\xbd\xa0\xe0\xbd\xb2\xe0\xbc\x8b\xe0\xbd\x9f\xe0\xbe\xb3 MMM d\0"
	"yyyy\xe0\xbd\xa3\xe0\xbd\xbc\xe0\xbd\xa0\xe0\xbd\xb2\xe0\xbc\x8b\xe0\xbd\x9f\xe0\xbe\xb3 MMM d dddd\0"
	"yyyy'\xe0\xbd\xa3\xe0\xbd\xbc\xe0\xbd\xa0\xe0\xbd\xb2\xe0\xbc\x8b\xe0\xbd\x9f\xe0\xbe\xb3\xe0\xbc\x8b' M\0"
	"Dydd Sul\0"
	"Dydd Llun\0"
	"Dydd Mawrth\0"
	"Dydd Mercher\0"
	"Dydd Iau\0"
	"Dydd Gwener\0"
	"Dydd Sadwrn\0"
	"Sul\0"
	"Llun\0"
	"Maw\0"
	"Mer\0"
	"Iau\0"
	"Gwe\0"
	"Sad\0"
	"Ll\0"
	"Ionawr\0"
	"Chwefror\0"
	"Mawrth\0"
	"Ebrill\0"
	"Mehefin\0"
	"Gorffennaf\0"
	"Awst\0"
	"Medi\0"
	"Hydref\0"
	"Tachwedd\0"
	"Rhagfyr\0"
	"Ion\0"
	"Chw\0"
	"Ebr\0"
	"Meh\0"
	"Gor\0"
	"Hyd\0"
	"Tach\0"
	"Rhag\0"
	"\xe1\x9e\x96\xe1\x9f\x92\xe1\x9e\x9a\xe1\x9e\xb9\xe1\x9e\x80\0"
	"\xe1\x9e\x9b\xe1\x9f\x92\xe1\x9e\x84\xe1\x9e\xb6\xe1\x9e\x85\0"
	"\xe1\x9e\xa2\xe1\x9e\xb6\xe1\x9e\x91\xe1\x9e\xb7\xe1\x9e\x8f\xe1\x9f\x92\xe1\x9e\x99\0"
	"\xe1\x9e\x85\xe1\x9e\x93\xe1\x9f\x92\xe1\x9e\x91\0"
	"\xe1\x9e\xa2\xe1\x9e\x84\xe1\x9f\x92\xe1\x9e\x82\xe1\x9e\xb6\xe1\x9e\x9a\0"
	"\xe1\x9e\x96\xe1\x9e\xbb\xe1\x9e\x92\0"
	"\xe1\x9e\x96\xe1\x9f\x92\xe1\x9e\x9a\xe1\x9e\xa0\xe1\x9e\x9f\xe1\x9f\x92\xe1\x9e\x94\xe1\x9e\x8f\xe1\x9e\xb7\xe1\x9f\x8d\0"
	"\xe1\x9e\x9f\xe1\x9e\xbb\xe1\x9e\x80\xe1\x9f\x92\xe1\x9e\x9a\0"
	"\xe1\x9e\x9f\xe1\x9f\x85\xe1\x9e\x9a\xe1\x9f\x8d\0"
	"\xe1\x9e\x98\xe1\x9e\x80\xe1\x9e\x9a\xe1\x9e\xb6\0"
	"\xe1\x9e\x80\xe1\x9e\xbb\xe1\x9e\x98\xe1\x9f\x92\xe1\x9e\x97\xe1\x9f\x88\0"
	"\xe1\x9e\x98\xe1\x9e\xb8\xe1\x9e\x93\xe1\x9e\xb6\0"
	"\xe1\x9e\x98\xe1\x9f\x81\xe1\x9e\x9f\xe1\x9e\xb6\0"
	"\xe1\x9e\xa7\xe1\x9e\x9f\xe1\x9e\x97\xe1\x9e\xb6\0"
	"\xe1\x9e\x98\xe1\x9e\xb7\xe1\x9e\x90\xe1\x9e\xbb\xe1\x9e\x93\xe1\x9e\xb6\0"
	"\xe1\x9e\x80\xe1\x9e\x80\xe1\x9f\x92\xe1\x9e\x80\xe1\x9e\x8a\xe1\x9e\xb6\0"
	"\xe1\x9e\x9f\xe1\x9e\xb8\xe1\x9e\xa0\xe1\x9e\xb6\0"
	"\xe1\x9e\x80\xe1\x9e\x89\xe1\x9f\x92\xe1\x9e\x89\xe1\x9e\xb6\0"
	"\xe1\x9e\x8f\xe1\x9e\xbb\xe1\x9e\x9b\xe1\x9e\xb6\0"
	"\xe1\x9e\x9c\xe1\x9e\xb7\xe1\x9e\x85\xe1\x9f\x92\xe1\x9e\x86\xe1\x9e\xb7\xe1\x9e\x80\xe1\x9e\xb6\0"
	"\xe1\x9e\x92\xe1\x9f\x92\xe1\x9e\x93\xe1\x9e\xbc\0"
	"'\xe1\x9e\x81\xe1\x9f\x82' MM '\xe1\x9e\x86\xe1\x9f\x92\xe1\x9e\x93\xe1\x9e\xb6\xe1\x9f\x86' yyyy\0"
	"\xe0\xba\x81\xe0\xbb\x88\xe0\xba\xad\xe0\xba\x99\xe0\xba\x97\xe0\xbb\x88\xe0\xba\xbd\xe0\xba\x87\0"
	"\xe0\xba\xab\xe0\xba\xbc\xe0\xba\xb1\xe0\xba\x87\xe0\xba\x97\xe0\xbb\x88\xe0\xba\xbd\xe0\xba\x87\0"
	"\xe0\xba\xa7\xe0\xba\xb1\xe0\xba\x99\xe0\xba\xad\xe0\xba\xb2\xe0\xba\x97\xe0\xba\xb4\xe0\xba\x94\0"
	"\xe0\xba\xa7\xe0\xba\xb1\xe0\xba\x99\xe0\xba\x88\xe0\xba\xb1\xe0\xba\x99\0"
	"\xe0\xba\xa7\xe0\xba\xb1\xe0\xba\x99\xe0\xba\xad\xe0\xba\xb1\xe0\xba\x87\xe0\xba\x84\xe0\xba\xb2\xe0\xba\x99\0"
	"\xe0\xba\xa7\xe0\xba\xb1\xe0\xba\x99\xe0\xba\x9e\xe0\xba\xb8\xe0\xba\x94\0"
	"\xe0\xba\xa7\xe0\xba\xb1\xe0\xba\x99\xe0\xba\x9e\xe0\xba\xb0\xe0\xba\xab\xe0\xba\xb1\xe0\xba\x94\0"
	"\xe0\xba\xa7\xe0\xba\xb1\xe0\xba\x99\xe0\xba\xaa\xe0\xba\xb8\xe0\xba\x81\0"
	"\xe0\xba\xa7\xe0\xba\xb1\xe0\xba\x99\xe0\xbb\x80\xe0\xba\xaa\xe0\xba\xbb\xe0\xba\xb2\0"
	"\xe0\xba\x97\0"
	"\xe0\xba\x88\0"
	"\xe0\xba\x84\0"
	"\xe2\x80\x8b\xe0\xba\x9e\xe0\xba\xb8\0"
	"\xe0\xba\x9e\0"
	"\xe2\x80\x8b\xe0\xba\xaa\xe0\xba\xb8\0"
	"\xe0\xba\xaa\0"
	"\xe0\xba\xa1\xe0\xba\xb1\xe0\xba\x87\xe0\xba\x81\xe0\xba\xad\xe0\xba\x99\0"
	"\xe0\xba\x81\xe0\xba\xb8\xe0\xba\xa1\xe0\xba\x9e\xe0\xba\xb2\0"
	"\xe0\xba\xa1\xe0\xba\xb5\xe0\xba\x99\xe0\xba\xb2\0"
	"\xe0\xbb\x80\xe0\xba\xa1\xe0\xba\xaa\xe0\xba\xb2\0"
	"\xe0\xba\x9e\xe0\xba\xb6\xe0\xba\x94\xe0\xba\xaa\xe0\xba\xb0\xe0\xba\x9e\xe0\xba\xb2\0"
	"\xe0\xba\xa1\xe0\xba\xb4\xe0\xba\x96\xe0\xba\xb8\xe0\xba\x99\xe0\xba\xb2\0"
	"\xe0\xba\x81\xe0\xbb\x8d\xe0\xba\xa5\xe0\xba\xb0\xe0\xba\x81\xe0\xba\xbb\xe0\xba\x94\0"
	"\xe0\xba\xaa\xe0\xba\xb4\xe0\xba\x87\xe0\xba\xab\xe0\xba\xb2\0"
	"\xe0\xba\x81\xe0\xba\xb1\xe0\xba\x99\xe0\xba\x8d\xe0\xba\xb2\0"
	"\xe0\xba\x95\xe0\xba\xb8\xe0\xba\xa5\xe0\xba\xb2\0"
	"\xe0\xba\x9e\xe0\xba\xb0\xe0\xba\x88\xe0\xba\xb4\xe0\xba\x81\0"
	"\xe0\xba\x97\xe0\xba\xb1\xe0\xba\x99\xe0\xba\xa7\xe0\xba\xb2\0"
	"\xe0\xba\xa1.\xe0\xba\x81.\0"
	"\xe0\xba\x81.\xe0\xba\x9e.\0"
	"\xe0\xba\xa1.\xe0\xba\x99.\0"
	"\xe0\xba\xa1.\xe0\xba\xaa.\0"
	"\xe0\xba\x9e.\xe0\xba\x9e.\0"
	"\xe0\xba\xa1\xe0\xba\xb4.\xe0\xba\x96.\0"
	"\xe0\xba\x81.\xe0\xba\xa5.\0"
	"\xe0\xba\xaa.\xe0\xba\xab.\0"
	"\xe0\xba\x81.\xe0\xba\x8d.\0"
	"\xe0\xba\x95.\xe0\xba\xa5.\0"
	"\xe0\xba\x9e.\xe0\xba\x88.\0"
	"\xe0\xba\x97.\xe0\xba\xa7.\0"
	"dddd \xe0\xba\x97\xe0\xba\xb5 d MMMM gg yyyy\0"
	"luns\0"
	"m\xc3\xa9rcores\0"
	"xoves\0"
	"venres\0"
	"Dom\0"
	"M\xc3\xa9r\0"
	"Xov\0"
	"Ven\0"
	"S\xc3\xa1\x62\0"
	"X\0"
	"Xaneiro\0"
	"Febreiro\0"
	"Maio\0"
	"Xu\xc3\xb1o\0"
	"Xullo\0"
	"Setembro\0"
	"Outubro\0"
	"Novembro\0"
	"Decembro\0"
	"xaneiro\0"
	"febreiro\0"
	"xu\xc3\xb1o\0"
	"xullo\0"
	"decembro\0"
	"Xan\0"
	"Abr\0"
	"Xu\xc3\xb1\0"
	"Xul\0"
	"Out\0"
	"\xe0\xa4\xae.\xe0\xa4\xaa\xe0\xa5\x82.\0"
	"\xe0\xa4\xae.\xe0\xa4\xa8\xe0\xa4\x82.\0"
	"\xe0\xa4\x86\xe0\xa4\xa6\xe0\xa4\xbf\xe0\xa4\xa4\xe0\xa5\x8d\xe0\xa4\xaf\xe0\xa4\xb5\xe0\xa4\xbe\xe0\xa4\xb0\0"
	"\xe0\xa4\xae\xe0\xa4\x82\xe0\xa4\x97\xe0\xa4\xb3\xe0\xa4\xbe\xe0\xa4\xb0\0"
	"\xe0\xa4\x93\xe0\xa4\x97\xe0\xa4\xb8\xe0\xa5\x8d\xe0\xa4\x9f\0"
	"\xe0\xa4\xb8\xe0\xa5\x87\xe0\xa4\xaa\xe0\xa5\x8d\xe0\xa4\x9f\xe0\xa5\x87\xe0\xa4\x82\xe0\xa4\xac\xe0\xa4\xb0\0"
	"\xe0\xa4\x93\xe0\xa4\x95\xe0\xa5\x8d\xe0\xa4\x9f\xe0\xa5\x8b\xe0\xa4\xac\xe0\xa4\xb0\0"
	"\xe0\xb6\xb4\xe0\xb7\x99.\xe0\xb7\x80.\0"
	"\xe0\xb6\xb4.\xe0\xb7\x80.\0"
	"\xe0\xb6\x89\xe0\xb6\xbb\xe0\xb7\x92\xe0\xb6\xaf\xe0\xb7\x8f\0"
	"\xe0\xb7\x83\xe0\xb6\xb3\xe0\xb7\x94\xe0\xb6\xaf\xe0\xb7\x8f\0"
	"\xe0\xb6\x85\xe0\xb6\x9f\xe0\xb7\x84\xe0\xb6\xbb\xe0\xb7\x94\xe0\xb7\x80\xe0\xb7\x8f\xe0\xb6\xaf\xe0\xb7\x8f\0"
	"\xe0\xb6\xb6\xe0\xb6\xaf\xe0\xb7\x8f\xe0\xb6\xaf\xe0\xb7\x8f\0"
	"\xe0\xb6\xb6\xe0\xb7\x8a\xe2\x80\x8d\xe0\xb6\xbb\xe0\xb7\x84\xe0\xb7\x83\xe0\xb7\x8a\xe0\xb6\xb4\xe0\xb6\xad\xe0\xb7\x92\xe0\xb6\xb1\xe0\xb7\x8a\xe0\xb6\xaf\xe0\xb7\x8f\0"
	"\xe0\xb7\x83\xe0\xb7\x92\xe0\xb6\x9a\xe0\xb7\x94\xe0\xb6\xbb\xe0\xb7\x8f\xe0\xb6\xaf\xe0\xb7\x8f\0"
	"\xe0\xb7\x83\xe0\xb7\x99\xe0\xb6\xb1\xe0\xb7\x83\xe0\xb7\x94\xe0\xb6\xbb\xe0\xb7\x8f\xe0\xb6\xaf\xe0\xb7\x8f\0"
	"\xe0\xb6\x85\xe0\xb6\x9f\xe0\xb7\x84\0"
	"\xe0\xb6\xb6\xe0\xb7\x8a\xe2\x80\x8d\xe0\xb6\xbb\xe0\xb7\x84\xe0\xb7\x83\xe0\xb7\x8a\0"
	"\xe0\xb7\x83\xe0\xb7\x92\xe0\xb6\x9a\xe0\xb7\x94\0"
	"\xe0\xb7\x83\xe0\xb7\x99\xe0\xb6\xb1\0"
	"\xe0\xb6\x89\0"
	"\xe0\xb7\x83\0"
	"\xe0\xb6\x85\0"
	"\xe0\xb6\xb6\0"
	"\xe0\xb6\xb6\xe0\xb7\x8a\xe2\x80\x8d\xe0\xb6\xbb\0"
	"\xe0\xb7\x83\xe0\xb7\x92\0"
	"\xe0\xb7\x83\xe0\xb7\x99\0"
	"\xe0\xb6\xa2\xe0\xb6\xb1\xe0\xb7\x80\xe0\xb7\x8f\xe0\xb6\xbb\xe0\xb7\x92\0"
	"\xe0\xb6\xb4\xe0\xb7\x99\xe0\xb6\xb6\xe0\xb6\xbb\xe0\xb7\x80\xe0\xb7\x8f\xe0\xb6\xbb\xe0\xb7\x92\0"
	"\xe0\xb6\xb8\xe0\xb7\x8f\xe0\xb6\xbb\xe0\xb7\x8a\xe0\xb6\xad\xe0\xb7\x94\0"
	"\xe0\xb6\x85\xe0\xb6\xb4\xe0\xb7\x8a\xe2\x80\x8d\xe0\xb6\xbb\xe0\xb7\x9a\xe0\xb6\xbd\xe0\xb7\x8a\0"
	"\xe0\xb6\xb8\xe0\xb7\x90\xe0\xb6\xba\xe0\xb7\x92\0"
	"\xe0\xb6\xa2\xe0\xb7\x96\xe0\xb6\xb1\xe0\xb7\x92\0"
	"\xe0\xb6\xa2\xe0\xb7\x96\xe0\xb6\xbd\xe0\xb7\x92\0"
	"\xe0\xb6\x85\xe0\xb6\x9c\xe0\xb7\x9d\xe0\xb7\x83\xe0\xb7\x8a\xe0\xb6\xad\xe0\xb7\x94\0"
	"\xe0\xb7\x83\xe0\xb7\x90\xe0\xb6\xb4\xe0\xb7\x8a\xe0\xb6\xad\xe0\xb7\x90\xe0\xb6\xb8\xe0\xb7\x8a\xe0\xb6\xb6\xe0\xb6\xbb\xe0\xb7\x8a\0"
	"\xe0\xb6\x94\xe0\xb6\x9a\xe0\xb7\x8a\xe0\xb6\xad\xe0\xb7\x9d\xe0\xb6\xb6\xe0\xb6\xbb\xe0\xb7\x8a\0"
	"\xe0\xb6\xb1\xe0\xb7\x9c\xe0\xb7\x80\xe0\xb7\x90\xe0\xb6\xb8\xe0\xb7\x8a\xe0\xb6\xb6\xe0\xb6\xbb\xe0\xb7\x8a\0"
	"\xe0\xb6\xaf\xe0\xb7\x99\xe0\xb7\x83\xe0\xb7\x90\xe0\xb6\xb8\xe0\xb7\x8a\xe0\xb6\xb6\xe0\xb6\xbb\xe0\xb7\x8a\0"
	"\xe0\xb6\xa2\xe0\xb6\xb1\0"
	"\xe0\xb6\xb4\xe0\xb7\x99\xe0\xb6\xb6\0"
	"\xe0\xb6\xb8\xe0\xb7\x8f\xe0\xb6\xbb\xe0\xb7\x8a\0"
	"\xe0\xb6\x85\xe0\xb6\x9c\xe0\xb7\x9d\0"
	"\xe0\xb7\x83\xe0\xb7\x90\xe0\xb6\xb4\xe0\xb7\x8a\0"
	"\xe0\xb6\x94\xe0\xb6\x9a\xe0\xb7\x8a\0"
	"\xe0\xb6\xb1\xe0\xb7\x9c\xe0\xb7\x80\xe0\xb7\x90\0"
	"\xe0\xb6\xaf\xe0\xb7\x99\xe0\xb7\x83\xe0\xb7\x90\0"
	"\xe1\x8c\xa5\xe1\x8b\x8b\xe1\x89\xb5\0"
	"\xe1\x8a\xa8\xe1\x88\xb0\xe1\x8b\x93\xe1\x89\xb5\0"
	"\xe1\x8a\xa5\xe1\x88\x91\xe1\x8b\xb5\0"
	"\xe1\x88\xb0\xe1\x8a\x9e\0"
	"\xe1\x88\x9b\xe1\x8a\xad\xe1\x88\xb0\xe1\x8a\x9e\0"
	"\xe1\x88\xa8\xe1\x89\xa1\xe1\x8b\x95\0"
	"\xe1\x88\x90\xe1\x88\x99\xe1\x88\xb5\0"
	"\xe1\x8b\x93\xe1\x88\xad\xe1\x89\xa5\0"
	"\xe1\x89\x85\xe1\x8b\xb3\xe1\x88\x9c\0"
	"\xe1\x88\x9b\xe1\x8a\xad\xe1\x88\xb0\0"
	"\xe1\x8a\xa5\0"
	"\xe1\x88\xb0\0"
	"\xe1\x88\x9b\0"
	"\xe1\x88\xa8\0"
	"\xe1\x88\x90\0"
	"\xe1\x8b\x93\0"
	"\xe1\x89\x85\0"
	"\xe1\x8c\x83\xe1\x8a\x95\xe1\x8b\xa9\xe1\x8b\x88\xe1\x88\xaa\0"
	"\xe1\x8d\x8c\xe1\x89\xa5\xe1\x88\xa9\xe1\x8b\x88\xe1\x88\xaa\0"
	"\xe1\x88\x9b\xe1\x88\xad\xe1\x89\xbd\0"
	"\xe1\x8a\xa4\xe1\x8d\x95\xe1\x88\xaa\xe1\x88\x8d\0"
	"\xe1\x88\x9c\xe1\x8b\xad\0"
	"\xe1\x8c\x81\xe1\x8a\x95\0"
	"\xe1\x8c\x81\xe1\x88\x8b\xe1\x8b\xad\0"
	"\xe1\x8a\xa6\xe1\x8c\x88\xe1\x88\xb5\xe1\x89\xb5\0"
	"\xe1\x88\xb4\xe1\x8d\x95\xe1\x89\xb4\xe1\x88\x9d\xe1\x89\xa0\xe1\x88\xad\0"
	"\xe1\x8a\xa6\xe1\x8a\xad\xe1\x89\xb6\xe1\x89\xa0\xe1\x88\xad\0"
	"\xe1\x8a\x96\xe1\x89\xac\xe1\x88\x9d\xe1\x89\xa0\xe1\x88\xad\0"
	"\xe1\x8b\xb2\xe1\x88\xb4\xe1\x88\x9d\xe1\x89\xa0\xe1\x88\xad\0"
	"\xe1\x8a\xa6\xe1\x8a\xad\xe1\x89\xb0\xe1\x8b\x8d\xe1\x89\xa0\xe1\x88\xad\0"
	"\xe1\x8c\x83\xe1\x8a\x95\xe1\x8b\xa9\0"
	"\xe1\x8d\x8c\xe1\x89\xa5\xe1\x88\xa9\0"
	"\xe1\x8a\xa4\xe1\x8d\x95\xe1\x88\xaa\0"
	"\xe1\x8a\xa6\xe1\x8c\x88\xe1\x88\xb5\0"
	"\xe1\x88\xb4\xe1\x8d\x95\xe1\x89\xb4\0"
	"\xe1\x8a\xa6\xe1\x8a\xad\xe1\x89\xb6\0"
	"\xe1\x8a\x96\xe1\x89\xac\xe1\x88\x9d\0"
	"\xe1\x8b\xb2\xe1\x88\xb4\xe1\x88\x9d\0"
	"Zdat azal\0"
	"\xe1\xb8\x8c\x65\x66\x66ir aza\0"
	"Asamas\0"
	"Aynas\0"
	"Asinas\0"
	"Akras\0"
	"Akwas\0"
	"Asimwas\0"
	"Asi\xe1\xb8\x8dyas\0"
	"Asa\0"
	"Ayn\0"
	"Asn\0"
	"Akr\0"
	"Akw\0"
	"Asm\0"
	"As\xe1\xb8\x8d\0"
	"Yennayer\0"
	"Yebrayer\0"
	"Mars\0"
	"Ibrir\0"
	"Mayyu\0"
	"Yunyu\0"
	"Yulyuz\0"
	"\xc6\x94uct\0"
	"Cutanbir\0"
	"K\xe1\xb9\xaduber\0"
	"Nwanbir\0"
	"Dujanbir\0"
	"Yen\0"
	"Yeb\0"
	"Ibr\0"
	"Yun\0"
	"Yul\0"
	"\xc6\x94uc\0"
	"Cut\0"
	"K\xe1\xb9\xadu\0"
	"Nwa\0"
	"Duj\0"
	"\xe0\xa4\xaa\xe0\xa5\x82\xe0\xa4\xb0\xe0\xa5\x8d\xe0\xa4\xb5 \xe0\xa4\xae\xe0\xa4\xa7\xe0\xa5\x8d\xe0\xa4\xaf\xe0\xa4\xbe\xe0\xa4\xa8\xe0\xa5\x8d\xe0\xa4\xb9\0"
	"\xe0\xa4\x89\xe0\xa4\xa4\xe0\xa5\x8d\xe0\xa4\xa4\xe0\xa4\xb0 \xe0\xa4\xae\xe0\xa4\xa7\xe0\xa5\x8d\xe0\xa4\xaf\xe0\xa4\xbe\xe0\xa4\xa8\xe0\xa5\x8d\xe0\xa4\xb9\0"
	"\xe0\xa4\x86\xe0\xa4\x87\xe0\xa4\xa4\xe0\xa4\xac\xe0\xa4\xbe\xe0\xa4\xb0\0"
	"\xe0\xa4\xb8\xe0\xa5\x8b\xe0\xa4\xae\xe0\xa4\xac\xe0\xa4\xbe\xe0\xa4\xb0\0"
	"\xe0\xa4\xae\xe0\xa4\x99\xe0\xa5\x8d\xe0\xa4\x97\xe0\xa4\xb2\xe0\xa4\xac\xe0\xa4\xbe\xe0\xa4\xb0\0"
	"\xe0\xa4\xac\xe0\xa5\x81\xe0\xa4\xa7\xe0\xa4\xac\xe0\xa4\xbe\xe0\xa4\xb0\0"
	"\xe0\xa4\xac\xe0\xa4\xbf\xe0\xa4\xb9\xe0\xa5\x80\xe0\xa4\xac\xe0\xa4\xbe\xe0\xa4\xb0\0"
	"\xe0\xa4\xb6\xe0\xa5\x81\xe0\xa4\x95\xe0\xa5\x8d\xe0\xa4\xb0\xe0\xa4\xac\xe0\xa4\xbe\xe0\xa4\xb0\0"
	"\xe0\xa4\xb6\xe0\xa4\xa8\xe0\xa4\xbf\xe0\xa4\xac\xe0\xa4\xbe\xe0\xa4\xb0\0"
	"\xe0\xa4\x86\xe0\xa4\x87\xe0\xa4\xa4\0"
	"\xe0\xa4\xae\xe0\xa4\x99\xe0\xa5\x8d\xe0\xa4\x97\xe0\xa4\xb2\0"
	"\xe0\xa4\xac\xe0\xa4\xbf\xe0\xa4\xb9\xe0\xa5\x80\0"
	"\xe0\xa4\x86\0"
	"\xe0\xa4\xae\0"
	"\xe0\xa4\xac\xe0\xa4\xbf\0"
	"\xe0\xa4\xab\xe0\xa5\x87\xe0\xa4\xac\xe0\xa5\x8d\xe0\xa4\xb0\xe0\xa5\x81\xe0\xa4\x85\xe0\xa4\xb0\xe0\xa5\x80\0"
	"\xe0\xa4\x85\xe0\xa4\xaa\xe0\xa5\x8d\xe0\xa4\xb0\xe0\xa4\xbf\xe0\xa4\xb2\0"
	"\xe0\xa4\x9c\xe0\xa5\x81\xe0\xa4\xa8\0"
	"\xe0\xa4\x85\xe0\xa4\x97\xe0\xa4\xb8\xe0\xa5\x8d\xe0\xa4\x9f\0"
	"\xe0\xa4\xb8\xe0\xa5\x87\xe0\xa4\xaa\xe0\xa5\x8d\xe0\xa4\x9f\xe0\xa5\x87\xe0\xa4\xae\xe0\xa5\x8d\xe0\xa4\xac\xe0\xa4\xb0\0"
	"\xe0\xa4\x85\xe0\xa4\x95\xe0\xa5\x8d\xe0\xa4\x9f\xe0\xa5\x8b\xe0\xa4\xac\xe0\xa4\xb0\0"
	"\xe0\xa4\xa8\xe0\xa5\x8b\xe0\xa4\xad\xe0\xa5\x87\xe0\xa4\xae\xe0\xa5\x8d\xe0\xa4\xac\xe0\xa4\xb0\0"
	"\xe0\xa4\xa1\xe0\xa4\xbf\xe0\xa4\xb8\xe0\xa5\x87\xe0\xa4\xae\xe0\xa5\x8d\xe0\xa4\xac\xe0\xa4\xb0\0"
	"dddd, MMMM dd, yyyy\0"
	"MMMM dd, yyyy\0"
	"\xd9\x88\xd8\xb1\xdb\x8c\0"
	"\xd8\xba\xd9\x88\xdb\x8c\xdb\x8c\0"
	"\xd8\xba\xd8\xa8\xd8\xb1\xda\xab\xd9\x88\xd9\x84\xdb\x8c\0"
	"\xda\x86\xd9\x86\xda\xab\xd8\xa7\xda\x9a\0"
	"\xd8\xb2\xd9\x85\xd8\xb1\xdb\x8c\0"
	"\xd9\x88\xda\x96\xdb\x8c\0"
	"\xd8\xaa\xd9\x84\xd9\x87\0"
	"\xd9\x84\xda\x93\xd9\x85\0"
	"\xd9\x84\xdb\x8c\xd9\x86\xd8\xaf\xdb\x8d\0"
	"\xd9\x85\xd8\xb1\xd8\xba\xd9\x88\xd9\x85\xdb\x8c\0"
	"\xd8\xb3\xd9\x84\xd9\x88\xd8\xa7\xd8\xba\xd9\x87\0"
	"\xda\xa9\xd8\xa8\0"
	"Linggo\0"
	"Lunes\0"
	"Martes\0"
	"Miyerkules\0"
	"Huwebes\0"
	"Biyernes\0"
	"Sabado\0"
	"Lin\0"
	"Miy\0"
	"Huw\0"
	"Biy\0"
	"Pebrero\0"
	"Marso\0"
	"Hunyo\0"
	"Hulyo\0"
	"Setyembre\0"
	"Oktubre\0"
	"Nobyembre\0"
	"Disyembre\0"
	"Ene\0"
	"Peb\0"
	"Hun\0"
	"Hul\0"
	"Nob\0"
	"Lahadi\0"
	"Litinin\0"
	"Talata\0"
	"Laraba\0"
	"Alhamis\0"
	"Jumma'a\0"
	"Asabar\0"
	"Lh\0"
	"Li\0"
	"Ta\0"
	"Lr\0"
	"Al\0"
	"Ju\0"
	"As\0"
	"Janairu\0"
	"Faburairu\0"
	"Maris\0"
	"Afirilu\0"
	"Mayu\0"
	"Yuni\0"
	"Yuli\0"
	"Agusta\0"
	"Satumba\0"
	"Nuwamba\0"
	"Disamba\0"
	"Fab\0"
	"Afi\0"
	"Agu\0"
	"Nuw\0"
	"\xc3\x80\xc3\xa1r\xe1\xbb\x8d\xcc\x80\0"
	"\xe1\xbb\x8c\xcc\x80s\xc3\xa1n\0"
	"\xe1\xbb\x8cj\xe1\xbb\x8d\xcc\x81 \xc3\x80\xc3\xack\xc3\xba\0"
	"\xe1\xbb\x8cj\xe1\xbb\x8d\xcc\x81 Aj\xc3\xa9\0"
	"\xe1\xbb\x8cj\xe1\xbb\x8d\xcc\x81 \xc3\x8cs\xe1\xba\xb9\xcc\x81gun\0"
	"\xe1\xbb\x8cj\xe1\xbb\x8d\xcc\x81r\xc3\xba\0"
	"\xe1\xbb\x8cj\xe1\xbb\x8d\xcc\x81\x62\xe1\xbb\x8d\0"
	"\xe1\xbb\x8cj\xe1\xbb\x8d\xcc\x81 \xe1\xba\xb8t\xc3\xac\0"
	"\xe1\xbb\x8cj\xe1\xbb\x8d\xcc\x81 \xc3\x80\x62\xc3\xa1m\xe1\xba\xb9\xcc\x81ta\0"
	"\xc3\x80\xc3\xack\xc3\xba\0"
	"Aj\xc3\xa9\0"
	"\xc3\x8cs\xe1\xba\xb9\xcc\x81gun\0"
	"\xe1\xba\xb8t\xc3\xac\0"
	"\xc3\x80\x62\xc3\xa1m\xe1\xba\xb9\xcc\x81ta\0"
	"O\xe1\xb9\xa3\xc3\xb9 \xe1\xb9\xa2\xe1\xba\xb9\xcc\x81r\xe1\xba\xb9\xcc\x81\0"
	"O\xe1\xb9\xa3\xc3\xb9 \xc3\x88r\xc3\xa8l\xc3\xa8\0"
	"O\xe1\xb9\xa3\xc3\xb9 \xe1\xba\xb8r\xe1\xba\xb9\xcc\x80n\xc3\xa0\0"
	"O\xe1\xb9\xa3\xc3\xb9 \xc3\x8cgb\xc3\xa9\0"
	"O\xe1\xb9\xa3\xc3\xb9 \xe1\xba\xb8\xcc\x80\x62ibi\0"
	"O\xe1\xb9\xa3\xc3\xb9 \xc3\x92k\xc3\xba\x64u\0"
	"O\xe1\xb9\xa3\xc3\xb9 Ag\xe1\xba\xb9m\xe1\xbb\x8d\0"
	"O\xe1\xb9\xa3\xc3\xb9 \xc3\x92g\xc3\xban\0"
	"O\xe1\xb9\xa3\xc3\xb9 Owewe\0"
	"O\xe1\xb9\xa3\xc3\xb9 \xe1\xbb\x8c\xcc\x80w\xc3\xa0r\xc3\xa0\0"
	"O\xe1\xb9\xa3\xc3\xb9 B\xc3\xa9l\xc3\xba\0"
	"O\xe1\xb9\xa3\xc3\xb9 \xe1\xbb\x8c\xcc\x80p\xe1\xba\xb9\xcc\x80\0"
	"\xe1\xb9\xa2\xe1\xba\xb9\xcc\x81r\xe1\xba\xb9\xcc\x81\0"
	"\xc3\x88r\xc3\xa8l\xc3\xa8\0"
	"\xe1\xba\xb8r\xe1\xba\xb9\xcc\x80n\xc3\xa0\0"
	"\xc3\x8cgb\xc3\xa9\0"
	"\xe1\xba\xb8\xcc\x80\x62ibi\0"
	"\xc3\x92k\xc3\xba\x64u\0"
	"Ag\xe1\xba\xb9m\xe1\xbb\x8d\0"
	"\xc3\x92g\xc3\xban\0"
	"Owewe\0"
	"\xe1\xbb\x8c\xcc\x80w\xc3\xa0r\xc3\xa0\0"
	"B\xc3\xa9l\xc3\xba\0"
	"\xe1\xbb\x8c\xcc\x80p\xe1\xba\xb9\xcc\x80\0"
	"Sontaga\0"
	"Mosupalogo\0"
	"Labohlano\0"
	"Mokibelo\0"
	"Mok\0"
	"Janaware\0"
	"Feberware\0"
	"Mat\xc5\xa1he\0"
	"Aporele\0"
	"Julae\0"
	"Agostose\0"
	"Setemere\0"
	"Oktobore\0"
	"Nofemere\0"
	"Disemere\0"
	"Apo\0"
	"Nof\0"
	"MMMM d'.-at'\0"
	"u.t.\0"
	"u.k.\0"
	"sabaat\0"
	"ataasinngorneq\0"
	"marlunngorneq\0"
	"pingasunngorneq\0"
	"sisamanngorneq\0"
	"tallimanngorneq\0"
	"arfininngorneq\0"
	"ata\0"
	"pin\0"
	"sis\0"
	"tal\0"
	"arf\0"
	"martsi\0"
	"aprili\0"
	"maji\0"
	"augustusi\0"
	"septemberi\0"
	"oktoberi\0"
	"novemberi\0"
	"decemberi\0"
	"MMMM d'.-at, 'yyyy\0"
	"A.M.\0"
	"P.M.\0"
	"Mb\xe1\xbb\x8ds\xe1\xbb\x8b \xe1\xbb\xa4ka\0"
	"M\xe1\xbb\x8dnde\0"
	"Tiuzdee\0"
	"Wenezdee\0"
	"T\xe1\xbb\x8d\xe1\xbb\x8dzdee\0"
	"Fra\xe1\xbb\x8b\x64\x65\x65\0"
	"Sat\xe1\xbb\x8d\x64\x65\x65\0"
	"\xe1\xbb\xa4ka\0"
	"M\xe1\xbb\x8dn\0"
	"Tiu\0"
	"Wen\0"
	"T\xe1\xbb\x8d\xe1\xbb\x8d\0"
	"Fra\xe1\xbb\x8b\0"
	"Jen\xe1\xbb\xa5war\xe1\xbb\x8b\0"
	"Febr\xe1\xbb\xa5war\xe1\xbb\x8b\0"
	"Maach\xe1\xbb\x8b\0"
	"Eprel\0"
	"Mee\0"
	"Juun\0"
	"Jula\xe1\xbb\x8b\0"
	"\xe1\xbb\x8cg\xe1\xbb\x8d\xe1\xbb\x8dst\0"
	"\xe1\xbb\x8cktoba\0"
	"Jen\0"
	"Maa\0"
	"Juu\0"
	"\xe1\xbb\x8cg\xe1\xbb\x8d\0"
	"\xe1\xbb\x8ckt\0"
	"M\xe2\x80\x99 \xea\x86\xaa\xe2\x80\x99\x64\xe2\x80\x99 \xea\x91\x8d\xe2\x80\x99\0"
	"\xea\x8e\xb8\xea\x84\x91\0"
	"\xea\x81\xaf\xea\x8b\x92\0"
	"\xea\x91\xad\xea\x86\x8f\xea\x91\x8d\0"
	"\xea\x86\x8f\xea\x8a\x82\xea\x8b\x8d\0"
	"\xea\x86\x8f\xea\x8a\x82\xea\x91\x8d\0"
	"\xea\x86\x8f\xea\x8a\x82\xea\x8c\x95\0"
	"\xea\x86\x8f\xea\x8a\x82\xea\x87\x96\0"
	"\xea\x86\x8f\xea\x8a\x82\xea\x89\xac\0"
	"\xea\x86\x8f\xea\x8a\x82\xea\x83\x98\0"
	"\xea\x91\xad\xea\x86\x8f\0"
	"\xea\x86\x8f\xea\x8b\x8d\0"
	"\xea\x86\x8f\xea\x91\x8d\0"
	"\xea\x86\x8f\xea\x8c\x95\0"
	"\xea\x86\x8f\xea\x87\x96\0"
	"\xea\x86\x8f\xea\x89\xac\0"
	"\xea\x86\x8f\xea\x83\x98\0"
	"\xea\x86\x8f\0"
	"\xea\x8b\x8d\0"
	"\xea\x91\x8d\0"
	"\xea\x8c\x95\0"
	"\xea\x87\x96\0"
	"\xea\x89\xac\0"
	"\xea\x83\x98\0"
	"\xea\x8b\x8d\xea\x86\xaa\0"
	"\xea\x91\x8d\xea\x86\xaa\0"
	"\xea\x8c\x95\xea\x86\xaa\0"
	"\xea\x87\x96\xea\x86\xaa\0"
	"\xea\x89\xac\xea\x86\xaa\0"
	"\xea\x83\x98\xea\x86\xaa\0"
	"\xea\x8f\x83\xea\x86\xaa\0"
	"\xea\x89\x86\xea\x86\xaa\0"
	"\xea\x88\xac\xea\x86\xaa\0"
	"\xea\x8a\xb0\xea\x86\xaa\0"
	"\xea\x8a\xb0\xea\x8a\xaa\xea\x86\xaa\0"
	"\xea\x8a\xb0\xea\x91\x8b\xea\x86\xaa\0"
	"yyyy'\xea\x88\x8e' M'\xea\x86\xaa' d'\xea\x91\x8d'\0"
	"dddd, yyyy'\xea\x88\x8e' M'\xea\x86\xaa' d'\xea\x91\x8d'\0"
	"yyyy'\xea\x88\x8e' M'\xea\x86\xaa' d'\xea\x91\x8d', dddd\0"
	"yyyy\xea\x88\x8e MMM d\xea\x91\x8d\0"
	"dddd, yyyy\xea\x88\x8e MMM d\xea\x91\x8d\0"
	"yyyy'\xea\x88\x8e' M'\xea\x86\xaa'\0"
	"Meurzh\0"
	"Merc\xca\xbcher\0"
	"Yaou\0"
	"Gwener\0"
	"Sadorn\0"
	"sul\0"
	"meu.\0"
	"yaou\0"
	"gwe.\0"
	"sad.\0"
	"lu\0"
	"mz\0"
	"mc\0"
	"ya\0"
	"gw\0"
	"sa\0"
	"Genver\0"
	"C\xca\xbchwevrer\0"
	"Ebrel\0"
	"Mae\0"
	"Mezheven\0"
	"Gouere\0"
	"Eost\0"
	"Gwengolo\0"
	"Here\0"
	"Du\0"
	"Kerzu\0"
	"Gen\0"
	"C\xca\xbchwe\0"
	"Meur\0"
	"Mezh\0"
	"Goue\0"
	"Gwen\0"
	"Ker\0"
	"v.m.\0"
	"n.m.\0"
	"Sunntig\0"
	"M\xc3\xa4\xc3\xa4ntig\0"
	"Ziischtig\0"
	"Mittwuch\0"
	"Dunschtig\0"
	"Friitig\0"
	"Samschtig\0"
	"Su.\0"
	"M\xc3\xa4.\0"
	"Zi.\0"
	"Mi.\0"
	"Du.\0"
	"Fr.\0"
	"Sa.\0"
	"Auguscht\0"
	"Sept\xc3\xa4mber\0"
	"Oktoober\0"
	"Nov\xc3\xa4mber\0"
	"Dez\xc3\xa4mber\0"
	"MMMM d \xd0\xba\xd2\xaf\xd0\xbd\xd1\x8d\0"
	"\xd0\xad\xd0\x98\0"
	"\xd0\xad\xd0\x9a\0"
	"\xd0\x91\xd0\xb0\xd1\x81\xd0\xba\xd1\x8b\xd2\xbb\xd1\x8b\xd0\xb0\xd0\xbd\xd0\xbd\xd1\x8c\xd0\xb0\0"
	"\xd0\x91\xd1\x8d\xd0\xbd\xd0\xb8\xd0\xb4\xd0\xb8\xd1\x8d\xd0\xbb\xd0\xb8\xd0\xbd\xd0\xbd\xd1\x8c\xd0\xb8\xd0\xba\0"
	"\xd0\x9e\xd0\xbf\xd1\x82\xd1\x83\xd0\xbe\xd1\x80\xd1\x83\xd0\xbd\xd0\xbd\xd1\x8c\xd1\x83\xd0\xba\0"
	"\xd0\xa1\xd1\x8d\xd1\x80\xd1\x8d\xd0\xb4\xd1\x8d\0"
	"\xd0\xa7\xd1\x8d\xd0\xbf\xd0\xbf\xd0\xb8\xd1\x8d\xd1\x80\0"
	"\xd0\x91\xd1\x8d\xd1\x8d\xd1\x82\xd0\xb8\xd2\xa5\xd1\x81\xd1\x8d\0"
	"\xd0\xa1\xd1\x83\xd0\xb1\xd1\x83\xd0\xbe\xd1\x82\xd0\xb0\0"
	"\xd0\x91\xd1\x81\0"
	"\xd0\x91\xd0\xbd\0"
	"\xd0\x9e\xd0\xbf\0"
	"\xd0\xa1\xd1\x8d\0"
	"\xd0\xa7\xd0\xbf\0"
	"\xd0\x91\xd1\x8d\0"
	"\xd0\x9e\0"
	"\xd0\xa2\xd0\xbe\xd1\x85\xd1\x81\xd1\x83\xd0\xbd\xd0\xbd\xd1\x8c\xd1\x83\0"
	"\xd0\x9e\xd0\xbb\xd1\x83\xd0\xbd\xd0\xbd\xd1\x8c\xd1\x83\0"
	"\xd0\x9a\xd1\x83\xd0\xbb\xd1\x83\xd0\xbd \xd1\x82\xd1\x83\xd1\x82\xd0\xb0\xd1\x80\0"
	"\xd0\x9c\xd1\x83\xd1\x83\xd1\x81 \xd1\x83\xd1\x81\xd1\x82\xd0\xb0\xd1\x80\0"
	"\xd0\xab\xd0\xb0\xd0\xbc \xd1\x8b\xd0\xb9\xd1\x8b\xd0\xbd\0"
	"\xd0\x91\xd1\x8d\xd1\x81 \xd1\x8b\xd0\xb9\xd1\x8b\xd0\xbd\0"
	"\xd0\x9e\xd1\x82 \xd1\x8b\xd0\xb9\xd1\x8b\xd0\xbd\0"
	"\xd0\x90\xd1\x82\xd1\x8b\xd1\x80\xd0\xb4\xd1\x8c\xd1\x8b\xd1\x85 \xd1\x8b\xd0\xb9\xd1\x8b\xd0\xbd\0"
	"\xd0\x91\xd0\xb0\xd0\xbb\xd0\xb0\xd2\x95\xd0\xb0\xd0\xbd \xd1\x8b\xd0\xb9\xd1\x8b\xd0\xbd\0"
	"\xd0\x90\xd0\xbb\xd1\x82\xd1\x8b\xd0\xbd\xd0\xbd\xd1\x8c\xd1\x8b\0"
	"\xd0\xa1\xd1\x8d\xd1\x82\xd0\xb8\xd0\xbd\xd0\xbd\xd1\x8c\xd0\xb8\0"
	"\xd0\x90\xd1\x85\xd1\x81\xd1\x8b\xd0\xbd\xd0\xbd\xd1\x8c\xd1\x8b\0"
	"\xd0\xa2\xd0\xbe\xd1\x85\xd1\x81\0"
	"\xd0\x9e\xd0\xbb\xd1\x83\xd0\xbd\0"
	"\xd0\x9a\xd0\xbb\xd0\xbd_\xd1\x82\xd1\x82\xd1\x80\0"
	"\xd0\x9c\xd1\x83\xd1\x81_\xd1\x83\xd1\x81\xd1\x82\0"
	"\xd0\xab\xd0\xb0\xd0\xbc_\xd0\xb9\xd0\xbd\0"
	"\xd0\x91\xd1\x8d\xd1\x81_\xd0\xb9\xd0\xbd\0"
	"\xd0\x9e\xd1\x82_\xd0\xb9\xd0\xbd\0"
	"\xd0\x90\xd1\x82\xd1\x80\xd0\xb4\xd1\x8c_\xd0\xb9\xd0\xbd\0"
	"\xd0\x91\xd0\xbb\xd2\x95\xd0\xbd_\xd0\xb9\xd0\xbd\0"
	"\xd0\x90\xd0\xbb\xd1\x82\0"
	"\xd0\xa1\xd1\x8d\xd1\x82\0"
	"\xd0\x90\xd1\x85\xd1\x81\0"
	"yyyy MM d\0"
	"dd yyyy MM d\0"
	"dddd, yyyy '\xd1\x81.' MMMM d '\xd0\xba\xd2\xaf\xd0\xbd\xd1\x8d'\0"
	"yyyy '\xd1\x81.' MMMM d '\xd0\xba\xd2\xaf\xd0\xbd\xd1\x8d'\0"
	"dddd, MMMM d '\xd0\xba\xd2\xaf\xd0\xbd\xd1\x8d' yyyy '\xd1\x81.'\0"
	"yyyy '\xd1\x81.' MMMM\0"
	"Ku cyumweru\0"
	"Kuwa mbere\0"
	"Kuwa kabiri\0"
	"Kuwa gatatu\0"
	"Kuwa kane\0"
	"Kuwa gatanu\0"
	"Kuwa gatandatu\0"
	"cyu.\0"
	"mbe.\0"
	"kab.\0"
	"gtu.\0"
	"kan.\0"
	"gnu.\0"
	"gnd.\0"
	"Mutarama\0"
	"Gashyantare\0"
	"Werurwe\0"
	"Mata\0"
	"Gicuransi\0"
	"Kamena\0"
	"Nyakanga\0"
	"Kanama\0"
	"Nzeli\0"
	"Ukwakira\0"
	"Ugushyingo\0"
	"Ukuboza\0"
	"mut.\0"
	"gas.\0"
	"wer.\0"
	"mat.\0"
	"gic.\0"
	"kam.\0"
	"nya.\0"
	"nze.\0"
	"ukw.\0"
	"ugu.\0"
	"uku.\0"
	"d'mh' MMMM\0"
	"m\0"
	"f\0"
	"DiD\xc3\xb2mhnaich\0"
	"DiLuain\0"
	"DiM\xc3\xa0irt\0"
	"DiCiadain\0"
	"Diardaoin\0"
	"DihAoine\0"
	"DiSathairne\0"
	"DiD\0"
	"DiL\0"
	"DiM\0"
	"DiC\0"
	"Dia\0"
	"Dih\0"
	"DiS\0"
	"Am Faoilleach\0"
	"An Gearran\0"
	"Am M\xc3\xa0rt\0"
	"An Giblean\0"
	"An C\xc3\xa8itean\0"
	"An t-\xc3\x92gmhios\0"
	"An t-Iuchar\0"
	"An L\xc3\xb9nastal\0"
	"An t-Sultain\0"
	"An D\xc3\xa0mhair\0"
	"An t-Samhain\0"
	"An D\xc3\xb9\x62hlachd\0"
	"Faoi\0"
	"Gearr\0"
	"M\xc3\xa0rt\0"
	"Gibl\0"
	"C\xc3\xa8it\0"
	"\xc3\x92gmh\0"
	"Iuch\0"
	"L\xc3\xb9na\0"
	"Sult\0"
	"D\xc3\xa0mh\0"
	"D\xc3\xb9\x62h\0"
	"dddd, d'mh' MMMM yyyy\0"
	"d'mh' MMMM yyyy\0"
	"\xe9\x80\xb1\xe6\x97\xa5\0"
	"\xe9\x80\xb1\xe4\xb8\x80\0"
	"\xe9\x80\xb1\xe4\xba\x8c\0"
	"\xe9\x80\xb1\xe4\xb8\x89\0"
	"\xe9\x80\xb1\xe5\x9b\x9b\0"
	"\xe9\x80\xb1\xe4\xba\x94\0"
	"\xe9\x80\xb1\xe5\x85\xad\0"
	"dddd yyyy\xe5\xb9\xb4MMMd\xe6\x97\xa5\0"
	"\xd8\xb5\0"
	"\xd9\x85\0"
	"\xd8\xa7\xd9\x84\xd8\xa3\xd8\xad\xd8\xaf\0"
	"\xd8\xa7\xd9\x84\xd8\xa7\xd8\xab\xd9\x86\xd9\x8a\xd9\x86\0"
	"\xd8\xa7\xd9\x84\xd8\xab\xd9\x84\xd8\xa7\xd8\xab\xd8\xa7\xd8\xa1\0"
	"\xd8\xa7\xd9\x84\xd8\xa3\xd8\xb1\xd8\xa8\xd8\xb9\xd8\xa7\xd8\xa1\0"
	"\xd8\xa7\xd9\x84\xd8\xae\xd9\x85\xd9\x8a\xd8\xb3\0"
	"\xd8\xa7\xd9\x84\xd8\xac\xd9\x85\xd8\xb9\xd8\xa9\0"
	"\xd8\xa7\xd9\x84\xd8\xb3\xd8\xa8\xd8\xaa\0"
	"\xd8\xad\0"
	"\xd9\x86\0"
	"\xd8\xab\0"
	"\xd8\xb1\0"
	"\xd8\xae\0"
	"\xd9\x83\xd8\xa7\xd9\x86\xd9\x88\xd9\x86 \xd8\xa7\xd9\x84\xd8\xab\xd8\xa7\xd9\x86\xd9\x8a\0"
	"\xd8\xb4\xd8\xa8\xd8\xa7\xd8\xb7\0"
	"\xd8\xa2\xd8\xb0\xd8\xa7\xd8\xb1\0"
	"\xd9\x86\xd9\x8a\xd8\xb3\xd8\xa7\xd9\x86\0"
	"\xd8\xa3\xd9\x8a\xd8\xa7\xd8\xb1\0"
	"\xd8\xad\xd8\xb2\xd9\x8a\xd8\xb1\xd8\xa7\xd9\x86\0"
	"\xd8\xaa\xd9\x85\xd9\x88\xd8\xb2\0"
	"\xd8\xa2\xd8\xa8\0"
	"\xd8\xa3\xd9\x8a\xd9\x84\xd9\x88\xd9\x84\0"
	"\xd8\xaa\xd8\xb4\xd8\xb1\xd9\x8a\xd9\x86 \xd8\xa7\xd9\x84\xd8\xa3\xd9\x88\xd9\x84\0"
	"\xd8\xaa\xd8\xb4\xd8\xb1\xd9\x8a\xd9\x86 \xd8\xa7\xd9\x84\xd8\xab\xd8\xa7\xd9\x86\xd9\x8a\0"
	"\xd9\x83\xd8\xa7\xd9\x86\xd9\x88\xd9\x86 \xd8\xa7\xd9\x84\xd8\xa3\xd9\x88\xd9\x84\0"
	"dd.MMMM.\0"
	"dom.\0"
	"mi\xc3\xa9.\0"
	"jue.\0"
	"vie\0"
	"s\xc3\xa1\x62.\0"
	"en.\0"
	"mzo.\0"
	"my.\0"
	"dic.\0"
	"dd.MMMM\0"
	"MMMM.yyyy\0"
	"H.mm' u.'\0"
	"f.m.\0"
	"e.m.\0"
	"tysdag\0"
	"laurdag\0"
	"tys\0"
	"lau\0"
	"Janeiro\0"
	"Fevereiro\0"
	"Mar\xc3\xa7o\0"
	"Junho\0"
	"Julho\0"
	"Dezembro\0"
	"yy.MM.dd\0"
	"\xd0\xb1\xd0\xb0\xd0\xb7\xd0\xb0\xd1\x80\0"
	"\xd0\xb1\xd0\xb0\xd0\xb7\xd0\xb0\xd1\x80 \xd0\xb5\xd1\x80\xd1\x82\xd3\x99\xd1\x81\xd0\xb8\0"
	"\xd1\x87\xd3\x99\xd1\x80\xd1\x88\xd3\x99\xd0\xbd\xd0\xb1\xd3\x99 \xd0\xb0\xd1\x85\xd1\x88\xd0\xb0\xd0\xbc\xd1\x8b\0"
	"\xd1\x87\xd3\x99\xd1\x80\xd1\x88\xd3\x99\xd0\xbd\xd0\xb1\xd3\x99\0"
	"\xd2\xb9\xd2\xaf\xd0\xbc\xd3\x99 \xd0\xb0\xd1\x85\xd1\x88\xd0\xb0\xd0\xbc\xd1\x8b\0"
	"\xd2\xb9\xd2\xaf\xd0\xbc\xd3\x99\0"
	"\xd1\x88\xd3\x99\xd0\xbd\xd0\xb1\xd3\x99\0"
	"\xd1\x98\xd0\xb0\xd0\xbd\xd0\xb2\xd0\xb0\xd1\x80\0"
	"\xd1\x84\xd0\xb5\xd0\xb2\xd1\x80\xd0\xb0\xd0\xbb\0"
	"\xd0\xb0\xd0\xbf\xd1\x80\xd0\xb5\xd0\xbb\0"
	"\xd0\xb8\xd1\x98\xd1\x83\xd0\xbd\0"
	"\xd0\xb8\xd1\x98\xd1\x83\xd0\xbb\0"
	"\xd1\x81\xd0\xb5\xd0\xbd\xd1\x82\xd1\x98\xd0\xb0\xd0\xb1\xd1\x80\0"
	"\xd0\xbe\xd0\xba\xd1\x82\xd1\x98\xd0\xb0\xd0\xb1\xd1\x80\0"
	"\xd0\xbd\xd0\xbe\xd1\x98\xd0\xb0\xd0\xb1\xd1\x80\0"
	"\xd0\xb4\xd0\xb5\xd0\xba\xd0\xb0\xd0\xb1\xd1\x80\0"
	"\xd1\x8f\xd0\xba\xd1\x88\xd0\xb0\xd0\xbd\xd0\xb1\xd0\xb0\0"
	"\xd0\xb4\xd1\x83\xd1\x88\xd0\xb0\xd0\xbd\xd0\xb1\xd0\xb0\0"
	"\xd1\x81\xd0\xb5\xd1\x88\xd0\xb0\xd0\xbd\xd0\xb1\xd0\xb0\0"
	"\xd1\x87\xd0\xbe\xd1\x80\xd1\x88\xd0\xb0\xd0\xbd\xd0\xb1\xd0\xb0\0"
	"\xd0\xbf\xd0\xb0\xd0\xb9\xd1\x88\xd0\xb0\xd0\xbd\xd0\xb1\xd0\xb0\0"
	"\xd0\xb6\xd1\x83\xd0\xbc\xd0\xb0\0"
	"\xd1\x88\xd0\xb0\xd0\xbd\xd0\xb1\xd0\xb0\0"
	"\xd0\xaf\xd0\xba\xd1\x88\0"
	"\xd0\x94\xd1\x83\xd1\x88\0"
	"\xd0\xa1\xd0\xb5\xd1\x88\0"
	"\xd0\xa7\xd0\xbe\xd1\x80\0"
	"\xd0\x9f\xd0\xb0\xd0\xb9\0"
	"\xd0\xa8\xd0\xb0\xd0\xbd\0"
	"\xd0\xaf\0"
	"\xd9\x8a\xd9\x86\xd8\xa7\xd9\x8a\xd8\xb1\0"
	"\xd9\x81\xd8\xa8\xd8\xb1\xd8\xa7\xd9\x8a\xd8\xb1\0"
	"\xd8\xa3\xd8\xa8\xd8\xb1\xd9\x8a\xd9\x84\0"
	"\xd9\x85\xd8\xa7\xd9\x8a\xd9\x88\0"
	"\xd9\x8a\xd9\x88\xd9\x86\xd9\x8a\xd9\x88\0"
	"\xd9\x8a\xd9\x88\xd9\x84\xd9\x8a\xd9\x88\0"
	"\xd8\xa3\xd8\xba\xd8\xb3\xd8\xb7\xd8\xb3\0"
	"\xd8\xb3\xd8\xa8\xd8\xaa\xd9\x85\xd8\xa8\xd8\xb1\0"
	"\xd8\xa3\xd9\x83\xd8\xaa\xd9\x88\xd8\xa8\xd8\xb1\0"
	"\xd9\x86\xd9\x88\xd9\x81\xd9\x85\xd8\xa8\xd8\xb1\0"
	"\xd8\xaf\xd9\x8a\xd8\xb3\xd9\x85\xd8\xa8\xd8\xb1\0"
	"J\xc3\xa4nner\0"
	"J\xc3\xa4n\0"
	"yy MM dd\0"
	"MMMM d'. b.'\0"
	"aejlege\0"
	"m\xc3\xa5\x61nta\0"
	"d\xc3\xa4jsta\0"
	"gaskevahkoe\0"
	"d\xc3\xa5\x61rsta\0"
	"bearjadahke\0"
	"laavadahke\0"
	"o\xc4\x91\xc4\x91\x61jage\0"
	"guovva\0"
	"njuk\xc4\x8d\x61\0"
	"cuo\xc5\x8bo\0"
	"miesse\0"
	"geasse\0"
	"suoidne\0"
	"borge\0"
	"\xc4\x8d\x61k\xc4\x8d\x61\0"
	"golggot\0"
	"sk\xc3\xa1\x62ma\0"
	"juovla\0"
	"dddd', 'MMMM d'. b. 'yyyy\0"
	"MMMM d'. b. 'yyyy\0"
	"M/dd/yy\0"
	"MMMM-dd-yy\0"
	"dd-MMMM\0"
	"dddd, d 'de' MMMM 'de' yyyy\0"
	"d 'de' MMMM 'de' yyyy\0"
	"d. MMM yyyy.\0"
	"MMMM yyyy.\0"
	"\xd8\xac\xd8\xa7\xd9\x86\xd9\x81\xd9\x8a\0"
	"\xd9\x81\xd9\x8a\xd9\x81\xd8\xb1\xd9\x8a\0"
	"\xd8\xa3\xd9\x81\xd8\xb1\xd9\x8a\xd9\x84\0"
	"\xd9\x85\xd8\xa7\xd9\x8a\0"
	"\xd8\xac\xd9\x88\xd8\xa7\xd9\x86\0"
	"\xd8\xac\xd9\x88\xd9\x8a\xd9\x84\xd9\x8a\xd8\xa9\0"
	"\xd8\xa3\xd9\x88\xd8\xaa\0"
	"dddd yyyy'\xe5\xb9\xb4'M'\xe6\x9c\x88'd'\xe6\x97\xa5'\0"
	"dddd yyyy MM dd\0"
	"d/MMMM\0"
	"MMMM/yyyy\0"
	"pre podne\0"
	"popodne\0"
	"septembar\0"
	"oktobar\0"
	"novembar\0"
	"decembar\0"
	"dd. MMM. yyyy.\0"
	"dddd, dd. MMMM yyyy.\0"
	"\xd9\x8a\xd9\x88\xd9\x84\xd9\x8a\xd9\x88\xd8\xb2\0"
	"\xd8\xba\xd8\xb4\xd8\xaa\0"
	"\xd8\xb4\xd8\xaa\xd9\x86\xd8\xa8\xd8\xb1\0"
	"\xd9\x86\xd9\x88\xd9\x86\xd8\xa8\xd8\xb1\0"
	"\xd8\xaf\xd8\xac\xd9\x86\xd8\xa8\xd8\xb1\0"
	"MMMM/dd\0"
	"ponedeljak\0"
	"\xd0\xbf\xd1\x80\xd0\xb5 \xd0\xbf\xd0\xbe\xd0\xb4\xd0\xbd\xd0\xb5\0"
	"\xd0\xbf\xd0\xbe\xd0\xbf\xd0\xbe\xd0\xb4\xd0\xbd\xd0\xb5\0"
	"\xd0\xbd\xd0\xb5\xd0\xb4\xd0\xb5\xd1\x99\xd0\xb0\0"
	"\xd0\xbf\xd0\xbe\xd0\xbd\xd0\xb5\xd0\xb4\xd0\xb5\xd1\x99\xd0\xb0\xd0\xba\0"
	"\xd1\x83\xd1\x82\xd0\xbe\xd1\x80\xd0\xb0\xd0\xba\0"
	"\xd1\x81\xd1\x80\xd0\xb8\xd1\x98\xd0\xb5\xd0\xb4\xd0\xb0\0"
	"\xd1\x87\xd0\xb5\xd1\x82\xd0\xb2\xd1\x80\xd1\x82\xd0\xb0\xd0\xba\0"
	"\xd0\xbf\xd0\xb5\xd1\x82\xd0\xb0\xd0\xba\0"
	"\xd0\xbd\xd0\xb5\xd0\xb4\0"
	"\xd0\xbf\xd0\xbe\xd0\xbd\0"
	"\xd1\x83\xd1\x82\xd0\xbe\0"
	"\xd1\x81\xd1\x80\xd0\xb8\0"
	"\xd1\x87\xd0\xb5\xd1\x82\0"
	"\xd0\xbf\xd0\xb5\xd1\x82\0"
	"\xd1\x81\xd1\x83\xd0\xb1\0"
	"\xd1\x83\0"
	"\xd1\x98\xd0\xb0\xd0\xbd\xd1\x83\xd0\xb0\xd1\x80\0"
	"\xd1\x84\xd0\xb5\xd0\xb1\xd1\x80\xd1\x83\xd0\xb0\xd1\x80\0"
	"\xd1\x81\xd0\xb5\xd0\xbf\xd1\x82\xd0\xb5\xd0\xbc\xd0\xb1\xd0\xb0\xd1\x80\0"
	"\xd0\xbe\xd0\xba\xd1\x82\xd0\xbe\xd0\xb1\xd0\xb0\xd1\x80\0"
	"\xd0\xbd\xd0\xbe\xd0\xb2\xd0\xb5\xd0\xbc\xd0\xb1\xd0\xb0\xd1\x80\0"
	"\xd0\xb4\xd0\xb5\xd1\x86\xd0\xb5\xd0\xbc\xd0\xb1\xd0\xb0\xd1\x80\0"
	"\xd1\x98\xd0\xb0\xd0\xbd\0"
	"\xd1\x84\xd0\xb5\xd0\xb1\0"
	"\xd0\xbc\xd0\xb0\xd1\x80\0"
	"\xd0\xb0\xd0\xbf\xd1\x80\0"
	"\xd1\x98\xd1\x83\xd0\xbd\0"
	"\xd1\x98\xd1\x83\xd0\xbb\0"
	"\xd0\xb0\xd0\xb2\xd0\xb3\0"
	"\xd1\x81\xd0\xb5\xd0\xbf\0"
	"\xd0\xbe\xd0\xba\xd1\x82\0"
	"\xd0\xbd\xd0\xbe\xd0\xb2\0"
	"\xd0\xb4\xd0\xb5\xd1\x86\0"
	"d. M. yy\0"
	"dd. MM. yy\0"
	"\xd1\x81\xd1\x80\xd0\xb5\0"
	"MMMM-yyyy\0"
	"dd MMM,yyyy\0"
	"yyyy-MM-dd.\0"
	"h.mm.ss tt\0"
	"dddd dd 'de' MMMM 'de' yyyy\0"
	"dd 'de' MMMM 'de' yyyy\0"
	",\0"
	"\xd8\xb1.\xd8\xb3.\xe2\x80\x8f\0"
	"\xd9\xaa\0"
	"\xd9\x84\xd9\x8a\xd8\xb3\xc2\xa0\xd8\xb1\xd9\x82\xd9\x85\0"
	"\xd8\x89\0"
	"-Infinity\0"
	"Infinity\0"
	"\xe2\x80\x8f+\0"
	"\xc2\xa0\0"
	"\xd0\xbb\xd0\xb2.\0"
	"%\0"
	"NaN\0"
	"\xe2\x80\xb0\0"
	"+\0"
	"\xe2\x82\xac\0"
	"-Infinit\0"
	"Infinit\0"
	"\xc2\xa5\0"
	"K\xc4\x8d\0"
	"-nekone\xc4\x8dno\0"
	"+nekone\xc4\x8dno\0"
	"kr\0"
	"-unendlich\0"
	"+unendlich\0"
	"-\xce\x86\xcf\x80\xce\xb5\xce\xb9\xcf\x81\xce\xbf\0"
	"\xce\x86\xcf\x80\xce\xb5\xce\xb9\xcf\x81\xce\xbf\0"
	"$\0"
	"-Infinito\0"
	"Infinito\0"
	"ep\xc3\xa4luku\0"
	"-Infini\0"
	"+Infini\0"
	"\xe2\x82\xaa\0"
	"\xe2\x80\x8e+\0"
	"Ft\0"
	"+Infinito\0"
	"\xef\xbf\xa5\0"
	"\xe2\x82\xa9\0"
	"-oneindig\0"
	"oneindig\0"
	"z\xc5\x82\0"
	"-niesko\xc5\x84\x63zono\xc5\x9b\xc4\x87\0"
	"+niesko\xc5\x84\x63zono\xc5\x9b\xc4\x87\0"
	"R$\0"
	"\xe2\x80\x99\0"
	"CHF\0"
	"-infinit\0"
	"+infinit\0"
	"\xd1\x80\xd1\x83\xd0\xb1.\0"
	"\xd0\xbd\xd0\xb5\xc2\xa0\xd1\x87\xd0\xb8\xd1\x81\xd0\xbb\xd0\xbe\0"
	"-\xd0\xb1\xd0\xb5\xd1\x81\xd0\xba\xd0\xbe\xd0\xbd\xd0\xb5\xd1\x87\xd0\xbd\xd0\xbe\xd1\x81\xd1\x82\xd1\x8c\0"
	"\xd0\xb1\xd0\xb5\xd1\x81\xd0\xba\xd0\xbe\xd0\xbd\xd0\xb5\xd1\x87\xd0\xbd\xd0\xbe\xd1\x81\xd1\x82\xd1\x8c\0"
	"kn\0"
	"Lek\xc3\xab\0"
	"\xc2\xa4\xc2\xa4\xc2\xa4\0"
	"\xe0\xb8\xbf\0"
	"\xe2\x82\xba\0"
	"Rs\0"
	"\xdb\x8c\xdb\x81\xc2\xa0\xd8\xb9\xd8\xaf\xd8\xaf\xc2\xa0\xd9\x86\xdb\x81\xdb\x8c\xda\xba\0"
	"\xe2\x80\x8e-\xe2\x80\x8eInfinity\0"
	"\xe2\x80\x8e-\xe2\x80\x8e\0"
	"\xe2\x80\x8e+\xe2\x80\x8e\0"
	"Rp\0"
	"\xe2\x82\xb4\0"
	"\xd0\x9d\xd0\xb5\xc2\xa0\xd1\x87\xd0\xb8\xd1\x81\xd0\xbb\xd0\xbe\0"
	"\xd1\x80.\0"
	"-neskon\xc4\x8dnost\0"
	"neskon\xc4\x8dnost\0"
	"nav\xc2\xa0skaitlis\0"
	"-bezgal\xc4\xab\x62\x61\0"
	"bezgal\xc4\xab\x62\x61\0"
	"Lt\0"
	"-begalyb\xc4\x97\0"
	"begalyb\xc4\x97\0"
	"\xd8\xb1\xdb\x8c\xd8\xa7\xd9\x84\0"
	"\xd9\x86\xd8\xa7\xd8\xb9\xd8\xaf\xd8\xaf\0"
	"\xe2\x80\x8e\xe2\x88\x92Infinity\0"
	"\xe2\x80\x8e\xe2\x88\x92\0"
	"\xe2\x82\xab\0"
	"\xd5\xa4\xd6\x80.\0"
	"-Infinitu\0"
	"Infinitu\0"
	"\xd0\xb4\xd0\xb5\xd0\xbd\0"
	"I-NaN\0"
	"\xe1\x83\x90\xe1\x83\xa0\xc2\xa0\xe1\x83\x90\xe1\x83\xa0\xe1\x83\x98\xe1\x83\xa1\xc2\xa0\xe1\x83\xa0\xe1\x83\x98\xe1\x83\xaa\xe1\x83\xae\xe1\x83\x95\xe1\x83\x98\0"
	"\xe2\x82\xb9\0"
	"RM\0"
	"\xd1\x81\xd0\xb0\xd0\xbd\xc2\xa0\xd1\x8d\xd0\xbc\xd0\xb5\xd1\x81\0"
	"Ksh\0"
	"\xe0\xa6\x9f\xe0\xa6\xbe\0"
	"\xe0\xa6\xb8\xe0\xa6\x82\xe0\xa6\x96\xe0\xa7\x8d\xe0\xa6\xaf\xe0\xa6\xbe\xc2\xa0\xe0\xa6\xa8\xe0\xa6\xbe\0"
	"`\0"
	"\xc2\xa3\0"
	"\xe1\x9f\x9b\0"
	"\xe2\x82\xad\0"
	"\xe0\xba\x9a\xe0\xbb\x8d\xe0\xbb\x88\xe0\xbb\x81\xe0\xba\xa1\xe0\xbb\x88\xe0\xba\x99\xe0\xbb\x82\xe0\xba\x95\xe0\xbb\x80\xe0\xba\xa5\xe0\xba\x81\0"
	"\xe0\xb6\xbb\xe0\xb7\x94.\0"
	"\xe1\x89\xa5\xe1\x88\xad\0"
	"\xe0\xa4\xa8\xe0\xa5\x87\xe0\xa4\xb0\xe0\xa5\x82\0"
	"\xd8\x8b\0"
	"\xe2\x82\xb1\0"
	"\xe2\x82\xa6\0"
	"RF\0"
	"NT$\0"
	"\xe9\x9d\x9e\xe6\x95\xb8\xe5\x80\xbc\0"
	"\xd1\x81\xd0\xbe\xd0\xbc\0"
	"man.\0"
	"so\xca\xbbm\0"
	"\xd8\xaf.\xd8\xb9.\xe2\x80\x8f\0"
	"'\0"
	"\xd0\xbc\xd0\xb0\xd0\xbd.\0"
	"\xd1\x81\xd1\x9e\xd0\xbc\0"
	"\xe0\xa7\xb3\0"
	"\xd8\xac.\xd9\x85.\xe2\x80\x8f\0"
	"\xd8\xaf.\xd9\x84.\xe2\x80\x8f\0"
	"KM\0"
	"\xd8\xaf.\xd8\xac.\xe2\x80\x8f\0"
	"MOP$\0"
	"\xe2\x82\xa1\0"
	"\xd8\xaf.\xd9\x85.\xe2\x80\x8f\0"
	"B/.\0"
	"\xd8\xaf.\xd8\xaa.\xe2\x80\x8f\0"
	"\xd0\x9a\xd0\x9c\0"
	"\xd8\xb1.\xd8\xb9.\xe2\x80\x8f\0"
	"Bs.\0"
	"\xd8\xb1.\xd9\x8a.\xe2\x80\x8f\0"
	"din.\0"
	"\xd9\x84.\xd8\xb3.\xe2\x80\x8f\0"
	"S/.\0"
	"\xd0\xb4\xd0\xb8\xd0\xbd.\0"
	"\xd8\xaf.\xd8\xa3.\xe2\x80\x8f\0"
	"\xd9\x84.\xd9\x84.\xe2\x80\x8f\0"
	"\xd8\xaf.\xd9\x83.\xe2\x80\x8f\0"
	"\xd8\xaf.\xd8\xa5.\xe2\x80\x8f\0"
	"\xd8\xaf.\xd8\xa8.\xe2\x80\x8f\0"
	"\xe2\x82\xb2\0"
	"\xd8\xb1.\xd9\x82.\xe2\x80\x8f\0"
	"Bs\0"
	"C$\0"
	"\xd0\x94\xd0\xb8\xd0\xbd.\0"
	"Din.\0"
	"HK$\0"
	"ar\0"
	"Arabic\0"
	"\xd8\xa7\xd9\x84\xd8\xb9\xd8\xb1\xd8\xa8\xd9\x8a\xd8\xa9\0"
	"ARA\0"
	"ara\0"
	"\xd8\xa7\xd9\x84\xd8\xaa\xd9\x82\xd9\x88\xd9\x8a\xd9\x85 \xd8\xa7\xd9\x84\xd9\x87\xd8\xac\xd8\xb1\xd9\x8a\0"
	"bg\0"
	"Bulgarian\0"
	"\xd0\xb1\xd1\x8a\xd0\xbb\xd0\xb3\xd0\xb0\xd1\x80\xd1\x81\xd0\xba\xd0\xb8\0"
	"BGR\0"
	"bul\0"
	"\xd0\x93\xd1\x80\xd0\xb8\xd0\xb3\xd0\xbe\xd1\x80\xd0\xb8\xd0\xb0\xd0\xbd\xd1\x81\xd0\xba\xd0\xb8 \xd0\xba\xd0\xb0\xd0\xbb\xd0\xb5\xd0\xbd\xd0\xb4\xd0\xb0\xd1\x80\0"
	"ca\0"
	"Catalan\0"
	"catal\xc3\xa0\0"
	"CAT\0"
	"cat\0"
	"calendari gregori\xc3\xa0\0"
	"zh-CHS\0"
	"Chinese (Simplified) Legacy\0"
	"\xe4\xb8\xad\xe6\x96\x87\0"
	"CHS\0"
	"zho\0"
	"zh\0"
	"\xe5\x85\xac\xe5\x8e\x86\0"
	"zh-Hans\0"
	"Chinese (Simplified)\0"
	"cs\0"
	"Czech\0"
	"\xc4\x8d\x65\xc5\xa1tina\0"
	"CSY\0"
	"ces\0"
	"Gregori\xc3\xa1nsk\xc3\xbd kalend\xc3\xa1\xc5\x99\0"
	"da\0"
	"Danish\0"
	"dansk\0"
	"DAN\0"
	"dan\0"
	"gregoriansk kalender\0"
	"de\0"
	"German\0"
	"Deutsch\0"
	"DEU\0"
	"deu\0"
	"Gregorianischer Kalender\0"
	"el\0"
	"Greek\0"
	"\xce\x95\xce\xbb\xce\xbb\xce\xb7\xce\xbd\xce\xb9\xce\xba\xce\xac\0"
	"ELL\0"
	"ell\0"
	"\xce\x93\xcf\x81\xce\xb7\xce\xb3\xce\xbf\xcf\x81\xce\xb9\xce\xb1\xce\xbd\xcf\x8c \xce\xb7\xce\xbc\xce\xb5\xcf\x81\xce\xbf\xce\xbb\xcf\x8c\xce\xb3\xce\xb9\xce\xbf\0"
	"en\0"
	"English\0"
	"ENU\0"
	"eng\0"
	"Gregorian Calendar\0"
	"es\0"
	"Spanish\0"
	"espa\xc3\xb1ol\0"
	"ESP\0"
	"spa\0"
	"calendario gregoriano\0"
	"fi\0"
	"Finnish\0"
	"suomi\0"
	"FIN\0"
	"fin\0"
	"gregoriaaninen kalenteri\0"
	"fr\0"
	"French\0"
	"fran\xc3\xa7\x61is\0"
	"FRA\0"
	"fra\0"
	"calendrier gr\xc3\xa9gorien\0"
	"he\0"
	"Hebrew\0"
	"\xd7\xa2\xd7\x91\xd7\xa8\xd7\x99\xd7\xaa\0"
	"HEB\0"
	"heb\0"
	"\xd7\x9c\xd7\x95\xd7\x97 \xd7\xa9\xd7\xa0\xd7\x94 \xd7\x92\xd7\xa8\xd7\x92\xd7\x95\xd7\xa8\xd7\x99\xd7\x90\xd7\xa0\xd7\x99\0"
	"hu\0"
	"Hungarian\0"
	"magyar\0"
	"HUN\0"
	"hun\0"
	"Gergely-napt\xc3\xa1r\0"
	"is\0"
	"Icelandic\0"
	"\xc3\xadslenska\0"
	"ISL\0"
	"isl\0"
	"Gregor\xc3\xadskt dagatal\0"
	"it\0"
	"Italian\0"
	"italiano\0"
	"ITA\0"
	"ita\0"
	"Calendario gregoriano\0"
	"ja\0"
	"Japanese\0"
	"\xe6\x97\xa5\xe6\x9c\xac\xe8\xaa\x9e\0"
	"JPN\0"
	"jpn\0"
	"\xe8\xa5\xbf\xe6\x9a\xa6(\xe3\x82\xb0\xe3\x83\xac\xe3\x82\xb4\xe3\x83\xaa\xe3\x82\xaa\xe6\x9a\xa6)\0"
	"ko\0"
	"Korean\0"
	"\xed\x95\x9c\xea\xb5\xad\xec\x96\xb4\0"
	"KOR\0"
	"kor\0"
	"\xed\x83\x9c\xec\x96\x91\xeb\xa0\xa5\0"
	"nl\0"
	"Dutch\0"
	"Nederlands\0"
	"NLD\0"
	"nld\0"
	"Gregoriaanse kalender\0"
	"no\0"
	"Norwegian\0"
	"norsk\0"
	"NOR\0"
	"nob\0"
	"nb\0"
	"pl\0"
	"Polish\0"
	"polski\0"
	"PLK\0"
	"pol\0"
	"kalendarz gregoria\xc5\x84ski\0"
	"pt\0"
	"Portuguese\0"
	"portugu\xc3\xaas\0"
	"PTB\0"
	"por\0"
	"Calend\xc3\xa1rio Gregoriano\0"
	"rm\0"
	"Romansh\0"
	"rumantsch\0"
	"RMC\0"
	"roh\0"
	"chalender gregorian\0"
	"ro\0"
	"Romanian\0"
	"rom\xc3\xa2n\xc4\x83\0"
	"ROM\0"
	"ron\0"
	"calendar gregorian\0"
	"ru\0"
	"Russian\0"
	"\xd1\x80\xd1\x83\xd1\x81\xd1\x81\xd0\xba\xd0\xb8\xd0\xb9\0"
	"RUS\0"
	"rus\0"
	"\xd0\x93\xd1\x80\xd0\xb8\xd0\xb3\xd0\xbe\xd1\x80\xd0\xb8\xd0\xb0\xd0\xbd\xd1\x81\xd0\xba\xd0\xb8\xd0\xb9 \xd0\xba\xd0\xb0\xd0\xbb\xd0\xb5\xd0\xbd\xd0\xb4\xd0\xb0\xd1\x80\xd1\x8c\0"
	"hr\0"
	"Croatian\0"
	"hrvatski\0"
	"HRV\0"
	"hrv\0"
	"gregorijanski kalendar\0"
	"Slovak\0"
	"sloven\xc4\x8dina\0"
	"SKY\0"
	"slk\0"
	"Gregori\xc3\xa1nsky kalend\xc3\xa1r\0"
	"sq\0"
	"Albanian\0"
	"Shqip\0"
	"SQI\0"
	"sqi\0"
	"Kalendari Gregorian\0"
	"sv\0"
	"Swedish\0"
	"svenska\0"
	"SVE\0"
	"swe\0"
	"th\0"
	"Thai\0"
	"\xe0\xb9\x84\xe0\xb8\x97\xe0\xb8\xa2\0"
	"THA\0"
	"tha\0"
	"\xe0\xb8\x9b\xe0\xb8\x8f\xe0\xb8\xb4\xe0\xb8\x97\xe0\xb8\xb4\xe0\xb8\x99\xe0\xb8\x9e\xe0\xb8\xb8\xe0\xb8\x97\xe0\xb8\x98\0"
	"Turkish\0"
	"T\xc3\xbcrk\xc3\xa7\x65\0"
	"TRK\0"
	"tur\0"
	"Miladi Takvim\0"
	"ur\0"
	"Urdu\0"
	"\xd8\xa7\xd8\xb1\xd8\xaf\xd9\x88\0"
	"URD\0"
	"urd\0"
	"\xd8\xac\xd8\xa7\xd8\xb1\xd8\xac\xdb\x8c\xd8\xa7\xd8\xa6\xdb\x8c \xda\xa9\xdb\x8c\xd9\x84\xd9\x86\xda\x88\xd8\xb1\0"
	"id\0"
	"Indonesian\0"
	"Bahasa Indonesia\0"
	"IND\0"
	"ind\0"
	"Kalender Gregorian\0"
	"uk\0"
	"Ukrainian\0"
	"\xd1\x83\xd0\xba\xd1\x80\xd0\xb0\xd1\x97\xd0\xbd\xd1\x81\xd1\x8c\xd0\xba\xd0\xb0\0"
	"UKR\0"
	"ukr\0"
	"\xd0\x93\xd1\x80\xd0\xb8\xd0\xb3\xd0\xbe\xd1\x80\xd1\x96\xd0\xb0\xd0\xbd\xd1\x81\xd1\x8c\xd0\xba\xd0\xb8\xd0\xb9 \xd0\xba\xd0\xb0\xd0\xbb\xd0\xb5\xd0\xbd\xd0\xb4\xd0\xb0\xd1\x80\0"
	"be\0"
	"Belarusian\0"
	"\xd0\xb1\xd0\xb5\xd0\xbb\xd0\xb0\xd1\x80\xd1\x83\xd1\x81\xd0\xba\xd0\xb0\xd1\x8f\0"
	"BEL\0"
	"bel\0"
	"\xd0\xb3\xd1\x80\xd1\x8d\xd0\xb3\xd0\xb0\xd1\x80\xd1\x8b\xd1\x8f\xd0\xbd\xd1\x81\xd0\xba\xd1\x96 \xd0\xba\xd0\xb0\xd0\xbb\xd1\x8f\xd0\xbd\xd0\xb4\xd0\xb0\xd1\x80\0"
	"sl\0"
	"Slovenian\0"
	"sloven\xc5\xa1\xc4\x8dina\0"
	"SLV\0"
	"slv\0"
	"gregorijanski koledar\0"
	"et\0"
	"Estonian\0"
	"eesti\0"
	"ETI\0"
	"est\0"
	"Gregoriuse kalender\0"
	"lv\0"
	"Latvian\0"
	"latvie\xc5\xa1u\0"
	"LVI\0"
	"lav\0"
	"Gregora kalend\xc4\x81rs\0"
	"lt\0"
	"Lithuanian\0"
	"lietuvi\xc5\xb3\0"
	"LTH\0"
	"lit\0"
	"Grigaliaus kalendorius\0"
	"tg\0"
	"Tajik\0"
	"\xd0\xa2\xd0\xbe\xd2\xb7\xd0\xb8\xd0\xba\xd3\xa3\0"
	"TAJ\0"
	"tgk\0"
	"fa\0"
	"Persian\0"
	"\xd9\x81\xd8\xa7\xd8\xb1\xd8\xb3\xdb\x8c\0"
	"FAR\0"
	"fas\0"
	"\xd8\xaa\xd9\x82\xd9\x88\xdb\x8c\xd9\x85 \xd9\x85\xdb\x8c\xd9\x84\xd8\xa7\xd8\xaf\xdb\x8c\0"
	"vi\0"
	"Vietnamese\0"
	"Ti\xe1\xba\xbfng Vi\xe1\xbb\x87t\0"
	"VIT\0"
	"L\xe1\xbb\x8b\x63h Gregory\0"
	"hy\0"
	"Armenian\0"
	"\xd5\xb0\xd5\xa1\xd5\xb5\xd5\xa5\xd6\x80\xd5\xa5\xd5\xb6\0"
	"HYE\0"
	"hye\0"
	"\xd4\xb3\xd6\x80\xd5\xab\xd5\xa3\xd5\xb8\xd6\x80\xd5\xb5\xd5\xa1\xd5\xb6 \xd5\xbf\xd5\xb8\xd5\xb4\xd5\xa1\xd6\x80\0"
	"az\0"
	"Azerbaijani\0"
	"az\xc9\x99rbaycan\0"
	"AZE\0"
	"aze\0"
	"Qreqorian T\xc9\x99qvimi\0"
	"eu\0"
	"Basque\0"
	"euskara\0"
	"EUQ\0"
	"eus\0"
	"Egutegi gregoriarra\0"
	"mk\0"
	"Macedonian\0"
	"\xd0\xbc\xd0\xb0\xd0\xba\xd0\xb5\xd0\xb4\xd0\xbe\xd0\xbd\xd1\x81\xd0\xba\xd0\xb8\0"
	"MKI\0"
	"mkd\0"
	"\xd0\x93\xd1\x80\xd0\xb5\xd0\xb3\xd0\xbe\xd1\x80\xd0\xb8\xd1\x98\xd0\xb0\xd0\xbd\xd1\x81\xd0\xba\xd0\xb8 \xd0\xba\xd0\xb0\xd0\xbb\xd0\xb5\xd0\xbd\xd0\xb4\xd0\xb0\xd1\x80\0"
	"tn\0"
	"Tswana\0"
	"Setswana\0"
	"TSN\0"
	"tsn\0"
	"xh\0"
	"Xhosa\0"
	"isiXhosa\0"
	"XHO\0"
	"xho\0"
	"zu\0"
	"Zulu\0"
	"isiZulu\0"
	"ZUL\0"
	"zul\0"
	"i-Gregorian Calender\0"
	"af\0"
	"Afrikaans\0"
	"AFK\0"
	"afr\0"
	"Gregoriese kalender\0"
	"ka\0"
	"Georgian\0"
	"\xe1\x83\xa5\xe1\x83\x90\xe1\x83\xa0\xe1\x83\x97\xe1\x83\xa3\xe1\x83\x9a\xe1\x83\x98\0"
	"KAT\0"
	"kat\0"
	"\xe1\x83\x92\xe1\x83\xa0\xe1\x83\x98\xe1\x83\x92\xe1\x83\x9d\xe1\x83\xa0\xe1\x83\x98\xe1\x83\x90\xe1\x83\x9c\xe1\x83\xa3\xe1\x83\x9a\xe1\x83\x98 \xe1\x83\x99\xe1\x83\x90\xe1\x83\x9a\xe1\x83\x94\xe1\x83\x9c\xe1\x83\x93\xe1\x83\x90\xe1\x83\xa0\xe1\x83\x98\0"
	"fo\0"
	"Faroese\0"
	"f\xc3\xb8royskt\0"
	"FOS\0"
	"fao\0"
	"hi\0"
	"Hindi\0"
	"\xe0\xa4\xb9\xe0\xa4\xbf\xe0\xa4\x82\xe0\xa4\xa6\xe0\xa5\x80\0"
	"HIN\0"
	"hin\0"
	"\xe0\xa4\x97\xe0\xa5\x8d\xe0\xa4\xb0\xe0\xa5\x87\xe0\xa4\x97\xe0\xa5\x8b\xe0\xa4\xb0\xe0\xa4\xbf\xe0\xa4\xaf\xe0\xa4\xa8 \xe0\xa4\x95\xe0\xa5\x88\xe0\xa4\xb2\xe0\xa5\x87\xe0\xa4\x82\xe0\xa4\xa1\xe0\xa4\xb0\0"
	"mt\0"
	"Maltese\0"
	"Malti\0"
	"MLT\0"
	"mlt\0"
	"Kalendarju Gregorjan\0"
	"se\0"
	"Northern Sami\0"
	"davvis\xc3\xa1megiella\0"
	"SME\0"
	"sme\0"
	"gregoria kaleander\0"
	"ga\0"
	"Irish\0"
	"Gaeilge\0"
	"IRE\0"
	"gle\0"
	"F\xc3\xa9ilire Greag\xc3\xb3rach\0"
	"ms\0"
	"Malay\0"
	"Bahasa Melayu\0"
	"MSL\0"
	"msa\0"
	"Kalendar Gregory\0"
	"kk\0"
	"Kazakh\0"
	"\xd2\x9b\xd0\xb0\xd0\xb7\xd0\xb0\xd2\x9b \xd1\x82\xd1\x96\xd0\xbb\xd1\x96\0"
	"KKZ\0"
	"kaz\0"
	"\xd0\x93\xd1\x80\xd0\xb5\xd0\xb3\xd0\xbe\xd1\x80\xd0\xb8\xd0\xb0\xd0\xbd\xd0\xb4\xd1\x8b\xd2\x9b \xd0\xba\xd2\xaf\xd0\xbd\xd1\x82\xd1\x96\xd0\xb7\xd0\xb1\xd0\xb5\0"
	"ky\0"
	"Kyrgyz\0"
	"\xd0\xba\xd1\x8b\xd1\x80\xd0\xb3\xd1\x8b\xd0\xb7\xd1\x87\xd0\xb0\0"
	"KYR\0"
	"kir\0"
	"\xd0\x93\xd1\x80\xd0\xb8\xd0\xb3\xd0\xbe\xd1\x80\xd0\xb8\xd0\xb0\xd0\xbd \xd0\xba\xd0\xb0\xd0\xbb\xd0\xb5\xd0\xbd\xd0\xb4\xd0\xb0\xd1\x80\xd1\x8b\0"
	"sw\0"
	"Swahili\0"
	"Kiswahili\0"
	"SWK\0"
	"swa\0"
	"Kalenda ya Kigregori\0"
	"uz\0"
	"Uzbek\0"
	"o\xca\xbbzbekcha\0"
	"UZB\0"
	"uzb\0"
	"Grigorian kalendari\0"
	"bn\0"
	"Bengali\0"
	"\xe0\xa6\xac\xe0\xa6\xbe\xe0\xa6\x82\xe0\xa6\xb2\xe0\xa6\xbe\0"
	"BNG\0"
	"bng\0"
	"\xe0\xa6\x97\xe0\xa7\x8d\xe0\xa6\xb0\xe0\xa6\xbf\xe0\xa6\x97\xe0\xa7\x8b\xe0\xa6\xb0\xe0\xa6\xbf\xe0\xa6\xaf\xe0\xa6\xbc\xe0\xa6\xbe\xe0\xa6\xa8 \xe0\xa6\xac\xe0\xa6\xb0\xe0\xa7\x8d\xe0\xa6\xb7\xe0\xa6\xaa\xe0\xa6\x9e\xe0\xa7\x8d\xe0\xa6\x9c\xe0\xa7\x80\0"
	"pa\0"
	"Punjabi\0"
	"\xe0\xa8\xaa\xe0\xa9\xb0\xe0\xa8\x9c\xe0\xa8\xbe\xe0\xa8\xac\xe0\xa9\x80\0"
	"PAN\0"
	"pan\0"
	"\xe0\xa8\x97\xe0\xa8\xb0\xe0\xa9\x80\xe0\xa8\x9c\xe0\xa9\x8b\xe0\xa8\xb0\xe0\xa9\x80\xe0\xa8\x85\xe0\xa8\xa8 \xe0\xa8\x95\xe0\xa9\x88\xe0\xa8\xb2\xe0\xa9\xb0\xe0\xa8\xa1\xe0\xa8\xb0\0"
	"gu\0"
	"Gujarati\0"
	"\xe0\xaa\x97\xe0\xab\x81\xe0\xaa\x9c\xe0\xaa\xb0\xe0\xaa\xbe\xe0\xaa\xa4\xe0\xab\x80\0"
	"GUJ\0"
	"guj\0"
	"\xe0\xaa\x97\xe0\xab\x8d\xe0\xaa\xb0\xe0\xab\x87\xe0\xaa\x97\xe0\xab\x8b\xe0\xaa\xb0\xe0\xaa\xbf\xe0\xaa\xaf\xe0\xaa\xa8 \xe0\xaa\x95\xe0\xab\x87\xe0\xaa\xb2\xe0\xab\x87\xe0\xaa\xa8\xe0\xab\x8d\xe0\xaa\xa1\xe0\xaa\xb0\0"
	"or\0"
	"Oriya\0"
	"\xe0\xac\x93\xe0\xac\xa1\xe0\xac\xbc\xe0\xac\xbf\xe0\xac\x86\0"
	"ORI\0"
	"ori\0"
	"ta\0"
	"Tamil\0"
	"\xe0\xae\xa4\xe0\xae\xae\xe0\xae\xbf\xe0\xae\xb4\xe0\xaf\x8d\0"
	"TAM\0"
	"tam\0"
	"\xe0\xae\x95\xe0\xae\xbf\xe0\xae\xb0\xe0\xae\xbf\xe0\xae\x95\xe0\xaf\x8b\xe0\xae\xb0\xe0\xae\xbf\xe0\xae\xaf\xe0\xae\xa9\xe0\xaf\x8d \xe0\xae\xa8\xe0\xae\xbe\xe0\xae\xb3\xe0\xaf\x8d\xe0\xae\x95\xe0\xae\xbe\xe0\xae\x9f\xe0\xaf\x8d\xe0\xae\x9f\xe0\xae\xbf\0"
	"te\0"
	"Telugu\0"
	"\xe0\xb0\xa4\xe0\xb1\x86\xe0\xb0\xb2\xe0\xb1\x81\xe0\xb0\x97\xe0\xb1\x81\0"
	"TEL\0"
	"tel\0"
	"\xe0\xb0\x97\xe0\xb1\x8d\xe0\xb0\xb0\xe0\xb1\x87\xe0\xb0\x97\xe0\xb1\x8b\xe0\xb0\xb0\xe0\xb0\xbf\xe0\xb0\xaf\xe0\xb0\xa8\xe0\xb1\x8d \xe0\xb0\x95\xe0\xb1\x8d\xe0\xb0\xaf\xe0\xb0\xbe\xe0\xb0\xb2\xe0\xb1\x86\xe0\xb0\x82\xe0\xb0\xa1\xe0\xb0\xb0\xe0\xb1\x8d\0"
	"Kannada\0"
	"\xe0\xb2\x95\xe0\xb2\xa8\xe0\xb3\x8d\xe0\xb2\xa8\xe0\xb2\xa1\0"
	"KDI\0"
	"kan\0"
	"\xe0\xb2\x97\xe0\xb3\x8d\xe0\xb2\xb0\xe0\xb2\xbf\xe0\xb2\x97\xe0\xb3\x8b\xe0\xb2\xb0\xe0\xb2\xbf\xe0\xb2\xaf\xe0\xb2\xa8\xe0\xb3\x8d \xe0\xb2\x95\xe0\xb3\x8d\xe0\xb2\xaf\xe0\xb2\xbe\xe0\xb2\xb2\xe0\xb3\x86\xe0\xb2\x82\xe0\xb2\xa1\xe0\xb2\xb0\xe0\xb3\x8d\0"
	"ml\0"
	"Malayalam\0"
	"\xe0\xb4\xae\xe0\xb4\xb2\xe0\xb4\xaf\xe0\xb4\xbe\xe0\xb4\xb3\xe0\xb4\x82\0"
	"MYM\0"
	"mym\0"
	"\xe0\xb4\x87\xe0\xb4\x82\xe0\xb4\x97\xe0\xb5\x8d\xe0\xb4\xb2\xe0\xb5\x80\xe0\xb4\xb7\xe0\xb5\x8d \xe0\xb4\x95\xe0\xb4\xb2\xe0\xb4\xa3\xe0\xb5\x8d\xe0\xb4\x9f\xe0\xb5\xbc\0"
	"as\0"
	"Assamese\0"
	"\xe0\xa6\x85\xe0\xa6\xb8\xe0\xa6\xae\xe0\xa7\x80\xe0\xa6\xaf\xe0\xa6\xbc\xe0\xa6\xbe\0"
	"ASM\0"
	"asm\0"
	"\xe0\xa6\x97\xe0\xa7\x8d\xe0\xa7\xb0\xe0\xa6\xbf\xe0\xa6\x97\xe0\xa7\x8b\xe0\xa7\xb0\xe0\xa7\x80\xe0\xa6\xaf\xe0\xa6\xbc \xe0\xa6\xaa\xe0\xa6\x9e\xe0\xa7\x8d\xe0\xa6\x9c\xe0\xa6\xbf\xe0\xa6\x95\xe0\xa6\xbe\0"
	"mr\0"
	"Marathi\0"
	"\xe0\xa4\xae\xe0\xa4\xb0\xe0\xa4\xbe\xe0\xa4\xa0\xe0\xa5\x80\0"
	"MAR\0"
	"\xe0\xa4\x97\xe0\xa5\x8d\xe0\xa4\xb0\xe0\xa5\x87\xe0\xa4\x97\xe0\xa5\x8b\xe0\xa4\xb0\xe0\xa4\xbf\xe0\xa4\xaf\xe0\xa4\xa8 \xe0\xa4\xa6\xe0\xa4\xbf\xe0\xa4\xa8\xe0\xa4\xa6\xe0\xa4\xb0\xe0\xa5\x8d\xe0\xa4\xb6\xe0\xa4\xbf\xe0\xa4\x95\xe0\xa4\xbe\0"
	"mn\0"
	"Mongolian\0"
	"\xd0\xbc\xd0\xbe\xd0\xbd\xd0\xb3\xd0\xbe\xd0\xbb\0"
	"MON\0"
	"mon\0"
	"\xd0\xb3\xd1\x80\xd0\xb5\xd0\xb3\xd0\xbe\xd1\x80\xd0\xb8\xd0\xb9\xd0\xbd \xd1\x85\xd1\x83\xd0\xb0\xd0\xbd\xd0\xbb\xd0\xb8\0"
	"bo\0"
	"Tibetan\0"
	"\xe0\xbd\x94\xe0\xbd\xbc\xe0\xbd\x91\xe0\xbc\x8b\xe0\xbd\xa6\xe0\xbe\x90\xe0\xbd\x91\xe0\xbc\x8b\0"
	"BOB\0"
	"bod\0"
	"cy\0"
	"Welsh\0"
	"Cymraeg\0"
	"CYM\0"
	"cym\0"
	"Calendr Gregori\0"
	"km\0"
	"Khmer\0"
	"\xe1\x9e\x81\xe1\x9f\x92\xe1\x9e\x98\xe1\x9f\x82\xe1\x9e\x9a\0"
	"KHM\0"
	"khm\0"
	"\xe1\x9e\x94\xe1\x9f\x92\xe1\x9e\x9a\xe1\x9e\x8f\xe1\x9e\xb7\xe1\x9e\x91\xe1\x9e\xb7\xe1\x9e\x93\xe2\x80\x8b\xe1\x9e\xa0\xe1\x9f\x92\xe1\x9e\x9f\xe1\x9e\x80\xe1\x9e\xa0\xe1\x9f\x92\xe1\x9e\x9f\xe1\x9f\x8a\xe1\x9e\xb8\0"
	"lo\0"
	"Lao\0"
	"\xe0\xba\xa5\xe0\xba\xb2\xe0\xba\xa7\0"
	"LAO\0"
	"lao\0"
	"\xe0\xba\x9b\xe0\xba\xb0\xe0\xba\x95\xe0\xba\xb4\xe0\xba\x97\xe0\xba\xb4\xe0\xba\x99\xe0\xbb\x80\xe0\xba\x81\xe0\xba\xa3\xe0\xbb\x82\xe0\xba\x81\xe0\xba\xa3\xe0\xba\xbd\xe0\xba\x99\0"
	"gl\0"
	"Galician\0"
	"galego\0"
	"GLC\0"
	"glg\0"
	"kok\0"
	"Konkani\0"
	"\xe0\xa4\x95\xe0\xa5\x8b\xe0\xa4\x82\xe0\xa4\x95\xe0\xa4\xa3\xe0\xa5\x80\0"
	"KNK\0"
	"si\0"
	"Sinhala\0"
	"\xe0\xb7\x83\xe0\xb7\x92\xe0\xb6\x82\xe0\xb7\x84\xe0\xb6\xbd\0"
	"SIN\0"
	"sin\0"
	"\xe0\xb6\x9c\xe0\xb7\x8a\xe2\x80\x8d\xe0\xb6\xbb\xe0\xb7\x99\xe0\xb6\x9c\xe0\xb6\xbb\xe0\xb7\x92\xe0\xb6\xba\xe0\xb7\x8f\xe0\xb6\xb1\xe0\xb7\x94 \xe0\xb6\xaf\xe0\xb7\x92\xe0\xb6\xb1\xe0\xb6\xaf\xe0\xb7\x83\xe0\xb7\x94\xe0\xb6\xb1\0"
	"Amharic\0"
	"\xe1\x8a\xa0\xe1\x88\x9b\xe1\x88\xad\xe1\x8a\x9b\0"
	"AMH\0"
	"amh\0"
	"\xe1\x8b\xa8\xe1\x8c\x8d\xe1\x88\xaa\xe1\x8c\x8e\xe1\x88\xaa\xe1\x8b\xab\xe1\x8a\x95 \xe1\x89\x80\xe1\x8a\x95 \xe1\x8a\xa0\xe1\x89\x86\xe1\x8c\xa3\xe1\x8c\xa0\xe1\x88\xad\0"
	"tzm\0"
	"Central Atlas Tamazight\0"
	"Tamazi\xc9\xa3t\0"
	"TZM\0"
	"Nepali\0"
	"\xe0\xa4\xa8\xe0\xa5\x87\xe0\xa4\xaa\xe0\xa4\xbe\xe0\xa4\xb2\xe0\xa5\x80\0"
	"NEP\0"
	"nep\0"
	"\xe0\xa4\x97\xe0\xa5\x8d\xe0\xa4\xb0\xe0\xa5\x87\xe0\xa4\x97\xe0\xa5\x8b\xe0\xa4\xb0\xe0\xa4\xbf\xe0\xa4\xaf\xe0\xa4\xa8 \xe0\xa4\xaa\xe0\xa4\xbe\xe0\xa4\xa4\xe0\xa5\x8d\xe0\xa4\xb0\xe0\xa5\x8b\0"
	"ps\0"
	"Pashto\0"
	"\xd9\xbe\xda\x9a\xd8\xaa\xd9\x88\0"
	"PAS\0"
	"pus\0"
	"fil\0"
	"Filipino\0"
	"FPO\0"
	"Kalendaryong Gregorian\0"
	"ha\0"
	"Hausa\0"
	"HAU\0"
	"hau\0"
	"yo\0"
	"Yoruba\0"
	"\xc3\x88\x64\xc3\xa8 Yor\xc3\xb9\x62\xc3\xa1\0"
	"YOR\0"
	"yor\0"
	"nso\0"
	"Northern Sotho\0"
	"Sesotho sa Leboa\0"
	"NSO\0"
	"kl\0"
	"Kalaallisut\0"
	"kalaallisut\0"
	"KAL\0"
	"kal\0"
	"gregorianskit ullorsiutaat\0"
	"ig\0"
	"Igbo\0"
	"IBO\0"
	"ibo\0"
	"ii\0"
	"Sichuan Yi\0"
	"\xea\x86\x88\xea\x8c\xa0\xea\x89\x99\0"
	"III\0"
	"iii\0"
	"\xea\x84\x89\xea\x89\xbb\xea\x83\x85\xea\x91\x8d\0"
	"br\0"
	"Breton\0"
	"brezhoneg\0"
	"BRE\0"
	"bre\0"
	"deiziadur gregorian\0"
	"gsw\0"
	"Swiss German\0"
	"Schwiizert\xc3\xbc\xc3\xbctsch\0"
	"GSW\0"
	"Gregoriaanisch Kal\xc3\xa4nder\0"
	"sah\0"
	"Sakha\0"
	"\xd1\x81\xd0\xb0\xd1\x85\xd0\xb0 \xd1\x82\xd1\x8b\xd0\xbb\xd0\xb0\0"
	"SAH\0"
	"rw\0"
	"Kinyarwanda\0"
	"KIN\0"
	"kin\0"
	"gd\0"
	"Scottish Gaelic\0"
	"G\xc3\xa0idhlig\0"
	"GLA\0"
	"gla\0"
	"Am M\xc3\xacosachan Griogarach\0"
	"ar-SA\0"
	"Arabic (Saudi Arabia)\0"
	"\xd8\xa7\xd9\x84\xd8\xb9\xd8\xb1\xd8\xa8\xd9\x8a\xd8\xa9 (\xd8\xa7\xd9\x84\xd9\x85\xd9\x85\xd9\x84\xd9\x83\xd8\xa9 \xd8\xa7\xd9\x84\xd8\xb9\xd8\xb1\xd8\xa8\xd9\x8a\xd8\xa9 \xd8\xa7\xd9\x84\xd8\xb3\xd8\xb9\xd9\x88\xd8\xaf\xd9\x8a\xd8\xa9)\0"
	"bg-BG\0"
	"Bulgarian (Bulgaria)\0"
	"\xd0\xb1\xd1\x8a\xd0\xbb\xd0\xb3\xd0\xb0\xd1\x80\xd1\x81\xd0\xba\xd0\xb8 (\xd0\x91\xd1\x8a\xd0\xbb\xd0\xb3\xd0\xb0\xd1\x80\xd0\xb8\xd1\x8f)\0"
	"BG\0"
	"ca-ES\0"
	"Catalan (Spain)\0"
	"catal\xc3\xa0 (Espanya)\0"
	"ES\0"
	"zh-TW\0"
	"Chinese (Traditional)\0"
	"\xe4\xb8\xad\xe6\x96\x87 (\xe5\x8f\xb0\xe6\xb9\xbe)\0"
	"CHT\0"
	"TW\0"
	"\xe5\x85\xac\xe6\x9b\x86\0"
	"cs-CZ\0"
	"Czech (Czech Republic)\0"
	"\xc4\x8d\x65\xc5\xa1tina (\xc4\x8c\x65sk\xc3\xa1 republika)\0"
	"CZ\0"
	"da-DK\0"
	"Danish (Denmark)\0"
	"dansk (Danmark)\0"
	"DK\0"
	"de-DE\0"
	"German (Germany)\0"
	"Deutsch (Deutschland)\0"
	"DE\0"
	"el-GR\0"
	"Greek (Greece)\0"
	"\xce\x95\xce\xbb\xce\xbb\xce\xb7\xce\xbd\xce\xb9\xce\xba\xce\xac (\xce\x95\xce\xbb\xce\xbb\xce\xac\xce\xb4\xce\xb1)\0"
	"GR\0"
	"en-US\0"
	"English (United States)\0"
	"US\0"
	"fi-FI\0"
	"Finnish (Finland)\0"
	"suomi (Suomi)\0"
	"FI\0"
	"fr-FR\0"
	"French (France)\0"
	"fran\xc3\xa7\x61is (France)\0"
	"FR\0"
	"he-IL\0"
	"Hebrew (Israel)\0"
	"\xd7\xa2\xd7\x91\xd7\xa8\xd7\x99\xd7\xaa (\xd7\x99\xd7\xa9\xd7\xa8\xd7\x90\xd7\x9c)\0"
	"IL\0"
	"hu-HU\0"
	"Hungarian (Hungary)\0"
	"magyar (Magyarorsz\xc3\xa1g)\0"
	"HU\0"
	"is-IS\0"
	"Icelandic (Iceland)\0"
	"\xc3\xadslenska (\xc3\x8dsland)\0"
	"IS\0"
	"it-IT\0"
	"Italian (Italy)\0"
	"italiano (Italia)\0"
	"IT\0"
	"ja-JP\0"
	"Japanese (Japan)\0"
	"\xe6\x97\xa5\xe6\x9c\xac\xe8\xaa\x9e (\xe6\x97\xa5\xe6\x9c\xac)\0"
	"JP\0"
	"ko-KR\0"
	"Korean (South Korea)\0"
	"\xed\x95\x9c\xea\xb5\xad\xec\x96\xb4 (\xeb\x8c\x80\xed\x95\x9c\xeb\xaf\xbc\xea\xb5\xad)\0"
	"KR\0"
	"nl-NL\0"
	"Dutch (Netherlands)\0"
	"Nederlands (Nederland)\0"
	"NL\0"
	"nb-NO\0"
	"Norwegian Bokm\xc3\xa5l (Norway)\0"
	"norsk bokm\xc3\xa5l (Norge)\0"
	"NO\0"
	"pl-PL\0"
	"Polish (Poland)\0"
	"polski (Polska)\0"
	"PL\0"
	"pt-BR\0"
	"Portuguese (Brazil)\0"
	"portugu\xc3\xaas (Brasil)\0"
	"BR\0"
	"rm-CH\0"
	"Romansh (Switzerland)\0"
	"rumantsch (Svizra)\0"
	"ro-RO\0"
	"Romanian (Romania)\0"
	"rom\xc3\xa2n\xc4\x83 (Rom\xc3\xa2nia)\0"
	"RO\0"
	"ru-RU\0"
	"Russian (Russia)\0"
	"\xd1\x80\xd1\x83\xd1\x81\xd1\x81\xd0\xba\xd0\xb8\xd0\xb9 (\xd0\xa0\xd0\xbe\xd1\x81\xd1\x81\xd0\xb8\xd1\x8f)\0"
	"RU\0"
	"hr-HR\0"
	"Croatian (Croatia)\0"
	"hrvatski (Hrvatska)\0"
	"HR\0"
	"sk-SK\0"
	"Slovak (Slovakia)\0"
	"sloven\xc4\x8dina (Slovensko)\0"
	"SK\0"
	"sq-AL\0"
	"Albanian (Albania)\0"
	"Shqip (Shqip\xc3\xabri)\0"
	"AL\0"
	"sv-SE\0"
	"Swedish (Sweden)\0"
	"svenska (Sverige)\0"
	"SE\0"
	"th-TH\0"
	"Thai (Thailand)\0"
	"\xe0\xb9\x84\xe0\xb8\x97\xe0\xb8\xa2 (\xe0\xb9\x84\xe0\xb8\x97\xe0\xb8\xa2)\0"
	"TH\0"
	"tr-TR\0"
	"Turkish (Turkey)\0"
	"T\xc3\xbcrk\xc3\xa7\x65 (T\xc3\xbcrkiye)\0"
	"TR\0"
	"ur-PK\0"
	"Urdu (Pakistan)\0"
	"\xd8\xa7\xd8\xb1\xd8\xaf\xd9\x88 (\xd9\xbe\xd8\xa7\xda\xa9\xd8\xb3\xd8\xaa\xd8\xa7\xd9\x86)\0"
	"PK\0"
	"id-ID\0"
	"Indonesian (Indonesia)\0"
	"Bahasa Indonesia (Indonesia)\0"
	"ID\0"
	"uk-UA\0"
	"Ukrainian (Ukraine)\0"
	"\xd1\x83\xd0\xba\xd1\x80\xd0\xb0\xd1\x97\xd0\xbd\xd1\x81\xd1\x8c\xd0\xba\xd0\xb0 (\xd0\xa3\xd0\xba\xd1\x80\xd0\xb0\xd1\x97\xd0\xbd\xd0\xb0)\0"
	"UA\0"
	"be-BY\0"
	"Belarusian (Belarus)\0"
	"\xd0\xb1\xd0\xb5\xd0\xbb\xd0\xb0\xd1\x80\xd1\x83\xd1\x81\xd0\xba\xd0\xb0\xd1\x8f (\xd0\x91\xd0\xb5\xd0\xbb\xd0\xb0\xd1\x80\xd1\x83\xd1\x81\xd1\x8c)\0"
	"BY\0"
	"sl-SI\0"
	"Slovenian (Slovenia)\0"
	"sloven\xc5\xa1\xc4\x8dina (Slovenija)\0"
	"SI\0"
	"et-EE\0"
	"Estonian (Estonia)\0"
	"eesti (Eesti)\0"
	"EE\0"
	"lv-LV\0"
	"Latvian (Latvia)\0"
	"latvie\xc5\xa1u (Latvija)\0"
	"LV\0"
	"lt-LT\0"
	"Lithuanian (Lithuania)\0"
	"lietuvi\xc5\xb3 (Lietuva)\0"
	"LT\0"
	"tg-Cyrl-TJ\0"
	"Tajik (Cyrillic, Tajikistan)\0"
	"\xd0\xa2\xd0\xbe\xd2\xb7\xd0\xb8\xd0\xba\xd3\xa3 (\xd0\xa2\xd0\xbe\xd2\xb7\xd0\xb8\xd0\xba\xd0\xb8\xd1\x81\xd1\x82\xd0\xbe\xd0\xbd)\0"
	"TJ\0"
	"fa-IR\0"
	"Persian (Iran)\0"
	"\xd9\x81\xd8\xa7\xd8\xb1\xd8\xb3\xdb\x8c (\xd8\xa7\xdb\x8c\xd8\xb1\xd8\xa7\xd9\x86)\0"
	"IR\0"
	"vi-VN\0"
	"Vietnamese (Vietnam)\0"
	"Ti\xe1\xba\xbfng Vi\xe1\xbb\x87t (Vi\xe1\xbb\x87t Nam)\0"
	"VN\0"
	"hy-AM\0"
	"Armenian (Armenia)\0"
	"\xd5\xb0\xd5\xa1\xd5\xb5\xd5\xa5\xd6\x80\xd5\xa5\xd5\xb6 (\xd5\x80\xd5\xa1\xd5\xb5\xd5\xa1\xd5\xbd\xd5\xbf\xd5\xa1\xd5\xb6)\0"
	"az-Latn-AZ\0"
	"Azerbaijani (Latin, Azerbaijan)\0"
	"az\xc9\x99rbaycan (Az\xc9\x99rbaycan)\0"
	"AZ\0"
	"eu-ES\0"
	"Basque (Spain)\0"
	"euskara (Espainia)\0"
	"mk-MK\0"
	"Macedonian (Macedonia)\0"
	"\xd0\xbc\xd0\xb0\xd0\xba\xd0\xb5\xd0\xb4\xd0\xbe\xd0\xbd\xd1\x81\xd0\xba\xd0\xb8 (\xd0\x9c\xd0\xb0\xd0\xba\xd0\xb5\xd0\xb4\xd0\xbe\xd0\xbd\xd0\xb8\xd1\x98\xd0\xb0)\0"
	"MK\0"
	"tn-ZA\0"
	"Tswana (South Africa)\0"
	"ZA\0"
	"xh-ZA\0"
	"Xhosa (South Africa)\0"
	"zu-ZA\0"
	"Zulu (South Africa)\0"
	"isiZulu (i-South Africa)\0"
	"af-ZA\0"
	"Afrikaans (South Africa)\0"
	"Afrikaans (Suid-Afrika)\0"
	"ka-GE\0"
	"Georgian (Georgia)\0"
	"\xe1\x83\xa5\xe1\x83\x90\xe1\x83\xa0\xe1\x83\x97\xe1\x83\xa3\xe1\x83\x9a\xe1\x83\x98 (\xe1\x83\xa1\xe1\x83\x90\xe1\x83\xa5\xe1\x83\x90\xe1\x83\xa0\xe1\x83\x97\xe1\x83\x95\xe1\x83\x94\xe1\x83\x9a\xe1\x83\x9d)\0"
	"GE\0"
	"fo-FO\0"
	"Faroese (Faroe Islands)\0"
	"f\xc3\xb8royskt (F\xc3\xb8royar)\0"
	"FO\0"
	"hi-IN\0"
	"Hindi (India)\0"
	"\xe0\xa4\xb9\xe0\xa4\xbf\xe0\xa4\x82\xe0\xa4\xa6\xe0\xa5\x80 (\xe0\xa4\xad\xe0\xa4\xbe\xe0\xa4\xb0\xe0\xa4\xa4)\0"
	"IN\0"
	"mt-MT\0"
	"Maltese (Malta)\0"
	"Malti (Malta)\0"
	"MT\0"
	"se-NO\0"
	"Northern Sami (Norway)\0"
	"davvis\xc3\xa1megiella (Norga)\0"
	"sw-KE\0"
	"Swahili (Kenya)\0"
	"Kiswahili (Kenya)\0"
	"KE\0"
	"uz-Latn-UZ\0"
	"Uzbek (Latin, Uzbekistan)\0"
	"o\xca\xbbzbekcha (O\xca\xbbzbekiston)\0"
	"UZ\0"
	"bn-IN\0"
	"Bengali (India)\0"
	"\xe0\xa6\xac\xe0\xa6\xbe\xe0\xa6\x82\xe0\xa6\xb2\xe0\xa6\xbe (\xe0\xa6\xad\xe0\xa6\xbe\xe0\xa6\xb0\xe0\xa6\xa4)\0"
	"gu-IN\0"
	"Gujarati (India)\0"
	"\xe0\xaa\x97\xe0\xab\x81\xe0\xaa\x9c\xe0\xaa\xb0\xe0\xaa\xbe\xe0\xaa\xa4\xe0\xab\x80 (\xe0\xaa\xad\xe0\xaa\xbe\xe0\xaa\xb0\xe0\xaa\xa4)\0"
	"or-IN\0"
	"Oriya (India)\0"
	"\xe0\xac\x93\xe0\xac\xa1\xe0\xac\xbc\xe0\xac\xbf\xe0\xac\x86 (\xe0\xac\xad\xe0\xac\xbe\xe0\xac\xb0\xe0\xac\xa4)\0"
	"ta-IN\0"
	"Tamil (India)\0"
	"\xe0\xae\xa4\xe0\xae\xae\xe0\xae\xbf\xe0\xae\xb4\xe0\xaf\x8d (\xe0\xae\x87\xe0\xae\xa8\xe0\xaf\x8d\xe0\xae\xa4\xe0\xae\xbf\xe0\xae\xaf\xe0\xae\xbe)\0"
	"te-IN\0"
	"Telugu (India)\0"
	"\xe0\xb0\xa4\xe0\xb1\x86\xe0\xb0\xb2\xe0\xb1\x81\xe0\xb0\x97\xe0\xb1\x81 (\xe0\xb0\xad\xe0\xb0\xbe\xe0\xb0\xb0\xe0\xb0\xa4 \xe0\xb0\xa6\xe0\xb1\x87\xe0\xb0\xb6\xe0\xb0\x82)\0"
	"kn-IN\0"
	"Kannada (India)\0"
	"\xe0\xb2\x95\xe0\xb2\xa8\xe0\xb3\x8d\xe0\xb2\xa8\xe0\xb2\xa1 (\xe0\xb2\xad\xe0\xb2\xbe\xe0\xb2\xb0\xe0\xb2\xa4)\0"
	"ml-IN\0"
	"Malayalam (India)\0"
	"\xe0\xb4\xae\xe0\xb4\xb2\xe0\xb4\xaf\xe0\xb4\xbe\xe0\xb4\xb3\xe0\xb4\x82 (\xe0\xb4\x87\xe0\xb4\xa8\xe0\xb5\x8d\xe0\xb4\xa4\xe0\xb5\x8d\xe0\xb4\xaf)\0"
	"as-IN\0"
	"Assamese (India)\0"
	"\xe0\xa6\x85\xe0\xa6\xb8\xe0\xa6\xae\xe0\xa7\x80\xe0\xa6\xaf\xe0\xa6\xbc\xe0\xa6\xbe (\xe0\xa6\xad\xe0\xa6\xbe\xe0\xa7\xb0\xe0\xa6\xa4)\0"
	"mr-IN\0"
	"Marathi (India)\0"
	"\xe0\xa4\xae\xe0\xa4\xb0\xe0\xa4\xbe\xe0\xa4\xa0\xe0\xa5\x80 (\xe0\xa4\xad\xe0\xa4\xbe\xe0\xa4\xb0\xe0\xa4\xa4)\0"
	"bo-CN\0"
	"Tibetan (China)\0"
	"\xe0\xbd\x94\xe0\xbd\xbc\xe0\xbd\x91\xe0\xbc\x8b\xe0\xbd\xa6\xe0\xbe\x90\xe0\xbd\x91\xe0\xbc\x8b (\xe0\xbd\xa2\xe0\xbe\x92\xe0\xbe\xb1\xe0\xbc\x8b\xe0\xbd\x93\xe0\xbd\x82)\0"
	"cy-GB\0"
	"Welsh (United Kingdom)\0"
	"Cymraeg (Y Deyrnas Unedig)\0"
	"GB\0"
	"km-KH\0"
	"Khmer (Cambodia)\0"
	"\xe1\x9e\x81\xe1\x9f\x92\xe1\x9e\x98\xe1\x9f\x82\xe1\x9e\x9a (\xe1\x9e\x80\xe1\x9e\x98\xe1\x9f\x92\xe1\x9e\x96\xe1\x9e\xbb\xe1\x9e\x87\xe1\x9e\xb6)\0"
	"KH\0"
	"lo-LA\0"
	"Lao (Laos)\0"
	"\xe0\xba\xa5\xe0\xba\xb2\xe0\xba\xa7 (\xe0\xba\xa5\xe0\xba\xb2\xe0\xba\xa7)\0"
	"LA\0"
	"gl-ES\0"
	"Galician (Spain)\0"
	"galego (Espa\xc3\xb1\x61)\0"
	"kok-IN\0"
	"Konkani (India)\0"
	"\xe0\xa4\x95\xe0\xa5\x8b\xe0\xa4\x82\xe0\xa4\x95\xe0\xa4\xa3\xe0\xa5\x80 (\xe0\xa4\xad\xe0\xa4\xbe\xe0\xa4\xb0\xe0\xa4\xa4)\0"
	"si-LK\0"
	"Sinhala (Sri Lanka)\0"
	"\xe0\xb7\x83\xe0\xb7\x92\xe0\xb6\x82\xe0\xb7\x84\xe0\xb6\xbd (\xe0\xb7\x81\xe0\xb7\x8a\xe2\x80\x8d\xe0\xb6\xbb\xe0\xb7\x93 \xe0\xb6\xbd\xe0\xb6\x82\xe0\xb6\x9a\xe0\xb7\x8f\xe0\xb7\x80)\0"
	"LK\0"
	"am-ET\0"
	"Amharic (Ethiopia)\0"
	"\xe1\x8a\xa0\xe1\x88\x9b\xe1\x88\xad\xe1\x8a\x9b (\xe1\x8a\xa2\xe1\x89\xb5\xe1\x8b\xae\xe1\x8c\xb5\xe1\x8b\xab)\0"
	"ET\0"
	"ne-NP\0"
	"Nepali (Nepal)\0"
	"\xe0\xa4\xa8\xe0\xa5\x87\xe0\xa4\xaa\xe0\xa4\xbe\xe0\xa4\xb2\xe0\xa5\x80 (\xe0\xa4\xa8\xe0\xa5\x87\xe0\xa4\xaa\xe0\xa4\xbe\xe0\xa4\xb2)\0"
	"NP\0"
	"ps-AF\0"
	"Pashto (Afghanistan)\0"
	"\xd9\xbe\xda\x9a\xd8\xaa\xd9\x88 (\xd8\xa7\xd9\x81\xd8\xba\xd8\xa7\xd9\x86\xd8\xb3\xd8\xaa\xd8\xa7\xd9\x86)\0"
	"AF\0"
	"fil-PH\0"
	"Filipino (Philippines)\0"
	"Filipino (Pilipinas)\0"
	"PH\0"
	"ha-Latn-NG\0"
	"Hausa (Latin, Nigeria)\0"
	"Hausa (Najeriya)\0"
	"NG\0"
	"yo-NG\0"
	"Yoruba (Nigeria)\0"
	"\xc3\x88\x64\xc3\xa8 Yor\xc3\xb9\x62\xc3\xa1 (Or\xc3\xadl\xe1\xba\xb9\xcc\x81\xc3\xa8\x64\x65 N\xc3\xa0\xc3\xacj\xc3\xadr\xc3\xad\xc3\xa0)\0"
	"nso-ZA\0"
	"Northern Sotho (South Africa)\0"
	"kl-GL\0"
	"Kalaallisut (Greenland)\0"
	"kalaallisut (Kalaallit Nunaat)\0"
	"GL\0"
	"ig-NG\0"
	"Igbo (Nigeria)\0"
	"ii-CN\0"
	"Sichuan Yi (China)\0"
	"\xea\x86\x88\xea\x8c\xa0\xea\x89\x99 (\xea\x8d\x8f\xea\x87\xa9)\0"
	"br-FR\0"
	"Breton (France)\0"
	"brezhoneg (Fra\xc3\xb1s)\0"
	"sah-RU\0"
	"Sakha (Russia)\0"
	"rw-RW\0"
	"Kinyarwanda (Rwanda)\0"
	"RW\0"
	"gd-GB\0"
	"Scottish Gaelic (United Kingdom)\0"
	"G\xc3\xa0idhlig (An R\xc3\xacoghachd Aonaichte)\0"
	"ar-IQ\0"
	"Arabic (Iraq)\0"
	"\xd8\xa7\xd9\x84\xd8\xb9\xd8\xb1\xd8\xa8\xd9\x8a\xd8\xa9 (\xd8\xa7\xd9\x84\xd8\xb9\xd8\xb1\xd8\xa7\xd9\x82)\0"
	"ARI\0"
	"IQ\0"
	"\xd8\xa7\xd9\x84\xd8\xaa\xd9\x82\xd9\x88\xd9\x8a\xd9\x85 \xd8\xa7\xd9\x84\xd9\x85\xd9\x8a\xd9\x84\xd8\xa7\xd8\xaf\xd9\x8a\0"
	"zh-CN\0"
	"\xe4\xb8\xad\xe6\x96\x87 (\xe4\xb8\xad\xe5\x9b\xbd)\0"
	"de-CH\0"
	"German (Switzerland)\0"
	"Deutsch (Schweiz)\0"
	"DES\0"
	"en-GB\0"
	"English (United Kingdom)\0"
	"ENG\0"
	"es-MX\0"
	"Spanish (Mexico)\0"
	"espa\xc3\xb1ol (M\xc3\xa9xico)\0"
	"ESM\0"
	"MX\0"
	"fr-BE\0"
	"French (Belgium)\0"
	"fran\xc3\xa7\x61is (Belgique)\0"
	"FRB\0"
	"BE\0"
	"it-CH\0"
	"Italian (Switzerland)\0"
	"italiano (Svizzera)\0"
	"ITS\0"
	"nl-BE\0"
	"Dutch (Belgium)\0"
	"Nederlands (Belgi\xc3\xab)\0"
	"NLB\0"
	"nn-NO\0"
	"Norwegian Nynorsk (Norway)\0"
	"nynorsk (Noreg)\0"
	"NON\0"
	"nno\0"
	"nn\0"
	"pt-PT\0"
	"Portuguese (Portugal)\0"
	"portugu\xc3\xaas (Portugal)\0"
	"PTG\0"
	"PT\0"
	"sv-FI\0"
	"Swedish (Finland)\0"
	"svenska (Finland)\0"
	"SVF\0"
	"az-Cyrl-AZ\0"
	"Azerbaijani (Cyrillic, Azerbaijan)\0"
	"AZC\0"
	"ga-IE\0"
	"Irish (Ireland)\0"
	"Gaeilge (\xc3\x89ire)\0"
	"IE\0"
	"uz-Cyrl-UZ\0"
	"Uzbek (Cyrillic, Uzbekistan)\0"
	"\xd0\x93\xd1\x80\xd0\xb8\xd0\xb3\xd0\xbe\xd1\x80\xd0\xb8\xd0\xb0\xd0\xbd \xd0\xba\xd0\xb0\xd0\xbb\xd0\xb5\xd0\xbd\xd0\xb4\xd0\xb0\xd1\x80\xd0\xb8\0"
	"bn-BD\0"
	"Bengali (Bangladesh)\0"
	"\xe0\xa6\xac\xe0\xa6\xbe\xe0\xa6\x82\xe0\xa6\xb2\xe0\xa6\xbe (\xe0\xa6\xac\xe0\xa6\xbe\xe0\xa6\x82\xe0\xa6\xb2\xe0\xa6\xbe\xe0\xa6\xa6\xe0\xa7\x87\xe0\xa6\xb6)\0"
	"BNB\0"
	"BD\0"
	"ar-EG\0"
	"Arabic (Egypt)\0"
	"\xd8\xa7\xd9\x84\xd8\xb9\xd8\xb1\xd8\xa8\xd9\x8a\xd8\xa9 (\xd9\x85\xd8\xb5\xd8\xb1)\0"
	"ARE\0"
	"EG\0"
	"zh-HK\0"
	"Chinese (Traditional, Hong Kong SAR China)\0"
	"\xe4\xb8\xad\xe6\x96\x87 (\xe4\xb8\xad\xe5\x9b\xbd\xe9\xa6\x99\xe6\xb8\xaf\xe7\x89\xb9\xe5\x88\xab\xe8\xa1\x8c\xe6\x94\xbf\xe5\x8c\xba)\0"
	"ZHH\0"
	"HK\0"
	"de-AT\0"
	"German (Austria)\0"
	"Deutsch (\xc3\x96sterreich)\0"
	"DEA\0"
	"AT\0"
	"en-AU\0"
	"English (Australia)\0"
	"ENA\0"
	"AU\0"
	"es-ES\0"
	"Spanish (Spain)\0"
	"espa\xc3\xb1ol (Espa\xc3\xb1\x61)\0"
	"ESN\0"
	"fr-CA\0"
	"French (Canada)\0"
	"fran\xc3\xa7\x61is (Canada)\0"
	"FRC\0"
	"CA\0"
	"Calendrier gr\xc3\xa9gorien\0"
	"se-FI\0"
	"Northern Sami (Finland)\0"
	"davvis\xc3\xa1megiella (Suopma)\0"
	"SMG\0"
	"smg\0"
	"ar-LY\0"
	"Arabic (Libya)\0"
	"\xd8\xa7\xd9\x84\xd8\xb9\xd8\xb1\xd8\xa8\xd9\x8a\xd8\xa9 (\xd9\x84\xd9\x8a\xd8\xa8\xd9\x8a\xd8\xa7)\0"
	"ARL\0"
	"LY\0"
	"zh-SG\0"
	"Chinese (Simplified, Singapore)\0"
	"\xe4\xb8\xad\xe6\x96\x87 (\xe6\x96\xb0\xe5\x8a\xa0\xe5\x9d\xa1)\0"
	"ZHI\0"
	"SG\0"
	"de-LU\0"
	"German (Luxembourg)\0"
	"Deutsch (Luxemburg)\0"
	"DEL\0"
	"LU\0"
	"en-CA\0"
	"English (Canada)\0"
	"ENC\0"
	"es-GT\0"
	"Spanish (Guatemala)\0"
	"espa\xc3\xb1ol (Guatemala)\0"
	"ESG\0"
	"GT\0"
	"fr-CH\0"
	"French (Switzerland)\0"
	"fran\xc3\xa7\x61is (Suisse)\0"
	"FRS\0"
	"hr-BA\0"
	"Croatian (Bosnia and Herzegovina)\0"
	"hrvatski (Bosna i Hercegovina)\0"
	"HRB\0"
	"hrb\0"
	"BA\0"
	"ar-DZ\0"
	"Arabic (Algeria)\0"
	"\xd8\xa7\xd9\x84\xd8\xb9\xd8\xb1\xd8\xa8\xd9\x8a\xd8\xa9 (\xd8\xa7\xd9\x84\xd8\xac\xd8\xb2\xd8\xa7\xd8\xa6\xd8\xb1)\0"
	"ARG\0"
	"DZ\0"
	"zh-MO\0"
	"Chinese (Traditional, Macau SAR China)\0"
	"\xe4\xb8\xad\xe6\x96\x87 (\xe4\xb8\xad\xe5\x9b\xbd\xe6\xbe\xb3\xe9\x97\xa8\xe7\x89\xb9\xe5\x88\xab\xe8\xa1\x8c\xe6\x94\xbf\xe5\x8c\xba)\0"
	"ZHM\0"
	"MO\0"
	"de-LI\0"
	"German (Liechtenstein)\0"
	"Deutsch (Liechtenstein)\0"
	"DEC\0"
	"LI\0"
	"en-NZ\0"
	"English (New Zealand)\0"
	"ENZ\0"
	"NZ\0"
	"es-CR\0"
	"Spanish (Costa Rica)\0"
	"espa\xc3\xb1ol (Costa Rica)\0"
	"ESC\0"
	"CR\0"
	"fr-LU\0"
	"French (Luxembourg)\0"
	"fran\xc3\xa7\x61is (Luxembourg)\0"
	"FRL\0"
	"bs-Latn-BA\0"
	"Bosnian (Latin, Bosnia and Herzegovina)\0"
	"bosanski (Bosna i Hercegovina)\0"
	"BSB\0"
	"bsb\0"
	"bs\0"
	"Gregorijanski kalendar\0"
	"ar-MA\0"
	"Arabic (Morocco)\0"
	"\xd8\xa7\xd9\x84\xd8\xb9\xd8\xb1\xd8\xa8\xd9\x8a\xd8\xa9 (\xd8\xa7\xd9\x84\xd9\x85\xd8\xba\xd8\xb1\xd8\xa8)\0"
	"ARM\0"
	"MA\0"
	"en-IE\0"
	"English (Ireland)\0"
	"ENI\0"
	"es-PA\0"
	"Spanish (Panama)\0"
	"espa\xc3\xb1ol (Panam\xc3\xa1)\0"
	"ESA\0"
	"PA\0"
	"fr-MC\0"
	"French (Monaco)\0"
	"fran\xc3\xa7\x61is (Monaco)\0"
	"FRM\0"
	"MC\0"
	"sr-Latn-BA\0"
	"Serbian (Latin, Bosnia and Herzegovina)\0"
	"\xd0\xa1\xd1\x80\xd0\xbf\xd1\x81\xd0\xba\xd0\xb8 (\xd0\x91\xd0\xbe\xd1\x81\xd0\xbd\xd0\xb0 \xd0\xb8 \xd0\xa5\xd0\xb5\xd1\x80\xd1\x86\xd0\xb5\xd0\xb3\xd0\xbe\xd0\xb2\xd0\xb8\xd0\xbd\xd0\xb0)\0"
	"SRS\0"
	"srs\0"
	"sr\0"
	"ar-TN\0"
	"Arabic (Tunisia)\0"
	"\xd8\xa7\xd9\x84\xd8\xb9\xd8\xb1\xd8\xa8\xd9\x8a\xd8\xa9 (\xd8\xaa\xd9\x88\xd9\x86\xd8\xb3)\0"
	"ART\0"
	"TN\0"
	"en-ZA\0"
	"English (South Africa)\0"
	"ENS\0"
	"es-DO\0"
	"Spanish (Dominican Republic)\0"
	"espa\xc3\xb1ol (Rep\xc3\xba\x62lica Dominicana)\0"
	"ESD\0"
	"DO\0"
	"sr-Cyrl-BA\0"
	"Serbian (Cyrillic, Bosnia and Herzegovina)\0"
	"SRN\0"
	"srn\0"
	"ar-OM\0"
	"Arabic (Oman)\0"
	"\xd8\xa7\xd9\x84\xd8\xb9\xd8\xb1\xd8\xa8\xd9\x8a\xd8\xa9 (\xd8\xb9\xd9\x8f\xd9\x85\xd8\xa7\xd9\x86)\0"
	"ARO\0"
	"OM\0"
	"en-JM\0"
	"English (Jamaica)\0"
	"ENJ\0"
	"JM\0"
	"es-VE\0"
	"Spanish (Venezuela)\0"
	"espa\xc3\xb1ol (Venezuela)\0"
	"ESV\0"
	"VE\0"
	"bs-Cyrl-BA\0"
	"Bosnian (Cyrillic, Bosnia and Herzegovina)\0"
	"BSC\0"
	"bsc\0"
	"ar-YE\0"
	"Arabic (Yemen)\0"
	"\xd8\xa7\xd9\x84\xd8\xb9\xd8\xb1\xd8\xa8\xd9\x8a\xd8\xa9 (\xd8\xa7\xd9\x84\xd9\x8a\xd9\x85\xd9\x86)\0"
	"ARY\0"
	"YE\0"
	"es-CO\0"
	"Spanish (Colombia)\0"
	"espa\xc3\xb1ol (Colombia)\0"
	"ESO\0"
	"CO\0"
	"sr-Latn-RS\0"
	"Serbian (Latin, Serbia)\0"
	"\xd0\xa1\xd1\x80\xd0\xbf\xd1\x81\xd0\xba\xd0\xb8 (\xd0\xa1\xd1\x80\xd0\xb1\xd0\xb8\xd1\x98\xd0\xb0)\0"
	"SRM\0"
	"RS\0"
	"ar-SY\0"
	"Arabic (Syria)\0"
	"\xd8\xa7\xd9\x84\xd8\xb9\xd8\xb1\xd8\xa8\xd9\x8a\xd8\xa9 (\xd8\xb3\xd9\x88\xd8\xb1\xd9\x8a\xd8\xa7)\0"
	"ARS\0"
	"SY\0"
	"en-BZ\0"
	"English (Belize)\0"
	"ENL\0"
	"BZ\0"
	"es-PE\0"
	"Spanish (Peru)\0"
	"espa\xc3\xb1ol (Per\xc3\xba)\0"
	"ESR\0"
	"PE\0"
	"sr-Cyrl-RS\0"
	"Serbian (Cyrillic, Serbia)\0"
	"SRO\0"
	"ar-JO\0"
	"Arabic (Jordan)\0"
	"\xd8\xa7\xd9\x84\xd8\xb9\xd8\xb1\xd8\xa8\xd9\x8a\xd8\xa9 (\xd8\xa7\xd9\x84\xd8\xa3\xd8\xb1\xd8\xaf\xd9\x86)\0"
	"ARJ\0"
	"JO\0"
	"en-TT\0"
	"English (Trinidad and Tobago)\0"
	"ENT\0"
	"TT\0"
	"es-AR\0"
	"Spanish (Argentina)\0"
	"espa\xc3\xb1ol (Argentina)\0"
	"ESS\0"
	"AR\0"
	"sr-Latn-ME\0"
	"Serbian (Latin, Montenegro)\0"
	"\xd0\xa1\xd1\x80\xd0\xbf\xd1\x81\xd0\xba\xd0\xb8 (\xd0\xa6\xd1\x80\xd0\xbd\xd0\xb0 \xd0\x93\xd0\xbe\xd1\x80\xd0\xb0)\0"
	"SRP\0"
	"ME\0"
	"ar-LB\0"
	"Arabic (Lebanon)\0"
	"\xd8\xa7\xd9\x84\xd8\xb9\xd8\xb1\xd8\xa8\xd9\x8a\xd8\xa9 (\xd9\x84\xd8\xa8\xd9\x86\xd8\xa7\xd9\x86)\0"
	"ARB\0"
	"LB\0"
	"en-ZW\0"
	"English (Zimbabwe)\0"
	"ENW\0"
	"ZW\0"
	"es-EC\0"
	"Spanish (Ecuador)\0"
	"espa\xc3\xb1ol (Ecuador)\0"
	"ESF\0"
	"EC\0"
	"sr-Cyrl-ME\0"
	"Serbian (Cyrillic, Montenegro)\0"
	"SRQ\0"
	"ar-KW\0"
	"Arabic (Kuwait)\0"
	"\xd8\xa7\xd9\x84\xd8\xb9\xd8\xb1\xd8\xa8\xd9\x8a\xd8\xa9 (\xd8\xa7\xd9\x84\xd9\x83\xd9\x88\xd9\x8a\xd8\xaa)\0"
	"ARK\0"
	"KW\0"
	"en-PH\0"
	"English (Philippines)\0"
	"ENP\0"
	"es-CL\0"
	"Spanish (Chile)\0"
	"espa\xc3\xb1ol (Chile)\0"
	"ESL\0"
	"CL\0"
	"ar-AE\0"
	"Arabic (United Arab Emirates)\0"
	"\xd8\xa7\xd9\x84\xd8\xb9\xd8\xb1\xd8\xa8\xd9\x8a\xd8\xa9 (\xd8\xa7\xd9\x84\xd8\xa5\xd9\x85\xd8\xa7\xd8\xb1\xd8\xa7\xd8\xaa \xd8\xa7\xd9\x84\xd8\xb9\xd8\xb1\xd8\xa8\xd9\x8a\xd8\xa9 \xd8\xa7\xd9\x84\xd9\x85\xd8\xaa\xd8\xad\xd8\xaf\xd8\xa9)\0"
	"ARU\0"
	"AE\0"
	"es-UY\0"
	"Spanish (Uruguay)\0"
	"espa\xc3\xb1ol (Uruguay)\0"
	"ESY\0"
	"UY\0"
	"ar-BH\0"
	"Arabic (Bahrain)\0"
	"\xd8\xa7\xd9\x84\xd8\xb9\xd8\xb1\xd8\xa8\xd9\x8a\xd8\xa9 (\xd8\xa7\xd9\x84\xd8\xa8\xd8\xad\xd8\xb1\xd9\x8a\xd9\x86)\0"
	"ARH\0"
	"BH\0"
	"es-PY\0"
	"Spanish (Paraguay)\0"
	"espa\xc3\xb1ol (Paraguay)\0"
	"ESZ\0"
	"PY\0"
	"ar-QA\0"
	"Arabic (Qatar)\0"
	"\xd8\xa7\xd9\x84\xd8\xb9\xd8\xb1\xd8\xa8\xd9\x8a\xd8\xa9 (\xd9\x82\xd8\xb7\xd8\xb1)\0"
	"ARQ\0"
	"QA\0"
	"en-IN\0"
	"English (India)\0"
	"ENN\0"
	"es-BO\0"
	"Spanish (Bolivia)\0"
	"espa\xc3\xb1ol (Bolivia)\0"
	"ESB\0"
	"BO\0"
	"es-SV\0"
	"Spanish (El Salvador)\0"
	"espa\xc3\xb1ol (El Salvador)\0"
	"ESE\0"
	"SV\0"
	"en-SG\0"
	"English (Singapore)\0"
	"ENE\0"
	"es-HN\0"
	"Spanish (Honduras)\0"
	"espa\xc3\xb1ol (Honduras)\0"
	"ESH\0"
	"HN\0"
	"es-NI\0"
	"Spanish (Nicaragua)\0"
	"espa\xc3\xb1ol (Nicaragua)\0"
	"ESI\0"
	"NI\0"
	"es-PR\0"
	"Spanish (Puerto Rico)\0"
	"espa\xc3\xb1ol (Puerto Rico)\0"
	"ESU\0"
	"PR\0"
	"es-US\0"
	"Spanish (United States)\0"
	"espa\xc3\xb1ol (Estados Unidos)\0"
	"EST\0"
	"bs-Cyrl\0"
	"Bosnian (Cyrillic)\0"
	"bosanski\0"
	"bs-Latn\0"
	"Bosnian (Latin)\0"
	"sr-Cyrl\0"
	"Serbian (Cyrillic)\0"
	"\xd0\xa1\xd1\x80\xd0\xbf\xd1\x81\xd0\xba\xd0\xb8\0"
	"sr-Latn\0"
	"Serbian (Latin)\0"
	"az-Cyrl\0"
	"Azerbaijani (Cyrillic)\0"
	"Norwegian Nynorsk\0"
	"nynorsk\0"
	"Bosnian\0"
	"az-Latn\0"
	"Azerbaijani (Latin)\0"
	"uz-Cyrl\0"
	"Uzbek (Cyrillic)\0"
	"mn-Cyrl\0"
	"Mongolian (Cyrillic)\0"
	"MNN\0"
	"zh-Hant\0"
	"zh-CHT\0"
	"Chinese (Traditional) Legacy\0"
	"Norwegian Bokm\xc3\xa5l\0"
	"norsk bokm\xc3\xa5l\0"
	"Serbian\0"
	"SRB\0"
	"tg-Cyrl\0"
	"Tajik (Cyrillic)\0"
	"uz-Latn\0"
	"Uzbek (Latin)\0"
	"tzm-Latn\0"
	"Central Atlas Tamazight (Latin)\0"
	"ha-Latn\0"
	"Hausa (Latin)\0"
	"af-za\0"
	"am-et\0"
	"ar-ae\0"
	"ar-bh\0"
	"ar-dz\0"
	"ar-eg\0"
	"ar-iq\0"
	"ar-jo\0"
	"ar-kw\0"
	"ar-lb\0"
	"ar-ly\0"
	"ar-ma\0"
	"ar-om\0"
	"ar-qa\0"
	"ar-sa\0"
	"ar-sy\0"
	"ar-tn\0"
	"ar-ye\0"
	"as-in\0"
	"az-cyrl\0"
	"az-cyrl-az\0"
	"az-latn\0"
	"az-latn-az\0"
	"be-by\0"
	"bg-bg\0"
	"bn-bd\0"
	"bn-in\0"
	"bo-cn\0"
	"br-fr\0"
	"bs-cyrl\0"
	"bs-cyrl-ba\0"
	"bs-latn\0"
	"bs-latn-ba\0"
	"ca-es\0"
	"cs-cz\0"
	"cy-gb\0"
	"da-dk\0"
	"de-at\0"
	"de-ch\0"
	"de-de\0"
	"de-li\0"
	"de-lu\0"
	"el-gr\0"
	"en-au\0"
	"en-bz\0"
	"en-ca\0"
	"en-gb\0"
	"en-ie\0"
	"en-in\0"
	"en-jm\0"
	"en-nz\0"
	"en-ph\0"
	"en-sg\0"
	"en-tt\0"
	"en-us\0"
	"en-za\0"
	"en-zw\0"
	"es-ar\0"
	"es-bo\0"
	"es-cl\0"
	"es-co\0"
	"es-cr\0"
	"es-do\0"
	"es-ec\0"
	"es-es\0"
	"es-gt\0"
	"es-hn\0"
	"es-mx\0"
	"es-ni\0"
	"es-pa\0"
	"es-pe\0"
	"es-pr\0"
	"es-py\0"
	"es-sv\0"
	"es-us\0"
	"es-uy\0"
	"es-ve\0"
	"et-ee\0"
	"eu-es\0"
	"fa-ir\0"
	"fi-fi\0"
	"fil-ph\0"
	"fo-fo\0"
	"fr-be\0"
	"fr-ca\0"
	"fr-ch\0"
	"fr-fr\0"
	"fr-lu\0"
	"fr-mc\0"
	"ga-ie\0"
	"gd-gb\0"
	"gl-es\0"
	"gu-in\0"
	"ha-latn\0"
	"ha-latn-ng\0"
	"he-il\0"
	"hi-in\0"
	"hr-ba\0"
	"hr-hr\0"
	"hu-hu\0"
	"hy-am\0"
	"id-id\0"
	"ig-ng\0"
	"ii-cn\0"
	"is-is\0"
	"it-ch\0"
	"it-it\0"
	"ja-jp\0"
	"ka-ge\0"
	"kl-gl\0"
	"km-kh\0"
	"kn-in\0"
	"ko-kr\0"
	"kok-in\0"
	"lo-la\0"
	"lt-lt\0"
	"lv-lv\0"
	"mk-mk\0"
	"ml-in\0"
	"mn-cyrl\0"
	"mr-in\0"
	"mt-mt\0"
	"nb-no\0"
	"ne-np\0"
	"nl-be\0"
	"nl-nl\0"
	"nn-no\0"
	"nso-za\0"
	"or-in\0"
	"pl-pl\0"
	"ps-af\0"
	"pt-br\0"
	"pt-pt\0"
	"rm-ch\0"
	"ro-ro\0"
	"ru-ru\0"
	"rw-rw\0"
	"sah-ru\0"
	"se-fi\0"
	"se-no\0"
	"si-lk\0"
	"sk-sk\0"
	"sl-si\0"
	"sq-al\0"
	"sr-cyrl\0"
	"sr-cyrl-ba\0"
	"sr-cyrl-me\0"
	"sr-cyrl-rs\0"
	"sr-latn\0"
	"sr-latn-ba\0"
	"sr-latn-me\0"
	"sr-latn-rs\0"
	"sv-fi\0"
	"sv-se\0"
	"sw-ke\0"
	"ta-in\0"
	"te-in\0"
	"tg-cyrl\0"
	"tg-cyrl-tj\0"
	"th-th\0"
	"tn-za\0"
	"tr-tr\0"
	"tzm-latn\0"
	"uk-ua\0"
	"ur-pk\0"
	"uz-cyrl\0"
	"uz-cyrl-uz\0"
	"uz-latn\0"
	"uz-latn-uz\0"
	"vi-vn\0"
	"xh-za\0"
	"yo-ng\0"
	"zh-chs\0"
	"zh-cht\0"
	"zh-cn\0"
	"zh-hans\0"
	"zh-hant\0"
	"zh-hk\0"
	"zh-mo\0"
	"zh-sg\0"
	"zh-tw\0"
	"zu-za\0"
	"United Arab Emirates\0"
	"\xd8\xa7\xd9\x84\xd8\xa5\xd9\x85\xd8\xa7\xd8\xb1\xd8\xa7\xd8\xaa \xd8\xa7\xd9\x84\xd8\xb9\xd8\xb1\xd8\xa8\xd9\x8a\xd8\xa9 \xd8\xa7\xd9\x84\xd9\x85\xd8\xaa\xd8\xad\xd8\xaf\xd8\xa9\0"
	"AED\0"
	"United Arab Emirates Dirham\0"
	"\xd8\xaf\xd8\xb1\xd9\x87\xd9\x85 \xd8\xa5\xd9\x85\xd8\xa7\xd8\xb1\xd8\xa7\xd8\xaa\xd9\x8a\0"
	"AFG\0"
	"Afghanistan\0"
	"\xd8\xa7\xd9\x81\xd8\xba\xd8\xa7\xd9\x86\xd8\xb3\xd8\xaa\xd8\xa7\xd9\x86\0"
	"AFN\0"
	"Afghan Afghani\0"
	"\xd8\xa7\xd9\x81\xd8\xba\xd8\xa7\xd9\x86\xdb\x8d\0"
	"ALB\0"
	"Albania\0"
	"Shqip\xc3\xabri\0"
	"ALL\0"
	"Albanian Lek\0"
	"Leku shqiptar\0"
	"Armenia\0"
	"\xd5\x80\xd5\xa1\xd5\xb5\xd5\xa1\xd5\xbd\xd5\xbf\xd5\xa1\xd5\xb6\0"
	"AMD\0"
	"Armenian Dram\0"
	"\xd5\x80\xd5\xa1\xd5\xb5\xd5\xaf\xd5\xa1\xd5\xaf\xd5\xa1\xd5\xb6 \xd5\xa4\xd6\x80\xd5\xa1\xd5\xb4\0"
	"Argentina\0"
	"Argentine Peso\0"
	"peso argentino\0"
	"AUT\0"
	"Austria\0"
	"\xc3\x96sterreich\0"
	"EUR\0"
	"Euro\0"
	"AUS\0"
	"Australia\0"
	"AUD\0"
	"Australian Dollar\0"
	"Azerbaijan\0"
	"Az\xc9\x99rbaycan\0"
	"AZN\0"
	"Azerbaijani Manat\0"
	"Az\xc9\x99rbaycan Manat\xc4\xb1\0"
	"BIH\0"
	"Bosnia and Herzegovina\0"
	"Bosna i Hercegovina\0"
	"BAM\0"
	"Bosnia-Herzegovina Convertible Mark\0"
	"konvertibilna marka\0"
	"BGD\0"
	"Bangladesh\0"
	"\xe0\xa6\xac\xe0\xa6\xbe\xe0\xa6\x82\xe0\xa6\xb2\xe0\xa6\xbe\xe0\xa6\xa6\xe0\xa7\x87\xe0\xa6\xb6\0"
	"BDT\0"
	"Bangladeshi Taka\0"
	"\xe0\xa6\xac\xe0\xa6\xbe\xe0\xa6\x82\xe0\xa6\xb2\xe0\xa6\xbe\xe0\xa6\xa6\xe0\xa7\x87\xe0\xa6\xb6\xe0\xa7\x80 \xe0\xa6\x9f\xe0\xa6\xbe\xe0\xa6\x95\xe0\xa6\xbe\0"
	"Belgium\0"
	"Belgique\0"
	"euro\0"
	"Bulgaria\0"
	"\xd0\x91\xd1\x8a\xd0\xbb\xd0\xb3\xd0\xb0\xd1\x80\xd0\xb8\xd1\x8f\0"
	"BGN\0"
	"Bulgarian Lev\0"
	"\xd0\x91\xd1\x8a\xd0\xbb\xd0\xb3\xd0\xb0\xd1\x80\xd1\x81\xd0\xba\xd0\xb8 \xd0\xbb\xd0\xb5\xd0\xb2\0"
	"BHR\0"
	"Bahrain\0"
	"\xd8\xa7\xd9\x84\xd8\xa8\xd8\xad\xd8\xb1\xd9\x8a\xd9\x86\0"
	"BHD\0"
	"Bahraini Dinar\0"
	"\xd8\xaf\xd9\x8a\xd9\x86\xd8\xa7\xd8\xb1 \xd8\xa8\xd8\xad\xd8\xb1\xd9\x8a\xd9\x86\xd9\x8a\0"
	"BOL\0"
	"Bolivia\0"
	"Bolivian Boliviano\0"
	"boliviano\0"
	"BRA\0"
	"Brazil\0"
	"Brasil\0"
	"BRL\0"
	"Brazilian Real\0"
	"Real brasileiro\0"
	"BLR\0"
	"Belarus\0"
	"\xd0\x91\xd0\xb5\xd0\xbb\xd0\xb0\xd1\x80\xd1\x83\xd1\x81\xd1\x8c\0"
	"BYR\0"
	"Belarusian Ruble\0"
	"\xd0\xb1\xd0\xb5\xd0\xbb\xd0\xb0\xd1\x80\xd1\x83\xd1\x81\xd0\xba\xd1\x96 \xd1\x80\xd1\x83\xd0\xb1\xd0\xb5\xd0\xbb\xd1\x8c\0"
	"BLZ\0"
	"Belize\0"
	"BZD\0"
	"Belize Dollar\0"
	"CAN\0"
	"Canada\0"
	"CAD\0"
	"Canadian Dollar\0"
	"dollar canadien\0"
	"CHE\0"
	"Switzerland\0"
	"Svizra\0"
	"Swiss Franc\0"
	"franc svizzer\0"
	"CHL\0"
	"Chile\0"
	"CLP\0"
	"Chilean Peso\0"
	"peso chileno\0"
	"CHN\0"
	"China\0"
	"\xe0\xbd\xa2\xe0\xbe\x92\xe0\xbe\xb1\xe0\xbc\x8b\xe0\xbd\x93\xe0\xbd\x82\0"
	"CNY\0"
	"Chinese Yuan\0"
	"\xe0\xbd\xa1\xe0\xbd\xb4\xe0\xbc\x8b\xe0\xbd\xa8\xe0\xbd\x93\xe0\xbc\x8b\0"
	"COL\0"
	"Colombia\0"
	"COP\0"
	"Colombian Peso\0"
	"peso colombiano\0"
	"CRI\0"
	"Costa Rica\0"
	"CRC\0"
	"Costa Rican Col\xc3\xb3n\0"
	"col\xc3\xb3n costarricense\0"
	"CZE\0"
	"Czech Republic\0"
	"\xc4\x8c\x65sk\xc3\xa1 republika\0"
	"CZK\0"
	"Czech Republic Koruna\0"
	"\xc4\x8d\x65sk\xc3\xa1 koruna\0"
	"Germany\0"
	"Deutschland\0"
	"DNK\0"
	"Denmark\0"
	"Danmark\0"
	"DKK\0"
	"Danish Krone\0"
	"Dansk krone\0"
	"DOM\0"
	"Dominican Republic\0"
	"Rep\xc3\xba\x62lica Dominicana\0"
	"DOP\0"
	"Dominican Peso\0"
	"peso dominicano\0"
	"DZA\0"
	"Algeria\0"
	"\xd8\xa7\xd9\x84\xd8\xac\xd8\xb2\xd8\xa7\xd8\xa6\xd8\xb1\0"
	"DZD\0"
	"Algerian Dinar\0"
	"\xd8\xaf\xd9\x8a\xd9\x86\xd8\xa7\xd8\xb1 \xd8\xac\xd8\xb2\xd8\xa7\xd8\xa6\xd8\xb1\xd9\x8a\0"
	"ECU\0"
	"Ecuador\0"
	"USD\0"
	"US Dollar\0"
	"d\xc3\xb3lar estadounidense\0"
	"Estonia\0"
	"Eesti\0"
	"EGY\0"
	"Egypt\0"
	"\xd9\x85\xd8\xb5\xd8\xb1\0"
	"EGP\0"
	"Egyptian Pound\0"
	"\xd8\xac\xd9\x86\xd9\x8a\xd9\x87 \xd9\x85\xd8\xb5\xd8\xb1\xd9\x8a\0"
	"Spain\0"
	"Espanya\0"
	"ETH\0"
	"Ethiopia\0"
	"\xe1\x8a\xa2\xe1\x89\xb5\xe1\x8b\xae\xe1\x8c\xb5\xe1\x8b\xab\0"
	"ETB\0"
	"Ethiopian Birr\0"
	"\xe1\x8b\xa8\xe1\x8a\xa2\xe1\x89\xb5\xe1\x8b\xae\xe1\x8c\xb5\xe1\x8b\xab \xe1\x89\xa5\xe1\x88\xad\0"
	"Finland\0"
	"Suomi\0"
	"FRO\0"
	"Faroe Islands\0"
	"F\xc3\xb8royar\0"
	"donsk kr\xc3\xb3na\0"
	"France\0"
	"GBR\0"
	"United Kingdom\0"
	"Y Deyrnas Unedig\0"
	"GBP\0"
	"British Pound Sterling\0"
	"Punt Sterling Prydain\0"
	"GEO\0"
	"Georgia\0"
	"\xe1\x83\xa1\xe1\x83\x90\xe1\x83\xa5\xe1\x83\x90\xe1\x83\xa0\xe1\x83\x97\xe1\x83\x95\xe1\x83\x94\xe1\x83\x9a\xe1\x83\x9d\0"
	"GEL\0"
	"Georgian Lari\0"
	"\xe1\x83\xa5\xe1\x83\x90\xe1\x83\xa0\xe1\x83\x97\xe1\x83\xa3\xe1\x83\x9a\xe1\x83\x98 \xe1\x83\x9a\xe1\x83\x90\xe1\x83\xa0\xe1\x83\x98\0"
	"GRL\0"
	"Greenland\0"
	"Kalaallit Nunaat\0"
	"danmarkimut koruuni\0"
	"GRC\0"
	"Greece\0"
	"\xce\x95\xce\xbb\xce\xbb\xce\xac\xce\xb4\xce\xb1\0"
	"\xce\x95\xcf\x85\xcf\x81\xcf\x8e\0"
	"GTM\0"
	"Guatemala\0"
	"GTQ\0"
	"Guatemalan Quetzal\0"
	"quetzal guatemalteco\0"
	"HKG\0"
	"Hong Kong SAR China\0"
	"\xe4\xb8\xad\xe5\x9b\xbd\xe9\xa6\x99\xe6\xb8\xaf\xe7\x89\xb9\xe5\x88\xab\xe8\xa1\x8c\xe6\x94\xbf\xe5\x8c\xba\0"
	"HKD\0"
	"Hong Kong Dollar\0"
	"\xe6\xb8\xaf\xe5\x85\x83\0"
	"HND\0"
	"Honduras\0"
	"HNL\0"
	"Honduran Lempira\0"
	"lempira hondure\xc3\xb1o\0"
	"Croatia\0"
	"Hrvatska\0"
	"HRK\0"
	"Croatian Kuna\0"
	"hrvatska kuna\0"
	"Hungary\0"
	"Magyarorsz\xc3\xa1g\0"
	"HUF\0"
	"Hungarian Forint\0"
	"magyar forint\0"
	"IDN\0"
	"Indonesia\0"
	"IDR\0"
	"Indonesian Rupiah\0"
	"Rupiah Indonesia\0"
	"IRL\0"
	"Ireland\0"
	"\xc3\x89ire\0"
	"ISR\0"
	"Israel\0"
	"\xd7\x99\xd7\xa9\xd7\xa8\xd7\x90\xd7\x9c\0"
	"ILS\0"
	"Israeli New Sheqel\0"
	"\xd7\xa9\xd7\xb4\xd7\x97\0"
	"India\0"
	"\xe0\xa4\xad\xe0\xa4\xbe\xe0\xa4\xb0\xe0\xa4\xa4\0"
	"INR\0"
	"Indian Rupee\0"
	"\xe0\xa4\xad\xe0\xa4\xbe\xe0\xa4\xb0\xe0\xa4\xa4\xe0\xa5\x80\xe0\xa4\xaf \xe0\xa4\xb0\xe0\xa5\x81\xe0\xa4\xaa\xe0\xa4\xaf\xe0\xa4\xbe\0"
	"IRQ\0"
	"Iraq\0"
	"\xd8\xa7\xd9\x84\xd8\xb9\xd8\xb1\xd8\xa7\xd9\x82\0"
	"IQD\0"
	"Iraqi Dinar\0"
	"\xd8\xaf\xd9\x8a\xd9\x86\xd8\xa7\xd8\xb1 \xd8\xb9\xd8\xb1\xd8\xa7\xd9\x82\xd9\x8a\0"
	"IRN\0"
	"Iran\0"
	"\xd8\xa7\xdb\x8c\xd8\xb1\xd8\xa7\xd9\x86\0"
	"IRR\0"
	"Iranian Rial\0"
	"\xd8\xb1\xdb\x8c\xd8\xa7\xd9\x84 \xd8\xa7\xdb\x8c\xd8\xb1\xd8\xa7\xd9\x86\0"
	"Iceland\0"
	"\xc3\x8dsland\0"
	"ISK\0"
	"Icelandic Kr\xc3\xb3na\0"
	"\xc3\x8dslensk kr\xc3\xb3na\0"
	"Italy\0"
	"Italia\0"
	"JAM\0"
	"Jamaica\0"
	"JMD\0"
	"Jamaican Dollar\0"
	"JOR\0"
	"Jordan\0"
	"\xd8\xa7\xd9\x84\xd8\xa3\xd8\xb1\xd8\xaf\xd9\x86\0"
	"JOD\0"
	"Jordanian Dinar\0"
	"\xd8\xaf\xd9\x8a\xd9\x86\xd8\xa7\xd8\xb1 \xd8\xa3\xd8\xb1\xd8\xaf\xd9\x86\xd9\x8a\0"
	"Japan\0"
	"\xe6\x97\xa5\xe6\x9c\xac\0"
	"JPY\0"
	"Japanese Yen\0"
	"\xe6\x97\xa5\xe6\x9c\xac\xe5\x86\x86\0"
	"KEN\0"
	"Kenya\0"
	"KES\0"
	"Kenyan Shilling\0"
	"shilingi ya Kenya\0"
	"Cambodia\0"
	"\xe1\x9e\x80\xe1\x9e\x98\xe1\x9f\x92\xe1\x9e\x96\xe1\x9e\xbb\xe1\x9e\x87\xe1\x9e\xb6\0"
	"KHR\0"
	"Cambodian Riel\0"
	"\xe1\x9e\x9a\xe1\x9f\x80\xe1\x9e\x9b \xe1\x9e\x80\xe1\x9e\x98\xe1\x9f\x92\xe1\x9e\x96\xe1\x9e\xbb\xe1\x9e\x87\xe1\x9e\xb6\0"
	"South Korea\0"
	"\xeb\x8c\x80\xed\x95\x9c\xeb\xaf\xbc\xea\xb5\xad\0"
	"KRW\0"
	"South Korean Won\0"
	"\xeb\x8c\x80\xed\x95\x9c\xeb\xaf\xbc\xea\xb5\xad \xec\x9b\x90\0"
	"KWT\0"
	"Kuwait\0"
	"\xd8\xa7\xd9\x84\xd9\x83\xd9\x88\xd9\x8a\xd8\xaa\0"
	"KWD\0"
	"Kuwaiti Dinar\0"
	"\xd8\xaf\xd9\x8a\xd9\x86\xd8\xa7\xd8\xb1 \xd9\x83\xd9\x88\xd9\x8a\xd8\xaa\xd9\x8a\0"
	"Laos\0"
	"LAK\0"
	"Laotian Kip\0"
	"\xe0\xba\x81\xe0\xba\xb5\xe0\xba\x9a \xe0\xba\xa5\xe0\xba\xb2\xe0\xba\xa7\0"
	"LBN\0"
	"Lebanon\0"
	"\xd9\x84\xd8\xa8\xd9\x86\xd8\xa7\xd9\x86\0"
	"LBP\0"
	"Lebanese Pound\0"
	"\xd8\xac\xd9\x86\xd9\x8a\xd8\xa9 \xd9\x84\xd8\xa8\xd9\x86\xd8\xa7\xd9\x86\xd9\x8a\0"
	"LIE\0"
	"Liechtenstein\0"
	"Schweizer Franken\0"
	"LKA\0"
	"Sri Lanka\0"
	"\xe0\xb7\x81\xe0\xb7\x8a\xe2\x80\x8d\xe0\xb6\xbb\xe0\xb7\x93 \xe0\xb6\xbd\xe0\xb6\x82\xe0\xb6\x9a\xe0\xb7\x8f\xe0\xb7\x80\0"
	"LKR\0"
	"Sri Lankan Rupee\0"
	"\xe0\xb7\x81\xe0\xb7\x8a\xe2\x80\x8d\xe0\xb6\xbb\xe0\xb7\x93 \xe0\xb6\xbd\xe0\xb6\x82\xe0\xb6\x9a\xe0\xb7\x8f \xe0\xb6\xbb\xe0\xb7\x94\xe0\xb6\xb4\xe0\xb7\x92\xe0\xb6\xba\xe0\xb6\xbd\0"
	"LTU\0"
	"Lithuania\0"
	"Lietuva\0"
	"LTL\0"
	"Lithuanian Litas\0"
	"Lietuvos litas\0"
	"LUX\0"
	"Luxembourg\0"
	"Luxemburg\0"
	"LVA\0"
	"Latvia\0"
	"Latvija\0"
	"eiro\0"
	"LBY\0"
	"Libya\0"
	"\xd9\x84\xd9\x8a\xd8\xa8\xd9\x8a\xd8\xa7\0"
	"LYD\0"
	"Libyan Dinar\0"
	"\xd8\xaf\xd9\x8a\xd9\x86\xd8\xa7\xd8\xb1 \xd9\x84\xd9\x8a\xd8\xa8\xd9\x8a\0"
	"Morocco\0"
	"\xd8\xa7\xd9\x84\xd9\x85\xd8\xba\xd8\xb1\xd8\xa8\0"
	"MAD\0"
	"Moroccan Dirham\0"
	"\xd8\xaf\xd8\xb1\xd9\x87\xd9\x85 \xd9\x85\xd8\xba\xd8\xb1\xd8\xa8\xd9\x8a\0"
	"MCO\0"
	"Monaco\0"
	"MNE\0"
	"Montenegro\0"
	"\xd0\xa6\xd1\x80\xd0\xbd\xd0\xb0 \xd0\x93\xd0\xbe\xd1\x80\xd0\xb0\0"
	"Evro\0"
	"MKD\0"
	"Macedonia\0"
	"\xd0\x9c\xd0\xb0\xd0\xba\xd0\xb5\xd0\xb4\xd0\xbe\xd0\xbd\xd0\xb8\xd1\x98\xd0\xb0\0"
	"Macedonian Denar\0"
	"\xd0\x9c\xd0\xb0\xd0\xba\xd0\xb5\xd0\xb4\xd0\xbe\xd0\xbd\xd1\x81\xd0\xba\xd0\xb8 \xd0\xb4\xd0\xb5\xd0\xbd\xd0\xb0\xd1\x80\0"
	"MAC\0"
	"Macau SAR China\0"
	"\xe4\xb8\xad\xe5\x9b\xbd\xe6\xbe\xb3\xe9\x97\xa8\xe7\x89\xb9\xe5\x88\xab\xe8\xa1\x8c\xe6\x94\xbf\xe5\x8c\xba\0"
	"MOP\0"
	"Macanese Pataca\0"
	"\xe6\xbe\xb3\xe9\x96\x80\xe5\x85\x83\0"
	"Malta\0"
	"Ewro\0"
	"MEX\0"
	"Mexico\0"
	"M\xc3\xa9xico\0"
	"MXN\0"
	"Mexican Peso\0"
	"peso mexicano\0"
	"NGA\0"
	"Nigeria\0"
	"Najeriya\0"
	"NGN\0"
	"Nigerian Naira\0"
	"Naira\0"
	"NIC\0"
	"Nicaragua\0"
	"NIO\0"
	"Nicaraguan C\xc3\xb3rdoba\0"
	"c\xc3\xb3rdoba nicarag\xc3\xbc\x65nse\0"
	"Netherlands\0"
	"Nederland\0"
	"Norway\0"
	"Norge\0"
	"NOK\0"
	"Norwegian Krone\0"
	"norske kroner\0"
	"NPL\0"
	"Nepal\0"
	"\xe0\xa4\xa8\xe0\xa5\x87\xe0\xa4\xaa\xe0\xa4\xbe\xe0\xa4\xb2\0"
	"NPR\0"
	"Nepalese Rupee\0"
	"\xe0\xa4\xa8\xe0\xa5\x87\xe0\xa4\xaa\xe0\xa4\xbe\xe0\xa4\xb2\xe0\xa5\x80 \xe0\xa4\xb0\xe0\xa5\x82\xe0\xa4\xaa\xe0\xa5\x88\xe0\xa4\xaf\xe0\xa4\xbe\xe0\xa4\x81\0"
	"NZL\0"
	"New Zealand\0"
	"NZD\0"
	"New Zealand Dollar\0"
	"OMN\0"
	"Oman\0"
	"\xd8\xb9\xd9\x8f\xd9\x85\xd8\xa7\xd9\x86\0"
	"OMR\0"
	"Omani Rial\0"
	"\xd8\xb1\xd9\x8a\xd8\xa7\xd9\x84 \xd8\xb9\xd9\x85\xd8\xa7\xd9\x86\xd9\x8a\0"
	"Panama\0"
	"Panam\xc3\xa1\0"
	"PAB\0"
	"Panamanian Balboa\0"
	"balboa paname\xc3\xb1o\0"
	"PER\0"
	"Peru\0"
	"Per\xc3\xba\0"
	"PEN\0"
	"Peruvian Nuevo Sol\0"
	"nuevo sol peruano\0"
	"PHL\0"
	"Philippines\0"
	"Pilipinas\0"
	"PHP\0"
	"Philippine Peso\0"
	"PAK\0"
	"Pakistan\0"
	"\xd9\xbe\xd8\xa7\xda\xa9\xd8\xb3\xd8\xaa\xd8\xa7\xd9\x86\0"
	"PKR\0"
	"Pakistani Rupee\0"
	"\xd9\xbe\xd8\xa7\xda\xa9\xd8\xb3\xd8\xaa\xd8\xa7\xd9\x86\xdb\x8c \xd8\xb1\xd9\x88\xd9\xbe\xdb\x8c\xdb\x81\0"
	"POL\0"
	"Poland\0"
	"Polska\0"
	"PLN\0"
	"Polish Zloty\0"
	"z\xc5\x82oty polski\0"
	"PRI\0"
	"Puerto Rico\0"
	"PRT\0"
	"Portugal\0"
	"PRY\0"
	"Paraguay\0"
	"PYG\0"
	"Paraguayan Guarani\0"
	"guaran\xc3\xad paraguayo\0"
	"QAT\0"
	"Qatar\0"
	"\xd9\x82\xd8\xb7\xd8\xb1\0"
	"QAR\0"
	"Qatari Rial\0"
	"\xd8\xb1\xd9\x8a\xd8\xa7\xd9\x84 \xd9\x82\xd8\xb7\xd8\xb1\xd9\x8a\0"
	"ROU\0"
	"Romania\0"
	"Rom\xc3\xa2nia\0"
	"RON\0"
	"Romanian Leu\0"
	"leu rom\xc3\xa2nesc\0"
	"Serbia\0"
	"\xd0\xa1\xd1\x80\xd0\xb1\xd0\xb8\xd1\x98\xd0\xb0\0"
	"RSD\0"
	"Serbian Dinar\0"
	"Srpski dinar\0"
	"Russia\0"
	"\xd0\xa0\xd0\xbe\xd1\x81\xd1\x81\xd0\xb8\xd1\x8f\0"
	"RUB\0"
	"Russian Ruble\0"
	"\xd0\xa0\xd0\xbe\xd1\x81\xd1\x81\xd0\xb8\xd0\xb9\xd1\x81\xd0\xba\xd0\xb8\xd0\xb9 \xd1\x80\xd1\x83\xd0\xb1\xd0\xbb\xd1\x8c\0"
	"RWA\0"
	"Rwanda\0"
	"RWF\0"
	"Rwandan Franc\0"
	"SAU\0"
	"Saudi Arabia\0"
	"\xd8\xa7\xd9\x84\xd9\x85\xd9\x85\xd9\x84\xd9\x83\xd8\xa9 \xd8\xa7\xd9\x84\xd8\xb9\xd8\xb1\xd8\xa8\xd9\x8a\xd8\xa9 \xd8\xa7\xd9\x84\xd8\xb3\xd8\xb9\xd9\x88\xd8\xaf\xd9\x8a\xd8\xa9\0"
	"SAR\0"
	"Saudi Riyal\0"
	"\xd8\xb1\xd9\x8a\xd8\xa7\xd9\x84 \xd8\xb3\xd8\xb9\xd9\x88\xd8\xaf\xd9\x8a\0"
	"SWE\0"
	"Sweden\0"
	"Sverige\0"
	"SEK\0"
	"Swedish Krona\0"
	"svensk krona\0"
	"SGP\0"
	"Singapore\0"
	"\xe6\x96\xb0\xe5\x8a\xa0\xe5\x9d\xa1\0"
	"SGD\0"
	"Singapore Dollar\0"
	"\xe6\x96\xb0\xe5\x8a\xa0\xe5\x9d\xa1\xe5\x85\x83\0"
	"SVN\0"
	"Slovenia\0"
	"Slovenija\0"
	"evro\0"
	"SVK\0"
	"Slovakia\0"
	"Slovensko\0"
	"El Salvador\0"
	"SYR\0"
	"Syria\0"
	"\xd8\xb3\xd9\x88\xd8\xb1\xd9\x8a\xd8\xa7\0"
	"SYP\0"
	"Syrian Pound\0"
	"\xd9\x84\xd9\x8a\xd8\xb1\xd8\xa9 \xd8\xb3\xd9\x88\xd8\xb1\xd9\x8a\xd8\xa9\0"
	"Thailand\0"
	"THB\0"
	"Thai Baht\0"
	"\xe0\xb8\x9a\xe0\xb8\xb2\xe0\xb8\x97\xe0\xb9\x84\xe0\xb8\x97\xe0\xb8\xa2\0"
	"TJK\0"
	"Tajikistan\0"
	"\xd0\xa2\xd0\xbe\xd2\xb7\xd0\xb8\xd0\xba\xd0\xb8\xd1\x81\xd1\x82\xd0\xbe\xd0\xbd\0"
	"TJS\0"
	"Tajikistani Somoni\0"
	"\xd0\xa1\xd0\xbe\xd0\xbc\xd0\xbe\xd0\xbd\xd3\xa3\0"
	"TUN\0"
	"Tunisia\0"
	"\xd8\xaa\xd9\x88\xd9\x86\xd8\xb3\0"
	"TND\0"
	"Tunisian Dinar\0"
	"\xd8\xaf\xd9\x8a\xd9\x86\xd8\xa7\xd8\xb1\xd8\xaa\xd9\x88\xd9\x86\xd8\xb3\xd9\x8a\0"
	"TUR\0"
	"Turkey\0"
	"T\xc3\xbcrkiye\0"
	"TRY\0"
	"Turkish Lira\0"
	"T\xc3\xbcrk Liras\xc4\xb1\0"
	"TTO\0"
	"Trinidad and Tobago\0"
	"TTD\0"
	"Trinidad and Tobago Dollar\0"
	"TWN\0"
	"Taiwan\0"
	"\xe5\x8f\xb0\xe6\xb9\xbe\0"
	"TWD\0"
	"New Taiwan Dollar\0"
	"\xe6\x96\xb0\xe8\x87\xba\xe5\xb9\xa3\0"
	"Ukraine\0"
	"\xd0\xa3\xd0\xba\xd1\x80\xd0\xb0\xd1\x97\xd0\xbd\xd0\xb0\0"
	"UAH\0"
	"Ukrainian Hryvnia\0"
	"\xd1\x83\xd0\xba\xd1\x80\xd0\xb0\xd1\x97\xd0\xbd\xd1\x81\xd1\x8c\xd0\xba\xd0\xb0 \xd0\xb3\xd1\x80\xd0\xb8\xd0\xb2\xd0\xbd\xd1\x8f\0"
	"USA\0"
	"United States\0"
	"URY\0"
	"Uruguay\0"
	"UYU\0"
	"Uruguayan Peso\0"
	"peso uruguayo\0"
	"Uzbekistan\0"
	"O\xca\xbbzbekiston\0"
	"UZS\0"
	"Uzbekistan Som\0"
	"O\xca\xbbzbekiston so\xca\xbbm\0"
	"VEN\0"
	"Venezuela\0"
	"VEF\0"
	"Venezuelan Bol\xc3\xadvar\0"
	"bol\xc3\xadvar venezolano\0"
	"VNM\0"
	"Vietnam\0"
	"Vi\xe1\xbb\x87t Nam\0"
	"VND\0"
	"Vietnamese Dong\0"
	"\xc4\x90\xe1\xbb\x93ng Vi\xe1\xbb\x87t Nam\0"
	"YEM\0"
	"Yemen\0"
	"\xd8\xa7\xd9\x84\xd9\x8a\xd9\x85\xd9\x86\0"
	"YER\0"
	"Yemeni Rial\0"
	"\xd8\xb1\xd9\x8a\xd8\xa7\xd9\x84 \xd9\x8a\xd9\x85\xd9\x86\xd9\x8a\0"
	"ZAF\0"
	"South Africa\0"
	"ZAR\0"
	"South African Rand\0"
	"ZWE\0"
	"Zimbabwe\0"
};


#endif

