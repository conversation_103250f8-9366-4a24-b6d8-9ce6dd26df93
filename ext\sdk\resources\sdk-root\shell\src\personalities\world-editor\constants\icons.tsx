import React from 'react';
import { AiOutlineRotateRight } from 'react-icons/ai';
import { CgArrowsExpandUpRight } from 'react-icons/cg';
import { FiBox, FiPlus } from 'react-icons/fi';
import { GiResize } from 'react-icons/gi';
import { IoBandageSharp } from 'react-icons/io5';

export const addObjectToolIcon = <FiPlus style={{ shapeRendering: 'crispEdges' }} />;
export const additionsToolIcon = <FiBox />;
export const patchesToolIcon = <IoBandageSharp />;

export const translateIcon = <CgArrowsExpandUpRight />;
export const rotateIcon = <AiOutlineRotateRight />;
export const scaleIcon = <GiResize />;
