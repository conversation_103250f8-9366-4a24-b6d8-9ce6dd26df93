@use "~@cfx-dev/ui-components/dist/styles-scss/ui" as ui;

$fg: hsl(45, 7%, 11%);
$bg: hsl(0, 0%, 100%);

.cfxui-theme-rdr3-light {
  @include ui.define-main-colors($bg, $fg, (
    'primary': #d80d0d
  ));

  // Asset overrides
  @include ui.def('checkered-pattern', url(assets/images/checkered_light.svg));
  @include ui.def('backdrop-image', url(assets/images/bg2-redm.jpg));
  @include ui.def('backdrop-image-blur', url(assets/images/bg2-redm-blur.png));

  // Color overrides
  @include ui.def               ('text-opacity-10',                .20);
  @include ui.define-color-token('text-a10', ui.color('main', 950, .20));
  @include ui.def               ('text-opacity-25',                .35);
  @include ui.define-color-token('text-a25', ui.color('main', 950, .35));
  @include ui.def               ('text-opacity-50',                .60);
  @include ui.define-color-token('text-a50', ui.color('main', 950, .60));
  @include ui.def               ('text-opacity-75',                .85);
  @include ui.define-color-token('text-a75', ui.color('main', 950, .85));

  @include ui.define-color-token('text-warning', ui.color('warning', 700));

  @include ui.define-color-token('shadow-small', rgba(0, 0, 0, .3));
  @include ui.define-color-token('shadow-large', rgba(0, 0, 0, .3));
  @include ui.define-color-token('backdrop-shader', ui.color('main', 100, .75));

  @include ui.define-color-token('button-primary-text', ui.color('main'));
  @include ui.define-color-token('button-primary-hover-text', ui.color('main'));
  @include ui.define-color-token('button-primary-active-text', ui.color('main', 950));

  @include ui.define-color-token('premium-badge-text', ui.color('main'));
  @include ui.define-color-token('premium-badge-background', ui.color('main', 950));

  @include ui.define-color-token('play-button-background-1', rgb(228, 0, 0));
  @include ui.define-color-token('play-button-background-2', rgb(206, 75, 0));


  .color-1 { color: darken(#F44336, 30%);	}
	.color-2 { color: darken(#4CAF50, 30%);	}
	.color-3 { color: darken(#FFEB3B, 30%);	}
	.color-4 { color: darken(#42A5F5, 30%);	}
	.color-5 { color: darken(#03A9F4, 30%);	}
	.color-6 { color: darken(#9C27B0, 30%);	}
	.color-8 { color: darken(#FF5722, 30%);	}
	.color-9 { color: darken(#9E9E9E, 30%);	}

  // Too thin otherwise
  --font-weight-normal: 400;
  --font-weight-bold: 500;
  --font-weight-bolder: 600;
}
