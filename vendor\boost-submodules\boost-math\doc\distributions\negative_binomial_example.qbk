[section:neg_binom_eg Negative Binomial Distribution Examples]

(See also the reference documentation for the __negative_binomial_distrib.)

[section:neg_binom_conf Calculating Confidence Limits on the Frequency of Occurrence for the Negative Binomial Distribution]

Imagine you have a process that follows a negative binomial distribution:
for each trial conducted, an event either occurs or does it does not, referred
to as "successes" and "failures". The frequency with which successes occur
is variously referred to as the
success fraction, success ratio, success percentage, occurrence frequency, or probability of occurrence.

If, by experiment, you want to measure the
 the best estimate of success fraction is given simply
by /k/ \/ /N/, for /k/ successes out of /N/ trials.

However our confidence in that estimate will be shaped by how many trials were conducted,
and how many successes were observed.  The static member functions 
`negative_binomial_distribution<>::find_lower_bound_on_p` and
`negative_binomial_distribution<>::find_upper_bound_on_p`
allow you to calculate the confidence intervals for your estimate of the success fraction.

The sample program [@../../example/neg_binom_confidence_limits.cpp 
neg_binom_confidence_limits.cpp] illustrates their use.

[import ../../example/neg_binom_confidence_limits.cpp]

[neg_binomial_confidence_limits]
Let's see some sample output for a 1 in 10
success ratio, first for a mere 20 trials:

[pre'''______________________________________________
2-Sided Confidence Limits For Success Fraction
______________________________________________
Number of trials                         =  20
Number of successes                      =  2
Number of failures                       =  18
Observed frequency of occurrence         =  0.1
___________________________________________
Confidence        Lower          Upper
 Value (%)        Limit          Limit
___________________________________________
    50.000        0.04812        0.13554
    75.000        0.03078        0.17727
    90.000        0.01807        0.22637
    95.000        0.01235        0.26028
    99.000        0.00530        0.33111
    99.900        0.00164        0.41802
    99.990        0.00051        0.49202
    99.999        0.00016        0.55574
''']

As you can see, even at the 95% confidence level the bounds (0.012 to 0.26) are
really very wide, and very asymmetric about the observed value 0.1.

Compare that with the program output for a mass
2000 trials:

[pre'''______________________________________________
2-Sided Confidence Limits For Success Fraction
______________________________________________
Number of trials                         =  2000
Number of successes                      =  200
Number of failures                       =  1800
Observed frequency of occurrence         =  0.1
___________________________________________
Confidence        Lower          Upper
 Value (%)        Limit          Limit
___________________________________________
    50.000        0.09536        0.10445
    75.000        0.09228        0.10776
    90.000        0.08916        0.11125
    95.000        0.08720        0.11352
    99.000        0.08344        0.11802
    99.900        0.07921        0.12336
    99.990        0.07577        0.12795
    99.999        0.07282        0.13206
''']

Now even when the confidence level is very high, the limits (at 99.999%, 0.07 to 0.13) are really
quite close and nearly symmetric to the observed value of 0.1.

[endsect][/section:neg_binom_conf Calculating Confidence Limits on the Frequency of Occurrence]

[section:neg_binom_size_eg Estimating Sample Sizes for the Negative Binomial.]

Imagine you have an event
(let's call it a "failure" - though we could equally well call it a success if we felt it was a 'good' event)
that you know will occur in 1 in N trials.  You may want to know how many trials you need to
conduct to be P% sure of observing at least k such failures.  
If the failure events follow a negative binomial
distribution (each trial either succeeds or fails)
then the static member function `negative_binomial_distibution<>::find_minimum_number_of_trials`
can be used to estimate the minimum number of trials required to be P% sure
of observing the desired number of failures.

The example program 
[@../../example/neg_binomial_sample_sizes.cpp neg_binomial_sample_sizes.cpp]
demonstrates its usage. 

[import ../../example/neg_binomial_sample_sizes.cpp]
[neg_binomial_sample_sizes]

[note Since we're calculating the /minimum/ number of trials required,
we'll err on the safe side and take the ceiling of the result.
Had we been calculating the
/maximum/ number of trials permitted to observe less than a certain 
number of /failures/ then we would have taken the floor instead.  We
would also have called `find_minimum_number_of_trials` like this:
``
   floor(negative_binomial::find_minimum_number_of_trials(failures, p, alpha[i]))
``            
which would give us the largest number of trials we could conduct and
still be P% sure of observing /failures or less/ failure events, when the
probability of success is /p/.]

We'll finish off by looking at some sample output, firstly suppose
we wish to observe at least 5 "failures" with a 50/50 (0.5) chance of
success or failure:

[pre
'''Target number of failures = 5,   Success fraction = 50%

____________________________
Confidence        Min Number
 Value (%)        Of Trials
____________________________
    50.000          11
    75.000          14
    90.000          17
    95.000          18
    99.000          22
    99.900          27
    99.990          31
    99.999          36
'''
]

So 18 trials or more would yield a 95% chance that at least our 5
required failures would be observed.

Compare that to what happens if the success ratio is 90%:

[pre'''Target number of failures = 5.000,   Success fraction = 90.000%

____________________________
Confidence        Min Number
 Value (%)        Of Trials
____________________________
    50.000          57
    75.000          73
    90.000          91
    95.000         103
    99.000         127
    99.900         159
    99.990         189
    99.999         217
''']

So now 103 trials are required to observe at least 5 failures with
95% certainty.

[endsect] [/section:neg_binom_size_eg Estimating Sample Sizes.]

[section:negative_binomial_example1 Negative Binomial Sales Quota Example.]

This example program 
[@../../example/negative_binomial_example1.cpp negative_binomial_example1.cpp (full source code)]
demonstrates a simple use to find the probability of meeting a sales quota.

[import ../../example/negative_binomial_example1.cpp]
[negative_binomial_eg1_1]
[negative_binomial_eg1_2]

[endsect] [/section:negative_binomial_example1]

[section:negative_binomial_example2 Negative Binomial Table Printing Example.]
Example program showing output of a table of values of cdf and pdf for various k failures.
[import ../../example/negative_binomial_example2.cpp]
[neg_binomial_example2]
[neg_binomial_example2_1]
[endsect] [/section:negative_binomial_example1 Negative Binomial example 2.]

[endsect] [/section:neg_binom_eg Negative Binomial Distribution Examples]

[/ 
  Copyright 2006 John Maddock and Paul A. Bristow.
  Distributed under the Boost Software License, Version 1.0.
  (See accompanying file LICENSE_1_0.txt or copy at
  http://www.boost.org/LICENSE_1_0.txt).
]

