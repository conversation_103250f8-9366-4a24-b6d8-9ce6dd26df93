<math  xmlns="http://www.w3.org/1998/Math/MathML" display="block" ><mrow >                              <msup><mrow ><mi >z</mi></mrow><mrow ><mn>2</mn></mrow></msup ><mfrac><mrow ><msup><mrow ><mi >d</mi></mrow><mrow ><mn>2</mn></mrow></msup ><mi >u</mi></mrow> <mrow ><mi >d</mi><msup><mrow ><mi >z</mi></mrow><mrow ><mn>2</mn></mrow></msup ></mrow></mfrac> <mo class="MathClass-bin">+</mo> <mi >z</mi><mfrac><mrow ><mi >d</mi><mi >u</mi></mrow> <mrow ><mi >d</mi><mi >z</mi></mrow></mfrac> <mo class="MathClass-bin">+</mo> <mrow ><mo class="MathClass-open">(</mo><mrow><msup><mrow ><mi >z</mi></mrow><mrow ><mn>2</mn></mrow></msup > <mo class="MathClass-bin">&#x2212;</mo> <msup><mrow ><mi >&#x03BD;</mi></mrow><mrow ><mn>2</mn></mrow></msup ></mrow><mo class="MathClass-close">)</mo></mrow><mi >u</mi> <mo class="MathClass-rel">=</mo> <mn>0</mn></mrow></math>