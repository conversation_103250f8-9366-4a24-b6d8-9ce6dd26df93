# Copyright (c) 2013 The Chromium Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//build/compiled_action.gni")
import("//build/config/nacl/config.gni")
import("//build/config/ui.gni")

# Reset sources_assignment_filter for the BUILD.gn file to prevent
# regression during the migration of Chromium away from the feature.
# See build/no_sources_assignment_filter.md for more information.
# TODO(crbug.com/1018739): remove this when migration is done.
set_sources_assignment_filter([])

if (is_android) {
  import("//build/config/android/rules.gni")
}

static_library("test_config") {
  testonly = true
  sources = [
    "test_switches.cc",
    "test_switches.h",
    "test_timeouts.cc",
    "test_timeouts.h",
  ]
  deps = [
    "//base",
    "//base:clang_coverage_buildflags",
  ]
}

static_library("test_support") {
  testonly = true
  sources = [
    "../task/sequence_manager/test/fake_task.cc",
    "../task/sequence_manager/test/fake_task.h",
    "../task/sequence_manager/test/mock_time_domain.cc",
    "../task/sequence_manager/test/mock_time_domain.h",
    "../task/sequence_manager/test/mock_time_message_pump.cc",
    "../task/sequence_manager/test/mock_time_message_pump.h",
    "../task/sequence_manager/test/sequence_manager_for_test.cc",
    "../task/sequence_manager/test/sequence_manager_for_test.h",
    "../task/sequence_manager/test/test_task_queue.cc",
    "../task/sequence_manager/test/test_task_queue.h",
    "../task/sequence_manager/test/test_task_time_observer.h",
    "../timer/mock_timer.cc",
    "../timer/mock_timer.h",
    "../trace_event/trace_config_memory_test_util.h",
    "bind_test_util.cc",
    "bind_test_util.h",
    "copy_only_int.cc",
    "copy_only_int.h",
    "gmock_callback_support.h",
    "gmock_move_support.h",
    "gtest_util.cc",
    "gtest_util.h",
    "gtest_xml_unittest_result_printer.cc",
    "gtest_xml_unittest_result_printer.h",
    "gtest_xml_util.cc",
    "gtest_xml_util.h",
    "icu_test_util.cc",
    "icu_test_util.h",
    "launcher/test_result.cc",
    "launcher/test_result.h",
    "launcher/test_results_tracker.h",
    "launcher/unit_test_launcher.h",
    "metrics/histogram_enum_reader.cc",
    "metrics/histogram_enum_reader.h",
    "metrics/histogram_tester.cc",
    "metrics/histogram_tester.h",
    "metrics/user_action_tester.cc",
    "metrics/user_action_tester.h",
    "mock_callback.h",
    "mock_devices_changed_observer.cc",
    "mock_devices_changed_observer.h",
    "mock_entropy_provider.cc",
    "mock_entropy_provider.h",
    "mock_log.cc",
    "mock_log.h",
    "move_only_int.h",
    "multiprocess_test.h",
    "null_task_runner.cc",
    "null_task_runner.h",
    "perf_log.cc",
    "perf_log.h",
    "perf_test_suite.cc",
    "perf_test_suite.h",
    "perf_time_logger.cc",
    "perf_time_logger.h",
    "power_monitor_test_base.cc",
    "power_monitor_test_base.h",
    "scoped_command_line.cc",
    "scoped_command_line.h",
    "scoped_environment_variable_override.cc",
    "scoped_environment_variable_override.h",
    "scoped_feature_list.cc",
    "scoped_feature_list.h",
    "scoped_field_trial_list_resetter.cc",
    "scoped_field_trial_list_resetter.h",
    "scoped_mock_clock_override.cc",
    "scoped_mock_clock_override.h",
    "scoped_mock_time_message_loop_task_runner.cc",
    "scoped_mock_time_message_loop_task_runner.h",
    "scoped_path_override.cc",
    "scoped_path_override.h",
    "sequenced_task_runner_test_template.cc",
    "sequenced_task_runner_test_template.h",
    "simple_test_clock.cc",
    "simple_test_clock.h",
    "simple_test_tick_clock.cc",
    "simple_test_tick_clock.h",
    "task_environment.cc",
    "task_environment.h",
    "task_runner_test_template.cc",
    "task_runner_test_template.h",
    "test_discardable_memory_allocator.cc",
    "test_discardable_memory_allocator.h",
    "test_file_util.cc",
    "test_file_util.h",
    "test_io_thread.cc",
    "test_io_thread.h",
    "test_message_loop.cc",
    "test_message_loop.h",
    "test_mock_time_task_runner.cc",
    "test_mock_time_task_runner.h",
    "test_pending_task.cc",
    "test_pending_task.h",
    "test_shared_memory_util.cc",
    "test_shared_memory_util.h",
    "test_simple_task_runner.cc",
    "test_simple_task_runner.h",
    "test_suite.cc",
    "test_suite.h",
    "thread_test_helper.cc",
    "thread_test_helper.h",
    "trace_event_analyzer.cc",
    "trace_event_analyzer.h",
    "trace_to_file.cc",
    "trace_to_file.h",
    "values_test_util.cc",
    "values_test_util.h",
  ]

  if (is_win) {
    sources += [
      "scoped_os_info_override_win.cc",
      "scoped_os_info_override_win.h",
      "test_file_util_win.cc",
      "test_reg_util_win.cc",
      "test_reg_util_win.h",
      "test_shortcut_win.cc",
      "test_shortcut_win.h",
    ]
  }

  if (is_linux) {
    sources += [ "test_file_util_linux.cc" ]
  }

  if (is_mac) {
    sources += [
      "mock_chrome_application_mac.h",
      "mock_chrome_application_mac.mm",
      "test_file_util_mac.cc",
    ]
  }

  if (is_android) {
    sources += [
      "android/java_handler_thread_helpers.cc",
      "android/java_handler_thread_helpers.h",
      "android/url_utils.cc",
      "android/url_utils.h",
      "multiprocess_test_android.cc",
      "reached_code_profiler_android.cc",
      "test_file_util_android.cc",
      "test_support_android.cc",
      "test_support_android.h",
      "thread_pool_test_helpers_android.cc",
    ]
  }

  if (is_ios) {
    sources += [
      "../ios/weak_nsobject.h",
      "../ios/weak_nsobject.mm",
      "ios/wait_util.h",
      "ios/wait_util.mm",
      "launcher/unit_test_launcher_ios.cc",
      "test_listener_ios.h",
      "test_listener_ios.mm",
      "test_support_ios.h",
      "test_support_ios.mm",
    ]
  } else if (!is_nacl_nonsfi) {
    sources += [
      "launcher/test_launcher.cc",
      "launcher/test_launcher.h",
      "launcher/test_launcher_test_utils.cc",
      "launcher/test_launcher_test_utils.h",
      "launcher/test_launcher_tracer.cc",
      "launcher/test_launcher_tracer.h",
      "launcher/test_results_tracker.cc",
      "launcher/unit_test_launcher.cc",
      "multiprocess_test.cc",
    ]
  }

  configs += [ "//build/config:precompiled_headers" ]

  data = [
    # The isolate needs this script for setting up the test. It's not actually
    # needed to run this target locally.
    "//testing/test_env.py",
  ]

  public_deps = [
    ":test_config",
    "//base",
    "//base:base_static",
    "//base:i18n",
  ]
  deps = [
    "//base/third_party/dynamic_annotations",
    "//testing/gmock",
    "//testing/gtest",
    "//third_party/icu:icuuc",
    "//third_party/libxml:libxml_utils",
    "//third_party/libxml:xml_reader",
  ]

  if (is_posix || is_fuchsia) {
    sources += [
      "scoped_locale.cc",
      "scoped_locale.h",
      "test_file_util_posix.cc",
    ]
  }

  if (is_fuchsia) {
    deps += [ "//third_party/fuchsia-sdk/sdk:zx" ]
  }

  if (is_linux) {
    public_deps += [ ":fontconfig_util_linux" ]
    data_deps = [ "//third_party/test_fonts" ]
    if (current_toolchain == host_toolchain) {
      data_deps += [ ":do_generate_fontconfig_caches" ]
    }
  }

  if (is_ios) {
    sources += [ "test_file_util_mac.cc" ]
    deps += [ ":google_test_runner_shared_headers" ]
  }

  if (is_mac) {
    libs = [ "AppKit.framework" ]
  }

  if (is_android) {
    sources += [ "test_file_util_linux.cc" ]
    deps += [
      ":base_unittests_jni_headers",
      ":test_support_jni_headers",
    ]
    public_deps += [ ":test_support_java" ]
  }

  if (is_nacl_nonsfi) {
    sources += [
      "launcher/test_launcher.h",
      "launcher/test_result.h",
      "launcher/unit_test_launcher.h",
      "launcher/unit_test_launcher_nacl_nonsfi.cc",
    ]
    sources -= [
      "gtest_xml_util.cc",
      "gtest_xml_util.h",
      "icu_test_util.cc",
      "icu_test_util.h",
      "metrics/histogram_enum_reader.cc",
      "metrics/histogram_enum_reader.h",
      "perf_test_suite.cc",
      "perf_test_suite.h",
      "scoped_path_override.cc",
      "scoped_path_override.h",
      "test_discardable_memory_allocator.cc",
      "test_discardable_memory_allocator.h",
      "test_file_util.cc",
      "test_file_util.h",
      "test_file_util_posix.cc",
      "test_suite.cc",
      "test_suite.h",
      "trace_to_file.cc",
      "trace_to_file.h",
    ]
    public_deps -= [ "//base:i18n" ]
    deps -= [
      "//third_party/icu:icuuc",
      "//third_party/libxml:libxml_utils",
      "//third_party/libxml:xml_reader",
    ]
  }
}

config("base_test_implementation") {
  defines = [ "IS_BASE_TEST_IMPL" ]
}

config("perf_test_config") {
  defines = [ "PERF_TEST" ]
}

# This is a source set instead of a static library because it seems like some
# linkers get confused when "main" is in a static library, and if you link to
# this, you always want the object file anyway.
source_set("test_support_perf") {
  testonly = true
  sources = [ "run_all_perftests.cc" ]
  deps = [
    ":test_support",
    "//base",
    "//testing/gtest",
  ]

  public_configs = [ ":perf_test_config" ]
}

static_library("test_launcher_nacl_nonsfi") {
  testonly = true
  sources = [
    "launcher/test_launcher_nacl_nonsfi.cc",
    "launcher/test_launcher_nacl_nonsfi.h",
  ]
  deps = [ ":test_support" ]
}

static_library("run_all_unittests") {
  testonly = true
  sources = [ "run_all_unittests.cc" ]
  deps = [ ":test_support" ]
}

# These sources are linked into both the base_unittests binary and the test
# shared library target below.
source_set("native_library_test_utils") {
  testonly = true
  sources = [
    "native_library_test_utils.cc",
    "native_library_test_utils.h",
  ]
}

# This shared library is dynamically loaded by ImmediateCrash unittests.
shared_library("immediate_crash_test_helper") {
  sources = [ "immediate_crash_test_helper.cc" ]

  # Note: the helper has a header-only dependency on //base/immediate_helper.h.
  # However, the build rule intentionally omits an explicit //base dependency
  # to avoid potential ODR violations and minimize the amount of code linked in.

  # Try to minimize the risk of non-official builds generating different code.
  if (!is_official_build) {
    configs -= [ "//build/config/compiler:default_optimization" ]
    configs += [ "//build/config/compiler:optimize_max" ]
  }

  # Disable sanitization: sanitized builds are assumed to be saner than normal,
  # and can affect codegen in surprising ways, which breaks the tests.
  configs -= [ "//build/config/sanitizers:default_sanitizer_flags" ]

  if (is_android) {
    configs -= [ "//build/config/android:hide_all_but_jni_onload" ]
  }
}

# This shared library is dynamically loaded by NativeLibrary unittests.
shared_library("test_shared_library") {
  testonly = true
  sources = [ "test_shared_library.cc" ]

  deps = [ ":native_library_test_utils" ]
}

static_library("run_all_base_unittests") {
  # Only targets in base should depend on this, targets outside base
  # should depend on run_all_unittests above.
  visibility = [ "//base/*" ]
  testonly = true
  sources = [ "run_all_base_unittests.cc" ]
  deps = [ ":test_support" ]
}

if (is_linux) {
  source_set("fontconfig_util_linux") {
    sources = [
      "fontconfig_util_linux.cc",
      "fontconfig_util_linux.h",
    ]
    data_deps = [ ":fonts_conf" ]
    deps = [
      "//base",
      "//third_party/fontconfig",
    ]
  }

  copy("fonts_conf") {
    sources = [ "fonts.conf" ]
    outputs = [ "${root_build_dir}/etc/fonts/{{source_file_part}}" ]
  }

  if (current_toolchain == host_toolchain) {
    executable("generate_fontconfig_caches") {
      testonly = true
      sources = [ "generate_fontconfig_caches.cc" ]
      deps = [
        ":fontconfig_util_linux",
        "//base",
      ]
    }

    compiled_action("do_generate_fontconfig_caches") {
      testonly = true
      tool = ":generate_fontconfig_caches"

      # The copied test fonts are inputs to generate_fontconfig_caches, so must
      # be listed in deps, not data_deps (https://crbug.com/919422).
      deps = [ "//third_party/test_fonts" ]
      args = []
      outputs = [
        "$root_out_dir/fontconfig_caches/fb5c91b2895aa445d23aebf7f9e2189c-le64.cache-7",
        "$root_out_dir/test_fonts/.uuid",
      ]
    }
  }
}

if (is_fuchsia || is_linux) {
  shared_library("malloc_wrapper") {
    testonly = true
    sources = [ "malloc_wrapper.cc" ]
    deps = [ "//base" ]
  }
}

if (is_android) {
  generate_jni("base_unittests_jni_headers") {
    testonly = true
    sources = [
      "android/java/src/org/chromium/base/ContentUriTestUtils.java",
      "android/java/src/org/chromium/base/JavaHandlerThreadHelpers.java",
    ]
  }

  generate_jni("test_support_jni_headers") {
    testonly = true
    sources = [
      "android/java/src/org/chromium/base/MainReturnCodeResult.java",
      "android/java/src/org/chromium/base/MultiprocessTestClientLauncher.java",
      "android/javatests/src/org/chromium/base/test/ReachedCodeProfiler.java",
      "android/javatests/src/org/chromium/base/test/task/ThreadPoolTestHelpers.java",
      "android/javatests/src/org/chromium/base/test/util/UrlUtils.java",
    ]
  }

  android_library("test_support_java") {
    testonly = true
    deps = [
      "//base:base_java",
      "//base:base_java_test_support",
      "//testing/android/native_test:native_main_runner_java",
      "//third_party/android_deps:androidx_annotation_annotation_java",
      "//third_party/jsr-305:jsr_305_javalib",
    ]
    srcjar_deps = [ ":test_support_java_aidl" ]
    sources = [
      "android/java/src/org/chromium/base/GarbageCollectionTestUtils.java",
      "android/java/src/org/chromium/base/MainReturnCodeResult.java",
      "android/java/src/org/chromium/base/MultiprocessTestClientLauncher.java",
      "android/java/src/org/chromium/base/MultiprocessTestClientService.java",
      "android/java/src/org/chromium/base/MultiprocessTestClientService0.java",
      "android/java/src/org/chromium/base/MultiprocessTestClientService1.java",
      "android/java/src/org/chromium/base/MultiprocessTestClientService2.java",
      "android/java/src/org/chromium/base/MultiprocessTestClientService3.java",
      "android/java/src/org/chromium/base/MultiprocessTestClientService4.java",
      "android/java/src/org/chromium/base/MultiprocessTestClientService5.java",
      "android/java/src/org/chromium/base/MultiprocessTestClientService6.java",
      "android/java/src/org/chromium/base/MultiprocessTestClientService7.java",
      "android/java/src/org/chromium/base/MultiprocessTestClientService8.java",
      "android/java/src/org/chromium/base/MultiprocessTestClientService9.java",
      "android/java/src/org/chromium/base/MultiprocessTestClientServiceDelegate.java",
    ]
  }

  android_aidl("test_support_java_aidl") {
    testonly = true
    import_include = [
      "android/java/src",
      "//base/android/java/src",
    ]
    sources = [
      "android/java/src/org/chromium/base/ITestCallback.aidl",
      "android/java/src/org/chromium/base/ITestController.aidl",
    ]
  }
}

if (is_ios) {
  source_set("google_test_runner_shared_headers") {
    sources = [ "ios/google_test_runner_delegate.h" ]
  }

  source_set("google_test_runner") {
    sources = [ "ios/google_test_runner.mm" ]
    deps = [
      ":google_test_runner_shared_headers",
      "//base",
    ]
    libs = [ "UIKit.framework" ]
    configs += [
      "//build/config/compiler:enable_arc",
      "//build/config/ios:xctest_config",
    ]
  }
}

# Trivial executable which outputs space-delimited argv to stdout,
# used for testing.
executable("test_child_process") {
  testonly = true
  sources = [ "test_child_process.cc" ]
}
