// Copyright 2014 The Chromium Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     base/android/jni_generator/jni_generator.py
// For
//     java/util/HashSet

#ifndef java_util_HashSet_JNI
#define java_util_HashSet_JNI

#include <jni.h>

#include "base/android/jni_generator/jni_generator_helper.h"


// Step 1: Forward declarations.

JNI_REGISTRATION_EXPORT extern const char kClassPath_java_util_HashSet[];
const char kClassPath_java_util_HashSet[] = "java/util/HashSet";
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_REGISTRATION_EXPORT std::atomic<jclass> g_java_util_HashSet_clazz(nullptr);
#ifndef java_util_HashSet_clazz_defined
#define java_util_HashSet_clazz_defined
inline jclass java_util_HashSet_clazz(JNIEnv* env) {
  return base::android::LazyGetClass(env, kClassPath_java_util_HashSet, &g_java_util_HashSet_clazz);
}
#endif


// Step 2: Constants (optional).


// Step 3: Method stubs.
namespace JNI_HashSet {


static std::atomic<jmethodID> g_java_util_HashSet_dummy(nullptr);
static void Java_HashSet_dummy(JNIEnv* env, const base::android::JavaRef<jobject>& obj)
    __attribute__ ((unused));
static void Java_HashSet_dummy(JNIEnv* env, const base::android::JavaRef<jobject>& obj) {
  jclass clazz = java_util_HashSet_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_HashSet_clazz(env));

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "dummy",
          "()V",
          &g_java_util_HashSet_dummy);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id);
}

static std::atomic<jmethodID> g_java_util_HashSet_getClass(nullptr);
static base::android::ScopedJavaLocalRef<jclass> Java_HashSet_getClass(JNIEnv* env, const
    base::android::JavaRef<jobject>& obj) __attribute__ ((unused));
static base::android::ScopedJavaLocalRef<jclass> Java_HashSet_getClass(JNIEnv* env, const
    base::android::JavaRef<jobject>& obj) {
  jclass clazz = java_util_HashSet_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_HashSet_clazz(env), NULL);

  jni_generator::JniJavaCallContextChecked call_context;
  call_context.Init<
      base::android::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getClass",
          "()Ljava/lang/Class<*>;",
          &g_java_util_HashSet_getClass);

  jclass ret =
      static_cast<jclass>(env->CallObjectMethod(obj.obj(),
          call_context.base.method_id));
  return base::android::ScopedJavaLocalRef<jclass>(env, ret);
}

}  // namespace JNI_HashSet

// Step 4: Generated test functions (optional).


#endif  // java_util_HashSet_JNI
