[article Statistical Distribution Explorer
    [quickbook 1.4]
    [copyright 2008 <PERSON>, <PERSON>]
    [license
        Distributed under the Boost Software License, Version 1.0.
        (See accompanying file LICENSE_1_0.txt or copy at
        [@http://www.boost.org/LICENSE_1_0.txt])
    ]
    [authors [<PERSON><PERSON><PERSON>, <PERSON>], [<PERSON>, <PERSON>]]
    [category math]
    [purpose mathematics]
    [/last-revision $Date$]
]

A Windows utility to show the properties of statistical distributions
using parameters provided interactively by the user.

The distributions provided are:

*bernoulli
*beta_distribution
*binomial_distribution
*cauchy
*chi_squared
*exponential
*extreme_value
*fisher_f
*gamma_distribution
*lognormal_distribution
*negative_binomial_distribution
*normal_distribution
*pareto
*poisson
*rayleigh
*students_t
*triangular
*uniform
*weibull

Properties of distributions computed are:

*mean
*mode
*median
*variance
*standard deviation
*coefficient of variation,
*skewness
*kurtosis
*excess
*range supported

Calculated, from values provided, are:

*probability density (or mass) function (PDF)
*cumulative distribution function (CDF), and complement
*Quantiles (percentiles or fractiles) are calculated for typical risk (alpha) probabilities (0.001, 0.01, 0.5, 0.1, 0.333)
and for additional probabilities provided by the user.

Results can be saved to text files using Save or SaveAs.
All the values on the four tabs are output to the file chosen,
and are tab separated to assist input to other programs,
for example, spreadsheets or text editors.

Note: Excel (for example), only shows 10 decimal digits, by default:
to display the maximum possible precision (about 15 decimal digits),
it is necessary to format all cells to display this precision.
Although unusually accurate, not all values computed by Distexplorer will be as accurate as this.
Values shown as NaN cannot be calculated from the value(s) given,
most commonly because the value input is outside the range for the distribution.

For more information, including downloads, see

[@http://distexplorer.sourceforge.net/ Distexplorer at Sourceforge]

This Microsoft Windows 32 package distribution.exe
was generated from a C# program
and uses a boost_math.dll generated using the
Boost.Math C++ source code from the Boost.Math Toolkit, compiled in CLI mode,
containing the underlying statistical distribution classes and functions.

All source code is freely available for view and use under the
[@http://www.boost.org/LICENSE_1_0.txt Boost Open Source License].

[@https://svn.boost.org/svn/boost/sandbox\math_toolkit\libs\math\dot_net_example
Math Toolkit C++ source code]
to produce boost_math.dll is in the most recent [@http://www.boost.org Boost] release, initially 1.35.0.

It is distributed as a single Windows Installer package Setupdistex.msi.
Unzip the distexplorer.zip to a temporary location of your choice and run setup.exe.

(Note that .NET framework 2.0 and VCredist are requirements for this program.
Most recent and updated Windows environments will already have these,
but they are quickly, easily and safely installed from the Microsoft site if required.)

(The package cannot be run on other platforms at present but it should be possible
to build an equivalent utility on any C/C++ platform if anyone would like to undertake this task.)

[/ Distexplorer.qbk
  Copyright 2008 John Maddock and Paul A. Bristow.
  Distributed under the Boost Software License, Version 1.0.
  (See accompanying file LICENSE_1_0.txt or copy at
  http://www.boost.org/LICENSE_1_0.txt).
]

