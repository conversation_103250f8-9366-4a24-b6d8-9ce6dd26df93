// Copyright 2014 The Chromium Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     base/android/jni_generator/jni_generator.py
// For
//     org/chromium/example/SampleProxyJni

#ifndef org_chromium_example_SampleProxyJni_JNI
#define org_chromium_example_SampleProxyJni_JNI

#include <jni.h>

#include "base/android/jni_generator/jni_generator_helper.h"


// Step 1: Forward declarations.


// Step 2: Constants (optional).


// Step 3: Method stubs.
JNI_GENERATOR_EXPORT void
    Java_org_chromium_base_natives_GEN_1JNI_org_1chromium_1example_1SampleProxyJni_1foo(
    JNIEnv* env,
    jclass jcaller,
    jlong nativePtr) {
  FooAndroid::BarDelegate* native = reinterpret_cast<FooAndroid::BarDelegate*>(nativePtr);
  CHECK_NATIVE_PTR(env, jcaller, native, "Foo");
  return native->Foo(env);
}

static jint JNI_SampleProxyJni_Bar(JNIEnv* env, jint x,
    jint y);

JNI_GENERATOR_EXPORT jint
    Java_org_chromium_base_natives_GEN_1JNI_org_1chromium_1example_1SampleProxyJni_1bar(
    JNIEnv* env,
    jclass jcaller,
    jint x,
    jint y) {
  return JNI_SampleProxyJni_Bar(env, x, y);
}

static base::android::ScopedJavaLocalRef<jstring> JNI_SampleProxyJni_Foobar(JNIEnv* env, const
    base::android::JavaParamRef<jstring>& x,
    const base::android::JavaParamRef<jstring>& y);

JNI_GENERATOR_EXPORT jstring
    Java_org_chromium_base_natives_GEN_1JNI_org_1chromium_1example_1SampleProxyJni_1foobar(
    JNIEnv* env,
    jclass jcaller,
    jstring x,
    jstring y) {
  return JNI_SampleProxyJni_Foobar(env, base::android::JavaParamRef<jstring>(env, x),
      base::android::JavaParamRef<jstring>(env, y)).Release();
}


// Step 4: Generated test functions (optional).


#endif  // org_chromium_example_SampleProxyJni_JNI
