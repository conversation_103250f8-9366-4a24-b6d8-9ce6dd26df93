@import '../../../../vars.scss';

.root {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  width: 100%;
  height: 100%;

  .wrapper {
    flex-grow: 1;

    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    width: 100%;

    gap: $q*8;

    &.editing {
      .item {
        opacity: .5;
      }
    }

    .item {
      position: relative;

      display: flex;
      align-items: center;
      justify-content: space-between;

      padding: $q*2;

      width: 40%;
      max-width: calc(50% - #{$q*2});

      // border-radius: $q;

      background-color: rgba($fgColor, .05);

      cursor: pointer;
      user-select: none;

      &:hover {
        box-shadow: 0 0 0 2px rgba($fgColor, .2) inset;
      }

      &.editing {
        box-shadow: 0 0 0 2px $acColor inset;

        opacity: 1;

        .controls {
          position: absolute;

          display: flex;
          align-items: center;
          justify-content: center;

          --height: #{$q*5};

          gap: $q;

          height: var(--height);
          bottom: calc(-1 * var(--height));
          right: 0;

          button {
            display: flex;
            align-items: center;
            justify-content: center;

            gap: $q;

            height: var(--height);

            padding: $q*2;
            padding-top: calc(#{$q*2} + 2px);

            @include fontPrimary;

            color: $fgColor;
            background-color: rgba($fgColor, .05);
            border: solid 2px rgba($fgColor, .1);
            border-top: none;

            cursor: pointer;

            &.cancel svg {
              color: $erColor;
            }
            &.save svg {
              color: $scColor;
            }

            @include interactiveTransition;

            &:hover {
              background-color: $acColor;

              svg {
                color: $fgColor;
              }
            }
          }
        }
      }

      .title {
        display: block;
      }
    }
  }
}
