{"key": "iterator", "name": "Iterator", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "description": "The Boost Iterator Library contains two parts. The first is a system of concepts which extend the C++ standard iterator requirements. The second is a framework of components for building iterators based on these extended concepts and includes several useful iterator adaptors.", "category": ["Iterators"], "maintainers": ["<PERSON> <dave -at- boost-consulting.com>", "<PERSON> <witt - at - acm.org>", "<PERSON>. <jeffrey.hellrung -at- gmail.com>"], "cxxstd": "03"}