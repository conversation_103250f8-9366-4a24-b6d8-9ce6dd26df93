// Full-Screen Authentication UI Overlay - No background, just overlay
.root {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  margin: 0 !important;
  padding: 0 !important;
  background: transparent !important; // No background - slideshow handles it
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10 !important; // Behind navbar but above slideshow
  animation: fadeIn 0.5s ease-out;

  // Override any parent constraints
  max-width: none !important;
  max-height: none !important;
  min-width: 100vw !important;
  min-height: 100vh !important;

  // Remove any unwanted elements
  * {
    box-sizing: border-box;
  }

  // Hide any potential scrollbars or unwanted elements
  overflow: hidden;

  // Ensure no borders or margins
  border: none;
  outline: none;
}

// Simple form container - just center the form on fullscreen background
.formContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  padding: 2rem;
  position: relative;
  z-index: 60; // Above background but below navbar
}

.formContent {
  width: 100%;
  max-width: 400px;
  background: rgba(44, 44, 44, 0.9); // Semi-transparent background
  backdrop-filter: blur(15px);
  border-radius: 16px;
  padding: 2.5rem;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
  border: none !important;
  position: relative;
  z-index: 60; // Above background but below navbar

  // Force remove any potential borders from all children
  * {
    border: none !important;
    border-image: none !important;
    box-shadow: none !important;
    outline: none !important;
  }

  // Enhanced glassmorphism effect
  background: linear-gradient(135deg,
    rgba(44, 44, 44, 0.9) 0%,
    rgba(50, 50, 50, 0.85) 100%);

  // Better shadow for depth
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4),
  0 8px 32px rgba(0, 0, 0, 0.3),
  inset 0 1px 0 rgba(255, 255, 255, 0.1);

  // Remove any unwanted browser styling
  * {
    box-sizing: border-box;
  }

  // Fix form layout
  form {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  // Remove any default input styling that might cause visual issues
  input,
  button,
  select,
  textarea {
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
    margin: 0;
    border: 0;
    outline: 0;
    background: transparent;
    color: inherit;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
  }

  // Remove autofill background
  input:-webkit-autofill,
  input:-webkit-autofill:hover,
  input:-webkit-autofill:focus,
  input:-webkit-autofill:active {
    -webkit-box-shadow: 0 0 0 30px rgba(255, 255, 255, 0.05) inset !important;
    -webkit-text-fill-color: white !important;
  }
}

// Image side removed - using full background instead

// Form Header
.formHeader {
  margin-bottom: 2rem;
  text-align: left;
}

.title {
  color: white;
  font-size: 2rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  line-height: 1.2;
  font-family: inherit;

  // Remove any default heading styling
  padding: 0;
  border: 0;
  outline: 0;
  text-decoration: none;
}

.subtitle {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.9rem;
  margin: 0;
  line-height: 1.4;
}

// Input Groups - Clean layout
.inputGroup {
  margin-bottom: 1.5rem;
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;

  // Reset any inherited styles
  * {
    box-sizing: border-box;
  }
}

.inputLabel {
  display: block;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.passwordHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.forgotPassword {
  background: none;
  border: none;
  color: #F5E6A8; // Vàng kem
  font-size: 0.875rem;
  cursor: pointer;
  text-decoration: none;
  transition: color 0.2s ease;

  &:hover {
    color: #FF8C42; // Cam khi hover
  }
}

// Modern Input Styling
.modernInput {
  width: 100%;
  padding: 0.875rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: none !important;
  border-radius: 8px;
  color: white;
  font-size: 0.9rem;
  font-family: inherit;
  transition: all 0.2s ease;
  outline: none;
  box-sizing: border-box;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;

  &::placeholder {
    color: rgba(255, 255, 255, 0.4);
  }

  &:focus {
    border: none !important;
    background: rgba(255, 255, 255, 0.08);
    box-shadow: none !important;
  }

  &:hover:not(:focus) {
    border: none !important;
  }

  &.error {
    border: none !important;
    background: rgba(239, 68, 68, 0.1);
  }

  // Remove autofill styling
  &:-webkit-autofill,
  &:-webkit-autofill:hover,
  &:-webkit-autofill:focus {
    -webkit-box-shadow: 0 0 0 1000px rgba(255, 255, 255, 0.05) inset;
    -webkit-text-fill-color: white;
    transition: background-color 5000s ease-in-out 0s;
  }
}

// Password Input Container
.passwordInputContainer {
  position: relative;
  display: flex;
  align-items: center;

  .modernInput {
    padding-right: 3rem; // Make space for the toggle button
  }
}

// Password Toggle Button
.passwordToggle {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  outline: none;
  z-index: 1;

  &:hover:not(:disabled) {
    color: rgba(255, 255, 255, 0.9);
    background: rgba(255, 255, 255, 0.1);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  svg {
    width: 18px;
    height: 18px;
  }
}

// Modern Button
.modernButton {
  width: 100%;
  padding: 0.875rem 1.5rem;
  background: linear-gradient(135deg, #FF8C42 0%, #7CB342 100%); // Cam đến xanh lá
  border: none;
  border-radius: 8px;
  color: white;
  font-size: 0.9rem;
  font-weight: 600;
  font-family: inherit;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 1.5rem;
  outline: none;
  box-sizing: border-box;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  text-transform: uppercase;
  letter-spacing: 0.5px;

  &:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    background: linear-gradient(135deg, #ff9d5c 0%, #8bc556 100%); // Cam nhạt đến xanh lá nhạt
  }

  &:active {
    transform: translateY(0);
  }

  &:focus {
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.3);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    background: rgba(255, 255, 255, 0.1);
  }
}

// Auth Toggle - Improved layout and colors
.authToggle {
  text-align: center;
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: none !important;

  p {
    color: rgba(255, 255, 255, 0.9); // Màu trắng sáng hơn
    font-size: 0.9rem;
    margin: 0 0 0.75rem 0;
    font-weight: 400;
  }
}

.toggleButton {
  background: none;
  border: none;
  color: #FF8C42; // Cam chính
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  text-decoration: underline;
  transition: all 0.2s ease;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;

  &:hover {
    color: #7CB342; // Xanh lá khi hover
    background: rgba(255, 255, 255, 0.05);
    text-decoration: none;
  }

  &:active {
    transform: translateY(1px);
  }
}

// Loading Indicator
.loadingIndicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  justify-content: center;
}

.spinner {
  width: 16px;
  height: 16px;
  border: none !important;
  background: conic-gradient(from 0deg, transparent, white);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

// Error Message
.errorMessage {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #ef4444;
  font-size: 0.875rem;
  margin-top: 0.5rem;
}

// Animations
@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }

  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes gradientShift {

  0%,
  100% {
    transform: rotate(0deg) scale(1);
  }

  33% {
    transform: rotate(120deg) scale(1.1);
  }

  66% {
    transform: rotate(240deg) scale(0.9);
  }
}

.content {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2.5rem;
  position: relative;
  z-index: 1;
}

// Removed duplicate formContainer class

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Modern sophisticated form header
.formHeader {
  text-align: center;
  margin-bottom: 2.5rem;
  position: relative;

  .title {
    font-size: 2.5rem;
    font-weight: 700;
    background: linear-gradient(135deg,
        var(--gaming-text) 0%,
        var(--gaming-primary) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.75rem;
    line-height: 1.2;
    letter-spacing: -0.01em;
    animation: subtleGlow 3s ease-in-out infinite;
  }

  .subtitle {
    color: var(--gaming-text-muted);
    font-size: 1.1rem;
    font-weight: 400;
    line-height: 1.6;
    max-width: 380px;
    margin: 0 auto;
    opacity: 0.9;
  }

  // Minimal decorative accent
  &::before {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 1px;
    background: linear-gradient(90deg,
        transparent 0%,
        var(--gaming-primary) 50%,
        transparent 100%);
    opacity: 0.5;
  }
}

@keyframes titleGlow {
  from {
    filter: brightness(1);
  }

  to {
    filter: brightness(1.2);
  }
}

// Modern input group styles
.inputGroup {
  position: relative;
  margin-bottom: 1.5rem;

  .inputWrapper {
    position: relative;

    .modernInput {
      width: 100%;
      padding: 1rem 1.25rem;
      background: rgba(45, 45, 74, 0.6);
      border: none !important;
      border-radius: 8px;
      color: var(--gaming-text);
      font-size: 1rem;
      font-weight: 400;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      box-sizing: border-box;
      backdrop-filter: blur(10px);

      &::placeholder {
        color: var(--gaming-text-secondary);
        font-weight: 400;
      }

      &:focus {
        outline: none;
        border: none !important;
        background: rgba(45, 45, 74, 0.8);
        box-shadow: none !important;
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }

      &.error {
        border: none !important;
        background: rgba(217, 83, 79, 0.1);
        box-shadow: none !important;
      }
    }

    .inputIcon {
      position: absolute;
      right: 1.2rem;
      top: 50%;
      transform: translateY(-50%);
      color: #a0aec0;
      pointer-events: none;
      font-size: 1.2rem;
      transition: color 0.3s ease;
    }
  }

  .errorMessage {
    color: #ff6b6b;
    font-size: 0.875rem;
    margin-top: 0.5rem;
    margin-left: 0.25rem;
    animation: shake 0.5s ease-in-out;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
}

@keyframes shake {

  0%,
  100% {
    transform: translateX(0);
  }

  25% {
    transform: translateX(-5px);
  }

  75% {
    transform: translateX(5px);
  }
}

// Modern sophisticated button styles
.modernButton {
  width: 100%;
  padding: 1rem 2rem;
  background: linear-gradient(135deg, var(--gaming-primary) 0%, var(--gaming-secondary) 100%);
  border: none;
  border-radius: 8px;
  color: var(--gaming-text);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 12px rgba(74, 144, 226, 0.2);
  letter-spacing: 0.01em;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent);
    transition: left 0.5s;
  }

  &:hover {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);

    &::before {
      left: 100%;
    }
  }

  &:active {
    transform: translateY(0);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;

    &:hover {
      transform: none;
      box-shadow: none;

      &::before {
        left: -100%;
      }
    }
  }
}

.secondaryButton {
  background: transparent;
  border: none !important;
  color: rgba(0, 0, 0, 0.8);

  &:hover {
    background: rgba(0, 0, 0, 0.05);
    border: none !important;
    box-shadow: none !important;
  }
}

// Loading indicator styles
.loadingIndicator {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;

  .spinner {
    width: 1rem;
    height: 1rem;
    border: none !important;
    background: conic-gradient(from 0deg, transparent, white);
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

// Form footer styles - Improved layout and colors
.formFooter {
  text-align: center;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: none !important;

  .linkText {
    color: rgba(255, 255, 255, 0.9); // Màu trắng sáng
    font-size: 0.9rem;
    margin: 0 0 1rem 0;
    font-weight: 400;
  }

  .linkButton {
    color: #FF8C42; // Cam chính
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;

    &:hover {
      color: #7CB342; // Xanh lá khi hover
      background: rgba(255, 255, 255, 0.05);
      text-decoration: underline;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .root {
    width: 100%;
    height: 100vh;
    border-radius: 0;
  }

  .content {
    padding: ui.offset('large');
  }

  // Removed duplicate formContainer class

  .formHeader {
    margin-bottom: 2rem;

    .title {
      font-size: 2rem;
    }

    .subtitle {
      font-size: 0.9rem;
    }
  }

  .inputGroup {
    margin-bottom: 1.25rem;
  }

  .modernButton {
    padding: 0.875rem 1.5rem;
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .content {
    padding: ui.offset('normal');
  }

  .formHeader {
    .title {
      font-size: 1.75rem;
    }
  }

  .modernInput {
    padding: 0.875rem 1rem;
    font-size: 0.9rem;
  }
}

// Accessibility improvements
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .root::before {
    animation: none;
  }

  .formHeader .title {
    animation: none;
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .modernInput {
    border: none !important;
    background: rgba(0, 0, 0, 0.3);

    &:focus {
      border: none !important;
      box-shadow: none !important;
    }
  }

  .modernButton {
    border: none !important;
  }

  .errorMessage {
    background: rgba(255, 0, 0, 0.2);
    padding: 0.5rem;
    border-radius: 8px;
  }
}