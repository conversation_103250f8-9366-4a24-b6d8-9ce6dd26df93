//  Copyright <PERSON> 2008.
//  Use, modification and distribution are subject to the
//  Boost Software License, Version 1.0. (See accompanying file
//  LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

#include <boost/math/tools/config.hpp>

#ifdef BOOST_MATH_NO_LONG_DOUBLE_MATH_FUNCTIONS
#error "long double support is not supported by Boost.Math on this Platform: the long double version of the TR1 library will not be built."
#endif
