# Copyright 2014 The Chromium Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//build/config/android/config.gni")
import("//build/config/sysroot.gni")

assert(is_android)

shared_library("chromium_android_linker") {
  sources = [
    "legacy_linker_jni.cc",
    "legacy_linker_jni.h",
    "linker_jni.cc",
    "linker_jni.h",
    "modern_linker_jni.cc",
    "modern_linker_jni.h",
  ]

  # The NDK contains the crazy_linker here:
  #   '<(android_ndk_root)/crazy_linker.gyp:crazy_linker'
  # However, we use our own fork.  See bug 384700.
  deps = [
    "//build:buildflag_header_h",
    "//third_party/android_crazy_linker",
  ]

  # Export JNI symbols.
  configs -= [ "//build/config/android:hide_all_but_jni_onload" ]
  configs += [ "//build/config/android:hide_all_but_jni" ]

  # Disable coverage to avoid linker issue.
  configs -= [ "//build/config/coverage:default_coverage" ]

  # Avoid linking libc++ and support libraries, to avoid 100 kiB of
  # un-necessary code.
  no_default_deps = true

  # The linker is used on Android platforms that do not support GNU-style
  # hash tables, so ensure one isn't included in it to save space (since the SysV
  # format is always supported). It would be nice to also remove the GNU version
  # tables, for the same reason, but a linker flag to disable them doesn't seem
  # to exist. This saves 52 bytes on ARM.
  ldflags = [ "-Wl,--hash-style=sysv" ]
}
