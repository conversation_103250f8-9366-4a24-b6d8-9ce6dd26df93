name: CI

on:
  pull_request:
  push:
    branches:
      - master
      - develop
      - feature/**

env:
  UBSAN_OPTIONS: print_stacktrace=1

jobs:
  posix:
    strategy:
      fail-fast: false
      matrix:
        include:
          - toolset: gcc-4.8
            cxxstd: "11"
            os: ubuntu-latest
            container: ubuntu:18.04
            install: g++-4.8-multilib
            address-model: 32,64
          - toolset: gcc-5
            cxxstd: "11,14,1z"
            os: ubuntu-latest
            container: ubuntu:18.04
            install: g++-5-multilib
            address-model: 32,64
          - toolset: gcc-6
            cxxstd: "11,14,1z"
            os: ubuntu-latest
            container: ubuntu:18.04
            install: g++-6-multilib
            address-model: 32,64
          - toolset: gcc-7
            cxxstd: "11,14,17"
            os: ubuntu-20.04
            install: g++-7-multilib
            address-model: 32,64
          - toolset: gcc-8
            cxxstd: "11,14,17,2a"
            os: ubuntu-20.04
            install: g++-8-multilib
            address-model: 32,64
          - toolset: gcc-9
            cxxstd: "11,14,17,2a"
            os: ubuntu-20.04
            install: g++-9-multilib
            address-model: 32,64
          - toolset: gcc-10
            cxxstd: "11,14,17,2a"
            os: ubuntu-22.04
            install: g++-10-multilib
            address-model: 32,64
          - toolset: gcc-11
            cxxstd: "11,14,17,20"
            os: ubuntu-22.04
            install: g++-11-multilib
            address-model: 32,64
          - toolset: gcc-12
            cxxstd: "11,14,17,20,2b"
            os: ubuntu-22.04
            install: g++-12-multilib
            address-model: 32,64
          - toolset: gcc-13
            cxxstd: "11,14,17,20,2b"
            os: ubuntu-latest
            container: ubuntu:23.04
            install: g++-13-multilib
            address-model: 32,64
          - toolset: clang
            compiler: clang++-3.9
            cxxstd: "11,14"
            os: ubuntu-latest
            container: ubuntu:18.04
            install: clang-3.9
          - toolset: clang
            compiler: clang++-4.0
            cxxstd: "11,14"
            os: ubuntu-latest
            container: ubuntu:18.04
            install: clang-4.0
          - toolset: clang
            compiler: clang++-5.0
            cxxstd: "11,14,1z"
            os: ubuntu-latest
            container: ubuntu:18.04
            install: clang-5.0
          - toolset: clang
            compiler: clang++-6.0
            cxxstd: "11,14,17"
            os: ubuntu-20.04
            install: clang-6.0
          - toolset: clang
            compiler: clang++-7
            cxxstd: "11,14,17"
            os: ubuntu-20.04
            install: clang-7
          - toolset: clang
            compiler: clang++-8
            cxxstd: "11,14,17"
            os: ubuntu-20.04
            install: clang-8
          - toolset: clang
            compiler: clang++-9
            cxxstd: "11,14,17,2a"
            os: ubuntu-20.04
            install: clang-9
          - toolset: clang
            compiler: clang++-10
            cxxstd: "11,14,17,2a"
            os: ubuntu-20.04
          - toolset: clang
            compiler: clang++-11
            cxxstd: "11,14,17,2a"
            os: ubuntu-20.04
          - toolset: clang
            compiler: clang++-12
            cxxstd: "11,14,17,20"
            os: ubuntu-20.04
          - toolset: clang
            compiler: clang++-13
            cxxstd: "11,14,17,20,2b"
            container: ubuntu:22.04
            os: ubuntu-latest
            install: clang-13
          - toolset: clang
            compiler: clang++-14
            cxxstd: "11,14,17,20,2b"
            container: ubuntu:22.04
            os: ubuntu-latest
            install: clang-14
          - toolset: clang
            compiler: clang++-15
            cxxstd: "11,14,17,20,2b"
            container: ubuntu:22.04
            os: ubuntu-latest
            install: clang-15
          - toolset: clang
            compiler: clang++-16
            cxxstd: "11,14,17,20,2b"
            container: ubuntu:23.04
            os: ubuntu-latest
            install: clang-16
          - toolset: clang
            compiler: clang++-17
            cxxstd: "11,14,17,20,2b"
            container: ubuntu:23.10
            os: ubuntu-latest
            install: clang-17
          - toolset: clang
            cxxstd: "11,14,17,2a"
            os: macos-11
          - toolset: clang
            cxxstd: "11,14,17,20,2b"
            os: macos-12
          - toolset: clang
            cxxstd: "11,14,17,20,2b"
            os: macos-13

    runs-on: ${{matrix.os}}
    container: ${{matrix.container}}

    defaults:
      run:
        shell: bash

    steps:
      - uses: actions/checkout@v3

      - name: Setup container environment
        if: matrix.container
        run: |
          apt-get update
          apt-get -y install sudo python3 git g++

      - name: Install packages
        if: matrix.install
        run: |
          sudo apt-get update
          sudo apt-get -y install ${{matrix.install}}

      - name: Setup Boost
        run: |
          echo GITHUB_REPOSITORY: $GITHUB_REPOSITORY
          LIBRARY=${GITHUB_REPOSITORY#*/}
          echo LIBRARY: $LIBRARY
          echo "LIBRARY=$LIBRARY" >> $GITHUB_ENV
          echo GITHUB_BASE_REF: $GITHUB_BASE_REF
          echo GITHUB_REF: $GITHUB_REF
          REF=${GITHUB_BASE_REF:-$GITHUB_REF}
          REF=${REF#refs/heads/}
          echo REF: $REF
          BOOST_BRANCH=develop && [ "$REF" == "master" ] && BOOST_BRANCH=master || true
          echo BOOST_BRANCH: $BOOST_BRANCH
          cd ..
          git clone -b $BOOST_BRANCH --depth 1 https://github.com/boostorg/boost.git boost-root
          cd boost-root
          cp -r $GITHUB_WORKSPACE/* libs/$LIBRARY
          git submodule update --init tools/boostdep
          python3 tools/boostdep/depinst/depinst.py -I examples --git_args "--jobs 3" $LIBRARY
          ./bootstrap.sh
          ./b2 -d0 headers

      - name: Create user-config.jam
        if: matrix.compiler
        run: |
          echo "using ${{matrix.toolset}} : : ${{matrix.compiler}} ;" > ~/user-config.jam

      - name: Run tests
        run: |
          cd ../boost-root
          export ADDRMD=${{matrix.address-model}}
          ./b2 -j3 --verbose-test libs/$LIBRARY/test//hash_info toolset=${{matrix.toolset}} cxxstd=${{matrix.cxxstd}} ${ADDRMD:+address-model=$ADDRMD} variant=debug,release
          ./b2 -j3 libs/$LIBRARY/test toolset=${{matrix.toolset}} cxxstd=${{matrix.cxxstd}} ${ADDRMD:+address-model=$ADDRMD} variant=debug,release

  windows:
    strategy:
      fail-fast: false
      matrix:
        include:
          - toolset: msvc-14.0
            cxxstd: 14,latest
            addrmd: 32,64
            os: windows-2019
          - toolset: msvc-14.2
            cxxstd: "14,17,20,latest"
            addrmd: 32,64
            os: windows-2019
          - toolset: msvc-14.3
            cxxstd: "14,17,20,latest"
            addrmd: 32,64
            os: windows-2022
          - toolset: clang-win
            cxxstd: "14,17,latest"
            addrmd: 32,64
            os: windows-2022
          - toolset: gcc
            cxxstd: "11,14,17,2a"
            addrmd: 64
            os: windows-2019

    runs-on: ${{matrix.os}}

    steps:
      - uses: actions/checkout@v3

      - name: Setup Boost
        shell: cmd
        run: |
          echo GITHUB_REPOSITORY: %GITHUB_REPOSITORY%
          for /f %%i in ("%GITHUB_REPOSITORY%") do set LIBRARY=%%~nxi
          echo LIBRARY: %LIBRARY%
          echo LIBRARY=%LIBRARY%>>%GITHUB_ENV%
          echo GITHUB_BASE_REF: %GITHUB_BASE_REF%
          echo GITHUB_REF: %GITHUB_REF%
          if "%GITHUB_BASE_REF%" == "" set GITHUB_BASE_REF=%GITHUB_REF%
          set BOOST_BRANCH=develop
          for /f %%i in ("%GITHUB_BASE_REF%") do if "%%~nxi" == "master" set BOOST_BRANCH=master
          echo BOOST_BRANCH: %BOOST_BRANCH%
          cd ..
          git clone -b %BOOST_BRANCH% --depth 1 https://github.com/boostorg/boost.git boost-root
          cd boost-root
          xcopy /s /e /q %GITHUB_WORKSPACE% libs\%LIBRARY%\
          git submodule update --init tools/boostdep
          python tools/boostdep/depinst/depinst.py -I examples --git_args "--jobs 3" %LIBRARY%
          cmd /c bootstrap
          b2 -d0 headers

      - name: Run tests
        shell: cmd
        run: |
          cd ../boost-root
          b2 -j3 --verbose-test libs/%LIBRARY%/test//hash_info toolset=${{matrix.toolset}} cxxstd=${{matrix.cxxstd}} address-model=${{matrix.addrmd}} variant=debug,release embed-manifest-via=linker
          b2 -j3 libs/%LIBRARY%/test toolset=${{matrix.toolset}} cxxstd=${{matrix.cxxstd}} address-model=${{matrix.addrmd}} variant=debug,release embed-manifest-via=linker

  posix-cmake-subdir:
    strategy:
      fail-fast: false
      matrix:
        include:
          - os: ubuntu-20.04
          - os: ubuntu-22.04
          - os: macos-11
          - os: macos-12
          - os: macos-13

    runs-on: ${{matrix.os}}

    steps:
      - uses: actions/checkout@v3

      - name: Install packages
        if: matrix.install
        run: sudo apt-get -y install ${{matrix.install}}

      - name: Setup Boost
        run: |
          echo GITHUB_REPOSITORY: $GITHUB_REPOSITORY
          LIBRARY=${GITHUB_REPOSITORY#*/}
          echo LIBRARY: $LIBRARY
          echo "LIBRARY=$LIBRARY" >> $GITHUB_ENV
          echo GITHUB_BASE_REF: $GITHUB_BASE_REF
          echo GITHUB_REF: $GITHUB_REF
          REF=${GITHUB_BASE_REF:-$GITHUB_REF}
          REF=${REF#refs/heads/}
          echo REF: $REF
          BOOST_BRANCH=develop && [ "$REF" == "master" ] && BOOST_BRANCH=master || true
          echo BOOST_BRANCH: $BOOST_BRANCH
          cd ..
          git clone -b $BOOST_BRANCH --depth 1 https://github.com/boostorg/boost.git boost-root
          cd boost-root
          cp -r $GITHUB_WORKSPACE/* libs/$LIBRARY
          git submodule update --init tools/boostdep
          python tools/boostdep/depinst/depinst.py --git_args "--jobs 3" $LIBRARY

      - name: Use library with add_subdirectory
        run: |
          cd ../boost-root/libs/$LIBRARY/test/cmake_subdir_test
          mkdir __build__ && cd __build__
          cmake ..
          cmake --build .
          ctest --output-on-failure --no-tests=error

  posix-cmake-install:
    strategy:
      fail-fast: false
      matrix:
        include:
          - os: ubuntu-20.04
          - os: ubuntu-22.04
          - os: macos-11
          - os: macos-12
          - os: macos-13

    runs-on: ${{matrix.os}}

    steps:
      - uses: actions/checkout@v3

      - name: Install packages
        if: matrix.install
        run: sudo apt-get -y install ${{matrix.install}}

      - name: Setup Boost
        run: |
          echo GITHUB_REPOSITORY: $GITHUB_REPOSITORY
          LIBRARY=${GITHUB_REPOSITORY#*/}
          echo LIBRARY: $LIBRARY
          echo "LIBRARY=$LIBRARY" >> $GITHUB_ENV
          echo GITHUB_BASE_REF: $GITHUB_BASE_REF
          echo GITHUB_REF: $GITHUB_REF
          REF=${GITHUB_BASE_REF:-$GITHUB_REF}
          REF=${REF#refs/heads/}
          echo REF: $REF
          BOOST_BRANCH=develop && [ "$REF" == "master" ] && BOOST_BRANCH=master || true
          echo BOOST_BRANCH: $BOOST_BRANCH
          cd ..
          git clone -b $BOOST_BRANCH --depth 1 https://github.com/boostorg/boost.git boost-root
          cd boost-root
          cp -r $GITHUB_WORKSPACE/* libs/$LIBRARY
          git submodule update --init tools/boostdep
          python tools/boostdep/depinst/depinst.py --git_args "--jobs 3" $LIBRARY

      - name: Configure
        run: |
          cd ../boost-root
          mkdir __build__ && cd __build__
          cmake -DBOOST_INCLUDE_LIBRARIES=$LIBRARY -DCMAKE_INSTALL_PREFIX=~/.local ..

      - name: Install
        run: |
          cd ../boost-root/__build__
          cmake --build . --target install

      - name: Use the installed library
        run: |
          cd ../boost-root/libs/$LIBRARY/test/cmake_install_test && mkdir __build__ && cd __build__
          cmake -DCMAKE_INSTALL_PREFIX=~/.local ..
          cmake --build .
          ctest --output-on-failure --no-tests=error

  posix-cmake-test:
    strategy:
      fail-fast: false
      matrix:
        include:
          - os: ubuntu-20.04
          - os: ubuntu-22.04
          - os: macos-11
          - os: macos-12
          - os: macos-13

    runs-on: ${{matrix.os}}

    steps:
      - uses: actions/checkout@v3

      - name: Install packages
        if: matrix.install
        run: sudo apt-get -y install ${{matrix.install}}

      - name: Setup Boost
        run: |
          echo GITHUB_REPOSITORY: $GITHUB_REPOSITORY
          LIBRARY=${GITHUB_REPOSITORY#*/}
          echo LIBRARY: $LIBRARY
          echo "LIBRARY=$LIBRARY" >> $GITHUB_ENV
          echo GITHUB_BASE_REF: $GITHUB_BASE_REF
          echo GITHUB_REF: $GITHUB_REF
          REF=${GITHUB_BASE_REF:-$GITHUB_REF}
          REF=${REF#refs/heads/}
          echo REF: $REF
          BOOST_BRANCH=develop && [ "$REF" == "master" ] && BOOST_BRANCH=master || true
          echo BOOST_BRANCH: $BOOST_BRANCH
          cd ..
          git clone -b $BOOST_BRANCH --depth 1 https://github.com/boostorg/boost.git boost-root
          cd boost-root
          cp -r $GITHUB_WORKSPACE/* libs/$LIBRARY
          git submodule update --init tools/boostdep
          python tools/boostdep/depinst/depinst.py --git_args "--jobs 3" $LIBRARY

      - name: Configure
        run: |
          cd ../boost-root
          mkdir __build__ && cd __build__
          cmake -DBOOST_INCLUDE_LIBRARIES=$LIBRARY -DBUILD_TESTING=ON ..

      - name: Build tests
        run: |
          cd ../boost-root/__build__
          cmake --build . --target tests

      - name: Run tests
        run: |
          cd ../boost-root/__build__
          ctest --output-on-failure --no-tests=error

  windows-cmake-subdir:
    strategy:
      fail-fast: false
      matrix:
        include:
          - os: windows-2019
          - os: windows-2022

    runs-on: ${{matrix.os}}

    steps:
      - uses: actions/checkout@v3

      - name: Setup Boost
        shell: cmd
        run: |
          echo GITHUB_REPOSITORY: %GITHUB_REPOSITORY%
          for /f %%i in ("%GITHUB_REPOSITORY%") do set LIBRARY=%%~nxi
          echo LIBRARY: %LIBRARY%
          echo LIBRARY=%LIBRARY%>>%GITHUB_ENV%
          echo GITHUB_BASE_REF: %GITHUB_BASE_REF%
          echo GITHUB_REF: %GITHUB_REF%
          if "%GITHUB_BASE_REF%" == "" set GITHUB_BASE_REF=%GITHUB_REF%
          set BOOST_BRANCH=develop
          for /f %%i in ("%GITHUB_BASE_REF%") do if "%%~nxi" == "master" set BOOST_BRANCH=master
          echo BOOST_BRANCH: %BOOST_BRANCH%
          cd ..
          git clone -b %BOOST_BRANCH% --depth 1 https://github.com/boostorg/boost.git boost-root
          cd boost-root
          xcopy /s /e /q %GITHUB_WORKSPACE% libs\%LIBRARY%\
          git submodule update --init tools/boostdep
          python tools/boostdep/depinst/depinst.py --git_args "--jobs 3" %LIBRARY%

      - name: Use library with add_subdirectory (Debug)
        shell: cmd
        run: |
          cd ../boost-root/libs/%LIBRARY%/test/cmake_subdir_test
          mkdir __build__ && cd __build__
          cmake ..
          cmake --build . --config Debug
          ctest --output-on-failure --no-tests=error -C Debug

      - name: Use library with add_subdirectory (Release)
        shell: cmd
        run: |
          cd ../boost-root/libs/%LIBRARY%/test/cmake_subdir_test/__build__
          cmake --build . --config Release
          ctest --output-on-failure --no-tests=error -C Release

  windows-cmake-install:
    strategy:
      fail-fast: false
      matrix:
        include:
          - os: windows-2019
          - os: windows-2022

    runs-on: ${{matrix.os}}

    steps:
      - uses: actions/checkout@v3

      - name: Setup Boost
        shell: cmd
        run: |
          echo GITHUB_REPOSITORY: %GITHUB_REPOSITORY%
          for /f %%i in ("%GITHUB_REPOSITORY%") do set LIBRARY=%%~nxi
          echo LIBRARY: %LIBRARY%
          echo LIBRARY=%LIBRARY%>>%GITHUB_ENV%
          echo GITHUB_BASE_REF: %GITHUB_BASE_REF%
          echo GITHUB_REF: %GITHUB_REF%
          if "%GITHUB_BASE_REF%" == "" set GITHUB_BASE_REF=%GITHUB_REF%
          set BOOST_BRANCH=develop
          for /f %%i in ("%GITHUB_BASE_REF%") do if "%%~nxi" == "master" set BOOST_BRANCH=master
          echo BOOST_BRANCH: %BOOST_BRANCH%
          cd ..
          git clone -b %BOOST_BRANCH% --depth 1 https://github.com/boostorg/boost.git boost-root
          cd boost-root
          xcopy /s /e /q %GITHUB_WORKSPACE% libs\%LIBRARY%\
          git submodule update --init tools/boostdep
          python tools/boostdep/depinst/depinst.py --git_args "--jobs 3" %LIBRARY%

      - name: Configure
        shell: cmd
        run: |
          cd ../boost-root
          mkdir __build__ && cd __build__
          cmake -DBOOST_INCLUDE_LIBRARIES=%LIBRARY% -DCMAKE_INSTALL_PREFIX=C:/cmake-prefix ..

      - name: Install (Debug)
        shell: cmd
        run: |
          cd ../boost-root/__build__
          cmake --build . --target install --config Debug

      - name: Install (Release)
        shell: cmd
        run: |
          cd ../boost-root/__build__
          cmake --build . --target install --config Release

      - name: Use the installed library (Debug)
        shell: cmd
        run: |
          cd ../boost-root/libs/%LIBRARY%/test/cmake_install_test && mkdir __build__ && cd __build__
          cmake -DCMAKE_INSTALL_PREFIX=C:/cmake-prefix ..
          cmake --build . --config Debug
          ctest --output-on-failure --no-tests=error -C Debug

      - name: Use the installed library (Release)
        shell: cmd
        run: |
          cd ../boost-root/libs/%LIBRARY%/test/cmake_install_test/__build__
          cmake --build . --config Release
          ctest --output-on-failure --no-tests=error -C Release

  windows-cmake-test:
    strategy:
      fail-fast: false
      matrix:
        include:
          - os: windows-2019
          - os: windows-2022

    runs-on: ${{matrix.os}}

    steps:
      - uses: actions/checkout@v3

      - name: Setup Boost
        shell: cmd
        run: |
          echo GITHUB_REPOSITORY: %GITHUB_REPOSITORY%
          for /f %%i in ("%GITHUB_REPOSITORY%") do set LIBRARY=%%~nxi
          echo LIBRARY: %LIBRARY%
          echo LIBRARY=%LIBRARY%>>%GITHUB_ENV%
          echo GITHUB_BASE_REF: %GITHUB_BASE_REF%
          echo GITHUB_REF: %GITHUB_REF%
          if "%GITHUB_BASE_REF%" == "" set GITHUB_BASE_REF=%GITHUB_REF%
          set BOOST_BRANCH=develop
          for /f %%i in ("%GITHUB_BASE_REF%") do if "%%~nxi" == "master" set BOOST_BRANCH=master
          echo BOOST_BRANCH: %BOOST_BRANCH%
          cd ..
          git clone -b %BOOST_BRANCH% --depth 1 https://github.com/boostorg/boost.git boost-root
          cd boost-root
          xcopy /s /e /q %GITHUB_WORKSPACE% libs\%LIBRARY%\
          git submodule update --init tools/boostdep
          python tools/boostdep/depinst/depinst.py --git_args "--jobs 3" %LIBRARY%

      - name: Configure
        shell: cmd
        run: |
          cd ../boost-root
          mkdir __build__ && cd __build__
          cmake -DBOOST_INCLUDE_LIBRARIES=%LIBRARY% -DBUILD_TESTING=ON ..

      - name: Build tests (Debug)
        shell: cmd
        run: |
          cd ../boost-root/__build__
          cmake --build . --target tests --config Debug

      - name: Run tests (Debug)
        shell: cmd
        run: |
          cd ../boost-root/__build__
          ctest --output-on-failure --no-tests=error -C Debug

      - name: Build tests (Release)
        shell: cmd
        run: |
          cd ../boost-root/__build__
          cmake --build . --target tests --config Release

      - name: Run tests (Release)
        shell: cmd
        run: |
          cd ../boost-root/__build__
          ctest --output-on-failure --no-tests=error -C Release
