<math  xmlns="http://www.w3.org/1998/Math/MathML" display="block" ><mrow >                        <msub><mrow ><mi >R</mi></mrow><mrow ><mi >C</mi></mrow></msub ><mrow ><mo class="MathClass-open">(</mo><mrow><mi >x</mi><mo class="MathClass-punc">,</mo><mi >y</mi></mrow><mo class="MathClass-close">)</mo></mrow> <mo class="MathClass-rel">=</mo> <mfrac><mrow ><mn>1</mn></mrow> <mrow ><mn>2</mn></mrow></mfrac><msubsup><mrow ><mo class="MathClass-op"> &#x222B; <!--nolimits--></mo><!--nolimits--></mrow><mrow ><mn>0</mn></mrow><mrow ><mi >&#x221E;</mi></mrow></msubsup ><msup><mrow ><mrow ><mo class="MathClass-open">(</mo><mrow><mi >t</mi> <mo class="MathClass-bin">+</mo> <mi >x</mi></mrow><mo class="MathClass-close">)</mo></mrow></mrow><mrow ><mo class="MathClass-bin">&#x2212;</mo><mfrac><mrow ><mn>1</mn></mrow><mrow ><mn>2</mn></mrow></mfrac> </mrow></msup ><msup><mrow ><mrow ><mo class="MathClass-open">(</mo><mrow><mi >t</mi> <mo class="MathClass-bin">+</mo> <mi >y</mi></mrow><mo class="MathClass-close">)</mo></mrow></mrow><mrow ><mo class="MathClass-bin">&#x2212;</mo><mn>1</mn></mrow></msup ><mi >d</mi><mi >t</mi></mrow></math>