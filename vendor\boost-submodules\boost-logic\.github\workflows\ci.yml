# Copyright 2020-2021 <PERSON>
# Copyright 2021 <PERSON><PERSON>
# Copyright 2021 <PERSON>
# Copyright 2022 <PERSON> III
#
# Distributed under the Boost Software License, Version 1.0.
# (See accompanying file LICENSE_1_0.txt or copy at http://boost.org/LICENSE_1_0.txt)
---
name: CI

on:
  pull_request:
  push:
    branches:
      - master
      - develop
      - bugfix/**
      - feature/**
      - fix/**
      - pr/**

concurrency:
  group: ${{format('{0}:{1}', github.repository, github.ref)}}
  cancel-in-progress: true

env:
  SELF: logic
  ASAN_OPTIONS: allocator_may_return_null=1
  GIT_FETCH_JOBS: 8
  NET_RETRY_COUNT: 5
  B2_CI_VERSION: 1
  B2_VARIANT: debug,release
  B2_LINK: shared,static
  LCOV_BRANCH_COVERAGE: 0
  CODECOV_NAME: Github Actions

jobs:
  posix:
    defaults:
      run:
        shell: bash

    strategy:
      fail-fast: false
      matrix:
        include:
          # Linux, gcc
          - { compiler: gcc-4.4,   cxxstd: '98,0x',          os: ubuntu-20.04, container: 'ubuntu:16.04' }
          - { compiler: gcc-4.6,   cxxstd: '03,0x',          os: ubuntu-20.04, container: 'ubuntu:16.04' }
          - { compiler: gcc-4.7,   cxxstd: '03,11',          os: ubuntu-20.04, container: 'ubuntu:16.04' }
          - { compiler: gcc-4.8,   cxxstd: '03,11',          os: ubuntu-18.04 }
          - { compiler: gcc-4.9,   cxxstd: '03,11',          os: ubuntu-20.04, container: 'ubuntu:16.04' }
          - { compiler: gcc-5,     cxxstd: '03,11,14,1z',    os: ubuntu-18.04 }
          - { compiler: gcc-6,     cxxstd: '03,11,14,17',    os: ubuntu-18.04 }
          - { compiler: gcc-7,     cxxstd: '03,11,14,17',    os: ubuntu-18.04 }
          - { compiler: gcc-8,     cxxstd: '03,11,14,17,2a', os: ubuntu-18.04 }
          - { compiler: gcc-9,     cxxstd: '03,11,14,17,2a', os: ubuntu-18.04 }
          - { compiler: gcc-10,    cxxstd: '03,11,14,17,20', os: ubuntu-20.04 }
          - { compiler: gcc-11,    cxxstd: '03,11,14,17,20', os: ubuntu-20.04 }
          - { name: GCC w/ sanitizers, sanitize: yes,
              compiler: gcc-11,    cxxstd: '03,11,14,17,20', os: ubuntu-20.04 }
          - { name: Collect coverage, coverage: yes,
              compiler: gcc-10,    cxxstd: '03,11',          os: ubuntu-20.04, install: 'g++-10-multilib', address-model: '32,64' }

          # Linux, clang
          - { compiler: clang-3.5, cxxstd: '03,11',          os: ubuntu-20.04, container: 'ubuntu:16.04' }
          - { compiler: clang-3.6, cxxstd: '03,11,14',       os: ubuntu-20.04, container: 'ubuntu:16.04' }
          - { compiler: clang-3.7, cxxstd: '03,11,14',       os: ubuntu-20.04, container: 'ubuntu:16.04' }
          - { compiler: clang-3.8, cxxstd: '03,11,14',       os: ubuntu-20.04, container: 'ubuntu:16.04' }
          - { compiler: clang-3.9, cxxstd: '03,11,14',       os: ubuntu-18.04 }
          - { compiler: clang-4.0, cxxstd: '03,11,14',       os: ubuntu-18.04 }
          - { compiler: clang-5.0, cxxstd: '03,11,14,1z',    os: ubuntu-18.04 }
          - { compiler: clang-6.0, cxxstd: '03,11,14,17',    os: ubuntu-18.04 }
          - { compiler: clang-7,   cxxstd: '03,11,14,17',    os: ubuntu-18.04 }
          # Note: clang-8 does not fully support C++20, so it is not compatible with some libstdc++ versions in this mode
          - { compiler: clang-8,   cxxstd: '03,11,14,17,2a', os: ubuntu-18.04, install: 'clang-8 g++-7', gcc_toolchain: 7 }
          - { compiler: clang-9,   cxxstd: '03,11,14,17,2a', os: ubuntu-20.04 }
          - { compiler: clang-10,  cxxstd: '03,11,14,17,20', os: ubuntu-20.04 }
          - { compiler: clang-11,  cxxstd: '03,11,14,17,20', os: ubuntu-20.04 }
          - { name: Clang w/ valgrind, valgrind: yes,
              compiler: clang-12,  cxxstd: '03,11,14,17,20', os: ubuntu-20.04, install: 'valgrind' }

          # libc++
          - { compiler: clang-6.0, cxxstd: '03,11,14',       os: ubuntu-18.04, stdlib: libc++, install: 'clang-6.0 libc++-dev libc++abi-dev' }
          - { compiler: clang-12,  cxxstd: '03,11,14,17,20', os: ubuntu-20.04, stdlib: libc++, install: 'clang-12 libc++-12-dev libc++abi-12-dev' }
          - { name: Clang w/ sanitizers, sanitize: yes,
              compiler: clang-12,  cxxstd: '03,11,14,17,20', os: ubuntu-20.04, stdlib: libc++, install: 'clang-12 libc++-12-dev libc++abi-12-dev' }

          # OSX, clang
          - { compiler: clang,     cxxstd: '03,11,14,17,2a', os: macos-11, sanitize: yes }

          # Coverity Scan
          # requires two github secrets in repo to activate; see ci/github/coverity.sh
          # does not run on pull requests, only on pushes into develop and master
          - { name: Coverity, coverity: yes,
              compiler: clang-10,  cxxstd: '17',             os: ubuntu-20.04, ccache: no }

          # multiarch (bigendian testing) - does not support coverage yet
          - { name: Big-endian, multiarch: yes,
              compiler: clang,     cxxstd: '17',             os: ubuntu-20.04, ccache: no, distro: fedora, edition: 34, arch: s390x }


    timeout-minutes: 120
    runs-on: ${{matrix.os}}
    container: ${{matrix.container}}
    env: {B2_USE_CCACHE: 1}

    steps:
      - name: Setup environment
        run: |
            if [ -f "/etc/debian_version" ]; then
                echo "DEBIAN_FRONTEND=noninteractive" >> $GITHUB_ENV
                export DEBIAN_FRONTEND=noninteractive
            fi
            if [ -n "${{matrix.container}}" ] && [ -f "/etc/debian_version" ]; then
                apt-get -o Acquire::Retries=$NET_RETRY_COUNT update
                apt-get -o Acquire::Retries=$NET_RETRY_COUNT install -y sudo software-properties-common
                # Need (newer) git
                for i in {1..${NET_RETRY_COUNT:-3}}; do sudo -E add-apt-repository -y ppa:git-core/ppa && break || sleep 10; done
                apt-get -o Acquire::Retries=$NET_RETRY_COUNT update
                apt-get -o Acquire::Retries=$NET_RETRY_COUNT install -y g++ python libpython-dev git
            fi
            # multiple job types are not compatible with ccache, they use "ccache: no" in the matrix
            if [[ "${{ matrix.ccache }}" == "no" ]]; then
                echo "B2_USE_CCACHE=0" >> $GITHUB_ENV
            fi
            if [[ "${{ matrix.valgrind }}" == "yes" ]]; then
                echo "B2_DEFINES=BOOST_NO_STRESS_TEST=1" >> $GITHUB_ENV
                echo "B2_TESTFLAGS=testing.launcher=valgrind" >> $GITHUB_ENV
                echo "B2_FLAGS=define=BOOST_USE_VALGRIND=1" >> $GITHUB_ENV
                echo "VALGRIND_OPTS=--error-exitcode=1" >> $GITHUB_ENV
            fi
            git config --global pack.threads 0

      - uses: actions/checkout@v3
        with:
          # For coverage builds fetch the whole history, else only 1 commit using a 'fake ternary'
          fetch-depth: ${{ matrix.coverage && '0' || '1' }}

      - name: Cache ccache
        uses: actions/cache@v3
        if: env.B2_USE_CCACHE
        with:
          path: ~/.ccache
          key: ${{matrix.os}}-${{matrix.container}}-${{matrix.compiler}}-${{github.sha}}
          restore-keys: |
            ${{matrix.os}}-${{matrix.container}}-${{matrix.compiler}}-
            ${{matrix.os}}-${{matrix.container}}-${{matrix.compiler}}

      - name: Fetch Boost.CI
        uses: actions/checkout@v3
        with:
          repository: boostorg/boost-ci
          ref: master
          path: boost-ci-cloned

      - name: Get CI scripts folder
        run: |
            # Copy ci folder if not testing Boost.CI
            [[ "$GITHUB_REPOSITORY" =~ "boost-ci" ]] || cp -r boost-ci-cloned/ci .
            rm -rf boost-ci-cloned

      - name: Install packages
        if: startsWith(matrix.os, 'ubuntu')
        run: |
            SOURCE_KEYS=(${{join(matrix.source_keys, ' ')}})
            SOURCES=(${{join(matrix.sources, ' ')}})
            # Add this by default
            SOURCES+=(ppa:ubuntu-toolchain-r/test)
            for key in "${SOURCE_KEYS[@]}"; do
                for i in {1..$NET_RETRY_COUNT}; do
                    wget -O - "$key" | sudo apt-key add - && break || sleep 10
                done
            done
            for source in "${SOURCES[@]}"; do
                for i in {1..$NET_RETRY_COUNT}; do
                    sudo add-apt-repository $source && break || sleep 10
                done
            done
            sudo apt-get -o Acquire::Retries=$NET_RETRY_COUNT update
            if [[ -z "${{matrix.install}}" ]]; then
                pkgs="${{matrix.compiler}}"
                pkgs="${pkgs/gcc-/g++-}"
            else
                pkgs="${{matrix.install}}"
            fi
            sudo apt-get -o Acquire::Retries=$NET_RETRY_COUNT install -y $pkgs

      - name: Setup GCC Toolchain
        if: matrix.gcc_toolchain
        run: |
            GCC_TOOLCHAIN_ROOT="$HOME/gcc-toolchain"
            echo "GCC_TOOLCHAIN_ROOT=$GCC_TOOLCHAIN_ROOT" >> $GITHUB_ENV
            MULTIARCH_TRIPLET="$(dpkg-architecture -qDEB_HOST_MULTIARCH)"
            mkdir -p "$GCC_TOOLCHAIN_ROOT"
            ln -s /usr/include "$GCC_TOOLCHAIN_ROOT/include"
            ln -s /usr/bin "$GCC_TOOLCHAIN_ROOT/bin"
            mkdir -p "$GCC_TOOLCHAIN_ROOT/lib/gcc/$MULTIARCH_TRIPLET"
            ln -s "/usr/lib/gcc/$MULTIARCH_TRIPLET/${{matrix.gcc_toolchain}}" "$GCC_TOOLCHAIN_ROOT/lib/gcc/$MULTIARCH_TRIPLET/${{matrix.gcc_toolchain}}"

      - name: Setup multiarch
        if: matrix.multiarch
        run: |
          sudo apt-get install --no-install-recommends -y binfmt-support qemu-user-static
          sudo docker run --rm --privileged multiarch/qemu-user-static --reset -p yes
          git clone https://github.com/jeking3/bdde.git
          echo "$(pwd)/bdde/bin/linux" >> ${GITHUB_PATH}
          echo "BDDE_DISTRO=${{ matrix.distro }}" >> ${GITHUB_ENV}
          echo "BDDE_EDITION=${{ matrix.edition }}" >> ${GITHUB_ENV}
          echo "BDDE_ARCH=${{ matrix.arch }}" >> ${GITHUB_ENV}
          echo "B2_DEFINES=BOOST_NO_STRESS_TEST=1" >> ${GITHUB_ENV}
          echo "B2_WRAPPER=bdde" >> ${GITHUB_ENV}

      - name: Setup Boost
        env:
          B2_ADDRESS_MODEL: ${{matrix.address-model}}
          B2_COMPILER: ${{matrix.compiler}}
          B2_CXXSTD: ${{matrix.cxxstd}}
          B2_SANITIZE: ${{matrix.sanitize}}
          B2_STDLIB: ${{matrix.stdlib}}
        run: source ci/github/install.sh

      - name: Setup coverage collection
        if: matrix.coverage
        run: ci/github/codecov.sh "setup"

      - name: Run tests
        if: '!matrix.coverity'
        run: ci/build.sh

      - name: Upload coverage
        if: matrix.coverage
        run: ci/codecov.sh "upload"

      - name: Run coverity
        if: matrix.coverity && github.event_name == 'push' && (github.ref_name == 'develop' || github.ref_name == 'master')
        run: ci/github/coverity.sh
        env:
          COVERITY_SCAN_NOTIFICATION_EMAIL: ${{ secrets.COVERITY_SCAN_NOTIFICATION_EMAIL }}
          COVERITY_SCAN_TOKEN: ${{ secrets.COVERITY_SCAN_TOKEN }}

  windows:
    defaults:
      run:
        shell: cmd
    strategy:
      fail-fast: false
      matrix:
        include:
          - { toolset: msvc-14.2, cxxstd: '14,17,20',       addrmd: '32,64', os: windows-2019 }
          - { name: Collect coverage, coverage: yes,
              toolset: msvc-14.3, cxxstd: '14,17,20',       addrmd: '32,64', os: windows-2022 }
          - { toolset: gcc,       cxxstd: '03,11,14,17,2a', addrmd: '64',    os: windows-2019 }

    runs-on: ${{matrix.os}}

    steps:
      - uses: actions/checkout@v2

      - name: Fetch Boost.CI
        uses: actions/checkout@v2
        with:
          repository: boostorg/boost-ci
          ref: master
          path: boost-ci-cloned
      - name: Get CI scripts folder
        run: |
            REM Copy ci folder if not testing Boost.CI
            if "%GITHUB_REPOSITORY%" == "%GITHUB_REPOSITORY:boost-ci=%" xcopy /s /e /q /i /y boost-ci-cloned\ci .\ci
            rmdir /s /q boost-ci-cloned

      - name: Setup Boost
        run: ci\github\install.bat

      - name: Run tests
        if: '!matrix.coverage'
        run: ci\build.bat
        env:
          B2_TOOLSET: ${{matrix.toolset}}
          B2_CXXSTD: ${{matrix.cxxstd}}
          B2_ADDRESS_MODEL: ${{matrix.addrmd}}

      - name: Collect coverage
        shell: powershell
        if: matrix.coverage
        run: ci\opencppcoverage.ps1
        env:
          B2_TOOLSET: ${{matrix.toolset}}
          B2_CXXSTD: ${{matrix.cxxstd}}
          B2_ADDRESS_MODEL: ${{matrix.addrmd}}

      - name: Upload coverage
        if: matrix.coverage
        uses: codecov/codecov-action@v2
        with:
          files: __out/cobertura.xml

  CMake:
    defaults:
      run:
        shell: bash

    strategy:
      fail-fast: false
      matrix:
        include:
          - { os: ubuntu-20.04, build_shared: ON,  build_type: Release, generator: 'Unix Makefiles' }
          - { os: ubuntu-20.04, build_shared: OFF, build_type: Debug, generator: 'Unix Makefiles' }
          - { os: windows-2019, build_shared: ON,  build_type: Release, generator: 'Visual Studio 16 2019' }
          - { os: windows-2019, build_shared: OFF, build_type: Debug, generator: 'Visual Studio 16 2019' }

    timeout-minutes: 120
    runs-on: ${{matrix.os}}

    steps:
      - uses: actions/checkout@v2

      - name: Fetch Boost.CI
        uses: actions/checkout@v2
        with:
          repository: boostorg/boost-ci
          ref: master
          path: boost-ci-cloned

      - name: Get CI scripts folder
        run: |
            # Copy ci folder if not testing Boost.CI
            [[ "$GITHUB_REPOSITORY" =~ "boost-ci" ]] || cp -r boost-ci-cloned/ci .
            rm -rf boost-ci-cloned

      - name: Setup Boost
        env: {B2_DONT_BOOTSTRAP: 1}
        run: source ci/github/install.sh

      - name: Run CMake
        run: |
            cd "$BOOST_ROOT"
            mkdir __build_cmake_test__ && cd __build_cmake_test__
            cmake -G "${{matrix.generator}}" -DCMAKE_BUILD_TYPE=${{matrix.build_type}} -DBOOST_INCLUDE_LIBRARIES=$SELF -DBUILD_SHARED_LIBS=${{matrix.build_shared}} -DBUILD_TESTING=ON -DBoost_VERBOSE=ON ..
            cmake --build . --config ${{matrix.build_type}}
